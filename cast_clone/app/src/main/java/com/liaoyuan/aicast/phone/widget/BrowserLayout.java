package com.liaoyuan.aicast.phone.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import android.webkit.WebView;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.liaoyuan.aicast.R;
import com.liaoyuan.aicast.phone.browser.model.BrowserInfo;
import java.util.List;

public class BrowserLayout extends FrameLayout implements View.OnClickListener {

    public interface BrowserLayoutListener {
        void onBrowserAction();
        void onBrowserButtonClick(View view);
    }

    public RelativeLayout webPhoneLayout;
    public RelativeLayout webLayout;
    public FrameLayout webViewContainer;
    public LinearProgressIndicator progressIndicator;
    public BrowserNavLayout browserNavLayout;
    public View divisionLine;
    public AppCompatImageView homeButton;
    public AppCompatImageView backButton;
    public AppCompatImageView forwardButton;
    public AppCompatImageView sendToRemoteButton;
    public AppCompatImageView sendToPhoneButton;
    public AppCompatImageView refreshButton;
    public AppCompatImageView starButton;
    public RelativeLayout favoriteLayout;
    public RecyclerView bookmarkRecyclerView;
    public TextView emptyBookmarkText;
    public LinearLayout bottomLayout;
    public TextView deleteButton;
    public TextView selectAllButton;

    public boolean autoHideNavigation = true;
    public boolean isNavigationVisible = false;
    public boolean isPresentationMode = false;
    public List<BrowserInfo> bookmarkList;

    private GestureDetector gestureDetector;
    private BrowserLayoutListener listener;
    private CustomWebView webView;

    public BrowserLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    private void init(Context context) {
        View.inflate(context, R.layout.layout_browser, this);
        
        webPhoneLayout = findViewById(R.id.rl_web_phone);
        webLayout = findViewById(R.id.rl_web);
        webViewContainer = findViewById(R.id.webview_container);
        progressIndicator = findViewById(R.id.load_progress);
        browserNavLayout = findViewById(R.id.browser_naiv_layout);
        divisionLine = findViewById(R.id.view_division_line);
        
        homeButton = findViewById(R.id.home);
        backButton = findViewById(R.id.reward);
        forwardButton = findViewById(R.id.forward);
        sendToRemoteButton = findViewById(R.id.send_to_remote);
        sendToPhoneButton = findViewById(R.id.send_to_phone);
        refreshButton = findViewById(R.id.refresh);
        starButton = findViewById(R.id.star);
        
        favoriteLayout = findViewById(R.id.rl_favorite);
        bookmarkRecyclerView = findViewById(R.id.rv_bookmark);
        emptyBookmarkText = findViewById(R.id.tv_empty_fav_tips);
        bottomLayout = findViewById(R.id.ll_bottom);
        deleteButton = findViewById(R.id.tv_delete);
        selectAllButton = findViewById(R.id.tv_all_select);
        
        gestureDetector = new GestureDetector(getContext(), new GestureListener());
        
        webView = new CustomWebView(getContext());
        webViewContainer.addView(webView);
        webView.setBackgroundColor(0);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(-1, -1);
        webView.setLayoutParams(params);
        
        homeButton.setOnClickListener(this);
        backButton.setOnClickListener(this);
        forwardButton.setOnClickListener(this);
        sendToPhoneButton.setOnClickListener(this);
        sendToRemoteButton.setOnClickListener(this);
        refreshButton.setOnClickListener(this);
        starButton.setOnClickListener(this);
        deleteButton.setOnClickListener(this);
        selectAllButton.setOnClickListener(this);
        
        setNavigationButtonsEnabled(false);
        hideNavigation();
    }

    public void setOnBrowserClickListener(BrowserLayoutListener listener) {
        this.listener = listener;
    }

    public WebView getWebView() {
        return webView;
    }

    public void setLoadingProgress(boolean visible, int progress) {
        progressIndicator.setVisibility(visible ? View.VISIBLE : View.GONE);
        progressIndicator.setProgress(progress);
    }

    public void showBrowserNavigation(boolean show) {
        browserNavLayout.setVisibility(show ? View.VISIBLE : View.GONE);
        isNavigationVisible = show;
    }

    public void hideNavigation() {
        browserNavLayout.setVisibility(View.GONE);
        isNavigationVisible = false;
    }

    public void setAutoHideBrowserNavi(boolean autoHide) {
        this.autoHideNavigation = autoHide;
    }

    public void setNavigationButtonsEnabled(boolean enabled) {
        homeButton.setEnabled(enabled);
        backButton.setEnabled(enabled);
        forwardButton.setEnabled(enabled);
        refreshButton.setEnabled(enabled);
        starButton.setEnabled(enabled);
    }

    public void setEnableHome(boolean enabled) {
        homeButton.setEnabled(enabled);
    }

    public void setEnableReward(boolean enabled) {
        backButton.setEnabled(enabled);
    }

    public void setEnableForward(boolean enabled) {
        forwardButton.setEnabled(enabled);
    }

    public void setEnableRefresh(boolean enabled) {
        refreshButton.setEnabled(enabled);
    }

    public void setEnableStar(boolean enabled) {
        starButton.setEnabled(enabled);
    }

    public void setSelectStar(boolean selected) {
        starButton.setSelected(selected);
    }

    public void initialize() {
        isNavigationVisible = true;
        showBrowserNavigation(true);
    }

    public void adjustLayoutMargins(int orientation, int leftRight, int topBottom) {
        // Implementation for layout margin adjustment
    }

    public void updateBookmarks() {
        // Implementation for updating bookmarks
    }

    public boolean isBookmarkVisible() {
        return favoriteLayout.getVisibility() == View.VISIBLE;
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_UP && 
            webView.getHitTestResult().getType() == WebView.HitTestResult.SRC_ANCHOR_TYPE && 
            listener != null) {
            listener.onBrowserAction();
        }
        
        if (autoHideNavigation) {
            gestureDetector.onTouchEvent(event);
        }
        
        return super.dispatchTouchEvent(event);
    }

    @Override
    public void onClick(View view) {
        if (listener != null) {
            listener.onBrowserButtonClick(view);
        }
    }

    private class GestureListener extends GestureDetector.SimpleOnGestureListener {
        @Override
        public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
            float deltaX = e2.getX() - e1.getX();
            if (Math.abs(deltaX) <= Math.abs(e2.getY() - e1.getY()) || Math.abs(deltaX) <= 10.0f) {
                return false;
            }
            
            if (deltaX > 0.0f) {
                Log.d("BrowserLayout", "Right swipe");
                showBrowserNavigation(true);
                if (browserNavLayout.getVisibility() == View.VISIBLE) {
                    hideNavigation();
                }
            } else {
                Log.d("BrowserLayout", "Left swipe");
                showBrowserNavigation(false);
            }
            return true;
        }
    }
}