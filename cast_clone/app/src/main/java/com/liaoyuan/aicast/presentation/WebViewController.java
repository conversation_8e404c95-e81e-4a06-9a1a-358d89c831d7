package com.liaoyuan.aicast.presentation;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.webkit.WebHistoryItem;
import android.webkit.WebView;
import android.text.TextUtils;
import com.liaoyuan.aicast.utils.WebViewCallback;
import java.util.ArrayList;
import java.util.List;

public abstract class AbstractWebViewCallbackManager<V> {

    protected final Object lock = new Object();
    protected final List<V> callbacks = new ArrayList<>();
    protected final Handler handler = new Handler(Looper.getMainLooper());

    public final void addCallback(V callback) {
        synchronized (lock) {
            if (callback != null && !callbacks.contains(callback)) {
                callbacks.add(callback);
                onCallbackAdded(callback);
            }
        }
    }

    public final void addCallbackOnMainThread(V callback) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            addCallback(callback);
        } else {
            handler.post(() -> addCallback(callback));
        }
    }

    public final void removeCallback(V callback) {
        synchronized (lock) {
            if (callback != null && callbacks.contains(callback)) {
                callbacks.remove(callback);
            }
        }
    }

    protected abstract void onCallbackAdded(V callback);
}

public final class WebViewController extends AbstractWebViewCallbackManager<WebViewCallback> {

    private static WebViewController instance;
    private Context context;
    private WebView webView;
    private boolean isConnected = false;
    private boolean isLocalBrowserEnabled = false;
    private String currentUrl;

    private WebViewController() {
    }

    public static WebViewController getInstance() {
        if (instance == null) {
            instance = new WebViewController();
        }
        return instance;
    }

    public void initialize(Context context) {
        this.context = context;
    }

    @Override
    protected void onCallbackAdded(WebViewCallback callback) {
        // 当添加新回调时，通知当前状态
        callback.onLocalBrowserStatus(isLocalBrowserEnabled);
    }

    public void setWebView(WebView webView) {
        this.webView = webView;
    }

    public WebView getWebView() {
        return webView;
    }

    public void setConnected(boolean connected) {
        isConnected = connected;
    }

    public boolean isConnected() {
        return isConnected;
    }

    public void enableLocalBrowser(boolean enable) {
        if (this.isLocalBrowserEnabled != enable) {
            this.isLocalBrowserEnabled = enable;
            
            if (enable) {
                Log.d("WebViewController", "Enabling local browser mode");
                // 启用本地浏览器模式 - 开始录音等操作
                // startAudioRecording();
            } else {
                Log.d("WebViewController", "Disabling local browser mode");
                // 关闭本地浏览器模式 - 停止录音等操作
                // stopAudioRecording();
            }
            
            // 获取当前URL并重新加载（使用m5311k的完整逻辑）
            if (webView != null) {
                WebHistoryItem currentItem = webView.copyBackForwardList().getCurrentItem();
                String url = currentItem != null ? currentItem.getUrl() : currentUrl;
                if (!TextUtils.isEmpty(url)) {
                    // 调用完整的URL处理逻辑
                    processAndLoadUrl(url, enable);
                }
            }
            
            // 通知所有回调监听器
            synchronized (lock) {
                for (WebViewCallback callback : callbacks) {
                    callback.onLocalBrowserStatus(enable);
                }
            }
        }
    }

    private void processAndLoadUrl(String inputUrl, boolean isLocalBrowserMode) {
        // 实现m5311k的完整URL处理逻辑
        
        // 1. 权限和状态检查（占位符实现）
        boolean isProjecting = isProjecting(); // 需要实现isProjecting方法
        if (isProjecting && isLocalBrowserMode) {
            // m5307g(true) - 设置媒体静音
            setMediaMute(true);
        } else {
            // m5307g(false) - 取消媒体静音
            setMediaMute(false);
        }
        
        // 2. 录音权限检查（占位符实现）
        if (!hasRecordAudioPermission()) {
            // m5314n(10) - 发送权限通知
            notifyPermissionRequired(10);
        }
        
        // 3. URL验证和搜索引擎转换
        String processedUrl = inputUrl;
        if (TextUtils.isEmpty(inputUrl) || !isValidUrl(inputUrl)) {
            // 不是有效的URL，转换为搜索引擎查询
            int searchEngineIndex = getSearchEngineIndex(); // 需要实现搜索引擎选择
            
            switch (searchEngineIndex) {
                case 0: // Google (默认)
                    processedUrl = buildSearchUrl("https://www.google.com", "search", "q", inputUrl);
                    break;
                case 1: // Bing
                    processedUrl = buildSearchUrl("https://www.bing.com", "search", "q", inputUrl);
                    break;
                case 2: // Yahoo
                    processedUrl = buildSearchUrl("https://search.yahoo.com", "search", "p", inputUrl);
                    break;
                case 3: // 百度
                    processedUrl = buildSearchUrl("https://www.baidu.com", "s", "wd", inputUrl);
                    break;
                case 4: // Yandex
                    processedUrl = buildSearchUrl("https://yandex.com", "search", "text", inputUrl);
                    break;
                case 5: // DuckDuckGo
                    processedUrl = buildSearchUrl("https://duckduckgo.com", "", "q", inputUrl);
                    break;
                default:
                    processedUrl = buildSearchUrl("https://www.google.com", "search", "q", inputUrl);
            }
        }
        
        // 4. 保存处理后的URL
        this.currentUrl = processedUrl;
        
        // 5. 异步加载URL（模拟RunnableC2119f的功能）
        handler.post(() -> {
            // 通知搜索状态变化
            synchronized (lock) {
                for (WebViewCallback callback : callbacks) {
                    // 这里应该调用对应的回调方法，类似mo81j
                    // callback.onSearchStatusChanged(isLocalBrowserMode);
                }
            }
            
            // 加载URL
            if (webView != null) {
                webView.loadUrl(processedUrl);
            }
        });
    }
    
    // 辅助方法 - 需要实现
    private boolean isProjecting() {
        // 实现投影状态检查逻辑
        return false;
    }
    
    private void setMediaMute(boolean mute) {
        // 实现媒体静音设置逻辑
    }
    
    private boolean hasRecordAudioPermission() {
        // 实现录音权限检查逻辑
        return false;
    }
    
    private void notifyPermissionRequired(int permissionType) {
        // 实现权限通知逻辑
    }
    
    private boolean isValidUrl(String url) {
        // 实现URL验证逻辑
        return android.util.Patterns.WEB_URL.matcher(url).matches();
    }
    
    private int getSearchEngineIndex() {
        // 实现搜索引擎选择逻辑（默认返回Google）
        return 0;
    }
    
    private String buildSearchUrl(String baseUrl, String path, String queryParam, String query) {
        android.net.Uri.Builder builder = android.net.Uri.parse(baseUrl).buildUpon();
        if (!TextUtils.isEmpty(path)) {
            builder.appendPath(path);
        }
        builder.appendQueryParameter(queryParam, query);
        return builder.build().toString();
    }

    public void disconnect() {
        isConnected = false;
        // 断开连接的具体实现
    }

    public void reconnect() {
        // 重新连接的具体实现
        isConnected = true;
    }
}