package com.liaoyuan.aicast.utils;

import android.webkit.WebView;
import com.liaoyuan.aicast.phone.browser.model.BrowserInfo;
import java.util.List;

public interface WebViewCallback {
    default void onStarStatusChanged(boolean isStarred) {}
    default void onNavigationVisibilityChanged(boolean visible, String url) {}
    default void onBookmarksUpdated(List<BrowserInfo> bookmarks) {}
    default void onWebViewReady(WebView webView) {}
    default void onPermissionRequest(int permissionType) {}
    default void onLocalBrowserStatus(boolean enabled) {}
    default void onPageLoadStatus(boolean loading, WebView webView, boolean showProgress, int progress) {}
}