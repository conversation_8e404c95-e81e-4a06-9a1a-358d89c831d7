package com.liaoyuan.aicast.presentation;

import android.app.Presentation;
import android.content.Context;
import android.util.Log;
import android.view.Display;
import android.view.View;
import android.webkit.WebView;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;
import com.liaoyuan.aicast.R;
import com.liaoyuan.aicast.phone.browser.model.BrowserInfo;
import com.liaoyuan.aicast.phone.widget.BrowserLayout;
import com.liaoyuan.aicast.phone.widget.DisconnectLayout;
import com.liaoyuan.aicast.phone.widget.TipBrowserLayout;
import com.liaoyuan.aicast.phone.widget.TipLocalBrowserLayout;
import com.liaoyuan.aicast.phone.widget.ToastBrowserLayout;
import com.liaoyuan.aicast.utils.DisplayUtils;
import com.liaoyuan.aicast.utils.WebViewCallback;
import java.util.List;

public final class CustomPresentation extends Presentation {

    public static final int SYNTHETIC = 0;

    public WebViewController webViewController;
    public View controlPointView;
    public BrowserLayout browserLayout;
    public TipLocalBrowserLayout tipLocalBrowserLayout;
    public ToastBrowserLayout toastBrowserLayout;
    public TipBrowserLayout tipBrowserLayout;
    public DisconnectLayout disconnectLayout;
    public int displayWidth;
    public int displayHeight;
    public DisplayUtils displayUtils;
    public ControlPointRunnable controlPointRunnable;
    public PresentationCallback presentationCallback;

    public class ControlPointRunnable implements Runnable {
        public ControlPointRunnable() {
        }

        @Override
        public final void run() {
            View view;
            int visibility;
            View view2 = CustomPresentation.this.controlPointView;
            if (view2 != null) {
                if (view2.getVisibility() == 0) {
                    view = CustomPresentation.this.controlPointView;
                    visibility = 8;
                } else {
                    view = CustomPresentation.this.controlPointView;
                    visibility = 0;
                }
                view.setVisibility(visibility);
                if (CustomPresentation.this.isShowing()) {
                    CustomPresentation presentation = CustomPresentation.this;
                    presentation.controlPointView.postDelayed(presentation.controlPointRunnable, 33L);
                }
            }
        }
    }

    public class PresentationCallback implements WebViewCallback {
        public PresentationCallback() {
        }

        @Override
        public final void onStarStatusChanged(boolean isStarred) {
            CustomPresentation.this.browserLayout.setSelectStar(isStarred);
        }

        @Override
        public final void onNavigationVisibilityChanged(boolean visible, String url) {
            BrowserLayout browserLayout;
            boolean show;
            if (visible) {
                browserLayout = CustomPresentation.this.browserLayout;
                show = true;
            } else {
                browserLayout = CustomPresentation.this.browserLayout;
                show = false;
            }
            browserLayout.showBrowserNavigation(show);
        }

        @Override
        public final void onBookmarksUpdated(List<BrowserInfo> bookmarks) {
            BrowserLayout browserLayout = CustomPresentation.this.browserLayout;
            browserLayout.bookmarkList = bookmarks;
            browserLayout.updateBookmarks();
        }

        @Override
        public final void onWebViewReady(WebView webView) {
            CustomPresentation.this.browserLayout.setLoadingProgress(false, 0);
            CustomPresentation.this.browserLayout.setEnableReward(webView.canGoBack());
            CustomPresentation.this.browserLayout.setEnableForward(webView.canGoForward());
            CustomPresentation.this.browserLayout.setEnableHome(true);
            CustomPresentation.this.browserLayout.setEnableRefresh(true);
            CustomPresentation.this.browserLayout.setEnableStar(true);
        }

        @Override
        public final void onPermissionRequest(int permissionType) {
            CustomPresentation presentation = CustomPresentation.this;
            presentation.toastBrowserLayout.setToast(presentation.getContext().getResources().getString(R.string.virtual_toast_phone_permissions));
            ToastBrowserLayout toastLayout = CustomPresentation.this.toastBrowserLayout;
            toastLayout.setVisibility(0);
            toastLayout.removeCallbacks(toastLayout.hideRunnable);
            toastLayout.postDelayed(toastLayout.hideRunnable, 3000L);
        }

        @Override
        public final void onLocalBrowserStatus(boolean enabled) {
            if (!enabled) {
                CustomPresentation.this.tipLocalBrowserLayout.localBrowserButton.setVisibility(0);
                return;
            }
            CustomPresentation presentation = CustomPresentation.this;
            presentation.webViewController.connectWebView(presentation.browserLayout.getWebView());
            CustomPresentation.this.tipLocalBrowserLayout.localBrowserButton.setVisibility(8);
        }

        @Override
        public final void onPageLoadStatus(boolean loading, WebView webView, boolean showProgress, int progress) {
            CustomPresentation.this.browserLayout.setLoadingProgress(showProgress, progress);
            CustomPresentation.this.browserLayout.setEnableReward(webView.canGoBack());
            CustomPresentation.this.browserLayout.setEnableForward(webView.canGoForward());
            CustomPresentation.this.browserLayout.setEnableHome(true);
            CustomPresentation.this.browserLayout.setEnableRefresh(true);
            CustomPresentation.this.browserLayout.setEnableStar(true);
        }
    }

    public CustomPresentation(Context context, Display display, boolean isHorizontal, DisplayUtils displayUtils) {
        super(context, display, R.style.FullScreenDialog);
        this.controlPointRunnable = new ControlPointRunnable();
        this.presentationCallback = new PresentationCallback();
        setContentView(R.layout.cast_presentation);
        this.displayWidth = display.getWidth();
        this.displayHeight = display.getHeight();
        int displayId = display.getDisplayId();
        this.displayUtils = displayUtils;
        Log.d("CustomPresentation", "Creating presentation - horizontal:" + isHorizontal + ",width:" + this.displayWidth + ",height:" + this.displayHeight + ",displayId:" + displayId);
        
        this.webViewController = WebViewController.getInstance();
        this.webViewController.initialize(getContext());
        
        this.controlPointView = findViewById(R.id.view_point);
        this.tipLocalBrowserLayout = (TipLocalBrowserLayout) findViewById(R.id.tip_local_browser_layout);
        this.toastBrowserLayout = (ToastBrowserLayout) findViewById(R.id.toast_browser_layout);
        this.tipBrowserLayout = (TipBrowserLayout) findViewById(R.id.tip_input_browser_layout);
        this.disconnectLayout = (DisconnectLayout) findViewById(R.id.tip_disconnect_layout);
        BrowserLayout browserLayout = (BrowserLayout) findViewById(R.id.bl_remote);
        this.browserLayout = browserLayout;
        
        final int FLAG_1 = 1;
        final int FLAG_0 = 0;
        
        browserLayout.setAutoHideBrowserNavi(true);
        this.browserLayout.browserNavLayout.forwardButton.setVisibility(0);
        this.browserLayout.browserNavLayout.backButton.setVisibility(0);
        this.browserLayout.initialize();
        this.browserLayout.isPresentationMode = true;
        
        int dimension = (int) getContext().getResources().getDimension(R.dimen.browser_nav_default_size);
        if (isHorizontal) {
            BrowserLayout browserLayout2 = this.browserLayout;
            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) browserLayout2.bookmarkRecyclerView.getLayoutParams();
            layoutParams.setMargins(dimension, 0, 0, 0);
            browserLayout2.bookmarkRecyclerView.setLayoutParams(layoutParams);
            this.browserLayout.adjustLayoutMargins(0, dimension, -1);
        } else {
            BrowserLayout browserLayout3 = this.browserLayout;
            RelativeLayout.LayoutParams layoutParams2 = (RelativeLayout.LayoutParams) browserLayout3.bookmarkRecyclerView.getLayoutParams();
            layoutParams2.setMargins(0, 0, 0, dimension);
            browserLayout3.bookmarkRecyclerView.setLayoutParams(layoutParams2);
            this.browserLayout.adjustLayoutMargins(2, -1, dimension);
        }
        
        this.webViewController.connectWebView(this.browserLayout.getWebView());
        this.webViewController.setCallback(this.presentationCallback);
        
        this.browserLayout.setOnBrowserClickListener(new BrowserClickListener(this));
        
        this.tipLocalBrowserLayout.setOnConfirmClickListener(new View.OnClickListener() {
            @Override
            public final void onClick(View view) {
                CustomPresentation.this.webViewController.enableLocalBrowser(true);
            }
        });
        
        this.tipBrowserLayout.setOnConfirmClickListener(new View.OnClickListener() {
            @Override
            public final void onClick(View view) {
                // 关闭本地浏览器模式
                CustomPresentation.this.webViewController.enableLocalBrowser(false);
            }
        });
        
        this.disconnectLayout.setOnConfirmClickListener(new View.OnClickListener() {
            @Override
            public final void onClick(View view) {
                CustomPresentation presentation = CustomPresentation.this;
                presentation.disconnectLayout.disconnectButton.setVisibility(8);
                WebViewController controller = presentation.webViewController;
                if (controller.isConnected()) {
                    controller.disconnect();
                    controller.reconnect();
                }
            }
        });
        
        getWindow().getDecorView().post(new Runnable() {
            @Override
            public void run() {
                calculateWebViewDimensions();
            }
        });
    }

    public final void calculateWebViewDimensions() {
        int[] dimensions;
        int width = this.displayWidth;
        int height = this.displayHeight;
        int aspectRatioMode = this.displayUtils.getAspectRatioMode();
        
        if (width > height) {
            switch (aspectRatioMode) {
                case 0: // Original
                    dimensions = new int[]{width, height};
                    break;
                case 1: // 16:9
                    int width16_9 = (int) (height * 1.7777778f);
                    if (width16_9 <= width) {
                        width = width16_9;
                    }
                    dimensions = new int[]{width, height};
                    break;
                case 2: // 4:3
                    int width4_3 = (int) (height * 1.3333334f);
                    if (width4_3 <= width) {
                        width = width4_3;
                    }
                    dimensions = new int[]{width, height};
                    break;
                case 3: // 2:1
                    int width2_1 = (int) (height * 2.0f);
                    if (width2_1 <= width) {
                        width = width2_1;
                    }
                    dimensions = new int[]{width, height};
                    break;
                case 4: // 1:1
                    int width1_1 = (int) (height * 1.0f);
                    if (width1_1 <= width) {
                        width = width1_1;
                    }
                    dimensions = new int[]{width, height};
                    break;
                default:
                    dimensions = new int[]{width, height};
            }
            
            BrowserLayout browserLayout = this.browserLayout;
            int calculatedWidth = dimensions[0];
            int calculatedHeight = dimensions[1];
            FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) browserLayout.getWebView().getLayoutParams();
            layoutParams.width = calculatedWidth;
            layoutParams.height = calculatedHeight;
            browserLayout.getWebView().setLayoutParams(layoutParams);
        }
        
        dimensions = new int[]{width, height};
        BrowserLayout browserLayout2 = this.browserLayout;
        int finalWidth = dimensions[0];
        int finalHeight = dimensions[1];
        FrameLayout.LayoutParams layoutParams2 = (FrameLayout.LayoutParams) browserLayout2.getWebView().getLayoutParams();
        layoutParams2.width = finalWidth;
        layoutParams2.height = finalHeight;
        browserLayout2.getWebView().setLayoutParams(layoutParams2);
    }

    public final void showPresentation() {
        if (isShowing()) {
            return;
        }
        try {
            show();
        } catch (Exception e) {
            e.printStackTrace();
            Log.d("CustomPresentation", "show error:" + e.getMessage());
        }
        this.controlPointView.post(this.controlPointRunnable);
    }

    @Override
    public final void dismiss() {
        try {
            super.dismiss();
        } catch (Exception e) {
            e.printStackTrace();
            Log.d("CustomPresentation", "dismiss error:" + e.getMessage());
        }
        this.controlPointView.removeCallbacks(this.controlPointRunnable);
    }
}