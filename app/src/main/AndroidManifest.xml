<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    android:versionCode="3"
    android:versionName="2025071018"
    android:compileSdkVersion="34"
    android:compileSdkVersionCodename="14"
    package="com.liaoyuan.aicast"
    platformBuildVersionCode="34"
    platformBuildVersionName="14">
    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="34"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.NEARBY_WIFI_DEVICES"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE"/>
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS"/>
    <uses-permission android:name="android.Manifest.permission.CAPTURE_AUDIO_OUTPUT"/>
    <uses-permission android:name="android.permission.ADJUST_VOLUME"/>
    <uses-permission android:name="android.permission.DEVICE_POWER"/>
    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE"/>
    <application
        android:theme="@style/Theme.LyProjectionScreen"
        android:label="@string/app_name_ai_cast"
        android:icon="@mipmap/ic_launcher"
        android:name="com.liaoyuan.aicast.LinkApp"
        android:supportsRtl="true"
        android:extractNativeLibs="false"
        android:roundIcon="@mipmap/ic_launcher"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory">
        <activity
            android:name="com.liaoyuan.aicast.MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:configChanges="screenSize|orientation|keyboardHidden">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <activity
            android:name="com.liaoyuan.aicast.phone.browser.view.BrowserActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:configChanges="screenSize|orientation|keyboardHidden"/>
        <activity
            android:name="com.liaoyuan.aicast.phone.html.HtmlActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:configChanges="screenSize|orientation|keyboardHidden"/>
        <activity
            android:name="com.liaoyuan.aicast.phone.wifip2p.view.WifiP2pActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:configChanges="screenSize|orientation|keyboardHidden"/>
        <service
            android:name="com.liaoyuan.aicast.phone.projection.service.ProjectionScreenService"
            android:exported="true"
            android:foregroundServiceType="mediaProjection"/>
        <provider
            android:name="androidx.core.content.FileProvider"
            android:exported="false"
            android:authorities="com.liaoyuan.aicast.fileprovider"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths"/>
        </provider>
        <receiver
            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
            android:enabled="true"
            android:exported="false"/>
        <service
            android:name="com.google.android.gms.measurement.AppMeasurementService"
            android:enabled="true"
            android:exported="false"/>
        <service
            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:enabled="true"
            android:exported="false"/>
        <provider
            android:name="com.google.firebase.provider.FirebaseInitProvider"
            android:exported="false"
            android:authorities="com.liaoyuan.aicast.firebaseinitprovider"
            android:initOrder="100"
            android:directBootAware="true"/>
        <service
            android:name="com.google.firebase.components.ComponentDiscoveryService"
            android:exported="false"
            android:directBootAware="true"/>
        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:exported="false"/>
        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false"/>
        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false"/>
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version"/>
    </application>
</manifest>
