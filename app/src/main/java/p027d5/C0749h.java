package p027d5;

import android.content.Context;
import android.util.Log;
import android.webkit.WebSettings;
import android.webkit.WebView;

/**
 * 增强型WebView组件
 * 预配置了适合投屏应用的WebView设置，支持JavaScript、DOM存储、文件访问等功能
 */
public class EnhancedWebView extends WebView {

    private static final String TAG = "EnhancedWebView";

    public EnhancedWebView(Context context) {
        super(context);
        initializeWebViewSettings();
    }

    /**
     * 初始化WebView设置
     * 配置适合投屏应用的各种WebView参数
     */
    private void initializeWebViewSettings() {
        WebSettings settings = getSettings();

        // 基础设置
        settings.setDefaultTextEncodingName("UTF-8");
        settings.setJavaScriptEnabled(true);
        settings.setJavaScriptCanOpenWindowsAutomatically(true);

        // 缓存设置
        settings.setCacheMode(WebSettings.LOAD_CACHE_ELSE_NETWORK);
        settings.setDomStorageEnabled(true);
        settings.setDatabaseEnabled(true);

        // 文件访问设置
        settings.setAllowFileAccess(true);
        settings.setSavePassword(true);

        // 缩放设置
        settings.setSupportZoom(false);
        settings.setBuiltInZoomControls(true);
        settings.setDisplayZoomControls(false);

        // 视口设置
        settings.setUseWideViewPort(true);
        settings.setLoadWithOverviewMode(true);
        settings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NORMAL);

        // 图片和媒体设置
        settings.setLoadsImagesAutomatically(true);
        settings.setMediaPlaybackRequiresUserGesture(false);

        // 安全设置
        settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);

        // 插件设置
        settings.setPluginState(WebSettings.PluginState.ON);

        Log.d(TAG, "WebView settings initialized");
    }

    @Override
    public void onPause() {
        super.onPause();
        Log.d(TAG, "WebView paused");
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "WebView resumed");
    }

    /**
     * 启用调试模式
     * 在开发环境中可以调用此方法启用WebView调试
     */
    public void enableDebugMode() {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(true);
            Log.d(TAG, "WebView debugging enabled");
        }
    }

    /**
     * 设置用户代理
     * @param userAgent 用户代理字符串
     */
    public void setCustomUserAgent(String userAgent) {
        getSettings().setUserAgentString(userAgent);
        Log.d(TAG, "Custom user agent set: " + userAgent);
    }

    /**
     * 启用或禁用JavaScript
     * @param enabled 是否启用JavaScript
     */
    public void setJavaScriptEnabled(boolean enabled) {
        getSettings().setJavaScriptEnabled(enabled);
        Log.d(TAG, "JavaScript " + (enabled ? "enabled" : "disabled"));
    }

    /**
     * 设置缓存模式
     * @param cacheMode 缓存模式
     */
    public void setCacheMode(int cacheMode) {
        getSettings().setCacheMode(cacheMode);
        Log.d(TAG, "Cache mode set to: " + cacheMode);
    }

    /**
     * 清除所有缓存
     */
    public void clearAllCache() {
        clearCache(true);
        clearHistory();
        clearFormData();
        Log.d(TAG, "All cache cleared");
    }

    /**
     * 获取当前页面标题
     * @return 页面标题
     */
    public String getCurrentPageTitle() {
        return getTitle();
    }

    /**
     * 获取当前页面URL
     * @return 页面URL
     */
    public String getCurrentPageUrl() {
        return getUrl();
    }

    /**
     * 检查是否可以后退
     * @return 是否可以后退
     */
    public boolean canGoBackward() {
        return canGoBack();
    }

    /**
     * 检查是否可以前进
     * @return 是否可以前进
     */
    public boolean canGoForward() {
        return canGoForward();
    }

    /**
     * 安全地销毁WebView
     */
    public void safeDestroy() {
        try {
            stopLoading();
            onPause();
            removeAllViews();
            destroy();
            Log.d(TAG, "WebView safely destroyed");
        } catch (Exception e) {
            Log.e(TAG, "Error destroying WebView", e);
        }
    }
}
