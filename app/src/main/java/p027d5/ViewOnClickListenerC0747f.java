package p027d5;

import android.view.View;
import com.liaoyuan.aicast.phone.widget.ProjectionScreenMenu;
import com.liaoyuan.aicast.phone.widget.TipBrowserLayout;
import com.liaoyuan.aicast.phone.wifip2p.view.WifiP2pActivity;
import p041f5.C0865b;

/* renamed from: d5.f */
/* loaded from: classes.dex */
public final /* synthetic */ class ViewOnClickListenerC0747f implements View.OnClickListener {

    /* renamed from: j */
    public final /* synthetic */ int f4007j;

    /* renamed from: k */
    public final /* synthetic */ Object f4008k;

    public /* synthetic */ ViewOnClickListenerC0747f(Object obj, int i6) {
        this.f4007j = i6;
        this.f4008k = obj;
    }

    @Override // android.view.View.OnClickListener
    public final void onClick(View view) {
        switch (this.f4007j) {
            case 0:
                ProjectionScreenMenu projectionScreenMenu = (ProjectionScreenMenu) this.f4008k;
                View.OnClickListener onClickListener = projectionScreenMenu.f3515j;
                if (onClickListener != null) {
                    onClickListener.onClick(view);
                    projectionScreenMenu.f3523r = 0;
                    projectionScreenMenu.f3520o.post(new RunnableC0748g(projectionScreenMenu));
                    break;
                }
                break;
            case 1:
                TipBrowserLayout tipBrowserLayout = (TipBrowserLayout) this.f4008k;
                View.OnClickListener onClickListener2 = tipBrowserLayout.f3525k;
                if (onClickListener2 != null) {
                    onClickListener2.onClick(view);
                }
                tipBrowserLayout.m1926a();
                break;
            default:
                C0865b c0865b = ((WifiP2pActivity) this.f4008k).f3541I.f4503f;
                if (!c0865b.f4381h) {
                    c0865b.m2389e();
                    break;
                } else {
                    c0865b.m2399o();
                    break;
                }
        }
    }
}
