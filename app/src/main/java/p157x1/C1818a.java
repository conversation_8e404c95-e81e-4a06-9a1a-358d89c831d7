package p157x1;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.RandomAccessFile;
import java.nio.ByteBuffer;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.util.concurrent.atomic.AtomicReference;

/* renamed from: x1.a */
/* loaded from: classes.dex */
public final class C1818a {

    /* renamed from: a */
    public static final AtomicReference<byte[]> f7502a = new AtomicReference<>();

    /* renamed from: x1.a$a */
    public static class a extends InputStream {

        /* renamed from: j */
        public final ByteBuffer f7503j;

        /* renamed from: k */
        public int f7504k = -1;

        public a(ByteBuffer byteBuffer) {
            this.f7503j = byteBuffer;
        }

        @Override // java.io.InputStream
        public final int available() {
            return this.f7503j.remaining();
        }

        @Override // java.io.InputStream
        public final synchronized void mark(int i6) {
            this.f7504k = this.f7503j.position();
        }

        @Override // java.io.InputStream
        public final boolean markSupported() {
            return true;
        }

        @Override // java.io.InputStream
        public final int read() {
            if (this.f7503j.hasRemaining()) {
                return this.f7503j.get() & 255;
            }
            return -1;
        }

        @Override // java.io.InputStream
        public final int read(byte[] bArr, int i6, int i7) {
            if (!this.f7503j.hasRemaining()) {
                return -1;
            }
            int min = Math.min(i7, available());
            this.f7503j.get(bArr, i6, min);
            return min;
        }

        @Override // java.io.InputStream
        public final synchronized void reset() {
            int i6 = this.f7504k;
            if (i6 == -1) {
                throw new IOException("Cannot reset to unset mark position");
            }
            this.f7503j.position(i6);
        }

        @Override // java.io.InputStream
        public final long skip(long j6) {
            if (!this.f7503j.hasRemaining()) {
                return -1L;
            }
            long min = Math.min(j6, available());
            this.f7503j.position((int) (r0.position() + min));
            return min;
        }
    }

    /* renamed from: x1.a$b */
    public static final class b {

        /* renamed from: a */
        public final int f7505a;

        /* renamed from: b */
        public final int f7506b;

        /* renamed from: c */
        public final byte[] f7507c;

        public b(byte[] bArr, int i6, int i7) {
            this.f7507c = bArr;
            this.f7505a = i6;
            this.f7506b = i7;
        }
    }

    /* renamed from: a */
    public static ByteBuffer m4659a(File file) {
        RandomAccessFile randomAccessFile;
        FileChannel fileChannel = null;
        try {
            long length = file.length();
            if (length > 2147483647L) {
                throw new IOException("File too large to map into memory");
            }
            if (length == 0) {
                throw new IOException("File unsuitable for memory mapping");
            }
            randomAccessFile = new RandomAccessFile(file, "r");
            try {
                fileChannel = randomAccessFile.getChannel();
                MappedByteBuffer load = fileChannel.map(FileChannel.MapMode.READ_ONLY, 0L, length).load();
                try {
                    fileChannel.close();
                } catch (IOException unused) {
                }
                try {
                    randomAccessFile.close();
                } catch (IOException unused2) {
                }
                return load;
            } catch (Throwable th) {
                th = th;
                if (fileChannel != null) {
                    try {
                        fileChannel.close();
                    } catch (IOException unused3) {
                    }
                }
                if (randomAccessFile == null) {
                    throw th;
                }
                try {
                    randomAccessFile.close();
                    throw th;
                } catch (IOException unused4) {
                    throw th;
                }
            }
        } catch (Throwable th2) {
            th = th2;
            randomAccessFile = null;
        }
    }

    /* renamed from: b */
    public static ByteBuffer m4660b(InputStream inputStream) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(16384);
        byte[] andSet = f7502a.getAndSet(null);
        if (andSet == null) {
            andSet = new byte[16384];
        }
        while (true) {
            int read = inputStream.read(andSet);
            if (read < 0) {
                f7502a.set(andSet);
                byte[] byteArray = byteArrayOutputStream.toByteArray();
                return m4661c(ByteBuffer.allocateDirect(byteArray.length).put(byteArray));
            }
            byteArrayOutputStream.write(andSet, 0, read);
        }
    }

    /* renamed from: c */
    public static ByteBuffer m4661c(ByteBuffer byteBuffer) {
        return (ByteBuffer) byteBuffer.position(0);
    }

    /* renamed from: d */
    public static void m4662d(ByteBuffer byteBuffer, File file) {
        RandomAccessFile randomAccessFile;
        FileChannel fileChannel = null;
        try {
            randomAccessFile = new RandomAccessFile(file, "rw");
        } catch (Throwable th) {
            th = th;
            randomAccessFile = null;
        }
        try {
            fileChannel = randomAccessFile.getChannel();
            fileChannel.write(byteBuffer);
            fileChannel.force(false);
            fileChannel.close();
            randomAccessFile.close();
            try {
                fileChannel.close();
            } catch (IOException unused) {
            }
            try {
                randomAccessFile.close();
            } catch (IOException unused2) {
            }
        } catch (Throwable th2) {
            th = th2;
            if (fileChannel != null) {
                try {
                    fileChannel.close();
                } catch (IOException unused3) {
                }
            }
            if (randomAccessFile == null) {
                throw th;
            }
            try {
                randomAccessFile.close();
                throw th;
            } catch (IOException unused4) {
                throw th;
            }
        }
    }
}
