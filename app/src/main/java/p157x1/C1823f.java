package p157x1;

import java.util.Objects;

/* renamed from: x1.f */
/* loaded from: classes.dex */
public final class C1823f implements InterfaceC1824g<Object> {

    /* renamed from: a */
    public volatile Object f7516a;

    /* renamed from: b */
    public final /* synthetic */ InterfaceC1824g f7517b;

    public C1823f(InterfaceC1824g interfaceC1824g) {
        this.f7517b = interfaceC1824g;
    }

    @Override // p157x1.InterfaceC1824g
    public final Object get() {
        if (this.f7516a == null) {
            synchronized (this) {
                if (this.f7516a == null) {
                    Object obj = this.f7517b.get();
                    Objects.requireNonNull(obj, "Argument must not be null");
                    this.f7516a = obj;
                }
            }
        }
        return this.f7516a;
    }
}
