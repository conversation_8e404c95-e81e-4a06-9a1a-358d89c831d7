package p157x1;

import java.util.concurrent.Executor;

/* renamed from: x1.e */
/* loaded from: classes.dex */
public final class C1822e {

    /* renamed from: a */
    public static final a f7514a = new a();

    /* renamed from: b */
    public static final b f7515b = new b();

    /* renamed from: x1.e$a */
    public class a implements Executor {
        @Override // java.util.concurrent.Executor
        public final void execute(Runnable runnable) {
            C1829l.m4681k(runnable);
        }
    }

    /* renamed from: x1.e$b */
    public class b implements Executor {
        @Override // java.util.concurrent.Executor
        public final void execute(Runnable runnable) {
            runnable.run();
        }
    }
}
