package p157x1;

import android.annotation.TargetApi;
import android.graphics.Bitmap;
import android.os.Handler;
import android.os.Looper;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/* renamed from: x1.l */
/* loaded from: classes.dex */
public final class C1829l {

    /* renamed from: a */
    public static final char[] f7529a = "0123456789abcdef".toCharArray();

    /* renamed from: b */
    public static final char[] f7530b = new char[64];

    /* renamed from: c */
    public static volatile Handler f7531c;

    /* renamed from: x1.l$a */
    public static /* synthetic */ class a {

        /* renamed from: a */
        public static final /* synthetic */ int[] f7532a;

        static {
            int[] iArr = new int[Bitmap.Config.values().length];
            f7532a = iArr;
            try {
                iArr[Bitmap.Config.ALPHA_8.ordinal()] = 1;
            } catch (NoSuchFieldError unused) {
            }
            try {
                f7532a[Bitmap.Config.RGB_565.ordinal()] = 2;
            } catch (NoSuchFieldError unused2) {
            }
            try {
                f7532a[Bitmap.Config.ARGB_4444.ordinal()] = 3;
            } catch (NoSuchFieldError unused3) {
            }
            try {
                f7532a[Bitmap.Config.RGBA_F16.ordinal()] = 4;
            } catch (NoSuchFieldError unused4) {
            }
            try {
                f7532a[Bitmap.Config.ARGB_8888.ordinal()] = 5;
            } catch (NoSuchFieldError unused5) {
            }
        }
    }

    /* renamed from: a */
    public static void m4671a() {
        if (!m4679i()) {
            throw new IllegalArgumentException("You must call this method on the main thread");
        }
    }

    /* renamed from: b */
    public static boolean m4672b(Object obj, Object obj2) {
        return obj == null ? obj2 == null : obj.equals(obj2);
    }

    @TargetApi(19)
    /* renamed from: c */
    public static int m4673c(Bitmap bitmap) {
        if (!bitmap.isRecycled()) {
            try {
                return bitmap.getAllocationByteCount();
            } catch (NullPointerException unused) {
                return bitmap.getRowBytes() * bitmap.getHeight();
            }
        }
        throw new IllegalStateException("Cannot obtain size for recycled Bitmap: " + bitmap + "[" + bitmap.getWidth() + "x" + bitmap.getHeight() + "] " + bitmap.getConfig());
    }

    /* renamed from: d */
    public static int m4674d(Bitmap.Config config) {
        if (config == null) {
            config = Bitmap.Config.ARGB_8888;
        }
        int i6 = a.f7532a[config.ordinal()];
        if (i6 == 1) {
            return 1;
        }
        if (i6 == 2 || i6 == 3) {
            return 2;
        }
        return i6 != 4 ? 4 : 8;
    }

    /* renamed from: e */
    public static <T> List<T> m4675e(Collection<T> collection) {
        ArrayList arrayList = new ArrayList(collection.size());
        for (T t : collection) {
            if (t != null) {
                arrayList.add(t);
            }
        }
        return arrayList;
    }

    /* renamed from: f */
    public static Handler m4676f() {
        if (f7531c == null) {
            synchronized (C1829l.class) {
                if (f7531c == null) {
                    f7531c = new Handler(Looper.getMainLooper());
                }
            }
        }
        return f7531c;
    }

    /* renamed from: g */
    public static int m4677g(Object obj, int i6) {
        return (i6 * 31) + (obj == null ? 0 : obj.hashCode());
    }

    /* renamed from: h */
    public static boolean m4678h() {
        return !m4679i();
    }

    /* renamed from: i */
    public static boolean m4679i() {
        return Looper.myLooper() == Looper.getMainLooper();
    }

    /* renamed from: j */
    public static boolean m4680j(int i6, int i7) {
        if (i6 > 0 || i6 == Integer.MIN_VALUE) {
            return i7 > 0 || i7 == Integer.MIN_VALUE;
        }
        return false;
    }

    /* renamed from: k */
    public static void m4681k(Runnable runnable) {
        m4676f().post(runnable);
    }
}
