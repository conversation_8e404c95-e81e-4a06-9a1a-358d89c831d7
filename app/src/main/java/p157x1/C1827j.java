package p157x1;

import java.io.FilterInputStream;
import java.io.InputStream;

/* renamed from: x1.j */
/* loaded from: classes.dex */
public final class C1827j extends FilterInputStream {

    /* renamed from: j */
    public int f7525j;

    public C1827j(InputStream inputStream) {
        super(inputStream);
        this.f7525j = Integer.MIN_VALUE;
    }

    /* renamed from: C */
    public final void m4668C(long j6) {
        int i6 = this.f7525j;
        if (i6 == Integer.MIN_VALUE || j6 == -1) {
            return;
        }
        this.f7525j = (int) (i6 - j6);
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public final int available() {
        int i6 = this.f7525j;
        return i6 == Integer.MIN_VALUE ? super.available() : Math.min(i6, super.available());
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public final synchronized void mark(int i6) {
        super.mark(i6);
        this.f7525j = i6;
    }

    /* renamed from: o */
    public final long m4669o(long j6) {
        int i6 = this.f7525j;
        if (i6 == 0) {
            return -1L;
        }
        return (i6 == Integer.MIN_VALUE || j6 <= ((long) i6)) ? j6 : i6;
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public final int read() {
        if (m4669o(1L) == -1) {
            return -1;
        }
        int read = super.read();
        m4668C(1L);
        return read;
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public final int read(byte[] bArr, int i6, int i7) {
        int m4669o = (int) m4669o(i7);
        if (m4669o == -1) {
            return -1;
        }
        int read = super.read(bArr, i6, m4669o);
        m4668C(read);
        return read;
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public final synchronized void reset() {
        super.reset();
        this.f7525j = Integer.MIN_VALUE;
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public final long skip(long j6) {
        long m4669o = m4669o(j6);
        if (m4669o == -1) {
            return 0L;
        }
        long skip = super.skip(m4669o);
        m4668C(skip);
        return skip;
    }
}
