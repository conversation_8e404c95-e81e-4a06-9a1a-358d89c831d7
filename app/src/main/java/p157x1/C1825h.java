package p157x1;

import android.os.SystemClock;

/* renamed from: x1.h */
/* loaded from: classes.dex */
public final class C1825h {

    /* renamed from: a */
    public static final double f7518a = 1.0d / Math.pow(10.0d, 6.0d);

    /* renamed from: b */
    public static final /* synthetic */ int f7519b = 0;

    /* renamed from: a */
    public static double m4664a(long j6) {
        return (SystemClock.elapsedRealtimeNanos() - j6) * f7518a;
    }
}
