package p157x1;

import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

/* renamed from: x1.i */
/* loaded from: classes.dex */
public class C1826i<T, Y> {

    /* renamed from: a */
    public final Map<T, a<Y>> f7520a = new LinkedHashMap(100, 0.75f, true);

    /* renamed from: b */
    public long f7521b;

    /* renamed from: c */
    public long f7522c;

    /* renamed from: x1.i$a */
    public static final class a<Y> {

        /* renamed from: a */
        public final Y f7523a;

        /* renamed from: b */
        public final int f7524b;

        public a(Y y2, int i6) {
            this.f7523a = y2;
            this.f7524b = i6;
        }
    }

    public C1826i(long j6) {
        this.f7521b = j6;
    }

    /* renamed from: a */
    public final synchronized Y m4665a(T t) {
        a aVar;
        aVar = (a) this.f7520a.get(t);
        return aVar != null ? aVar.f7523a : null;
    }

    /* renamed from: b */
    public int mo2348b(Y y2) {
        return 1;
    }

    /* renamed from: c */
    public void mo2349c(T t, Y y2) {
    }

    /* renamed from: d */
    public final synchronized Y m4666d(T t, Y y2) {
        int mo2348b = mo2348b(y2);
        long j6 = mo2348b;
        if (j6 >= this.f7521b) {
            mo2349c(t, y2);
            return null;
        }
        if (y2 != null) {
            this.f7522c += j6;
        }
        a<Y> put = this.f7520a.put(t, y2 == null ? null : new a<>(y2, mo2348b));
        if (put != null) {
            this.f7522c -= put.f7524b;
            if (!put.f7523a.equals(y2)) {
                mo2349c(t, put.f7523a);
            }
        }
        m4667e(this.f7521b);
        return put != null ? put.f7523a : null;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* renamed from: e */
    public final synchronized void m4667e(long j6) {
        while (this.f7522c > j6) {
            Iterator it = this.f7520a.entrySet().iterator();
            Map.Entry entry = (Map.Entry) it.next();
            a aVar = (a) entry.getValue();
            this.f7522c -= aVar.f7524b;
            Object key = entry.getKey();
            it.remove();
            mo2349c(key, aVar.f7523a);
        }
    }
}
