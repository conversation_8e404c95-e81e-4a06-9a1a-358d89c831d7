package p157x1;

import androidx.activity.result.C0052a;

/* renamed from: x1.k */
/* loaded from: classes.dex */
public final class C1828k {

    /* renamed from: a */
    public Class<?> f7526a;

    /* renamed from: b */
    public Class<?> f7527b;

    /* renamed from: c */
    public Class<?> f7528c;

    public C1828k() {
    }

    public C1828k(Class<?> cls, Class<?> cls2, Class<?> cls3) {
        m4670a(cls, cls2, cls3);
    }

    /* renamed from: a */
    public final void m4670a(Class<?> cls, Class<?> cls2, Class<?> cls3) {
        this.f7526a = cls;
        this.f7527b = cls2;
        this.f7528c = cls3;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || C1828k.class != obj.getClass()) {
            return false;
        }
        C1828k c1828k = (C1828k) obj;
        return this.f7526a.equals(c1828k.f7526a) && this.f7527b.equals(c1828k.f7527b) && C1829l.m4672b(this.f7528c, c1828k.f7528c);
    }

    public final int hashCode() {
        int hashCode = (this.f7527b.hashCode() + (this.f7526a.hashCode() * 31)) * 31;
        Class<?> cls = this.f7528c;
        return hashCode + (cls != null ? cls.hashCode() : 0);
    }

    public final String toString() {
        StringBuilder m104h = C0052a.m104h("MultiClassKey{first=");
        m104h.append(this.f7526a);
        m104h.append(", second=");
        m104h.append(this.f7527b);
        m104h.append('}');
        return m104h.toString();
    }
}
