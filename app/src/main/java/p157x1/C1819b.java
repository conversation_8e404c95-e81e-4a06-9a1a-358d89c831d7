package p157x1;

import p077l.C1047a;
import p077l.C1053g;

/* renamed from: x1.b */
/* loaded from: classes.dex */
public final class C1819b<K, V> extends C1047a<K, V> {

    /* renamed from: r */
    public int f7508r;

    @Override // p077l.C1053g, java.util.Map
    public final void clear() {
        this.f7508r = 0;
        super.clear();
    }

    @Override // p077l.C1053g, java.util.Map
    public final int hashCode() {
        if (this.f7508r == 0) {
            this.f7508r = super.hashCode();
        }
        return this.f7508r;
    }

    @Override // p077l.C1053g
    /* renamed from: i */
    public final void mo2691i(C1053g<? extends K, ? extends V> c1053g) {
        this.f7508r = 0;
        super.mo2691i(c1053g);
    }

    @Override // p077l.C1053g
    /* renamed from: j */
    public final V mo2692j(int i6) {
        this.f7508r = 0;
        return (V) super.mo2692j(i6);
    }

    @Override // p077l.C1053g
    /* renamed from: k */
    public final V mo2693k(int i6, V v6) {
        this.f7508r = 0;
        return (V) super.mo2693k(i6, v6);
    }

    @Override // p077l.C1053g, java.util.Map
    public final V put(K k6, V v6) {
        this.f7508r = 0;
        return (V) super.put(k6, v6);
    }
}
