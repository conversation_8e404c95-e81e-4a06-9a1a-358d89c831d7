package p157x1;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayDeque;
import java.util.Queue;

/* renamed from: x1.d */
/* loaded from: classes.dex */
public final class C1821d extends InputStream {

    /* renamed from: l */
    public static final Queue<C1821d> f7511l;

    /* renamed from: j */
    public InputStream f7512j;

    /* renamed from: k */
    public IOException f7513k;

    static {
        char[] cArr = C1829l.f7529a;
        f7511l = new ArrayDeque(0);
    }

    @Override // java.io.InputStream
    public final int available() {
        return this.f7512j.available();
    }

    @Override // java.io.InputStream, java.io.Closeable, java.lang.AutoCloseable
    public final void close() {
        this.f7512j.close();
    }

    @Override // java.io.InputStream
    public final void mark(int i6) {
        this.f7512j.mark(i6);
    }

    @Override // java.io.InputStream
    public final boolean markSupported() {
        return this.f7512j.markSupported();
    }

    @Override // java.io.InputStream
    public final int read() {
        try {
            return this.f7512j.read();
        } catch (IOException e6) {
            this.f7513k = e6;
            throw e6;
        }
    }

    @Override // java.io.InputStream
    public final int read(byte[] bArr) {
        try {
            return this.f7512j.read(bArr);
        } catch (IOException e6) {
            this.f7513k = e6;
            throw e6;
        }
    }

    @Override // java.io.InputStream
    public final int read(byte[] bArr, int i6, int i7) {
        try {
            return this.f7512j.read(bArr, i6, i7);
        } catch (IOException e6) {
            this.f7513k = e6;
            throw e6;
        }
    }

    @Override // java.io.InputStream
    public final synchronized void reset() {
        this.f7512j.reset();
    }

    @Override // java.io.InputStream
    public final long skip(long j6) {
        try {
            return this.f7512j.skip(j6);
        } catch (IOException e6) {
            this.f7513k = e6;
            throw e6;
        }
    }
}
