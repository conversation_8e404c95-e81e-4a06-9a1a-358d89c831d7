package p157x1;

import androidx.activity.result.C0052a;
import java.io.FilterInputStream;
import java.io.IOException;
import java.io.InputStream;

/* renamed from: x1.c */
/* loaded from: classes.dex */
public final class C1820c extends FilterInputStream {

    /* renamed from: j */
    public final long f7509j;

    /* renamed from: k */
    public int f7510k;

    public C1820c(InputStream inputStream, long j6) {
        super(inputStream);
        this.f7509j = j6;
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public final synchronized int available() {
        return (int) Math.max(this.f7509j - this.f7510k, ((FilterInputStream) this).in.available());
    }

    /* renamed from: o */
    public final int m4663o(int i6) {
        if (i6 >= 0) {
            this.f7510k += i6;
        } else if (this.f7509j - this.f7510k > 0) {
            StringBuilder m104h = C0052a.m104h("Failed to read all expected data, expected: ");
            m104h.append(this.f7509j);
            m104h.append(", but read: ");
            m104h.append(this.f7510k);
            throw new IOException(m104h.toString());
        }
        return i6;
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public final synchronized int read() {
        int read;
        read = super.read();
        m4663o(read >= 0 ? 1 : -1);
        return read;
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public final int read(byte[] bArr) {
        return read(bArr, 0, bArr.length);
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public final synchronized int read(byte[] bArr, int i6, int i7) {
        int read;
        read = super.read(bArr, i6, i7);
        m4663o(read);
        return read;
    }
}
