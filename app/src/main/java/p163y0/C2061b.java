package p163y0;

import android.animation.LayoutTransition;
import android.view.View;
import android.view.ViewGroup;
import androidx.recyclerview.widget.LinearLayoutManager;

/* renamed from: y0.b */
/* loaded from: classes.dex */
public final class C2061b {

    /* renamed from: b */
    public static final ViewGroup.MarginLayoutParams f8315b;

    /* renamed from: a */
    public LinearLayoutManager f8316a;

    static {
        ViewGroup.MarginLayoutParams marginLayoutParams = new ViewGroup.MarginLayoutParams(-1, -1);
        f8315b = marginLayoutParams;
        marginLayoutParams.setMargins(0, 0, 0, 0);
    }

    public C2061b(LinearLayoutManager linearLayoutManager) {
        this.f8316a = linearLayoutManager;
    }

    /* renamed from: a */
    public static boolean m5174a(View view) {
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            LayoutTransition layoutTransition = viewGroup.getLayoutTransition();
            if (layoutTransition != null && layoutTransition.isChangingLayout()) {
                return true;
            }
            int childCount = viewGroup.getChildCount();
            for (int i6 = 0; i6 < childCount; i6++) {
                if (m5174a(viewGroup.getChildAt(i6))) {
                    return true;
                }
            }
        }
        return false;
    }
}
