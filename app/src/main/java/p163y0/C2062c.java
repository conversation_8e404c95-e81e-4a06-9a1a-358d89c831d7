package p163y0;

import android.view.View;
import android.view.ViewGroup;
import androidx.recyclerview.widget.RecyclerView;

/* renamed from: y0.c */
/* loaded from: classes.dex */
public final class C2062c implements RecyclerView.InterfaceC0290o {
    @Override // androidx.recyclerview.widget.RecyclerView.InterfaceC0290o
    /* renamed from: a */
    public final void mo1168a(View view) {
        RecyclerView.C0289n c0289n = (RecyclerView.C0289n) view.getLayoutParams();
        if (((ViewGroup.MarginLayoutParams) c0289n).width != -1 || ((ViewGroup.MarginLayoutParams) c0289n).height != -1) {
            throw new IllegalStateException("Pages must fill the whole ViewPager2 (use match_parent)");
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.InterfaceC0290o
    /* renamed from: b */
    public final void mo1169b() {
    }
}
