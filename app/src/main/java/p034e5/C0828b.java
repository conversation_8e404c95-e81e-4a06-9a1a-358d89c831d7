package p034e5;

import android.util.SparseArray;
import android.view.View;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 通用ViewHolder基类
 * 提供了视图缓存功能，避免重复的findViewById调用，提高性能
 */
public class BaseViewHolder extends RecyclerView.ViewHolder {

    // 视图缓存，使用SparseArray提高查找效率
    private SparseArray<View> viewCache;

    // 根视图
    public View itemView;

    /**
     * 构造函数
     * @param itemView 根视图
     */
    public BaseViewHolder(View itemView) {
        super(itemView);
        this.itemView = itemView;
        this.viewCache = new SparseArray<>();
    }

    /**
     * 根据ID查找视图，带缓存功能
     * 第一次查找时会缓存结果，后续查找直接从缓存中获取
     *
     * @param viewId 视图ID
     * @param <T> 视图类型
     * @return 找到的视图
     */
    @SuppressWarnings("unchecked")
    public <T extends View> T findViewById(int viewId) {
        // 先从缓存中查找
        T cachedView = (T) viewCache.get(viewId);
        if (cachedView != null) {
            return cachedView;
        }

        // 缓存中没有，从根视图中查找
        T foundView = (T) itemView.findViewById(viewId);
        if (foundView != null) {
            // 将找到的视图放入缓存
            viewCache.put(viewId, foundView);
        }

        return foundView;
    }

    /**
     * 获取ViewHolder的位置
     * @return 位置索引
     */
    public int getPosition() {
        return getAdapterPosition();
    }

    /**
     * 设置根视图的点击监听器
     * @param listener 点击监听器
     */
    public void setOnClickListener(View.OnClickListener listener) {
        itemView.setOnClickListener(listener);
    }

    /**
     * 设置根视图的长按监听器
     * @param listener 长按监听器
     */
    public void setOnLongClickListener(View.OnLongClickListener listener) {
        itemView.setOnLongClickListener(listener);
    }

    /**
     * 设置指定视图的点击监听器
     * @param viewId 视图ID
     * @param listener 点击监听器
     */
    public void setOnClickListener(int viewId, View.OnClickListener listener) {
        View view = findViewById(viewId);
        if (view != null) {
            view.setOnClickListener(listener);
        }
    }

    /**
     * 设置指定视图的长按监听器
     * @param viewId 视图ID
     * @param listener 长按监听器
     */
    public void setOnLongClickListener(int viewId, View.OnLongClickListener listener) {
        View view = findViewById(viewId);
        if (view != null) {
            view.setOnLongClickListener(listener);
        }
    }

    /**
     * 设置视图的可见性
     * @param viewId 视图ID
     * @param visibility 可见性（View.VISIBLE, View.GONE, View.INVISIBLE）
     */
    public void setVisibility(int viewId, int visibility) {
        View view = findViewById(viewId);
        if (view != null) {
            view.setVisibility(visibility);
        }
    }

    /**
     * 设置视图是否可用
     * @param viewId 视图ID
     * @param enabled 是否可用
     */
    public void setEnabled(int viewId, boolean enabled) {
        View view = findViewById(viewId);
        if (view != null) {
            view.setEnabled(enabled);
        }
    }

    /**
     * 清空视图缓存
     * 在ViewHolder被回收时可以调用此方法释放内存
     */
    public void clearViewCache() {
        if (viewCache != null) {
            viewCache.clear();
        }
    }

    /**
     * 获取缓存的视图数量
     * @return 缓存的视图数量
     */
    public int getCachedViewCount() {
        return viewCache != null ? viewCache.size() : 0;
    }

    // Getter 方法

    public View getItemView() {
        return itemView;
    }

    public SparseArray<View> getViewCache() {
        return viewCache;
    }
}
