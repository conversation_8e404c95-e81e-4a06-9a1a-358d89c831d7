package p034e5;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import androidx.recyclerview.widget.RecyclerView;
import java.util.List;

/* renamed from: e5.a */
/* loaded from: classes.dex */
public abstract class AbstractC0827a<T> extends RecyclerView.AbstractC0280e<C0828b> {

    /* renamed from: c */
    public Context f4244c;

    /* renamed from: d */
    public int f4245d;

    /* renamed from: e */
    public List<T> f4246e;

    public AbstractC0827a(Context context, int i6, List<T> list) {
        this.f4244c = context;
        this.f4245d = i6;
        this.f4246e = list;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0280e
    /* renamed from: c */
    public final int mo1081c() {
        List<T> list = this.f4246e;
        if (list != null) {
            return list.size();
        }
        return 0;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0280e
    /* renamed from: g */
    public final void mo1085g(C0828b c0828b, int i6) {
        mo1485o(c0828b, this.f4246e.get(i6), i6);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0280e
    /* renamed from: h */
    public final RecyclerView.AbstractC0277b0 mo1086h(ViewGroup viewGroup) {
        Context context = this.f4244c;
        int i6 = this.f4245d;
        int i7 = C0828b.f4247v;
        return new C0828b(LayoutInflater.from(context).inflate(i6, viewGroup, false));
    }

    /* renamed from: o */
    public abstract void mo1485o(C0828b c0828b, T t, int i6);

    /* renamed from: p */
    public final Object m2310p(int i6) {
        return this.f4246e.get(i6);
    }
}
