package p034e5;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import androidx.recyclerview.widget.RecyclerView;
import java.util.List;

/**
 * 通用RecyclerView适配器抽象基类
 * 提供了基础的适配器功能，简化了RecyclerView适配器的实现
 *
 * @param <T> 数据项的类型
 */
public abstract class BaseRecyclerViewAdapter<T> extends RecyclerView.Adapter<BaseViewHolder> {

    // 上下文对象
    protected Context context;

    // 布局资源ID
    protected int layoutResourceId;

    // 数据列表
    public List<T> dataList;

    /**
     * 构造函数
     * @param context 上下文对象
     * @param layoutResourceId 布局资源ID
     * @param dataList 数据列表
     */
    public BaseRecyclerViewAdapter(Context context, int layoutResourceId, List<T> dataList) {
        this.context = context;
        this.layoutResourceId = layoutResourceId;
        this.dataList = dataList;
    }

    @Override
    public int getItemCount() {
        return dataList != null ? dataList.size() : 0;
    }

    @Override
    public void onBindViewHolder(BaseViewHolder holder, int position) {
        if (dataList != null && position >= 0 && position < dataList.size()) {
            bindViewHolder(holder, dataList.get(position), position);
        }
    }

    @Override
    public BaseViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new BaseViewHolder(LayoutInflater.from(context).inflate(layoutResourceId, parent, false));
    }

    /**
     * 绑定视图数据的抽象方法
     * 子类必须实现此方法来绑定具体的数据到视图
     *
     * @param holder 视图持有者
     * @param item 数据项
     * @param position 位置
     */
    public abstract void bindViewHolder(BaseViewHolder holder, T item, int position);

    /**
     * 获取指定位置的数据项
     * @param position 位置
     * @return 数据项
     */
    public T getItem(int position) {
        if (dataList != null && position >= 0 && position < dataList.size()) {
            return dataList.get(position);
        }
        return null;
    }

    /**
     * 设置新的数据列表
     * @param newDataList 新的数据列表
     */
    public void setDataList(List<T> newDataList) {
        this.dataList = newDataList;
        notifyDataSetChanged();
    }

    /**
     * 添加数据项
     * @param item 要添加的数据项
     */
    public void addItem(T item) {
        if (dataList != null) {
            dataList.add(item);
            notifyItemInserted(dataList.size() - 1);
        }
    }

    /**
     * 在指定位置添加数据项
     * @param position 位置
     * @param item 要添加的数据项
     */
    public void addItem(int position, T item) {
        if (dataList != null && position >= 0 && position <= dataList.size()) {
            dataList.add(position, item);
            notifyItemInserted(position);
        }
    }

    /**
     * 移除指定位置的数据项
     * @param position 位置
     */
    public void removeItem(int position) {
        if (dataList != null && position >= 0 && position < dataList.size()) {
            dataList.remove(position);
            notifyItemRemoved(position);
        }
    }

    /**
     * 移除指定的数据项
     * @param item 要移除的数据项
     */
    public void removeItem(T item) {
        if (dataList != null) {
            int position = dataList.indexOf(item);
            if (position >= 0) {
                removeItem(position);
            }
        }
    }

    /**
     * 清空所有数据
     */
    public void clearData() {
        if (dataList != null) {
            int size = dataList.size();
            dataList.clear();
            notifyItemRangeRemoved(0, size);
        }
    }

    /**
     * 更新指定位置的数据项
     * @param position 位置
     * @param item 新的数据项
     */
    public void updateItem(int position, T item) {
        if (dataList != null && position >= 0 && position < dataList.size()) {
            dataList.set(position, item);
            notifyItemChanged(position);
        }
    }

    /**
     * 检查数据列表是否为空
     * @return 是否为空
     */
    public boolean isEmpty() {
        return dataList == null || dataList.isEmpty();
    }

    // Getter 方法

    public Context getContext() {
        return context;
    }

    public int getLayoutResourceId() {
        return layoutResourceId;
    }

    public List<T> getDataList() {
        return dataList;
    }
}
