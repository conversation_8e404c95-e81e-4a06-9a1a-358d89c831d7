package p115q4;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import androidx.activity.result.C0052a;
import java.util.Objects;
import p021d.ActivityC0680e;
import p133t4.C1405b;
import p140u4.C1429b;
import p140u4.InterfaceC1428a;
import p173z4.C2123j;
import p173z4.InterfaceC2115b;

/* renamed from: q4.a */
/* loaded from: classes.dex */
public abstract class AbstractActivityC1295a extends ActivityC0680e {

    /* renamed from: w */
    public C2123j f6078w;

    /* renamed from: x */
    public a f6079x = new a();

    /* renamed from: q4.a$a */
    public class a implements InterfaceC2115b {
        public a() {
        }

        @Override // p173z4.InterfaceC2115b
        /* renamed from: i */
        public final void mo80i(int i6) {
            C0052a.m105i("notifyRequestPermissions requestCode:", i6, "PermissionsActivity");
            AbstractActivityC1295a abstractActivityC1295a = AbstractActivityC1295a.this;
            abstractActivityC1295a.f6078w.m5317q(abstractActivityC1295a, i6);
        }
    }

    @Override // androidx.fragment.app.ActivityC0229p, androidx.activity.ComponentActivity, android.app.Activity
    public final void onActivityResult(int i6, int i7, Intent intent) {
        super.onActivityResult(i6, i7, intent);
        Log.d("PermissionsActivity", "onActivityResult requestCode:" + i6 + ",resultCode:" + i7 + ",data:" + intent);
        C1405b c1405b = this.f6078w.f8495j;
        Objects.requireNonNull(c1405b);
        Log.d("PermissionsManager", "onActivityResult requestCode:" + i6 + ",resultCode:" + i7 + ",data:" + intent);
        c1405b.f6479d.m3568d(i6, i7, intent);
    }

    @Override // androidx.fragment.app.ActivityC0229p, androidx.activity.ComponentActivity, p135u.ActivityC1410d, android.app.Activity
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        this.f6078w = C2123j.h.f8521a;
    }

    @Override // p021d.ActivityC0680e, androidx.fragment.app.ActivityC0229p, android.app.Activity
    public void onDestroy() {
        super.onDestroy();
    }

    @Override // p021d.ActivityC0680e, android.app.Activity, android.view.KeyEvent.Callback
    public boolean onKeyDown(int i6, KeyEvent keyEvent) {
        if (this.f6078w.m5316p(i6)) {
            return true;
        }
        return super.onKeyDown(i6, keyEvent);
    }

    @Override // android.app.Activity, android.view.KeyEvent.Callback
    public final boolean onKeyUp(int i6, KeyEvent keyEvent) {
        if (this.f6078w.m5316p(i6)) {
            return true;
        }
        return super.onKeyUp(i6, keyEvent);
    }

    @Override // androidx.fragment.app.ActivityC0229p, android.app.Activity
    public void onPause() {
        super.onPause();
        this.f6078w.m5303d(this.f6079x);
    }

    @Override // androidx.fragment.app.ActivityC0229p, androidx.activity.ComponentActivity, android.app.Activity
    public void onRequestPermissionsResult(int i6, String[] strArr, int[] iArr) {
        InterfaceC1428a interfaceC1428a;
        super.onRequestPermissionsResult(i6, strArr, iArr);
        C0052a.m105i("onRequestPermissionsResult requestCode:", i6, "PermissionsActivity");
        C1429b c1429b = this.f6078w.f8495j.f6480e;
        Objects.requireNonNull(c1429b);
        if (i6 == 10) {
            boolean z5 = false;
            if (iArr.length <= 0 || iArr[0] != 0) {
                interfaceC1428a = c1429b.f6550b;
            } else {
                interfaceC1428a = c1429b.f6550b;
                z5 = true;
            }
            interfaceC1428a.mo1907a(z5);
        }
    }

    @Override // androidx.fragment.app.ActivityC0229p, android.app.Activity
    public void onResume() {
        super.onResume();
        this.f6078w.m5302c(this.f6079x);
    }
}
