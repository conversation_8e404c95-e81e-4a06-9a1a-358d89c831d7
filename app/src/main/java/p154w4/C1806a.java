package p154w4;

import android.content.Context;
import android.util.Log;
import androidx.activity.result.C0052a;
import java.util.concurrent.Executors;
import p088m4.C1085a;
import p142v.C1450a;
import p160x4.C2053c;
import p160x4.InterfaceC2054d;
import p160x4.RunnableC2052b;
import p166y4.C2069b;
import p166y4.InterfaceC2068a;

/* renamed from: w4.a */
/* loaded from: classes.dex */
public final class C1806a {

    /* renamed from: a */
    public C2069b f7462a;

    /* renamed from: b */
    public C2053c f7463b;

    /* renamed from: c */
    public C1085a f7464c;

    /* renamed from: d */
    public a f7465d = new a();

    /* renamed from: e */
    public b f7466e = new b();

    /* renamed from: w4.a$a */
    public class a implements InterfaceC2068a {
        public a() {
        }
    }

    /* renamed from: w4.a$b */
    public class b implements InterfaceC2054d {
        public b() {
        }
    }

    public C1806a(Context context, C1085a c1085a) {
        this.f7464c = c1085a;
        C2069b c2069b = new C2069b(context);
        this.f7462a = c2069b;
        c2069b.f8334l = this.f7465d;
        C2053c c2053c = new C2053c(context);
        this.f7463b = c2053c;
        b bVar = this.f7466e;
        Log.e("AudioRecorder", "setOnRecordAudioDataListener onRecordAudioDataListener:" + bVar);
        c2053c.f8303c = bVar;
    }

    /* renamed from: a */
    public final void m4618a() {
        C1085a c1085a = this.f7464c;
        if (c1085a != null) {
            C2069b c2069b = this.f7462a;
            c1085a.m2778i(c2069b.f8323a, c2069b.f8324b);
        }
        C2069b c2069b2 = this.f7462a;
        if (c2069b2.f8330h.get()) {
            return;
        }
        c2069b2.f8330h.set(true);
        if (c2069b2.f8335m == null) {
            c2069b2.f8335m = Executors.newSingleThreadExecutor();
        }
        c2069b2.f8335m.execute(c2069b2.f8339q);
    }

    /* renamed from: b */
    public final void m4619b() {
        C1085a c1085a = this.f7464c;
        if (c1085a != null) {
            c1085a.m2776g();
        }
        C2053c c2053c = this.f7463b;
        int i6 = 0;
        boolean z5 = C1450a.m3520a(c2053c.f8304d, "android.permission.RECORD_AUDIO") == 0;
        StringBuilder m104h = C0052a.m104h("startRecord 开始录音 isRunning:");
        m104h.append(c2053c.f8302b.get());
        m104h.append(",hasPermission:");
        m104h.append(z5);
        Log.d("AudioRecorder", m104h.toString());
        if (z5 && !c2053c.f8302b.get()) {
            if (c2053c.f8308h == null) {
                c2053c.f8308h = Executors.newSingleThreadExecutor();
            }
            c2053c.f8308h.execute(new RunnableC2052b(c2053c, i6));
        }
    }

    /* renamed from: c */
    public final void m4620c() {
        C1085a c1085a = this.f7464c;
        if (c1085a != null) {
            c1085a.m2779j();
        }
        this.f7463b.m5167e();
    }
}
