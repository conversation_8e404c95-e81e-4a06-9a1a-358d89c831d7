package android.support.v4.graphics.drawable;

import androidx.core.graphics.drawable.IconCompat;
import p143v0.AbstractC1451a;

/* loaded from: classes.dex */
public final class IconCompatParcelizer extends androidx.core.graphics.drawable.IconCompatParcelizer {
    public static IconCompat read(AbstractC1451a abstractC1451a) {
        return androidx.core.graphics.drawable.IconCompatParcelizer.read(abstractC1451a);
    }

    public static void write(IconCompat iconCompat, AbstractC1451a abstractC1451a) {
        androidx.core.graphics.drawable.IconCompatParcelizer.write(iconCompat, abstractC1451a);
    }
}
