package android.support.v4.app;

import androidx.core.app.RemoteActionCompat;
import p143v0.AbstractC1451a;

/* loaded from: classes.dex */
public final class RemoteActionCompatParcelizer extends androidx.core.app.RemoteActionCompatParcelizer {
    public static RemoteActionCompat read(AbstractC1451a abstractC1451a) {
        return androidx.core.app.RemoteActionCompatParcelizer.read(abstractC1451a);
    }

    public static void write(RemoteActionCompat remoteActionCompat, AbstractC1451a abstractC1451a) {
        androidx.core.app.RemoteActionCompatParcelizer.write(remoteActionCompat, abstractC1451a);
    }
}
