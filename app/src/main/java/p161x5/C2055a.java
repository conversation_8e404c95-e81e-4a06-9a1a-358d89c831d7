package p161x5;

import androidx.activity.result.AbstractC0055d;
import java.security.GeneralSecurityException;
import java.security.cert.Certificate;
import java.security.cert.X509Certificate;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import javax.net.ssl.SSLPeerUnverifiedException;

/* renamed from: x5.a */
/* loaded from: classes.dex */
public final class C2055a extends AbstractC0055d {

    /* renamed from: a */
    public final InterfaceC2058d f8312a;

    public C2055a(InterfaceC2058d interfaceC2058d) {
        this.f8312a = interfaceC2058d;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        return (obj instanceof C2055a) && ((C2055a) obj).f8312a.equals(this.f8312a);
    }

    @Override // androidx.activity.result.AbstractC0055d
    /* renamed from: f */
    public final List<Certificate> mo114f(List<Certificate> list, String str) {
        ArrayDeque arrayDeque = new ArrayDeque(list);
        ArrayList arrayList = new ArrayList();
        arrayList.add(arrayDeque.removeFirst());
        boolean z5 = false;
        for (int i6 = 0; i6 < 9; i6++) {
            X509Certificate x509Certificate = (X509Certificate) arrayList.get(arrayList.size() - 1);
            X509Certificate mo3579a = this.f8312a.mo3579a(x509Certificate);
            if (mo3579a == null) {
                Iterator it = arrayDeque.iterator();
                while (it.hasNext()) {
                    X509Certificate x509Certificate2 = (X509Certificate) it.next();
                    if (m5168r(x509Certificate, x509Certificate2)) {
                        it.remove();
                        arrayList.add(x509Certificate2);
                    }
                }
                if (z5) {
                    return arrayList;
                }
                throw new SSLPeerUnverifiedException("Failed to find a trusted cert that signed " + x509Certificate);
            }
            if (arrayList.size() > 1 || !x509Certificate.equals(mo3579a)) {
                arrayList.add(mo3579a);
            }
            if (m5168r(mo3579a, mo3579a)) {
                return arrayList;
            }
            z5 = true;
        }
        throw new SSLPeerUnverifiedException("Certificate chain too long: " + arrayList);
    }

    public final int hashCode() {
        return this.f8312a.hashCode();
    }

    /* renamed from: r */
    public final boolean m5168r(X509Certificate x509Certificate, X509Certificate x509Certificate2) {
        if (!x509Certificate.getIssuerDN().equals(x509Certificate2.getSubjectDN())) {
            return false;
        }
        try {
            x509Certificate.verify(x509Certificate2.getPublicKey());
            return true;
        } catch (GeneralSecurityException unused) {
            return false;
        }
    }
}
