package p161x5;

import java.security.cert.X509Certificate;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;
import javax.security.auth.x500.X500Principal;

/* renamed from: x5.b */
/* loaded from: classes.dex */
public final class C2056b implements InterfaceC2058d {

    /* renamed from: a */
    public final Map<X500Principal, Set<X509Certificate>> f8313a = new LinkedHashMap();

    /* JADX WARN: Type inference failed for: r4v0, types: [java.util.LinkedHashMap, java.util.Map<javax.security.auth.x500.X500Principal, java.util.Set<java.security.cert.X509Certificate>>] */
    public C2056b(X509Certificate... x509CertificateArr) {
        for (X509Certificate x509Certificate : x509CertificateArr) {
            X500Principal subjectX500Principal = x509Certificate.getSubjectX500Principal();
            Set<X509Certificate> set = (Set) this.f8313a.get(subjectX500Principal);
            if (set == null) {
                set = new LinkedHashSet<>(1);
                this.f8313a.put(subjectX500Principal, set);
            }
            set.add(x509Certificate);
        }
    }

    /* JADX WARN: Type inference failed for: r1v0, types: [java.util.LinkedHashMap, java.util.Map<javax.security.auth.x500.X500Principal, java.util.Set<java.security.cert.X509Certificate>>] */
    @Override // p161x5.InterfaceC2058d
    /* renamed from: a */
    public final X509Certificate mo3579a(X509Certificate x509Certificate) {
        Set<X509Certificate> set = (Set) this.f8313a.get(x509Certificate.getIssuerX500Principal());
        if (set == null) {
            return null;
        }
        for (X509Certificate x509Certificate2 : set) {
            try {
                x509Certificate.verify(x509Certificate2.getPublicKey());
                return x509Certificate2;
            } catch (Exception unused) {
            }
        }
        return null;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        return (obj instanceof C2056b) && ((C2056b) obj).f8313a.equals(this.f8313a);
    }

    public final int hashCode() {
        return this.f8313a.hashCode();
    }
}
