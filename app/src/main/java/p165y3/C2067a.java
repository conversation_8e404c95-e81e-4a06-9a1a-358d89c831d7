package p165y3;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import androidx.activity.result.C0052a;
import p064j0.AbstractC0956a;
import p077l.C1053g;

/* renamed from: y3.a */
/* loaded from: classes.dex */
public final class C2067a extends AbstractC0956a {
    public static final Parcelable.Creator<C2067a> CREATOR = new a();

    /* renamed from: l */
    public final C1053g<String, Bundle> f8322l;

    /* renamed from: y3.a$a */
    public static class a implements Parcelable.ClassLoaderCreator<C2067a> {
        @Override // android.os.Parcelable.Creator
        public final Object createFromParcel(Parcel parcel) {
            return new C2067a(parcel, null);
        }

        @Override // android.os.Parcelable.Creator
        public final Object[] newArray(int i6) {
            return new C2067a[i6];
        }

        @Override // android.os.Parcelable.ClassLoaderCreator
        public final C2067a createFromParcel(Parcel parcel, ClassLoader classLoader) {
            return new C2067a(parcel, classLoader);
        }
    }

    public C2067a(Parcel parcel, ClassLoader classLoader) {
        super(parcel, classLoader);
        int readInt = parcel.readInt();
        String[] strArr = new String[readInt];
        parcel.readStringArray(strArr);
        Bundle[] bundleArr = new Bundle[readInt];
        parcel.readTypedArray(bundleArr, Bundle.CREATOR);
        this.f8322l = new C1053g<>(readInt);
        for (int i6 = 0; i6 < readInt; i6++) {
            this.f8322l.put(strArr[i6], bundleArr[i6]);
        }
    }

    public final String toString() {
        StringBuilder m104h = C0052a.m104h("ExtendableSavedState{");
        m104h.append(Integer.toHexString(System.identityHashCode(this)));
        m104h.append(" states=");
        m104h.append(this.f8322l);
        m104h.append("}");
        return m104h.toString();
    }

    @Override // p064j0.AbstractC0956a, android.os.Parcelable
    public final void writeToParcel(Parcel parcel, int i6) {
        parcel.writeParcelable(this.f4731j, i6);
        int i7 = this.f8322l.f5038l;
        parcel.writeInt(i7);
        String[] strArr = new String[i7];
        Bundle[] bundleArr = new Bundle[i7];
        for (int i8 = 0; i8 < i7; i8++) {
            strArr[i8] = this.f8322l.m2690h(i8);
            bundleArr[i8] = this.f8322l.m2694l(i8);
        }
        parcel.writeStringArray(strArr);
        parcel.writeTypedArray(bundleArr, 0);
    }
}
