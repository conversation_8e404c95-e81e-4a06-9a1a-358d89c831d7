package p155w5;

import java.io.InputStream;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicBoolean;
import p109p5.C1246c;
import p167y5.C2081k;
import p167y5.C2084n;
import p167y5.C2087q;
import p167y5.C2093w;

/* renamed from: w5.a */
/* loaded from: classes.dex */
public final class C1807a {

    /* renamed from: e */
    public static final byte[] f7469e = {42};

    /* renamed from: f */
    public static final String[] f7470f = new String[0];

    /* renamed from: g */
    public static final String[] f7471g = {"*"};

    /* renamed from: h */
    public static final C1807a f7472h = new C1807a();

    /* renamed from: a */
    public final AtomicBoolean f7473a = new AtomicBoolean(false);

    /* renamed from: b */
    public final CountDownLatch f7474b = new CountDownLatch(1);

    /* renamed from: c */
    public byte[] f7475c;

    /* renamed from: d */
    public byte[] f7476d;

    /* renamed from: a */
    public static String m4621a(byte[] bArr, byte[][] bArr2, int i6) {
        int i7;
        boolean z5;
        int i8;
        int i9;
        int length = bArr.length;
        int i10 = 0;
        while (i10 < length) {
            int i11 = (i10 + length) / 2;
            while (i11 > -1 && bArr[i11] != 10) {
                i11--;
            }
            int i12 = i11 + 1;
            int i13 = 1;
            while (true) {
                i7 = i12 + i13;
                if (bArr[i7] == 10) {
                    break;
                }
                i13++;
            }
            int i14 = i7 - i12;
            int i15 = i6;
            boolean z6 = false;
            int i16 = 0;
            int i17 = 0;
            while (true) {
                if (z6) {
                    i8 = 46;
                    z5 = false;
                } else {
                    z5 = z6;
                    i8 = bArr2[i15][i16] & 255;
                }
                i9 = i8 - (bArr[i12 + i17] & 255);
                if (i9 == 0) {
                    i17++;
                    i16++;
                    if (i17 == i14) {
                        break;
                    }
                    if (bArr2[i15].length != i16) {
                        z6 = z5;
                    } else {
                        if (i15 == bArr2.length - 1) {
                            break;
                        }
                        i15++;
                        i16 = -1;
                        z6 = true;
                    }
                } else {
                    break;
                }
            }
            if (i9 >= 0) {
                if (i9 <= 0) {
                    int i18 = i14 - i17;
                    int length2 = bArr2[i15].length - i16;
                    while (true) {
                        i15++;
                        if (i15 >= bArr2.length) {
                            break;
                        }
                        length2 += bArr2[i15].length;
                    }
                    if (length2 >= i18) {
                        if (length2 <= i18) {
                            return new String(bArr, i12, i14, C1246c.f5906i);
                        }
                    }
                }
                i10 = i7 + 1;
            }
            length = i12 - 1;
        }
        return null;
    }

    /* renamed from: b */
    public final void m4622b() {
        InputStream resourceAsStream = C1807a.class.getResourceAsStream("publicsuffixes.gz");
        if (resourceAsStream == null) {
            return;
        }
        C2087q c2087q = new C2087q(new C2081k(C2084n.m5254c(resourceAsStream, new C2093w())));
        try {
            byte[] bArr = new byte[c2087q.readInt()];
            c2087q.m5258C(bArr);
            byte[] bArr2 = new byte[c2087q.readInt()];
            c2087q.m5258C(bArr2);
            synchronized (this) {
                this.f7475c = bArr;
                this.f7476d = bArr2;
            }
            this.f7474b.countDown();
        } finally {
            C1246c.m3116f(c2087q);
        }
    }
}
