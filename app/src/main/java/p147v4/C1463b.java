package p147v4;

import android.content.Context;
import android.content.Intent;
import android.media.projection.MediaProjection;
import android.media.projection.MediaProjectionManager;
import android.os.Build;
import android.util.Log;
import androidx.activity.result.C0052a;
import com.liaoyuan.aicast.phone.projection.service.ProjectionScreenService;

/* renamed from: v4.b */
/* loaded from: classes.dex */
public final class C1463b {

    /* renamed from: a */
    public Context f6728a;

    /* renamed from: b */
    public MediaProjectionManager f6729b;

    /* renamed from: c */
    public MediaProjection f6730c;

    /* renamed from: d */
    public int f6731d = 1;

    /* renamed from: e */
    public InterfaceC1462a f6732e;

    public C1463b(Context context) {
        StringBuilder m104h = C0052a.m104h("ScreenCapturePermissions VERSION:");
        m104h.append(Build.VERSION.SDK_INT);
        Log.d("ScreenCapturePermissions", m104h.toString());
        this.f6728a = context;
        this.f6729b = (MediaProjectionManager) context.getSystemService("media_projection");
    }

    /* renamed from: a */
    public final boolean m3565a() {
        StringBuilder m104h = C0052a.m104h("hasScreenCapturePermission mMediaProjection:");
        m104h.append(this.f6730c);
        Log.d("ScreenCapturePermissions", m104h.toString());
        return this.f6730c != null;
    }

    /* renamed from: b */
    public final void m3566b(int i6) {
        InterfaceC1462a interfaceC1462a;
        C0052a.m105i("notifyMediaProjection requestCode:", i6, "ScreenCapturePermissions");
        this.f6731d = i6;
        if (!m3565a() || (interfaceC1462a = this.f6732e) == null) {
            return;
        }
        interfaceC1462a.mo1909c(this.f6730c, this.f6731d);
    }

    /* renamed from: c */
    public final void m3567c(boolean z5) {
        InterfaceC1462a interfaceC1462a = this.f6732e;
        if (interfaceC1462a != null) {
            interfaceC1462a.mo1908b(z5);
        }
    }

    /* renamed from: d */
    public final void m3568d(int i6, int i7, Intent intent) {
        Log.d("ScreenCapturePermissions", "onActivityResult requestCode:" + i6 + ",resultCode:" + i7 + ",intent:" + intent);
        if (intent != null) {
            if (i6 == 1 || i6 == 2 || i6 == 3) {
                Intent intent2 = new Intent(this.f6728a, (Class<?>) ProjectionScreenService.class);
                intent2.putExtra("code", i7);
                intent.putExtra("requestCode", i6);
                intent2.putExtra("data", intent);
                if (Build.VERSION.SDK_INT >= 26) {
                    this.f6728a.startForegroundService(intent2);
                } else {
                    this.f6728a.startService(intent2);
                }
            }
        }
    }
}
