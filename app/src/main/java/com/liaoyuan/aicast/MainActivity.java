package com.liaoyuan.aicast;

import android.R;
import android.app.AlertDialog;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.method.LinkMovementMethod;
import android.util.Log;
import android.view.KeyEvent;
import android.widget.TextView;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.ActivityC0229p;
import androidx.fragment.app.ComponentCallbacksC0223m;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;
import com.google.android.material.tabs.C0549c;
import com.google.android.material.tabs.C0549c.a;
import com.google.android.material.tabs.TabLayout;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import p019c5.C0427c;
import p019c5.C0428d;
import p031e2.C0800p;
import p082l4.C1066b;
import p082l4.C1067c;
import p082l4.DialogInterfaceOnClickListenerC1065a;
import p115q4.AbstractActivityC1295a;
import p127s4.DialogInterfaceOnClickListenerC1343b;
import p173z4.C2123j;

/* loaded from: classes.dex */
public class MainActivity extends AbstractActivityC1295a {

    /* renamed from: A */
    public List<ComponentCallbacksC0223m> f3449A;

    /* renamed from: B */
    public C2123j f3450B;

    /* renamed from: C */
    public int[] f3451C = {R.string.menus_projection_screen, R.string.menus_settings};

    /* renamed from: D */
    public int[] f3452D = {R.drawable.ic_ctrl_projection_screen, R.drawable.ic_settings};

    /* renamed from: y */
    public TabLayout f3453y;

    /* renamed from: z */
    public ViewPager2 f3454z;

    /* renamed from: com.liaoyuan.aicast.MainActivity$a */
    public class C0662a extends FragmentStateAdapter {

        /* renamed from: k */
        public List<ComponentCallbacksC0223m> f3455k;

        public C0662a(List<ComponentCallbacksC0223m> list, ActivityC0229p activityC0229p) {
            super(activityC0229p);
            this.f3455k = list;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0280e
        /* renamed from: c */
        public final int mo1081c() {
            return this.f3455k.size();
        }
    }

    /* JADX WARN: Type inference failed for: r10v13, types: [java.util.ArrayList, java.util.List<androidx.fragment.app.m>] */
    @Override // p115q4.AbstractActivityC1295a, androidx.fragment.app.ActivityC0229p, androidx.activity.ComponentActivity, p135u.ActivityC1410d, android.app.Activity
    public final void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(R.layout.activity_main);
        C2123j c2123j = C2123j.h.f8521a;
        this.f3450B = c2123j;
        Objects.requireNonNull(c2123j);
        Log.d("ProjectionPresenter", "setMainActivity activity:" + this);
        c2123j.f8504s = this;
        m1964t((Toolbar) findViewById(R.id.toolbar_local_browser));
        this.f3453y = (TabLayout) findViewById(R.id.tb_nav);
        this.f3454z = (ViewPager2) findViewById(R.id.vp_content);
        ArrayList arrayList = new ArrayList();
        this.f3449A = arrayList;
        arrayList.add(new C0427c());
        this.f3449A.add(new C0428d());
        this.f3454z.setAdapter(new C0662a(this.f3449A, this));
        TabLayout tabLayout = this.f3453y;
        ViewPager2 viewPager2 = this.f3454z;
        C0549c c0549c = new C0549c(tabLayout, viewPager2, new C0800p(this, 3));
        if (c0549c.f3221e) {
            throw new IllegalStateException("TabLayoutMediator is already attached");
        }
        RecyclerView.AbstractC0280e<?> adapter = viewPager2.getAdapter();
        c0549c.f3220d = adapter;
        if (adapter == null) {
            throw new IllegalStateException("TabLayoutMediator attached before ViewPager2 has an adapter");
        }
        c0549c.f3221e = true;
        viewPager2.f2217l.m1391d(new C0549c.c(tabLayout));
        C0549c.d dVar = new C0549c.d(viewPager2, true);
        c0549c.f3222f = dVar;
        tabLayout.m1787a(dVar);
        C0549c.a aVar = c0549c.new a();
        c0549c.f3223g = aVar;
        c0549c.f3220d.m1091m(aVar);
        c0549c.m1826a();
        tabLayout.m1798l(viewPager2.getCurrentItem(), 0.0f, true, true);
        if (this.f3450B.f8502q.f2455a.getBoolean("showTermsConditionsAndPrivacyPolicy", false)) {
            return;
        }
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        String string = getString(R.string.terms_conditions_and_privacy_policy_explain);
        SpannableString spannableString = new SpannableString(string);
        String string2 = getString(R.string.settings_item_privacy_policy);
        int indexOf = string.indexOf(string2);
        int length = string2.length() + indexOf;
        String string3 = getString(R.string.settings_item_terms_conditions);
        int indexOf2 = string.indexOf(string3);
        int length2 = string3.length() + indexOf2;
        C1066b c1066b = new C1066b(this);
        C1067c c1067c = new C1067c(this);
        spannableString.setSpan(c1066b, indexOf, length, 33);
        spannableString.setSpan(c1067c, indexOf2, length2, 33);
        AlertDialog create = builder.setTitle(getString(R.string.terms_conditions_and_privacy_policy)).setMessage(spannableString).create();
        create.setButton(-1, getString(R.string.terms_conditions_and_privacy_policy_agree), new DialogInterfaceOnClickListenerC1343b(this, 2));
        create.setButton(-2, getString(R.string.terms_conditions_and_privacy_policy_no), new DialogInterfaceOnClickListenerC1065a(this, 0));
        create.setCanceledOnTouchOutside(false);
        create.setCancelable(false);
        create.show();
        ((TextView) create.findViewById(R.id.message)).setMovementMethod(LinkMovementMethod.getInstance());
    }

    @Override // p115q4.AbstractActivityC1295a, p021d.ActivityC0680e, android.app.Activity, android.view.KeyEvent.Callback
    public final boolean onKeyDown(int i6, KeyEvent keyEvent) {
        if (i6 != 4) {
            return super.onKeyDown(i6, keyEvent);
        }
        moveTaskToBack(true);
        return true;
    }

    @Override // p115q4.AbstractActivityC1295a, androidx.fragment.app.ActivityC0229p, android.app.Activity
    public final void onResume() {
        super.onResume();
        this.f3450B.f8498m.m2398n();
    }
}
