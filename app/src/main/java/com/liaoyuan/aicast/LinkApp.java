package com.liaoyuan.aicast;

import android.app.Application;
import android.content.Context;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.net.wifi.p2p.WifiP2pManager;
import android.os.HandlerThread;
import android.util.Log;
import com.p020ly.appupdate.download.VersionInfo;
import java.io.File;
import java.util.Objects;
import java.util.concurrent.Executors;
import org.litepal.LitePal;
import p006a5.PresentationC0040c;
import p031e2.RunnableC0801q;
import p041f5.C0865b;
import p069j5.C0975b;
import p069j5.C0977d;
import p076k5.C1043a;
import p088m4.C1085a;
import p090n.C1094g;
import p120r4.C1323b;
import p120r4.C1324c;
import p133t4.C1405b;
import p140u4.C1429b;
import p147v4.C1463b;
import p160x4.RunnableC2052b;
import p173z4.C2123j;

/* loaded from: classes.dex */
public class LinkApp extends Application {

    /* renamed from: j */
    public static final /* synthetic */ int f3448j = 0;

    @Override // android.app.Application, android.content.ComponentCallbacks
    public final void onConfigurationChanged(Configuration configuration) {
        super.onConfigurationChanged(configuration);
        Log.d("LinkApp", "App onConfigurationChanged");
        PresentationC0040c presentationC0040c = C2123j.h.f8521a.f8497l;
        if (presentationC0040c != null) {
            Log.d("ProjectionScreenPresentation", "onConfigurationChanged newConfig:" + configuration);
            presentationC0040c.getWindow().getDecorView().post(new RunnableC2052b(presentationC0040c, 1));
        }
    }

    @Override // android.app.Application
    public final void onCreate() {
        super.onCreate();
        Log.d("LinkApp", "onCreate");
        LitePal.initialize(getApplicationContext());
        C1085a c1085a = C1085a.b.f5193a;
        Context applicationContext = getApplicationContext();
        if (!c1085a.f5187a) {
            c1085a.f5187a = true;
            applicationContext.getApplicationContext();
        }
        C1324c c1324c = C1324c.a.f6165a;
        Context applicationContext2 = getApplicationContext();
        if (!c1324c.f6162b) {
            c1324c.f6162b = true;
            Context applicationContext3 = applicationContext2.getApplicationContext();
            c1324c.f6161a = applicationContext3;
            c1324c.f6163c = new C1323b(applicationContext3);
            Executors.newSingleThreadExecutor().execute(new RunnableC0801q(c1324c, 1));
            c1324c.m3238e();
            Log.d("BookmarkManager", "init");
        }
        C1405b c1405b = C1405b.c.f6485a;
        Context applicationContext4 = getApplicationContext();
        if (!c1405b.f6477b) {
            c1405b.f6477b = true;
            Context applicationContext5 = applicationContext4.getApplicationContext();
            c1405b.f6476a = applicationContext5;
            C1463b c1463b = new C1463b(applicationContext5);
            c1405b.f6479d = c1463b;
            C1405b.a aVar = c1405b.f6481f;
            Log.d("ScreenCapturePermissions", "setOnMediaProjectionChangeListener onMediaProjectionChangeListener:" + aVar);
            c1463b.f6732e = aVar;
            C1429b c1429b = new C1429b(c1405b.f6476a);
            c1405b.f6480e = c1429b;
            c1429b.f6550b = c1405b.f6482g;
            Log.d("PermissionsManager", "init");
        }
        C0865b c0865b = C0865b.k.f4401a;
        Context applicationContext6 = getApplicationContext();
        if (!c0865b.f4375b) {
            c0865b.f4375b = true;
            c0865b.f4374a = applicationContext6.getApplicationContext();
            HandlerThread handlerThread = new HandlerThread("WifiP2p-Thread");
            handlerThread.start();
            WifiP2pManager wifiP2pManager = (WifiP2pManager) c0865b.f4374a.getSystemService("wifip2p");
            c0865b.f4376c = wifiP2pManager;
            c0865b.f4377d = wifiP2pManager.initialize(c0865b.f4374a, handlerThread.getLooper(), null);
            c0865b.f4384k = c0865b.f4374a.getSharedPreferences("WifiP2pDevice", 0);
            IntentFilter intentFilter = new IntentFilter();
            c0865b.f4378e = intentFilter;
            intentFilter.addAction("android.net.wifi.p2p.STATE_CHANGED");
            c0865b.f4378e.addAction("android.net.wifi.p2p.PEERS_CHANGED");
            c0865b.f4378e.addAction("android.net.wifi.p2p.CONNECTION_STATE_CHANGE");
            c0865b.f4378e.addAction("android.net.wifi.p2p.THIS_DEVICE_CHANGED");
            Log.d("BoxWifiP2pManager", "init");
        }
        C1043a c1043a = C1043a.d.f4988a;
        Context applicationContext7 = getApplicationContext();
        Objects.requireNonNull(c1043a);
        c1043a.f4979d = applicationContext7.getApplicationContext();
        StringBuilder sb = new StringBuilder();
        sb.append(c1043a.f4979d.getFilesDir().getPath());
        String m2839c = C1094g.m2839c(sb, File.separator, "version.json");
        c1043a.f4980e = new C0977d(c1043a.f4979d, m2839c, c1043a.f4985j);
        C0975b.a aVar2 = new C0975b.a(c1043a.f4979d);
        aVar2.f4778b = "https://cpbox-abroad.oss-us-west-1.aliyuncs.com/0003/version.json";
        aVar2.f4779c = m2839c;
        aVar2.f4781e = c1043a.f4983h;
        aVar2.f4780d = false;
        c1043a.f4981f = new C0975b(aVar2);
        VersionInfo versionInfo = c1043a.f4980e.f4788f;
        if (versionInfo != null) {
            versionInfo.getUrl();
        }
        c1043a.f4979d.getFilesDir().getPath();
        Executors.newSingleThreadExecutor();
        C2123j.h.f8521a.m5309i(getApplicationContext());
        getApplicationContext();
    }
}
