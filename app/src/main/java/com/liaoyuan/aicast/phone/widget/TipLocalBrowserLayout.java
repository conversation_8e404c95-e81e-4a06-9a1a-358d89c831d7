package com.liaoyuan.aicast.phone.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.liaoyuan.aicast.R;

/**
 * 本地浏览器提示布局组件
 * 用于显示本地浏览相关的提示信息和操作按钮
 */
public class TipLocalBrowserLayout extends LinearLayout {

    // 本地浏览提示文本视图
    private TextView localBrowsingTextView;

    // 提示布局容器
    private LinearLayout tipLayoutContainer;

    public TipLocalBrowserLayout(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        initializeViews(context);
    }

    /**
     * 初始化视图组件
     */
    private void initializeViews(Context context) {
        // 加载布局文件
        View.inflate(context, R.layout.layout_browser_local_tip, this);

        // 初始化视图组件
        tipLayoutContainer = findViewById(R.id.tip_layout_browser_local);
        localBrowsingTextView = findViewById(R.id.tv_tip_local_browsing);
    }

    /**
     * 设置确认按钮点击监听器
     * @param onClickListener 点击监听器
     */
    public void setOnConfirmClickListener(View.OnClickListener onClickListener) {
        localBrowsingTextView.setOnClickListener(onClickListener);
    }

    /**
     * 设置本地浏览提示文本
     * @param text 提示文本
     */
    public void setLocalBrowsingText(String text) {
        localBrowsingTextView.setText(text);
    }

    /**
     * 设置本地浏览提示文本资源
     * @param resId 字符串资源ID
     */
    public void setLocalBrowsingText(int resId) {
        localBrowsingTextView.setText(resId);
    }

    /**
     * 显示本地浏览提示
     */
    public void showLocalBrowsingTip() {
        setVisibility(View.VISIBLE);
    }

    /**
     * 隐藏本地浏览提示
     */
    public void hideLocalBrowsingTip() {
        setVisibility(View.GONE);
    }

    /**
     * 设置提示布局容器的可见性
     * @param visible 是否可见
     */
    public void setTipLayoutVisible(boolean visible) {
        tipLayoutContainer.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    /**
     * 设置本地浏览文本视图是否可点击
     * @param clickable 是否可点击
     */
    public void setLocalBrowsingClickable(boolean clickable) {
        localBrowsingTextView.setClickable(clickable);
    }

    // Getter 方法

    public TextView getLocalBrowsingTextView() {
        return localBrowsingTextView;
    }

    public LinearLayout getTipLayoutContainer() {
        return tipLayoutContainer;
    }
}
