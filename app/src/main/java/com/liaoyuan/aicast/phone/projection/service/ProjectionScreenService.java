package com.liaoyuan.aicast.phone.projection.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.media.projection.MediaProjection;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;
import com.liaoyuan.aicast.MainActivity;
import com.liaoyuan.aicast.R;
import java.util.Objects;
import p133t4.C1405b;
import p133t4.InterfaceC1404a;
import p147v4.C1463b;

/* loaded from: classes.dex */
public class ProjectionScreenService extends Service {

    /* renamed from: j */
    public C1405b f3467j;

    /* renamed from: k */
    public Notification f3468k;

    /* renamed from: l */
    public RunnableC0665a f3469l = new RunnableC0665a();

    /* renamed from: m */
    public C0666b f3470m = new C0666b();

    /* renamed from: com.liaoyuan.aicast.phone.projection.service.ProjectionScreenService$a */
    public class RunnableC0665a implements Runnable {
        public RunnableC0665a() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            Objects.requireNonNull(ProjectionScreenService.this);
        }
    }

    /* renamed from: com.liaoyuan.aicast.phone.projection.service.ProjectionScreenService$b */
    public class C0666b implements InterfaceC1404a {
        public C0666b() {
        }

        @Override // p140u4.InterfaceC1428a
        /* renamed from: a */
        public final void mo1907a(boolean z5) {
        }

        @Override // p147v4.InterfaceC1462a
        /* renamed from: b */
        public final void mo1908b(boolean z5) {
            if (z5) {
                return;
            }
            ProjectionScreenService.this.stopForeground(true);
        }

        @Override // p147v4.InterfaceC1462a
        /* renamed from: c */
        public final void mo1909c(MediaProjection mediaProjection, int i6) {
        }
    }

    @Override // android.app.Service
    public final IBinder onBind(Intent intent) {
        return null;
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [java.util.ArrayList, java.util.List<t4.a>] */
    /* JADX WARN: Type inference failed for: r2v0, types: [java.util.ArrayList, java.util.List<t4.a>] */
    @Override // android.app.Service
    public final void onCreate() {
        super.onCreate();
        C1405b c1405b = C1405b.c.f6485a;
        this.f3467j = c1405b;
        C0666b c0666b = this.f3470m;
        if (c1405b.f6478c.contains(c0666b)) {
            return;
        }
        c1405b.f6478c.add(c0666b);
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [java.util.ArrayList, java.util.List<t4.a>] */
    /* JADX WARN: Type inference failed for: r2v0, types: [java.util.ArrayList, java.util.List<t4.a>] */
    @Override // android.app.Service
    public final void onDestroy() {
        super.onDestroy();
        C1405b c1405b = this.f3467j;
        C0666b c0666b = this.f3470m;
        if (c1405b.f6478c.contains(c0666b)) {
            c1405b.f6478c.remove(c0666b);
        }
    }

    @Override // android.app.Service
    public final int onStartCommand(Intent intent, int i6, int i7) {
        if (intent != null) {
            intent.getIntExtra("service_type", 0);
            int intExtra = intent.getIntExtra("code", 0);
            Intent intent2 = (Intent) intent.getParcelableExtra("data");
            if (this.f3468k == null) {
                Notification.Builder builder = new Notification.Builder(getApplicationContext());
                Intent intent3 = new Intent(this, (Class<?>) MainActivity.class);
                int i8 = Build.VERSION.SDK_INT;
                builder.setContentIntent(PendingIntent.getActivity(this, 123, intent3, i8 >= 31 ? 67108864 : 1073741824)).setLargeIcon(BitmapFactory.decodeResource(getResources(), R.mipmap.ic_launcher)).setSmallIcon(R.mipmap.ic_launcher).setContentText(getResources().getString(R.string.virtual_projection_ing)).setWhen(System.currentTimeMillis());
                if (i8 >= 26) {
                    builder.setChannelId("PROJECTION_SCREEN");
                    ((NotificationManager) getSystemService("notification")).createNotificationChannel(new NotificationChannel("PROJECTION_SCREEN", "IA_CAST", 2));
                }
                Notification build = builder.build();
                build.defaults = 1;
                this.f3468k = build;
            }
            if (intExtra == -1) {
                startForeground(1, this.f3468k);
            }
            C1405b c1405b = this.f3467j;
            Objects.requireNonNull(c1405b);
            Log.d("PermissionsManager", "onStartCommand resultCode:" + intExtra + ",data:" + intent2);
            C1463b c1463b = c1405b.f6479d;
            Objects.requireNonNull(c1463b);
            Log.d("ScreenCapturePermissions", "onStartCommand onNotifyMediaProjection resultCode:" + intExtra + "data:" + intent2);
            if (intent2 != null) {
                boolean z5 = intExtra == -1;
                int intExtra2 = intent2.getIntExtra("requestCode", 2);
                if (z5) {
                    c1463b.f6730c = c1463b.f6729b.getMediaProjection(intExtra, intent2);
                    c1463b.m3567c(true);
                } else {
                    c1463b.m3567c(false);
                    c1463b.f6730c = null;
                }
                Log.d("ScreenCapturePermissions", "onStartCommand ok:" + z5 + ",requestCode:" + intExtra2 + ",mMediaProjection:" + c1463b.f6730c);
                c1463b.m3566b(intExtra2);
            } else {
                Log.d("ScreenCapturePermissions", "onStartCommand data is null");
            }
        }
        return 1;
    }
}
