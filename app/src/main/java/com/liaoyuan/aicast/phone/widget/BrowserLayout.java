package com.liaoyuan.aicast.phone.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.webkit.WebView;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.liaoyuan.aicast.R;
import com.liaoyuan.aicast.phone.browser.model.BrowserInfo;
import java.util.List;
import p027d5.C0749h;
import p034e5.AbstractC0827a;
import p034e5.C0828b;
import p160x4.RunnableC2052b;

/**
 * 浏览器布局组件
 * 包含WebView、导航栏、收藏夹等功能的复合布局
 */
public class BrowserLayout extends FrameLayout implements View.OnClickListener {

    // 收藏夹布局容器
    private RelativeLayout favoriteLayout;

    // 收藏夹列表
    private RecyclerView bookmarkRecyclerView;

    // 空收藏夹提示文本
    private TextView emptyFavoriteTextView;

    // 底部操作栏
    private LinearLayout bottomLayout;

    // 删除按钮
    private TextView deleteTextView;

    // 全选按钮
    private TextView allSelectTextView;

    // 是否自动隐藏导航栏
    private boolean autoHideBrowserNavi;

    // 是否显示导航栏
    private boolean showBrowserNavi;

    // 是否显示分割线
    private boolean showDivisionLine;

    // 收藏夹数据列表
    private List<BrowserInfo> browserInfoList;

    // 自动隐藏任务
    private RunnableC2052b autoHideTask;

    // 手势检测器
    private GestureDetector gestureDetector;

    // 主Web容器
    private RelativeLayout webContainer;

    // 手机Web容器
    private RelativeLayout phoneWebContainer;

    // WebView容器
    private FrameLayout webViewContainer;

    // WebView实例
    private C0749h webView;

    // 加载进度条
    private LinearProgressIndicator loadProgressIndicator;

    // 浏览器导航布局
    private BrowserNaivLayout browserNaviLayout;

    // 分割线视图
    private View divisionLineView;

    // 首页按钮
    private AppCompatImageView homeButton;

    // 后退按钮
    private AppCompatImageView rewardButton;

    // 前进按钮
    private AppCompatImageView forwardButton;

    // 发送到远程设备按钮
    private AppCompatImageView sendToRemoteButton;

    // 发送到手机按钮
    private AppCompatImageView sendToPhoneButton;

    // 刷新按钮
    private AppCompatImageView refreshButton;

    // 收藏按钮
    private AppCompatImageView starButton;

    // 浏览器点击监听器
    private OnBrowserClickListener browserClickListener;

    // 项目点击监听器
    private OnItemClickListener itemClickListener;

    // 收藏夹适配器
    private BookmarkAdapter bookmarkAdapter;

    /**
     * 手势检测监听器
     * 处理滑动手势来控制导航栏的显示和隐藏
     */
    private class BrowserGestureListener implements GestureDetector.OnGestureListener {

        @Override
        public boolean onDown(MotionEvent motionEvent) {
            return false;
        }

        @Override
        public boolean onFling(MotionEvent motionEvent, MotionEvent motionEvent2, float velocityX, float velocityY) {
            resetAutoHideTimer();
            return false;
        }

        @Override
        public void onLongPress(MotionEvent motionEvent) {
            resetAutoHideTimer();
        }

        @Override
        public boolean onScroll(MotionEvent motionEvent, MotionEvent motionEvent2, float distanceX, float distanceY) {
            browserNaviLayout.getWidth();
            browserNaviLayout.getHeight();
            float deltaX = motionEvent2.getX() - motionEvent.getX();

            // 判断是否为水平滑动
            if (Math.abs(deltaX) <= Math.abs(motionEvent2.getY() - motionEvent.getY()) || Math.abs(deltaX) <= 10.0f) {
                return false;
            }

            if (deltaX > 0.0f) {
                Log.d("BrowserLayout", "右边滑动");
                setShowBrowserNavi(true);
                if (browserNaviLayout.getVisibility() == View.VISIBLE) {
                    resetAutoHideTimer();
                }
            } else {
                Log.d("BrowserLayout", "左边滑动");
                setShowBrowserNavi(false);
            }
            return true;
        }

        @Override
        public void onShowPress(MotionEvent motionEvent) {
            resetAutoHideTimer();
        }

        @Override
        public boolean onSingleTapUp(MotionEvent motionEvent) {
            resetAutoHideTimer();
            return false;
        }
    }

    /**
     * 收藏夹适配器
     * 用于显示收藏的网页列表
     */
    private class BookmarkAdapter extends AbstractC0827a<BrowserInfo> {

        public BookmarkAdapter(Context context, List<BrowserInfo> list) {
            super(context, R.layout.item_bookmark, list);
        }

        @Override
        public void mo1485o(C0828b viewHolder, BrowserInfo browserInfo, int position) {
            // 由于原代码被混淆，这里提供基本的绑定逻辑框架
            // 实际实现需要根据具体的布局文件和需求来完成
            throw new UnsupportedOperationException("需要根据实际布局文件实现数据绑定逻辑");
        }

        /**
         * 处理项目点击事件
         * @param viewHolder 视图持有者
         * @param isLongClick 是否为长按点击
         */
        public void handleItemClick(C0828b viewHolder, boolean isLongClick) {
            boolean isInEditMode = isInEditMode();
            int position = viewHolder.m1060e();

            if (position < 0 || position >= mo1081c()) {
                return;
            }

            BrowserInfo browserInfo = (BrowserInfo) m2310p(position);
            OnItemClickListener listener = BrowserLayout.this.itemClickListener;

            if (listener != null) {
                View itemView = viewHolder.f1852a;
                if (isLongClick) {
                    listener.onLongClick(isInEditMode);
                } else {
                    listener.onItemClick(itemView, isInEditMode, browserInfo);
                }
            }
        }
    }

    /**
     * 浏览器点击监听器接口
     */
    public interface OnBrowserClickListener {
        /**
         * 当检测到链接点击时调用
         */
        default void onLinkClick() {
        }

        /**
         * 当按钮被点击时调用
         * @param view 被点击的视图
         */
        default void onButtonClick(View view) {
        }
    }

    /**
     * 项目点击监听器接口
     */
    public interface OnItemClickListener {
        /**
         * 当项目被点击时调用
         * @param view 被点击的视图
         * @param isInEditMode 是否处于编辑模式
         * @param browserInfo 浏览器信息
         */
        void onItemClick(View view, boolean isInEditMode, BrowserInfo browserInfo);

        /**
         * 当项目被长按时调用
         * @param isInEditMode 是否处于编辑模式
         */
        default void onLongClick(boolean isInEditMode) {
        }
    }

    public BrowserLayout(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        initializeComponents(context);
    }

    /**
     * 初始化组件
     */
    private void initializeComponents(Context context) {
        // 初始化自动隐藏任务和手势检测器
        this.autoHideTask = new RunnableC2052b(this, 2);
        this.gestureDetector = new GestureDetector(getContext(), new BrowserGestureListener());

        // 加载布局
        View.inflate(context, R.layout.layout_browser, this);

        // 初始化视图组件
        initializeViews();

        // 初始化WebView
        initializeWebView();

        // 设置点击监听器
        setupClickListeners();

        // 设置初始状态
        setupInitialState();

        // 重置自动隐藏计时器
        resetAutoHideTimer();
    }

    /**
     * 初始化视图组件
     */
    private void initializeViews() {
        phoneWebContainer = findViewById(R.id.rl_web_phone);
        webContainer = findViewById(R.id.rl_web);
        webViewContainer = findViewById(R.id.webview_container);
        loadProgressIndicator = findViewById(R.id.load_progress);
        browserNaviLayout = findViewById(R.id.browser_naiv_layout);
        divisionLineView = findViewById(R.id.view_division_line);

        // 导航按钮
        homeButton = findViewById(R.id.home);
        rewardButton = findViewById(R.id.reward);
        forwardButton = findViewById(R.id.forward);
        sendToRemoteButton = findViewById(R.id.send_to_remote);
        sendToPhoneButton = findViewById(R.id.send_to_phone);
        refreshButton = findViewById(R.id.refresh);
        starButton = findViewById(R.id.star);

        // 收藏夹相关视图
        favoriteLayout = findViewById(R.id.rl_favorite);
        bookmarkRecyclerView = findViewById(R.id.rv_bookmark);
        emptyFavoriteTextView = findViewById(R.id.tv_empty_fav_tips);
        bottomLayout = findViewById(R.id.ll_bottom);
        deleteTextView = findViewById(R.id.tv_delete);
        allSelectTextView = findViewById(R.id.tv_all_select);
    }

    /**
     * 初始化WebView
     */
    private void initializeWebView() {
        webView = new C0749h(getContext());
        webViewContainer.addView(webView);
        webView.setBackgroundColor(0);

        FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) webView.getLayoutParams();
        layoutParams.width = FrameLayout.LayoutParams.MATCH_PARENT;
        layoutParams.height = FrameLayout.LayoutParams.MATCH_PARENT;
        webView.setLayoutParams(layoutParams);
    }

    /**
     * 设置点击监听器
     */
    private void setupClickListeners() {
        homeButton.setOnClickListener(this);
        rewardButton.setOnClickListener(this);
        forwardButton.setOnClickListener(this);
        sendToPhoneButton.setOnClickListener(this);
        sendToRemoteButton.setOnClickListener(this);
        refreshButton.setOnClickListener(this);
        starButton.setOnClickListener(this);
        deleteTextView.setOnClickListener(this);
        allSelectTextView.setOnClickListener(this);
    }

    /**
     * 设置初始状态
     */
    private void setupInitialState() {
        setEnableHome(false);
        setEnableReward(false);
        setEnableForward(false);
        setEnableStar(false);
        setEnableRefresh(false);
    }

    /**
     * 设置分割线位置
     * @param position 位置：0=左侧，1=右侧，2=底部
     */
    private void setDivisionLinePosition(int position) {
        // 设置分割线可见性
        divisionLineView.setVisibility(showDivisionLine ? View.VISIBLE : View.GONE);

        float dimension = getResources().getDimension(R.dimen.browser_navi_width);
        RelativeLayout.LayoutParams layoutParams = null;
        int backgroundResource = R.drawable.division_line_vertical;

        switch (position) {
            case 0: // 左侧
                layoutParams = new RelativeLayout.LayoutParams((int) dimension, RelativeLayout.LayoutParams.MATCH_PARENT);
                layoutParams.addRule(RelativeLayout.CENTER_IN_PARENT, browserNaviLayout.getId());
                divisionLineView.setBackgroundResource(backgroundResource);
                break;

            case 1: // 右侧
                layoutParams = new RelativeLayout.LayoutParams((int) dimension, RelativeLayout.LayoutParams.MATCH_PARENT);
                layoutParams.addRule(RelativeLayout.ALIGN_PARENT_RIGHT, browserNaviLayout.getId());
                divisionLineView.setBackgroundResource(backgroundResource);
                break;

            case 2: // 底部
                layoutParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, (int) dimension);
                layoutParams.addRule(RelativeLayout.BELOW, browserNaviLayout.getId());
                divisionLineView.setBackgroundResource(R.drawable.division_line_horizontal);
                break;
        }

        if (layoutParams != null) {
            divisionLineView.setLayoutParams(layoutParams);
        }
    }

    /**
     * 设置是否显示浏览器导航栏
     */
    public void setShowBrowserNavi(boolean show) {
        if (showBrowserNavi && isFavoriteVisible() && !show) {
            browserNaviLayout.setVisibility(View.VISIBLE);
        } else {
            browserNaviLayout.setVisibility(show ? View.VISIBLE : View.GONE);
        }
    }

    /**
     * 重置自动隐藏计时器
     */
    private void resetAutoHideTimer() {
        boolean isNaviVisible = browserNaviLayout.getVisibility() == View.VISIBLE;
        boolean isFavoriteVisible = isFavoriteVisible();

        if (autoHideBrowserNavi && isNaviVisible && !isFavoriteVisible) {
            browserNaviLayout.removeCallbacks(autoHideTask);
            browserNaviLayout.postDelayed(autoHideTask, 3000L);
        }
    }

    /**
     * 刷新收藏夹适配器
     */
    private void refreshBookmarkAdapter() {
        boolean hasData = (browserInfoList != null ? browserInfoList.size() : 0) > 0;
        Log.d("BrowserLayout", "refreshBookmarkAdapter hasData:" + hasData);

        bookmarkRecyclerView.setVisibility(hasData ? View.VISIBLE : View.GONE);
        emptyFavoriteTextView.setVisibility(hasData ? View.GONE : View.VISIBLE);

        if (bookmarkAdapter != null) {
            bookmarkAdapter.f4246e = browserInfoList;
            bookmarkAdapter.m1083e();
            return;
        }

        bookmarkAdapter = new BookmarkAdapter(getContext(), browserInfoList);
        GridLayoutManager gridLayoutManager = new GridLayoutManager(getContext());
        gridLayoutManager.m973k1(1);
        bookmarkRecyclerView.setLayoutManager(gridLayoutManager);
        bookmarkRecyclerView.setAdapter(bookmarkAdapter);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent motionEvent) {
        // 检测链接点击
        if (motionEvent.getAction() == MotionEvent.ACTION_UP &&
            webView.getHitTestResult().getType() == WebView.HitTestResult.SRC_ANCHOR_TYPE &&
            browserClickListener != null) {
            browserClickListener.onLinkClick();
        }

        // 处理手势检测
        if (autoHideBrowserNavi) {
            gestureDetector.onTouchEvent(motionEvent);
        }

        return super.dispatchTouchEvent(motionEvent);
    }

    /**
     * 检查是否处于编辑模式
     */
    private boolean isInEditMode() {
        return bottomLayout.getVisibility() == View.VISIBLE;
    }

    /**
     * 检查收藏夹是否可见
     */
    private boolean isFavoriteVisible() {
        return favoriteLayout.getVisibility() == View.VISIBLE;
    }

    /**
     * 设置导航栏布局参数
     * @param position 位置：0=左侧，1=右侧，2=底部
     * @param width 宽度
     * @param height 高度
     */
    public void setNaviLayoutParams(int position, int width, int height) {
        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(width, height);
        webContainer.setPadding(0, 0, 0, 0);

        switch (position) {
            case 0: // 左侧
                layoutParams.addRule(RelativeLayout.ALIGN_PARENT_LEFT, RelativeLayout.TRUE);
                break;
            case 1: // 右侧
                layoutParams.addRule(RelativeLayout.ALIGN_PARENT_RIGHT, RelativeLayout.TRUE);
                break;
            case 2: // 底部
                layoutParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM, RelativeLayout.TRUE);
                if (!autoHideBrowserNavi) {
                    webContainer.setPadding(0, 0, 0, height);
                }
                break;
        }

        setDivisionLinePosition(position);
        browserNaviLayout.setLayoutParams(layoutParams);

        // 设置导航栏内部布局
        if (browserNaviLayout.getBottomButtonLayout() != null) {
            switch (position) {
                case 0: // 左侧
                    browserNaviLayout.getBottomButtonLayout().setOrientation(LinearLayout.VERTICAL);
                    browserNaviLayout.setViewLayoutParams(browserNaviLayout.getBottomButtonLayout(),
                        Gravity.LEFT, width, RelativeLayout.LayoutParams.MATCH_PARENT);
                    break;
                case 1: // 右侧
                    browserNaviLayout.getBottomButtonLayout().setOrientation(LinearLayout.VERTICAL);
                    browserNaviLayout.setViewLayoutParams(browserNaviLayout.getBottomButtonLayout(),
                        Gravity.RIGHT, width, RelativeLayout.LayoutParams.MATCH_PARENT);
                    break;
                case 2: // 底部
                    browserNaviLayout.getBottomButtonLayout().setOrientation(LinearLayout.HORIZONTAL);
                    browserNaviLayout.setViewLayoutParams(browserNaviLayout.getBottomButtonLayout(),
                        Gravity.BOTTOM, RelativeLayout.LayoutParams.MATCH_PARENT, height);
                    break;
            }
        }
    }

    /**
     * 获取WebView实例
     */
    public WebView getWebView() {
        return webView;
    }

    /**
     * 设置加载进度
     * @param visible 是否显示进度条
     * @param progress 进度值
     */
    public void setLoadProgress(boolean visible, int progress) {
        loadProgressIndicator.setVisibility(visible ? View.VISIBLE : View.GONE);
        loadProgressIndicator.setProgress(progress);
    }

    /**
     * 显示导航栏
     */
    public void showBrowserNavi() {
        showBrowserNavi = true;
        setShowBrowserNavi(true);
    }

    /**
     * 切换Web视图和收藏夹视图
     * @param showWeb 是否显示Web视图
     */
    public void switchView(boolean showWeb) {
        phoneWebContainer.setVisibility(showWeb ? View.VISIBLE : View.GONE);
        favoriteLayout.setVisibility(!showWeb ? View.VISIBLE : View.GONE);

        if (autoHideBrowserNavi && isFavoriteVisible()) {
            browserNaviLayout.setVisibility(View.VISIBLE);
        } else {
            browserNaviLayout.setVisibility(showWeb ? View.VISIBLE : View.GONE);
        }

        if (showWeb) {
            webView.onResume();
        } else {
            webView.onPause();
        }

        resetAutoHideTimer();
    }

    @Override
    public void onClick(View view) {
        resetAutoHideTimer();
        if (browserClickListener != null) {
            browserClickListener.onButtonClick(view);
        }
    }

    // 公共设置方法

    /**
     * 设置是否自动隐藏浏览器导航栏
     */
    public void setAutoHideBrowserNavi(boolean autoHide) {
        this.autoHideBrowserNavi = autoHide;
    }

    /**
     * 设置前进按钮是否可用
     */
    public void setEnableForward(boolean enabled) {
        forwardButton.setEnabled(enabled);
    }

    /**
     * 设置首页按钮是否可用
     */
    public void setEnableHome(boolean enabled) {
        homeButton.setEnabled(enabled);
    }

    /**
     * 设置刷新按钮是否可用
     */
    public void setEnableRefresh(boolean enabled) {
        refreshButton.setEnabled(enabled);
    }

    /**
     * 设置后退按钮是否可用
     */
    public void setEnableReward(boolean enabled) {
        rewardButton.setEnabled(enabled);
    }

    /**
     * 设置发送按钮是否可用
     */
    public void setEnableSendTo(boolean enabled) {
        sendToRemoteButton.setEnabled(enabled);
    }

    /**
     * 设置收藏按钮是否可用
     */
    public void setEnableStar(boolean enabled) {
        starButton.setEnabled(enabled);
    }

    /**
     * 设置浏览器点击监听器
     */
    public void setOnClickBrowserListener(OnBrowserClickListener listener) {
        this.browserClickListener = listener;
    }

    /**
     * 设置项目点击监听器
     */
    public void setOnItemClickListener(OnItemClickListener listener) {
        this.itemClickListener = listener;
    }

    /**
     * 设置收藏按钮选中状态
     */
    public void setSelectStar(boolean selected) {
        starButton.setSelected(selected);
    }

    /**
     * 设置全选按钮文本
     */
    public void setTextAllSelect(String text) {
        allSelectTextView.setText(text);
    }

    /**
     * 设置收藏夹数据
     */
    public void setBrowserInfoList(List<BrowserInfo> list) {
        this.browserInfoList = list;
        refreshBookmarkAdapter();
    }

    /**
     * 设置是否显示分割线
     */
    public void setShowDivisionLine(boolean show) {
        this.showDivisionLine = show;
    }

    // Getter 方法

    public BrowserNaivLayout getBrowserNaviLayout() {
        return browserNaviLayout;
    }

    public LinearProgressIndicator getLoadProgressIndicator() {
        return loadProgressIndicator;
    }

    public RecyclerView getBookmarkRecyclerView() {
        return bookmarkRecyclerView;
    }

    public List<BrowserInfo> getBrowserInfoList() {
        return browserInfoList;
    }
}
