package com.liaoyuan.aicast.phone.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.liaoyuan.aicast.R;

/**
 * 浏览器Toast提示布局组件
 * 用于显示临时的Toast消息，支持自动隐藏功能
 */
public class ToastBrowserLayout extends LinearLayout {

    // Toast文本视图
    private TextView toastTextView;

    // 自动隐藏任务
    private AutoHideTask autoHideTask;

    /**
     * 自动隐藏任务
     */
    private class AutoHideTask implements Runnable {

        @Override
        public void run() {
            setVisibility(View.GONE);
        }
    }

    public ToastBrowserLayout(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        initializeComponents(context);
    }

    /**
     * 初始化组件
     */
    private void initializeComponents(Context context) {
        this.autoHideTask = new AutoHideTask();

        // 初始状态为隐藏
        setVisibility(View.GONE);

        // 加载布局
        View.inflate(context, R.layout.layout_browser_toast, this);

        // 初始化视图
        initializeViews();
    }

    /**
     * 初始化视图组件
     */
    private void initializeViews() {
        toastTextView = findViewById(R.id.tv_toast);
    }

    /**
     * 设置Toast文本
     * @param text Toast文本
     */
    public void setToast(String text) {
        toastTextView.setText(text);
    }

    /**
     * 设置Toast文本资源
     * @param resId 字符串资源ID
     */
    public void setToast(int resId) {
        toastTextView.setText(resId);
    }

    /**
     * 显示Toast
     */
    public void showToast() {
        setVisibility(View.VISIBLE);
    }

    /**
     * 隐藏Toast
     */
    public void hideToast() {
        setVisibility(View.GONE);
        removeCallbacks(autoHideTask);
    }

    /**
     * 显示Toast并自动隐藏
     * @param text Toast文本
     * @param duration 显示时长（毫秒）
     */
    public void showToast(String text, long duration) {
        setToast(text);
        showToastWithDuration(duration);
    }

    /**
     * 显示Toast并自动隐藏
     * @param resId 字符串资源ID
     * @param duration 显示时长（毫秒）
     */
    public void showToast(int resId, long duration) {
        setToast(resId);
        showToastWithDuration(duration);
    }

    /**
     * 显示Toast指定时长
     * @param duration 显示时长（毫秒）
     */
    public void showToastWithDuration(long duration) {
        showToast();
        removeCallbacks(autoHideTask);
        postDelayed(autoHideTask, duration);
    }

    /**
     * 取消自动隐藏
     */
    public void cancelAutoHide() {
        removeCallbacks(autoHideTask);
    }

    /**
     * 检查Toast是否正在显示
     * @return 是否显示中
     */
    public boolean isShowing() {
        return getVisibility() == View.VISIBLE;
    }

    // Getter 方法

    public TextView getToastTextView() {
        return toastTextView;
    }
}
