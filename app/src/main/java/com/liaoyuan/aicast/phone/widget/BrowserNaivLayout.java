package com.liaoyuan.aicast.phone.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;
import androidx.appcompat.widget.AppCompatImageView;
import com.liaoyuan.aicast.R;

/* loaded from: classes.dex */
public class BrowserNaivLayout extends LinearLayout {

    /* renamed from: j */
    public LinearLayout f3505j;

    /* renamed from: k */
    public AppCompatImageView f3506k;

    /* renamed from: l */
    public AppCompatImageView f3507l;

    /* renamed from: m */
    public AppCompatImageView f3508m;

    public BrowserNaivLayout(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        View.inflate(context, R.layout.layout_browser_navi, this);
        this.f3505j = (LinearLayout) findViewById(R.id.bottom_btn_layout);
        this.f3506k = (AppCompatImageView) findViewById(R.id.home);
        this.f3507l = (AppCompatImageView) findViewById(R.id.send_to_phone);
        this.f3508m = (AppCompatImageView) findViewById(R.id.send_to_remote);
    }

    /* renamed from: a */
    public final void m1923a(View view, int i6, int i7, int i8) {
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) view.getLayoutParams();
        layoutParams.width = i7;
        layoutParams.height = i8;
        layoutParams.gravity = i6;
        view.setLayoutParams(layoutParams);
    }

    @Override // android.view.View
    public void setBackgroundColor(int i6) {
        this.f3505j.setBackgroundColor(i6);
    }
}
