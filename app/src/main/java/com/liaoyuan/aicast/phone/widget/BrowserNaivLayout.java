package com.liaoyuan.aicast.phone.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;
import androidx.appcompat.widget.AppCompatImageView;
import com.liaoyuan.aicast.R;

/**
 * 浏览器导航布局组件
 * 包含底部按钮布局和导航按钮（首页、发送到手机、发送到远程设备）
 */
public class BrowserNaivLayout extends LinearLayout {

    // 底部按钮布局容器
    private LinearLayout bottomButtonLayout;

    // 首页按钮
    private AppCompatImageView homeButton;

    // 发送到手机按钮
    private AppCompatImageView sendToPhoneButton;

    // 发送到远程设备按钮
    private AppCompatImageView sendToRemoteButton;

    public BrowserNaivLayout(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        initializeViews(context);
    }

    /**
     * 初始化视图组件
     */
    private void initializeViews(Context context) {
        View.inflate(context, R.layout.layout_browser_navi, this);
        bottomButtonLayout = findViewById(R.id.bottom_btn_layout);
        homeButton = findViewById(R.id.home);
        sendToPhoneButton = findViewById(R.id.send_to_phone);
        sendToRemoteButton = findViewById(R.id.send_to_remote);
    }

    /**
     * 设置视图的布局参数
     * @param view 要设置的视图
     * @param gravity 重力方向
     * @param width 宽度
     * @param height 高度
     */
    public final void setViewLayoutParams(View view, int gravity, int width, int height) {
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) view.getLayoutParams();
        layoutParams.width = width;
        layoutParams.height = height;
        layoutParams.gravity = gravity;
        view.setLayoutParams(layoutParams);
    }

    @Override
    public void setBackgroundColor(int color) {
        if (bottomButtonLayout != null) {
            bottomButtonLayout.setBackgroundColor(color);
        }
    }

    // Getter 方法，便于外部访问组件
    public LinearLayout getBottomButtonLayout() {
        return bottomButtonLayout;
    }

    public AppCompatImageView getHomeButton() {
        return homeButton;
    }

    public AppCompatImageView getSendToPhoneButton() {
        return sendToPhoneButton;
    }

    public AppCompatImageView getSendToRemoteButton() {
        return sendToRemoteButton;
    }
}
