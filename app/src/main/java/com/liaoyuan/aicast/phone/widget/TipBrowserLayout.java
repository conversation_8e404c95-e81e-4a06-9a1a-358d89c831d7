package com.liaoyuan.aicast.phone.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.liaoyuan.aicast.R;
import p027d5.ViewOnClickListenerC0747f;

/**
 * 浏览器提示布局组件
 * 用于显示浏览器相关的提示信息，支持自动隐藏和确认操作
 */
public class TipBrowserLayout extends LinearLayout {

    // 提示文本视图
    private TextView tipTextView;

    // 确认按钮点击监听器
    private View.OnClickListener confirmClickListener;

    // 自动隐藏任务
    private AutoHideTask autoHideTask;

    // 确认按钮
    private TextView confirmButton;

    /**
     * 自动隐藏任务
     */
    private class AutoHideTask implements Runnable {

        @Override
        public void run() {
            hideTip();
        }
    }

    public TipBrowserLayout(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        initializeComponents(context);
    }

    /**
     * 初始化组件
     */
    private void initializeComponents(Context context) {
        this.autoHideTask = new AutoHideTask();

        // 初始状态为隐藏
        hideTip();

        // 加载布局
        View.inflate(context, R.layout.layout_browser_tip, this);

        // 初始化视图
        initializeViews();
    }

    /**
     * 初始化视图组件
     */
    private void initializeViews() {
        tipTextView = findViewById(R.id.tv_tip);
        confirmButton = findViewById(R.id.tv_tip_confirm);

        // 设置确认按钮点击监听器
        confirmButton.setOnClickListener(new ViewOnClickListenerC0747f(this, 1));
    }

    /**
     * 隐藏提示
     */
    private void hideTip() {
        setVisibility(View.GONE);
    }

    /**
     * 显示提示
     */
    public void showTip() {
        setVisibility(View.VISIBLE);
    }

    /**
     * 显示提示并自动隐藏
     * @param delayMillis 延迟隐藏时间（毫秒）
     */
    public void showTipWithAutoHide(long delayMillis) {
        showTip();
        removeCallbacks(autoHideTask);
        postDelayed(autoHideTask, delayMillis);
    }

    /**
     * 取消自动隐藏
     */
    public void cancelAutoHide() {
        removeCallbacks(autoHideTask);
    }

    /**
     * 设置确认按钮点击监听器
     * @param onClickListener 点击监听器
     */
    public void setOnConfirmClickListener(View.OnClickListener onClickListener) {
        this.confirmClickListener = onClickListener;
    }

    /**
     * 设置提示文本
     * @param tip 提示文本
     */
    public void setTip(String tip) {
        tipTextView.setText(tip);
    }

    /**
     * 设置提示文本资源
     * @param resId 字符串资源ID
     */
    public void setTip(int resId) {
        tipTextView.setText(resId);
    }

    /**
     * 设置确认按钮文本
     * @param text 按钮文本
     */
    public void setConfirmButtonText(String text) {
        confirmButton.setText(text);
    }

    /**
     * 设置确认按钮文本资源
     * @param resId 字符串资源ID
     */
    public void setConfirmButtonText(int resId) {
        confirmButton.setText(resId);
    }

    /**
     * 获取确认按钮点击监听器
     */
    public View.OnClickListener getConfirmClickListener() {
        return confirmClickListener;
    }

    // Getter 方法

    public TextView getTipTextView() {
        return tipTextView;
    }

    public TextView getConfirmButton() {
        return confirmButton;
    }
}
