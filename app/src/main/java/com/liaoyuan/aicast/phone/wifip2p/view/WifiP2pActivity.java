package com.liaoyuan.aicast.phone.wifip2p.view;

import android.content.Context;
import android.net.wifi.WifiManager;
import android.net.wifi.p2p.WifiP2pDevice;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.activity.result.C0052a;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.liaoyuan.aicast.R;
import java.util.List;
import java.util.Objects;
import p006a5.ViewOnClickListenerC0039b;
import p019c5.ViewOnClickListenerC0429e;
import p027d5.ViewOnClickListenerC0747f;
import p031e2.C0796l;
import p034e5.AbstractC0827a;
import p034e5.C0828b;
import p041f5.C0865b;
import p048g5.C0891c;
import p048g5.InterfaceC0892d;
import p081l3.C1064b;
import p082l4.DialogInterfaceOnClickListenerC1065a;
import p115q4.AbstractActivityC1295a;
import p127s4.DialogInterfaceOnClickListenerC1345d;
import p135u.C1407a;
import p173z4.RunnableC2125l;

/* loaded from: classes.dex */
public class WifiP2pActivity extends AbstractActivityC1295a {

    /* renamed from: A */
    public RecyclerView f3533A;

    /* renamed from: B */
    public RecyclerView f3534B;

    /* renamed from: C */
    public TextView f3535C;

    /* renamed from: D */
    public LinearLayout f3536D;

    /* renamed from: E */
    public TextView f3537E;

    /* renamed from: F */
    public TextView f3538F;

    /* renamed from: G */
    public C0674b f3539G;

    /* renamed from: H */
    public C0674b f3540H;

    /* renamed from: I */
    public C0891c f3541I;

    /* renamed from: J */
    public C0673a f3542J = new C0673a();

    /* renamed from: y */
    public Button f3543y;

    /* renamed from: z */
    public ProgressBar f3544z;

    /* renamed from: com.liaoyuan.aicast.phone.wifip2p.view.WifiP2pActivity$a */
    public class C0673a implements InterfaceC0892d {
        public C0673a() {
        }

        @Override // p048g5.InterfaceC0892d
        /* renamed from: a */
        public final void mo1927a(int i6) {
            C0052a.m105i("updateRequestPermissions 11 requestCode:", i6, "WifiP2pActivity");
            if (Build.VERSION.SDK_INT >= 33) {
                C1407a.m3435b(WifiP2pActivity.this, new String[]{"android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.NEARBY_WIFI_DEVICES"}, i6);
            } else {
                C1407a.m3435b(WifiP2pActivity.this, new String[]{"android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION"}, i6);
            }
        }

        @Override // p048g5.InterfaceC0892d
        /* renamed from: b */
        public final void mo1928b() {
            C1064b c1064b = new C1064b(WifiP2pActivity.this);
            c1064b.f284a.f267d = WifiP2pActivity.this.getResources().getString(R.string.devices_open_location_settings_title);
            c1064b.f284a.f269f = WifiP2pActivity.this.getResources().getString(R.string.devices_open_location_settings_message);
            c1064b.m2717c(WifiP2pActivity.this.getResources().getString(R.string.devices_dialog_cancel), DialogInterfaceOnClickListenerC1345d.f6242n);
            c1064b.m2718d(WifiP2pActivity.this.getResources().getString(R.string.devices_dialog_confirm), new DialogInterfaceOnClickListenerC1065a(this, 1));
            c1064b.mo135a();
            c1064b.m136b().getWindow().setBackgroundDrawableResource(R.drawable.dialog_bg);
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // p048g5.InterfaceC0892d
        /* renamed from: c */
        public final void mo1929c(List<WifiP2pDevice> list, List<WifiP2pDevice> list2) {
            WifiP2pActivity wifiP2pActivity = WifiP2pActivity.this;
            Objects.requireNonNull(wifiP2pActivity);
            boolean z5 = (list2 != 0 ? list2.size() : 0) > 0;
            wifiP2pActivity.f3533A.setVisibility(z5 ? 0 : 8);
            wifiP2pActivity.f3537E.setVisibility(z5 ? 0 : 8);
            C0674b c0674b = wifiP2pActivity.f3540H;
            if (c0674b == null) {
                wifiP2pActivity.f3540H = wifiP2pActivity.new C0674b(wifiP2pActivity.getApplicationContext(), list2);
                wifiP2pActivity.getApplicationContext();
                LinearLayoutManager linearLayoutManager = new LinearLayoutManager(1);
                linearLayoutManager.m973k1(1);
                wifiP2pActivity.f3533A.setLayoutManager(linearLayoutManager);
                wifiP2pActivity.f3533A.setAdapter(wifiP2pActivity.f3540H);
            } else {
                c0674b.f4246e = list2;
                c0674b.m1083e();
            }
            WifiP2pActivity wifiP2pActivity2 = WifiP2pActivity.this;
            Objects.requireNonNull(wifiP2pActivity2);
            boolean z6 = (list != 0 ? list.size() : 0) > 0;
            wifiP2pActivity2.f3534B.setVisibility(z6 ? 0 : 8);
            wifiP2pActivity2.f3538F.setVisibility(z6 ? 8 : 0);
            C0674b c0674b2 = wifiP2pActivity2.f3539G;
            if (c0674b2 != null) {
                c0674b2.f4246e = list;
                c0674b2.m1083e();
                return;
            }
            wifiP2pActivity2.f3539G = wifiP2pActivity2.new C0674b(wifiP2pActivity2.getApplicationContext(), list);
            wifiP2pActivity2.getApplicationContext();
            LinearLayoutManager linearLayoutManager2 = new LinearLayoutManager(1);
            linearLayoutManager2.m973k1(1);
            wifiP2pActivity2.f3534B.setLayoutManager(linearLayoutManager2);
            wifiP2pActivity2.f3534B.setAdapter(wifiP2pActivity2.f3539G);
        }

        @Override // p048g5.InterfaceC0892d
        /* renamed from: d */
        public final void mo1930d(boolean z5) {
            ProgressBar progressBar;
            int i6;
            if (z5) {
                WifiP2pActivity wifiP2pActivity = WifiP2pActivity.this;
                wifiP2pActivity.f3543y.setText(wifiP2pActivity.getText(R.string.devices_discover_stop));
                WifiP2pActivity wifiP2pActivity2 = WifiP2pActivity.this;
                wifiP2pActivity2.f3538F.setText(wifiP2pActivity2.getText(R.string.devices_searching));
                progressBar = WifiP2pActivity.this.f3544z;
                i6 = 0;
            } else {
                WifiP2pActivity wifiP2pActivity3 = WifiP2pActivity.this;
                wifiP2pActivity3.f3543y.setText(wifiP2pActivity3.getText(R.string.devices_discover_start));
                WifiP2pActivity wifiP2pActivity4 = WifiP2pActivity.this;
                wifiP2pActivity4.f3538F.setText(wifiP2pActivity4.getText(R.string.devices_click_searching));
                progressBar = WifiP2pActivity.this.f3544z;
                i6 = 8;
            }
            progressBar.setVisibility(i6);
        }

        @Override // p048g5.InterfaceC0892d
        /* renamed from: e */
        public final void mo1931e(boolean z5, boolean z6) {
            Log.d("WifiP2pActivity", "updateTip hasPermission:" + z5 + ",isWifiOpen:" + z6);
            if (!z5) {
                WifiP2pActivity.this.f3536D.setVisibility(0);
                WifiP2pActivity wifiP2pActivity = WifiP2pActivity.this;
                wifiP2pActivity.f3535C.setText(wifiP2pActivity.getString(R.string.devices_access_fine_location));
                return;
            }
            WifiP2pActivity wifiP2pActivity2 = WifiP2pActivity.this;
            if (z6) {
                wifiP2pActivity2.f3536D.setVisibility(8);
                return;
            }
            wifiP2pActivity2.f3536D.setVisibility(0);
            WifiP2pActivity wifiP2pActivity3 = WifiP2pActivity.this;
            wifiP2pActivity3.f3535C.setText(wifiP2pActivity3.getString(R.string.devices_open_wifi));
        }
    }

    /* renamed from: com.liaoyuan.aicast.phone.wifip2p.view.WifiP2pActivity$b */
    public class C0674b extends AbstractC0827a<WifiP2pDevice> {
        /* JADX WARN: Incorrect types in method signature: (Landroid/content/Context;ILjava/util/List<Landroid/net/wifi/p2p/WifiP2pDevice;>;)V */
        public C0674b(Context context, List list) {
            super(context, R.layout.item_pair, list);
        }

        @Override // p034e5.AbstractC0827a
        /* renamed from: o */
        public final void mo1485o(C0828b c0828b, WifiP2pDevice wifiP2pDevice, int i6) {
            WifiP2pDevice wifiP2pDevice2 = wifiP2pDevice;
            ImageView imageView = (ImageView) c0828b.m2311x(R.id.iv_icon);
            TextView textView = (TextView) c0828b.m2311x(R.id.tv_name);
            TextView textView2 = (TextView) c0828b.m2311x(R.id.tv_state);
            c0828b.m2311x(R.id.divider).setVisibility(mo1081c() + (-1) == i6 ? 8 : 0);
            imageView.setImageResource(R.drawable.icon_pair);
            textView.setText(wifiP2pDevice2.deviceName);
            textView2.setText(WifiP2pActivity.this.getResources().getStringArray(R.array.wifi_ap_state)[wifiP2pDevice2.status]);
            c0828b.f1852a.setOnClickListener(new ViewOnClickListenerC0429e(this, c0828b, 2));
        }
    }

    @Override // p115q4.AbstractActivityC1295a, androidx.fragment.app.ActivityC0229p, androidx.activity.ComponentActivity, p135u.ActivityC1410d, android.app.Activity
    public final void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(R.layout.activity_wifi_p2p);
        m1964t((Toolbar) findViewById(R.id.toolbar_wifi_p2p));
        m1962r().mo1950m(true);
        m1962r().mo1952o(getString(R.string.devices_title));
        this.f3536D = (LinearLayout) findViewById(R.id.ll_permission);
        this.f3535C = (TextView) findViewById(R.id.tv_permission);
        this.f3537E = (TextView) findViewById(R.id.tv_paired);
        this.f3533A = (RecyclerView) findViewById(R.id.rv_paired);
        this.f3534B = (RecyclerView) findViewById(R.id.rv_available);
        this.f3544z = (ProgressBar) findViewById(R.id.pb_loading);
        this.f3538F = (TextView) findViewById(R.id.tv_no_available);
        this.f3543y = (Button) findViewById(R.id.btn_pair_refresh);
        C0891c c0891c = C0891c.b.f4507a;
        this.f3541I = c0891c;
        Context applicationContext = getApplicationContext();
        int i6 = 3;
        if (!c0891c.f4502e) {
            c0891c.f4502e = true;
            Log.d("BoxWifiP2pPresenter", "init");
            c0891c.f4501d = applicationContext.getApplicationContext();
            C0865b c0865b = C0865b.k.f4401a;
            c0891c.f4503f = c0865b;
            c0865b.m2395k(c0891c.f4505h);
            c0891c.f4503f.f4383j = new C0796l(c0891c, 3);
        }
        c0891c.f4503f.m2386b();
        this.f3543y.setOnClickListener(new ViewOnClickListenerC0747f(this, 2));
        this.f3536D.setOnClickListener(new ViewOnClickListenerC0039b(this, i6));
        this.f3541I.m5302c(this.f3542J);
    }

    @Override // p115q4.AbstractActivityC1295a, p021d.ActivityC0680e, androidx.fragment.app.ActivityC0229p, android.app.Activity
    public final void onDestroy() {
        super.onDestroy();
        this.f3541I.m5303d(this.f3542J);
    }

    @Override // android.app.Activity
    public final boolean onOptionsItemSelected(MenuItem menuItem) {
        if (menuItem.getItemId() != 16908332) {
            return super.onOptionsItemSelected(menuItem);
        }
        onBackPressed();
        return true;
    }

    @Override // p115q4.AbstractActivityC1295a, androidx.fragment.app.ActivityC0229p, android.app.Activity
    public final void onPause() {
        super.onPause();
        this.f3541I.f4503f.m2399o();
    }

    @Override // p115q4.AbstractActivityC1295a, androidx.fragment.app.ActivityC0229p, androidx.activity.ComponentActivity, android.app.Activity
    public final void onRequestPermissionsResult(int i6, String[] strArr, int[] iArr) {
        String str;
        String str2;
        super.onRequestPermissionsResult(i6, strArr, iArr);
        C0891c c0891c = this.f3541I;
        Objects.requireNonNull(c0891c);
        Log.d("BoxWifiP2pPresenter", "requestCode:" + i6);
        if (i6 == 2) {
            if (iArr.length > 0 && iArr[0] == 0) {
                Log.d("BoxWifiP2pPresenter", "REQUEST_CODE_DISCOVER_PEERS 授权成功");
                c0891c.f4503f.m2389e();
                return;
            } else {
                str = "REQUEST_CODE_DISCOVER_PEERS 权限被拒绝";
                Log.d("BoxWifiP2pPresenter", str);
                c0891c.m2431e();
            }
        }
        if (i6 == 3) {
            if (iArr.length <= 0 || iArr[0] != 0) {
                str = "REQUEST_CODE_REQUEST_PEERS 权限被拒绝";
                Log.d("BoxWifiP2pPresenter", str);
                c0891c.m2431e();
            } else {
                str2 = "REQUEST_CODE_REQUEST_PEERS 授权成功";
                Log.d("BoxWifiP2pPresenter", str2);
                c0891c.f4503f.m2397m();
                return;
            }
        }
        if (i6 == 4) {
            if (iArr.length <= 0 || iArr[0] != 0) {
                Log.d("BoxWifiP2pPresenter", "REQUEST_CODE_STOP_PEERS 权限被拒绝");
                return;
            }
            str2 = "REQUEST_CODE_STOP_PEERS 授权成功";
            Log.d("BoxWifiP2pPresenter", str2);
            c0891c.f4503f.m2397m();
            return;
        }
        if (i6 == 5) {
            if (iArr.length > 0 && iArr[0] == 0) {
                Log.d("BoxWifiP2pPresenter", "REQUEST_CODE_CONNECT 授权成功");
                WifiP2pDevice wifiP2pDevice = c0891c.f4504g;
                if (wifiP2pDevice != null) {
                    c0891c.f4503f.m2387c(wifiP2pDevice);
                    return;
                }
                return;
            }
        } else if (i6 == 6) {
            if (iArr.length > 0 && iArr[0] == 0) {
                Log.d("BoxWifiP2pPresenter", "REQUEST_CODE_CONNECT 授权成功");
                c0891c.f4503f.m2388d();
                return;
            }
        } else {
            if (i6 != 1) {
                return;
            }
            if (iArr.length > 0 && iArr[0] == 0) {
                Log.d("BoxWifiP2pPresenter", "REQUEST_CODE_CONNECT 授权成功");
                c0891c.f4503f.m2389e();
                return;
            }
        }
        str = "REQUEST_CODE_CONNECT 权限被拒绝";
        Log.d("BoxWifiP2pPresenter", str);
        c0891c.m2431e();
    }

    @Override // p115q4.AbstractActivityC1295a, androidx.fragment.app.ActivityC0229p, android.app.Activity
    public final void onResume() {
        super.onResume();
        C0891c c0891c = this.f3541I;
        boolean m2432f = c0891c.m2432f();
        boolean isWifiEnabled = ((WifiManager) c0891c.f4501d.getApplicationContext().getSystemService("wifi")).isWifiEnabled();
        boolean z5 = c0891c.f4503f.f4391r;
        Log.d("BoxWifiP2pPresenter", "doDiscoverPeers hasPermission:" + m2432f + ",wifiEnabled:" + isWifiEnabled + ",isConnected:" + z5);
        c0891c.f8462c.post(new RunnableC2125l(c0891c, m2432f, isWifiEnabled, 1));
        if (m2432f && isWifiEnabled && !z5) {
            c0891c.f4503f.m2389e();
        }
    }
}
