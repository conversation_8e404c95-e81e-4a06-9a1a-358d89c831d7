package com.liaoyuan.aicast.phone.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.liaoyuan.aicast.R;
import p006a5.ViewOnClickListenerC0039b;

/**
 * 断开连接提示布局组件
 * 用于显示断开连接确认对话框，包含确认和取消按钮
 */
public class DisconnectLayout extends LinearLayout {

    // 确认按钮
    private TextView confirmButton;

    // 提示布局容器
    private LinearLayout tipLayoutContainer;

    // 取消按钮
    private TextView cancelButton;

    public DisconnectLayout(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        initializeViews(context);
    }

    /**
     * 初始化视图组件
     */
    private void initializeViews(Context context) {
        // 加载布局文件
        View.inflate(context, R.layout.layout_disconnect_tip, this);

        // 初始化视图组件
        tipLayoutContainer = findViewById(R.id.tip_layout_disconnect);
        confirmButton = findViewById(R.id.tv_disconnect_confirm);
        cancelButton = findViewById(R.id.tv_disconnect_cancel);

        // 设置取消按钮点击监听器
        setupCancelButton();
    }

    /**
     * 设置取消按钮的点击监听器
     */
    private void setupCancelButton() {
        cancelButton.setOnClickListener(new ViewOnClickListenerC0039b(this, 2));
    }

    /**
     * 设置确认按钮点击监听器
     * @param onClickListener 点击监听器
     */
    public void setOnConfirmClickListener(View.OnClickListener onClickListener) {
        confirmButton.setOnClickListener(onClickListener);
    }

    /**
     * 设置取消按钮点击监听器
     * @param onClickListener 点击监听器
     */
    public void setOnCancelClickListener(View.OnClickListener onClickListener) {
        cancelButton.setOnClickListener(onClickListener);
    }

    /**
     * 显示断开连接提示
     */
    public void showDisconnectTip() {
        setVisibility(View.VISIBLE);
    }

    /**
     * 隐藏断开连接提示
     */
    public void hideDisconnectTip() {
        setVisibility(View.GONE);
    }

    /**
     * 设置确认按钮文本
     * @param text 按钮文本
     */
    public void setConfirmButtonText(String text) {
        confirmButton.setText(text);
    }

    /**
     * 设置取消按钮文本
     * @param text 按钮文本
     */
    public void setCancelButtonText(String text) {
        cancelButton.setText(text);
    }

    // Getter 方法

    public TextView getConfirmButton() {
        return confirmButton;
    }

    public TextView getCancelButton() {
        return cancelButton;
    }

    public LinearLayout getTipLayoutContainer() {
        return tipLayoutContainer;
    }
}
