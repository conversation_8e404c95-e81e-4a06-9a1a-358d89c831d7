package p169z0;

import android.annotation.TargetApi;
import android.os.Build;
import android.os.StrictMode;
import androidx.activity.result.C0052a;
import java.io.BufferedWriter;
import java.io.Closeable;
import java.io.EOFException;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.concurrent.Callable;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/* renamed from: z0.a */
/* loaded from: classes.dex */
public final class C2098a implements Closeable {

    /* renamed from: j */
    public final File f8401j;

    /* renamed from: k */
    public final File f8402k;

    /* renamed from: l */
    public final File f8403l;

    /* renamed from: m */
    public final File f8404m;

    /* renamed from: o */
    public long f8406o;

    /* renamed from: r */
    public BufferedWriter f8409r;

    /* renamed from: t */
    public int f8411t;

    /* renamed from: q */
    public long f8408q = 0;

    /* renamed from: s */
    public final LinkedHashMap<String, d> f8410s = new LinkedHashMap<>(0, 0.75f, true);

    /* renamed from: u */
    public long f8412u = 0;

    /* renamed from: v */
    public final ThreadPoolExecutor f8413v = new ThreadPoolExecutor(0, 1, 60, TimeUnit.SECONDS, new LinkedBlockingQueue(), new b());

    /* renamed from: w */
    public final Callable<Void> f8414w = new a();

    /* renamed from: n */
    public final int f8405n = 1;

    /* renamed from: p */
    public final int f8407p = 1;

    /* renamed from: z0.a$a */
    public class a implements Callable<Void> {
        public a() {
        }

        @Override // java.util.concurrent.Callable
        public final Void call() {
            synchronized (C2098a.this) {
                C2098a c2098a = C2098a.this;
                if (c2098a.f8409r != null) {
                    c2098a.m5285P();
                    if (C2098a.this.m5280I()) {
                        C2098a.this.m5284N();
                        C2098a.this.f8411t = 0;
                    }
                }
            }
            return null;
        }
    }

    /* renamed from: z0.a$b */
    public static final class b implements ThreadFactory {
        @Override // java.util.concurrent.ThreadFactory
        public final synchronized Thread newThread(Runnable runnable) {
            Thread thread;
            thread = new Thread(runnable, "glide-disk-lru-cache-thread");
            thread.setPriority(1);
            return thread;
        }
    }

    /* renamed from: z0.a$c */
    public final class c {

        /* renamed from: a */
        public final d f8416a;

        /* renamed from: b */
        public final boolean[] f8417b;

        /* renamed from: c */
        public boolean f8418c;

        public c(d dVar) {
            this.f8416a = dVar;
            this.f8417b = dVar.f8424e ? null : new boolean[C2098a.this.f8407p];
        }

        /* renamed from: a */
        public final void m5286a() {
            C2098a.m5276o(C2098a.this, this, false);
        }

        /* renamed from: b */
        public final File m5287b() {
            File file;
            synchronized (C2098a.this) {
                d dVar = this.f8416a;
                if (dVar.f8425f != this) {
                    throw new IllegalStateException();
                }
                if (!dVar.f8424e) {
                    this.f8417b[0] = true;
                }
                file = dVar.f8423d[0];
                C2098a.this.f8401j.mkdirs();
            }
            return file;
        }
    }

    /* renamed from: z0.a$d */
    public final class d {

        /* renamed from: a */
        public final String f8420a;

        /* renamed from: b */
        public final long[] f8421b;

        /* renamed from: c */
        public File[] f8422c;

        /* renamed from: d */
        public File[] f8423d;

        /* renamed from: e */
        public boolean f8424e;

        /* renamed from: f */
        public c f8425f;

        /* renamed from: g */
        public long f8426g;

        public d(String str) {
            this.f8420a = str;
            int i6 = C2098a.this.f8407p;
            this.f8421b = new long[i6];
            this.f8422c = new File[i6];
            this.f8423d = new File[i6];
            StringBuilder sb = new StringBuilder(str);
            sb.append('.');
            int length = sb.length();
            for (int i7 = 0; i7 < C2098a.this.f8407p; i7++) {
                sb.append(i7);
                this.f8422c[i7] = new File(C2098a.this.f8401j, sb.toString());
                sb.append(".tmp");
                this.f8423d[i7] = new File(C2098a.this.f8401j, sb.toString());
                sb.setLength(length);
            }
        }

        /* renamed from: a */
        public final String m5288a() {
            StringBuilder sb = new StringBuilder();
            for (long j6 : this.f8421b) {
                sb.append(' ');
                sb.append(j6);
            }
            return sb.toString();
        }

        /* renamed from: b */
        public final IOException m5289b(String[] strArr) {
            StringBuilder m104h = C0052a.m104h("unexpected journal line: ");
            m104h.append(Arrays.toString(strArr));
            throw new IOException(m104h.toString());
        }
    }

    /* renamed from: z0.a$e */
    public final class e {

        /* renamed from: a */
        public final File[] f8428a;

        public e(File[] fileArr) {
            this.f8428a = fileArr;
        }
    }

    public C2098a(File file, long j6) {
        this.f8401j = file;
        this.f8402k = new File(file, "journal");
        this.f8403l = new File(file, "journal.tmp");
        this.f8404m = new File(file, "journal.bkp");
        this.f8406o = j6;
    }

    @TargetApi(26)
    /* renamed from: D */
    public static void m5271D(Writer writer) {
        if (Build.VERSION.SDK_INT < 26) {
            writer.close();
            return;
        }
        StrictMode.ThreadPolicy threadPolicy = StrictMode.getThreadPolicy();
        StrictMode.setThreadPolicy(new StrictMode.ThreadPolicy.Builder(threadPolicy).permitUnbufferedIo().build());
        try {
            writer.close();
        } finally {
            StrictMode.setThreadPolicy(threadPolicy);
        }
    }

    /* renamed from: E */
    public static void m5272E(File file) {
        if (file.exists() && !file.delete()) {
            throw new IOException();
        }
    }

    @TargetApi(26)
    /* renamed from: G */
    public static void m5273G(Writer writer) {
        if (Build.VERSION.SDK_INT < 26) {
            writer.flush();
            return;
        }
        StrictMode.ThreadPolicy threadPolicy = StrictMode.getThreadPolicy();
        StrictMode.setThreadPolicy(new StrictMode.ThreadPolicy.Builder(threadPolicy).permitUnbufferedIo().build());
        try {
            writer.flush();
        } finally {
            StrictMode.setThreadPolicy(threadPolicy);
        }
    }

    /* renamed from: J */
    public static C2098a m5274J(File file, long j6) {
        if (j6 <= 0) {
            throw new IllegalArgumentException("maxSize <= 0");
        }
        File file2 = new File(file, "journal.bkp");
        if (file2.exists()) {
            File file3 = new File(file, "journal");
            if (file3.exists()) {
                file2.delete();
            } else {
                m5275O(file2, file3, false);
            }
        }
        C2098a c2098a = new C2098a(file, j6);
        if (c2098a.f8402k.exists()) {
            try {
                c2098a.m5282L();
                c2098a.m5281K();
                return c2098a;
            } catch (IOException e6) {
                System.out.println("DiskLruCache " + file + " is corrupt: " + e6.getMessage() + ", removing");
                c2098a.close();
                C2100c.m5292a(c2098a.f8401j);
            }
        }
        file.mkdirs();
        C2098a c2098a2 = new C2098a(file, j6);
        c2098a2.m5284N();
        return c2098a2;
    }

    /* renamed from: O */
    public static void m5275O(File file, File file2, boolean z5) {
        if (z5) {
            m5272E(file2);
        }
        if (!file.renameTo(file2)) {
            throw new IOException();
        }
    }

    /* renamed from: o */
    public static void m5276o(C2098a c2098a, c cVar, boolean z5) {
        synchronized (c2098a) {
            d dVar = cVar.f8416a;
            if (dVar.f8425f != cVar) {
                throw new IllegalStateException();
            }
            if (z5 && !dVar.f8424e) {
                for (int i6 = 0; i6 < c2098a.f8407p; i6++) {
                    if (!cVar.f8417b[i6]) {
                        cVar.m5286a();
                        throw new IllegalStateException("Newly created entry didn't create value for index " + i6);
                    }
                    if (!dVar.f8423d[i6].exists()) {
                        cVar.m5286a();
                        break;
                    }
                }
            }
            for (int i7 = 0; i7 < c2098a.f8407p; i7++) {
                File file = dVar.f8423d[i7];
                if (!z5) {
                    m5272E(file);
                } else if (file.exists()) {
                    File file2 = dVar.f8422c[i7];
                    file.renameTo(file2);
                    long j6 = dVar.f8421b[i7];
                    long length = file2.length();
                    dVar.f8421b[i7] = length;
                    c2098a.f8408q = (c2098a.f8408q - j6) + length;
                }
            }
            c2098a.f8411t++;
            dVar.f8425f = null;
            if (dVar.f8424e || z5) {
                dVar.f8424e = true;
                c2098a.f8409r.append((CharSequence) "CLEAN");
                c2098a.f8409r.append(' ');
                c2098a.f8409r.append((CharSequence) dVar.f8420a);
                c2098a.f8409r.append((CharSequence) dVar.m5288a());
                c2098a.f8409r.append('\n');
                if (z5) {
                    long j7 = c2098a.f8412u;
                    c2098a.f8412u = 1 + j7;
                    dVar.f8426g = j7;
                }
            } else {
                c2098a.f8410s.remove(dVar.f8420a);
                c2098a.f8409r.append((CharSequence) "REMOVE");
                c2098a.f8409r.append(' ');
                c2098a.f8409r.append((CharSequence) dVar.f8420a);
                c2098a.f8409r.append('\n');
            }
            m5273G(c2098a.f8409r);
            if (c2098a.f8408q > c2098a.f8406o || c2098a.m5280I()) {
                c2098a.f8413v.submit(c2098a.f8414w);
            }
        }
    }

    /* renamed from: C */
    public final void m5277C() {
        if (this.f8409r == null) {
            throw new IllegalStateException("cache is closed");
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:12:0x001c, code lost:
    
        if (r0.f8425f != null) goto L8;
     */
    /* renamed from: F */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final p169z0.C2098a.c m5278F(java.lang.String r4) {
        /*
            r3 = this;
            monitor-enter(r3)
            r3.m5277C()     // Catch: java.lang.Throwable -> L48
            java.util.LinkedHashMap<java.lang.String, z0.a$d> r0 = r3.f8410s     // Catch: java.lang.Throwable -> L48
            java.lang.Object r0 = r0.get(r4)     // Catch: java.lang.Throwable -> L48
            z0.a$d r0 = (p169z0.C2098a.d) r0     // Catch: java.lang.Throwable -> L48
            r1 = 0
            if (r0 != 0) goto L1a
            z0.a$d r0 = new z0.a$d     // Catch: java.lang.Throwable -> L48
            r0.<init>(r4)     // Catch: java.lang.Throwable -> L48
            java.util.LinkedHashMap<java.lang.String, z0.a$d> r1 = r3.f8410s     // Catch: java.lang.Throwable -> L48
            r1.put(r4, r0)     // Catch: java.lang.Throwable -> L48
            goto L20
        L1a:
            z0.a$c r2 = r0.f8425f     // Catch: java.lang.Throwable -> L48
            if (r2 == 0) goto L20
        L1e:
            monitor-exit(r3)
            goto L47
        L20:
            z0.a$c r1 = new z0.a$c     // Catch: java.lang.Throwable -> L48
            r1.<init>(r0)     // Catch: java.lang.Throwable -> L48
            r0.f8425f = r1     // Catch: java.lang.Throwable -> L48
            java.io.BufferedWriter r0 = r3.f8409r     // Catch: java.lang.Throwable -> L48
            java.lang.String r2 = "DIRTY"
            r0.append(r2)     // Catch: java.lang.Throwable -> L48
            java.io.BufferedWriter r0 = r3.f8409r     // Catch: java.lang.Throwable -> L48
            r2 = 32
            r0.append(r2)     // Catch: java.lang.Throwable -> L48
            java.io.BufferedWriter r0 = r3.f8409r     // Catch: java.lang.Throwable -> L48
            r0.append(r4)     // Catch: java.lang.Throwable -> L48
            java.io.BufferedWriter r4 = r3.f8409r     // Catch: java.lang.Throwable -> L48
            r0 = 10
            r4.append(r0)     // Catch: java.lang.Throwable -> L48
            java.io.BufferedWriter r4 = r3.f8409r     // Catch: java.lang.Throwable -> L48
            m5273G(r4)     // Catch: java.lang.Throwable -> L48
            goto L1e
        L47:
            return r1
        L48:
            r4 = move-exception
            monitor-exit(r3)
            throw r4
        */
        throw new UnsupportedOperationException("Method not decompiled: p169z0.C2098a.m5278F(java.lang.String):z0.a$c");
    }

    /* renamed from: H */
    public final synchronized e m5279H(String str) {
        m5277C();
        d dVar = this.f8410s.get(str);
        if (dVar == null) {
            return null;
        }
        if (!dVar.f8424e) {
            return null;
        }
        for (File file : dVar.f8422c) {
            if (!file.exists()) {
                return null;
            }
        }
        this.f8411t++;
        this.f8409r.append((CharSequence) "READ");
        this.f8409r.append(' ');
        this.f8409r.append((CharSequence) str);
        this.f8409r.append('\n');
        if (m5280I()) {
            this.f8413v.submit(this.f8414w);
        }
        return new e(dVar.f8422c);
    }

    /* renamed from: I */
    public final boolean m5280I() {
        int i6 = this.f8411t;
        return i6 >= 2000 && i6 >= this.f8410s.size();
    }

    /* renamed from: K */
    public final void m5281K() {
        m5272E(this.f8403l);
        Iterator<d> it = this.f8410s.values().iterator();
        while (it.hasNext()) {
            d next = it.next();
            int i6 = 0;
            if (next.f8425f == null) {
                while (i6 < this.f8407p) {
                    this.f8408q += next.f8421b[i6];
                    i6++;
                }
            } else {
                next.f8425f = null;
                while (i6 < this.f8407p) {
                    m5272E(next.f8422c[i6]);
                    m5272E(next.f8423d[i6]);
                    i6++;
                }
                it.remove();
            }
        }
    }

    /* renamed from: L */
    public final void m5282L() {
        C2099b c2099b = new C2099b(new FileInputStream(this.f8402k), C2100c.f8435a);
        try {
            String m5290C = c2099b.m5290C();
            String m5290C2 = c2099b.m5290C();
            String m5290C3 = c2099b.m5290C();
            String m5290C4 = c2099b.m5290C();
            String m5290C5 = c2099b.m5290C();
            if (!"libcore.io.DiskLruCache".equals(m5290C) || !"1".equals(m5290C2) || !Integer.toString(this.f8405n).equals(m5290C3) || !Integer.toString(this.f8407p).equals(m5290C4) || !"".equals(m5290C5)) {
                throw new IOException("unexpected journal header: [" + m5290C + ", " + m5290C2 + ", " + m5290C4 + ", " + m5290C5 + "]");
            }
            int i6 = 0;
            while (true) {
                try {
                    m5283M(c2099b.m5290C());
                    i6++;
                } catch (EOFException unused) {
                    this.f8411t = i6 - this.f8410s.size();
                    if (c2099b.f8433n == -1) {
                        m5284N();
                    } else {
                        this.f8409r = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(this.f8402k, true), C2100c.f8435a));
                    }
                    try {
                        c2099b.close();
                        return;
                    } catch (RuntimeException e6) {
                        throw e6;
                    } catch (Exception unused2) {
                        return;
                    }
                }
            }
        } catch (Throwable th) {
            try {
                c2099b.close();
            } catch (RuntimeException e7) {
                throw e7;
            } catch (Exception unused3) {
            }
            throw th;
        }
    }

    /* renamed from: M */
    public final void m5283M(String str) {
        String substring;
        int indexOf = str.indexOf(32);
        if (indexOf == -1) {
            throw new IOException(C0052a.m103g("unexpected journal line: ", str));
        }
        int i6 = indexOf + 1;
        int indexOf2 = str.indexOf(32, i6);
        if (indexOf2 == -1) {
            substring = str.substring(i6);
            if (indexOf == 6 && str.startsWith("REMOVE")) {
                this.f8410s.remove(substring);
                return;
            }
        } else {
            substring = str.substring(i6, indexOf2);
        }
        d dVar = this.f8410s.get(substring);
        if (dVar == null) {
            dVar = new d(substring);
            this.f8410s.put(substring, dVar);
        }
        if (indexOf2 == -1 || indexOf != 5 || !str.startsWith("CLEAN")) {
            if (indexOf2 == -1 && indexOf == 5 && str.startsWith("DIRTY")) {
                dVar.f8425f = new c(dVar);
                return;
            } else {
                if (indexOf2 != -1 || indexOf != 4 || !str.startsWith("READ")) {
                    throw new IOException(C0052a.m103g("unexpected journal line: ", str));
                }
                return;
            }
        }
        String[] split = str.substring(indexOf2 + 1).split(" ");
        dVar.f8424e = true;
        dVar.f8425f = null;
        if (split.length != C2098a.this.f8407p) {
            dVar.m5289b(split);
            throw null;
        }
        for (int i7 = 0; i7 < split.length; i7++) {
            try {
                dVar.f8421b[i7] = Long.parseLong(split[i7]);
            } catch (NumberFormatException unused) {
                dVar.m5289b(split);
                throw null;
            }
        }
    }

    /* renamed from: N */
    public final synchronized void m5284N() {
        StringBuilder sb;
        BufferedWriter bufferedWriter = this.f8409r;
        if (bufferedWriter != null) {
            m5271D(bufferedWriter);
        }
        BufferedWriter bufferedWriter2 = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(this.f8403l), C2100c.f8435a));
        try {
            bufferedWriter2.write("libcore.io.DiskLruCache");
            bufferedWriter2.write("\n");
            bufferedWriter2.write("1");
            bufferedWriter2.write("\n");
            bufferedWriter2.write(Integer.toString(this.f8405n));
            bufferedWriter2.write("\n");
            bufferedWriter2.write(Integer.toString(this.f8407p));
            bufferedWriter2.write("\n");
            bufferedWriter2.write("\n");
            for (d dVar : this.f8410s.values()) {
                if (dVar.f8425f != null) {
                    sb = new StringBuilder();
                    sb.append("DIRTY ");
                    sb.append(dVar.f8420a);
                    sb.append('\n');
                } else {
                    sb = new StringBuilder();
                    sb.append("CLEAN ");
                    sb.append(dVar.f8420a);
                    sb.append(dVar.m5288a());
                    sb.append('\n');
                }
                bufferedWriter2.write(sb.toString());
            }
            m5271D(bufferedWriter2);
            if (this.f8402k.exists()) {
                m5275O(this.f8402k, this.f8404m, true);
            }
            m5275O(this.f8403l, this.f8402k, false);
            this.f8404m.delete();
            this.f8409r = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(this.f8402k, true), C2100c.f8435a));
        } catch (Throwable th) {
            m5271D(bufferedWriter2);
            throw th;
        }
    }

    /* renamed from: P */
    public final void m5285P() {
        while (this.f8408q > this.f8406o) {
            String key = this.f8410s.entrySet().iterator().next().getKey();
            synchronized (this) {
                m5277C();
                d dVar = this.f8410s.get(key);
                if (dVar != null && dVar.f8425f == null) {
                    for (int i6 = 0; i6 < this.f8407p; i6++) {
                        File file = dVar.f8422c[i6];
                        if (file.exists() && !file.delete()) {
                            throw new IOException("failed to delete " + file);
                        }
                        long j6 = this.f8408q;
                        long[] jArr = dVar.f8421b;
                        this.f8408q = j6 - jArr[i6];
                        jArr[i6] = 0;
                    }
                    this.f8411t++;
                    this.f8409r.append((CharSequence) "REMOVE");
                    this.f8409r.append(' ');
                    this.f8409r.append((CharSequence) key);
                    this.f8409r.append('\n');
                    this.f8410s.remove(key);
                    if (m5280I()) {
                        this.f8413v.submit(this.f8414w);
                    }
                }
            }
        }
    }

    @Override // java.io.Closeable, java.lang.AutoCloseable
    public final synchronized void close() {
        if (this.f8409r == null) {
            return;
        }
        Iterator it = new ArrayList(this.f8410s.values()).iterator();
        while (it.hasNext()) {
            c cVar = ((d) it.next()).f8425f;
            if (cVar != null) {
                cVar.m5286a();
            }
        }
        m5285P();
        m5271D(this.f8409r);
        this.f8409r = null;
    }
}
