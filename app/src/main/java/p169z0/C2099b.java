package p169z0;

import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.EOFException;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;

/* renamed from: z0.b */
/* loaded from: classes.dex */
public final class C2099b implements Closeable {

    /* renamed from: j */
    public final InputStream f8429j;

    /* renamed from: k */
    public final Charset f8430k;

    /* renamed from: l */
    public byte[] f8431l;

    /* renamed from: m */
    public int f8432m;

    /* renamed from: n */
    public int f8433n;

    /* renamed from: z0.b$a */
    public class a extends ByteArrayOutputStream {
        public a(int i6) {
            super(i6);
        }

        @Override // java.io.ByteArrayOutputStream
        public final String toString() {
            int i6 = ((ByteArrayOutputStream) this).count;
            if (i6 > 0 && ((ByteArrayOutputStream) this).buf[i6 - 1] == 13) {
                i6--;
            }
            try {
                return new String(((ByteArrayOutputStream) this).buf, 0, i6, C2099b.this.f8430k.name());
            } catch (UnsupportedEncodingException e6) {
                throw new AssertionError(e6);
            }
        }
    }

    public C2099b(InputStream inputStream, Charset charset) {
        if (charset == null) {
            throw null;
        }
        if (!charset.equals(C2100c.f8435a)) {
            throw new IllegalArgumentException("Unsupported encoding");
        }
        this.f8429j = inputStream;
        this.f8430k = charset;
        this.f8431l = new byte[8192];
    }

    /* renamed from: C */
    public final String m5290C() {
        int i6;
        byte[] bArr;
        int i7;
        synchronized (this.f8429j) {
            if (this.f8431l == null) {
                throw new IOException("LineReader is closed");
            }
            if (this.f8432m >= this.f8433n) {
                m5291o();
            }
            for (int i8 = this.f8432m; i8 != this.f8433n; i8++) {
                byte[] bArr2 = this.f8431l;
                if (bArr2[i8] == 10) {
                    int i9 = this.f8432m;
                    if (i8 != i9) {
                        i7 = i8 - 1;
                        if (bArr2[i7] == 13) {
                            String str = new String(bArr2, i9, i7 - i9, this.f8430k.name());
                            this.f8432m = i8 + 1;
                            return str;
                        }
                    }
                    i7 = i8;
                    String str2 = new String(bArr2, i9, i7 - i9, this.f8430k.name());
                    this.f8432m = i8 + 1;
                    return str2;
                }
            }
            a aVar = new a((this.f8433n - this.f8432m) + 80);
            loop1: while (true) {
                byte[] bArr3 = this.f8431l;
                int i10 = this.f8432m;
                aVar.write(bArr3, i10, this.f8433n - i10);
                this.f8433n = -1;
                m5291o();
                i6 = this.f8432m;
                while (i6 != this.f8433n) {
                    bArr = this.f8431l;
                    if (bArr[i6] == 10) {
                        break loop1;
                    }
                    i6++;
                }
            }
            int i11 = this.f8432m;
            if (i6 != i11) {
                aVar.write(bArr, i11, i6 - i11);
            }
            this.f8432m = i6 + 1;
            return aVar.toString();
        }
    }

    @Override // java.io.Closeable, java.lang.AutoCloseable
    public final void close() {
        synchronized (this.f8429j) {
            if (this.f8431l != null) {
                this.f8431l = null;
                this.f8429j.close();
            }
        }
    }

    /* renamed from: o */
    public final void m5291o() {
        InputStream inputStream = this.f8429j;
        byte[] bArr = this.f8431l;
        int read = inputStream.read(bArr, 0, bArr.length);
        if (read == -1) {
            throw new EOFException();
        }
        this.f8432m = 0;
        this.f8433n = read;
    }
}
