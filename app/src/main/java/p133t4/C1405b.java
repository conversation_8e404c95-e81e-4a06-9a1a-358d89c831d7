package p133t4;

import android.content.Context;
import android.media.projection.MediaProjection;
import android.util.Log;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import p140u4.C1429b;
import p140u4.InterfaceC1428a;
import p147v4.C1463b;
import p147v4.InterfaceC1462a;

/* renamed from: t4.b */
/* loaded from: classes.dex */
public final class C1405b {

    /* renamed from: a */
    public Context f6476a;

    /* renamed from: b */
    public boolean f6477b;

    /* renamed from: d */
    public C1463b f6479d;

    /* renamed from: e */
    public C1429b f6480e;

    /* renamed from: c */
    public List<InterfaceC1404a> f6478c = new ArrayList();

    /* renamed from: f */
    public a f6481f = new a();

    /* renamed from: g */
    public b f6482g = new b();

    /* renamed from: t4.b$a */
    public class a implements InterfaceC1462a {
        public a() {
        }

        /* JADX WARN: Type inference failed for: r0v1, types: [java.util.ArrayList, java.util.List<t4.a>] */
        @Override // p147v4.InterfaceC1462a
        /* renamed from: b */
        public final void mo1908b(boolean z5) {
            Iterator it = C1405b.this.f6478c.iterator();
            while (it.hasNext()) {
                ((InterfaceC1404a) it.next()).mo1908b(z5);
            }
        }

        /* JADX WARN: Type inference failed for: r0v1, types: [java.util.ArrayList, java.util.List<t4.a>] */
        @Override // p147v4.InterfaceC1462a
        /* renamed from: c */
        public final void mo1909c(MediaProjection mediaProjection, int i6) {
            Iterator it = C1405b.this.f6478c.iterator();
            while (it.hasNext()) {
                ((InterfaceC1404a) it.next()).mo1909c(mediaProjection, i6);
            }
        }
    }

    /* renamed from: t4.b$b */
    public class b implements InterfaceC1428a {
        public b() {
        }

        /* JADX WARN: Type inference failed for: r0v1, types: [java.util.ArrayList, java.util.List<t4.a>] */
        @Override // p140u4.InterfaceC1428a
        /* renamed from: a */
        public final void mo1907a(boolean z5) {
            Iterator it = C1405b.this.f6478c.iterator();
            while (it.hasNext()) {
                ((InterfaceC1404a) it.next()).mo1907a(z5);
            }
        }
    }

    /* renamed from: t4.b$c */
    public static class c {

        /* renamed from: a */
        public static final C1405b f6485a = new C1405b();
    }

    /* renamed from: a */
    public final int m3426a() {
        C1463b c1463b = this.f6479d;
        Objects.requireNonNull(c1463b);
        Log.d("ScreenCapturePermissions", "getRequestCode:" + c1463b.f6731d);
        return c1463b.f6731d;
    }
}
