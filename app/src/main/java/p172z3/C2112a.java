package p172z3;

import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.view.View;
import com.google.android.material.tabs.C0547a;
import com.google.android.material.tabs.TabLayout;
import p171z2.C2103a;

/* renamed from: z3.a */
/* loaded from: classes.dex */
public final class C2112a extends C0547a {
    @Override // com.google.android.material.tabs.C0547a
    /* renamed from: b */
    public final void mo1825b(TabLayout tabLayout, View view, View view2, float f6, Drawable drawable) {
        float cos;
        float f7;
        RectF m1824a = C0547a.m1824a(tabLayout, view);
        RectF m1824a2 = C0547a.m1824a(tabLayout, view2);
        if (m1824a.left < m1824a2.left) {
            double d6 = (f6 * 3.141592653589793d) / 2.0d;
            f7 = (float) (1.0d - Math.cos(d6));
            cos = (float) Math.sin(d6);
        } else {
            double d7 = (f6 * 3.141592653589793d) / 2.0d;
            float sin = (float) Math.sin(d7);
            cos = (float) (1.0d - Math.cos(d7));
            f7 = sin;
        }
        drawable.setBounds(C2103a.m5293a((int) m1824a.left, (int) m1824a2.left, f7), drawable.getBounds().top, C2103a.m5293a((int) m1824a.right, (int) m1824a2.right, cos), drawable.getBounds().bottom);
    }
}
