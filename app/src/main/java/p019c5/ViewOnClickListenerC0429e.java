package p019c5;

import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.net.wifi.p2p.WifiP2pDevice;
import android.util.Log;
import android.view.View;
import androidx.activity.result.C0052a;
import com.liaoyuan.aicast.LinkApp;
import com.liaoyuan.aicast.R;
import com.liaoyuan.aicast.phone.widget.BrowserLayout;
import com.liaoyuan.aicast.phone.wifip2p.view.WifiP2pActivity;
import com.p020ly.appupdate.download.VersionInfo;
import java.util.Locale;
import java.util.Objects;
import p019c5.C0428d;
import p031e2.RunnableC0801q;
import p034e5.AbstractC0827a;
import p034e5.C0828b;
import p048g5.C0891c;
import p062i5.C0953b;
import p069j5.C0975b;
import p076k5.C1043a;
import p081l3.C1064b;
import p127s4.DialogInterfaceOnClickListenerC1344c;

/* renamed from: c5.e */
/* loaded from: classes.dex */
public final /* synthetic */ class ViewOnClickListenerC0429e implements View.OnClickListener {

    /* renamed from: j */
    public final /* synthetic */ int f2526j;

    /* renamed from: k */
    public final /* synthetic */ C0828b f2527k;

    /* renamed from: l */
    public final /* synthetic */ AbstractC0827a f2528l;

    public /* synthetic */ ViewOnClickListenerC0429e(AbstractC0827a abstractC0827a, C0828b c0828b, int i6) {
        this.f2526j = i6;
        this.f2528l = abstractC0827a;
        this.f2527k = c0828b;
    }

    @Override // android.view.View.OnClickListener
    public final void onClick(View view) {
        C1064b c1064b;
        DialogInterface.OnClickListener onClickListener;
        String str;
        String str2;
        final int i6 = 0;
        final int i7 = 1;
        switch (this.f2526j) {
            case 0:
                C0428d.c cVar = (C0428d.c) this.f2528l;
                C0828b c0828b = this.f2527k;
                Objects.requireNonNull(cVar);
                int m1060e = c0828b.m1060e();
                if (m1060e >= 0 && m1060e < cVar.mo1081c()) {
                    C0428d.b bVar = (C0428d.b) cVar.m2310p(m1060e);
                    int i8 = bVar.f2522a;
                    C0428d c0428d = cVar.f2525f;
                    int i9 = C0428d.f2511j0;
                    Objects.requireNonNull(c0428d);
                    if (i8 != 0) {
                        int i10 = bVar.f2522a;
                        C0428d c0428d2 = cVar.f2525f;
                        if (i10 != c0428d2.f2517f0) {
                            if (i10 != c0428d2.f2518g0) {
                                if (i10 == c0428d2.f2519h0) {
                                    C0953b.m2491b(cVar.f4244c);
                                    break;
                                }
                            } else {
                                C0953b.m2492c(cVar.f4244c);
                                break;
                            }
                        } else {
                            Context context = cVar.f4244c;
                            String string = context.getResources().getString(R.string.settings_item_user_help);
                            Locale locale = context.getResources().getConfiguration().locale;
                            Locale build = new Locale.Builder().setLanguage("zh").setScript("Hans").setRegion("CN").build();
                            Locale build2 = new Locale.Builder().setLanguage("zh").setScript("Hant").setRegion("CN").build();
                            StringBuilder m104h = C0052a.m104h("getUserHelpUrl current:");
                            m104h.append(locale.toString());
                            m104h.append(",buildCN:");
                            m104h.append(build.toString());
                            m104h.append(",buildTW:");
                            m104h.append(build2.toString());
                            Log.d("StartHtmUtils", m104h.toString());
                            if (build.toString().contains(locale.toString())) {
                                int i11 = LinkApp.f3448j;
                                str2 = "file:///android_asset/zh_userhelp.htm";
                            } else if (build2.toString().contains(locale.toString())) {
                                int i12 = LinkApp.f3448j;
                                str2 = "file:///android_asset/tw_userhelp.htm";
                            } else {
                                int i13 = LinkApp.f3448j;
                                str2 = "file:///android_asset/en_userhelp.htm";
                            }
                            Log.d("StartHtmUtils", "getUserHelpUrl url:" + str2);
                            C0953b.m2490a(context, string, str2);
                            break;
                        }
                    } else {
                        C1043a c1043a = cVar.f2525f.f2512a0;
                        c1043a.f4982g = true;
                        if (!c1043a.f4980e.m2522b()) {
                            C0975b c0975b = c1043a.f4981f;
                            c0975b.f4773c.execute(new RunnableC0801q(c0975b, 4));
                            break;
                        } else {
                            VersionInfo versionInfo = c1043a.f4980e.f4788f;
                            String url = versionInfo != null ? versionInfo.getUrl() : "";
                            Intent intent = new Intent();
                            intent.setAction("android.intent.action.VIEW");
                            intent.setFlags(268435456);
                            intent.setData(Uri.parse(url));
                            c1043a.f4979d.startActivity(intent);
                            break;
                        }
                    }
                }
                break;
            case 1:
                ((BrowserLayout.C0668b) this.f2528l).m1920q(this.f2527k, false);
                break;
            default:
                final WifiP2pActivity.C0674b c0674b = (WifiP2pActivity.C0674b) this.f2528l;
                C0828b c0828b2 = this.f2527k;
                Objects.requireNonNull(c0674b);
                int m1060e2 = c0828b2.m1060e();
                Log.d("WifiP2pActivity", "onClick adapterPosition:" + m1060e2 + ",getItemCount:" + c0674b.mo1081c());
                if (m1060e2 >= 0 && m1060e2 < c0674b.mo1081c()) {
                    WifiP2pDevice wifiP2pDevice = (WifiP2pDevice) c0674b.m2310p(m1060e2);
                    int i14 = wifiP2pDevice.status;
                    if (i14 != 0) {
                        if (i14 != 1) {
                            C0891c c0891c = c0674b.f3546f.f3541I;
                            c0891c.f4504g = wifiP2pDevice;
                            c0891c.f4503f.m2387c(wifiP2pDevice);
                            break;
                        } else {
                            c1064b = new C1064b(c0674b.f3546f);
                            c1064b.f284a.f267d = c0674b.f3546f.getString(R.string.devices_cancel_connect_title);
                            c1064b.f284a.f269f = String.format(c0674b.f3546f.getString(R.string.devices_cancel_connect_message), wifiP2pDevice.deviceName);
                            c1064b.m2717c(c0674b.f3546f.getString(R.string.devices_dialog_cancel), DialogInterfaceOnClickListenerC1344c.f6237m);
                            String string2 = c0674b.f3546f.getString(R.string.devices_dialog_confirm);
                            onClickListener = new DialogInterface.OnClickListener() { // from class: h5.a
                                @Override // android.content.DialogInterface.OnClickListener
                                public final void onClick(DialogInterface dialogInterface, int i15) {
                                    switch (i7) {
                                        case 0:
                                            C0891c c0891c2 = c0674b.f3546f.f3541I;
                                            c0891c2.f4503f.m2390f();
                                            c0891c2.f4503f.m2388d();
                                            dialogInterface.dismiss();
                                            break;
                                        default:
                                            c0674b.f3546f.f3541I.f4503f.m2386b();
                                            dialogInterface.dismiss();
                                            break;
                                    }
                                }
                            };
                            str = string2;
                        }
                    } else {
                        c1064b = new C1064b(c0674b.f3546f);
                        c1064b.f284a.f267d = c0674b.f3546f.getString(R.string.devices_disconnect_title);
                        c1064b.f284a.f269f = String.format(c0674b.f3546f.getString(R.string.devices_disconnect_message), wifiP2pDevice.deviceName);
                        c1064b.m2717c(c0674b.f3546f.getString(R.string.devices_dialog_cancel), DialogInterfaceOnClickListenerC1344c.f6236l);
                        str = c0674b.f3546f.getString(R.string.devices_dialog_confirm);
                        onClickListener = new DialogInterface.OnClickListener() { // from class: h5.a
                            @Override // android.content.DialogInterface.OnClickListener
                            public final void onClick(DialogInterface dialogInterface, int i15) {
                                switch (i6) {
                                    case 0:
                                        C0891c c0891c2 = c0674b.f3546f.f3541I;
                                        c0891c2.f4503f.m2390f();
                                        c0891c2.f4503f.m2388d();
                                        dialogInterface.dismiss();
                                        break;
                                    default:
                                        c0674b.f3546f.f3541I.f4503f.m2386b();
                                        dialogInterface.dismiss();
                                        break;
                                }
                            }
                        };
                    }
                    c1064b.m2718d(str, onClickListener);
                    c1064b.mo135a();
                    c1064b.m136b().getWindow().setBackgroundDrawableResource(R.drawable.dialog_bg);
                    break;
                }
                break;
        }
    }
}
