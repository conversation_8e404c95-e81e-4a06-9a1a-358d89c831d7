package p171z2;

import android.graphics.Matrix;
import android.util.Property;
import android.widget.ImageView;

/* renamed from: z2.e */
/* loaded from: classes.dex */
public final class C2107e extends Property<ImageView, Matrix> {

    /* renamed from: a */
    public final Matrix f8449a;

    public C2107e() {
        super(Matrix.class, "imageMatrixProperty");
        this.f8449a = new Matrix();
    }

    @Override // android.util.Property
    public final Matrix get(ImageView imageView) {
        this.f8449a.set(imageView.getImageMatrix());
        return this.f8449a;
    }

    @Override // android.util.Property
    public final void set(ImageView imageView, Matrix matrix) {
        imageView.setImageMatrix(matrix);
    }
}
