package p171z2;

import android.animation.Animator;
import android.animation.TimeInterpolator;
import android.animation.ValueAnimator;

/* renamed from: z2.h */
/* loaded from: classes.dex */
public final class C2110h {

    /* renamed from: a */
    public long f8455a;

    /* renamed from: b */
    public long f8456b;

    /* renamed from: c */
    public TimeInterpolator f8457c;

    /* renamed from: d */
    public int f8458d;

    /* renamed from: e */
    public int f8459e;

    public C2110h(long j6) {
        this.f8457c = null;
        this.f8458d = 0;
        this.f8459e = 1;
        this.f8455a = j6;
        this.f8456b = 150L;
    }

    public C2110h(long j6, long j7, TimeInterpolator timeInterpolator) {
        this.f8458d = 0;
        this.f8459e = 1;
        this.f8455a = j6;
        this.f8456b = j7;
        this.f8457c = timeInterpolator;
    }

    /* renamed from: a */
    public final void m5297a(Animator animator) {
        animator.setStartDelay(this.f8455a);
        animator.setDuration(this.f8456b);
        animator.setInterpolator(m5298b());
        if (animator instanceof ValueAnimator) {
            ValueAnimator valueAnimator = (ValueAnimator) animator;
            valueAnimator.setRepeatCount(this.f8458d);
            valueAnimator.setRepeatMode(this.f8459e);
        }
    }

    /* renamed from: b */
    public final TimeInterpolator m5298b() {
        TimeInterpolator timeInterpolator = this.f8457c;
        return timeInterpolator != null ? timeInterpolator : C2103a.f8442b;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof C2110h)) {
            return false;
        }
        C2110h c2110h = (C2110h) obj;
        if (this.f8455a == c2110h.f8455a && this.f8456b == c2110h.f8456b && this.f8458d == c2110h.f8458d && this.f8459e == c2110h.f8459e) {
            return m5298b().getClass().equals(c2110h.m5298b().getClass());
        }
        return false;
    }

    public final int hashCode() {
        long j6 = this.f8455a;
        long j7 = this.f8456b;
        return ((((m5298b().getClass().hashCode() + (((((int) (j6 ^ (j6 >>> 32))) * 31) + ((int) ((j7 >>> 32) ^ j7))) * 31)) * 31) + this.f8458d) * 31) + this.f8459e;
    }

    public final String toString() {
        return '\n' + C2110h.class.getName() + '{' + Integer.toHexString(System.identityHashCode(this)) + " delay: " + this.f8455a + " duration: " + this.f8456b + " interpolator: " + m5298b().getClass() + " repeatCount: " + this.f8458d + " repeatMode: " + this.f8459e + "}\n";
    }
}
