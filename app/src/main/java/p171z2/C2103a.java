package p171z2;

import android.view.animation.DecelerateInterpolator;
import android.view.animation.LinearInterpolator;
import p097o0.C1133a;
import p097o0.C1134b;
import p097o0.C1135c;

/* renamed from: z2.a */
/* loaded from: classes.dex */
public final class C2103a {

    /* renamed from: a */
    public static final LinearInterpolator f8441a = new LinearInterpolator();

    /* renamed from: b */
    public static final C1134b f8442b = new C1134b();

    /* renamed from: c */
    public static final C1133a f8443c = new C1133a();

    /* renamed from: d */
    public static final C1135c f8444d = new C1135c();

    /* renamed from: e */
    public static final DecelerateInterpolator f8445e = new DecelerateInterpolator();

    /* renamed from: a */
    public static int m5293a(int i6, int i7, float f6) {
        return Math.round(f6 * (i7 - i6)) + i6;
    }
}
