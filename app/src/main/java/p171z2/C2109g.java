package p171z2;

import android.animation.Animator;
import android.animation.AnimatorInflater;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.animation.TimeInterpolator;
import android.content.Context;
import android.util.Log;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import androidx.activity.result.C0052a;
import java.util.ArrayList;
import java.util.List;
import p077l.C1053g;

/* renamed from: z2.g */
/* loaded from: classes.dex */
public final class C2109g {

    /* renamed from: a */
    public final C1053g<String, C2110h> f8453a = new C1053g<>();

    /* renamed from: b */
    public final C1053g<String, PropertyValuesHolder[]> f8454b = new C1053g<>();

    /* renamed from: a */
    public static C2109g m5294a(Context context, int i6) {
        try {
            Animator loadAnimator = AnimatorInflater.loadAnimator(context, i6);
            if (loadAnimator instanceof AnimatorSet) {
                return m5295b(((AnimatorSet) loadAnimator).getChildAnimations());
            }
            if (loadAnimator == null) {
                return null;
            }
            ArrayList arrayList = new ArrayList();
            arrayList.add(loadAnimator);
            return m5295b(arrayList);
        } catch (Exception e6) {
            StringBuilder m104h = C0052a.m104h("Can't load animation resource ID #0x");
            m104h.append(Integer.toHexString(i6));
            Log.w("MotionSpec", m104h.toString(), e6);
            return null;
        }
    }

    /* renamed from: b */
    public static C2109g m5295b(List<Animator> list) {
        C2109g c2109g = new C2109g();
        int size = list.size();
        for (int i6 = 0; i6 < size; i6++) {
            Animator animator = list.get(i6);
            if (!(animator instanceof ObjectAnimator)) {
                throw new IllegalArgumentException("Animator must be an ObjectAnimator: " + animator);
            }
            ObjectAnimator objectAnimator = (ObjectAnimator) animator;
            c2109g.f8454b.put(objectAnimator.getPropertyName(), objectAnimator.getValues());
            String propertyName = objectAnimator.getPropertyName();
            long startDelay = objectAnimator.getStartDelay();
            long duration = objectAnimator.getDuration();
            TimeInterpolator interpolator = objectAnimator.getInterpolator();
            if ((interpolator instanceof AccelerateDecelerateInterpolator) || interpolator == null) {
                interpolator = C2103a.f8442b;
            } else if (interpolator instanceof AccelerateInterpolator) {
                interpolator = C2103a.f8443c;
            } else if (interpolator instanceof DecelerateInterpolator) {
                interpolator = C2103a.f8444d;
            }
            C2110h c2110h = new C2110h(startDelay, duration, interpolator);
            c2110h.f8458d = objectAnimator.getRepeatCount();
            c2110h.f8459e = objectAnimator.getRepeatMode();
            c2109g.f8453a.put(propertyName, c2110h);
        }
        return c2109g;
    }

    /* renamed from: c */
    public final C2110h m5296c(String str) {
        if (this.f8453a.getOrDefault(str, null) != null) {
            return this.f8453a.getOrDefault(str, null);
        }
        throw new IllegalArgumentException();
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj instanceof C2109g) {
            return this.f8453a.equals(((C2109g) obj).f8453a);
        }
        return false;
    }

    public final int hashCode() {
        return this.f8453a.hashCode();
    }

    public final String toString() {
        return '\n' + C2109g.class.getName() + '{' + Integer.toHexString(System.identityHashCode(this)) + " timings: " + this.f8453a + "}\n";
    }
}
