package p171z2;

import android.util.Property;
import android.view.ViewGroup;
import com.liaoyuan.aicast.R;

/* renamed from: z2.c */
/* loaded from: classes.dex */
public final class C2105c extends Property<ViewGroup, Float> {

    /* renamed from: a */
    public static final Property<ViewGroup, Float> f8447a = new C2105c();

    public C2105c() {
        super(Float.class, "childrenAlpha");
    }

    @Override // android.util.Property
    public final Float get(ViewGroup viewGroup) {
        Float f6 = (Float) viewGroup.getTag(R.id.mtrl_internal_children_alpha_tag);
        return f6 != null ? f6 : Float.valueOf(1.0f);
    }

    @Override // android.util.Property
    public final void set(ViewGroup viewGroup, Float f6) {
        ViewGroup viewGroup2 = viewGroup;
        float floatValue = f6.floatValue();
        viewGroup2.setTag(R.id.mtrl_internal_children_alpha_tag, Float.valueOf(floatValue));
        int childCount = viewGroup2.getChildCount();
        for (int i6 = 0; i6 < childCount; i6++) {
            viewGroup2.getChildAt(i6).setAlpha(floatValue);
        }
    }
}
