package p171z2;

import android.graphics.drawable.Drawable;
import android.util.Property;
import java.util.WeakHashMap;

/* renamed from: z2.d */
/* loaded from: classes.dex */
public final class C2106d extends Property<Drawable, Integer> {

    /* renamed from: a */
    public static final Property<Drawable, Integer> f8448a = new C2106d();

    public C2106d() {
        super(Integer.class, "drawableAlphaCompat");
        new WeakHashMap();
    }

    @Override // android.util.Property
    public final Integer get(Drawable drawable) {
        return Integer.valueOf(drawable.getAlpha());
    }

    @Override // android.util.Property
    public final void set(Drawable drawable, Integer num) {
        drawable.setAlpha(num.intValue());
    }
}
