package p081l3;

import android.content.DialogInterface;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.InsetDrawable;
import android.view.View;
import android.view.Window;
import androidx.appcompat.app.AlertController;
import androidx.appcompat.app.DialogC0063b;
import java.util.WeakHashMap;
import p029e0.C0766p;
import p029e0.C0769s;
import p153w3.C1799f;

/* renamed from: l3.b */
/* loaded from: classes.dex */
public final class C1064b extends DialogC0063b.a {

    /* renamed from: c */
    public C1799f f5093c;

    /* renamed from: d */
    public final Rect f5094d;

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public C1064b(android.content.Context r14) {
        /*
            r13 = this;
            r0 = 2130969116(0x7f04021c, float:1.7546905E38)
            android.util.TypedValue r1 = p132t3.C1400b.m3412a(r14, r0)
            r2 = 0
            if (r1 != 0) goto Lc
            r1 = r2
            goto Le
        Lc:
            int r1 = r1.data
        Le:
            r3 = 0
            r4 = **********(0x7f040027, float:1.7545889E38)
            r5 = **********(0x7f1100eb, float:1.9274283E38)
            android.content.Context r6 = p012b4.C0409a.m1460a(r14, r3, r4, r5)
            if (r1 != 0) goto L1c
            goto L22
        L1c:
            g.c r7 = new g.c
            r7.<init>(r6, r1)
            r6 = r7
        L22:
            android.util.TypedValue r14 = p132t3.C1400b.m3412a(r14, r0)
            if (r14 != 0) goto L2a
            r14 = r2
            goto L2c
        L2a:
            int r14 = r14.data
        L2c:
            r13.<init>(r6, r14)
            androidx.appcompat.app.AlertController$b r14 = r13.f284a
            android.content.Context r14 = r14.f264a
            android.content.res.Resources$Theme r0 = r14.getTheme()
            r9 = **********(0x7f040027, float:1.7545889E38)
            r10 = **********(0x7f1100eb, float:1.9274283E38)
            int[] r8 = p008b0.C0385m.f2350l0
            int[] r11 = new int[r2]
            r7 = 0
            r6 = r14
            android.content.res.TypedArray r1 = p114q3.C1290k.m3194d(r6, r7, r8, r9, r10, r11)
            android.content.res.Resources r6 = r14.getResources()
            r7 = **********(0x7f0700c6, float:1.794498E38)
            int r6 = r6.getDimensionPixelSize(r7)
            r7 = 2
            int r6 = r1.getDimensionPixelSize(r7, r6)
            android.content.res.Resources r7 = r14.getResources()
            r8 = **********(0x7f0700c7, float:1.7944982E38)
            int r7 = r7.getDimensionPixelSize(r8)
            r8 = 3
            int r7 = r1.getDimensionPixelSize(r8, r7)
            android.content.res.Resources r8 = r14.getResources()
            r9 = **********(0x7f0700c5, float:1.7944978E38)
            int r8 = r8.getDimensionPixelSize(r9)
            r9 = 1
            int r8 = r1.getDimensionPixelSize(r9, r8)
            android.content.res.Resources r10 = r14.getResources()
            r11 = **********(0x7f0700c4, float:1.7944975E38)
            int r10 = r10.getDimensionPixelSize(r11)
            int r2 = r1.getDimensionPixelSize(r2, r10)
            r1.recycle()
            android.content.res.Resources r1 = r14.getResources()
            android.content.res.Configuration r1 = r1.getConfiguration()
            int r1 = r1.getLayoutDirection()
            if (r1 != r9) goto L9a
            r12 = r8
            r8 = r6
            r6 = r12
        L9a:
            android.graphics.Rect r1 = new android.graphics.Rect
            r1.<init>(r6, r7, r8, r2)
            r13.f5094d = r1
            r1 = **********(0x7f0400c8, float:1.7546215E38)
            java.lang.Class<l3.b> r2 = p081l3.C1064b.class
            java.lang.String r2 = r2.getCanonicalName()
            int r1 = p132t3.C1400b.m3413b(r14, r1, r2)
            w3.f r2 = new w3.f
            r2.<init>(r14, r3, r4, r5)
            r2.m4585l(r14)
            android.content.res.ColorStateList r14 = android.content.res.ColorStateList.valueOf(r1)
            r2.m4587n(r14)
            int r14 = android.os.Build.VERSION.SDK_INT
            r1 = 28
            if (r14 < r1) goto Lf3
            android.util.TypedValue r14 = new android.util.TypedValue
            r14.<init>()
            r1 = 16844145(0x1010571, float:2.3697462E-38)
            r0.resolveAttribute(r1, r14, r9)
            androidx.appcompat.app.AlertController$b r0 = r13.f284a
            android.content.Context r0 = r0.f264a
            android.content.res.Resources r0 = r0.getResources()
            android.util.DisplayMetrics r0 = r0.getDisplayMetrics()
            float r0 = r14.getDimension(r0)
            int r14 = r14.type
            r1 = 5
            if (r14 != r1) goto Lf3
            r14 = 0
            int r14 = (r0 > r14 ? 1 : (r0 == r14 ? 0 : -1))
            if (r14 < 0) goto Lf3
            w3.f$b r14 = r2.f7360j
            w3.i r14 = r14.f7378a
            w3.i r14 = r14.m4600e(r0)
            r2.setShapeAppearanceModel(r14)
        Lf3:
            r13.f5093c = r2
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p081l3.C1064b.<init>(android.content.Context):void");
    }

    @Override // androidx.appcompat.app.DialogC0063b.a
    /* renamed from: a */
    public final DialogC0063b mo135a() {
        DialogC0063b mo135a = super.mo135a();
        Window window = mo135a.getWindow();
        View decorView = window.getDecorView();
        C1799f c1799f = this.f5093c;
        if (c1799f instanceof C1799f) {
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            c1799f.m4586m(decorView.getElevation());
        }
        C1799f c1799f2 = this.f5093c;
        Rect rect = this.f5094d;
        window.setBackgroundDrawable(new InsetDrawable((Drawable) c1799f2, rect.left, rect.top, rect.right, rect.bottom));
        decorView.setOnTouchListener(new ViewOnTouchListenerC1063a(mo135a, this.f5094d));
        return mo135a;
    }

    /* renamed from: c */
    public final C1064b m2717c(CharSequence charSequence, DialogInterface.OnClickListener onClickListener) {
        AlertController.C0059b c0059b = this.f284a;
        c0059b.f272i = charSequence;
        c0059b.f273j = onClickListener;
        return this;
    }

    /* renamed from: d */
    public final C1064b m2718d(CharSequence charSequence, DialogInterface.OnClickListener onClickListener) {
        AlertController.C0059b c0059b = this.f284a;
        c0059b.f270g = charSequence;
        c0059b.f271h = onClickListener;
        return this;
    }
}
