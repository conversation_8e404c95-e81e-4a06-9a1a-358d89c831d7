package p153w3;

import android.graphics.Canvas;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RadialGradient;
import android.graphics.RectF;
import android.graphics.Region;
import android.graphics.Shader;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import p146v3.C1460a;

/* renamed from: w3.k */
/* loaded from: classes.dex */
public final class C1804k {

    /* renamed from: a */
    @Deprecated
    public float f7437a;

    /* renamed from: b */
    @Deprecated
    public float f7438b;

    /* renamed from: c */
    @Deprecated
    public float f7439c;

    /* renamed from: d */
    @Deprecated
    public float f7440d;

    /* renamed from: e */
    @Deprecated
    public float f7441e;

    /* renamed from: f */
    @Deprecated
    public float f7442f;

    /* renamed from: g */
    public final List<f> f7443g = new ArrayList();

    /* renamed from: h */
    public final List<g> f7444h = new ArrayList();

    /* renamed from: w3.k$a */
    public class a extends g {

        /* renamed from: b */
        public final /* synthetic */ List f7445b;

        /* renamed from: c */
        public final /* synthetic */ Matrix f7446c;

        public a(List list, Matrix matrix) {
            this.f7445b = list;
            this.f7446c = matrix;
        }

        @Override // p153w3.C1804k.g
        /* renamed from: a */
        public final void mo4615a(Matrix matrix, C1460a c1460a, int i6, Canvas canvas) {
            Iterator it = this.f7445b.iterator();
            while (it.hasNext()) {
                ((g) it.next()).mo4615a(this.f7446c, c1460a, i6, canvas);
            }
        }
    }

    /* renamed from: w3.k$b */
    public static class b extends g {

        /* renamed from: b */
        public final d f7447b;

        public b(d dVar) {
            this.f7447b = dVar;
        }

        @Override // p153w3.C1804k.g
        /* renamed from: a */
        public final void mo4615a(Matrix matrix, C1460a c1460a, int i6, Canvas canvas) {
            d dVar = this.f7447b;
            float f6 = dVar.f7456f;
            float f7 = dVar.f7457g;
            d dVar2 = this.f7447b;
            RectF rectF = new RectF(dVar2.f7452b, dVar2.f7453c, dVar2.f7454d, dVar2.f7455e);
            boolean z5 = f7 < 0.0f;
            Path path = c1460a.f6726g;
            if (z5) {
                int[] iArr = C1460a.f6718k;
                iArr[0] = 0;
                iArr[1] = c1460a.f6725f;
                iArr[2] = c1460a.f6724e;
                iArr[3] = c1460a.f6723d;
            } else {
                path.rewind();
                path.moveTo(rectF.centerX(), rectF.centerY());
                path.arcTo(rectF, f6, f7);
                path.close();
                float f8 = -i6;
                rectF.inset(f8, f8);
                int[] iArr2 = C1460a.f6718k;
                iArr2[0] = 0;
                iArr2[1] = c1460a.f6723d;
                iArr2[2] = c1460a.f6724e;
                iArr2[3] = c1460a.f6725f;
            }
            float width = rectF.width() / 2.0f;
            if (width <= 0.0f) {
                return;
            }
            float f9 = 1.0f - (i6 / width);
            float[] fArr = C1460a.f6719l;
            fArr[1] = f9;
            fArr[2] = ((1.0f - f9) / 2.0f) + f9;
            c1460a.f6721b.setShader(new RadialGradient(rectF.centerX(), rectF.centerY(), width, C1460a.f6718k, fArr, Shader.TileMode.CLAMP));
            canvas.save();
            canvas.concat(matrix);
            canvas.scale(1.0f, rectF.height() / rectF.width());
            if (!z5) {
                canvas.clipPath(path, Region.Op.DIFFERENCE);
                canvas.drawPath(path, c1460a.f6727h);
            }
            canvas.drawArc(rectF, f6, f7, true, c1460a.f6721b);
            canvas.restore();
        }
    }

    /* renamed from: w3.k$c */
    public static class c extends g {

        /* renamed from: b */
        public final e f7448b;

        /* renamed from: c */
        public final float f7449c;

        /* renamed from: d */
        public final float f7450d;

        public c(e eVar, float f6, float f7) {
            this.f7448b = eVar;
            this.f7449c = f6;
            this.f7450d = f7;
        }

        @Override // p153w3.C1804k.g
        /* renamed from: a */
        public final void mo4615a(Matrix matrix, C1460a c1460a, int i6, Canvas canvas) {
            e eVar = this.f7448b;
            RectF rectF = new RectF(0.0f, 0.0f, (float) Math.hypot(eVar.f7459c - this.f7450d, eVar.f7458b - this.f7449c), 0.0f);
            Matrix matrix2 = new Matrix(matrix);
            matrix2.preTranslate(this.f7449c, this.f7450d);
            matrix2.preRotate(m4616b());
            Objects.requireNonNull(c1460a);
            rectF.bottom += i6;
            rectF.offset(0.0f, -i6);
            int[] iArr = C1460a.f6716i;
            iArr[0] = c1460a.f6725f;
            iArr[1] = c1460a.f6724e;
            iArr[2] = c1460a.f6723d;
            Paint paint = c1460a.f6722c;
            float f6 = rectF.left;
            paint.setShader(new LinearGradient(f6, rectF.top, f6, rectF.bottom, iArr, C1460a.f6717j, Shader.TileMode.CLAMP));
            canvas.save();
            canvas.concat(matrix2);
            canvas.drawRect(rectF, c1460a.f6722c);
            canvas.restore();
        }

        /* renamed from: b */
        public final float m4616b() {
            e eVar = this.f7448b;
            return (float) Math.toDegrees(Math.atan((eVar.f7459c - this.f7450d) / (eVar.f7458b - this.f7449c)));
        }
    }

    /* renamed from: w3.k$d */
    public static class d extends f {

        /* renamed from: h */
        public static final RectF f7451h = new RectF();

        /* renamed from: b */
        @Deprecated
        public float f7452b;

        /* renamed from: c */
        @Deprecated
        public float f7453c;

        /* renamed from: d */
        @Deprecated
        public float f7454d;

        /* renamed from: e */
        @Deprecated
        public float f7455e;

        /* renamed from: f */
        @Deprecated
        public float f7456f;

        /* renamed from: g */
        @Deprecated
        public float f7457g;

        public d(float f6, float f7, float f8, float f9) {
            this.f7452b = f6;
            this.f7453c = f7;
            this.f7454d = f8;
            this.f7455e = f9;
        }

        @Override // p153w3.C1804k.f
        /* renamed from: a */
        public final void mo4617a(Matrix matrix, Path path) {
            Matrix matrix2 = this.f7460a;
            matrix.invert(matrix2);
            path.transform(matrix2);
            RectF rectF = f7451h;
            rectF.set(this.f7452b, this.f7453c, this.f7454d, this.f7455e);
            path.arcTo(rectF, this.f7456f, this.f7457g, false);
            path.transform(matrix);
        }
    }

    /* renamed from: w3.k$e */
    public static class e extends f {

        /* renamed from: b */
        public float f7458b;

        /* renamed from: c */
        public float f7459c;

        @Override // p153w3.C1804k.f
        /* renamed from: a */
        public final void mo4617a(Matrix matrix, Path path) {
            Matrix matrix2 = this.f7460a;
            matrix.invert(matrix2);
            path.transform(matrix2);
            path.lineTo(this.f7458b, this.f7459c);
            path.transform(matrix);
        }
    }

    /* renamed from: w3.k$f */
    public static abstract class f {

        /* renamed from: a */
        public final Matrix f7460a = new Matrix();

        /* renamed from: a */
        public abstract void mo4617a(Matrix matrix, Path path);
    }

    /* renamed from: w3.k$g */
    public static abstract class g {

        /* renamed from: a */
        public static final Matrix f7461a = new Matrix();

        /* renamed from: a */
        public abstract void mo4615a(Matrix matrix, C1460a c1460a, int i6, Canvas canvas);
    }

    public C1804k() {
        m4614f(0.0f, 270.0f, 0.0f);
    }

    /* JADX WARN: Type inference failed for: r1v0, types: [java.util.ArrayList, java.util.List<w3.k$f>] */
    /* JADX WARN: Type inference failed for: r9v2, types: [java.util.ArrayList, java.util.List<w3.k$g>] */
    /* renamed from: a */
    public final void m4609a(float f6, float f7, float f8, float f9, float f10, float f11) {
        d dVar = new d(f6, f7, f8, f9);
        dVar.f7456f = f10;
        dVar.f7457g = f11;
        this.f7443g.add(dVar);
        b bVar = new b(dVar);
        float f12 = f10 + f11;
        boolean z5 = f11 < 0.0f;
        if (z5) {
            f10 = (f10 + 180.0f) % 360.0f;
        }
        float f13 = z5 ? (180.0f + f12) % 360.0f : f12;
        m4610b(f10);
        this.f7444h.add(bVar);
        this.f7441e = f13;
        double d6 = f12;
        this.f7439c = (((f8 - f6) / 2.0f) * ((float) Math.cos(Math.toRadians(d6)))) + ((f6 + f8) * 0.5f);
        this.f7440d = (((f9 - f7) / 2.0f) * ((float) Math.sin(Math.toRadians(d6)))) + ((f7 + f9) * 0.5f);
    }

    /* JADX WARN: Type inference failed for: r0v4, types: [java.util.ArrayList, java.util.List<w3.k$g>] */
    /* renamed from: b */
    public final void m4610b(float f6) {
        float f7 = this.f7441e;
        if (f7 == f6) {
            return;
        }
        float f8 = ((f6 - f7) + 360.0f) % 360.0f;
        if (f8 > 180.0f) {
            return;
        }
        float f9 = this.f7439c;
        float f10 = this.f7440d;
        d dVar = new d(f9, f10, f9, f10);
        dVar.f7456f = this.f7441e;
        dVar.f7457g = f8;
        this.f7444h.add(new b(dVar));
        this.f7441e = f6;
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<w3.k$f>] */
    /* JADX WARN: Type inference failed for: r2v0, types: [java.util.ArrayList, java.util.List<w3.k$f>] */
    /* renamed from: c */
    public final void m4611c(Matrix matrix, Path path) {
        int size = this.f7443g.size();
        for (int i6 = 0; i6 < size; i6++) {
            ((f) this.f7443g.get(i6)).mo4617a(matrix, path);
        }
    }

    /* renamed from: d */
    public final g m4612d(Matrix matrix) {
        m4610b(this.f7442f);
        return new a(new ArrayList(this.f7444h), new Matrix(matrix));
    }

    /* JADX WARN: Type inference failed for: r0v3, types: [java.util.ArrayList, java.util.List<w3.k$g>] */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.util.ArrayList, java.util.List<w3.k$f>] */
    /* renamed from: e */
    public final void m4613e(float f6, float f7) {
        e eVar = new e();
        eVar.f7458b = f6;
        eVar.f7459c = f7;
        this.f7443g.add(eVar);
        c cVar = new c(eVar, this.f7439c, this.f7440d);
        float m4616b = cVar.m4616b() + 270.0f;
        float m4616b2 = cVar.m4616b() + 270.0f;
        m4610b(m4616b);
        this.f7444h.add(cVar);
        this.f7441e = m4616b2;
        this.f7439c = f6;
        this.f7440d = f7;
    }

    /* JADX WARN: Type inference failed for: r2v2, types: [java.util.ArrayList, java.util.List<w3.k$f>] */
    /* JADX WARN: Type inference failed for: r2v3, types: [java.util.ArrayList, java.util.List<w3.k$g>] */
    /* renamed from: f */
    public final void m4614f(float f6, float f7, float f8) {
        this.f7437a = 0.0f;
        this.f7438b = f6;
        this.f7439c = 0.0f;
        this.f7440d = f6;
        this.f7441e = f7;
        this.f7442f = (f7 + f8) % 360.0f;
        this.f7443g.clear();
        this.f7444h.clear();
    }
}
