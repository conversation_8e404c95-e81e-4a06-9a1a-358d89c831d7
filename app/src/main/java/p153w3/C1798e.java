package p153w3;

import android.R;
import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ValueAnimator;
import android.app.AppOpsManager;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.Binder;
import android.os.Bundle;
import android.os.Looper;
import android.os.Parcel;
import android.os.Parcelable;
import android.os.Process;
import android.text.TextUtils;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewParent;
import android.widget.TextView;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.InterfaceC0266s;
import com.google.android.material.behavior.SwipeDismissBehavior;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.WeakHashMap;
import java.util.concurrent.atomic.AtomicReference;
import org.litepal.parser.LitePalParser;
import p008b0.C0385m;
import p009b1.C0395h;
import p023d1.InterfaceC0725w;
import p029e0.C0766p;
import p029e0.C0769s;
import p029e0.InterfaceC0770t;
import p079l1.C1061b;
import p090n.C1090c;
import p093n3.C1120a;
import p098o1.C1139c;
import p105p1.InterfaceC1232b;
import p110q.C1251d;
import p112q1.InterfaceC1261g;
import p112q1.InterfaceC1262h;
import p112q1.InterfaceC1263i;
import p112q1.InterfaceC1267m;
import p113q2.InterfaceC1273a;
import p132t3.C1400b;
import p152w2.AbstractC1590j;
import p152w2.AbstractC1691r4;
import p152w2.AbstractC1750w3;
import p152w2.C1485a5;
import p152w2.C1500b7;
import p152w2.C1534e3;
import p152w2.C1542f;
import p152w2.C1546f3;
import p152w2.C1566h;
import p152w2.C1573h6;
import p152w2.C1578i;
import p152w2.C1585i6;
import p152w2.C1678q3;
import p152w2.C1681q6;
import p152w2.C1698s;
import p152w2.C1703s4;
import p152w2.C1718t7;
import p152w2.C1752w5;
import p152w2.C1775y4;
import p152w2.InterfaceC1522d3;
import p152w2.InterfaceC1537e6;
import p152w2.InterfaceC1614l;
import p152w2.InterfaceC1653o2;
import p152w2.InterfaceC1662p;
import p152w2.InterfaceC1763x4;
import p153w3.C1799f;
import p156x.C1808a;
import p157x1.C1818a;
import p158x2.C1898h4;
import p158x2.C2010v2;
import p158x2.C2026x2;
import p158x2.InterfaceC2002u2;

/* renamed from: w3.e */
/* loaded from: classes.dex */
public class C1798e implements InterfaceC1232b, InterfaceC1262h, InterfaceC1267m, InterfaceC1261g, InterfaceC0770t, InterfaceC1273a, InterfaceC2002u2 {

    /* renamed from: C */
    public static Field f7324C;

    /* renamed from: D */
    public static boolean f7325D;

    /* renamed from: j */
    public static final int[] f7336j = new int[0];

    /* renamed from: k */
    public static final Object[] f7337k = new Object[0];

    /* renamed from: l */
    public static final int[] f7338l = {R.attr.orientation, R.attr.id, R.attr.visibility, R.attr.layout_width, R.attr.layout_height, R.attr.layout_marginLeft, R.attr.layout_marginTop, R.attr.layout_marginRight, R.attr.layout_marginBottom, R.attr.maxWidth, R.attr.maxHeight, R.attr.minWidth, R.attr.minHeight, R.attr.alpha, R.attr.transformPivotX, R.attr.transformPivotY, R.attr.translationX, R.attr.translationY, R.attr.scaleX, R.attr.scaleY, R.attr.rotation, R.attr.rotationX, R.attr.rotationY, R.attr.layout_marginStart, R.attr.layout_marginEnd, R.attr.translationZ, R.attr.elevation, com.liaoyuan.aicast.R.attr.animate_relativeTo, com.liaoyuan.aicast.R.attr.barrierAllowsGoneWidgets, com.liaoyuan.aicast.R.attr.barrierDirection, com.liaoyuan.aicast.R.attr.barrierMargin, com.liaoyuan.aicast.R.attr.chainUseRtl, com.liaoyuan.aicast.R.attr.constraint_referenced_ids, com.liaoyuan.aicast.R.attr.constraint_referenced_tags, com.liaoyuan.aicast.R.attr.drawPath, com.liaoyuan.aicast.R.attr.flow_firstHorizontalBias, com.liaoyuan.aicast.R.attr.flow_firstHorizontalStyle, com.liaoyuan.aicast.R.attr.flow_firstVerticalBias, com.liaoyuan.aicast.R.attr.flow_firstVerticalStyle, com.liaoyuan.aicast.R.attr.flow_horizontalAlign, com.liaoyuan.aicast.R.attr.flow_horizontalBias, com.liaoyuan.aicast.R.attr.flow_horizontalGap, com.liaoyuan.aicast.R.attr.flow_horizontalStyle, com.liaoyuan.aicast.R.attr.flow_lastHorizontalBias, com.liaoyuan.aicast.R.attr.flow_lastHorizontalStyle, com.liaoyuan.aicast.R.attr.flow_lastVerticalBias, com.liaoyuan.aicast.R.attr.flow_lastVerticalStyle, com.liaoyuan.aicast.R.attr.flow_maxElementsWrap, com.liaoyuan.aicast.R.attr.flow_verticalAlign, com.liaoyuan.aicast.R.attr.flow_verticalBias, com.liaoyuan.aicast.R.attr.flow_verticalGap, com.liaoyuan.aicast.R.attr.flow_verticalStyle, com.liaoyuan.aicast.R.attr.flow_wrapMode, com.liaoyuan.aicast.R.attr.layout_constrainedHeight, com.liaoyuan.aicast.R.attr.layout_constrainedWidth, com.liaoyuan.aicast.R.attr.layout_constraintBaseline_creator, com.liaoyuan.aicast.R.attr.layout_constraintBaseline_toBaselineOf, com.liaoyuan.aicast.R.attr.layout_constraintBottom_creator, com.liaoyuan.aicast.R.attr.layout_constraintBottom_toBottomOf, com.liaoyuan.aicast.R.attr.layout_constraintBottom_toTopOf, com.liaoyuan.aicast.R.attr.layout_constraintCircle, com.liaoyuan.aicast.R.attr.layout_constraintCircleAngle, com.liaoyuan.aicast.R.attr.layout_constraintCircleRadius, com.liaoyuan.aicast.R.attr.layout_constraintDimensionRatio, com.liaoyuan.aicast.R.attr.layout_constraintEnd_toEndOf, com.liaoyuan.aicast.R.attr.layout_constraintEnd_toStartOf, com.liaoyuan.aicast.R.attr.layout_constraintGuide_begin, com.liaoyuan.aicast.R.attr.layout_constraintGuide_end, com.liaoyuan.aicast.R.attr.layout_constraintGuide_percent, com.liaoyuan.aicast.R.attr.layout_constraintHeight_default, com.liaoyuan.aicast.R.attr.layout_constraintHeight_max, com.liaoyuan.aicast.R.attr.layout_constraintHeight_min, com.liaoyuan.aicast.R.attr.layout_constraintHeight_percent, com.liaoyuan.aicast.R.attr.layout_constraintHorizontal_bias, com.liaoyuan.aicast.R.attr.layout_constraintHorizontal_chainStyle, com.liaoyuan.aicast.R.attr.layout_constraintHorizontal_weight, com.liaoyuan.aicast.R.attr.layout_constraintLeft_creator, com.liaoyuan.aicast.R.attr.layout_constraintLeft_toLeftOf, com.liaoyuan.aicast.R.attr.layout_constraintLeft_toRightOf, com.liaoyuan.aicast.R.attr.layout_constraintRight_creator, com.liaoyuan.aicast.R.attr.layout_constraintRight_toLeftOf, com.liaoyuan.aicast.R.attr.layout_constraintRight_toRightOf, com.liaoyuan.aicast.R.attr.layout_constraintStart_toEndOf, com.liaoyuan.aicast.R.attr.layout_constraintStart_toStartOf, com.liaoyuan.aicast.R.attr.layout_constraintTag, com.liaoyuan.aicast.R.attr.layout_constraintTop_creator, com.liaoyuan.aicast.R.attr.layout_constraintTop_toBottomOf, com.liaoyuan.aicast.R.attr.layout_constraintTop_toTopOf, com.liaoyuan.aicast.R.attr.layout_constraintVertical_bias, com.liaoyuan.aicast.R.attr.layout_constraintVertical_chainStyle, com.liaoyuan.aicast.R.attr.layout_constraintVertical_weight, com.liaoyuan.aicast.R.attr.layout_constraintWidth_default, com.liaoyuan.aicast.R.attr.layout_constraintWidth_max, com.liaoyuan.aicast.R.attr.layout_constraintWidth_min, com.liaoyuan.aicast.R.attr.layout_constraintWidth_percent, com.liaoyuan.aicast.R.attr.layout_editor_absoluteX, com.liaoyuan.aicast.R.attr.layout_editor_absoluteY, com.liaoyuan.aicast.R.attr.layout_goneMarginBottom, com.liaoyuan.aicast.R.attr.layout_goneMarginEnd, com.liaoyuan.aicast.R.attr.layout_goneMarginLeft, com.liaoyuan.aicast.R.attr.layout_goneMarginRight, com.liaoyuan.aicast.R.attr.layout_goneMarginStart, com.liaoyuan.aicast.R.attr.layout_goneMarginTop, com.liaoyuan.aicast.R.attr.motionProgress, com.liaoyuan.aicast.R.attr.motionStagger, com.liaoyuan.aicast.R.attr.pathMotionArc, com.liaoyuan.aicast.R.attr.pivotAnchor, com.liaoyuan.aicast.R.attr.transitionEasing, com.liaoyuan.aicast.R.attr.transitionPathRotate, com.liaoyuan.aicast.R.attr.visibilityMode};

    /* renamed from: m */
    public static final int[] f7339m = {R.attr.orientation, R.attr.padding, R.attr.paddingLeft, R.attr.paddingTop, R.attr.paddingRight, R.attr.paddingBottom, R.attr.visibility, R.attr.maxWidth, R.attr.maxHeight, R.attr.minWidth, R.attr.minHeight, R.attr.paddingStart, R.attr.paddingEnd, R.attr.elevation, com.liaoyuan.aicast.R.attr.barrierAllowsGoneWidgets, com.liaoyuan.aicast.R.attr.barrierDirection, com.liaoyuan.aicast.R.attr.barrierMargin, com.liaoyuan.aicast.R.attr.chainUseRtl, com.liaoyuan.aicast.R.attr.constraintSet, com.liaoyuan.aicast.R.attr.constraint_referenced_ids, com.liaoyuan.aicast.R.attr.constraint_referenced_tags, com.liaoyuan.aicast.R.attr.flow_firstHorizontalBias, com.liaoyuan.aicast.R.attr.flow_firstHorizontalStyle, com.liaoyuan.aicast.R.attr.flow_firstVerticalBias, com.liaoyuan.aicast.R.attr.flow_firstVerticalStyle, com.liaoyuan.aicast.R.attr.flow_horizontalAlign, com.liaoyuan.aicast.R.attr.flow_horizontalBias, com.liaoyuan.aicast.R.attr.flow_horizontalGap, com.liaoyuan.aicast.R.attr.flow_horizontalStyle, com.liaoyuan.aicast.R.attr.flow_lastHorizontalBias, com.liaoyuan.aicast.R.attr.flow_lastHorizontalStyle, com.liaoyuan.aicast.R.attr.flow_lastVerticalBias, com.liaoyuan.aicast.R.attr.flow_lastVerticalStyle, com.liaoyuan.aicast.R.attr.flow_maxElementsWrap, com.liaoyuan.aicast.R.attr.flow_verticalAlign, com.liaoyuan.aicast.R.attr.flow_verticalBias, com.liaoyuan.aicast.R.attr.flow_verticalGap, com.liaoyuan.aicast.R.attr.flow_verticalStyle, com.liaoyuan.aicast.R.attr.flow_wrapMode, com.liaoyuan.aicast.R.attr.layoutDescription, com.liaoyuan.aicast.R.attr.layout_constrainedHeight, com.liaoyuan.aicast.R.attr.layout_constrainedWidth, com.liaoyuan.aicast.R.attr.layout_constraintBaseline_creator, com.liaoyuan.aicast.R.attr.layout_constraintBaseline_toBaselineOf, com.liaoyuan.aicast.R.attr.layout_constraintBottom_creator, com.liaoyuan.aicast.R.attr.layout_constraintBottom_toBottomOf, com.liaoyuan.aicast.R.attr.layout_constraintBottom_toTopOf, com.liaoyuan.aicast.R.attr.layout_constraintCircle, com.liaoyuan.aicast.R.attr.layout_constraintCircleAngle, com.liaoyuan.aicast.R.attr.layout_constraintCircleRadius, com.liaoyuan.aicast.R.attr.layout_constraintDimensionRatio, com.liaoyuan.aicast.R.attr.layout_constraintEnd_toEndOf, com.liaoyuan.aicast.R.attr.layout_constraintEnd_toStartOf, com.liaoyuan.aicast.R.attr.layout_constraintGuide_begin, com.liaoyuan.aicast.R.attr.layout_constraintGuide_end, com.liaoyuan.aicast.R.attr.layout_constraintGuide_percent, com.liaoyuan.aicast.R.attr.layout_constraintHeight_default, com.liaoyuan.aicast.R.attr.layout_constraintHeight_max, com.liaoyuan.aicast.R.attr.layout_constraintHeight_min, com.liaoyuan.aicast.R.attr.layout_constraintHeight_percent, com.liaoyuan.aicast.R.attr.layout_constraintHorizontal_bias, com.liaoyuan.aicast.R.attr.layout_constraintHorizontal_chainStyle, com.liaoyuan.aicast.R.attr.layout_constraintHorizontal_weight, com.liaoyuan.aicast.R.attr.layout_constraintLeft_creator, com.liaoyuan.aicast.R.attr.layout_constraintLeft_toLeftOf, com.liaoyuan.aicast.R.attr.layout_constraintLeft_toRightOf, com.liaoyuan.aicast.R.attr.layout_constraintRight_creator, com.liaoyuan.aicast.R.attr.layout_constraintRight_toLeftOf, com.liaoyuan.aicast.R.attr.layout_constraintRight_toRightOf, com.liaoyuan.aicast.R.attr.layout_constraintStart_toEndOf, com.liaoyuan.aicast.R.attr.layout_constraintStart_toStartOf, com.liaoyuan.aicast.R.attr.layout_constraintTag, com.liaoyuan.aicast.R.attr.layout_constraintTop_creator, com.liaoyuan.aicast.R.attr.layout_constraintTop_toBottomOf, com.liaoyuan.aicast.R.attr.layout_constraintTop_toTopOf, com.liaoyuan.aicast.R.attr.layout_constraintVertical_bias, com.liaoyuan.aicast.R.attr.layout_constraintVertical_chainStyle, com.liaoyuan.aicast.R.attr.layout_constraintVertical_weight, com.liaoyuan.aicast.R.attr.layout_constraintWidth_default, com.liaoyuan.aicast.R.attr.layout_constraintWidth_max, com.liaoyuan.aicast.R.attr.layout_constraintWidth_min, com.liaoyuan.aicast.R.attr.layout_constraintWidth_percent, com.liaoyuan.aicast.R.attr.layout_editor_absoluteX, com.liaoyuan.aicast.R.attr.layout_editor_absoluteY, com.liaoyuan.aicast.R.attr.layout_goneMarginBottom, com.liaoyuan.aicast.R.attr.layout_goneMarginEnd, com.liaoyuan.aicast.R.attr.layout_goneMarginLeft, com.liaoyuan.aicast.R.attr.layout_goneMarginRight, com.liaoyuan.aicast.R.attr.layout_goneMarginStart, com.liaoyuan.aicast.R.attr.layout_goneMarginTop, com.liaoyuan.aicast.R.attr.layout_optimizationLevel};

    /* renamed from: n */
    public static final int[] f7340n = {R.attr.orientation, R.attr.id, R.attr.visibility, R.attr.layout_width, R.attr.layout_height, R.attr.layout_marginLeft, R.attr.layout_marginTop, R.attr.layout_marginRight, R.attr.layout_marginBottom, R.attr.maxWidth, R.attr.maxHeight, R.attr.minWidth, R.attr.minHeight, R.attr.pivotX, R.attr.pivotY, R.attr.alpha, R.attr.transformPivotX, R.attr.transformPivotY, R.attr.translationX, R.attr.translationY, R.attr.scaleX, R.attr.scaleY, R.attr.rotation, R.attr.rotationX, R.attr.rotationY, R.attr.layout_marginStart, R.attr.layout_marginEnd, R.attr.translationZ, R.attr.elevation, com.liaoyuan.aicast.R.attr.animate_relativeTo, com.liaoyuan.aicast.R.attr.barrierAllowsGoneWidgets, com.liaoyuan.aicast.R.attr.barrierDirection, com.liaoyuan.aicast.R.attr.barrierMargin, com.liaoyuan.aicast.R.attr.chainUseRtl, com.liaoyuan.aicast.R.attr.constraint_referenced_ids, com.liaoyuan.aicast.R.attr.constraint_referenced_tags, com.liaoyuan.aicast.R.attr.deriveConstraintsFrom, com.liaoyuan.aicast.R.attr.drawPath, com.liaoyuan.aicast.R.attr.flow_firstHorizontalBias, com.liaoyuan.aicast.R.attr.flow_firstHorizontalStyle, com.liaoyuan.aicast.R.attr.flow_firstVerticalBias, com.liaoyuan.aicast.R.attr.flow_firstVerticalStyle, com.liaoyuan.aicast.R.attr.flow_horizontalAlign, com.liaoyuan.aicast.R.attr.flow_horizontalBias, com.liaoyuan.aicast.R.attr.flow_horizontalGap, com.liaoyuan.aicast.R.attr.flow_horizontalStyle, com.liaoyuan.aicast.R.attr.flow_lastHorizontalBias, com.liaoyuan.aicast.R.attr.flow_lastHorizontalStyle, com.liaoyuan.aicast.R.attr.flow_lastVerticalBias, com.liaoyuan.aicast.R.attr.flow_lastVerticalStyle, com.liaoyuan.aicast.R.attr.flow_maxElementsWrap, com.liaoyuan.aicast.R.attr.flow_verticalAlign, com.liaoyuan.aicast.R.attr.flow_verticalBias, com.liaoyuan.aicast.R.attr.flow_verticalGap, com.liaoyuan.aicast.R.attr.flow_verticalStyle, com.liaoyuan.aicast.R.attr.flow_wrapMode, com.liaoyuan.aicast.R.attr.layout_constrainedHeight, com.liaoyuan.aicast.R.attr.layout_constrainedWidth, com.liaoyuan.aicast.R.attr.layout_constraintBaseline_creator, com.liaoyuan.aicast.R.attr.layout_constraintBaseline_toBaselineOf, com.liaoyuan.aicast.R.attr.layout_constraintBottom_creator, com.liaoyuan.aicast.R.attr.layout_constraintBottom_toBottomOf, com.liaoyuan.aicast.R.attr.layout_constraintBottom_toTopOf, com.liaoyuan.aicast.R.attr.layout_constraintCircle, com.liaoyuan.aicast.R.attr.layout_constraintCircleAngle, com.liaoyuan.aicast.R.attr.layout_constraintCircleRadius, com.liaoyuan.aicast.R.attr.layout_constraintDimensionRatio, com.liaoyuan.aicast.R.attr.layout_constraintEnd_toEndOf, com.liaoyuan.aicast.R.attr.layout_constraintEnd_toStartOf, com.liaoyuan.aicast.R.attr.layout_constraintGuide_begin, com.liaoyuan.aicast.R.attr.layout_constraintGuide_end, com.liaoyuan.aicast.R.attr.layout_constraintGuide_percent, com.liaoyuan.aicast.R.attr.layout_constraintHeight_default, com.liaoyuan.aicast.R.attr.layout_constraintHeight_max, com.liaoyuan.aicast.R.attr.layout_constraintHeight_min, com.liaoyuan.aicast.R.attr.layout_constraintHeight_percent, com.liaoyuan.aicast.R.attr.layout_constraintHorizontal_bias, com.liaoyuan.aicast.R.attr.layout_constraintHorizontal_chainStyle, com.liaoyuan.aicast.R.attr.layout_constraintHorizontal_weight, com.liaoyuan.aicast.R.attr.layout_constraintLeft_creator, com.liaoyuan.aicast.R.attr.layout_constraintLeft_toLeftOf, com.liaoyuan.aicast.R.attr.layout_constraintLeft_toRightOf, com.liaoyuan.aicast.R.attr.layout_constraintRight_creator, com.liaoyuan.aicast.R.attr.layout_constraintRight_toLeftOf, com.liaoyuan.aicast.R.attr.layout_constraintRight_toRightOf, com.liaoyuan.aicast.R.attr.layout_constraintStart_toEndOf, com.liaoyuan.aicast.R.attr.layout_constraintStart_toStartOf, com.liaoyuan.aicast.R.attr.layout_constraintTag, com.liaoyuan.aicast.R.attr.layout_constraintTop_creator, com.liaoyuan.aicast.R.attr.layout_constraintTop_toBottomOf, com.liaoyuan.aicast.R.attr.layout_constraintTop_toTopOf, com.liaoyuan.aicast.R.attr.layout_constraintVertical_bias, com.liaoyuan.aicast.R.attr.layout_constraintVertical_chainStyle, com.liaoyuan.aicast.R.attr.layout_constraintVertical_weight, com.liaoyuan.aicast.R.attr.layout_constraintWidth_default, com.liaoyuan.aicast.R.attr.layout_constraintWidth_max, com.liaoyuan.aicast.R.attr.layout_constraintWidth_min, com.liaoyuan.aicast.R.attr.layout_constraintWidth_percent, com.liaoyuan.aicast.R.attr.layout_editor_absoluteX, com.liaoyuan.aicast.R.attr.layout_editor_absoluteY, com.liaoyuan.aicast.R.attr.layout_goneMarginBottom, com.liaoyuan.aicast.R.attr.layout_goneMarginEnd, com.liaoyuan.aicast.R.attr.layout_goneMarginLeft, com.liaoyuan.aicast.R.attr.layout_goneMarginRight, com.liaoyuan.aicast.R.attr.layout_goneMarginStart, com.liaoyuan.aicast.R.attr.layout_goneMarginTop, com.liaoyuan.aicast.R.attr.motionProgress, com.liaoyuan.aicast.R.attr.motionStagger, com.liaoyuan.aicast.R.attr.pathMotionArc, com.liaoyuan.aicast.R.attr.pivotAnchor, com.liaoyuan.aicast.R.attr.transitionEasing, com.liaoyuan.aicast.R.attr.transitionPathRotate};

    /* renamed from: o */
    public static final int[] f7341o = {com.liaoyuan.aicast.R.attr.attributeName, com.liaoyuan.aicast.R.attr.customBoolean, com.liaoyuan.aicast.R.attr.customColorDrawableValue, com.liaoyuan.aicast.R.attr.customColorValue, com.liaoyuan.aicast.R.attr.customDimension, com.liaoyuan.aicast.R.attr.customFloatValue, com.liaoyuan.aicast.R.attr.customIntegerValue, com.liaoyuan.aicast.R.attr.customPixelDimension, com.liaoyuan.aicast.R.attr.customStringValue};

    /* renamed from: p */
    public static final int[] f7342p = {R.attr.orientation, R.attr.layout_width, R.attr.layout_height, R.attr.layout_marginLeft, R.attr.layout_marginTop, R.attr.layout_marginRight, R.attr.layout_marginBottom, R.attr.layout_marginStart, R.attr.layout_marginEnd, com.liaoyuan.aicast.R.attr.barrierAllowsGoneWidgets, com.liaoyuan.aicast.R.attr.barrierDirection, com.liaoyuan.aicast.R.attr.barrierMargin, com.liaoyuan.aicast.R.attr.chainUseRtl, com.liaoyuan.aicast.R.attr.constraint_referenced_ids, com.liaoyuan.aicast.R.attr.constraint_referenced_tags, com.liaoyuan.aicast.R.attr.layout_constrainedHeight, com.liaoyuan.aicast.R.attr.layout_constrainedWidth, com.liaoyuan.aicast.R.attr.layout_constraintBaseline_creator, com.liaoyuan.aicast.R.attr.layout_constraintBaseline_toBaselineOf, com.liaoyuan.aicast.R.attr.layout_constraintBottom_creator, com.liaoyuan.aicast.R.attr.layout_constraintBottom_toBottomOf, com.liaoyuan.aicast.R.attr.layout_constraintBottom_toTopOf, com.liaoyuan.aicast.R.attr.layout_constraintCircle, com.liaoyuan.aicast.R.attr.layout_constraintCircleAngle, com.liaoyuan.aicast.R.attr.layout_constraintCircleRadius, com.liaoyuan.aicast.R.attr.layout_constraintDimensionRatio, com.liaoyuan.aicast.R.attr.layout_constraintEnd_toEndOf, com.liaoyuan.aicast.R.attr.layout_constraintEnd_toStartOf, com.liaoyuan.aicast.R.attr.layout_constraintGuide_begin, com.liaoyuan.aicast.R.attr.layout_constraintGuide_end, com.liaoyuan.aicast.R.attr.layout_constraintGuide_percent, com.liaoyuan.aicast.R.attr.layout_constraintHeight_default, com.liaoyuan.aicast.R.attr.layout_constraintHeight_max, com.liaoyuan.aicast.R.attr.layout_constraintHeight_min, com.liaoyuan.aicast.R.attr.layout_constraintHeight_percent, com.liaoyuan.aicast.R.attr.layout_constraintHorizontal_bias, com.liaoyuan.aicast.R.attr.layout_constraintHorizontal_chainStyle, com.liaoyuan.aicast.R.attr.layout_constraintHorizontal_weight, com.liaoyuan.aicast.R.attr.layout_constraintLeft_creator, com.liaoyuan.aicast.R.attr.layout_constraintLeft_toLeftOf, com.liaoyuan.aicast.R.attr.layout_constraintLeft_toRightOf, com.liaoyuan.aicast.R.attr.layout_constraintRight_creator, com.liaoyuan.aicast.R.attr.layout_constraintRight_toLeftOf, com.liaoyuan.aicast.R.attr.layout_constraintRight_toRightOf, com.liaoyuan.aicast.R.attr.layout_constraintStart_toEndOf, com.liaoyuan.aicast.R.attr.layout_constraintStart_toStartOf, com.liaoyuan.aicast.R.attr.layout_constraintTop_creator, com.liaoyuan.aicast.R.attr.layout_constraintTop_toBottomOf, com.liaoyuan.aicast.R.attr.layout_constraintTop_toTopOf, com.liaoyuan.aicast.R.attr.layout_constraintVertical_bias, com.liaoyuan.aicast.R.attr.layout_constraintVertical_chainStyle, com.liaoyuan.aicast.R.attr.layout_constraintVertical_weight, com.liaoyuan.aicast.R.attr.layout_constraintWidth_default, com.liaoyuan.aicast.R.attr.layout_constraintWidth_max, com.liaoyuan.aicast.R.attr.layout_constraintWidth_min, com.liaoyuan.aicast.R.attr.layout_constraintWidth_percent, com.liaoyuan.aicast.R.attr.layout_editor_absoluteX, com.liaoyuan.aicast.R.attr.layout_editor_absoluteY, com.liaoyuan.aicast.R.attr.layout_goneMarginBottom, com.liaoyuan.aicast.R.attr.layout_goneMarginEnd, com.liaoyuan.aicast.R.attr.layout_goneMarginLeft, com.liaoyuan.aicast.R.attr.layout_goneMarginRight, com.liaoyuan.aicast.R.attr.layout_goneMarginStart, com.liaoyuan.aicast.R.attr.layout_goneMarginTop, com.liaoyuan.aicast.R.attr.maxHeight, com.liaoyuan.aicast.R.attr.maxWidth, com.liaoyuan.aicast.R.attr.minHeight, com.liaoyuan.aicast.R.attr.minWidth};

    /* renamed from: q */
    public static final int[] f7343q = {com.liaoyuan.aicast.R.attr.animate_relativeTo, com.liaoyuan.aicast.R.attr.drawPath, com.liaoyuan.aicast.R.attr.motionPathRotate, com.liaoyuan.aicast.R.attr.motionStagger, com.liaoyuan.aicast.R.attr.pathMotionArc, com.liaoyuan.aicast.R.attr.transitionEasing};

    /* renamed from: r */
    public static final int[] f7344r = {R.attr.visibility, R.attr.alpha, com.liaoyuan.aicast.R.attr.layout_constraintTag, com.liaoyuan.aicast.R.attr.motionProgress, com.liaoyuan.aicast.R.attr.visibilityMode};

    /* renamed from: s */
    public static final int[] f7345s = {R.attr.id, com.liaoyuan.aicast.R.attr.constraints};

    /* renamed from: t */
    public static final int[] f7346t = {R.attr.transformPivotX, R.attr.transformPivotY, R.attr.translationX, R.attr.translationY, R.attr.scaleX, R.attr.scaleY, R.attr.rotation, R.attr.rotationX, R.attr.rotationY, R.attr.translationZ, R.attr.elevation};

    /* renamed from: u */
    public static final int[] f7347u = {com.liaoyuan.aicast.R.attr.constraints, com.liaoyuan.aicast.R.attr.region_heightLessThan, com.liaoyuan.aicast.R.attr.region_heightMoreThan, com.liaoyuan.aicast.R.attr.region_widthLessThan, com.liaoyuan.aicast.R.attr.region_widthMoreThan};

    /* renamed from: v */
    public static final C1798e f7348v = new C1798e();

    /* renamed from: w */
    public static final int[] f7349w = {R.attr.orientation, R.attr.clipToPadding, R.attr.descendantFocusability, com.liaoyuan.aicast.R.attr.fastScrollEnabled, com.liaoyuan.aicast.R.attr.fastScrollHorizontalThumbDrawable, com.liaoyuan.aicast.R.attr.fastScrollHorizontalTrackDrawable, com.liaoyuan.aicast.R.attr.fastScrollVerticalThumbDrawable, com.liaoyuan.aicast.R.attr.fastScrollVerticalTrackDrawable, com.liaoyuan.aicast.R.attr.layoutManager, com.liaoyuan.aicast.R.attr.reverseLayout, com.liaoyuan.aicast.R.attr.spanCount, com.liaoyuan.aicast.R.attr.stackFromEnd};

    /* renamed from: x */
    public static final int[] f7350x = {R.attr.color, R.attr.alpha, com.liaoyuan.aicast.R.attr.alpha};

    /* renamed from: y */
    public static final int[] f7351y = {com.liaoyuan.aicast.R.attr.fontProviderAuthority, com.liaoyuan.aicast.R.attr.fontProviderCerts, com.liaoyuan.aicast.R.attr.fontProviderFetchStrategy, com.liaoyuan.aicast.R.attr.fontProviderFetchTimeout, com.liaoyuan.aicast.R.attr.fontProviderPackage, com.liaoyuan.aicast.R.attr.fontProviderQuery, com.liaoyuan.aicast.R.attr.fontProviderSystemFontFamily};

    /* renamed from: z */
    public static final int[] f7352z = {R.attr.font, R.attr.fontWeight, R.attr.fontStyle, R.attr.ttcIndex, R.attr.fontVariationSettings, com.liaoyuan.aicast.R.attr.font, com.liaoyuan.aicast.R.attr.fontStyle, com.liaoyuan.aicast.R.attr.fontVariationSettings, com.liaoyuan.aicast.R.attr.fontWeight, com.liaoyuan.aicast.R.attr.ttcIndex};

    /* renamed from: A */
    public static final int[] f7322A = {R.attr.startColor, R.attr.endColor, R.attr.type, R.attr.centerX, R.attr.centerY, R.attr.gradientRadius, R.attr.tileMode, R.attr.centerColor, R.attr.startX, R.attr.startY, R.attr.endX, R.attr.endY};

    /* renamed from: B */
    public static final int[] f7323B = {R.attr.color, R.attr.offset};

    /* renamed from: E */
    public static final C1573h6 f7326E = new C1573h6();

    /* renamed from: F */
    public static final C1585i6 f7327F = new C1585i6();

    /* renamed from: G */
    public static final C1798e f7328G = new C1798e();

    /* renamed from: H */
    public static final String[] f7329H = {"ad_activeview", "ad_click", "ad_exposure", "ad_query", "ad_reward", "adunit_exposure", "app_background", "app_clear_data", "app_exception", "app_remove", "app_store_refund", "app_store_subscription_cancel", "app_store_subscription_convert", "app_store_subscription_renew", "app_upgrade", "app_update", "ga_campaign", "error", "first_open", "first_visit", "in_app_purchase", "notification_dismiss", "notification_foreground", "notification_open", "notification_receive", "os_update", "session_start", "session_start_with_rollout", "user_engagement", "ad_impression", "screen_view", "ga_extra_parameter", "firebase_campaign"};

    /* renamed from: I */
    public static final String[] f7330I = {"ad_impression"};

    /* renamed from: J */
    public static final String[] f7331J = {"_aa", "_ac", "_xa", "_aq", "_ar", "_xu", "_ab", "_cd", "_ae", "_ui", "app_store_refund", "app_store_subscription_cancel", "app_store_subscription_convert", "app_store_subscription_renew", "_ug", "_au", "_cmp", "_err", "_f", "_v", "_iap", "_nd", "_nf", "_no", "_nr", "_ou", "_s", "_ssr", "_e", "_ai", "_vs", "_ep", "_cmp"};

    /* renamed from: K */
    public static final String[] f7332K = {"purchase", "refund", "add_payment_info", "add_shipping_info", "add_to_cart", "add_to_wishlist", "begin_checkout", "remove_from_cart", "select_item", "select_promotion", "view_cart", "view_item", "view_item_list", "view_promotion", "ecommerce_purchase", "purchase_refund", "set_checkout_option", "checkout_progress", "select_content", "view_search_results"};

    /* renamed from: L */
    public static final String[] f7333L = {"firebase_last_notification", "first_open_time", "first_visit_time", "last_deep_link_referrer", "user_id", "first_open_after_install", "lifetime_user_engagement", "session_user_engagement", "non_personalized_ads", "ga_session_number", "ga_session_id", "last_gclid", "session_number", "session_id"};

    /* renamed from: M */
    public static final String[] f7334M = {"_ln", "_fot", "_fvt", "_ldl", "_id", "_fi", "_lte", "_se", "_npa", "_sno", "_sid", "_lgclid", "_sno", "_sid"};

    /* renamed from: N */
    public static final byte[] f7335N = {65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 43, 47};

    public /* synthetic */ C1798e() {
    }

    /* renamed from: A */
    public static int m4510A(View view, int i6) {
        return C1400b.m3413b(view.getContext(), i6, view.getClass().getCanonicalName());
    }

    /* renamed from: C */
    public static TextView m4511C(Toolbar toolbar, CharSequence charSequence) {
        for (int i6 = 0; i6 < toolbar.getChildCount(); i6++) {
            View childAt = toolbar.getChildAt(i6);
            if (childAt instanceof TextView) {
                TextView textView = (TextView) childAt;
                if (TextUtils.equals(textView.getText(), charSequence)) {
                    return textView;
                }
            }
        }
        return null;
    }

    /* renamed from: D */
    public static int m4512D(int i6) {
        int i7 = i6 * 4;
        int i8 = 4;
        while (true) {
            if (i8 >= 32) {
                break;
            }
            int i9 = (1 << i8) - 12;
            if (i7 <= i9) {
                i7 = i9;
                break;
            }
            i8++;
        }
        return i7 / 4;
    }

    /* renamed from: E */
    public static int m4513E(int i6) {
        int i7 = i6 * 8;
        int i8 = 4;
        while (true) {
            if (i8 >= 32) {
                break;
            }
            int i9 = (1 << i8) - 12;
            if (i7 <= i9) {
                i7 = i9;
                break;
            }
            i8++;
        }
        return i7 / 8;
    }

    /* renamed from: F */
    public static int m4514F(int i6, int i7, float f6) {
        return C1808a.m4623a(C1808a.m4625c(i7, Math.round(Color.alpha(i7) * f6)), i6);
    }

    /* renamed from: G */
    public static float m4515G(float f6, float f7, float f8) {
        return (f8 * f7) + ((1.0f - f8) * f6);
    }

    /* renamed from: H */
    public static boolean m4516H(String str) {
        return (str.equals("GET") || str.equals("HEAD")) ? false : true;
    }

    /* renamed from: I */
    public static void m4517I(AnimatorSet animatorSet, List list) {
        int size = list.size();
        long j6 = 0;
        for (int i6 = 0; i6 < size; i6++) {
            Animator animator = (Animator) list.get(i6);
            j6 = Math.max(j6, animator.getDuration() + animator.getStartDelay());
        }
        ValueAnimator ofInt = ValueAnimator.ofInt(0, 0);
        ofInt.setDuration(j6);
        list.add(0, ofInt);
        animatorSet.playTogether(list);
    }

    /* renamed from: J */
    public static void m4518J(View view, InterfaceC0266s interfaceC0266s) {
        view.setTag(com.liaoyuan.aicast.R.id.view_tree_view_model_store_owner, interfaceC0266s);
    }

    /* renamed from: K */
    public static void m4519K(View view, float f6) {
        Drawable background = view.getBackground();
        if (background instanceof C1799f) {
            ((C1799f) background).m4586m(f6);
        }
    }

    /* renamed from: L */
    public static void m4520L(View view) {
        Drawable background = view.getBackground();
        if (background instanceof C1799f) {
            m4521M(view, (C1799f) background);
        }
    }

    /* renamed from: M */
    public static void m4521M(View view, C1799f c1799f) {
        C1120a c1120a = c1799f.f7360j.f7379b;
        if (c1120a != null && c1120a.f5334a) {
            float f6 = 0.0f;
            for (ViewParent parent = view.getParent(); parent instanceof View; parent = parent.getParent()) {
                WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
                f6 += ((View) parent).getElevation();
            }
            C1799f.b bVar = c1799f.f7360j;
            if (bVar.f7390m != f6) {
                bVar.f7390m = f6;
                c1799f.m4595v();
            }
        }
    }

    /* renamed from: O */
    public static void m4522O(Parcel parcel, int i6, Bundle bundle) {
        if (bundle == null) {
            return;
        }
        int m4526S = m4526S(parcel, i6);
        parcel.writeBundle(bundle);
        m4539f0(parcel, m4526S);
    }

    /* renamed from: P */
    public static void m4523P(Parcel parcel, int i6, Parcelable parcelable, int i7) {
        if (parcelable == null) {
            return;
        }
        int m4526S = m4526S(parcel, i6);
        parcelable.writeToParcel(parcel, i7);
        m4539f0(parcel, m4526S);
    }

    /* renamed from: Q */
    public static void m4524Q(Parcel parcel, int i6, String str) {
        if (str == null) {
            return;
        }
        int m4526S = m4526S(parcel, i6);
        parcel.writeString(str);
        m4539f0(parcel, m4526S);
    }

    /* renamed from: R */
    public static void m4525R(Parcel parcel, int i6, Parcelable[] parcelableArr, int i7) {
        if (parcelableArr == null) {
            return;
        }
        int m4526S = m4526S(parcel, i6);
        parcel.writeInt(parcelableArr.length);
        for (Parcelable parcelable : parcelableArr) {
            if (parcelable == null) {
                parcel.writeInt(0);
            } else {
                int dataPosition = parcel.dataPosition();
                parcel.writeInt(1);
                int dataPosition2 = parcel.dataPosition();
                parcelable.writeToParcel(parcel, i7);
                int dataPosition3 = parcel.dataPosition();
                parcel.setDataPosition(dataPosition);
                parcel.writeInt(dataPosition3 - dataPosition2);
                parcel.setDataPosition(dataPosition3);
            }
        }
        m4539f0(parcel, m4526S);
    }

    /* renamed from: S */
    public static int m4526S(Parcel parcel, int i6) {
        parcel.writeInt(i6 | (-65536));
        parcel.writeInt(0);
        return parcel.dataPosition();
    }

    /* renamed from: T */
    public static int m4527T(byte[] bArr, int i6, C1678q3 c1678q3) {
        int i7 = i6 + 1;
        byte b6 = bArr[i6];
        if (b6 < 0) {
            return m4535b0(b6, bArr, i7, c1678q3);
        }
        c1678q3.f7144a = b6;
        return i7;
    }

    /* renamed from: U */
    public static Object m4528U(InterfaceC1653o2 interfaceC1653o2) {
        try {
            return interfaceC1653o2.mo2029a();
        } catch (SecurityException unused) {
            long clearCallingIdentity = Binder.clearCallingIdentity();
            try {
                return interfaceC1653o2.mo2029a();
            } finally {
                Binder.restoreCallingIdentity(clearCallingIdentity);
            }
        }
    }

    /* renamed from: V */
    public static String m4529V() {
        return m4543i0("_ai", f7331J, f7329H);
    }

    /* renamed from: W */
    public static String m4530W(Context context, String str) {
        Objects.requireNonNull(context, "null reference");
        Resources resources = context.getResources();
        if (TextUtils.isEmpty(str)) {
            str = C1898h4.m4845a(context);
        }
        return C1898h4.m4846b("google_app_id", resources, str);
    }

    /* renamed from: X */
    public static String m4531X(AbstractC1750w3 abstractC1750w3) {
        String str;
        StringBuilder sb = new StringBuilder(abstractC1750w3.mo4287g());
        for (int i6 = 0; i6 < abstractC1750w3.mo4287g(); i6++) {
            int mo4285c = abstractC1750w3.mo4285c(i6);
            if (mo4285c == 34) {
                str = "\\\"";
            } else if (mo4285c == 39) {
                str = "\\'";
            } else if (mo4285c != 92) {
                switch (mo4285c) {
                    case 7:
                        str = "\\a";
                        break;
                    case 8:
                        str = "\\b";
                        break;
                    case 9:
                        str = "\\t";
                        break;
                    case 10:
                        str = "\\n";
                        break;
                    case 11:
                        str = "\\v";
                        break;
                    case 12:
                        str = "\\f";
                        break;
                    case 13:
                        str = "\\r";
                        break;
                    default:
                        if (mo4285c < 32 || mo4285c > 126) {
                            sb.append('\\');
                            sb.append((char) (((mo4285c >>> 6) & 3) + 48));
                            sb.append((char) (((mo4285c >>> 3) & 7) + 48));
                            mo4285c = (mo4285c & 7) + 48;
                        }
                        sb.append((char) mo4285c);
                        continue;
                }
            } else {
                str = "\\\\";
            }
            sb.append(str);
        }
        return sb.toString();
    }

    /* renamed from: Y */
    public static InterfaceC1662p m4532Y(InterfaceC1614l interfaceC1614l, InterfaceC1662p interfaceC1662p, C1090c c1090c, List list) {
        C1698s c1698s = (C1698s) interfaceC1662p;
        if (interfaceC1614l.mo3765j(c1698s.f7169j)) {
            InterfaceC1662p mo3767m = interfaceC1614l.mo3767m(c1698s.f7169j);
            if (mo3767m instanceof AbstractC1590j) {
                return ((AbstractC1590j) mo3767m).mo3670a(c1090c, list);
            }
            throw new IllegalArgumentException(String.format("%s is not a function", c1698s.f7169j));
        }
        if (!"hasOwnProperty".equals(c1698s.f7169j)) {
            throw new IllegalArgumentException(String.format("Object has no function %s", c1698s.f7169j));
        }
        C0385m.m1426r("hasOwnProperty", 1, list);
        return interfaceC1614l.mo3765j(c1090c.m2803b((InterfaceC1662p) ((ArrayList) list).get(0)).mo3761c()) ? InterfaceC1662p.f7131g : InterfaceC1662p.f7132h;
    }

    /* renamed from: Z */
    public static InterfaceC1522d3 m4533Z(InterfaceC1522d3 interfaceC1522d3) {
        return ((interfaceC1522d3 instanceof C1546f3) || (interfaceC1522d3 instanceof C1534e3)) ? interfaceC1522d3 : interfaceC1522d3 instanceof Serializable ? new C1534e3(interfaceC1522d3) : new C1546f3(interfaceC1522d3);
    }

    /* renamed from: a0 */
    public static void m4534a0(Bundle bundle, Object obj) {
        if (obj instanceof Double) {
            bundle.putDouble(LitePalParser.ATTR_VALUE, ((Double) obj).doubleValue());
        } else if (obj instanceof Long) {
            bundle.putLong(LitePalParser.ATTR_VALUE, ((Long) obj).longValue());
        } else {
            bundle.putString(LitePalParser.ATTR_VALUE, obj.toString());
        }
    }

    /* renamed from: b0 */
    public static int m4535b0(int i6, byte[] bArr, int i7, C1678q3 c1678q3) {
        int i8;
        int i9;
        int i10 = i6 & 127;
        int i11 = i7 + 1;
        byte b6 = bArr[i7];
        if (b6 < 0) {
            int i12 = i10 | ((b6 & Byte.MAX_VALUE) << 7);
            int i13 = i11 + 1;
            byte b7 = bArr[i11];
            if (b7 >= 0) {
                i8 = b7 << 14;
            } else {
                i10 = i12 | ((b7 & Byte.MAX_VALUE) << 14);
                i11 = i13 + 1;
                byte b8 = bArr[i13];
                if (b8 >= 0) {
                    i9 = b8 << 21;
                } else {
                    i12 = i10 | ((b8 & Byte.MAX_VALUE) << 21);
                    i13 = i11 + 1;
                    byte b9 = bArr[i11];
                    if (b9 >= 0) {
                        i8 = b9 << 28;
                    } else {
                        int i14 = i12 | ((b9 & Byte.MAX_VALUE) << 28);
                        while (true) {
                            int i15 = i13 + 1;
                            if (bArr[i13] >= 0) {
                                c1678q3.f7144a = i14;
                                return i15;
                            }
                            i13 = i15;
                        }
                    }
                }
            }
            c1678q3.f7144a = i12 | i8;
            return i13;
        }
        i9 = b6 << 7;
        c1678q3.f7144a = i10 | i9;
        return i11;
    }

    /* renamed from: c0 */
    public static Object m4536c0(Bundle bundle, String str, Class cls, Object obj) {
        Object obj2 = bundle.get(str);
        if (obj2 == null) {
            return obj;
        }
        if (cls.isAssignableFrom(obj2.getClass())) {
            return obj2;
        }
        throw new IllegalStateException(String.format("Invalid conditional user property field type. '%s' expected [%s] but was [%s]", str, cls.getCanonicalName(), obj2.getClass().getCanonicalName()));
    }

    /* renamed from: d0 */
    public static String m4537d0(String str) {
        return m4543i0(str, f7329H, f7331J);
    }

    /* renamed from: e0 */
    public static InterfaceC1662p m4538e0(C1542f c1542f, C1090c c1090c, List list, boolean z5) {
        InterfaceC1662p interfaceC1662p;
        C0385m.m1430w("reduce", 1, list);
        C0385m.m1433z("reduce", 2, list);
        ArrayList arrayList = (ArrayList) list;
        InterfaceC1662p m2803b = c1090c.m2803b((InterfaceC1662p) arrayList.get(0));
        if (!(m2803b instanceof AbstractC1590j)) {
            throw new IllegalArgumentException("Callback should be a method");
        }
        if (arrayList.size() == 2) {
            interfaceC1662p = c1090c.m2803b((InterfaceC1662p) arrayList.get(1));
            if (interfaceC1662p instanceof C1566h) {
                throw new IllegalArgumentException("Failed to parse initial value");
            }
        } else {
            if (c1542f.m3770q() == 0) {
                throw new IllegalStateException("Empty array with no initial value error");
            }
            interfaceC1662p = null;
        }
        AbstractC1590j abstractC1590j = (AbstractC1590j) m2803b;
        int m3770q = c1542f.m3770q();
        int i6 = z5 ? 0 : m3770q - 1;
        int i7 = z5 ? m3770q - 1 : 0;
        int i8 = true == z5 ? 1 : -1;
        if (interfaceC1662p == null) {
            interfaceC1662p = c1542f.m3773t(i6);
            i6 += i8;
        }
        while ((i7 - i6) * i8 >= 0) {
            if (c1542f.m3775v(i6)) {
                interfaceC1662p = abstractC1590j.mo3670a(c1090c, Arrays.asList(interfaceC1662p, c1542f.m3773t(i6), new C1578i(Double.valueOf(i6)), c1542f));
                if (interfaceC1662p instanceof C1566h) {
                    throw new IllegalStateException("Reduce operation failed");
                }
                i6 += i8;
            } else {
                i6 += i8;
            }
        }
        return interfaceC1662p;
    }

    /* renamed from: f0 */
    public static void m4539f0(Parcel parcel, int i6) {
        int dataPosition = parcel.dataPosition();
        parcel.setDataPosition(i6 - 4);
        parcel.writeInt(dataPosition - i6);
        parcel.setDataPosition(dataPosition);
    }

    /* renamed from: g0 */
    public static void m4540g0(Parcel parcel, int i6, int i7) {
        if (i7 < 65535) {
            parcel.writeInt(i6 | (i7 << 16));
        } else {
            parcel.writeInt(i6 | (-65536));
            parcel.writeInt(i7);
        }
    }

    /* renamed from: h0 */
    public static int m4541h0(byte[] bArr, int i6, C1678q3 c1678q3) {
        int i7 = i6 + 1;
        long j6 = bArr[i6];
        if (j6 >= 0) {
            c1678q3.f7145b = j6;
            return i7;
        }
        int i8 = i7 + 1;
        byte b6 = bArr[i7];
        long j7 = (j6 & 127) | ((b6 & Byte.MAX_VALUE) << 7);
        int i9 = 7;
        while (b6 < 0) {
            int i10 = i8 + 1;
            i9 += 7;
            j7 |= (r10 & Byte.MAX_VALUE) << i9;
            b6 = bArr[i8];
            i8 = i10;
        }
        c1678q3.f7145b = j7;
        return i8;
    }

    /* JADX WARN: Code restructure failed: missing block: B:104:0x0197, code lost:
    
        if (r3 == r6) goto L563;
     */
    /* JADX WARN: Code restructure failed: missing block: B:105:0x01b1, code lost:
    
        r3 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:146:0x0278, code lost:
    
        if (r4[r16].f5368f.f5366d == r6) goto L613;
     */
    /* JADX WARN: Code restructure failed: missing block: B:418:0x01af, code lost:
    
        r3 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:426:0x01ad, code lost:
    
        if (r3 == r6) goto L563;
     */
    /* JADX WARN: Code restructure failed: missing block: B:62:0x010a, code lost:
    
        if (r5[r2].f5368f.f5366d == r6) goto L510;
     */
    /* JADX WARN: Removed duplicated region for block: B:178:0x02fe  */
    /* JADX WARN: Removed duplicated region for block: B:181:0x031b  */
    /* JADX WARN: Removed duplicated region for block: B:190:0x0336  */
    /* JADX WARN: Removed duplicated region for block: B:225:0x043d A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:242:0x06b6 A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:248:0x06ca  */
    /* JADX WARN: Removed duplicated region for block: B:251:0x06d3  */
    /* JADX WARN: Removed duplicated region for block: B:253:0x06da  */
    /* JADX WARN: Removed duplicated region for block: B:258:0x06ea  */
    /* JADX WARN: Removed duplicated region for block: B:260:0x06f0 A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:264:0x070f A[ADDED_TO_REGION, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:265:0x06d6  */
    /* JADX WARN: Removed duplicated region for block: B:266:0x06cd  */
    /* JADX WARN: Removed duplicated region for block: B:275:0x04a2 A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:294:0x0587  */
    /* JADX WARN: Removed duplicated region for block: B:297:0x0589  */
    /* JADX WARN: Removed duplicated region for block: B:307:0x0513  */
    /* JADX WARN: Removed duplicated region for block: B:310:0x0533  */
    /* JADX WARN: Removed duplicated region for block: B:312:0x053a  */
    /* JADX WARN: Removed duplicated region for block: B:318:0x054d  */
    /* JADX WARN: Removed duplicated region for block: B:321:0x0558  */
    /* JADX WARN: Removed duplicated region for block: B:323:0x0567  */
    /* JADX WARN: Removed duplicated region for block: B:325:0x056a  */
    /* JADX WARN: Removed duplicated region for block: B:326:0x0563  */
    /* JADX WARN: Removed duplicated region for block: B:327:0x051e  */
    /* JADX WARN: Removed duplicated region for block: B:345:0x0596 A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:353:0x05a9  */
    /* JADX WARN: Removed duplicated region for block: B:370:0x0601  */
    /* JADX WARN: Removed duplicated region for block: B:373:0x0616  */
    /* JADX WARN: Removed duplicated region for block: B:387:0x0619  */
    /* JADX WARN: Removed duplicated region for block: B:388:0x0609  */
    /* JADX WARN: Removed duplicated region for block: B:401:0x066e  */
    /* JADX WARN: Removed duplicated region for block: B:409:0x06a3  */
    /* renamed from: i */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static void m4542i(p096o.C1127e r34, p090n.C1091d r35, java.util.ArrayList r36, int r37) {
        /*
            Method dump skipped, instructions count: 1821
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p153w3.C1798e.m4542i(o.e, n.d, java.util.ArrayList, int):void");
    }

    /* renamed from: i0 */
    public static String m4543i0(String str, String[] strArr, String[] strArr2) {
        Objects.requireNonNull(strArr2, "null reference");
        int min = Math.min(strArr.length, strArr2.length);
        for (int i6 = 0; i6 < min; i6++) {
            String str2 = strArr[i6];
            if ((str == null && str2 == null) || (str != null && str.equals(str2))) {
                return strArr2[i6];
            }
        }
        return null;
    }

    /* renamed from: j */
    public static int m4544j(int[] iArr, int i6, int i7) {
        int i8 = i6 - 1;
        int i9 = 0;
        while (i9 <= i8) {
            int i10 = (i9 + i8) >>> 1;
            int i11 = iArr[i10];
            if (i11 < i7) {
                i9 = i10 + 1;
            } else {
                if (i11 <= i7) {
                    return i10;
                }
                i8 = i10 - 1;
            }
        }
        return ~i9;
    }

    /* renamed from: j0 */
    public static C1542f m4545j0(C1542f c1542f, C1090c c1090c, AbstractC1590j abstractC1590j, Boolean bool, Boolean bool2) {
        C1542f c1542f2 = new C1542f();
        Iterator<Integer> m3769p = c1542f.m3769p();
        while (m3769p.hasNext()) {
            int intValue = m3769p.next().intValue();
            if (c1542f.m3775v(intValue)) {
                InterfaceC1662p mo3670a = abstractC1590j.mo3670a(c1090c, Arrays.asList(c1542f.m3773t(intValue), new C1578i(Double.valueOf(intValue)), c1542f));
                if (mo3670a.mo3763g().equals(bool)) {
                    return c1542f2;
                }
                if (bool2 == null || mo3670a.mo3763g().equals(bool2)) {
                    c1542f2.m3774u(intValue, mo3670a);
                }
            }
        }
        return c1542f2;
    }

    /* renamed from: k */
    public static int m4546k(long[] jArr, int i6, long j6) {
        int i7 = i6 - 1;
        int i8 = 0;
        while (i8 <= i7) {
            int i9 = (i8 + i7) >>> 1;
            long j7 = jArr[i9];
            if (j7 < j6) {
                i8 = i9 + 1;
            } else {
                if (j7 <= j6) {
                    return i9;
                }
                i7 = i9 - 1;
            }
        }
        return ~i8;
    }

    /* renamed from: k0 */
    public static int m4547k0(byte[] bArr, int i6) {
        return ((bArr[i6 + 3] & 255) << 24) | (bArr[i6] & 255) | ((bArr[i6 + 1] & 255) << 8) | ((bArr[i6 + 2] & 255) << 16);
    }

    /* renamed from: l */
    public static void m4548l(Object obj, StringBuilder sb) {
        String hexString;
        int lastIndexOf;
        if (obj == null) {
            hexString = "null";
        } else {
            String simpleName = obj.getClass().getSimpleName();
            if (simpleName.length() <= 0 && (lastIndexOf = (simpleName = obj.getClass().getName()).lastIndexOf(46)) > 0) {
                simpleName = simpleName.substring(lastIndexOf + 1);
            }
            sb.append(simpleName);
            sb.append('{');
            hexString = Integer.toHexString(System.identityHashCode(obj));
        }
        sb.append(hexString);
    }

    /* renamed from: l0 */
    public static long m4549l0(byte[] bArr, int i6) {
        return ((bArr[i6 + 7] & 255) << 56) | (bArr[i6] & 255) | ((bArr[i6 + 1] & 255) << 8) | ((bArr[i6 + 2] & 255) << 16) | ((bArr[i6 + 3] & 255) << 24) | ((bArr[i6 + 4] & 255) << 32) | ((bArr[i6 + 5] & 255) << 40) | ((bArr[i6 + 6] & 255) << 48);
    }

    /* renamed from: m */
    public static void m4550m(boolean z5) {
        if (!z5) {
            throw new IllegalArgumentException();
        }
    }

    /* renamed from: m0 */
    public static boolean m4551m0(byte b6) {
        return b6 > -65;
    }

    /* renamed from: n */
    public static void m4552n(String str) {
        if (!(Looper.getMainLooper() == Looper.myLooper())) {
            throw new IllegalStateException(str);
        }
    }

    /* renamed from: n0 */
    public static int m4553n0(byte[] bArr, int i6, C1678q3 c1678q3) {
        int m4527T = m4527T(bArr, i6, c1678q3);
        int i7 = c1678q3.f7144a;
        if (i7 < 0) {
            throw C1485a5.m3642b();
        }
        if (i7 == 0) {
            c1678q3.f7146c = "";
            return m4527T;
        }
        c1678q3.f7146c = new String(bArr, m4527T, i7, C1775y4.f7289a);
        return m4527T + i7;
    }

    /* renamed from: o */
    public static String m4554o(String str) {
        if (TextUtils.isEmpty(str)) {
            throw new IllegalArgumentException("Given String is empty or null");
        }
        return str;
    }

    /* renamed from: o0 */
    public static int m4555o0(byte[] bArr, int i6, C1678q3 c1678q3) {
        int m4527T = m4527T(bArr, i6, c1678q3);
        int i7 = c1678q3.f7144a;
        if (i7 < 0) {
            throw C1485a5.m3642b();
        }
        if (i7 == 0) {
            c1678q3.f7146c = "";
            return m4527T;
        }
        C0385m c0385m = C1500b7.f6899a;
        int length = bArr.length;
        if ((m4527T | i7 | ((length - m4527T) - i7)) < 0) {
            throw new ArrayIndexOutOfBoundsException(String.format("buffer length=%d, index=%d, size=%d", Integer.valueOf(length), Integer.valueOf(m4527T), Integer.valueOf(i7)));
        }
        int i8 = m4527T + i7;
        char[] cArr = new char[i7];
        int i9 = 0;
        while (m4527T < i8) {
            byte b6 = bArr[m4527T];
            if (!(b6 >= 0)) {
                break;
            }
            m4527T++;
            cArr[i9] = (char) b6;
            i9++;
        }
        while (m4527T < i8) {
            int i10 = m4527T + 1;
            byte b7 = bArr[m4527T];
            if (b7 >= 0) {
                int i11 = i9 + 1;
                cArr[i9] = (char) b7;
                m4527T = i10;
                while (true) {
                    i9 = i11;
                    if (m4527T >= i8) {
                        break;
                    }
                    byte b8 = bArr[m4527T];
                    if (!(b8 >= 0)) {
                        break;
                    }
                    m4527T++;
                    i11 = i9 + 1;
                    cArr[i9] = (char) b8;
                }
            } else if (b7 < -32) {
                if (i10 >= i8) {
                    throw C1485a5.m3644d();
                }
                int i12 = i10 + 1;
                int i13 = i9 + 1;
                byte b9 = bArr[i10];
                if (b7 < -62 || m4551m0(b9)) {
                    throw C1485a5.m3644d();
                }
                cArr[i9] = (char) (((b7 & 31) << 6) | (b9 & 63));
                m4527T = i12;
                i9 = i13;
            } else {
                if (b7 < -16) {
                    if (i10 >= i8 - 1) {
                        throw C1485a5.m3644d();
                    }
                    int i14 = i10 + 1;
                    int i15 = i14 + 1;
                    int i16 = i9 + 1;
                    byte b10 = bArr[i10];
                    byte b11 = bArr[i14];
                    if (!m4551m0(b10)) {
                        if (b7 == -32) {
                            if (b10 >= -96) {
                                b7 = -32;
                            }
                        }
                        if (b7 == -19) {
                            if (b10 < -96) {
                                b7 = -19;
                            }
                        }
                        if (!m4551m0(b11)) {
                            cArr[i9] = (char) (((b7 & 15) << 12) | ((b10 & 63) << 6) | (b11 & 63));
                            m4527T = i15;
                            i9 = i16;
                        }
                    }
                    throw C1485a5.m3644d();
                }
                if (i10 >= i8 - 2) {
                    throw C1485a5.m3644d();
                }
                int i17 = i10 + 1;
                int i18 = i17 + 1;
                int i19 = i18 + 1;
                byte b12 = bArr[i10];
                byte b13 = bArr[i17];
                byte b14 = bArr[i18];
                if (m4551m0(b12) || (((b12 + 112) + (b7 << 28)) >> 30) != 0 || m4551m0(b13) || m4551m0(b14)) {
                    throw C1485a5.m3644d();
                }
                int i20 = ((b7 & 7) << 18) | ((b12 & 63) << 12) | ((b13 & 63) << 6) | (b14 & 63);
                cArr[i9] = (char) ((i20 >>> 10) + 55232);
                cArr[i9 + 1] = (char) ((i20 & 1023) + 56320);
                i9 += 2;
                m4527T = i19;
            }
        }
        c1678q3.f7146c = new String(cArr, 0, i9);
        return i8;
    }

    /* renamed from: p */
    public static void m4556p(String str) {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            throw new IllegalStateException(str);
        }
    }

    /* renamed from: p0 */
    public static int m4557p0(byte[] bArr, int i6, C1678q3 c1678q3) {
        int m4527T = m4527T(bArr, i6, c1678q3);
        int i7 = c1678q3.f7144a;
        if (i7 < 0) {
            throw C1485a5.m3642b();
        }
        if (i7 > bArr.length - m4527T) {
            throw C1485a5.m3641a();
        }
        if (i7 == 0) {
            c1678q3.f7146c = AbstractC1750w3.f7244k;
            return m4527T;
        }
        c1678q3.f7146c = AbstractC1750w3.m4357p(bArr, m4527T, i7);
        return m4527T + i7;
    }

    /* renamed from: q */
    public static Object m4558q(Object obj, Object obj2) {
        if (obj != null) {
            return obj;
        }
        throw new NullPointerException(String.valueOf(obj2));
    }

    /* renamed from: q0 */
    public static int m4559q0(InterfaceC1537e6 interfaceC1537e6, byte[] bArr, int i6, int i7, C1678q3 c1678q3) {
        int i8 = i6 + 1;
        int i9 = bArr[i6];
        if (i9 < 0) {
            i8 = m4535b0(i9, bArr, i8, c1678q3);
            i9 = c1678q3.f7144a;
        }
        int i10 = i8;
        if (i9 < 0 || i9 > i7 - i10) {
            throw C1485a5.m3641a();
        }
        Object mo3748a = interfaceC1537e6.mo3748a();
        int i11 = i9 + i10;
        interfaceC1537e6.mo3756i(mo3748a, bArr, i10, i11, c1678q3);
        interfaceC1537e6.mo3753f(mo3748a);
        c1678q3.f7146c = mo3748a;
        return i11;
    }

    /* renamed from: r */
    public static Object m4560r(Object obj) {
        Objects.requireNonNull(obj, "null reference");
        return obj;
    }

    /* renamed from: r0 */
    public static int m4561r0(InterfaceC1537e6 interfaceC1537e6, byte[] bArr, int i6, int i7, int i8, C1678q3 c1678q3) {
        C1752w5 c1752w5 = (C1752w5) interfaceC1537e6;
        Object mo3604r = ((AbstractC1691r4) c1752w5.f7252e).mo3604r(4);
        int m4389z = c1752w5.m4389z(mo3604r, bArr, i6, i7, i8, c1678q3);
        c1752w5.mo3753f(mo3604r);
        c1678q3.f7146c = mo3604r;
        return m4389z;
    }

    /* renamed from: s */
    public static int m4562s(Context context, String str) {
        int myPid = Process.myPid();
        int myUid = Process.myUid();
        String packageName = context.getPackageName();
        if (context.checkPermission(str, myPid, myUid) == -1) {
            return -1;
        }
        String permissionToOp = AppOpsManager.permissionToOp(str);
        if (permissionToOp != null) {
            if (packageName == null) {
                String[] packagesForUid = context.getPackageManager().getPackagesForUid(myUid);
                if (packagesForUid == null || packagesForUid.length <= 0) {
                    return -1;
                }
                packageName = packagesForUid[0];
            }
            if (((AppOpsManager) context.getSystemService(AppOpsManager.class)).noteProxyOpNoThrow(permissionToOp, packageName) != 0) {
                return -2;
            }
        }
        return 0;
    }

    /* renamed from: s0 */
    public static int m4563s0(int i6, byte[] bArr, int i7, int i8, InterfaceC1763x4 interfaceC1763x4, C1678q3 c1678q3) {
        C1703s4 c1703s4 = (C1703s4) interfaceC1763x4;
        int m4527T = m4527T(bArr, i7, c1678q3);
        while (true) {
            c1703s4.m4142i(c1678q3.f7144a);
            if (m4527T >= i8) {
                break;
            }
            int m4527T2 = m4527T(bArr, m4527T, c1678q3);
            if (i6 != c1678q3.f7144a) {
                break;
            }
            m4527T = m4527T(bArr, m4527T2, c1678q3);
        }
        return m4527T;
    }

    /* renamed from: t */
    public static void m4564t(boolean z5, Object obj) {
        if (!z5) {
            throw new IllegalStateException(String.valueOf(obj));
        }
    }

    /* renamed from: t0 */
    public static int m4565t0(byte[] bArr, int i6, InterfaceC1763x4 interfaceC1763x4, C1678q3 c1678q3) {
        C1703s4 c1703s4 = (C1703s4) interfaceC1763x4;
        int m4527T = m4527T(bArr, i6, c1678q3);
        int i7 = c1678q3.f7144a + m4527T;
        while (m4527T < i7) {
            m4527T = m4527T(bArr, m4527T, c1678q3);
            c1703s4.m4142i(c1678q3.f7144a);
        }
        if (m4527T == i7) {
            return m4527T;
        }
        throw C1485a5.m3641a();
    }

    /* renamed from: u */
    public static int m4566u(int i6, int i7) {
        return C1808a.m4625c(i6, (Color.alpha(i6) * i7) / 255);
    }

    /* renamed from: u0 */
    public static int m4567u0(InterfaceC1537e6 interfaceC1537e6, int i6, byte[] bArr, int i7, int i8, InterfaceC1763x4 interfaceC1763x4, C1678q3 c1678q3) {
        int m4559q0 = m4559q0(interfaceC1537e6, bArr, i7, i8, c1678q3);
        while (true) {
            interfaceC1763x4.add(c1678q3.f7146c);
            if (m4559q0 >= i8) {
                break;
            }
            int m4527T = m4527T(bArr, m4559q0, c1678q3);
            if (i6 != c1678q3.f7144a) {
                break;
            }
            m4559q0 = m4559q0(interfaceC1537e6, bArr, m4527T, i8, c1678q3);
        }
        return m4559q0;
    }

    /* renamed from: v */
    public static C1251d m4568v(int i6) {
        return i6 != 0 ? i6 != 1 ? new C1801h() : new C1797d() : new C1801h();
    }

    /* renamed from: v0 */
    public static int m4569v0(int i6, byte[] bArr, int i7, int i8, C1681q6 c1681q6, C1678q3 c1678q3) {
        if ((i6 >>> 3) == 0) {
            throw new C1485a5("Protocol message contained an invalid tag (zero).");
        }
        int i9 = i6 & 7;
        if (i9 == 0) {
            int m4541h0 = m4541h0(bArr, i7, c1678q3);
            c1681q6.m4029c(i6, Long.valueOf(c1678q3.f7145b));
            return m4541h0;
        }
        if (i9 == 1) {
            c1681q6.m4029c(i6, Long.valueOf(m4549l0(bArr, i7)));
            return i7 + 8;
        }
        if (i9 == 2) {
            int m4527T = m4527T(bArr, i7, c1678q3);
            int i10 = c1678q3.f7144a;
            if (i10 < 0) {
                throw C1485a5.m3642b();
            }
            if (i10 > bArr.length - m4527T) {
                throw C1485a5.m3641a();
            }
            c1681q6.m4029c(i6, i10 == 0 ? AbstractC1750w3.f7244k : AbstractC1750w3.m4357p(bArr, m4527T, i10));
            return m4527T + i10;
        }
        if (i9 != 3) {
            if (i9 != 5) {
                throw new C1485a5("Protocol message contained an invalid tag (zero).");
            }
            c1681q6.m4029c(i6, Integer.valueOf(m4547k0(bArr, i7)));
            return i7 + 4;
        }
        int i11 = (i6 & (-8)) | 4;
        C1681q6 m4027a = C1681q6.m4027a();
        int i12 = 0;
        while (true) {
            if (i7 >= i8) {
                break;
            }
            int m4527T2 = m4527T(bArr, i7, c1678q3);
            int i13 = c1678q3.f7144a;
            i12 = i13;
            if (i13 == i11) {
                i7 = m4527T2;
                break;
            }
            int m4569v0 = m4569v0(i12, bArr, m4527T2, i8, m4027a, c1678q3);
            i12 = i13;
            i7 = m4569v0;
        }
        if (i7 > i8 || i12 != i11) {
            throw C1485a5.m3643c();
        }
        c1681q6.m4029c(i6, m4027a);
        return i7;
    }

    /* renamed from: x */
    public static float m4570x(float f6, float f7, float f8, float f9) {
        return (float) Math.hypot(f8 - f6, f9 - f7);
    }

    /* renamed from: y */
    public static boolean m4571y(Object obj, Object obj2) {
        return obj == obj2 || (obj != null && obj.equals(obj2));
    }

    /* renamed from: z */
    public static int m4572z(Context context, int i6, int i7) {
        TypedValue m3412a = C1400b.m3412a(context, i6);
        return m3412a != null ? m3412a.data : i7;
    }

    /* renamed from: B */
    public void m4573B(float f6, float f7, float f8, C1804k c1804k) {
        c1804k.m4613e(f6, 0.0f);
    }

    /* renamed from: N */
    public void mo3355N(View view, int i6) {
        if (!f7325D) {
            try {
                Field declaredField = View.class.getDeclaredField("mViewFlags");
                f7324C = declaredField;
                declaredField.setAccessible(true);
            } catch (NoSuchFieldException unused) {
                Log.i("ViewUtilsBase", "fetchViewFlagsField: ");
            }
            f7325D = true;
        }
        Field field = f7324C;
        if (field != null) {
            try {
                f7324C.setInt(view, i6 | (field.getInt(view) & (-13)));
            } catch (IllegalAccessException unused2) {
            }
        }
    }

    @Override // p158x2.InterfaceC2002u2
    /* renamed from: a */
    public Object mo1434a() {
        C2010v2<Long> c2010v2 = C2026x2.f8188b;
        return Integer.valueOf((int) C1718t7.f7187k.mo2029a().mo4318u());
    }

    @Override // p112q1.InterfaceC1261g
    /* renamed from: c */
    public void mo3141c() {
    }

    @Override // p112q1.InterfaceC1262h
    /* renamed from: d */
    public void mo3147d(InterfaceC1263i interfaceC1263i) {
        interfaceC1263i.mo1513b();
    }

    @Override // p112q1.InterfaceC1262h
    /* renamed from: e */
    public void mo3148e(InterfaceC1263i interfaceC1263i) {
    }

    /* renamed from: f */
    public void mo315f(View view) {
    }

    /* renamed from: g */
    public void mo316g() {
    }

    @Override // p105p1.InterfaceC1232b
    /* renamed from: h */
    public InterfaceC0725w mo662h(InterfaceC0725w interfaceC0725w, C0395h c0395h) {
        byte[] bArr;
        ByteBuffer asReadOnlyBuffer = ((C1139c) interfaceC0725w.get()).f5487j.f5497a.f5499a.mo10g().asReadOnlyBuffer();
        AtomicReference<byte[]> atomicReference = C1818a.f7502a;
        C1818a.b bVar = (asReadOnlyBuffer.isReadOnly() || !asReadOnlyBuffer.hasArray()) ? null : new C1818a.b(asReadOnlyBuffer.array(), asReadOnlyBuffer.arrayOffset(), asReadOnlyBuffer.limit());
        if (bVar != null && bVar.f7505a == 0 && bVar.f7506b == bVar.f7507c.length) {
            bArr = asReadOnlyBuffer.array();
        } else {
            ByteBuffer asReadOnlyBuffer2 = asReadOnlyBuffer.asReadOnlyBuffer();
            byte[] bArr2 = new byte[asReadOnlyBuffer2.limit()];
            asReadOnlyBuffer2.get(bArr2);
            bArr = bArr2;
        }
        return new C1061b(bArr);
    }

    /* renamed from: w */
    public long m4574w() {
        return System.currentTimeMillis();
    }

    public /* synthetic */ C1798e(SwipeDismissBehavior swipeDismissBehavior) {
        Objects.requireNonNull(swipeDismissBehavior);
        swipeDismissBehavior.f2717e = SwipeDismissBehavior.m1575t(0.1f);
        swipeDismissBehavior.f2718f = SwipeDismissBehavior.m1575t(0.6f);
        swipeDismissBehavior.f2715c = 0;
    }
}
