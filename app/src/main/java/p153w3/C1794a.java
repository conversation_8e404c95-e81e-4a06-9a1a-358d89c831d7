package p153w3;

import android.graphics.RectF;
import java.util.Arrays;

/* renamed from: w3.a */
/* loaded from: classes.dex */
public final class C1794a implements InterfaceC1796c {

    /* renamed from: a */
    public final float f7319a;

    public C1794a(float f6) {
        this.f7319a = f6;
    }

    @Override // p153w3.InterfaceC1796c
    /* renamed from: a */
    public final float mo4509a(RectF rectF) {
        return this.f7319a;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        return (obj instanceof C1794a) && this.f7319a == ((C1794a) obj).f7319a;
    }

    public final int hashCode() {
        return Arrays.hashCode(new Object[]{Float.valueOf(this.f7319a)});
    }
}
