package p153w3;

import android.graphics.RectF;
import java.util.Arrays;

/* renamed from: w3.g */
/* loaded from: classes.dex */
public final class C1800g implements InterfaceC1796c {

    /* renamed from: a */
    public final float f7399a;

    public C1800g(float f6) {
        this.f7399a = f6;
    }

    @Override // p153w3.InterfaceC1796c
    /* renamed from: a */
    public final float mo4509a(RectF rectF) {
        return rectF.height() * this.f7399a;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        return (obj instanceof C1800g) && this.f7399a == ((C1800g) obj).f7399a;
    }

    public final int hashCode() {
        return Arrays.hashCode(new Object[]{Float.valueOf(this.f7399a)});
    }
}
