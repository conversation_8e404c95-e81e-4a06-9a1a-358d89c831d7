package p153w3;

import android.graphics.RectF;
import java.util.Arrays;

/* renamed from: w3.b */
/* loaded from: classes.dex */
public final class C1795b implements InterfaceC1796c {

    /* renamed from: a */
    public final InterfaceC1796c f7320a;

    /* renamed from: b */
    public final float f7321b;

    public C1795b(float f6, InterfaceC1796c interfaceC1796c) {
        while (interfaceC1796c instanceof C1795b) {
            interfaceC1796c = ((C1795b) interfaceC1796c).f7320a;
            f6 += ((C1795b) interfaceC1796c).f7321b;
        }
        this.f7320a = interfaceC1796c;
        this.f7321b = f6;
    }

    @Override // p153w3.InterfaceC1796c
    /* renamed from: a */
    public final float mo4509a(RectF rectF) {
        return Math.max(0.0f, this.f7320a.mo4509a(rectF) + this.f7321b);
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof C1795b)) {
            return false;
        }
        C1795b c1795b = (C1795b) obj;
        return this.f7320a.equals(c1795b.f7320a) && this.f7321b == c1795b.f7321b;
    }

    public final int hashCode() {
        return Arrays.hashCode(new Object[]{this.f7320a, Float.valueOf(this.f7321b)});
    }
}
