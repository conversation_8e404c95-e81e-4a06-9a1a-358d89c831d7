package p153w3;

import android.graphics.Matrix;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.RectF;
import java.util.BitSet;
import java.util.Objects;
import p110q.C1251d;
import p153w3.C1799f;

/* renamed from: w3.j */
/* loaded from: classes.dex */
public final class C1803j {

    /* renamed from: a */
    public final C1804k[] f7424a = new C1804k[4];

    /* renamed from: b */
    public final Matrix[] f7425b = new Matrix[4];

    /* renamed from: c */
    public final Matrix[] f7426c = new Matrix[4];

    /* renamed from: d */
    public final PointF f7427d = new PointF();

    /* renamed from: e */
    public final Path f7428e = new Path();

    /* renamed from: f */
    public final Path f7429f = new Path();

    /* renamed from: g */
    public final C1804k f7430g = new C1804k();

    /* renamed from: h */
    public final float[] f7431h = new float[2];

    /* renamed from: i */
    public final float[] f7432i = new float[2];

    /* renamed from: j */
    public final Path f7433j = new Path();

    /* renamed from: k */
    public final Path f7434k = new Path();

    /* renamed from: l */
    public boolean f7435l = true;

    /* renamed from: w3.j$a */
    public static class a {

        /* renamed from: a */
        public static final C1803j f7436a = new C1803j();
    }

    /* renamed from: w3.j$b */
    public interface b {
    }

    public C1803j() {
        for (int i6 = 0; i6 < 4; i6++) {
            this.f7424a[i6] = new C1804k();
            this.f7425b[i6] = new Matrix();
            this.f7426c[i6] = new Matrix();
        }
    }

    /* renamed from: a */
    public final void m4607a(C1802i c1802i, float f6, RectF rectF, b bVar, Path path) {
        int i6;
        float centerX;
        float f7;
        C1804k c1804k;
        Matrix matrix;
        Path path2;
        float f8;
        float f9;
        path.rewind();
        this.f7428e.rewind();
        this.f7429f.rewind();
        this.f7429f.addRect(rectF, Path.Direction.CW);
        int i7 = 0;
        while (true) {
            if (i7 >= 4) {
                break;
            }
            InterfaceC1796c interfaceC1796c = i7 != 1 ? i7 != 2 ? i7 != 3 ? c1802i.f7405f : c1802i.f7404e : c1802i.f7407h : c1802i.f7406g;
            C1251d c1251d = i7 != 1 ? i7 != 2 ? i7 != 3 ? c1802i.f7401b : c1802i.f7400a : c1802i.f7403d : c1802i.f7402c;
            C1804k c1804k2 = this.f7424a[i7];
            Objects.requireNonNull(c1251d);
            c1251d.mo3140a(c1804k2, f6, interfaceC1796c.mo4509a(rectF));
            int i8 = i7 + 1;
            float f10 = i8 * 90;
            this.f7425b[i7].reset();
            PointF pointF = this.f7427d;
            if (i7 == 1) {
                f8 = rectF.right;
            } else if (i7 != 2) {
                f8 = i7 != 3 ? rectF.right : rectF.left;
                f9 = rectF.top;
                pointF.set(f8, f9);
                Matrix matrix2 = this.f7425b[i7];
                PointF pointF2 = this.f7427d;
                matrix2.setTranslate(pointF2.x, pointF2.y);
                this.f7425b[i7].preRotate(f10);
                float[] fArr = this.f7431h;
                C1804k[] c1804kArr = this.f7424a;
                fArr[0] = c1804kArr[i7].f7439c;
                fArr[1] = c1804kArr[i7].f7440d;
                this.f7425b[i7].mapPoints(fArr);
                this.f7426c[i7].reset();
                Matrix matrix3 = this.f7426c[i7];
                float[] fArr2 = this.f7431h;
                matrix3.setTranslate(fArr2[0], fArr2[1]);
                this.f7426c[i7].preRotate(f10);
                i7 = i8;
            } else {
                f8 = rectF.left;
            }
            f9 = rectF.bottom;
            pointF.set(f8, f9);
            Matrix matrix22 = this.f7425b[i7];
            PointF pointF22 = this.f7427d;
            matrix22.setTranslate(pointF22.x, pointF22.y);
            this.f7425b[i7].preRotate(f10);
            float[] fArr3 = this.f7431h;
            C1804k[] c1804kArr2 = this.f7424a;
            fArr3[0] = c1804kArr2[i7].f7439c;
            fArr3[1] = c1804kArr2[i7].f7440d;
            this.f7425b[i7].mapPoints(fArr3);
            this.f7426c[i7].reset();
            Matrix matrix32 = this.f7426c[i7];
            float[] fArr22 = this.f7431h;
            matrix32.setTranslate(fArr22[0], fArr22[1]);
            this.f7426c[i7].preRotate(f10);
            i7 = i8;
        }
        int i9 = 0;
        for (i6 = 4; i9 < i6; i6 = 4) {
            float[] fArr4 = this.f7431h;
            C1804k[] c1804kArr3 = this.f7424a;
            fArr4[0] = c1804kArr3[i9].f7437a;
            fArr4[1] = c1804kArr3[i9].f7438b;
            this.f7425b[i9].mapPoints(fArr4);
            float[] fArr5 = this.f7431h;
            if (i9 == 0) {
                path.moveTo(fArr5[0], fArr5[1]);
            } else {
                path.lineTo(fArr5[0], fArr5[1]);
            }
            this.f7424a[i9].m4611c(this.f7425b[i9], path);
            if (bVar != null) {
                C1804k c1804k3 = this.f7424a[i9];
                Matrix matrix4 = this.f7425b[i9];
                C1799f.a aVar = (C1799f.a) bVar;
                BitSet bitSet = C1799f.this.f7363m;
                Objects.requireNonNull(c1804k3);
                bitSet.set(i9, false);
                C1799f.this.f7361k[i9] = c1804k3.m4612d(matrix4);
            }
            int i10 = i9 + 1;
            int i11 = i10 % 4;
            float[] fArr6 = this.f7431h;
            C1804k[] c1804kArr4 = this.f7424a;
            fArr6[0] = c1804kArr4[i9].f7439c;
            fArr6[1] = c1804kArr4[i9].f7440d;
            this.f7425b[i9].mapPoints(fArr6);
            float[] fArr7 = this.f7432i;
            C1804k[] c1804kArr5 = this.f7424a;
            fArr7[0] = c1804kArr5[i11].f7437a;
            fArr7[1] = c1804kArr5[i11].f7438b;
            this.f7425b[i11].mapPoints(fArr7);
            float f11 = this.f7431h[0];
            float[] fArr8 = this.f7432i;
            float max = Math.max(((float) Math.hypot(f11 - fArr8[0], r13[1] - fArr8[1])) - 0.001f, 0.0f);
            float[] fArr9 = this.f7431h;
            C1804k[] c1804kArr6 = this.f7424a;
            fArr9[0] = c1804kArr6[i9].f7439c;
            fArr9[1] = c1804kArr6[i9].f7440d;
            this.f7425b[i9].mapPoints(fArr9);
            if (i9 == 1 || i9 == 3) {
                centerX = rectF.centerX();
                f7 = this.f7431h[0];
            } else {
                centerX = rectF.centerY();
                f7 = this.f7431h[1];
            }
            float abs = Math.abs(centerX - f7);
            this.f7430g.m4614f(0.0f, 270.0f, 0.0f);
            (i9 != 1 ? i9 != 2 ? i9 != 3 ? c1802i.f7409j : c1802i.f7408i : c1802i.f7411l : c1802i.f7410k).m4573B(max, abs, f6, this.f7430g);
            this.f7433j.reset();
            this.f7430g.m4611c(this.f7426c[i9], this.f7433j);
            if (this.f7435l && (m4608b(this.f7433j, i9) || m4608b(this.f7433j, i11))) {
                Path path3 = this.f7433j;
                path3.op(path3, this.f7429f, Path.Op.DIFFERENCE);
                float[] fArr10 = this.f7431h;
                C1804k c1804k4 = this.f7430g;
                fArr10[0] = c1804k4.f7437a;
                fArr10[1] = c1804k4.f7438b;
                this.f7426c[i9].mapPoints(fArr10);
                Path path4 = this.f7428e;
                float[] fArr11 = this.f7431h;
                path4.moveTo(fArr11[0], fArr11[1]);
                c1804k = this.f7430g;
                matrix = this.f7426c[i9];
                path2 = this.f7428e;
            } else {
                c1804k = this.f7430g;
                matrix = this.f7426c[i9];
                path2 = path;
            }
            c1804k.m4611c(matrix, path2);
            if (bVar != null) {
                C1804k c1804k5 = this.f7430g;
                Matrix matrix5 = this.f7426c[i9];
                C1799f.a aVar2 = (C1799f.a) bVar;
                Objects.requireNonNull(c1804k5);
                C1799f.this.f7363m.set(i9 + 4, false);
                C1799f.this.f7362l[i9] = c1804k5.m4612d(matrix5);
            }
            i9 = i10;
        }
        path.close();
        this.f7428e.close();
        if (this.f7428e.isEmpty()) {
            return;
        }
        path.op(this.f7428e, Path.Op.UNION);
    }

    /* renamed from: b */
    public final boolean m4608b(Path path, int i6) {
        this.f7434k.reset();
        this.f7424a[i6].m4611c(this.f7425b[i6], this.f7434k);
        RectF rectF = new RectF();
        path.computeBounds(rectF, true);
        this.f7434k.computeBounds(rectF, true);
        path.op(this.f7434k, Path.Op.INTERSECT);
        path.computeBounds(rectF, true);
        if (rectF.isEmpty()) {
            return rectF.width() > 1.0f && rectF.height() > 1.0f;
        }
        return true;
    }
}
