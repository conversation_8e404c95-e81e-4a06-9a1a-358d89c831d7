package p153w3;

import p110q.C1251d;

/* renamed from: w3.d */
/* loaded from: classes.dex */
public final class C1797d extends C1251d {
    public C1797d() {
        super(7);
    }

    @Override // p110q.C1251d
    /* renamed from: a */
    public final void mo3140a(C1804k c1804k, float f6, float f7) {
        c1804k.m4614f(f7 * f6, 180.0f, 90.0f);
        double d6 = f7;
        double d7 = f6;
        c1804k.m4613e((float) (Math.sin(Math.toRadians(90.0f)) * d6 * d7), (float) (Math.sin(Math.toRadians(0.0f)) * d6 * d7));
    }
}
