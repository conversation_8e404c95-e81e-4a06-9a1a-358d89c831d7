package p153w3;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.ContextThemeWrapper;
import p008b0.C0385m;
import p110q.C1251d;

/* renamed from: w3.i */
/* loaded from: classes.dex */
public final class C1802i {

    /* renamed from: a */
    public C1251d f7400a;

    /* renamed from: b */
    public C1251d f7401b;

    /* renamed from: c */
    public C1251d f7402c;

    /* renamed from: d */
    public C1251d f7403d;

    /* renamed from: e */
    public InterfaceC1796c f7404e;

    /* renamed from: f */
    public InterfaceC1796c f7405f;

    /* renamed from: g */
    public InterfaceC1796c f7406g;

    /* renamed from: h */
    public InterfaceC1796c f7407h;

    /* renamed from: i */
    public C1798e f7408i;

    /* renamed from: j */
    public C1798e f7409j;

    /* renamed from: k */
    public C1798e f7410k;

    /* renamed from: l */
    public C1798e f7411l;

    public C1802i() {
        this.f7400a = new C1801h();
        this.f7401b = new C1801h();
        this.f7402c = new C1801h();
        this.f7403d = new C1801h();
        this.f7404e = new C1794a(0.0f);
        this.f7405f = new C1794a(0.0f);
        this.f7406g = new C1794a(0.0f);
        this.f7407h = new C1794a(0.0f);
        this.f7408i = new C1798e();
        this.f7409j = new C1798e();
        this.f7410k = new C1798e();
        this.f7411l = new C1798e();
    }

    /* renamed from: a */
    public static a m4596a(Context context, int i6, int i7, InterfaceC1796c interfaceC1796c) {
        if (i7 != 0) {
            ContextThemeWrapper contextThemeWrapper = new ContextThemeWrapper(context, i6);
            i6 = i7;
            context = contextThemeWrapper;
        }
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(i6, C0385m.f2378z0);
        try {
            int i8 = obtainStyledAttributes.getInt(0, 0);
            int i9 = obtainStyledAttributes.getInt(3, i8);
            int i10 = obtainStyledAttributes.getInt(4, i8);
            int i11 = obtainStyledAttributes.getInt(2, i8);
            int i12 = obtainStyledAttributes.getInt(1, i8);
            InterfaceC1796c m4598c = m4598c(obtainStyledAttributes, 5, interfaceC1796c);
            InterfaceC1796c m4598c2 = m4598c(obtainStyledAttributes, 8, m4598c);
            InterfaceC1796c m4598c3 = m4598c(obtainStyledAttributes, 9, m4598c);
            InterfaceC1796c m4598c4 = m4598c(obtainStyledAttributes, 7, m4598c);
            InterfaceC1796c m4598c5 = m4598c(obtainStyledAttributes, 6, m4598c);
            a aVar = new a();
            C1251d m4568v = C1798e.m4568v(i9);
            aVar.f7412a = m4568v;
            a.m4601b(m4568v);
            aVar.f7416e = m4598c2;
            C1251d m4568v2 = C1798e.m4568v(i10);
            aVar.f7413b = m4568v2;
            a.m4601b(m4568v2);
            aVar.f7417f = m4598c3;
            C1251d m4568v3 = C1798e.m4568v(i11);
            aVar.f7414c = m4568v3;
            a.m4601b(m4568v3);
            aVar.f7418g = m4598c4;
            C1251d m4568v4 = C1798e.m4568v(i12);
            aVar.f7415d = m4568v4;
            a.m4601b(m4568v4);
            aVar.f7419h = m4598c5;
            return aVar;
        } finally {
            obtainStyledAttributes.recycle();
        }
    }

    /* renamed from: b */
    public static a m4597b(Context context, AttributeSet attributeSet, int i6, int i7) {
        C1794a c1794a = new C1794a(0);
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C0385m.f2366t0, i6, i7);
        int resourceId = obtainStyledAttributes.getResourceId(0, 0);
        int resourceId2 = obtainStyledAttributes.getResourceId(1, 0);
        obtainStyledAttributes.recycle();
        return m4596a(context, resourceId, resourceId2, c1794a);
    }

    /* renamed from: c */
    public static InterfaceC1796c m4598c(TypedArray typedArray, int i6, InterfaceC1796c interfaceC1796c) {
        TypedValue peekValue = typedArray.peekValue(i6);
        if (peekValue == null) {
            return interfaceC1796c;
        }
        int i7 = peekValue.type;
        return i7 == 5 ? new C1794a(TypedValue.complexToDimensionPixelSize(peekValue.data, typedArray.getResources().getDisplayMetrics())) : i7 == 6 ? new C1800g(peekValue.getFraction(1.0f, 1.0f)) : interfaceC1796c;
    }

    /* renamed from: d */
    public final boolean m4599d(RectF rectF) {
        boolean z5 = this.f7411l.getClass().equals(C1798e.class) && this.f7409j.getClass().equals(C1798e.class) && this.f7408i.getClass().equals(C1798e.class) && this.f7410k.getClass().equals(C1798e.class);
        float mo4509a = this.f7404e.mo4509a(rectF);
        return z5 && ((this.f7405f.mo4509a(rectF) > mo4509a ? 1 : (this.f7405f.mo4509a(rectF) == mo4509a ? 0 : -1)) == 0 && (this.f7407h.mo4509a(rectF) > mo4509a ? 1 : (this.f7407h.mo4509a(rectF) == mo4509a ? 0 : -1)) == 0 && (this.f7406g.mo4509a(rectF) > mo4509a ? 1 : (this.f7406g.mo4509a(rectF) == mo4509a ? 0 : -1)) == 0) && ((this.f7401b instanceof C1801h) && (this.f7400a instanceof C1801h) && (this.f7402c instanceof C1801h) && (this.f7403d instanceof C1801h));
    }

    /* renamed from: e */
    public final C1802i m4600e(float f6) {
        a aVar = new a(this);
        aVar.m4605e(f6);
        aVar.m4606f(f6);
        aVar.m4604d(f6);
        aVar.m4603c(f6);
        return aVar.m4602a();
    }

    /* renamed from: w3.i$a */
    public static final class a {

        /* renamed from: a */
        public C1251d f7412a;

        /* renamed from: b */
        public C1251d f7413b;

        /* renamed from: c */
        public C1251d f7414c;

        /* renamed from: d */
        public C1251d f7415d;

        /* renamed from: e */
        public InterfaceC1796c f7416e;

        /* renamed from: f */
        public InterfaceC1796c f7417f;

        /* renamed from: g */
        public InterfaceC1796c f7418g;

        /* renamed from: h */
        public InterfaceC1796c f7419h;

        /* renamed from: i */
        public C1798e f7420i;

        /* renamed from: j */
        public C1798e f7421j;

        /* renamed from: k */
        public C1798e f7422k;

        /* renamed from: l */
        public C1798e f7423l;

        public a() {
            this.f7412a = new C1801h();
            this.f7413b = new C1801h();
            this.f7414c = new C1801h();
            this.f7415d = new C1801h();
            this.f7416e = new C1794a(0.0f);
            this.f7417f = new C1794a(0.0f);
            this.f7418g = new C1794a(0.0f);
            this.f7419h = new C1794a(0.0f);
            this.f7420i = new C1798e();
            this.f7421j = new C1798e();
            this.f7422k = new C1798e();
            this.f7423l = new C1798e();
        }

        /* renamed from: b */
        public static void m4601b(C1251d c1251d) {
            if (c1251d instanceof C1801h) {
            } else if (c1251d instanceof C1797d) {
            }
        }

        /* renamed from: a */
        public final C1802i m4602a() {
            return new C1802i(this);
        }

        /* renamed from: c */
        public final a m4603c(float f6) {
            this.f7419h = new C1794a(f6);
            return this;
        }

        /* renamed from: d */
        public final a m4604d(float f6) {
            this.f7418g = new C1794a(f6);
            return this;
        }

        /* renamed from: e */
        public final a m4605e(float f6) {
            this.f7416e = new C1794a(f6);
            return this;
        }

        /* renamed from: f */
        public final a m4606f(float f6) {
            this.f7417f = new C1794a(f6);
            return this;
        }

        public a(C1802i c1802i) {
            this.f7412a = new C1801h();
            this.f7413b = new C1801h();
            this.f7414c = new C1801h();
            this.f7415d = new C1801h();
            this.f7416e = new C1794a(0.0f);
            this.f7417f = new C1794a(0.0f);
            this.f7418g = new C1794a(0.0f);
            this.f7419h = new C1794a(0.0f);
            this.f7420i = new C1798e();
            this.f7421j = new C1798e();
            this.f7422k = new C1798e();
            this.f7423l = new C1798e();
            this.f7412a = c1802i.f7400a;
            this.f7413b = c1802i.f7401b;
            this.f7414c = c1802i.f7402c;
            this.f7415d = c1802i.f7403d;
            this.f7416e = c1802i.f7404e;
            this.f7417f = c1802i.f7405f;
            this.f7418g = c1802i.f7406g;
            this.f7419h = c1802i.f7407h;
            this.f7420i = c1802i.f7408i;
            this.f7421j = c1802i.f7409j;
            this.f7422k = c1802i.f7410k;
            this.f7423l = c1802i.f7411l;
        }
    }

    public C1802i(a aVar) {
        this.f7400a = aVar.f7412a;
        this.f7401b = aVar.f7413b;
        this.f7402c = aVar.f7414c;
        this.f7403d = aVar.f7415d;
        this.f7404e = aVar.f7416e;
        this.f7405f = aVar.f7417f;
        this.f7406g = aVar.f7418g;
        this.f7407h = aVar.f7419h;
        this.f7408i = aVar.f7420i;
        this.f7409j = aVar.f7421j;
        this.f7410k = aVar.f7422k;
        this.f7411l = aVar.f7423l;
    }
}
