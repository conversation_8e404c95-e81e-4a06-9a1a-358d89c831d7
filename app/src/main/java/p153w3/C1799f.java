package p153w3;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorFilter;
import android.graphics.Matrix;
import android.graphics.Outline;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffColorFilter;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Region;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Looper;
import android.util.AttributeSet;
import android.util.Log;
import java.util.BitSet;
import java.util.Objects;
import p093n3.C1120a;
import p146v3.C1460a;
import p153w3.C1803j;
import p153w3.C1804k;
import p156x.C1808a;

/* renamed from: w3.f */
/* loaded from: classes.dex */
public class C1799f extends Drawable implements InterfaceC1805l {

    /* renamed from: F */
    public static final String f7353F = C1799f.class.getSimpleName();

    /* renamed from: G */
    public static final Paint f7354G = new Paint(1);

    /* renamed from: A */
    public final C1803j f7355A;

    /* renamed from: B */
    public PorterDuffColorFilter f7356B;

    /* renamed from: C */
    public PorterDuffColorFilter f7357C;

    /* renamed from: D */
    public final RectF f7358D;

    /* renamed from: E */
    public boolean f7359E;

    /* renamed from: j */
    public b f7360j;

    /* renamed from: k */
    public final C1804k.g[] f7361k;

    /* renamed from: l */
    public final C1804k.g[] f7362l;

    /* renamed from: m */
    public final BitSet f7363m;

    /* renamed from: n */
    public boolean f7364n;

    /* renamed from: o */
    public final Matrix f7365o;

    /* renamed from: p */
    public final Path f7366p;

    /* renamed from: q */
    public final Path f7367q;

    /* renamed from: r */
    public final RectF f7368r;

    /* renamed from: s */
    public final RectF f7369s;

    /* renamed from: t */
    public final Region f7370t;

    /* renamed from: u */
    public final Region f7371u;

    /* renamed from: v */
    public C1802i f7372v;

    /* renamed from: w */
    public final Paint f7373w;

    /* renamed from: x */
    public final Paint f7374x;

    /* renamed from: y */
    public final C1460a f7375y;

    /* renamed from: z */
    public final a f7376z;

    /* renamed from: w3.f$a */
    public class a implements C1803j.b {
        public a() {
        }
    }

    /* renamed from: w3.f$b */
    public static final class b extends Drawable.ConstantState {

        /* renamed from: a */
        public C1802i f7378a;

        /* renamed from: b */
        public C1120a f7379b;

        /* renamed from: c */
        public ColorStateList f7380c;

        /* renamed from: d */
        public ColorStateList f7381d;

        /* renamed from: e */
        public ColorStateList f7382e;

        /* renamed from: f */
        public ColorStateList f7383f;

        /* renamed from: g */
        public PorterDuff.Mode f7384g;

        /* renamed from: h */
        public Rect f7385h;

        /* renamed from: i */
        public float f7386i;

        /* renamed from: j */
        public float f7387j;

        /* renamed from: k */
        public float f7388k;

        /* renamed from: l */
        public int f7389l;

        /* renamed from: m */
        public float f7390m;

        /* renamed from: n */
        public float f7391n;

        /* renamed from: o */
        public float f7392o;

        /* renamed from: p */
        public int f7393p;

        /* renamed from: q */
        public int f7394q;

        /* renamed from: r */
        public int f7395r;

        /* renamed from: s */
        public int f7396s;

        /* renamed from: t */
        public boolean f7397t;

        /* renamed from: u */
        public Paint.Style f7398u;

        public b(b bVar) {
            this.f7380c = null;
            this.f7381d = null;
            this.f7382e = null;
            this.f7383f = null;
            this.f7384g = PorterDuff.Mode.SRC_IN;
            this.f7385h = null;
            this.f7386i = 1.0f;
            this.f7387j = 1.0f;
            this.f7389l = 255;
            this.f7390m = 0.0f;
            this.f7391n = 0.0f;
            this.f7392o = 0.0f;
            this.f7393p = 0;
            this.f7394q = 0;
            this.f7395r = 0;
            this.f7396s = 0;
            this.f7397t = false;
            this.f7398u = Paint.Style.FILL_AND_STROKE;
            this.f7378a = bVar.f7378a;
            this.f7379b = bVar.f7379b;
            this.f7388k = bVar.f7388k;
            this.f7380c = bVar.f7380c;
            this.f7381d = bVar.f7381d;
            this.f7384g = bVar.f7384g;
            this.f7383f = bVar.f7383f;
            this.f7389l = bVar.f7389l;
            this.f7386i = bVar.f7386i;
            this.f7395r = bVar.f7395r;
            this.f7393p = bVar.f7393p;
            this.f7397t = bVar.f7397t;
            this.f7387j = bVar.f7387j;
            this.f7390m = bVar.f7390m;
            this.f7391n = bVar.f7391n;
            this.f7392o = bVar.f7392o;
            this.f7394q = bVar.f7394q;
            this.f7396s = bVar.f7396s;
            this.f7382e = bVar.f7382e;
            this.f7398u = bVar.f7398u;
            if (bVar.f7385h != null) {
                this.f7385h = new Rect(bVar.f7385h);
            }
        }

        public b(C1802i c1802i) {
            this.f7380c = null;
            this.f7381d = null;
            this.f7382e = null;
            this.f7383f = null;
            this.f7384g = PorterDuff.Mode.SRC_IN;
            this.f7385h = null;
            this.f7386i = 1.0f;
            this.f7387j = 1.0f;
            this.f7389l = 255;
            this.f7390m = 0.0f;
            this.f7391n = 0.0f;
            this.f7392o = 0.0f;
            this.f7393p = 0;
            this.f7394q = 0;
            this.f7395r = 0;
            this.f7396s = 0;
            this.f7397t = false;
            this.f7398u = Paint.Style.FILL_AND_STROKE;
            this.f7378a = c1802i;
            this.f7379b = null;
        }

        @Override // android.graphics.drawable.Drawable.ConstantState
        public final int getChangingConfigurations() {
            return 0;
        }

        @Override // android.graphics.drawable.Drawable.ConstantState
        public final Drawable newDrawable() {
            C1799f c1799f = new C1799f(this);
            c1799f.f7364n = true;
            return c1799f;
        }
    }

    public C1799f() {
        this(new C1802i());
    }

    public C1799f(Context context, AttributeSet attributeSet, int i6, int i7) {
        this(C1802i.m4597b(context, attributeSet, i6, i7).m4602a());
    }

    public C1799f(b bVar) {
        this.f7361k = new C1804k.g[4];
        this.f7362l = new C1804k.g[4];
        this.f7363m = new BitSet(8);
        this.f7365o = new Matrix();
        this.f7366p = new Path();
        this.f7367q = new Path();
        this.f7368r = new RectF();
        this.f7369s = new RectF();
        this.f7370t = new Region();
        this.f7371u = new Region();
        Paint paint = new Paint(1);
        this.f7373w = paint;
        Paint paint2 = new Paint(1);
        this.f7374x = paint2;
        this.f7375y = new C1460a();
        this.f7355A = Looper.getMainLooper().getThread() == Thread.currentThread() ? C1803j.a.f7436a : new C1803j();
        this.f7358D = new RectF();
        this.f7359E = true;
        this.f7360j = bVar;
        paint2.setStyle(Paint.Style.STROKE);
        paint.setStyle(Paint.Style.FILL);
        Paint paint3 = f7354G;
        paint3.setColor(-1);
        paint3.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.DST_OUT));
        m4594u();
        m4593t(getState());
        this.f7376z = new a();
    }

    public C1799f(C1802i c1802i) {
        this(new b(c1802i));
    }

    /* renamed from: b */
    public final void m4575b(RectF rectF, Path path) {
        C1803j c1803j = this.f7355A;
        b bVar = this.f7360j;
        c1803j.m4607a(bVar.f7378a, bVar.f7387j, rectF, this.f7376z, path);
        if (this.f7360j.f7386i != 1.0f) {
            this.f7365o.reset();
            Matrix matrix = this.f7365o;
            float f6 = this.f7360j.f7386i;
            matrix.setScale(f6, f6, rectF.width() / 2.0f, rectF.height() / 2.0f);
            path.transform(this.f7365o);
        }
        path.computeBounds(this.f7358D, true);
    }

    /* renamed from: c */
    public final PorterDuffColorFilter m4576c(ColorStateList colorStateList, PorterDuff.Mode mode, Paint paint, boolean z5) {
        int color;
        int m4577d;
        if (colorStateList == null || mode == null) {
            return (!z5 || (m4577d = m4577d((color = paint.getColor()))) == color) ? null : new PorterDuffColorFilter(m4577d, PorterDuff.Mode.SRC_IN);
        }
        int colorForState = colorStateList.getColorForState(getState(), 0);
        if (z5) {
            colorForState = m4577d(colorForState);
        }
        return new PorterDuffColorFilter(colorForState, mode);
    }

    /* renamed from: d */
    public final int m4577d(int i6) {
        b bVar = this.f7360j;
        float f6 = bVar.f7391n + bVar.f7392o + bVar.f7390m;
        C1120a c1120a = bVar.f7379b;
        if (c1120a == null || !c1120a.f5334a) {
            return i6;
        }
        if (!(C1808a.m4625c(i6, 255) == c1120a.f5336c)) {
            return i6;
        }
        float f7 = 0.0f;
        if (c1120a.f5337d > 0.0f && f6 > 0.0f) {
            f7 = Math.min(((((float) Math.log1p(f6 / r3)) * 4.5f) + 2.0f) / 100.0f, 1.0f);
        }
        return C1808a.m4625c(C1798e.m4514F(C1808a.m4625c(i6, 255), c1120a.f5335b, f7), Color.alpha(i6));
    }

    /* JADX WARN: Code restructure failed: missing block: B:32:0x00ec, code lost:
    
        if (((r2.f7378a.m4599d(m4580g()) || r12.f7366p.isConvex() || android.os.Build.VERSION.SDK_INT >= 29) ? false : true) != false) goto L39;
     */
    /* JADX WARN: Removed duplicated region for block: B:36:0x00f5  */
    /* JADX WARN: Removed duplicated region for block: B:52:0x01a3  */
    /* JADX WARN: Removed duplicated region for block: B:55:0x01b8  */
    @Override // android.graphics.drawable.Drawable
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void draw(android.graphics.Canvas r13) {
        /*
            Method dump skipped, instructions count: 474
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p153w3.C1799f.draw(android.graphics.Canvas):void");
    }

    /* renamed from: e */
    public final void m4578e(Canvas canvas) {
        if (this.f7363m.cardinality() > 0) {
            Log.w(f7353F, "Compatibility shadow requested but can't be drawn for all operations in this shape.");
        }
        if (this.f7360j.f7395r != 0) {
            canvas.drawPath(this.f7366p, this.f7375y.f6720a);
        }
        for (int i6 = 0; i6 < 4; i6++) {
            C1804k.g gVar = this.f7361k[i6];
            C1460a c1460a = this.f7375y;
            int i7 = this.f7360j.f7394q;
            Matrix matrix = C1804k.g.f7461a;
            gVar.mo4615a(matrix, c1460a, i7, canvas);
            this.f7362l[i6].mo4615a(matrix, this.f7375y, this.f7360j.f7394q, canvas);
        }
        if (this.f7359E) {
            b bVar = this.f7360j;
            int sin = (int) (Math.sin(Math.toRadians(bVar.f7396s)) * bVar.f7395r);
            int m4582i = m4582i();
            canvas.translate(-sin, -m4582i);
            canvas.drawPath(this.f7366p, f7354G);
            canvas.translate(sin, m4582i);
        }
    }

    /* renamed from: f */
    public final void m4579f(Canvas canvas, Paint paint, Path path, C1802i c1802i, RectF rectF) {
        if (!c1802i.m4599d(rectF)) {
            canvas.drawPath(path, paint);
        } else {
            float mo4509a = c1802i.f7405f.mo4509a(rectF) * this.f7360j.f7387j;
            canvas.drawRoundRect(rectF, mo4509a, mo4509a, paint);
        }
    }

    /* renamed from: g */
    public final RectF m4580g() {
        this.f7368r.set(getBounds());
        return this.f7368r;
    }

    @Override // android.graphics.drawable.Drawable
    public final Drawable.ConstantState getConstantState() {
        return this.f7360j;
    }

    @Override // android.graphics.drawable.Drawable
    public int getOpacity() {
        return -3;
    }

    @Override // android.graphics.drawable.Drawable
    @TargetApi(21)
    public void getOutline(Outline outline) {
        b bVar = this.f7360j;
        if (bVar.f7393p == 2) {
            return;
        }
        if (bVar.f7378a.m4599d(m4580g())) {
            outline.setRoundRect(getBounds(), m4583j() * this.f7360j.f7387j);
            return;
        }
        m4575b(m4580g(), this.f7366p);
        if (this.f7366p.isConvex() || Build.VERSION.SDK_INT >= 29) {
            try {
                outline.setConvexPath(this.f7366p);
            } catch (IllegalArgumentException unused) {
            }
        }
    }

    @Override // android.graphics.drawable.Drawable
    public final boolean getPadding(Rect rect) {
        Rect rect2 = this.f7360j.f7385h;
        if (rect2 == null) {
            return super.getPadding(rect);
        }
        rect.set(rect2);
        return true;
    }

    @Override // android.graphics.drawable.Drawable
    public final Region getTransparentRegion() {
        this.f7370t.set(getBounds());
        m4575b(m4580g(), this.f7366p);
        this.f7371u.setPath(this.f7366p, this.f7370t);
        this.f7370t.op(this.f7371u, Region.Op.DIFFERENCE);
        return this.f7370t;
    }

    /* renamed from: h */
    public final RectF m4581h() {
        this.f7369s.set(m4580g());
        float strokeWidth = m4584k() ? this.f7374x.getStrokeWidth() / 2.0f : 0.0f;
        this.f7369s.inset(strokeWidth, strokeWidth);
        return this.f7369s;
    }

    /* renamed from: i */
    public final int m4582i() {
        b bVar = this.f7360j;
        return (int) (Math.cos(Math.toRadians(bVar.f7396s)) * bVar.f7395r);
    }

    @Override // android.graphics.drawable.Drawable
    public final void invalidateSelf() {
        this.f7364n = true;
        super.invalidateSelf();
    }

    @Override // android.graphics.drawable.Drawable
    public boolean isStateful() {
        ColorStateList colorStateList;
        ColorStateList colorStateList2;
        ColorStateList colorStateList3;
        ColorStateList colorStateList4;
        return super.isStateful() || ((colorStateList = this.f7360j.f7383f) != null && colorStateList.isStateful()) || (((colorStateList2 = this.f7360j.f7382e) != null && colorStateList2.isStateful()) || (((colorStateList3 = this.f7360j.f7381d) != null && colorStateList3.isStateful()) || ((colorStateList4 = this.f7360j.f7380c) != null && colorStateList4.isStateful())));
    }

    /* renamed from: j */
    public final float m4583j() {
        return this.f7360j.f7378a.f7404e.mo4509a(m4580g());
    }

    /* renamed from: k */
    public final boolean m4584k() {
        Paint.Style style = this.f7360j.f7398u;
        return (style == Paint.Style.FILL_AND_STROKE || style == Paint.Style.STROKE) && this.f7374x.getStrokeWidth() > 0.0f;
    }

    /* renamed from: l */
    public final void m4585l(Context context) {
        this.f7360j.f7379b = new C1120a(context);
        m4595v();
    }

    /* renamed from: m */
    public final void m4586m(float f6) {
        b bVar = this.f7360j;
        if (bVar.f7391n != f6) {
            bVar.f7391n = f6;
            m4595v();
        }
    }

    @Override // android.graphics.drawable.Drawable
    public final Drawable mutate() {
        this.f7360j = new b(this.f7360j);
        return this;
    }

    /* renamed from: n */
    public final void m4587n(ColorStateList colorStateList) {
        b bVar = this.f7360j;
        if (bVar.f7380c != colorStateList) {
            bVar.f7380c = colorStateList;
            onStateChange(getState());
        }
    }

    /* renamed from: o */
    public final void m4588o(float f6) {
        b bVar = this.f7360j;
        if (bVar.f7387j != f6) {
            bVar.f7387j = f6;
            this.f7364n = true;
            invalidateSelf();
        }
    }

    @Override // android.graphics.drawable.Drawable
    public final void onBoundsChange(Rect rect) {
        this.f7364n = true;
        super.onBoundsChange(rect);
    }

    @Override // android.graphics.drawable.Drawable, p114q3.C1288i.b
    public boolean onStateChange(int[] iArr) {
        boolean z5 = m4593t(iArr) || m4594u();
        if (z5) {
            invalidateSelf();
        }
        return z5;
    }

    /* renamed from: p */
    public final void m4589p(float f6, int i6) {
        m4592s(f6);
        m4591r(ColorStateList.valueOf(i6));
    }

    /* renamed from: q */
    public final void m4590q(float f6, ColorStateList colorStateList) {
        m4592s(f6);
        m4591r(colorStateList);
    }

    /* renamed from: r */
    public final void m4591r(ColorStateList colorStateList) {
        b bVar = this.f7360j;
        if (bVar.f7381d != colorStateList) {
            bVar.f7381d = colorStateList;
            onStateChange(getState());
        }
    }

    /* renamed from: s */
    public final void m4592s(float f6) {
        this.f7360j.f7388k = f6;
        invalidateSelf();
    }

    @Override // android.graphics.drawable.Drawable
    public void setAlpha(int i6) {
        b bVar = this.f7360j;
        if (bVar.f7389l != i6) {
            bVar.f7389l = i6;
            super.invalidateSelf();
        }
    }

    @Override // android.graphics.drawable.Drawable
    public void setColorFilter(ColorFilter colorFilter) {
        Objects.requireNonNull(this.f7360j);
        super.invalidateSelf();
    }

    @Override // p153w3.InterfaceC1805l
    public final void setShapeAppearanceModel(C1802i c1802i) {
        this.f7360j.f7378a = c1802i;
        invalidateSelf();
    }

    @Override // android.graphics.drawable.Drawable
    public final void setTint(int i6) {
        setTintList(ColorStateList.valueOf(i6));
    }

    @Override // android.graphics.drawable.Drawable
    public void setTintList(ColorStateList colorStateList) {
        this.f7360j.f7383f = colorStateList;
        m4594u();
        super.invalidateSelf();
    }

    @Override // android.graphics.drawable.Drawable
    public void setTintMode(PorterDuff.Mode mode) {
        b bVar = this.f7360j;
        if (bVar.f7384g != mode) {
            bVar.f7384g = mode;
            m4594u();
            super.invalidateSelf();
        }
    }

    /* renamed from: t */
    public final boolean m4593t(int[] iArr) {
        boolean z5;
        int color;
        int colorForState;
        int color2;
        int colorForState2;
        if (this.f7360j.f7380c == null || color2 == (colorForState2 = this.f7360j.f7380c.getColorForState(iArr, (color2 = this.f7373w.getColor())))) {
            z5 = false;
        } else {
            this.f7373w.setColor(colorForState2);
            z5 = true;
        }
        if (this.f7360j.f7381d == null || color == (colorForState = this.f7360j.f7381d.getColorForState(iArr, (color = this.f7374x.getColor())))) {
            return z5;
        }
        this.f7374x.setColor(colorForState);
        return true;
    }

    /* renamed from: u */
    public final boolean m4594u() {
        PorterDuffColorFilter porterDuffColorFilter = this.f7356B;
        PorterDuffColorFilter porterDuffColorFilter2 = this.f7357C;
        b bVar = this.f7360j;
        this.f7356B = m4576c(bVar.f7383f, bVar.f7384g, this.f7373w, true);
        b bVar2 = this.f7360j;
        this.f7357C = m4576c(bVar2.f7382e, bVar2.f7384g, this.f7374x, false);
        b bVar3 = this.f7360j;
        if (bVar3.f7397t) {
            this.f7375y.m3564a(bVar3.f7383f.getColorForState(getState(), 0));
        }
        return (Objects.equals(porterDuffColorFilter, this.f7356B) && Objects.equals(porterDuffColorFilter2, this.f7357C)) ? false : true;
    }

    /* renamed from: v */
    public final void m4595v() {
        b bVar = this.f7360j;
        float f6 = bVar.f7391n + bVar.f7392o;
        bVar.f7394q = (int) Math.ceil(0.75f * f6);
        this.f7360j.f7395r = (int) Math.ceil(f6 * 0.25f);
        m4594u();
        super.invalidateSelf();
    }
}
