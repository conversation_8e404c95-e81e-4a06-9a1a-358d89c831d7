package p164y1;

/* renamed from: y1.d */
/* loaded from: classes.dex */
public abstract class AbstractC2066d {

    /* renamed from: y1.d$a */
    public static class a extends AbstractC2066d {

        /* renamed from: a */
        public volatile boolean f8321a;

        /* renamed from: a */
        public final void m5177a() {
            if (this.f8321a) {
                throw new IllegalStateException("Already released");
            }
        }
    }
}
