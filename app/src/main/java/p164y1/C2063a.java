package p164y1;

import android.util.Log;
import androidx.activity.result.C0052a;
import p022d0.C0700e;
import p022d0.InterfaceC0698c;
import p164y1.AbstractC2066d;

/* renamed from: y1.a */
/* loaded from: classes.dex */
public final class C2063a {

    /* renamed from: a */
    public static final e<Object> f8317a = new a();

    /* renamed from: y1.a$a */
    public class a implements e<Object> {
        @Override // p164y1.C2063a.e
        /* renamed from: a */
        public final void mo5176a(Object obj) {
        }
    }

    /* renamed from: y1.a$b */
    public interface b<T> {
        /* renamed from: a */
        T mo2099a();
    }

    /* renamed from: y1.a$c */
    public static final class c<T> implements InterfaceC0698c<T> {

        /* renamed from: a */
        public final b<T> f8318a;

        /* renamed from: b */
        public final e<T> f8319b;

        /* renamed from: c */
        public final InterfaceC0698c<T> f8320c;

        public c(InterfaceC0698c<T> interfaceC0698c, b<T> bVar, e<T> eVar) {
            this.f8320c = interfaceC0698c;
            this.f8318a = bVar;
            this.f8319b = eVar;
        }

        @Override // p022d0.InterfaceC0698c
        /* renamed from: a */
        public final boolean mo2055a(T t) {
            if (t instanceof d) {
                ((AbstractC2066d.a) ((d) t).mo2072f()).f8321a = true;
            }
            this.f8319b.mo5176a(t);
            return this.f8320c.mo2055a(t);
        }

        @Override // p022d0.InterfaceC0698c
        /* renamed from: b */
        public final T mo2056b() {
            T mo2056b = this.f8320c.mo2056b();
            if (mo2056b == null) {
                mo2056b = this.f8318a.mo2099a();
                if (Log.isLoggable("FactoryPools", 2)) {
                    StringBuilder m104h = C0052a.m104h("Created new ");
                    m104h.append(mo2056b.getClass());
                    Log.v("FactoryPools", m104h.toString());
                }
            }
            if (mo2056b instanceof d) {
                ((AbstractC2066d.a) mo2056b.mo2072f()).f8321a = false;
            }
            return (T) mo2056b;
        }
    }

    /* renamed from: y1.a$d */
    public interface d {
        /* renamed from: f */
        AbstractC2066d mo2072f();
    }

    /* renamed from: y1.a$e */
    public interface e<T> {
        /* renamed from: a */
        void mo5176a(T t);
    }

    /* renamed from: a */
    public static <T extends d> InterfaceC0698c<T> m5175a(int i6, b<T> bVar) {
        return new c(new C0700e(i6), bVar, f8317a);
    }
}
