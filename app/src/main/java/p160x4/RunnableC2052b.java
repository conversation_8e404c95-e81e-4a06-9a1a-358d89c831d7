package p160x4;

import android.content.pm.PackageManager;
import android.media.AudioRecord;
import android.util.Log;
import androidx.activity.result.C0052a;
import com.google.gson.Gson;
import com.liaoyuan.aicast.R;
import com.liaoyuan.aicast.phone.widget.BrowserLayout;
import com.liaoyuan.aicast.phone.widget.BrowserNaivLayout;
import com.liaoyuan.aicast.phone.widget.DisconnectLayout;
import com.liaoyuan.aicast.phone.widget.TipBrowserLayout;
import com.liaoyuan.aicast.phone.widget.TipLocalBrowserLayout;
import com.liaoyuan.aicast.phone.widget.ToastBrowserLayout;
import com.p020ly.appupdate.download.VersionInfo;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Iterator;
import java.util.Objects;
import p006a5.PresentationC0040c;
import p031e2.RunnableC0801q;
import p048g5.C0891c;
import p048g5.InterfaceC0892d;
import p048g5.RunnableC0890b;
import p062i5.C0952a;
import p069j5.C0977d;
import p076k5.C1043a;
import p083l5.AbstractC1072e;
import p083l5.InterfaceC1073f;

/* renamed from: x4.b */
/* loaded from: classes.dex */
public final /* synthetic */ class RunnableC2052b implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f8299j;

    /* renamed from: k */
    public final /* synthetic */ Object f8300k;

    public /* synthetic */ RunnableC2052b(Object obj, int i6) {
        this.f8299j = i6;
        this.f8300k = obj;
    }

    /* JADX WARN: Type inference failed for: r0v16, types: [java.util.ArrayList, java.util.List<V>] */
    @Override // java.lang.Runnable
    public final void run() {
        String str;
        VersionInfo versionInfo = null;
        switch (this.f8299j) {
            case 0:
                C2053c c2053c = (C2053c) this.f8300k;
                Objects.requireNonNull(c2053c);
                try {
                    if (c2053c.f8301a == null) {
                        c2053c.f8301a = c2053c.m5164b();
                    }
                    AudioRecord audioRecord = c2053c.f8301a;
                    if (audioRecord != null) {
                        audioRecord.startRecording();
                        c2053c.f8302b.set(true);
                        c2053c.m5165c();
                        c2053c.m5166d();
                        break;
                    }
                } catch (IllegalStateException e6) {
                    e6.printStackTrace();
                    Log.e("AudioRecorder", "IllegalStateException e:" + e6);
                    return;
                }
                break;
            case 1:
                PresentationC0040c presentationC0040c = (PresentationC0040c) this.f8300k;
                int i6 = PresentationC0040c.f167v;
                C0952a.m2489b(presentationC0040c.getContext(), R.layout.cast_presentation, presentationC0040c.getWindow().getDecorView());
                BrowserLayout browserLayout = presentationC0040c.f170l;
                Objects.requireNonNull(browserLayout);
                Log.d("BrowserLayout", "configChanged");
                browserLayout.f3502z = null;
                browserLayout.m1913d();
                C0952a.m2489b(browserLayout.getContext(), R.layout.include_browser_navi, browserLayout.getRootView());
                BrowserNaivLayout browserNaivLayout = browserLayout.f3491o;
                C0952a.m2489b(browserNaivLayout.getContext(), R.layout.layout_browser_navi, browserNaivLayout.getRootView());
                TipLocalBrowserLayout tipLocalBrowserLayout = presentationC0040c.f171m;
                C0952a.m2489b(tipLocalBrowserLayout.getContext(), R.layout.layout_browser_local_tip, tipLocalBrowserLayout.getRootView());
                ToastBrowserLayout toastBrowserLayout = presentationC0040c.f172n;
                C0952a.m2489b(toastBrowserLayout.getContext(), R.layout.layout_browser_toast, toastBrowserLayout.getRootView());
                TipBrowserLayout tipBrowserLayout = presentationC0040c.f173o;
                C0952a.m2489b(tipBrowserLayout.getContext(), R.layout.layout_browser_tip, tipBrowserLayout.getRootView());
                DisconnectLayout disconnectLayout = presentationC0040c.f174p;
                Objects.requireNonNull(disconnectLayout);
                disconnectLayout.post(new RunnableC0801q(disconnectLayout, 3));
                break;
            case 2:
                ((BrowserLayout) this.f8300k).setShowBrowserNavi(false);
                break;
            case 3:
                Iterator it = C0891c.this.f8461b.iterator();
                while (it.hasNext()) {
                    ((InterfaceC0892d) it.next()).mo1928b();
                }
                break;
            case 4:
                C0977d c0977d = (C0977d) this.f8300k;
                Objects.requireNonNull(c0977d);
                try {
                    str = c0977d.f4786d.getPackageManager().getPackageInfo(c0977d.f4786d.getPackageName(), 0).versionName;
                    Log.d("AppInfo", "Version Name: " + str);
                } catch (PackageManager.NameNotFoundException e7) {
                    e7.printStackTrace();
                    str = "0";
                }
                c0977d.f4787e = new VersionInfo(str);
                String str2 = c0977d.f4789g;
                File file = new File(str2);
                boolean exists = file.exists();
                Log.d("ParserVersion", "parserNewestVersion file:" + file + ",exists:" + exists);
                if (exists) {
                    try {
                        FileInputStream fileInputStream = new FileInputStream(str2);
                        byte[] bArr = new byte[fileInputStream.available()];
                        fileInputStream.read(bArr);
                        fileInputStream.close();
                        String str3 = new String(bArr, "UTF-8");
                        Log.d("ParserVersion", "parserJsonFile jsonString:" + str3);
                        if (c0977d.f4784b == null) {
                            c0977d.f4784b = new Gson();
                        }
                        VersionInfo versionInfo2 = (VersionInfo) c0977d.f4784b.fromJson(str3, VersionInfo.class);
                        Log.d("ParserVersion", "VersionInfo version:" + versionInfo2.toString());
                        fileInputStream.close();
                        versionInfo = versionInfo2;
                    } catch (IOException e8) {
                        e8.printStackTrace();
                    }
                }
                c0977d.f4788f = versionInfo;
                StringBuilder m104h = C0052a.m104h("mCurrentVersionInfo:");
                m104h.append(c0977d.f4787e);
                m104h.append(",mNewestVersionInfo:");
                m104h.append(c0977d.f4788f);
                Log.d("ParserVersion", m104h.toString());
                C0977d.a aVar = c0977d.f4785c;
                if (aVar != null) {
                    C1043a.c cVar = (C1043a.c) aVar;
                    C1043a.this.f4993c.post(new RunnableC0890b(cVar, c0977d.f4787e, c0977d.f4788f, 1));
                    break;
                }
                break;
            default:
                InterfaceC1073f interfaceC1073f = ((AbstractC1072e) this.f8300k).f5112a;
                if (interfaceC1073f != null) {
                    interfaceC1073f.mo2725d();
                    break;
                }
                break;
        }
    }
}
