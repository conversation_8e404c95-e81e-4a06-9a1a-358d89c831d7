package p160x4;

import android.content.pm.PackageManager;
import android.media.AudioRecord;
import android.util.Log;
import androidx.activity.result.C0052a;
import com.google.gson.Gson;
import com.liaoyuan.aicast.R;
import com.liaoyuan.aicast.phone.widget.BrowserLayout;
import com.liaoyuan.aicast.phone.widget.BrowserNaivLayout;
import com.liaoyuan.aicast.phone.widget.DisconnectLayout;
import com.liaoyuan.aicast.phone.widget.TipBrowserLayout;
import com.liaoyuan.aicast.phone.widget.TipLocalBrowserLayout;
import com.liaoyuan.aicast.phone.widget.ToastBrowserLayout;
import com.p020ly.appupdate.download.VersionInfo;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Iterator;
import java.util.Objects;
import p006a5.PresentationC0040c;
import p031e2.RunnableC0801q;
import p048g5.C0891c;
import p048g5.InterfaceC0892d;
import p048g5.RunnableC0890b;
import p062i5.C0952a;
import p069j5.C0977d;
import p076k5.C1043a;
import p083l5.AbstractC1072e;
import p083l5.InterfaceC1073f;

/**
 * 多功能任务执行器
 * 根据不同的任务类型执行相应的操作，包括：
 * - 音频录制控制
 * - 投屏界面配置更新
 * - 浏览器导航栏自动隐藏
 * - 连接状态通知
 * - 版本信息解析
 * - 接口回调执行
 */
public class MultiPurposeTaskRunner implements Runnable {

    // 任务类型常量
    public static final int TASK_AUDIO_RECORDING = 0;
    public static final int TASK_PRESENTATION_CONFIG = 1;
    public static final int TASK_BROWSER_AUTO_HIDE = 2;
    public static final int TASK_CONNECTION_NOTIFY = 3;
    public static final int TASK_VERSION_PARSING = 4;
    public static final int TASK_INTERFACE_CALLBACK = 5;

    // 任务类型
    private final int taskType;

    // 目标对象
    private final Object targetObject;

    /**
     * 构造函数
     * @param targetObject 目标对象
     * @param taskType 任务类型
     */
    public MultiPurposeTaskRunner(Object targetObject, int taskType) {
        this.taskType = taskType;
        this.targetObject = targetObject;
    }

    @Override
    public void run() {
        switch (taskType) {
            case TASK_AUDIO_RECORDING:
                handleAudioRecordingTask();
                break;
            case TASK_PRESENTATION_CONFIG:
                handlePresentationConfigTask();
                break;
            case TASK_BROWSER_AUTO_HIDE:
                handleBrowserAutoHideTask();
                break;
            case TASK_CONNECTION_NOTIFY:
                handleConnectionNotifyTask();
                break;
            case TASK_VERSION_PARSING:
                handleVersionParsingTask();
                break;
            case TASK_INTERFACE_CALLBACK:
            default:
                handleInterfaceCallbackTask();
                break;
        }
    }

    /**
     * 处理音频录制任务
     */
    private void handleAudioRecordingTask() {
        C2053c audioRecorder = (C2053c) targetObject;
        Objects.requireNonNull(audioRecorder);

        try {
            // 初始化音频录制器
            if (audioRecorder.f8301a == null) {
                audioRecorder.f8301a = audioRecorder.m5164b();
            }

            AudioRecord audioRecord = audioRecorder.f8301a;
            if (audioRecord != null) {
                // 开始录制
                audioRecord.startRecording();
                audioRecorder.f8302b.set(true);

                // 执行录制相关操作
                audioRecorder.m5165c();
                audioRecorder.m5166d();

                Log.d("MultiPurposeTaskRunner", "Audio recording started successfully");
            } else {
                Log.w("MultiPurposeTaskRunner", "AudioRecord is null, cannot start recording");
            }
        } catch (IllegalStateException e) {
            e.printStackTrace();
            Log.e("MultiPurposeTaskRunner", "Failed to start audio recording: " + e.getMessage());
        }
    }
    /**
     * 处理投屏界面配置任务
     */
    private void handlePresentationConfigTask() {
        PresentationC0040c presentation = (PresentationC0040c) targetObject;

        try {
            // 更新投屏界面布局
            C0952a.m2489b(presentation.getContext(), R.layout.cast_presentation,
                presentation.getWindow().getDecorView());

            // 更新浏览器布局
            BrowserLayout browserLayout = presentation.f170l;
            if (browserLayout != null) {
                Log.d("MultiPurposeTaskRunner", "Updating browser layout configuration");

                // 重置适配器并刷新收藏夹
                browserLayout.f3502z = null;
                browserLayout.m1913d();

                // 更新浏览器导航布局
                C0952a.m2489b(browserLayout.getContext(), R.layout.include_browser_navi,
                    browserLayout.getRootView());

                BrowserNaivLayout browserNaviLayout = browserLayout.f3491o;
                C0952a.m2489b(browserNaviLayout.getContext(), R.layout.layout_browser_navi,
                    browserNaviLayout.getRootView());
            }

            // 更新各种提示布局
            updateTipLayouts(presentation);

            // 更新断开连接布局
            DisconnectLayout disconnectLayout = presentation.f174p;
            if (disconnectLayout != null) {
                disconnectLayout.post(new RunnableC0801q(disconnectLayout, 3));
            }

            Log.d("MultiPurposeTaskRunner", "Presentation configuration updated successfully");
        } catch (Exception e) {
            Log.e("MultiPurposeTaskRunner", "Failed to update presentation configuration", e);
        }
    }

    /**
     * 更新提示布局
     */
    private void updateTipLayouts(PresentationC0040c presentation) {
        // 更新本地浏览器提示布局
        TipLocalBrowserLayout tipLocalBrowserLayout = presentation.f171m;
        C0952a.m2489b(tipLocalBrowserLayout.getContext(), R.layout.layout_browser_local_tip,
            tipLocalBrowserLayout.getRootView());

        // 更新Toast浏览器布局
        ToastBrowserLayout toastBrowserLayout = presentation.f172n;
        C0952a.m2489b(toastBrowserLayout.getContext(), R.layout.layout_browser_toast,
            toastBrowserLayout.getRootView());

        // 更新浏览器提示布局
        TipBrowserLayout tipBrowserLayout = presentation.f173o;
        C0952a.m2489b(tipBrowserLayout.getContext(), R.layout.layout_browser_tip,
            tipBrowserLayout.getRootView());
    }

    /**
     * 处理浏览器自动隐藏任务
     */
    private void handleBrowserAutoHideTask() {
        BrowserLayout browserLayout = (BrowserLayout) targetObject;
        browserLayout.setShowBrowserNavi(false);
        Log.d("MultiPurposeTaskRunner", "Browser navigation auto-hidden");
    }
    /**
     * 处理连接状态通知任务
     */
    private void handleConnectionNotifyTask() {
        try {
            C0891c connectionManager = (C0891c) targetObject;
            Iterator<InterfaceC0892d> iterator = connectionManager.f8461b.iterator();

            while (iterator.hasNext()) {
                InterfaceC0892d listener = iterator.next();
                listener.mo1928b();
            }

            Log.d("MultiPurposeTaskRunner", "Connection status notifications sent");
        } catch (Exception e) {
            Log.e("MultiPurposeTaskRunner", "Failed to send connection notifications", e);
        }
    }
    /**
     * 处理版本信息解析任务
     */
    private void handleVersionParsingTask() {
        C0977d versionParser = (C0977d) targetObject;
        Objects.requireNonNull(versionParser);

        try {
            // 获取当前应用版本信息
            String currentVersionName = getCurrentVersionName(versionParser);
            versionParser.f4787e = new VersionInfo(currentVersionName);

            // 解析最新版本信息
            VersionInfo newestVersionInfo = parseNewestVersionInfo(versionParser);
            versionParser.f4788f = newestVersionInfo;

            // 记录版本信息
            logVersionInfo(versionParser.f4787e, versionParser.f4788f);

            // 通知版本检查结果
            notifyVersionCheckResult(versionParser);

        } catch (Exception e) {
            Log.e("MultiPurposeTaskRunner", "Failed to parse version information", e);
        }
    }

    /**
     * 获取当前应用版本名称
     */
    private String getCurrentVersionName(C0977d versionParser) {
        try {
            String versionName = versionParser.f4786d.getPackageManager()
                .getPackageInfo(versionParser.f4786d.getPackageName(), 0).versionName;
            Log.d("MultiPurposeTaskRunner", "Current version name: " + versionName);
            return versionName;
        } catch (PackageManager.NameNotFoundException e) {
            Log.e("MultiPurposeTaskRunner", "Failed to get current version name", e);
            return "0";
        }
    }

    /**
     * 解析最新版本信息
     */
    private VersionInfo parseNewestVersionInfo(C0977d versionParser) {
        String versionFilePath = versionParser.f4789g;
        File versionFile = new File(versionFilePath);
        boolean fileExists = versionFile.exists();

        Log.d("MultiPurposeTaskRunner", "Version file: " + versionFile + ", exists: " + fileExists);

        if (!fileExists) {
            return null;
        }

        try {
            // 读取版本文件内容
            String jsonContent = readVersionFile(versionFilePath);
            Log.d("MultiPurposeTaskRunner", "Version file content: " + jsonContent);

            // 解析JSON内容
            if (versionParser.f4784b == null) {
                versionParser.f4784b = new Gson();
            }

            VersionInfo versionInfo = versionParser.f4784b.fromJson(jsonContent, VersionInfo.class);
            Log.d("MultiPurposeTaskRunner", "Parsed version info: " + versionInfo.toString());

            return versionInfo;
        } catch (IOException e) {
            Log.e("MultiPurposeTaskRunner", "Failed to parse version file", e);
            return null;
        }
    }

    /**
     * 读取版本文件内容
     */
    private String readVersionFile(String filePath) throws IOException {
        FileInputStream fileInputStream = new FileInputStream(filePath);
        try {
            byte[] buffer = new byte[fileInputStream.available()];
            fileInputStream.read(buffer);
            return new String(buffer, "UTF-8");
        } finally {
            fileInputStream.close();
        }
    }

    /**
     * 记录版本信息
     */
    private void logVersionInfo(VersionInfo currentVersion, VersionInfo newestVersion) {
        StringBuilder logBuilder = new StringBuilder();
        logBuilder.append("Current version: ").append(currentVersion);
        logBuilder.append(", Newest version: ").append(newestVersion);
        Log.d("MultiPurposeTaskRunner", logBuilder.toString());
    }

    /**
     * 通知版本检查结果
     */
    private void notifyVersionCheckResult(C0977d versionParser) {
        C0977d.a callback = versionParser.f4785c;
        if (callback != null) {
            C1043a.c callbackWrapper = (C1043a.c) callback;
            C1043a.this.f4993c.post(new RunnableC0890b(callbackWrapper,
                versionParser.f4787e, versionParser.f4788f, 1));
        }
    }
    /**
     * 处理接口回调任务
     */
    private void handleInterfaceCallbackTask() {
        try {
            AbstractC1072e callbackObject = (AbstractC1072e) targetObject;
            InterfaceC1073f callback = callbackObject.f5112a;

            if (callback != null) {
                callback.mo2725d();
                Log.d("MultiPurposeTaskRunner", "Interface callback executed successfully");
            } else {
                Log.w("MultiPurposeTaskRunner", "Interface callback is null");
            }
        } catch (Exception e) {
            Log.e("MultiPurposeTaskRunner", "Failed to execute interface callback", e);
        }
    }

    /**
     * 获取任务类型描述
     * @param taskType 任务类型
     * @return 任务描述
     */
    public static String getTaskTypeDescription(int taskType) {
        switch (taskType) {
            case TASK_AUDIO_RECORDING:
                return "Audio Recording Task";
            case TASK_PRESENTATION_CONFIG:
                return "Presentation Configuration Task";
            case TASK_BROWSER_AUTO_HIDE:
                return "Browser Auto Hide Task";
            case TASK_CONNECTION_NOTIFY:
                return "Connection Notification Task";
            case TASK_VERSION_PARSING:
                return "Version Parsing Task";
            case TASK_INTERFACE_CALLBACK:
                return "Interface Callback Task";
            default:
                return "Unknown Task";
        }
    }

    /**
     * 创建音频录制任务
     */
    public static MultiPurposeTaskRunner createAudioRecordingTask(Object audioRecorder) {
        return new MultiPurposeTaskRunner(audioRecorder, TASK_AUDIO_RECORDING);
    }

    /**
     * 创建投屏配置任务
     */
    public static MultiPurposeTaskRunner createPresentationConfigTask(Object presentation) {
        return new MultiPurposeTaskRunner(presentation, TASK_PRESENTATION_CONFIG);
    }

    /**
     * 创建浏览器自动隐藏任务
     */
    public static MultiPurposeTaskRunner createBrowserAutoHideTask(Object browserLayout) {
        return new MultiPurposeTaskRunner(browserLayout, TASK_BROWSER_AUTO_HIDE);
    }

    /**
     * 创建连接通知任务
     */
    public static MultiPurposeTaskRunner createConnectionNotifyTask(Object connectionManager) {
        return new MultiPurposeTaskRunner(connectionManager, TASK_CONNECTION_NOTIFY);
    }

    /**
     * 创建版本解析任务
     */
    public static MultiPurposeTaskRunner createVersionParsingTask(Object versionParser) {
        return new MultiPurposeTaskRunner(versionParser, TASK_VERSION_PARSING);
    }

    /**
     * 创建接口回调任务
     */
    public static MultiPurposeTaskRunner createInterfaceCallbackTask(Object callbackObject) {
        return new MultiPurposeTaskRunner(callbackObject, TASK_INTERFACE_CALLBACK);
    }
}
