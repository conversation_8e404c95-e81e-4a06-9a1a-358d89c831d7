package p160x4;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.media.AudioManager;
import android.util.Log;
import java.util.Objects;
import p031e2.C0800p;
import p173z4.C2123j;

/* renamed from: x4.a */
/* loaded from: classes.dex */
public final class C2051a {

    /* renamed from: a */
    public AudioManager f8295a;

    /* renamed from: b */
    public c f8296b;

    /* renamed from: c */
    public b f8297c = new b();

    /* renamed from: x4.a$a */
    public class a extends BroadcastReceiver {
        public a() {
        }

        @Override // android.content.BroadcastReceiver
        public final void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (action != null && action.equals("android.media.VOLUME_CHANGED_ACTION") && intent.getIntExtra("android.media.EXTRA_VOLUME_STREAM_TYPE", -1) == 3) {
                int streamVolume = C2051a.this.f8295a.getStreamVolume(3);
                int streamMaxVolume = C2051a.this.f8295a.getStreamMaxVolume(3);
                Log.d("AudioFocus", "Media volume changed to: " + streamVolume + "/" + streamMaxVolume);
                c cVar = C2051a.this.f8296b;
                if (cVar != null) {
                    C2123j c2123j = (C2123j) ((C0800p) cVar).f4181b;
                    Objects.requireNonNull(c2123j);
                    Log.d("ProjectionPresenter", "onVolumeChanged newVolume:" + streamVolume + ",maxVolume:" + streamMaxVolume);
                    c2123j.m5313m();
                }
            }
        }
    }

    /* renamed from: x4.a$b */
    public class b implements AudioManager.OnAudioFocusChangeListener {
        @Override // android.media.AudioManager.OnAudioFocusChangeListener
        public final void onAudioFocusChange(int i6) {
        }
    }

    /* renamed from: x4.a$c */
    public interface c {
    }

    public C2051a(Context context) {
        this.f8295a = (AudioManager) context.getSystemService("audio");
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction("android.media.VOLUME_CHANGED_ACTION");
        context.registerReceiver(new a(), intentFilter);
    }

    /* renamed from: a */
    public final boolean m5161a() {
        return this.f8295a.getStreamVolume(3) == 0;
    }

    /* renamed from: b */
    public final void m5162b(boolean z5, int i6) {
        this.f8295a.adjustStreamVolume(3, z5 ? -100 : 100, i6);
    }
}
