package p160x4;

import android.annotation.SuppressLint;
import android.content.Context;
import android.media.AudioFormat;
import android.media.AudioPlaybackCaptureConfiguration;
import android.media.AudioRecord;
import android.media.projection.MediaProjection;
import android.os.Build;
import android.os.Process;
import android.util.Log;
import androidx.activity.result.C0052a;
import androidx.appcompat.widget.C0174y;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;
import p033e4.RunnableC0813g;
import p088m4.C1085a;
import p154w4.C1806a;
import p166y4.C2070c;

/* renamed from: x4.c */
/* loaded from: classes.dex */
public final class C2053c {

    /* renamed from: a */
    public AudioRecord f8301a;

    /* renamed from: c */
    public InterfaceC2054d f8303c;

    /* renamed from: d */
    public Context f8304d;

    /* renamed from: e */
    public boolean f8305e;

    /* renamed from: f */
    public C2070c f8306f;

    /* renamed from: g */
    public int f8307g;

    /* renamed from: h */
    public ExecutorService f8308h;

    /* renamed from: i */
    public AudioPlaybackCaptureConfiguration f8309i;

    /* renamed from: b */
    public AtomicBoolean f8302b = new AtomicBoolean(false);

    /* renamed from: j */
    public int f8310j = 0;

    /* renamed from: k */
    public int f8311k = 1000;

    public C2053c(Context context) {
        StringBuilder m104h = C0052a.m104h("AudioRecorder VERSION_CODES:");
        m104h.append(Build.VERSION.SDK_INT);
        Log.d("AudioRecorder", m104h.toString());
        this.f8304d = context;
        this.f8305e = false;
        C2070c c2070c = this.f8306f;
        if (c2070c != null) {
            c2070c.m5181a();
            this.f8306f = null;
        }
    }

    /* renamed from: a */
    public final void m5163a(MediaProjection mediaProjection) {
        if (Build.VERSION.SDK_INT >= 29) {
            this.f8309i = new AudioPlaybackCaptureConfiguration.Builder(mediaProjection).addMatchingUsage(1).addMatchingUsage(14).build();
        }
    }

    @SuppressLint({"MissingPermission"})
    /* renamed from: b */
    public final AudioRecord m5164b() {
        this.f8307g = AudioRecord.getMinBufferSize(48000, 12, 2);
        try {
            if (Build.VERSION.SDK_INT < 29) {
                return null;
            }
            AudioFormat build = new AudioFormat.Builder().setEncoding(2).setSampleRate(48000).setChannelMask(12).build();
            AudioRecord.Builder builder = new AudioRecord.Builder();
            builder.setAudioFormat(build).setBufferSizeInBytes(this.f8307g * 2).setAudioPlaybackCaptureConfig(this.f8309i);
            return builder.build();
        } catch (Exception e6) {
            e6.printStackTrace();
            return null;
        }
    }

    /* renamed from: c */
    public final void m5165c() {
        C2070c c2070c;
        C1085a c1085a;
        boolean z5;
        Log.d("AudioRecorder", "readMicData start");
        int i6 = this.f8307g;
        byte[] bArr = new byte[i6];
        Process.setThreadPriority(-16);
        while (this.f8302b.get()) {
            AudioRecord audioRecord = this.f8301a;
            if (audioRecord != null) {
                int read = audioRecord.read(bArr, 0, i6);
                if (this.f8310j % this.f8311k == 0) {
                    StringBuilder sb = new StringBuilder();
                    sb.append(Thread.currentThread().getName());
                    sb.append("--声音--有效数据:");
                    int i7 = 0;
                    while (true) {
                        if (i7 >= i6) {
                            z5 = false;
                            break;
                        } else {
                            if (bArr[i7] != 0) {
                                z5 = true;
                                break;
                            }
                            i7++;
                        }
                    }
                    sb.append(z5);
                    sb.append("----------read size:");
                    sb.append(read);
                    sb.append(",index:");
                    sb.append(this.f8310j);
                    String sb2 = sb.toString();
                    if (i6 > 15) {
                        StringBuilder m492j = C0174y.m492j(sb2, ":");
                        for (int i8 = 0; i8 < 15; i8++) {
                            m492j.append(String.format("%02X ", Byte.valueOf(bArr[i8])));
                            m492j.append(",");
                        }
                        m492j.append("最大长度：" + i6);
                        Log.d("AudioRecorder", "logData " + m492j.toString());
                    }
                }
                this.f8310j++;
                InterfaceC2054d interfaceC2054d = this.f8303c;
                if (interfaceC2054d != null && (c1085a = C1806a.this.f7464c) != null) {
                    c1085a.m2774e(bArr);
                }
                if (this.f8305e && (c2070c = this.f8306f) != null) {
                    c2070c.f8340a.execute(new RunnableC0813g(c2070c, bArr, 1));
                }
            }
        }
        Log.d("AudioRecorder", "readMicData end");
    }

    /* renamed from: d */
    public final void m5166d() {
        try {
            m5167e();
            Log.d("AudioRecorder", "release");
            AudioRecord audioRecord = this.f8301a;
            if (audioRecord != null) {
                audioRecord.stop();
                this.f8301a.release();
                this.f8301a = null;
            }
            C2070c c2070c = this.f8306f;
            if (c2070c != null) {
                c2070c.m5181a();
                this.f8306f = null;
            }
        } catch (Exception e6) {
            e6.printStackTrace();
            Log.e("AudioRecorder", "release Exception:" + e6);
        }
    }

    /* renamed from: e */
    public final void m5167e() {
        StringBuilder m104h = C0052a.m104h("stopRecord 停止录音:");
        m104h.append(this.f8302b.get());
        Log.d("AudioRecorder", m104h.toString());
        if (this.f8302b.get()) {
            this.f8302b.set(false);
        }
    }
}
