package p166y4;

import android.content.Context;
import android.hardware.display.VirtualDisplay;
import android.media.MediaCodec;
import android.media.MediaFormat;
import android.util.Log;
import android.view.Surface;
import java.nio.ByteBuffer;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;
import p031e2.RunnableC0801q;
import p033e4.RunnableC0813g;
import p088m4.C1085a;
import p154w4.C1806a;

/* renamed from: y4.b */
/* loaded from: classes.dex */
public final class C2069b {

    /* renamed from: a */
    public int f8323a;

    /* renamed from: b */
    public int f8324b;

    /* renamed from: c */
    public int f8325c;

    /* renamed from: d */
    public int f8326d;

    /* renamed from: e */
    public int f8327e;

    /* renamed from: f */
    public MediaCodec f8328f;

    /* renamed from: g */
    public Surface f8329g;

    /* renamed from: j */
    public VirtualDisplay f8332j;

    /* renamed from: k */
    public MediaFormat f8333k;

    /* renamed from: l */
    public InterfaceC2068a f8334l;

    /* renamed from: m */
    public ExecutorService f8335m;

    /* renamed from: n */
    public boolean f8336n;

    /* renamed from: o */
    public C2070c f8337o;

    /* renamed from: p */
    public byte[] f8338p;

    /* renamed from: h */
    public AtomicBoolean f8330h = new AtomicBoolean(false);

    /* renamed from: i */
    public MediaCodec.BufferInfo f8331i = new MediaCodec.BufferInfo();

    /* renamed from: q */
    public RunnableC0801q f8339q = new RunnableC0801q(this, 2);

    public C2069b(Context context) {
        Log.d("ScreenEncoder", "saveCacheFile save:false");
        this.f8336n = false;
        C2070c c2070c = this.f8337o;
        if (c2070c != null) {
            c2070c.m5181a();
            this.f8337o = null;
        }
    }

    /* renamed from: a */
    public final void m5178a(byte[] bArr) {
        System.currentTimeMillis();
        int length = bArr.length <= 15 ? bArr.length : 15;
        for (int i6 = 0; i6 < length; i6++) {
            String.format("%02X ", Byte.valueOf(bArr[i6]));
        }
    }

    /* renamed from: b */
    public final void m5179b() {
        MediaCodec mediaCodec;
        C2070c c2070c;
        C1085a c1085a;
        while (this.f8330h.get() && (mediaCodec = this.f8328f) != null) {
            int dequeueOutputBuffer = mediaCodec.dequeueOutputBuffer(this.f8331i, 1000000L);
            if (dequeueOutputBuffer != -3 && dequeueOutputBuffer != -2 && dequeueOutputBuffer != -1) {
                ByteBuffer outputBuffer = this.f8328f.getOutputBuffer(dequeueOutputBuffer);
                MediaCodec.BufferInfo bufferInfo = this.f8331i;
                if (bufferInfo.size != 0) {
                    outputBuffer.position(bufferInfo.offset);
                    MediaCodec.BufferInfo bufferInfo2 = this.f8331i;
                    outputBuffer.limit(bufferInfo2.offset + bufferInfo2.size);
                    int limit = outputBuffer.limit() - outputBuffer.position();
                    byte[] bArr = new byte[limit];
                    outputBuffer.get(bArr);
                    int i6 = this.f8331i.flags;
                    boolean z5 = (i6 & 1) != 0;
                    boolean z6 = (i6 & 2) != 0;
                    if (z5) {
                        m5178a(bArr);
                    }
                    if (z6) {
                        m5178a(bArr);
                        byte[] bArr2 = new byte[limit];
                        this.f8338p = bArr2;
                        System.arraycopy(bArr, 0, bArr2, 0, limit);
                    }
                    InterfaceC2068a interfaceC2068a = this.f8334l;
                    if (interfaceC2068a != null && (c1085a = C1806a.this.f7464c) != null) {
                        c1085a.m2775f(bArr);
                    }
                    if (this.f8336n && (c2070c = this.f8337o) != null) {
                        c2070c.f8340a.execute(new RunnableC0813g(c2070c, bArr, 1));
                    }
                }
                this.f8328f.releaseOutputBuffer(dequeueOutputBuffer, false);
                if ((this.f8331i.flags & 4) != 0) {
                    Log.d("ScreenEncoder", "-------------------BUFFER_FLAG_END_OF_STREAM");
                }
            }
        }
    }

    /* renamed from: c */
    public final void m5180c() {
        Log.d("ScreenEncoder", "---------release---pauseEncoder----stopEncoder---");
        if (this.f8330h.get()) {
            this.f8330h.set(false);
        }
        try {
            if (this.f8333k != null) {
                this.f8333k = null;
            }
            MediaCodec mediaCodec = this.f8328f;
            if (mediaCodec != null) {
                mediaCodec.stop();
                this.f8328f.release();
                this.f8328f = null;
            }
            Surface surface = this.f8329g;
            if (surface != null) {
                surface.release();
                this.f8329g = null;
            }
            VirtualDisplay virtualDisplay = this.f8332j;
            if (virtualDisplay != null) {
                virtualDisplay.release();
                this.f8332j = null;
            }
        } catch (Exception e6) {
            e6.printStackTrace();
            Log.d("ScreenEncoder", "release e:" + e6);
        }
    }
}
