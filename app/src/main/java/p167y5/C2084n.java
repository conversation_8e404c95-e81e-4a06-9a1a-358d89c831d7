package p167y5;

import androidx.activity.result.C0052a;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;
import java.util.logging.Logger;

/* renamed from: y5.n */
/* loaded from: classes.dex */
public final class C2084n {

    /* renamed from: a */
    public static final Logger f8375a = Logger.getLogger(C2084n.class.getName());

    /* renamed from: y5.n$a */
    public class a implements InterfaceC2092v {

        /* renamed from: j */
        public final /* synthetic */ C2093w f8376j;

        /* renamed from: k */
        public final /* synthetic */ InputStream f8377k;

        public a(C2093w c2093w, InputStream inputStream) {
            this.f8376j = c2093w;
            this.f8377k = inputStream;
        }

        @Override // p167y5.InterfaceC2092v
        /* renamed from: b */
        public final C2093w mo3431b() {
            return this.f8376j;
        }

        @Override // p167y5.InterfaceC2092v, java.io.Closeable, java.lang.AutoCloseable
        public final void close() {
            this.f8377k.close();
        }

        public final String toString() {
            StringBuilder m104h = C0052a.m104h("source(");
            m104h.append(this.f8377k);
            m104h.append(")");
            return m104h.toString();
        }

        @Override // p167y5.InterfaceC2092v
        /* renamed from: y */
        public final long mo3433y(C2074d c2074d, long j6) {
            try {
                this.f8376j.mo5247f();
                C2088r m5200O = c2074d.m5200O(1);
                int read = this.f8377k.read(m5200O.f8385a, m5200O.f8387c, (int) Math.min(8192L, 8192 - m5200O.f8387c));
                if (read == -1) {
                    return -1L;
                }
                m5200O.f8387c += read;
                long j7 = read;
                c2074d.f8355k += j7;
                return j7;
            } catch (AssertionError e6) {
                if (C2084n.m5252a(e6)) {
                    throw new IOException(e6);
                }
                throw e6;
            }
        }
    }

    /* renamed from: a */
    public static boolean m5252a(AssertionError assertionError) {
        return (assertionError.getCause() == null || assertionError.getMessage() == null || !assertionError.getMessage().contains("getsockname failed")) ? false : true;
    }

    /* renamed from: b */
    public static InterfaceC2091u m5253b(Socket socket) {
        if (socket == null) {
            throw new IllegalArgumentException("socket == null");
        }
        if (socket.getOutputStream() == null) {
            throw new IOException("socket's output stream == null");
        }
        C2085o c2085o = new C2085o(socket);
        OutputStream outputStream = socket.getOutputStream();
        if (outputStream != null) {
            return new C2071a(c2085o, new C2083m(c2085o, outputStream));
        }
        throw new IllegalArgumentException("out == null");
    }

    /* renamed from: c */
    public static InterfaceC2092v m5254c(InputStream inputStream, C2093w c2093w) {
        if (inputStream != null) {
            return new a(c2093w, inputStream);
        }
        throw new IllegalArgumentException("in == null");
    }

    /* renamed from: d */
    public static InterfaceC2092v m5255d(Socket socket) {
        if (socket == null) {
            throw new IllegalArgumentException("socket == null");
        }
        if (socket.getInputStream() == null) {
            throw new IOException("socket's input stream == null");
        }
        C2085o c2085o = new C2085o(socket);
        return new C2072b(c2085o, m5254c(socket.getInputStream(), c2085o));
    }
}
