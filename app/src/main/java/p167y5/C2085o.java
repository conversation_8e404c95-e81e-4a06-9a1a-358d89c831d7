package p167y5;

import java.io.IOException;
import java.net.Socket;
import java.net.SocketTimeoutException;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.annotation.Nullable;

/* renamed from: y5.o */
/* loaded from: classes.dex */
public final class C2085o extends C2073c {

    /* renamed from: k */
    public final /* synthetic */ Socket f8378k;

    public C2085o(Socket socket) {
        this.f8378k = socket;
    }

    @Override // p167y5.C2073c
    /* renamed from: m */
    public final IOException mo3508m(@Nullable IOException iOException) {
        SocketTimeoutException socketTimeoutException = new SocketTimeoutException("timeout");
        if (iOException != null) {
            socketTimeoutException.initCause(iOException);
        }
        return socketTimeoutException;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // p167y5.C2073c
    /* renamed from: n */
    public final void mo3509n() {
        Level level;
        StringBuilder sb;
        Logger logger;
        Exception exc;
        try {
            this.f8378k.close();
        } catch (AssertionError e6) {
            if (!C2084n.m5252a(e6)) {
                throw e6;
            }
            Logger logger2 = C2084n.f8375a;
            level = Level.WARNING;
            sb = new StringBuilder();
            exc = e6;
            logger = logger2;
            sb.append("Failed to close timed out socket ");
            sb.append(this.f8378k);
            logger.log(level, sb.toString(), (Throwable) exc);
        } catch (Exception e7) {
            Logger logger3 = C2084n.f8375a;
            level = Level.WARNING;
            sb = new StringBuilder();
            exc = e7;
            logger = logger3;
            sb.append("Failed to close timed out socket ");
            sb.append(this.f8378k);
            logger.log(level, sb.toString(), (Throwable) exc);
        }
    }
}
