package p167y5;

import androidx.activity.result.C0052a;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import p153w3.C1798e;

/* renamed from: y5.g */
/* loaded from: classes.dex */
public class C2077g implements Serializable, Comparable<C2077g> {

    /* renamed from: m */
    public static final char[] f8356m = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    /* renamed from: n */
    public static final C2077g f8357n = m5230k(new byte[0]);

    /* renamed from: j */
    public final byte[] f8358j;

    /* renamed from: k */
    public transient int f8359k;

    /* renamed from: l */
    public transient String f8360l;

    public C2077g(byte[] bArr) {
        this.f8358j = bArr;
    }

    /* renamed from: e */
    public static C2077g m5227e(String str) {
        if (str.length() % 2 != 0) {
            throw new IllegalArgumentException(C0052a.m103g("Unexpected hex string: ", str));
        }
        int length = str.length() / 2;
        byte[] bArr = new byte[length];
        for (int i6 = 0; i6 < length; i6++) {
            int i7 = i6 * 2;
            bArr[i6] = (byte) (m5228f(str.charAt(i7 + 1)) + (m5228f(str.charAt(i7)) << 4));
        }
        return m5230k(bArr);
    }

    /* renamed from: f */
    public static int m5228f(char c) {
        if (c >= '0' && c <= '9') {
            return c - '0';
        }
        char c6 = 'a';
        if (c < 'a' || c > 'f') {
            c6 = 'A';
            if (c < 'A' || c > 'F') {
                throw new IllegalArgumentException("Unexpected hex digit: " + c);
            }
        }
        return (c - c6) + 10;
    }

    /* renamed from: h */
    public static C2077g m5229h(String str) {
        if (str == null) {
            throw new IllegalArgumentException("s == null");
        }
        C2077g c2077g = new C2077g(str.getBytes(C2094x.f8400a));
        c2077g.f8360l = str;
        return c2077g;
    }

    /* renamed from: k */
    public static C2077g m5230k(byte... bArr) {
        if (bArr != null) {
            return new C2077g((byte[]) bArr.clone());
        }
        throw new IllegalArgumentException("data == null");
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0031, code lost:
    
        return -1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:?, code lost:
    
        return 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x002e, code lost:
    
        if (r0 < r1) goto L29;
     */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x0025, code lost:
    
        if (r7 < r8) goto L29;
     */
    @Override // java.lang.Comparable
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final int compareTo(p167y5.C2077g r10) {
        /*
            r9 = this;
            y5.g r10 = (p167y5.C2077g) r10
            int r0 = r9.mo5237n()
            int r1 = r10.mo5237n()
            int r2 = java.lang.Math.min(r0, r1)
            r3 = 0
            r4 = r3
        L10:
            r5 = -1
            r6 = 1
            if (r4 >= r2) goto L2b
            byte r7 = r9.mo5233i(r4)
            r7 = r7 & 255(0xff, float:3.57E-43)
            byte r8 = r10.mo5233i(r4)
            r8 = r8 & 255(0xff, float:3.57E-43)
            if (r7 != r8) goto L25
            int r4 = r4 + 1
            goto L10
        L25:
            if (r7 >= r8) goto L29
        L27:
            r3 = r5
            goto L31
        L29:
            r3 = r6
            goto L31
        L2b:
            if (r0 != r1) goto L2e
            goto L31
        L2e:
            if (r0 >= r1) goto L29
            goto L27
        L31:
            return r3
        */
        throw new UnsupportedOperationException("Method not decompiled: p167y5.C2077g.compareTo(java.lang.Object):int");
    }

    /* renamed from: d */
    public String mo5231d() {
        byte[] bArr = this.f8358j;
        byte[] bArr2 = C1798e.f7335N;
        byte[] bArr3 = new byte[((bArr.length + 2) / 3) * 4];
        int length = bArr.length - (bArr.length % 3);
        int i6 = 0;
        for (int i7 = 0; i7 < length; i7 += 3) {
            int i8 = i6 + 1;
            bArr3[i6] = bArr2[(bArr[i7] & 255) >> 2];
            int i9 = i8 + 1;
            int i10 = i7 + 1;
            bArr3[i8] = bArr2[((bArr[i7] & 3) << 4) | ((bArr[i10] & 255) >> 4)];
            int i11 = i9 + 1;
            int i12 = (bArr[i10] & 15) << 2;
            int i13 = i7 + 2;
            bArr3[i9] = bArr2[i12 | ((bArr[i13] & 255) >> 6)];
            i6 = i11 + 1;
            bArr3[i11] = bArr2[bArr[i13] & 63];
        }
        int length2 = bArr.length % 3;
        if (length2 == 1) {
            int i14 = i6 + 1;
            bArr3[i6] = bArr2[(bArr[length] & 255) >> 2];
            int i15 = i14 + 1;
            bArr3[i14] = bArr2[(bArr[length] & 3) << 4];
            bArr3[i15] = 61;
            bArr3[i15 + 1] = 61;
        } else if (length2 == 2) {
            int i16 = i6 + 1;
            bArr3[i6] = bArr2[(bArr[length] & 255) >> 2];
            int i17 = i16 + 1;
            int i18 = (bArr[length] & 3) << 4;
            int i19 = length + 1;
            bArr3[i16] = bArr2[((bArr[i19] & 255) >> 4) | i18];
            bArr3[i17] = bArr2[(bArr[i19] & 15) << 2];
            bArr3[i17 + 1] = 61;
        }
        try {
            return new String(bArr3, "US-ASCII");
        } catch (UnsupportedEncodingException e6) {
            throw new AssertionError(e6);
        }
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof C2077g) {
            C2077g c2077g = (C2077g) obj;
            int mo5237n = c2077g.mo5237n();
            byte[] bArr = this.f8358j;
            if (mo5237n == bArr.length && c2077g.mo5235l(0, bArr, 0, bArr.length)) {
                return true;
            }
        }
        return false;
    }

    /* renamed from: g */
    public final C2077g m5232g(String str) {
        try {
            return m5230k(MessageDigest.getInstance(str).digest(this.f8358j));
        } catch (NoSuchAlgorithmException e6) {
            throw new AssertionError(e6);
        }
    }

    public int hashCode() {
        int i6 = this.f8359k;
        if (i6 != 0) {
            return i6;
        }
        int hashCode = Arrays.hashCode(this.f8358j);
        this.f8359k = hashCode;
        return hashCode;
    }

    /* renamed from: i */
    public byte mo5233i(int i6) {
        return this.f8358j[i6];
    }

    /* renamed from: j */
    public String mo5234j() {
        byte[] bArr = this.f8358j;
        char[] cArr = new char[bArr.length * 2];
        int i6 = 0;
        for (byte b6 : bArr) {
            int i7 = i6 + 1;
            char[] cArr2 = f8356m;
            cArr[i6] = cArr2[(b6 >> 4) & 15];
            i6 = i7 + 1;
            cArr[i7] = cArr2[b6 & 15];
        }
        return new String(cArr);
    }

    /* renamed from: l */
    public boolean mo5235l(int i6, byte[] bArr, int i7, int i8) {
        boolean z5;
        if (i6 < 0) {
            return false;
        }
        byte[] bArr2 = this.f8358j;
        if (i6 > bArr2.length - i8 || i7 < 0 || i7 > bArr.length - i8) {
            return false;
        }
        Charset charset = C2094x.f8400a;
        int i9 = 0;
        while (true) {
            if (i9 >= i8) {
                z5 = true;
                break;
            }
            if (bArr2[i9 + i6] != bArr[i9 + i7]) {
                z5 = false;
                break;
            }
            i9++;
        }
        return z5;
    }

    /* renamed from: m */
    public boolean mo5236m(C2077g c2077g, int i6) {
        return c2077g.mo5235l(0, this.f8358j, 0, i6);
    }

    /* renamed from: n */
    public int mo5237n() {
        return this.f8358j.length;
    }

    /* renamed from: o */
    public C2077g mo5238o() {
        byte[] bArr = this.f8358j;
        if (64 > bArr.length) {
            StringBuilder m104h = C0052a.m104h("endIndex > length(");
            m104h.append(this.f8358j.length);
            m104h.append(")");
            throw new IllegalArgumentException(m104h.toString());
        }
        if (64 == bArr.length) {
            return this;
        }
        byte[] bArr2 = new byte[64];
        System.arraycopy(bArr, 0, bArr2, 0, 64);
        return new C2077g(bArr2);
    }

    /* renamed from: p */
    public C2077g mo5239p() {
        int i6 = 0;
        while (true) {
            byte[] bArr = this.f8358j;
            if (i6 >= bArr.length) {
                return this;
            }
            byte b6 = bArr[i6];
            if (b6 >= 65 && b6 <= 90) {
                byte[] bArr2 = (byte[]) bArr.clone();
                bArr2[i6] = (byte) (b6 + 32);
                for (int i7 = i6 + 1; i7 < bArr2.length; i7++) {
                    byte b7 = bArr2[i7];
                    if (b7 >= 65 && b7 <= 90) {
                        bArr2[i7] = (byte) (b7 + 32);
                    }
                }
                return new C2077g(bArr2);
            }
            i6++;
        }
    }

    /* renamed from: q */
    public String mo5240q() {
        String str = this.f8360l;
        if (str != null) {
            return str;
        }
        String str2 = new String(this.f8358j, C2094x.f8400a);
        this.f8360l = str2;
        return str2;
    }

    /* renamed from: r */
    public void mo5241r(C2074d c2074d) {
        byte[] bArr = this.f8358j;
        c2074d.m5202Q(bArr, 0, bArr.length);
    }

    public String toString() {
        StringBuilder sb;
        StringBuilder m104h;
        if (this.f8358j.length == 0) {
            return "[size=0]";
        }
        String mo5240q = mo5240q();
        int length = mo5240q.length();
        int i6 = 0;
        int i7 = 0;
        while (true) {
            if (i6 >= length) {
                i6 = mo5240q.length();
                break;
            }
            if (i7 == 64) {
                break;
            }
            int codePointAt = mo5240q.codePointAt(i6);
            if ((!Character.isISOControl(codePointAt) || codePointAt == 10 || codePointAt == 13) && codePointAt != 65533) {
                i7++;
                i6 += Character.charCount(codePointAt);
            }
        }
        i6 = -1;
        if (i6 == -1) {
            if (this.f8358j.length <= 64) {
                m104h = C0052a.m104h("[hex=");
                m104h.append(mo5234j());
                m104h.append("]");
            } else {
                m104h = C0052a.m104h("[size=");
                m104h.append(this.f8358j.length);
                m104h.append(" hex=");
                m104h.append(mo5238o().mo5234j());
                m104h.append("…]");
            }
            return m104h.toString();
        }
        String replace = mo5240q.substring(0, i6).replace("\\", "\\\\").replace("\n", "\\n").replace("\r", "\\r");
        if (i6 < mo5240q.length()) {
            sb = C0052a.m104h("[size=");
            sb.append(this.f8358j.length);
            sb.append(" text=");
            sb.append(replace);
            sb.append("…]");
        } else {
            sb = new StringBuilder();
            sb.append("[text=");
            sb.append(replace);
            sb.append("]");
        }
        return sb.toString();
    }
}
