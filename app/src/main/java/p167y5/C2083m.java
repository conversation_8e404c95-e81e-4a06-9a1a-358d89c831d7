package p167y5;

import androidx.activity.result.C0052a;
import java.io.OutputStream;

/* renamed from: y5.m */
/* loaded from: classes.dex */
public final class C2083m implements InterfaceC2091u {

    /* renamed from: j */
    public final /* synthetic */ C2093w f8373j;

    /* renamed from: k */
    public final /* synthetic */ OutputStream f8374k;

    public C2083m(C2093w c2093w, OutputStream outputStream) {
        this.f8373j = c2093w;
        this.f8374k = outputStream;
    }

    @Override // p167y5.InterfaceC2091u
    /* renamed from: b */
    public final C2093w mo3434b() {
        return this.f8373j;
    }

    @Override // p167y5.InterfaceC2091u, java.io.Closeable, java.lang.AutoCloseable
    public final void close() {
        this.f8374k.close();
    }

    @Override // p167y5.InterfaceC2091u, java.io.Flushable
    public final void flush() {
        this.f8374k.flush();
    }

    @Override // p167y5.InterfaceC2091u
    /* renamed from: m */
    public final void mo3277m(C2074d c2074d, long j6) {
        C2094x.m5270a(c2074d.f8355k, 0L, j6);
        while (j6 > 0) {
            this.f8373j.mo5247f();
            C2088r c2088r = c2074d.f8354j;
            int min = (int) Math.min(j6, c2088r.f8387c - c2088r.f8386b);
            this.f8374k.write(c2088r.f8385a, c2088r.f8386b, min);
            int i6 = c2088r.f8386b + min;
            c2088r.f8386b = i6;
            long j7 = min;
            j6 -= j7;
            c2074d.f8355k -= j7;
            if (i6 == c2088r.f8387c) {
                c2074d.f8354j = c2088r.m5261a();
                C2089s.m5265a(c2088r);
            }
        }
    }

    public final String toString() {
        StringBuilder m104h = C0052a.m104h("sink(");
        m104h.append(this.f8374k);
        m104h.append(")");
        return m104h.toString();
    }
}
