package p167y5;

/* renamed from: y5.h */
/* loaded from: classes.dex */
public abstract class AbstractC2078h implements InterfaceC2091u {

    /* renamed from: j */
    public final InterfaceC2091u f8361j;

    public AbstractC2078h(InterfaceC2091u interfaceC2091u) {
        if (interfaceC2091u == null) {
            throw new IllegalArgumentException("delegate == null");
        }
        this.f8361j = interfaceC2091u;
    }

    @Override // p167y5.InterfaceC2091u
    /* renamed from: b */
    public final C2093w mo3434b() {
        return this.f8361j.mo3434b();
    }

    @Override // p167y5.InterfaceC2091u, java.io.Closeable, java.lang.AutoCloseable
    public final void close() {
        this.f8361j.close();
    }

    @Override // p167y5.InterfaceC2091u, java.io.Flushable
    public final void flush() {
        this.f8361j.flush();
    }

    public final String toString() {
        return getClass().getSimpleName() + "(" + this.f8361j.toString() + ")";
    }
}
