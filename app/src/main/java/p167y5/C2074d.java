package p167y5;

import androidx.activity.result.C0052a;
import androidx.appcompat.widget.C0174y;
import java.io.EOFException;
import java.nio.ByteBuffer;
import java.nio.channels.ByteChannel;
import java.nio.charset.Charset;
import java.util.Objects;
import javax.annotation.Nullable;

/* renamed from: y5.d */
/* loaded from: classes.dex */
public final class C2074d implements InterfaceC2076f, InterfaceC2075e, Cloneable, ByteChannel {

    /* renamed from: l */
    public static final byte[] f8353l = {48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 97, 98, 99, 100, 101, 102};

    /* renamed from: j */
    @Nullable
    public C2088r f8354j;

    /* renamed from: k */
    public long f8355k;

    @Override // p167y5.InterfaceC2076f
    /* renamed from: A */
    public final String mo5187A(Charset charset) {
        try {
            return m5197L(this.f8355k, charset);
        } catch (EOFException e6) {
            throw new AssertionError(e6);
        }
    }

    /* renamed from: C */
    public final C2074d m5188C(C2074d c2074d, long j6, long j7) {
        if (c2074d == null) {
            throw new IllegalArgumentException("out == null");
        }
        C2094x.m5270a(this.f8355k, j6, j7);
        if (j7 == 0) {
            return this;
        }
        c2074d.f8355k += j7;
        C2088r c2088r = this.f8354j;
        while (true) {
            int i6 = c2088r.f8387c;
            int i7 = c2088r.f8386b;
            if (j6 < i6 - i7) {
                break;
            }
            j6 -= i6 - i7;
            c2088r = c2088r.f8390f;
        }
        while (j7 > 0) {
            C2088r m5263c = c2088r.m5263c();
            int i8 = (int) (m5263c.f8386b + j6);
            m5263c.f8386b = i8;
            m5263c.f8387c = Math.min(i8 + ((int) j7), m5263c.f8387c);
            C2088r c2088r2 = c2074d.f8354j;
            if (c2088r2 == null) {
                m5263c.f8391g = m5263c;
                m5263c.f8390f = m5263c;
                c2074d.f8354j = m5263c;
            } else {
                c2088r2.f8391g.m5262b(m5263c);
            }
            j7 -= m5263c.f8387c - m5263c.f8386b;
            c2088r = c2088r.f8390f;
            j6 = 0;
        }
        return this;
    }

    /* renamed from: D */
    public final byte m5189D(long j6) {
        int i6;
        C2094x.m5270a(this.f8355k, j6, 1L);
        long j7 = this.f8355k;
        if (j7 - j6 <= j6) {
            long j8 = j6 - j7;
            C2088r c2088r = this.f8354j;
            do {
                c2088r = c2088r.f8391g;
                int i7 = c2088r.f8387c;
                i6 = c2088r.f8386b;
                j8 += i7 - i6;
            } while (j8 < 0);
            return c2088r.f8385a[i6 + ((int) j8)];
        }
        C2088r c2088r2 = this.f8354j;
        while (true) {
            int i8 = c2088r2.f8387c;
            int i9 = c2088r2.f8386b;
            long j9 = i8 - i9;
            if (j6 < j9) {
                return c2088r2.f8385a[i9 + ((int) j6)];
            }
            j6 -= j9;
            c2088r2 = c2088r2.f8390f;
        }
    }

    /* renamed from: E */
    public final long m5190E(byte b6, long j6, long j7) {
        C2088r c2088r;
        long j8 = 0;
        if (j6 < 0 || j7 < j6) {
            throw new IllegalArgumentException(String.format("size=%s fromIndex=%s toIndex=%s", Long.valueOf(this.f8355k), Long.valueOf(j6), Long.valueOf(j7)));
        }
        long j9 = this.f8355k;
        long j10 = j7 > j9 ? j9 : j7;
        if (j6 == j10 || (c2088r = this.f8354j) == null) {
            return -1L;
        }
        if (j9 - j6 < j6) {
            while (j9 > j6) {
                c2088r = c2088r.f8391g;
                j9 -= c2088r.f8387c - c2088r.f8386b;
            }
        } else {
            while (true) {
                long j11 = (c2088r.f8387c - c2088r.f8386b) + j8;
                if (j11 >= j6) {
                    break;
                }
                c2088r = c2088r.f8390f;
                j8 = j11;
            }
            j9 = j8;
        }
        long j12 = j6;
        while (j9 < j10) {
            byte[] bArr = c2088r.f8385a;
            int min = (int) Math.min(c2088r.f8387c, (c2088r.f8386b + j10) - j9);
            for (int i6 = (int) ((c2088r.f8386b + j12) - j9); i6 < min; i6++) {
                if (bArr[i6] == b6) {
                    return (i6 - c2088r.f8386b) + j9;
                }
            }
            j9 += c2088r.f8387c - c2088r.f8386b;
            c2088r = c2088r.f8390f;
            j12 = j9;
        }
        return -1L;
    }

    /* renamed from: F */
    public final int m5191F(byte[] bArr, int i6, int i7) {
        C2094x.m5270a(bArr.length, i6, i7);
        C2088r c2088r = this.f8354j;
        if (c2088r == null) {
            return -1;
        }
        int min = Math.min(i7, c2088r.f8387c - c2088r.f8386b);
        System.arraycopy(c2088r.f8385a, c2088r.f8386b, bArr, i6, min);
        int i8 = c2088r.f8386b + min;
        c2088r.f8386b = i8;
        this.f8355k -= min;
        if (i8 == c2088r.f8387c) {
            this.f8354j = c2088r.m5261a();
            C2089s.m5265a(c2088r);
        }
        return min;
    }

    /* renamed from: G */
    public final byte[] m5192G(long j6) {
        C2094x.m5270a(this.f8355k, 0L, j6);
        if (j6 <= 2147483647L) {
            byte[] bArr = new byte[(int) j6];
            m5194I(bArr);
            return bArr;
        }
        throw new IllegalArgumentException("byteCount > Integer.MAX_VALUE: " + j6);
    }

    /* renamed from: H */
    public final C2077g m5193H() {
        try {
            return new C2077g(m5192G(this.f8355k));
        } catch (EOFException e6) {
            throw new AssertionError(e6);
        }
    }

    /* renamed from: I */
    public final void m5194I(byte[] bArr) {
        int i6 = 0;
        while (i6 < bArr.length) {
            int m5191F = m5191F(bArr, i6, bArr.length - i6);
            if (m5191F == -1) {
                throw new EOFException();
            }
            i6 += m5191F;
        }
    }

    /* renamed from: J */
    public final int m5195J() {
        int readInt = readInt();
        Charset charset = C2094x.f8400a;
        return ((readInt & 255) << 24) | (((-16777216) & readInt) >>> 24) | ((16711680 & readInt) >>> 8) | ((65280 & readInt) << 8);
    }

    /* renamed from: K */
    public final short m5196K() {
        short readShort = readShort();
        Charset charset = C2094x.f8400a;
        int i6 = readShort & 65535;
        return (short) (((i6 & 255) << 8) | ((65280 & i6) >>> 8));
    }

    /* renamed from: L */
    public final String m5197L(long j6, Charset charset) {
        C2094x.m5270a(this.f8355k, 0L, j6);
        if (charset == null) {
            throw new IllegalArgumentException("charset == null");
        }
        if (j6 > 2147483647L) {
            throw new IllegalArgumentException("byteCount > Integer.MAX_VALUE: " + j6);
        }
        if (j6 == 0) {
            return "";
        }
        C2088r c2088r = this.f8354j;
        int i6 = c2088r.f8386b;
        if (i6 + j6 > c2088r.f8387c) {
            return new String(m5192G(j6), charset);
        }
        String str = new String(c2088r.f8385a, i6, (int) j6, charset);
        int i7 = (int) (c2088r.f8386b + j6);
        c2088r.f8386b = i7;
        this.f8355k -= j6;
        if (i7 == c2088r.f8387c) {
            this.f8354j = c2088r.m5261a();
            C2089s.m5265a(c2088r);
        }
        return str;
    }

    /* renamed from: M */
    public final String m5198M() {
        try {
            return m5197L(this.f8355k, C2094x.f8400a);
        } catch (EOFException e6) {
            throw new AssertionError(e6);
        }
    }

    /* renamed from: N */
    public final String m5199N(long j6) {
        if (j6 > 0) {
            long j7 = j6 - 1;
            if (m5189D(j7) == 13) {
                String m5197L = m5197L(j7, C2094x.f8400a);
                mo5211a(2L);
                return m5197L;
            }
        }
        String m5197L2 = m5197L(j6, C2094x.f8400a);
        mo5211a(1L);
        return m5197L2;
    }

    /* renamed from: O */
    public final C2088r m5200O(int i6) {
        if (i6 < 1 || i6 > 8192) {
            throw new IllegalArgumentException();
        }
        C2088r c2088r = this.f8354j;
        if (c2088r == null) {
            C2088r m5266b = C2089s.m5266b();
            this.f8354j = m5266b;
            m5266b.f8391g = m5266b;
            m5266b.f8390f = m5266b;
            return m5266b;
        }
        C2088r c2088r2 = c2088r.f8391g;
        if (c2088r2.f8387c + i6 <= 8192 && c2088r2.f8389e) {
            return c2088r2;
        }
        C2088r m5266b2 = C2089s.m5266b();
        c2088r2.m5262b(m5266b2);
        return m5266b2;
    }

    /* renamed from: P */
    public final C2074d m5201P(byte[] bArr) {
        if (bArr == null) {
            throw new IllegalArgumentException("source == null");
        }
        m5202Q(bArr, 0, bArr.length);
        return this;
    }

    /* renamed from: Q */
    public final C2074d m5202Q(byte[] bArr, int i6, int i7) {
        if (bArr == null) {
            throw new IllegalArgumentException("source == null");
        }
        long j6 = i7;
        C2094x.m5270a(bArr.length, i6, j6);
        int i8 = i7 + i6;
        while (i6 < i8) {
            C2088r m5200O = m5200O(1);
            int min = Math.min(i8 - i6, 8192 - m5200O.f8387c);
            System.arraycopy(bArr, i6, m5200O.f8385a, m5200O.f8387c, min);
            i6 += min;
            m5200O.f8387c += min;
        }
        this.f8355k += j6;
        return this;
    }

    /* renamed from: R */
    public final long m5203R(InterfaceC2092v interfaceC2092v) {
        if (interfaceC2092v == null) {
            throw new IllegalArgumentException("source == null");
        }
        long j6 = 0;
        while (true) {
            long mo3433y = interfaceC2092v.mo3433y(this, 8192L);
            if (mo3433y == -1) {
                return j6;
            }
            j6 += mo3433y;
        }
    }

    /* renamed from: S */
    public final C2074d m5204S(int i6) {
        C2088r m5200O = m5200O(1);
        byte[] bArr = m5200O.f8385a;
        int i7 = m5200O.f8387c;
        m5200O.f8387c = i7 + 1;
        bArr[i7] = (byte) i6;
        this.f8355k++;
        return this;
    }

    @Override // p167y5.InterfaceC2075e
    /* renamed from: T */
    public final C2074d mo5214g(long j6) {
        if (j6 == 0) {
            m5204S(48);
            return this;
        }
        int numberOfTrailingZeros = (Long.numberOfTrailingZeros(Long.highestOneBit(j6)) / 4) + 1;
        C2088r m5200O = m5200O(numberOfTrailingZeros);
        byte[] bArr = m5200O.f8385a;
        int i6 = m5200O.f8387c;
        for (int i7 = (i6 + numberOfTrailingZeros) - 1; i7 >= i6; i7--) {
            bArr[i7] = f8353l[(int) (15 & j6)];
            j6 >>>= 4;
        }
        m5200O.f8387c += numberOfTrailingZeros;
        this.f8355k += numberOfTrailingZeros;
        return this;
    }

    /* renamed from: U */
    public final C2074d m5206U(int i6) {
        C2088r m5200O = m5200O(4);
        byte[] bArr = m5200O.f8385a;
        int i7 = m5200O.f8387c;
        int i8 = i7 + 1;
        bArr[i7] = (byte) ((i6 >>> 24) & 255);
        int i9 = i8 + 1;
        bArr[i8] = (byte) ((i6 >>> 16) & 255);
        int i10 = i9 + 1;
        bArr[i9] = (byte) ((i6 >>> 8) & 255);
        bArr[i10] = (byte) (i6 & 255);
        m5200O.f8387c = i10 + 1;
        this.f8355k += 4;
        return this;
    }

    /* renamed from: V */
    public final C2074d m5207V(int i6) {
        C2088r m5200O = m5200O(2);
        byte[] bArr = m5200O.f8385a;
        int i7 = m5200O.f8387c;
        int i8 = i7 + 1;
        bArr[i7] = (byte) ((i6 >>> 8) & 255);
        bArr[i8] = (byte) (i6 & 255);
        m5200O.f8387c = i8 + 1;
        this.f8355k += 2;
        return this;
    }

    /* renamed from: W */
    public final C2074d m5208W(String str) {
        m5209X(str, 0, str.length());
        return this;
    }

    /* renamed from: X */
    public final C2074d m5209X(String str, int i6, int i7) {
        char charAt;
        int i8;
        if (str == null) {
            throw new IllegalArgumentException("string == null");
        }
        if (i6 < 0) {
            throw new IllegalArgumentException(C0174y.m490h("beginIndex < 0: ", i6));
        }
        if (i7 < i6) {
            throw new IllegalArgumentException("endIndex < beginIndex: " + i7 + " < " + i6);
        }
        if (i7 > str.length()) {
            throw new IllegalArgumentException("endIndex > string.length: " + i7 + " > " + str.length());
        }
        while (i6 < i7) {
            char charAt2 = str.charAt(i6);
            if (charAt2 < 128) {
                C2088r m5200O = m5200O(1);
                byte[] bArr = m5200O.f8385a;
                int i9 = m5200O.f8387c - i6;
                int min = Math.min(i7, 8192 - i9);
                int i10 = i6 + 1;
                bArr[i6 + i9] = (byte) charAt2;
                while (true) {
                    i6 = i10;
                    if (i6 >= min || (charAt = str.charAt(i6)) >= 128) {
                        break;
                    }
                    i10 = i6 + 1;
                    bArr[i6 + i9] = (byte) charAt;
                }
                int i11 = m5200O.f8387c;
                int i12 = (i9 + i6) - i11;
                m5200O.f8387c = i11 + i12;
                this.f8355k += i12;
            } else {
                if (charAt2 < 2048) {
                    i8 = (charAt2 >> 6) | 192;
                } else if (charAt2 < 55296 || charAt2 > 57343) {
                    m5204S((charAt2 >> '\f') | 224);
                    i8 = ((charAt2 >> 6) & 63) | 128;
                } else {
                    int i13 = i6 + 1;
                    char charAt3 = i13 < i7 ? str.charAt(i13) : (char) 0;
                    if (charAt2 > 56319 || charAt3 < 56320 || charAt3 > 57343) {
                        m5204S(63);
                        i6 = i13;
                    } else {
                        int i14 = (((charAt2 & 10239) << 10) | (9215 & charAt3)) + 65536;
                        m5204S((i14 >> 18) | 240);
                        m5204S(((i14 >> 12) & 63) | 128);
                        m5204S(((i14 >> 6) & 63) | 128);
                        m5204S((i14 & 63) | 128);
                        i6 += 2;
                    }
                }
                m5204S(i8);
                m5204S((charAt2 & '?') | 128);
                i6++;
            }
        }
        return this;
    }

    /* renamed from: Y */
    public final C2074d m5210Y(int i6) {
        int i7;
        int i8;
        if (i6 >= 128) {
            if (i6 < 2048) {
                i8 = (i6 >> 6) | 192;
            } else {
                if (i6 < 65536) {
                    if (i6 >= 55296 && i6 <= 57343) {
                        m5204S(63);
                        return this;
                    }
                    i7 = (i6 >> 12) | 224;
                } else {
                    if (i6 > 1114111) {
                        StringBuilder m104h = C0052a.m104h("Unexpected code point: ");
                        m104h.append(Integer.toHexString(i6));
                        throw new IllegalArgumentException(m104h.toString());
                    }
                    m5204S((i6 >> 18) | 240);
                    i7 = ((i6 >> 12) & 63) | 128;
                }
                m5204S(i7);
                i8 = ((i6 >> 6) & 63) | 128;
            }
            m5204S(i8);
            i6 = (i6 & 63) | 128;
        }
        m5204S(i6);
        return this;
    }

    @Override // p167y5.InterfaceC2076f
    /* renamed from: a */
    public final void mo5211a(long j6) {
        while (j6 > 0) {
            if (this.f8354j == null) {
                throw new EOFException();
            }
            int min = (int) Math.min(j6, r0.f8387c - r0.f8386b);
            long j7 = min;
            this.f8355k -= j7;
            j6 -= j7;
            C2088r c2088r = this.f8354j;
            int i6 = c2088r.f8386b + min;
            c2088r.f8386b = i6;
            if (i6 == c2088r.f8387c) {
                this.f8354j = c2088r.m5261a();
                C2089s.m5265a(c2088r);
            }
        }
    }

    @Override // p167y5.InterfaceC2092v
    /* renamed from: b */
    public final C2093w mo3431b() {
        return C2093w.f8396d;
    }

    @Override // p167y5.InterfaceC2075e
    /* renamed from: c */
    public final /* bridge */ /* synthetic */ InterfaceC2075e mo5212c(byte[] bArr) {
        m5201P(bArr);
        return this;
    }

    public final Object clone() {
        C2074d c2074d = new C2074d();
        if (this.f8355k != 0) {
            C2088r m5263c = this.f8354j.m5263c();
            c2074d.f8354j = m5263c;
            m5263c.f8391g = m5263c;
            m5263c.f8390f = m5263c;
            C2088r c2088r = this.f8354j;
            while (true) {
                c2088r = c2088r.f8390f;
                if (c2088r == this.f8354j) {
                    break;
                }
                c2074d.f8354j.f8391g.m5262b(c2088r.m5263c());
            }
            c2074d.f8355k = this.f8355k;
        }
        return c2074d;
    }

    @Override // p167y5.InterfaceC2092v, java.io.Closeable, java.lang.AutoCloseable
    public final void close() {
    }

    @Override // p167y5.InterfaceC2076f
    /* renamed from: d */
    public final boolean mo5213d(C2077g c2077g) {
        byte[] bArr = c2077g.f8358j;
        int length = bArr.length;
        if (length < 0 || this.f8355k - 0 < length || bArr.length - 0 < length) {
            return false;
        }
        for (int i6 = 0; i6 < length; i6++) {
            if (m5189D(i6 + 0) != c2077g.f8358j[0 + i6]) {
                return false;
            }
        }
        return true;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof C2074d)) {
            return false;
        }
        C2074d c2074d = (C2074d) obj;
        long j6 = this.f8355k;
        if (j6 != c2074d.f8355k) {
            return false;
        }
        long j7 = 0;
        if (j6 == 0) {
            return true;
        }
        C2088r c2088r = this.f8354j;
        C2088r c2088r2 = c2074d.f8354j;
        int i6 = c2088r.f8386b;
        int i7 = c2088r2.f8386b;
        while (j7 < this.f8355k) {
            long min = Math.min(c2088r.f8387c - i6, c2088r2.f8387c - i7);
            int i8 = 0;
            while (i8 < min) {
                int i9 = i6 + 1;
                int i10 = i7 + 1;
                if (c2088r.f8385a[i6] != c2088r2.f8385a[i7]) {
                    return false;
                }
                i8++;
                i6 = i9;
                i7 = i10;
            }
            if (i6 == c2088r.f8387c) {
                c2088r = c2088r.f8390f;
                i6 = c2088r.f8386b;
            }
            if (i7 == c2088r2.f8387c) {
                c2088r2 = c2088r2.f8390f;
                i7 = c2088r2.f8386b;
            }
            j7 += min;
        }
        return true;
    }

    @Override // p167y5.InterfaceC2075e, p167y5.InterfaceC2091u, java.io.Flushable
    public final void flush() {
    }

    @Override // p167y5.InterfaceC2076f
    /* renamed from: h */
    public final C2074d mo5215h() {
        return this;
    }

    public final int hashCode() {
        C2088r c2088r = this.f8354j;
        if (c2088r == null) {
            return 0;
        }
        int i6 = 1;
        do {
            int i7 = c2088r.f8387c;
            for (int i8 = c2088r.f8386b; i8 < i7; i8++) {
                i6 = (i6 * 31) + c2088r.f8385a[i8];
            }
            c2088r = c2088r.f8390f;
        } while (c2088r != this.f8354j);
        return i6;
    }

    @Override // p167y5.InterfaceC2076f
    /* renamed from: i */
    public final C2077g mo5216i(long j6) {
        return new C2077g(m5192G(j6));
    }

    @Override // java.nio.channels.Channel
    public final boolean isOpen() {
        return true;
    }

    @Override // p167y5.InterfaceC2076f
    /* renamed from: j */
    public final String mo5217j(long j6) {
        if (j6 < 0) {
            throw new IllegalArgumentException("limit < 0: " + j6);
        }
        long j7 = j6 != Long.MAX_VALUE ? j6 + 1 : Long.MAX_VALUE;
        long m5190E = m5190E((byte) 10, 0L, j7);
        if (m5190E != -1) {
            return m5199N(m5190E);
        }
        if (j7 < this.f8355k && m5189D(j7 - 1) == 13 && m5189D(j7) == 10) {
            return m5199N(j7);
        }
        C2074d c2074d = new C2074d();
        m5188C(c2074d, 0L, Math.min(32L, this.f8355k));
        StringBuilder m104h = C0052a.m104h("\\n not found: limit=");
        m104h.append(Math.min(this.f8355k, j6));
        m104h.append(" content=");
        m104h.append(c2074d.m5193H().mo5234j());
        m104h.append((char) 8230);
        throw new EOFException(m104h.toString());
    }

    @Override // p167y5.InterfaceC2075e
    /* renamed from: k */
    public final /* bridge */ /* synthetic */ InterfaceC2075e mo5218k(int i6) {
        m5207V(i6);
        return this;
    }

    @Override // p167y5.InterfaceC2091u
    /* renamed from: m */
    public final void mo3277m(C2074d c2074d, long j6) {
        C2088r m5266b;
        if (c2074d == null) {
            throw new IllegalArgumentException("source == null");
        }
        if (c2074d == this) {
            throw new IllegalArgumentException("source == this");
        }
        C2094x.m5270a(c2074d.f8355k, 0L, j6);
        while (j6 > 0) {
            C2088r c2088r = c2074d.f8354j;
            if (j6 < c2088r.f8387c - c2088r.f8386b) {
                C2088r c2088r2 = this.f8354j;
                C2088r c2088r3 = c2088r2 != null ? c2088r2.f8391g : null;
                if (c2088r3 != null && c2088r3.f8389e) {
                    if ((c2088r3.f8387c + j6) - (c2088r3.f8388d ? 0 : c2088r3.f8386b) <= 8192) {
                        c2088r.m5264d(c2088r3, (int) j6);
                        c2074d.f8355k -= j6;
                        this.f8355k += j6;
                        return;
                    }
                }
                int i6 = (int) j6;
                Objects.requireNonNull(c2088r);
                if (i6 <= 0 || i6 > c2088r.f8387c - c2088r.f8386b) {
                    throw new IllegalArgumentException();
                }
                if (i6 >= 1024) {
                    m5266b = c2088r.m5263c();
                } else {
                    m5266b = C2089s.m5266b();
                    System.arraycopy(c2088r.f8385a, c2088r.f8386b, m5266b.f8385a, 0, i6);
                }
                m5266b.f8387c = m5266b.f8386b + i6;
                c2088r.f8386b += i6;
                c2088r.f8391g.m5262b(m5266b);
                c2074d.f8354j = m5266b;
            }
            C2088r c2088r4 = c2074d.f8354j;
            long j7 = c2088r4.f8387c - c2088r4.f8386b;
            c2074d.f8354j = c2088r4.m5261a();
            C2088r c2088r5 = this.f8354j;
            if (c2088r5 == null) {
                this.f8354j = c2088r4;
                c2088r4.f8391g = c2088r4;
                c2088r4.f8390f = c2088r4;
            } else {
                c2088r5.f8391g.m5262b(c2088r4);
                C2088r c2088r6 = c2088r4.f8391g;
                if (c2088r6 == c2088r4) {
                    throw new IllegalStateException();
                }
                if (c2088r6.f8389e) {
                    int i7 = c2088r4.f8387c - c2088r4.f8386b;
                    if (i7 <= (8192 - c2088r6.f8387c) + (c2088r6.f8388d ? 0 : c2088r6.f8386b)) {
                        c2088r4.m5264d(c2088r6, i7);
                        c2088r4.m5261a();
                        C2089s.m5265a(c2088r4);
                    }
                }
            }
            c2074d.f8355k -= j7;
            this.f8355k += j7;
            j6 -= j7;
        }
    }

    /* renamed from: o */
    public final void m5219o() {
        try {
            mo5211a(this.f8355k);
        } catch (EOFException e6) {
            throw new AssertionError(e6);
        }
    }

    @Override // p167y5.InterfaceC2075e
    /* renamed from: p */
    public final /* bridge */ /* synthetic */ InterfaceC2075e mo5220p(int i6) {
        m5206U(i6);
        return this;
    }

    @Override // java.nio.channels.ReadableByteChannel
    public final int read(ByteBuffer byteBuffer) {
        C2088r c2088r = this.f8354j;
        if (c2088r == null) {
            return -1;
        }
        int min = Math.min(byteBuffer.remaining(), c2088r.f8387c - c2088r.f8386b);
        byteBuffer.put(c2088r.f8385a, c2088r.f8386b, min);
        int i6 = c2088r.f8386b + min;
        c2088r.f8386b = i6;
        this.f8355k -= min;
        if (i6 == c2088r.f8387c) {
            this.f8354j = c2088r.m5261a();
            C2089s.m5265a(c2088r);
        }
        return min;
    }

    @Override // p167y5.InterfaceC2076f
    public final byte readByte() {
        long j6 = this.f8355k;
        if (j6 == 0) {
            throw new IllegalStateException("size == 0");
        }
        C2088r c2088r = this.f8354j;
        int i6 = c2088r.f8386b;
        int i7 = c2088r.f8387c;
        int i8 = i6 + 1;
        byte b6 = c2088r.f8385a[i6];
        this.f8355k = j6 - 1;
        if (i8 == i7) {
            this.f8354j = c2088r.m5261a();
            C2089s.m5265a(c2088r);
        } else {
            c2088r.f8386b = i8;
        }
        return b6;
    }

    @Override // p167y5.InterfaceC2076f
    public final int readInt() {
        long j6 = this.f8355k;
        if (j6 < 4) {
            StringBuilder m104h = C0052a.m104h("size < 4: ");
            m104h.append(this.f8355k);
            throw new IllegalStateException(m104h.toString());
        }
        C2088r c2088r = this.f8354j;
        int i6 = c2088r.f8386b;
        int i7 = c2088r.f8387c;
        if (i7 - i6 < 4) {
            return ((readByte() & 255) << 24) | ((readByte() & 255) << 16) | ((readByte() & 255) << 8) | (readByte() & 255);
        }
        byte[] bArr = c2088r.f8385a;
        int i8 = i6 + 1;
        int i9 = i8 + 1;
        int i10 = ((bArr[i6] & 255) << 24) | ((bArr[i8] & 255) << 16);
        int i11 = i9 + 1;
        int i12 = i10 | ((bArr[i9] & 255) << 8);
        int i13 = i11 + 1;
        int i14 = i12 | (bArr[i11] & 255);
        this.f8355k = j6 - 4;
        if (i13 == i7) {
            this.f8354j = c2088r.m5261a();
            C2089s.m5265a(c2088r);
        } else {
            c2088r.f8386b = i13;
        }
        return i14;
    }

    @Override // p167y5.InterfaceC2076f
    public final short readShort() {
        long j6 = this.f8355k;
        if (j6 < 2) {
            StringBuilder m104h = C0052a.m104h("size < 2: ");
            m104h.append(this.f8355k);
            throw new IllegalStateException(m104h.toString());
        }
        C2088r c2088r = this.f8354j;
        int i6 = c2088r.f8386b;
        int i7 = c2088r.f8387c;
        if (i7 - i6 < 2) {
            return (short) (((readByte() & 255) << 8) | (readByte() & 255));
        }
        byte[] bArr = c2088r.f8385a;
        int i8 = i6 + 1;
        int i9 = i8 + 1;
        int i10 = ((bArr[i6] & 255) << 8) | (bArr[i8] & 255);
        this.f8355k = j6 - 2;
        if (i9 == i7) {
            this.f8354j = c2088r.m5261a();
            C2089s.m5265a(c2088r);
        } else {
            c2088r.f8386b = i9;
        }
        return (short) i10;
    }

    @Override // p167y5.InterfaceC2076f
    /* renamed from: s */
    public final String mo5221s() {
        return mo5217j(Long.MAX_VALUE);
    }

    public final String toString() {
        long j6 = this.f8355k;
        if (j6 <= 2147483647L) {
            int i6 = (int) j6;
            return (i6 == 0 ? C2077g.f8357n : new C2090t(this, i6)).toString();
        }
        StringBuilder m104h = C0052a.m104h("size > Integer.MAX_VALUE: ");
        m104h.append(this.f8355k);
        throw new IllegalArgumentException(m104h.toString());
    }

    @Override // p167y5.InterfaceC2076f
    /* renamed from: u */
    public final void mo5222u(long j6) {
        if (this.f8355k < j6) {
            throw new EOFException();
        }
    }

    @Override // p167y5.InterfaceC2075e
    /* renamed from: v */
    public final InterfaceC2075e mo5223v(String str) {
        m5209X(str, 0, str.length());
        return this;
    }

    @Override // p167y5.InterfaceC2076f
    /* renamed from: w */
    public final boolean mo5224w() {
        return this.f8355k == 0;
    }

    @Override // java.nio.channels.WritableByteChannel
    public final int write(ByteBuffer byteBuffer) {
        if (byteBuffer == null) {
            throw new IllegalArgumentException("source == null");
        }
        int remaining = byteBuffer.remaining();
        int i6 = remaining;
        while (i6 > 0) {
            C2088r m5200O = m5200O(1);
            int min = Math.min(i6, 8192 - m5200O.f8387c);
            byteBuffer.get(m5200O.f8385a, m5200O.f8387c, min);
            i6 -= min;
            m5200O.f8387c += min;
        }
        this.f8355k += remaining;
        return remaining;
    }

    @Override // p167y5.InterfaceC2075e
    /* renamed from: x */
    public final /* bridge */ /* synthetic */ InterfaceC2075e mo5225x(int i6) {
        m5204S(i6);
        return this;
    }

    @Override // p167y5.InterfaceC2092v
    /* renamed from: y */
    public final long mo3433y(C2074d c2074d, long j6) {
        if (c2074d == null) {
            throw new IllegalArgumentException("sink == null");
        }
        if (j6 < 0) {
            throw new IllegalArgumentException("byteCount < 0: " + j6);
        }
        long j7 = this.f8355k;
        if (j7 == 0) {
            return -1L;
        }
        if (j6 > j7) {
            j6 = j7;
        }
        c2074d.mo3277m(this, j6);
        return j6;
    }

    /* JADX WARN: Removed duplicated region for block: B:33:0x0089  */
    /* JADX WARN: Removed duplicated region for block: B:35:0x0097  */
    /* JADX WARN: Removed duplicated region for block: B:41:0x009b A[EDGE_INSN: B:41:0x009b->B:38:0x009b BREAK  A[LOOP:0: B:4:0x000b->B:40:?], SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:42:0x0093  */
    @Override // p167y5.InterfaceC2076f
    /* renamed from: z */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final long mo5226z() {
        /*
            r14 = this;
            long r0 = r14.f8355k
            r2 = 0
            int r0 = (r0 > r2 ? 1 : (r0 == r2 ? 0 : -1))
            if (r0 == 0) goto La2
            r0 = 0
            r1 = r0
            r4 = r2
        Lb:
            y5.r r6 = r14.f8354j
            byte[] r7 = r6.f8385a
            int r8 = r6.f8386b
            int r9 = r6.f8387c
        L13:
            if (r8 >= r9) goto L87
            r10 = r7[r8]
            r11 = 48
            if (r10 < r11) goto L22
            r11 = 57
            if (r10 > r11) goto L22
            int r11 = r10 + (-48)
            goto L39
        L22:
            r11 = 97
            if (r10 < r11) goto L2d
            r11 = 102(0x66, float:1.43E-43)
            if (r10 > r11) goto L2d
            int r11 = r10 + (-97)
            goto L37
        L2d:
            r11 = 65
            if (r10 < r11) goto L6c
            r11 = 70
            if (r10 > r11) goto L6c
            int r11 = r10 + (-65)
        L37:
            int r11 = r11 + 10
        L39:
            r12 = -1152921504606846976(0xf000000000000000, double:-3.105036184601418E231)
            long r12 = r12 & r4
            int r12 = (r12 > r2 ? 1 : (r12 == r2 ? 0 : -1))
            if (r12 != 0) goto L49
            r10 = 4
            long r4 = r4 << r10
            long r10 = (long) r11
            long r4 = r4 | r10
            int r8 = r8 + 1
            int r0 = r0 + 1
            goto L13
        L49:
            y5.d r0 = new y5.d
            r0.<init>()
            y5.d r0 = r0.mo5214g(r4)
            r0.m5204S(r10)
            java.lang.NumberFormatException r1 = new java.lang.NumberFormatException
            java.lang.String r2 = "Number too large: "
            java.lang.StringBuilder r2 = androidx.activity.result.C0052a.m104h(r2)
            java.lang.String r0 = r0.m5198M()
            r2.append(r0)
            java.lang.String r0 = r2.toString()
            r1.<init>(r0)
            throw r1
        L6c:
            if (r0 == 0) goto L70
            r1 = 1
            goto L87
        L70:
            java.lang.NumberFormatException r0 = new java.lang.NumberFormatException
            java.lang.String r1 = "Expected leading [0-9a-fA-F] character but was 0x"
            java.lang.StringBuilder r1 = androidx.activity.result.C0052a.m104h(r1)
            java.lang.String r2 = java.lang.Integer.toHexString(r10)
            r1.append(r2)
            java.lang.String r1 = r1.toString()
            r0.<init>(r1)
            throw r0
        L87:
            if (r8 != r9) goto L93
            y5.r r7 = r6.m5261a()
            r14.f8354j = r7
            p167y5.C2089s.m5265a(r6)
            goto L95
        L93:
            r6.f8386b = r8
        L95:
            if (r1 != 0) goto L9b
            y5.r r6 = r14.f8354j
            if (r6 != 0) goto Lb
        L9b:
            long r1 = r14.f8355k
            long r6 = (long) r0
            long r1 = r1 - r6
            r14.f8355k = r1
            return r4
        La2:
            java.lang.IllegalStateException r0 = new java.lang.IllegalStateException
            java.lang.String r1 = "size == 0"
            r0.<init>(r1)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: p167y5.C2074d.mo5226z():long");
    }
}
