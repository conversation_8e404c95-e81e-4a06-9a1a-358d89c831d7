package p167y5;

import androidx.activity.result.C0052a;
import java.io.IOException;

/* renamed from: y5.b */
/* loaded from: classes.dex */
public final class C2072b implements InterfaceC2092v {

    /* renamed from: j */
    public final /* synthetic */ InterfaceC2092v f8345j;

    /* renamed from: k */
    public final /* synthetic */ C2073c f8346k;

    public C2072b(C2073c c2073c, InterfaceC2092v interfaceC2092v) {
        this.f8346k = c2073c;
        this.f8345j = interfaceC2092v;
    }

    @Override // p167y5.InterfaceC2092v
    /* renamed from: b */
    public final C2093w mo3431b() {
        return this.f8346k;
    }

    @Override // p167y5.InterfaceC2092v, java.io.Closeable, java.lang.AutoCloseable
    public final void close() {
        try {
            try {
                this.f8345j.close();
                this.f8346k.m5185k(true);
            } catch (IOException e6) {
                throw this.f8346k.m5184j(e6);
            }
        } catch (Throwable th) {
            this.f8346k.m5185k(false);
            throw th;
        }
    }

    public final String toString() {
        StringBuilder m104h = C0052a.m104h("AsyncTimeout.source(");
        m104h.append(this.f8345j);
        m104h.append(")");
        return m104h.toString();
    }

    @Override // p167y5.InterfaceC2092v
    /* renamed from: y */
    public final long mo3433y(C2074d c2074d, long j6) {
        this.f8346k.m5183i();
        try {
            try {
                long mo3433y = this.f8345j.mo3433y(c2074d, 8192L);
                this.f8346k.m5185k(true);
                return mo3433y;
            } catch (IOException e6) {
                throw this.f8346k.m5184j(e6);
            }
        } catch (Throwable th) {
            this.f8346k.m5185k(false);
            throw th;
        }
    }
}
