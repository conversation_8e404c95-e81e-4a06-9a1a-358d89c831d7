package p167y5;

import java.io.EOFException;
import java.io.IOException;
import java.util.zip.DataFormatException;
import java.util.zip.Inflater;

/* renamed from: y5.l */
/* loaded from: classes.dex */
public final class C2082l implements InterfaceC2092v {

    /* renamed from: j */
    public final InterfaceC2076f f8369j;

    /* renamed from: k */
    public final Inflater f8370k;

    /* renamed from: l */
    public int f8371l;

    /* renamed from: m */
    public boolean f8372m;

    public C2082l(InterfaceC2076f interfaceC2076f, Inflater inflater) {
        this.f8369j = interfaceC2076f;
        this.f8370k = inflater;
    }

    @Override // p167y5.InterfaceC2092v
    /* renamed from: b */
    public final C2093w mo3431b() {
        return this.f8369j.mo3431b();
    }

    @Override // p167y5.InterfaceC2092v, java.io.Closeable, java.lang.AutoCloseable
    public final void close() {
        if (this.f8372m) {
            return;
        }
        this.f8370k.end();
        this.f8372m = true;
        this.f8369j.close();
    }

    /* renamed from: o */
    public final void m5251o() {
        int i6 = this.f8371l;
        if (i6 == 0) {
            return;
        }
        int remaining = i6 - this.f8370k.getRemaining();
        this.f8371l -= remaining;
        this.f8369j.mo5211a(remaining);
    }

    @Override // p167y5.InterfaceC2092v
    /* renamed from: y */
    public final long mo3433y(C2074d c2074d, long j6) {
        boolean z5;
        if (this.f8372m) {
            throw new IllegalStateException("closed");
        }
        do {
            z5 = false;
            if (this.f8370k.needsInput()) {
                m5251o();
                if (this.f8370k.getRemaining() != 0) {
                    throw new IllegalStateException("?");
                }
                if (this.f8369j.mo5224w()) {
                    z5 = true;
                } else {
                    C2088r c2088r = this.f8369j.mo5215h().f8354j;
                    int i6 = c2088r.f8387c;
                    int i7 = c2088r.f8386b;
                    int i8 = i6 - i7;
                    this.f8371l = i8;
                    this.f8370k.setInput(c2088r.f8385a, i7, i8);
                }
            }
            try {
                C2088r m5200O = c2074d.m5200O(1);
                int inflate = this.f8370k.inflate(m5200O.f8385a, m5200O.f8387c, (int) Math.min(8192L, 8192 - m5200O.f8387c));
                if (inflate > 0) {
                    m5200O.f8387c += inflate;
                    long j7 = inflate;
                    c2074d.f8355k += j7;
                    return j7;
                }
                if (!this.f8370k.finished() && !this.f8370k.needsDictionary()) {
                }
                m5251o();
                if (m5200O.f8386b != m5200O.f8387c) {
                    return -1L;
                }
                c2074d.f8354j = m5200O.m5261a();
                C2089s.m5265a(m5200O);
                return -1L;
            } catch (DataFormatException e6) {
                throw new IOException(e6);
            }
        } while (!z5);
        throw new EOFException("source exhausted prematurely");
    }
}
