package p167y5;

import androidx.activity.result.C0052a;
import java.io.EOFException;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.util.Objects;

/* renamed from: y5.q */
/* loaded from: classes.dex */
public final class C2087q implements InterfaceC2076f {

    /* renamed from: j */
    public final C2074d f8382j = new C2074d();

    /* renamed from: k */
    public final InterfaceC2092v f8383k;

    /* renamed from: l */
    public boolean f8384l;

    public C2087q(InterfaceC2092v interfaceC2092v) {
        Objects.requireNonNull(interfaceC2092v, "source == null");
        this.f8383k = interfaceC2092v;
    }

    @Override // p167y5.InterfaceC2076f
    /* renamed from: A */
    public final String mo5187A(Charset charset) {
        if (charset == null) {
            throw new IllegalArgumentException("charset == null");
        }
        this.f8382j.m5203R(this.f8383k);
        C2074d c2074d = this.f8382j;
        Objects.requireNonNull(c2074d);
        try {
            return c2074d.m5197L(c2074d.f8355k, charset);
        } catch (EOFException e6) {
            throw new AssertionError(e6);
        }
    }

    /* renamed from: C */
    public final void m5258C(byte[] bArr) {
        try {
            mo5222u(bArr.length);
            this.f8382j.m5194I(bArr);
        } catch (EOFException e6) {
            int i6 = 0;
            while (true) {
                C2074d c2074d = this.f8382j;
                long j6 = c2074d.f8355k;
                if (j6 <= 0) {
                    throw e6;
                }
                int m5191F = c2074d.m5191F(bArr, i6, (int) j6);
                if (m5191F == -1) {
                    throw new AssertionError();
                }
                i6 += m5191F;
            }
        }
    }

    /* renamed from: D */
    public final boolean m5259D(long j6) {
        C2074d c2074d;
        if (j6 < 0) {
            throw new IllegalArgumentException("byteCount < 0: " + j6);
        }
        if (this.f8384l) {
            throw new IllegalStateException("closed");
        }
        do {
            c2074d = this.f8382j;
            if (c2074d.f8355k >= j6) {
                return true;
            }
        } while (this.f8383k.mo3433y(c2074d, 8192L) != -1);
        return false;
    }

    @Override // p167y5.InterfaceC2076f
    /* renamed from: a */
    public final void mo5211a(long j6) {
        if (this.f8384l) {
            throw new IllegalStateException("closed");
        }
        while (j6 > 0) {
            C2074d c2074d = this.f8382j;
            if (c2074d.f8355k == 0 && this.f8383k.mo3433y(c2074d, 8192L) == -1) {
                throw new EOFException();
            }
            long min = Math.min(j6, this.f8382j.f8355k);
            this.f8382j.mo5211a(min);
            j6 -= min;
        }
    }

    @Override // p167y5.InterfaceC2092v
    /* renamed from: b */
    public final C2093w mo3431b() {
        return this.f8383k.mo3431b();
    }

    @Override // p167y5.InterfaceC2092v, java.io.Closeable, java.lang.AutoCloseable
    public final void close() {
        if (this.f8384l) {
            return;
        }
        this.f8384l = true;
        this.f8383k.close();
        this.f8382j.m5219o();
    }

    @Override // p167y5.InterfaceC2076f
    /* renamed from: d */
    public final boolean mo5213d(C2077g c2077g) {
        byte[] bArr = c2077g.f8358j;
        int length = bArr.length;
        if (this.f8384l) {
            throw new IllegalStateException("closed");
        }
        if (length < 0 || bArr.length - 0 < length) {
            return false;
        }
        for (int i6 = 0; i6 < length; i6++) {
            long j6 = i6 + 0;
            if (!m5259D(1 + j6)) {
                return false;
            }
            if (this.f8382j.m5189D(j6) != c2077g.f8358j[0 + i6]) {
                return false;
            }
        }
        return true;
    }

    @Override // p167y5.InterfaceC2076f
    /* renamed from: h */
    public final C2074d mo5215h() {
        return this.f8382j;
    }

    @Override // p167y5.InterfaceC2076f
    /* renamed from: i */
    public final C2077g mo5216i(long j6) {
        mo5222u(j6);
        return this.f8382j.mo5216i(j6);
    }

    @Override // java.nio.channels.Channel
    public final boolean isOpen() {
        return !this.f8384l;
    }

    @Override // p167y5.InterfaceC2076f
    /* renamed from: j */
    public final String mo5217j(long j6) {
        if (j6 < 0) {
            throw new IllegalArgumentException("limit < 0: " + j6);
        }
        long j7 = j6 == Long.MAX_VALUE ? Long.MAX_VALUE : j6 + 1;
        long m5260o = m5260o((byte) 10, 0L, j7);
        if (m5260o != -1) {
            return this.f8382j.m5199N(m5260o);
        }
        if (j7 < Long.MAX_VALUE && m5259D(j7) && this.f8382j.m5189D(j7 - 1) == 13 && m5259D(1 + j7) && this.f8382j.m5189D(j7) == 10) {
            return this.f8382j.m5199N(j7);
        }
        C2074d c2074d = new C2074d();
        C2074d c2074d2 = this.f8382j;
        c2074d2.m5188C(c2074d, 0L, Math.min(32L, c2074d2.f8355k));
        StringBuilder m104h = C0052a.m104h("\\n not found: limit=");
        m104h.append(Math.min(this.f8382j.f8355k, j6));
        m104h.append(" content=");
        m104h.append(c2074d.m5193H().mo5234j());
        m104h.append((char) 8230);
        throw new EOFException(m104h.toString());
    }

    /* renamed from: o */
    public final long m5260o(byte b6, long j6, long j7) {
        if (this.f8384l) {
            throw new IllegalStateException("closed");
        }
        long j8 = 0;
        if (j7 < 0) {
            throw new IllegalArgumentException(String.format("fromIndex=%s toIndex=%s", 0L, Long.valueOf(j7)));
        }
        while (j8 < j7) {
            long m5190E = this.f8382j.m5190E(b6, j8, j7);
            if (m5190E == -1) {
                C2074d c2074d = this.f8382j;
                long j9 = c2074d.f8355k;
                if (j9 >= j7 || this.f8383k.mo3433y(c2074d, 8192L) == -1) {
                    break;
                }
                j8 = Math.max(j8, j9);
            } else {
                return m5190E;
            }
        }
        return -1L;
    }

    @Override // java.nio.channels.ReadableByteChannel
    public final int read(ByteBuffer byteBuffer) {
        C2074d c2074d = this.f8382j;
        if (c2074d.f8355k == 0 && this.f8383k.mo3433y(c2074d, 8192L) == -1) {
            return -1;
        }
        return this.f8382j.read(byteBuffer);
    }

    @Override // p167y5.InterfaceC2076f
    public final byte readByte() {
        mo5222u(1L);
        return this.f8382j.readByte();
    }

    @Override // p167y5.InterfaceC2076f
    public final int readInt() {
        mo5222u(4L);
        return this.f8382j.readInt();
    }

    @Override // p167y5.InterfaceC2076f
    public final short readShort() {
        mo5222u(2L);
        return this.f8382j.readShort();
    }

    @Override // p167y5.InterfaceC2076f
    /* renamed from: s */
    public final String mo5221s() {
        return mo5217j(Long.MAX_VALUE);
    }

    public final String toString() {
        StringBuilder m104h = C0052a.m104h("buffer(");
        m104h.append(this.f8383k);
        m104h.append(")");
        return m104h.toString();
    }

    @Override // p167y5.InterfaceC2076f
    /* renamed from: u */
    public final void mo5222u(long j6) {
        if (!m5259D(j6)) {
            throw new EOFException();
        }
    }

    @Override // p167y5.InterfaceC2076f
    /* renamed from: w */
    public final boolean mo5224w() {
        if (this.f8384l) {
            throw new IllegalStateException("closed");
        }
        return this.f8382j.mo5224w() && this.f8383k.mo3433y(this.f8382j, 8192L) == -1;
    }

    @Override // p167y5.InterfaceC2092v
    /* renamed from: y */
    public final long mo3433y(C2074d c2074d, long j6) {
        if (c2074d == null) {
            throw new IllegalArgumentException("sink == null");
        }
        if (j6 < 0) {
            throw new IllegalArgumentException("byteCount < 0: " + j6);
        }
        if (this.f8384l) {
            throw new IllegalStateException("closed");
        }
        C2074d c2074d2 = this.f8382j;
        if (c2074d2.f8355k == 0 && this.f8383k.mo3433y(c2074d2, 8192L) == -1) {
            return -1L;
        }
        return this.f8382j.mo3433y(c2074d, Math.min(j6, this.f8382j.f8355k));
    }

    /* JADX WARN: Code restructure failed: missing block: B:20:0x0032, code lost:
    
        if (r1 == 0) goto L21;
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x0049, code lost:
    
        throw new java.lang.NumberFormatException(java.lang.String.format("Expected leading [0-9a-fA-F] character but was %#x", java.lang.Byte.valueOf(r3)));
     */
    @Override // p167y5.InterfaceC2076f
    /* renamed from: z */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final long mo5226z() {
        /*
            r6 = this;
            r0 = 1
            r6.mo5222u(r0)
            r0 = 0
            r1 = r0
        L7:
            int r2 = r1 + 1
            long r3 = (long) r2
            boolean r3 = r6.m5259D(r3)
            if (r3 == 0) goto L4a
            y5.d r3 = r6.f8382j
            long r4 = (long) r1
            byte r3 = r3.m5189D(r4)
            r4 = 48
            if (r3 < r4) goto L1f
            r4 = 57
            if (r3 <= r4) goto L30
        L1f:
            r4 = 97
            if (r3 < r4) goto L27
            r4 = 102(0x66, float:1.43E-43)
            if (r3 <= r4) goto L30
        L27:
            r4 = 65
            if (r3 < r4) goto L32
            r4 = 70
            if (r3 <= r4) goto L30
            goto L32
        L30:
            r1 = r2
            goto L7
        L32:
            if (r1 == 0) goto L35
            goto L4a
        L35:
            java.lang.NumberFormatException r1 = new java.lang.NumberFormatException
            r2 = 1
            java.lang.Object[] r2 = new java.lang.Object[r2]
            java.lang.Byte r3 = java.lang.Byte.valueOf(r3)
            r2[r0] = r3
            java.lang.String r0 = "Expected leading [0-9a-fA-F] character but was %#x"
            java.lang.String r0 = java.lang.String.format(r0, r2)
            r1.<init>(r0)
            throw r1
        L4a:
            y5.d r0 = r6.f8382j
            long r0 = r0.mo5226z()
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: p167y5.C2087q.mo5226z():long");
    }
}
