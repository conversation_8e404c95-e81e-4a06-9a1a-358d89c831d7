package p167y5;

/* renamed from: y5.i */
/* loaded from: classes.dex */
public abstract class AbstractC2079i implements InterfaceC2092v {

    /* renamed from: j */
    public final InterfaceC2092v f8362j;

    public AbstractC2079i(InterfaceC2092v interfaceC2092v) {
        if (interfaceC2092v == null) {
            throw new IllegalArgumentException("delegate == null");
        }
        this.f8362j = interfaceC2092v;
    }

    @Override // p167y5.InterfaceC2092v
    /* renamed from: b */
    public final C2093w mo3431b() {
        return this.f8362j.mo3431b();
    }

    @Override // p167y5.InterfaceC2092v, java.io.Closeable, java.lang.AutoCloseable
    public void close() {
        this.f8362j.close();
    }

    public final String toString() {
        return getClass().getSimpleName() + "(" + this.f8362j.toString() + ")";
    }
}
