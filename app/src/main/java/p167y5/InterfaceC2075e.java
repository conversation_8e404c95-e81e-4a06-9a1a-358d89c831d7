package p167y5;

import java.nio.channels.WritableByteChannel;

/* renamed from: y5.e */
/* loaded from: classes.dex */
public interface InterfaceC2075e extends InterfaceC2091u, WritableByteChannel {
    /* renamed from: c */
    InterfaceC2075e mo5212c(byte[] bArr);

    @Override // p167y5.InterfaceC2091u, java.io.Flushable
    void flush();

    /* renamed from: g */
    InterfaceC2075e mo5214g(long j6);

    /* renamed from: k */
    InterfaceC2075e mo5218k(int i6);

    /* renamed from: p */
    InterfaceC2075e mo5220p(int i6);

    /* renamed from: v */
    InterfaceC2075e mo5223v(String str);

    /* renamed from: x */
    InterfaceC2075e mo5225x(int i6);
}
