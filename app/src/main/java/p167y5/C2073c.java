package p167y5;

import java.io.IOException;
import java.io.InterruptedIOException;
import java.util.concurrent.TimeUnit;
import javax.annotation.Nullable;

/* renamed from: y5.c */
/* loaded from: classes.dex */
public class C2073c extends C2093w {

    /* renamed from: h */
    public static final long f8347h;

    /* renamed from: i */
    public static final long f8348i;

    /* renamed from: j */
    @Nullable
    public static C2073c f8349j;

    /* renamed from: e */
    public boolean f8350e;

    /* renamed from: f */
    @Nullable
    public C2073c f8351f;

    /* renamed from: g */
    public long f8352g;

    /* renamed from: y5.c$a */
    public static final class a extends Thread {
        public a() {
            super("Okio Watchdog");
            setDaemon(true);
        }

        /* JADX WARN: Code restructure failed: missing block: B:19:0x0015, code lost:
        
            r1.mo3509n();
         */
        @Override // java.lang.Thread, java.lang.Runnable
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct code enable 'Show inconsistent code' option in preferences
        */
        public final void run() {
            /*
                r3 = this;
            L0:
                java.lang.Class<y5.c> r0 = p167y5.C2073c.class
                monitor-enter(r0)     // Catch: java.lang.InterruptedException -> L0
                y5.c r1 = p167y5.C2073c.m5182h()     // Catch: java.lang.Throwable -> L19
                if (r1 != 0) goto Lb
                monitor-exit(r0)     // Catch: java.lang.Throwable -> L19
                goto L0
            Lb:
                y5.c r2 = p167y5.C2073c.f8349j     // Catch: java.lang.Throwable -> L19
                if (r1 != r2) goto L14
                r1 = 0
                p167y5.C2073c.f8349j = r1     // Catch: java.lang.Throwable -> L19
                monitor-exit(r0)     // Catch: java.lang.Throwable -> L19
                return
            L14:
                monitor-exit(r0)     // Catch: java.lang.Throwable -> L19
                r1.mo3509n()     // Catch: java.lang.InterruptedException -> L0
                goto L0
            L19:
                r1 = move-exception
                monitor-exit(r0)     // Catch: java.lang.Throwable -> L19
                throw r1     // Catch: java.lang.InterruptedException -> L0
            */
            throw new UnsupportedOperationException("Method not decompiled: p167y5.C2073c.a.run():void");
        }
    }

    static {
        long millis = TimeUnit.SECONDS.toMillis(60L);
        f8347h = millis;
        f8348i = TimeUnit.MILLISECONDS.toNanos(millis);
    }

    @Nullable
    /* renamed from: h */
    public static C2073c m5182h() {
        C2073c c2073c = f8349j.f8351f;
        long nanoTime = System.nanoTime();
        if (c2073c == null) {
            C2073c.class.wait(f8347h);
            if (f8349j.f8351f != null || System.nanoTime() - nanoTime < f8348i) {
                return null;
            }
            return f8349j;
        }
        long j6 = c2073c.f8352g - nanoTime;
        if (j6 > 0) {
            long j7 = j6 / 1000000;
            C2073c.class.wait(j7, (int) (j6 - (1000000 * j7)));
            return null;
        }
        f8349j.f8351f = c2073c.f8351f;
        c2073c.f8351f = null;
        return c2073c;
    }

    /* JADX WARN: Removed duplicated region for block: B:29:0x0065 A[Catch: all -> 0x0070, TRY_LEAVE, TryCatch #0 {, blocks: (B:11:0x0017, B:13:0x001b, B:14:0x002a, B:17:0x0032, B:18:0x003e, B:19:0x004a, B:20:0x004f, B:22:0x0053, B:27:0x005d, B:29:0x0065, B:35:0x0044, B:36:0x006a, B:37:0x006f), top: B:10:0x0017 }] */
    /* renamed from: i */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m5183i() {
        /*
            r9 = this;
            boolean r0 = r9.f8350e
            if (r0 != 0) goto L73
            long r0 = r9.f8399c
            boolean r2 = r9.f8397a
            r3 = 0
            int r3 = (r0 > r3 ? 1 : (r0 == r3 ? 0 : -1))
            if (r3 != 0) goto L11
            if (r2 != 0) goto L11
            return
        L11:
            r4 = 1
            r9.f8350e = r4
            java.lang.Class<y5.c> r4 = p167y5.C2073c.class
            monitor-enter(r4)
            y5.c r5 = p167y5.C2073c.f8349j     // Catch: java.lang.Throwable -> L70
            if (r5 != 0) goto L2a
            y5.c r5 = new y5.c     // Catch: java.lang.Throwable -> L70
            r5.<init>()     // Catch: java.lang.Throwable -> L70
            p167y5.C2073c.f8349j = r5     // Catch: java.lang.Throwable -> L70
            y5.c$a r5 = new y5.c$a     // Catch: java.lang.Throwable -> L70
            r5.<init>()     // Catch: java.lang.Throwable -> L70
            r5.start()     // Catch: java.lang.Throwable -> L70
        L2a:
            long r5 = java.lang.System.nanoTime()     // Catch: java.lang.Throwable -> L70
            if (r3 == 0) goto L3c
            if (r2 == 0) goto L3c
            long r2 = r9.mo5244c()     // Catch: java.lang.Throwable -> L70
            long r2 = r2 - r5
            long r0 = java.lang.Math.min(r0, r2)     // Catch: java.lang.Throwable -> L70
            goto L3e
        L3c:
            if (r3 == 0) goto L42
        L3e:
            long r0 = r0 + r5
            r9.f8352g = r0     // Catch: java.lang.Throwable -> L70
            goto L4a
        L42:
            if (r2 == 0) goto L6a
            long r0 = r9.mo5244c()     // Catch: java.lang.Throwable -> L70
            r9.f8352g = r0     // Catch: java.lang.Throwable -> L70
        L4a:
            long r0 = r9.f8352g     // Catch: java.lang.Throwable -> L70
            long r0 = r0 - r5
            y5.c r2 = p167y5.C2073c.f8349j     // Catch: java.lang.Throwable -> L70
        L4f:
            y5.c r3 = r2.f8351f     // Catch: java.lang.Throwable -> L70
            if (r3 == 0) goto L5d
            long r7 = r3.f8352g     // Catch: java.lang.Throwable -> L70
            long r7 = r7 - r5
            int r7 = (r0 > r7 ? 1 : (r0 == r7 ? 0 : -1))
            if (r7 >= 0) goto L5b
            goto L5d
        L5b:
            r2 = r3
            goto L4f
        L5d:
            r9.f8351f = r3     // Catch: java.lang.Throwable -> L70
            r2.f8351f = r9     // Catch: java.lang.Throwable -> L70
            y5.c r0 = p167y5.C2073c.f8349j     // Catch: java.lang.Throwable -> L70
            if (r2 != r0) goto L68
            r4.notify()     // Catch: java.lang.Throwable -> L70
        L68:
            monitor-exit(r4)
            return
        L6a:
            java.lang.AssertionError r0 = new java.lang.AssertionError     // Catch: java.lang.Throwable -> L70
            r0.<init>()     // Catch: java.lang.Throwable -> L70
            throw r0     // Catch: java.lang.Throwable -> L70
        L70:
            r0 = move-exception
            monitor-exit(r4)
            throw r0
        L73:
            java.lang.IllegalStateException r0 = new java.lang.IllegalStateException
            java.lang.String r1 = "Unbalanced enter/exit"
            r0.<init>(r1)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: p167y5.C2073c.m5183i():void");
    }

    /* renamed from: j */
    public final IOException m5184j(IOException iOException) {
        return !m5186l() ? iOException : mo3508m(iOException);
    }

    /* renamed from: k */
    public final void m5185k(boolean z5) {
        if (m5186l() && z5) {
            throw mo3508m(null);
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:14:0x0013, code lost:
    
        r2.f8351f = r4.f8351f;
        r4.f8351f = null;
     */
    /* renamed from: l */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean m5186l() {
        /*
            r4 = this;
            boolean r0 = r4.f8350e
            r1 = 0
            if (r0 != 0) goto L6
            return r1
        L6:
            r4.f8350e = r1
            java.lang.Class<y5.c> r0 = p167y5.C2073c.class
            monitor-enter(r0)
            y5.c r2 = p167y5.C2073c.f8349j     // Catch: java.lang.Throwable -> L20
        Ld:
            if (r2 == 0) goto L1d
            y5.c r3 = r2.f8351f     // Catch: java.lang.Throwable -> L20
            if (r3 != r4) goto L1b
            y5.c r3 = r4.f8351f     // Catch: java.lang.Throwable -> L20
            r2.f8351f = r3     // Catch: java.lang.Throwable -> L20
            r2 = 0
            r4.f8351f = r2     // Catch: java.lang.Throwable -> L20
            goto L1e
        L1b:
            r2 = r3
            goto Ld
        L1d:
            r1 = 1
        L1e:
            monitor-exit(r0)
            return r1
        L20:
            r1 = move-exception
            monitor-exit(r0)
            throw r1
        */
        throw new UnsupportedOperationException("Method not decompiled: p167y5.C2073c.m5186l():boolean");
    }

    /* renamed from: m */
    public IOException mo3508m(@Nullable IOException iOException) {
        InterruptedIOException interruptedIOException = new InterruptedIOException("timeout");
        if (iOException != null) {
            interruptedIOException.initCause(iOException);
        }
        return interruptedIOException;
    }

    /* renamed from: n */
    public void mo3509n() {
    }
}
