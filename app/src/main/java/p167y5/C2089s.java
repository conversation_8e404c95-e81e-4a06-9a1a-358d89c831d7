package p167y5;

import javax.annotation.Nullable;

/* renamed from: y5.s */
/* loaded from: classes.dex */
public final class C2089s {

    /* renamed from: a */
    @Nullable
    public static C2088r f8392a;

    /* renamed from: b */
    public static long f8393b;

    /* renamed from: a */
    public static void m5265a(C2088r c2088r) {
        if (c2088r.f8390f != null || c2088r.f8391g != null) {
            throw new IllegalArgumentException();
        }
        if (c2088r.f8388d) {
            return;
        }
        synchronized (C2089s.class) {
            long j6 = f8393b + 8192;
            if (j6 > 65536) {
                return;
            }
            f8393b = j6;
            c2088r.f8390f = f8392a;
            c2088r.f8387c = 0;
            c2088r.f8386b = 0;
            f8392a = c2088r;
        }
    }

    /* renamed from: b */
    public static C2088r m5266b() {
        synchronized (C2089s.class) {
            C2088r c2088r = f8392a;
            if (c2088r == null) {
                return new C2088r();
            }
            f8392a = c2088r.f8390f;
            c2088r.f8390f = null;
            f8393b -= 8192;
            return c2088r;
        }
    }
}
