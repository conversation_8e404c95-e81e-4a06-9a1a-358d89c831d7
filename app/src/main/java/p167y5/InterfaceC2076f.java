package p167y5;

import java.nio.channels.ReadableByteChannel;
import java.nio.charset.Charset;

/* renamed from: y5.f */
/* loaded from: classes.dex */
public interface InterfaceC2076f extends InterfaceC2092v, ReadableByteChannel {
    /* renamed from: A */
    String mo5187A(Charset charset);

    /* renamed from: a */
    void mo5211a(long j6);

    /* renamed from: d */
    boolean mo5213d(C2077g c2077g);

    /* renamed from: h */
    C2074d mo5215h();

    /* renamed from: i */
    C2077g mo5216i(long j6);

    /* renamed from: j */
    String mo5217j(long j6);

    byte readByte();

    int readInt();

    short readShort();

    /* renamed from: s */
    String mo5221s();

    /* renamed from: u */
    void mo5222u(long j6);

    /* renamed from: w */
    boolean mo5224w();

    /* renamed from: z */
    long mo5226z();
}
