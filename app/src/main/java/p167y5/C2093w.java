package p167y5;

import java.io.InterruptedIOException;
import java.util.concurrent.TimeUnit;

/* renamed from: y5.w */
/* loaded from: classes.dex */
public class C2093w {

    /* renamed from: d */
    public static final a f8396d = new a();

    /* renamed from: a */
    public boolean f8397a;

    /* renamed from: b */
    public long f8398b;

    /* renamed from: c */
    public long f8399c;

    /* renamed from: y5.w$a */
    public class a extends C2093w {
        @Override // p167y5.C2093w
        /* renamed from: d */
        public final C2093w mo5245d(long j6) {
            return this;
        }

        @Override // p167y5.C2093w
        /* renamed from: f */
        public final void mo5247f() {
        }

        @Override // p167y5.C2093w
        /* renamed from: g */
        public final C2093w mo5248g(long j6) {
            return this;
        }
    }

    /* renamed from: a */
    public C2093w mo5242a() {
        this.f8397a = false;
        return this;
    }

    /* renamed from: b */
    public C2093w mo5243b() {
        this.f8399c = 0L;
        return this;
    }

    /* renamed from: c */
    public long mo5244c() {
        if (this.f8397a) {
            return this.f8398b;
        }
        throw new IllegalStateException("No deadline");
    }

    /* renamed from: d */
    public C2093w mo5245d(long j6) {
        this.f8397a = true;
        this.f8398b = j6;
        return this;
    }

    /* renamed from: e */
    public boolean mo5246e() {
        return this.f8397a;
    }

    /* renamed from: f */
    public void mo5247f() {
        if (Thread.interrupted()) {
            throw new InterruptedIOException("thread interrupted");
        }
        if (this.f8397a && this.f8398b - System.nanoTime() <= 0) {
            throw new InterruptedIOException("deadline reached");
        }
    }

    /* renamed from: g */
    public C2093w mo5248g(long j6) {
        TimeUnit timeUnit = TimeUnit.MILLISECONDS;
        if (j6 >= 0) {
            if (timeUnit == null) {
                throw new IllegalArgumentException("unit == null");
            }
            this.f8399c = timeUnit.toNanos(j6);
            return this;
        }
        throw new IllegalArgumentException("timeout < 0: " + j6);
    }
}
