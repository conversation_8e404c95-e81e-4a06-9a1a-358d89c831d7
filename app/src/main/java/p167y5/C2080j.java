package p167y5;

import java.util.concurrent.TimeUnit;

/* renamed from: y5.j */
/* loaded from: classes.dex */
public final class C2080j extends C2093w {

    /* renamed from: e */
    public C2093w f8363e;

    public C2080j(C2093w c2093w) {
        if (c2093w == null) {
            throw new IllegalArgumentException("delegate == null");
        }
        this.f8363e = c2093w;
    }

    @Override // p167y5.C2093w
    /* renamed from: a */
    public final C2093w mo5242a() {
        return this.f8363e.mo5242a();
    }

    @Override // p167y5.C2093w
    /* renamed from: b */
    public final C2093w mo5243b() {
        return this.f8363e.mo5243b();
    }

    @Override // p167y5.C2093w
    /* renamed from: c */
    public final long mo5244c() {
        return this.f8363e.mo5244c();
    }

    @Override // p167y5.C2093w
    /* renamed from: d */
    public final C2093w mo5245d(long j6) {
        return this.f8363e.mo5245d(j6);
    }

    @Override // p167y5.C2093w
    /* renamed from: e */
    public final boolean mo5246e() {
        return this.f8363e.mo5246e();
    }

    @Override // p167y5.C2093w
    /* renamed from: f */
    public final void mo5247f() {
        this.f8363e.mo5247f();
    }

    @Override // p167y5.C2093w
    /* renamed from: g */
    public final C2093w mo5248g(long j6) {
        TimeUnit timeUnit = TimeUnit.MILLISECONDS;
        return this.f8363e.mo5248g(j6);
    }
}
