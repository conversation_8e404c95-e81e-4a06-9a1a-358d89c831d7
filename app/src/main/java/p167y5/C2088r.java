package p167y5;

import javax.annotation.Nullable;

/* renamed from: y5.r */
/* loaded from: classes.dex */
public final class C2088r {

    /* renamed from: a */
    public final byte[] f8385a;

    /* renamed from: b */
    public int f8386b;

    /* renamed from: c */
    public int f8387c;

    /* renamed from: d */
    public boolean f8388d;

    /* renamed from: e */
    public boolean f8389e;

    /* renamed from: f */
    public C2088r f8390f;

    /* renamed from: g */
    public C2088r f8391g;

    public C2088r() {
        this.f8385a = new byte[8192];
        this.f8389e = true;
        this.f8388d = false;
    }

    public C2088r(byte[] bArr, int i6, int i7) {
        this.f8385a = bArr;
        this.f8386b = i6;
        this.f8387c = i7;
        this.f8388d = true;
        this.f8389e = false;
    }

    @Nullable
    /* renamed from: a */
    public final C2088r m5261a() {
        C2088r c2088r = this.f8390f;
        C2088r c2088r2 = c2088r != this ? c2088r : null;
        C2088r c2088r3 = this.f8391g;
        c2088r3.f8390f = c2088r;
        this.f8390f.f8391g = c2088r3;
        this.f8390f = null;
        this.f8391g = null;
        return c2088r2;
    }

    /* renamed from: b */
    public final C2088r m5262b(C2088r c2088r) {
        c2088r.f8391g = this;
        c2088r.f8390f = this.f8390f;
        this.f8390f.f8391g = c2088r;
        this.f8390f = c2088r;
        return c2088r;
    }

    /* renamed from: c */
    public final C2088r m5263c() {
        this.f8388d = true;
        return new C2088r(this.f8385a, this.f8386b, this.f8387c);
    }

    /* renamed from: d */
    public final void m5264d(C2088r c2088r, int i6) {
        if (!c2088r.f8389e) {
            throw new IllegalArgumentException();
        }
        int i7 = c2088r.f8387c;
        if (i7 + i6 > 8192) {
            if (c2088r.f8388d) {
                throw new IllegalArgumentException();
            }
            int i8 = c2088r.f8386b;
            if ((i7 + i6) - i8 > 8192) {
                throw new IllegalArgumentException();
            }
            byte[] bArr = c2088r.f8385a;
            System.arraycopy(bArr, i8, bArr, 0, i7 - i8);
            c2088r.f8387c -= c2088r.f8386b;
            c2088r.f8386b = 0;
        }
        System.arraycopy(this.f8385a, this.f8386b, c2088r.f8385a, c2088r.f8387c, i6);
        c2088r.f8387c += i6;
        this.f8386b += i6;
    }
}
