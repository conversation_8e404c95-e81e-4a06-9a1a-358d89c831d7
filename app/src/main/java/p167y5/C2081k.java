package p167y5;

import java.io.EOFException;
import java.io.IOException;
import java.util.logging.Logger;
import java.util.zip.CRC32;
import java.util.zip.Inflater;

/* renamed from: y5.k */
/* loaded from: classes.dex */
public final class C2081k implements InterfaceC2092v {

    /* renamed from: k */
    public final C2087q f8365k;

    /* renamed from: l */
    public final Inflater f8366l;

    /* renamed from: m */
    public final C2082l f8367m;

    /* renamed from: j */
    public int f8364j = 0;

    /* renamed from: n */
    public final CRC32 f8368n = new CRC32();

    public C2081k(InterfaceC2092v interfaceC2092v) {
        if (interfaceC2092v == null) {
            throw new IllegalArgumentException("source == null");
        }
        Inflater inflater = new Inflater(true);
        this.f8366l = inflater;
        Logger logger = C2084n.f8375a;
        C2087q c2087q = new C2087q(interfaceC2092v);
        this.f8365k = c2087q;
        this.f8367m = new C2082l(c2087q, inflater);
    }

    /* renamed from: C */
    public final void m5249C(C2074d c2074d, long j6, long j7) {
        C2088r c2088r = c2074d.f8354j;
        while (true) {
            int i6 = c2088r.f8387c;
            int i7 = c2088r.f8386b;
            if (j6 < i6 - i7) {
                break;
            }
            j6 -= i6 - i7;
            c2088r = c2088r.f8390f;
        }
        while (j7 > 0) {
            int min = (int) Math.min(c2088r.f8387c - r6, j7);
            this.f8368n.update(c2088r.f8385a, (int) (c2088r.f8386b + j6), min);
            j7 -= min;
            c2088r = c2088r.f8390f;
            j6 = 0;
        }
    }

    @Override // p167y5.InterfaceC2092v
    /* renamed from: b */
    public final C2093w mo3431b() {
        return this.f8365k.mo3431b();
    }

    @Override // p167y5.InterfaceC2092v, java.io.Closeable, java.lang.AutoCloseable
    public final void close() {
        this.f8367m.close();
    }

    /* renamed from: o */
    public final void m5250o(String str, int i6, int i7) {
        if (i7 != i6) {
            throw new IOException(String.format("%s: actual 0x%08x != expected 0x%08x", str, Integer.valueOf(i7), Integer.valueOf(i6)));
        }
    }

    @Override // p167y5.InterfaceC2092v
    /* renamed from: y */
    public final long mo3433y(C2074d c2074d, long j6) {
        long j7;
        if (this.f8364j == 0) {
            this.f8365k.mo5222u(10L);
            byte m5189D = this.f8365k.f8382j.m5189D(3L);
            boolean z5 = ((m5189D >> 1) & 1) == 1;
            if (z5) {
                m5249C(this.f8365k.f8382j, 0L, 10L);
            }
            C2087q c2087q = this.f8365k;
            c2087q.mo5222u(2L);
            m5250o("ID1ID2", 8075, c2087q.f8382j.readShort());
            this.f8365k.mo5211a(8L);
            if (((m5189D >> 2) & 1) == 1) {
                this.f8365k.mo5222u(2L);
                if (z5) {
                    m5249C(this.f8365k.f8382j, 0L, 2L);
                }
                long m5196K = this.f8365k.f8382j.m5196K();
                this.f8365k.mo5222u(m5196K);
                if (z5) {
                    j7 = m5196K;
                    m5249C(this.f8365k.f8382j, 0L, m5196K);
                } else {
                    j7 = m5196K;
                }
                this.f8365k.mo5211a(j7);
            }
            if (((m5189D >> 3) & 1) == 1) {
                long m5260o = this.f8365k.m5260o((byte) 0, 0L, Long.MAX_VALUE);
                if (m5260o == -1) {
                    throw new EOFException();
                }
                if (z5) {
                    m5249C(this.f8365k.f8382j, 0L, m5260o + 1);
                }
                this.f8365k.mo5211a(m5260o + 1);
            }
            if (((m5189D >> 4) & 1) == 1) {
                long m5260o2 = this.f8365k.m5260o((byte) 0, 0L, Long.MAX_VALUE);
                if (m5260o2 == -1) {
                    throw new EOFException();
                }
                if (z5) {
                    m5249C(this.f8365k.f8382j, 0L, m5260o2 + 1);
                }
                this.f8365k.mo5211a(m5260o2 + 1);
            }
            if (z5) {
                C2087q c2087q2 = this.f8365k;
                c2087q2.mo5222u(2L);
                m5250o("FHCRC", c2087q2.f8382j.m5196K(), (short) this.f8368n.getValue());
                this.f8368n.reset();
            }
            this.f8364j = 1;
        }
        if (this.f8364j == 1) {
            long j8 = c2074d.f8355k;
            long mo3433y = this.f8367m.mo3433y(c2074d, 8192L);
            if (mo3433y != -1) {
                m5249C(c2074d, j8, mo3433y);
                return mo3433y;
            }
            this.f8364j = 2;
        }
        if (this.f8364j == 2) {
            C2087q c2087q3 = this.f8365k;
            c2087q3.mo5222u(4L);
            m5250o("CRC", c2087q3.f8382j.m5195J(), (int) this.f8368n.getValue());
            C2087q c2087q4 = this.f8365k;
            c2087q4.mo5222u(4L);
            m5250o("ISIZE", c2087q4.f8382j.m5195J(), (int) this.f8366l.getBytesWritten());
            this.f8364j = 3;
            if (!this.f8365k.mo5224w()) {
                throw new IOException("gzip finished without exhausting source");
            }
        }
        return -1L;
    }
}
