package p167y5;

import java.nio.charset.Charset;

/* renamed from: y5.x */
/* loaded from: classes.dex */
public final class C2094x {

    /* renamed from: a */
    public static final Charset f8400a = Charset.forName("UTF-8");

    /* renamed from: a */
    public static void m5270a(long j6, long j7, long j8) {
        if ((j7 | j8) < 0 || j7 > j6 || j6 - j7 < j8) {
            throw new ArrayIndexOutOfBoundsException(String.format("size=%s offset=%s byteCount=%s", Long.valueOf(j6), Long.valueOf(j7), Long.valueOf(j8)));
        }
    }
}
