package p167y5;

import androidx.activity.result.C0052a;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.util.Objects;

/* renamed from: y5.p */
/* loaded from: classes.dex */
public final class C2086p implements InterfaceC2075e {

    /* renamed from: j */
    public final C2074d f8379j = new C2074d();

    /* renamed from: k */
    public final InterfaceC2091u f8380k;

    /* renamed from: l */
    public boolean f8381l;

    public C2086p(InterfaceC2091u interfaceC2091u) {
        this.f8380k = interfaceC2091u;
    }

    /* renamed from: C */
    public final InterfaceC2075e m5256C(byte[] bArr, int i6, int i7) {
        if (this.f8381l) {
            throw new IllegalStateException("closed");
        }
        this.f8379j.m5202Q(bArr, i6, i7);
        m5257o();
        return this;
    }

    @Override // p167y5.InterfaceC2091u
    /* renamed from: b */
    public final C2093w mo3434b() {
        return this.f8380k.mo3434b();
    }

    @Override // p167y5.InterfaceC2075e
    /* renamed from: c */
    public final InterfaceC2075e mo5212c(byte[] bArr) {
        if (this.f8381l) {
            throw new IllegalStateException("closed");
        }
        this.f8379j.m5201P(bArr);
        m5257o();
        return this;
    }

    @Override // p167y5.InterfaceC2091u, java.io.Closeable, java.lang.AutoCloseable
    public final void close() {
        if (this.f8381l) {
            return;
        }
        Throwable th = null;
        try {
            C2074d c2074d = this.f8379j;
            long j6 = c2074d.f8355k;
            if (j6 > 0) {
                this.f8380k.mo3277m(c2074d, j6);
            }
        } catch (Throwable th2) {
            th = th2;
        }
        try {
            this.f8380k.close();
        } catch (Throwable th3) {
            if (th == null) {
                th = th3;
            }
        }
        this.f8381l = true;
        if (th == null) {
            return;
        }
        Charset charset = C2094x.f8400a;
        throw th;
    }

    @Override // p167y5.InterfaceC2075e, p167y5.InterfaceC2091u, java.io.Flushable
    public final void flush() {
        if (this.f8381l) {
            throw new IllegalStateException("closed");
        }
        C2074d c2074d = this.f8379j;
        long j6 = c2074d.f8355k;
        if (j6 > 0) {
            this.f8380k.mo3277m(c2074d, j6);
        }
        this.f8380k.flush();
    }

    @Override // p167y5.InterfaceC2075e
    /* renamed from: g */
    public final InterfaceC2075e mo5214g(long j6) {
        if (this.f8381l) {
            throw new IllegalStateException("closed");
        }
        this.f8379j.mo5214g(j6);
        m5257o();
        return this;
    }

    @Override // java.nio.channels.Channel
    public final boolean isOpen() {
        return !this.f8381l;
    }

    @Override // p167y5.InterfaceC2075e
    /* renamed from: k */
    public final InterfaceC2075e mo5218k(int i6) {
        if (this.f8381l) {
            throw new IllegalStateException("closed");
        }
        this.f8379j.m5207V(i6);
        m5257o();
        return this;
    }

    @Override // p167y5.InterfaceC2091u
    /* renamed from: m */
    public final void mo3277m(C2074d c2074d, long j6) {
        if (this.f8381l) {
            throw new IllegalStateException("closed");
        }
        this.f8379j.mo3277m(c2074d, j6);
        m5257o();
    }

    /* renamed from: o */
    public final InterfaceC2075e m5257o() {
        if (this.f8381l) {
            throw new IllegalStateException("closed");
        }
        C2074d c2074d = this.f8379j;
        long j6 = c2074d.f8355k;
        if (j6 == 0) {
            j6 = 0;
        } else {
            C2088r c2088r = c2074d.f8354j.f8391g;
            if (c2088r.f8387c < 8192 && c2088r.f8389e) {
                j6 -= r6 - c2088r.f8386b;
            }
        }
        if (j6 > 0) {
            this.f8380k.mo3277m(c2074d, j6);
        }
        return this;
    }

    @Override // p167y5.InterfaceC2075e
    /* renamed from: p */
    public final InterfaceC2075e mo5220p(int i6) {
        if (this.f8381l) {
            throw new IllegalStateException("closed");
        }
        this.f8379j.m5206U(i6);
        m5257o();
        return this;
    }

    public final String toString() {
        StringBuilder m104h = C0052a.m104h("buffer(");
        m104h.append(this.f8380k);
        m104h.append(")");
        return m104h.toString();
    }

    @Override // p167y5.InterfaceC2075e
    /* renamed from: v */
    public final InterfaceC2075e mo5223v(String str) {
        if (this.f8381l) {
            throw new IllegalStateException("closed");
        }
        C2074d c2074d = this.f8379j;
        Objects.requireNonNull(c2074d);
        c2074d.m5209X(str, 0, str.length());
        m5257o();
        return this;
    }

    @Override // java.nio.channels.WritableByteChannel
    public final int write(ByteBuffer byteBuffer) {
        if (this.f8381l) {
            throw new IllegalStateException("closed");
        }
        int write = this.f8379j.write(byteBuffer);
        m5257o();
        return write;
    }

    @Override // p167y5.InterfaceC2075e
    /* renamed from: x */
    public final InterfaceC2075e mo5225x(int i6) {
        if (this.f8381l) {
            throw new IllegalStateException("closed");
        }
        this.f8379j.m5204S(i6);
        m5257o();
        return this;
    }
}
