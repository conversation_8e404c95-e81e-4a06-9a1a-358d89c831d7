package p167y5;

import java.nio.charset.Charset;
import java.util.Arrays;

/* renamed from: y5.t */
/* loaded from: classes.dex */
public final class C2090t extends C2077g {

    /* renamed from: o */
    public final transient byte[][] f8394o;

    /* renamed from: p */
    public final transient int[] f8395p;

    public C2090t(C2074d c2074d, int i6) {
        super(null);
        C2094x.m5270a(c2074d.f8355k, 0L, i6);
        C2088r c2088r = c2074d.f8354j;
        int i7 = 0;
        int i8 = 0;
        int i9 = 0;
        while (i8 < i6) {
            int i10 = c2088r.f8387c;
            int i11 = c2088r.f8386b;
            if (i10 == i11) {
                throw new AssertionError("s.limit == s.pos");
            }
            i8 += i10 - i11;
            i9++;
            c2088r = c2088r.f8390f;
        }
        this.f8394o = new byte[i9][];
        this.f8395p = new int[i9 * 2];
        C2088r c2088r2 = c2074d.f8354j;
        int i12 = 0;
        while (i7 < i6) {
            byte[][] bArr = this.f8394o;
            bArr[i12] = c2088r2.f8385a;
            int i13 = c2088r2.f8387c;
            int i14 = c2088r2.f8386b;
            int i15 = (i13 - i14) + i7;
            i7 = i15 > i6 ? i6 : i15;
            int[] iArr = this.f8395p;
            iArr[i12] = i7;
            iArr[bArr.length + i12] = i14;
            c2088r2.f8388d = true;
            i12++;
            c2088r2 = c2088r2.f8390f;
        }
    }

    @Override // p167y5.C2077g
    /* renamed from: d */
    public final String mo5231d() {
        return m5269u().mo5231d();
    }

    @Override // p167y5.C2077g
    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof C2077g) {
            C2077g c2077g = (C2077g) obj;
            if (c2077g.mo5237n() == mo5237n() && mo5236m(c2077g, mo5237n())) {
                return true;
            }
        }
        return false;
    }

    @Override // p167y5.C2077g
    public final int hashCode() {
        int i6 = this.f8359k;
        if (i6 != 0) {
            return i6;
        }
        int length = this.f8394o.length;
        int i7 = 0;
        int i8 = 1;
        int i9 = 0;
        while (i7 < length) {
            byte[] bArr = this.f8394o[i7];
            int[] iArr = this.f8395p;
            int i10 = iArr[length + i7];
            int i11 = iArr[i7];
            int i12 = (i11 - i9) + i10;
            while (i10 < i12) {
                i8 = (i8 * 31) + bArr[i10];
                i10++;
            }
            i7++;
            i9 = i11;
        }
        this.f8359k = i8;
        return i8;
    }

    @Override // p167y5.C2077g
    /* renamed from: i */
    public final byte mo5233i(int i6) {
        C2094x.m5270a(this.f8395p[this.f8394o.length - 1], i6, 1L);
        int m5267s = m5267s(i6);
        int i7 = m5267s == 0 ? 0 : this.f8395p[m5267s - 1];
        int[] iArr = this.f8395p;
        byte[][] bArr = this.f8394o;
        return bArr[m5267s][(i6 - i7) + iArr[bArr.length + m5267s]];
    }

    @Override // p167y5.C2077g
    /* renamed from: j */
    public final String mo5234j() {
        return m5269u().mo5234j();
    }

    @Override // p167y5.C2077g
    /* renamed from: l */
    public final boolean mo5235l(int i6, byte[] bArr, int i7, int i8) {
        if (i6 < 0 || i6 > mo5237n() - i8 || i7 < 0 || i7 > bArr.length - i8) {
            return false;
        }
        int m5267s = m5267s(i6);
        while (true) {
            boolean z5 = true;
            if (i8 <= 0) {
                return true;
            }
            int i9 = m5267s == 0 ? 0 : this.f8395p[m5267s - 1];
            int min = Math.min(i8, ((this.f8395p[m5267s] - i9) + i9) - i6);
            int[] iArr = this.f8395p;
            byte[][] bArr2 = this.f8394o;
            int i10 = (i6 - i9) + iArr[bArr2.length + m5267s];
            byte[] bArr3 = bArr2[m5267s];
            Charset charset = C2094x.f8400a;
            int i11 = 0;
            while (true) {
                if (i11 >= min) {
                    break;
                }
                if (bArr3[i11 + i10] != bArr[i11 + i7]) {
                    z5 = false;
                    break;
                }
                i11++;
            }
            if (!z5) {
                return false;
            }
            i6 += min;
            i7 += min;
            i8 -= min;
            m5267s++;
        }
    }

    @Override // p167y5.C2077g
    /* renamed from: m */
    public final boolean mo5236m(C2077g c2077g, int i6) {
        if (mo5237n() - i6 < 0) {
            return false;
        }
        int m5267s = m5267s(0);
        int i7 = 0;
        int i8 = 0;
        while (i6 > 0) {
            int i9 = m5267s == 0 ? 0 : this.f8395p[m5267s - 1];
            int min = Math.min(i6, ((this.f8395p[m5267s] - i9) + i9) - i7);
            int[] iArr = this.f8395p;
            byte[][] bArr = this.f8394o;
            if (!c2077g.mo5235l(i8, bArr[m5267s], (i7 - i9) + iArr[bArr.length + m5267s], min)) {
                return false;
            }
            i7 += min;
            i8 += min;
            i6 -= min;
            m5267s++;
        }
        return true;
    }

    @Override // p167y5.C2077g
    /* renamed from: n */
    public final int mo5237n() {
        return this.f8395p[this.f8394o.length - 1];
    }

    @Override // p167y5.C2077g
    /* renamed from: o */
    public final C2077g mo5238o() {
        return m5269u().mo5238o();
    }

    @Override // p167y5.C2077g
    /* renamed from: p */
    public final C2077g mo5239p() {
        return m5269u().mo5239p();
    }

    @Override // p167y5.C2077g
    /* renamed from: q */
    public final String mo5240q() {
        return m5269u().mo5240q();
    }

    @Override // p167y5.C2077g
    /* renamed from: r */
    public final void mo5241r(C2074d c2074d) {
        int length = this.f8394o.length;
        int i6 = 0;
        int i7 = 0;
        while (i6 < length) {
            int[] iArr = this.f8395p;
            int i8 = iArr[length + i6];
            int i9 = iArr[i6];
            C2088r c2088r = new C2088r(this.f8394o[i6], i8, (i8 + i9) - i7);
            C2088r c2088r2 = c2074d.f8354j;
            if (c2088r2 == null) {
                c2088r.f8391g = c2088r;
                c2088r.f8390f = c2088r;
                c2074d.f8354j = c2088r;
            } else {
                c2088r2.f8391g.m5262b(c2088r);
            }
            i6++;
            i7 = i9;
        }
        c2074d.f8355k += i7;
    }

    /* renamed from: s */
    public final int m5267s(int i6) {
        int binarySearch = Arrays.binarySearch(this.f8395p, 0, this.f8394o.length, i6 + 1);
        return binarySearch >= 0 ? binarySearch : ~binarySearch;
    }

    /* renamed from: t */
    public final byte[] m5268t() {
        int[] iArr = this.f8395p;
        byte[][] bArr = this.f8394o;
        byte[] bArr2 = new byte[iArr[bArr.length - 1]];
        int length = bArr.length;
        int i6 = 0;
        int i7 = 0;
        while (i6 < length) {
            int[] iArr2 = this.f8395p;
            int i8 = iArr2[length + i6];
            int i9 = iArr2[i6];
            System.arraycopy(this.f8394o[i6], i8, bArr2, i7, i9 - i7);
            i6++;
            i7 = i9;
        }
        return bArr2;
    }

    @Override // p167y5.C2077g
    public final String toString() {
        return m5269u().toString();
    }

    /* renamed from: u */
    public final C2077g m5269u() {
        return new C2077g(m5268t());
    }
}
