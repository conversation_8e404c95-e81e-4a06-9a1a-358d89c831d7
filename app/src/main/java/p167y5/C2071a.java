package p167y5;

import androidx.activity.result.C0052a;
import java.io.IOException;

/* renamed from: y5.a */
/* loaded from: classes.dex */
public final class C2071a implements InterfaceC2091u {

    /* renamed from: j */
    public final /* synthetic */ InterfaceC2091u f8343j;

    /* renamed from: k */
    public final /* synthetic */ C2073c f8344k;

    public C2071a(C2073c c2073c, InterfaceC2091u interfaceC2091u) {
        this.f8344k = c2073c;
        this.f8343j = interfaceC2091u;
    }

    @Override // p167y5.InterfaceC2091u
    /* renamed from: b */
    public final C2093w mo3434b() {
        return this.f8344k;
    }

    @Override // p167y5.InterfaceC2091u, java.io.Closeable, java.lang.AutoCloseable
    public final void close() {
        this.f8344k.m5183i();
        try {
            try {
                this.f8343j.close();
                this.f8344k.m5185k(true);
            } catch (IOException e6) {
                throw this.f8344k.m5184j(e6);
            }
        } catch (Throwable th) {
            this.f8344k.m5185k(false);
            throw th;
        }
    }

    @Override // p167y5.InterfaceC2091u, java.io.Flushable
    public final void flush() {
        this.f8344k.m5183i();
        try {
            try {
                this.f8343j.flush();
                this.f8344k.m5185k(true);
            } catch (IOException e6) {
                throw this.f8344k.m5184j(e6);
            }
        } catch (Throwable th) {
            this.f8344k.m5185k(false);
            throw th;
        }
    }

    @Override // p167y5.InterfaceC2091u
    /* renamed from: m */
    public final void mo3277m(C2074d c2074d, long j6) {
        C2094x.m5270a(c2074d.f8355k, 0L, j6);
        while (true) {
            long j7 = 0;
            if (j6 <= 0) {
                return;
            }
            C2088r c2088r = c2074d.f8354j;
            while (true) {
                if (j7 >= 65536) {
                    break;
                }
                j7 += c2088r.f8387c - c2088r.f8386b;
                if (j7 >= j6) {
                    j7 = j6;
                    break;
                }
                c2088r = c2088r.f8390f;
            }
            this.f8344k.m5183i();
            try {
                try {
                    this.f8343j.mo3277m(c2074d, j7);
                    j6 -= j7;
                    this.f8344k.m5185k(true);
                } catch (IOException e6) {
                    throw this.f8344k.m5184j(e6);
                }
            } catch (Throwable th) {
                this.f8344k.m5185k(false);
                throw th;
            }
        }
    }

    public final String toString() {
        StringBuilder m104h = C0052a.m104h("AsyncTimeout.sink(");
        m104h.append(this.f8343j);
        m104h.append(")");
        return m104h.toString();
    }
}
