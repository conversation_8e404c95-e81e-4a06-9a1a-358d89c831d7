package p173z4;

import android.os.Handler;
import android.os.Looper;
import java.util.ArrayList;
import java.util.List;

/* renamed from: z4.a */
/* loaded from: classes.dex */
public abstract class AbstractC2114a<V> {

    /* renamed from: a */
    public Object f8460a = new Object();

    /* renamed from: b */
    public List<V> f8461b = new ArrayList();

    /* renamed from: c */
    public Handler f8462c = new Handler(Looper.getMainLooper());

    /* renamed from: z4.a$a */
    public class a implements Runnable {

        /* renamed from: j */
        public final /* synthetic */ Object f8463j;

        public a(Object obj) {
            this.f8463j = obj;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // java.lang.Runnable
        public final void run() {
            AbstractC2114a.this.m5301a(this.f8463j);
        }
    }

    /* renamed from: a */
    public final void m5301a(V v6) {
        synchronized (this.f8460a) {
            if (v6 != null) {
                if (!this.f8461b.contains(v6)) {
                    this.f8461b.add(v6);
                    mo2430b(v6);
                }
            }
        }
    }

    /* renamed from: b */
    public abstract void mo2430b(V v6);

    /* renamed from: c */
    public final void m5302c(V v6) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            m5301a(v6);
        } else {
            this.f8462c.post(new a(v6));
        }
    }

    /* renamed from: d */
    public final void m5303d(V v6) {
        synchronized (this.f8460a) {
            if (v6 != null) {
                if (this.f8461b.contains(v6)) {
                    this.f8461b.remove(v6);
                }
            }
        }
    }
}
