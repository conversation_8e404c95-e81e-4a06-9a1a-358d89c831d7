package p173z4;

import java.util.Objects;
import p173z4.C2123j;

/* renamed from: z4.k */
/* loaded from: classes.dex */
public final /* synthetic */ class RunnableC2124k implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f8522j;

    /* renamed from: k */
    public final /* synthetic */ boolean f8523k;

    /* renamed from: l */
    public final /* synthetic */ boolean f8524l;

    /* renamed from: m */
    public final /* synthetic */ Object f8525m;

    public /* synthetic */ RunnableC2124k(Object obj, boolean z5, boolean z6, int i6) {
        this.f8522j = i6;
        this.f8525m = obj;
        this.f8523k = z5;
        this.f8524l = z6;
    }

    @Override // java.lang.Runnable
    public final void run() {
        switch (this.f8522j) {
            case 0:
                C2123j.b bVar = (C2123j.b) this.f8525m;
                boolean z5 = this.f8523k;
                boolean z6 = this.f8524l;
                Objects.requireNonNull(bVar);
                if (z5) {
                    C2123j c2123j = bVar.f8514b;
                    if (!z6) {
                        c2123j.f8496k.m4620c();
                        break;
                    } else {
                        c2123j.f8496k.m4619b();
                        break;
                    }
                }
                break;
            default:
                C2123j.c cVar = (C2123j.c) this.f8525m;
                boolean z7 = this.f8523k;
                boolean z8 = this.f8524l;
                Objects.requireNonNull(cVar);
                if (z7) {
                    C2123j c2123j2 = cVar.f8516a;
                    if (!z8) {
                        c2123j2.f8496k.m4620c();
                        break;
                    } else {
                        c2123j2.f8496k.m4619b();
                        break;
                    }
                }
                break;
        }
    }
}
