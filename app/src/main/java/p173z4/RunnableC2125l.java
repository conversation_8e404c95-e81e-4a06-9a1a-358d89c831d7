package p173z4;

import java.util.Iterator;
import java.util.Objects;
import p048g5.C0891c;
import p048g5.InterfaceC0892d;
import p173z4.C2123j;

/* renamed from: z4.l */
/* loaded from: classes.dex */
public final /* synthetic */ class RunnableC2125l implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f8526j;

    /* renamed from: k */
    public final /* synthetic */ boolean f8527k;

    /* renamed from: l */
    public final /* synthetic */ boolean f8528l;

    /* renamed from: m */
    public final /* synthetic */ Object f8529m;

    public /* synthetic */ RunnableC2125l(Object obj, boolean z5, boolean z6, int i6) {
        this.f8526j = i6;
        this.f8529m = obj;
        this.f8527k = z5;
        this.f8528l = z6;
    }

    /* JADX WARN: Type inference failed for: r0v3, types: [java.util.ArrayList, java.util.List<V>] */
    @Override // java.lang.Runnable
    public final void run() {
        switch (this.f8526j) {
            case 0:
                C2123j.b bVar = (C2123j.b) this.f8529m;
                boolean z5 = this.f8527k;
                boolean z6 = this.f8528l;
                Objects.requireNonNull(bVar);
                if (z5 && z6) {
                    C2123j c2123j = bVar.f8514b;
                    c2123j.m5314n(c2123j.f8495j.m3426a());
                    break;
                }
                break;
            default:
                C0891c c0891c = (C0891c) this.f8529m;
                boolean z7 = this.f8527k;
                boolean z8 = this.f8528l;
                Iterator it = c0891c.f8461b.iterator();
                while (it.hasNext()) {
                    ((InterfaceC0892d) it.next()).mo1931e(z7, z8);
                }
                break;
        }
    }
}
