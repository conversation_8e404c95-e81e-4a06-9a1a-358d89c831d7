package p173z4;

import android.webkit.WebView;
import com.liaoyuan.aicast.phone.browser.model.BrowserInfo;
import java.util.List;

/* renamed from: z4.b */
/* loaded from: classes.dex */
public interface InterfaceC2115b {
    /* renamed from: a */
    default void mo76a(boolean z5) {
    }

    /* renamed from: b */
    default void m5304b() {
    }

    /* renamed from: c */
    default void mo77c(boolean z5, String str) {
    }

    /* renamed from: d */
    default void mo1479d(boolean z5) {
    }

    /* renamed from: e */
    default void mo78e(List<BrowserInfo> list) {
    }

    /* renamed from: f */
    default void mo1906f(int i6, int i7) {
    }

    /* renamed from: g */
    default void mo1480g(int i6, String str) {
    }

    /* renamed from: h */
    default void mo79h(WebView webView) {
    }

    /* renamed from: i */
    default void mo80i(int i6) {
    }

    /* renamed from: j */
    default void mo81j(boolean z5) {
    }

    /* renamed from: k */
    default void mo82k(boolean z5, WebView webView, boolean z6, int i6) {
    }
}
