package p173z4;

import android.media.AudioManager;
import android.media.projection.MediaProjection;
import android.util.Log;
import java.util.Iterator;
import java.util.Objects;
import p006a5.PresentationC0040c;
import p147v4.C1463b;
import p154w4.C1806a;
import p160x4.C2051a;
import p173z4.C2123j;

/* renamed from: z4.e */
/* loaded from: classes.dex */
public final /* synthetic */ class RunnableC2118e implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f8469j;

    /* renamed from: k */
    public final /* synthetic */ int f8470k;

    /* renamed from: l */
    public final /* synthetic */ Object f8471l;

    public /* synthetic */ RunnableC2118e(Object obj, int i6, int i7) {
        this.f8469j = i7;
        this.f8471l = obj;
        this.f8470k = i6;
    }

    /* JADX WARN: Type inference failed for: r0v7, types: [java.util.ArrayList, java.util.List<V>] */
    @Override // java.lang.Runnable
    public final void run() {
        AudioManager audioManager;
        switch (this.f8469j) {
            case 0:
                C2123j c2123j = (C2123j) this.f8471l;
                int i6 = this.f8470k;
                Iterator it = c2123j.f8461b.iterator();
                while (it.hasNext()) {
                    ((InterfaceC2115b) it.next()).mo80i(i6);
                }
                break;
            default:
                C2123j.b bVar = (C2123j.b) this.f8471l;
                int i7 = this.f8470k;
                Objects.requireNonNull(bVar);
                if (i7 == 1) {
                    C2123j.this.m5314n(1);
                    break;
                } else {
                    int i8 = 2;
                    if (i7 == 2) {
                        boolean z5 = C2123j.this.f8498m.f4391r;
                        Log.d("ProjectionPresenter", z5 ? "onConnectedState 盒子已断开，wifi ap 是连接，那么也断开" : "onConnectedState 盒子已断开，wifi ap 已经是断卡的了");
                        if (z5) {
                            C2123j.this.f8498m.m2388d();
                        }
                        C2123j c2123j2 = C2123j.this;
                        c2123j2.f8494i = "";
                        c2123j2.f8505t = 0;
                        PresentationC0040c presentationC0040c = c2123j2.f8497l;
                        if (presentationC0040c != null) {
                            presentationC0040c.f170l.m1919j(false);
                            c2123j2.f8497l.dismiss();
                            c2123j2.f8497l = null;
                        }
                        C2051a c2051a = c2123j2.f8500o;
                        if (c2051a != null && (audioManager = c2051a.f8295a) != null) {
                            audioManager.abandonAudioFocus(c2051a.f8297c);
                        }
                        C1806a c1806a = c2123j2.f8496k;
                        if (c1806a != null) {
                            c1806a.f7463b.m5166d();
                            c1806a.f7462a.m5180c();
                        }
                        C1463b c1463b = c2123j2.f8495j.f6479d;
                        c1463b.m3567c(false);
                        Log.d("ScreenCapturePermissions", "release mMediaProjection:" + c1463b.f6730c);
                        MediaProjection mediaProjection = c1463b.f6730c;
                        if (mediaProjection != null) {
                            mediaProjection.stop();
                            c1463b.f6730c = null;
                        }
                        c1463b.f6731d = 1;
                        MediaProjection mediaProjection2 = c2123j2.f8510y;
                        if (mediaProjection2 != null) {
                            mediaProjection2.unregisterCallback(c2123j2.f8487A);
                        }
                        c2123j2.f8462c.postDelayed(new RunnableC2116c(c2123j2, i8), 1000L);
                        break;
                    }
                }
                break;
        }
    }
}
