package p173z4;

import android.webkit.WebView;
import java.util.Iterator;
import p048g5.C0891c;
import p048g5.InterfaceC0892d;
import p083l5.AbstractC1072e;
import p083l5.InterfaceC1073f;
import p173z4.C2123j;

/* renamed from: z4.f */
/* loaded from: classes.dex */
public final /* synthetic */ class RunnableC2119f implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f8472j;

    /* renamed from: k */
    public final /* synthetic */ boolean f8473k;

    /* renamed from: l */
    public final /* synthetic */ Object f8474l;

    public /* synthetic */ RunnableC2119f(Object obj, boolean z5, int i6) {
        this.f8472j = i6;
        this.f8474l = obj;
        this.f8473k = z5;
    }

    /* JADX WARN: Type inference failed for: r0v10, types: [java.util.ArrayList, java.util.List<V>] */
    /* JADX WARN: Type inference failed for: r0v15, types: [java.util.ArrayList, java.util.List<V>] */
    /* JADX WARN: Type inference failed for: r2v0, types: [java.util.ArrayList, java.util.List<V>] */
    @Override // java.lang.Runnable
    public final void run() {
        switch (this.f8472j) {
            case 0:
                C2123j c2123j = (C2123j) this.f8474l;
                boolean z5 = this.f8473k;
                Iterator it = c2123j.f8461b.iterator();
                while (it.hasNext()) {
                    ((InterfaceC2115b) it.next()).mo81j(z5);
                }
                WebView webView = c2123j.f8491f;
                if (webView != null) {
                    webView.loadUrl(c2123j.f8494i);
                    break;
                }
                break;
            case 1:
                C2123j.e eVar = (C2123j.e) this.f8474l;
                boolean z6 = this.f8473k;
                Iterator it2 = eVar.f8518a.f8461b.iterator();
                while (it2.hasNext()) {
                    ((InterfaceC2115b) it2.next()).mo76a(z6);
                }
                break;
            case 2:
                C0891c.a aVar = (C0891c.a) this.f8474l;
                boolean z7 = this.f8473k;
                Iterator it3 = C0891c.this.f8461b.iterator();
                while (it3.hasNext()) {
                    ((InterfaceC0892d) it3.next()).mo1930d(z7);
                }
                break;
            default:
                AbstractC1072e abstractC1072e = (AbstractC1072e) this.f8474l;
                boolean z8 = this.f8473k;
                InterfaceC1073f interfaceC1073f = abstractC1072e.f5112a;
                if (interfaceC1073f != null) {
                    interfaceC1073f.mo2727f(z8);
                    break;
                }
                break;
        }
    }
}
