package p173z4;

/* renamed from: z4.d */
/* loaded from: classes.dex */
public final /* synthetic */ class RunnableC2117d implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f8467j;

    /* renamed from: k */
    public final /* synthetic */ C2123j f8468k;

    public /* synthetic */ RunnableC2117d(C2123j c2123j, int i6) {
        this.f8467j = i6;
        this.f8468k = c2123j;
    }

    /* JADX WARN: Code restructure failed: missing block: B:15:0x0052, code lost:
    
        if (r0 >= 720) goto L26;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x005c, code lost:
    
        r10 = 240;
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x00f8, code lost:
    
        if (android.os.Build.VERSION.SDK_INT >= 26) goto L65;
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x0168, code lost:
    
        r2.m5312l(r2.f8511z, r8);
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x015f, code lost:
    
        r2.f8496k.m4618a();
        r2.m5307g(true);
     */
    /* JADX WARN: Code restructure failed: missing block: B:50:0x015d, code lost:
    
        if (android.os.Build.VERSION.SDK_INT >= 26) goto L65;
     */
    /* JADX WARN: Code restructure failed: missing block: B:62:0x0059, code lost:
    
        if (r4 >= 720) goto L26;
     */
    /* JADX WARN: Removed duplicated region for block: B:19:0x0073  */
    /* JADX WARN: Removed duplicated region for block: B:24:0x00cf  */
    /* JADX WARN: Removed duplicated region for block: B:27:0x00f6  */
    /* JADX WARN: Removed duplicated region for block: B:32:0x00fb  */
    /* JADX WARN: Removed duplicated region for block: B:51:0x00a7 A[EXC_TOP_SPLITTER, SYNTHETIC] */
    @Override // java.lang.Runnable
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void run() {
        /*
            Method dump skipped, instructions count: 450
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p173z4.RunnableC2117d.run():void");
    }
}
