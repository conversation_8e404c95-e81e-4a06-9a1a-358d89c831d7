package p173z4;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.hardware.display.VirtualDisplay;
import android.media.projection.MediaProjection;
import android.net.Uri;
import android.net.wifi.p2p.WifiP2pDevice;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.webkit.PermissionRequest;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebHistoryItem;
import android.webkit.WebResourceRequest;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;
import com.liaoyuan.aicast.R;
import com.liaoyuan.aicast.phone.browser.model.BrowserInfo;
import java.net.InetAddress;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import org.litepal.LitePal;
import p006a5.PresentationC0040c;
import p013b5.C0410a;
import p031e2.C0800p;
import p031e2.RunnableC0789e;
import p041f5.C0865b;
import p041f5.InterfaceC0867d;
import p088m4.C1085a;
import p088m4.InterfaceC1086b;
import p120r4.C1322a;
import p120r4.C1324c;
import p120r4.InterfaceC1325d;
import p133t4.C1405b;
import p133t4.InterfaceC1404a;
import p135u.C1407a;
import p142v.C1450a;
import p147v4.C1463b;
import p154w4.C1806a;
import p160x4.C2051a;
import p166y4.C2069b;
import p166y4.InterfaceC2068a;
import p173z4.C2123j;

/* renamed from: z4.j */
/* loaded from: classes.dex */
public final class C2123j extends AbstractC2114a<InterfaceC2115b> {

    /* renamed from: d */
    public Context f8489d;

    /* renamed from: e */
    public boolean f8490e;

    /* renamed from: f */
    public WebView f8491f;

    /* renamed from: g */
    public C1324c f8492g;

    /* renamed from: i */
    public String f8494i;

    /* renamed from: j */
    public C1405b f8495j;

    /* renamed from: k */
    public C1806a f8496k;

    /* renamed from: l */
    public PresentationC0040c f8497l;

    /* renamed from: m */
    public C0865b f8498m;

    /* renamed from: n */
    public C1085a f8499n;

    /* renamed from: o */
    public C2051a f8500o;

    /* renamed from: p */
    public C1322a f8501p;

    /* renamed from: q */
    public C0410a f8502q;

    /* renamed from: r */
    public Activity f8503r;

    /* renamed from: s */
    public Activity f8504s;

    /* renamed from: t */
    public int f8505t;

    /* renamed from: y */
    public MediaProjection f8510y;

    /* renamed from: z */
    public VirtualDisplay f8511z;

    /* renamed from: h */
    public boolean f8493h = false;

    /* renamed from: u */
    public C0800p f8506u = new C0800p(this, 5);

    /* renamed from: v */
    public a f8507v = new a();

    /* renamed from: w */
    public b f8508w = new b();

    /* renamed from: x */
    public c f8509x = new c();

    /* renamed from: A */
    public d f8487A = new d();

    /* renamed from: B */
    public e f8488B = new e();

    /* renamed from: z4.j$a */
    public class a implements InterfaceC0867d {
        public a() {
        }

        @Override // p041f5.InterfaceC0867d
        /* renamed from: a */
        public final void mo2400a(List<WifiP2pDevice> list, List<WifiP2pDevice> list2) {
            C2123j.this.f8462c.post(new RunnableC0789e(this, list2, 3));
        }

        @Override // p041f5.InterfaceC0867d
        /* renamed from: c */
        public final void mo2402c(int i6, String str) {
            if (i6 == 5 && C2123j.this.f8499n.m2773d()) {
                C2123j.this.f8499n.m2770a();
            }
            C2123j.this.f8462c.post(new Runnable() { // from class: z4.h

                /* renamed from: k */
                public final /* synthetic */ int f8481k;

                /* renamed from: l */
                public final /* synthetic */ String f8482l;

                public /* synthetic */ RunnableC2121h(int i62, String str2) {
                    r2 = i62;
                    r3 = str2;
                }

                /* JADX WARN: Type inference failed for: r0v2, types: [java.util.ArrayList, java.util.List<V>] */
                @Override // java.lang.Runnable
                public final void run() {
                    C2123j.a aVar = C2123j.a.this;
                    int i7 = r2;
                    String str2 = r3;
                    Iterator it = C2123j.this.f8461b.iterator();
                    while (it.hasNext()) {
                        ((InterfaceC2115b) it.next()).mo1480g(i7, str2);
                    }
                }
            });
        }

        @Override // p041f5.InterfaceC0867d
        /* renamed from: d */
        public final void mo2403d(InetAddress inetAddress, String str, String str2, String str3) {
            C2123j.this.f8462c.post(new Runnable(inetAddress, str2, str3, str) { // from class: z4.i

                /* renamed from: k */
                public final /* synthetic */ InetAddress f8484k;

                /* renamed from: l */
                public final /* synthetic */ String f8485l;

                /* renamed from: m */
                public final /* synthetic */ String f8486m;

                public /* synthetic */ RunnableC2122i(InetAddress inetAddress2, String str22, String str32, String str4) {
                    this.f8484k = inetAddress2;
                    this.f8485l = str22;
                    this.f8486m = str4;
                }

                @Override // java.lang.Runnable
                public final void run() {
                    C2123j.a aVar = C2123j.a.this;
                    InetAddress inetAddress2 = this.f8484k;
                    String str4 = this.f8485l;
                    String str5 = this.f8486m;
                    Objects.requireNonNull(aVar);
                    if (inetAddress2 != null) {
                        C2123j c2123j = C2123j.this;
                        String hostAddress = inetAddress2.getHostAddress();
                        boolean z5 = c2123j.f8498m.f4391r;
                        boolean m2773d = c2123j.f8499n.m2773d();
                        if (!z5 || m2773d) {
                            return;
                        }
                        c2123j.f8499n.m2777h(hostAddress, str5, str4);
                    }
                }
            });
        }
    }

    /* renamed from: z4.j$b */
    public class b implements InterfaceC1086b {

        /* renamed from: a */
        public a f8513a = new a();

        /* renamed from: z4.j$b$a */
        public class a implements Runnable {
            public a() {
            }

            @Override // java.lang.Runnable
            public final void run() {
                byte[] bArr;
                C1085a c1085a;
                if (C2123j.this.m5310j()) {
                    C2123j.this.f8496k.m4618a();
                    C2069b c2069b = C2123j.this.f8496k.f7462a;
                    InterfaceC2068a interfaceC2068a = c2069b.f8334l;
                    if (interfaceC2068a == null || (bArr = c2069b.f8338p) == null || (c1085a = C1806a.this.f7464c) == null) {
                        return;
                    }
                    c1085a.m2775f(bArr);
                }
            }
        }

        public b() {
        }

        @Override // p083l5.InterfaceC1073f
        /* renamed from: a */
        public final void mo2722a(int i6, int i7) {
            C2123j c2123j = C2123j.this;
            c2123j.f8462c.post(new RunnableC2117d(c2123j, 0));
        }

        @Override // p083l5.InterfaceC1073f
        /* renamed from: b */
        public final void mo2723b(int i6, int i7, int i8) {
            MotionEvent obtain = MotionEvent.obtain(SystemClock.uptimeMillis(), SystemClock.uptimeMillis(), i8, i6, i7, 0);
            PresentationC0040c presentationC0040c = C2123j.this.f8497l;
            boolean z5 = presentationC0040c != null && presentationC0040c.isShowing();
            boolean m5310j = C2123j.this.m5310j();
            if (z5 && m5310j) {
                C2123j.this.f8462c.post(new RunnableC0789e(this, obtain, 4));
            }
        }

        @Override // p083l5.InterfaceC1073f
        /* renamed from: c */
        public final void mo2724c(int i6, int i7) {
        }

        @Override // p083l5.InterfaceC1073f
        /* renamed from: d */
        public final void mo2725d() {
            C2123j.this.f8462c.removeCallbacks(this.f8513a);
            C2123j.this.f8462c.postDelayed(this.f8513a, 200L);
        }

        @Override // p083l5.InterfaceC1073f
        /* renamed from: e */
        public final void mo2726e(int i6, int i7) {
            C2123j.this.f8462c.post(new RunnableC2118e(this, i7, 1));
        }

        @Override // p083l5.InterfaceC1073f
        /* renamed from: f */
        public final void mo2727f(boolean z5) {
            C2123j.this.f8462c.post(new RunnableC2125l(this, C2123j.this.m5310j(), z5, 0));
        }

        @Override // p083l5.InterfaceC1073f
        /* renamed from: g */
        public final void mo2728g(boolean z5) {
            C2123j.this.f8462c.post(new RunnableC2124k(this, C2123j.this.m5310j(), z5, 0));
        }
    }

    /* renamed from: z4.j$c */
    public class c implements InterfaceC1404a {
        public c() {
        }

        @Override // p140u4.InterfaceC1428a
        /* renamed from: a */
        public final void mo1907a(boolean z5) {
            boolean m5310j = C2123j.this.m5310j();
            Log.d("ProjectionPresenter", "onRecordAudioPermission hasPermission:" + z5 + ",projecting:" + m5310j);
            C2123j.this.f8462c.post(new RunnableC2124k(this, m5310j, z5, 1));
        }

        @Override // p147v4.InterfaceC1462a
        /* renamed from: c */
        public final void mo1909c(MediaProjection mediaProjection, int i6) {
            C2123j c2123j = C2123j.this;
            c2123j.f8462c.post(new RunnableC2117d(c2123j, 0));
        }
    }

    /* renamed from: z4.j$d */
    public class d extends MediaProjection.Callback {
        public d() {
        }

        @Override // android.media.projection.MediaProjection.Callback
        public final void onStop() {
            super.onStop();
            MediaProjection mediaProjection = C2123j.this.f8510y;
            if (mediaProjection != null) {
                mediaProjection.stop();
                C2123j.this.f8510y = null;
            }
            VirtualDisplay virtualDisplay = C2123j.this.f8511z;
            if (virtualDisplay != null) {
                virtualDisplay.release();
                C2123j.this.f8511z = null;
            }
            C2123j c2123j = C2123j.this;
            MediaProjection mediaProjection2 = c2123j.f8510y;
            if (mediaProjection2 != null) {
                mediaProjection2.unregisterCallback(c2123j.f8487A);
            }
        }
    }

    /* renamed from: z4.j$e */
    public class e implements InterfaceC1325d {
        public e() {
        }

        @Override // p120r4.InterfaceC1325d
        /* renamed from: a */
        public final void mo3241a(List list, int i6, int i7) {
            C2123j.this.f8462c.post(new Runnable() { // from class: z4.m

                /* renamed from: k */
                public final /* synthetic */ List f8531k;

                /* renamed from: l */
                public final /* synthetic */ int f8532l;

                /* renamed from: m */
                public final /* synthetic */ int f8533m;

                public /* synthetic */ RunnableC2126m(List list2, int i62, int i72) {
                    r2 = list2;
                    r3 = i62;
                    r4 = i72;
                }

                /* JADX WARN: Type inference failed for: r0v2, types: [java.util.ArrayList, java.util.List<V>] */
                @Override // java.lang.Runnable
                public final void run() {
                    C2123j.e eVar = C2123j.e.this;
                    List<BrowserInfo> list2 = r2;
                    int i8 = r3;
                    int i9 = r4;
                    Iterator it = C2123j.this.f8461b.iterator();
                    while (it.hasNext()) {
                        InterfaceC2115b interfaceC2115b = (InterfaceC2115b) it.next();
                        interfaceC2115b.mo78e(list2);
                        interfaceC2115b.mo1906f(i8, i9);
                    }
                }
            });
        }

        @Override // p120r4.InterfaceC1325d
        /* renamed from: b */
        public final void mo3242b(boolean z5) {
            C2123j.this.f8462c.post(new RunnableC2119f(this, z5, 1));
        }
    }

    /* renamed from: z4.j$f */
    public class f extends WebViewClient {

        /* renamed from: z4.j$f$a */
        public class a implements ValueCallback<String> {
            @Override // android.webkit.ValueCallback
            public final void onReceiveValue(String str) {
                Log.d("ProjectionPresenter", "onPageFinished value:" + str);
            }
        }

        public f() {
        }

        @Override // android.webkit.WebViewClient
        public final void onPageFinished(WebView webView, String str) {
            C2123j c2123j = C2123j.this;
            boolean z5 = c2123j.f8493h;
            C2123j.m5305e(c2123j, z5, webView, false, 100);
            C2123j.this.f8462c.post(new Runnable() { // from class: z4.n

                /* renamed from: k */
                public final /* synthetic */ boolean f8535k;

                /* renamed from: l */
                public final /* synthetic */ WebView f8536l;

                /* renamed from: m */
                public final /* synthetic */ String f8537m;

                public /* synthetic */ RunnableC2127n(boolean z52, WebView webView2, String str2) {
                    r2 = z52;
                    r3 = webView2;
                    r4 = str2;
                }

                /* JADX WARN: Type inference failed for: r0v2, types: [java.util.ArrayList, java.util.List<V>] */
                @Override // java.lang.Runnable
                public final void run() {
                    C2123j.f fVar = C2123j.f.this;
                    WebView webView2 = r3;
                    Iterator it = C2123j.this.f8461b.iterator();
                    while (it.hasNext()) {
                        ((InterfaceC2115b) it.next()).mo79h(webView2);
                    }
                }
            });
            WebHistoryItem currentItem = webView2.copyBackForwardList().getCurrentItem();
            if (currentItem == null || currentItem.getFavicon() != null) {
                return;
            }
            webView2.evaluateJavascript("(function() { return $('link[rel~=\"icon\"]')[0]; })();", new a());
        }

        @Override // android.webkit.WebViewClient
        public final void onPageStarted(WebView webView, String str, Bitmap bitmap) {
            C2123j c2123j = C2123j.this;
            boolean z5 = c2123j.f8493h;
            WebHistoryItem currentItem = c2123j.f8491f.copyBackForwardList().getCurrentItem();
            if (currentItem != null) {
                C2123j.this.f8492g.m3239f(currentItem);
            }
            C2123j.m5305e(C2123j.this, z5, webView, true, 0);
            C2123j.this.f8462c.post(new Runnable() { // from class: z4.o

                /* renamed from: k */
                public final /* synthetic */ boolean f8539k;

                /* renamed from: l */
                public final /* synthetic */ WebView f8540l;

                /* renamed from: m */
                public final /* synthetic */ String f8541m;

                /* renamed from: n */
                public final /* synthetic */ Bitmap f8542n;

                public /* synthetic */ RunnableC2128o(boolean z52, WebView webView2, String str2, Bitmap bitmap2) {
                    r2 = z52;
                    r3 = webView2;
                    r4 = str2;
                    r5 = bitmap2;
                }

                /* JADX WARN: Type inference failed for: r0v2, types: [java.util.ArrayList, java.util.List<V>] */
                @Override // java.lang.Runnable
                public final void run() {
                    C2123j.f fVar = C2123j.f.this;
                    boolean z6 = r2;
                    String str2 = r4;
                    Iterator it = C2123j.this.f8461b.iterator();
                    while (it.hasNext()) {
                        ((InterfaceC2115b) it.next()).mo77c(z6, str2);
                    }
                }
            });
        }

        @Override // android.webkit.WebViewClient
        public final boolean shouldOverrideUrlLoading(WebView webView, WebResourceRequest webResourceRequest) {
            String uri = webResourceRequest.getUrl().toString();
            return (uri.startsWith("https://") || uri.startsWith("http://")) ? false : true;
        }
    }

    /* renamed from: z4.j$g */
    public class g extends WebChromeClient {
        public g() {
        }

        @Override // android.webkit.WebChromeClient
        public final void onPermissionRequest(PermissionRequest permissionRequest) {
            permissionRequest.grant(permissionRequest.getResources());
        }

        @Override // android.webkit.WebChromeClient
        public final void onProgressChanged(WebView webView, int i6) {
            C2123j c2123j = C2123j.this;
            boolean z5 = c2123j.f8493h;
            if (i6 == 100) {
                C2123j.m5305e(c2123j, z5, webView, false, 0);
            } else {
                C2123j.m5305e(c2123j, z5, webView, true, i6);
            }
        }

        @Override // android.webkit.WebChromeClient
        public final void onReceivedIcon(WebView webView, Bitmap bitmap) {
            super.onReceivedIcon(webView, bitmap);
        }

        @Override // android.webkit.WebChromeClient
        public final void onShowCustomView(View view, WebChromeClient.CustomViewCallback customViewCallback) {
            super.onShowCustomView(view, customViewCallback);
        }

        @Override // android.webkit.WebChromeClient
        public final boolean onShowFileChooser(WebView webView, ValueCallback<Uri[]> valueCallback, WebChromeClient.FileChooserParams fileChooserParams) {
            return super.onShowFileChooser(webView, valueCallback, fileChooserParams);
        }
    }

    /* renamed from: z4.j$h */
    public static class h {

        /* renamed from: a */
        public static final C2123j f8521a = new C2123j();
    }

    /* renamed from: e */
    public static void m5305e(C2123j c2123j, boolean z5, WebView webView, boolean z6, int i6) {
        c2123j.f8462c.post(new Runnable() { // from class: z4.g

            /* renamed from: k */
            public final /* synthetic */ boolean f8476k;

            /* renamed from: l */
            public final /* synthetic */ WebView f8477l;

            /* renamed from: m */
            public final /* synthetic */ boolean f8478m;

            /* renamed from: n */
            public final /* synthetic */ int f8479n;

            public /* synthetic */ RunnableC2120g(boolean z52, WebView webView2, boolean z62, int i62) {
                r2 = z52;
                r3 = webView2;
                r4 = z62;
                r5 = i62;
            }

            /* JADX WARN: Type inference failed for: r0v1, types: [java.util.ArrayList, java.util.List<V>] */
            @Override // java.lang.Runnable
            public final void run() {
                C2123j c2123j2 = C2123j.this;
                boolean z7 = r2;
                WebView webView2 = r3;
                boolean z8 = r4;
                int i7 = r5;
                Iterator it = c2123j2.f8461b.iterator();
                while (it.hasNext()) {
                    ((InterfaceC2115b) it.next()).mo82k(z7, webView2, z8, i7);
                }
            }
        });
    }

    @Override // p173z4.AbstractC2114a
    /* renamed from: b */
    public final void mo2430b(InterfaceC2115b interfaceC2115b) {
        interfaceC2115b.mo1479d(this.f8500o.m5161a());
    }

    /* renamed from: f */
    public final void m5306f() {
        WebHistoryItem currentItem = this.f8491f.copyBackForwardList().getCurrentItem();
        if (currentItem != null) {
            C1324c c1324c = this.f8492g;
            Objects.requireNonNull(c1324c);
            if (c1324c.m3240g(currentItem.getUrl())) {
                LitePal.deleteAll((Class<?>) BrowserInfo.class, "url = ?", currentItem.getUrl());
            } else {
                c1324c.m3235b(currentItem.getTitle(), currentItem.getUrl(), BrowserInfo.img(currentItem.getFavicon()));
            }
            c1324c.m3239f(currentItem);
            c1324c.m3238e();
        }
    }

    /* renamed from: g */
    public final void m5307g(boolean z5) {
        Log.d("ProjectionPresenter", "doMediaMute mute:" + z5);
        if (z5) {
            this.f8500o.m5162b(true, 0);
        } else {
            this.f8500o.m5162b(false, 0);
        }
        m5313m();
    }

    /* renamed from: h */
    public final int m5308h() {
        return this.f8492g.m3236c();
    }

    /* JADX WARN: Type inference failed for: r0v13, types: [java.util.ArrayList, java.util.List<m4.b>] */
    /* JADX WARN: Type inference failed for: r0v14, types: [java.util.ArrayList, java.util.List<t4.a>] */
    /* JADX WARN: Type inference failed for: r0v15, types: [java.util.ArrayList, java.util.List<r4.d>] */
    /* JADX WARN: Type inference failed for: r2v0, types: [java.util.ArrayList, java.util.List<r4.d>] */
    /* JADX WARN: Type inference failed for: r2v2, types: [java.util.ArrayList, java.util.List<t4.a>] */
    /* JADX WARN: Type inference failed for: r2v4, types: [java.util.ArrayList, java.util.List<m4.b>] */
    /* renamed from: i */
    public final void m5309i(Context context) {
        if (!this.f8490e) {
            this.f8490e = true;
            Log.d("ProjectionPresenter", "init");
            this.f8489d = context.getApplicationContext();
            C1324c c1324c = C1324c.a.f6165a;
            this.f8492g = c1324c;
            e eVar = this.f8488B;
            if (!c1324c.f6164d.contains(eVar)) {
                c1324c.f6164d.add(eVar);
            }
            C1405b c1405b = C1405b.c.f6485a;
            this.f8495j = c1405b;
            c cVar = this.f8509x;
            if (!c1405b.f6478c.contains(cVar)) {
                c1405b.f6478c.add(cVar);
            }
            C0865b c0865b = C0865b.k.f4401a;
            this.f8498m = c0865b;
            c0865b.m2395k(this.f8507v);
            this.f8498m.m2396l();
            C1085a c1085a = C1085a.b.f5193a;
            this.f8499n = c1085a;
            b bVar = this.f8508w;
            if (!c1085a.f5188b.contains(bVar)) {
                c1085a.f5188b.add(bVar);
            }
            this.f8496k = new C1806a(this.f8489d, this.f8499n);
            C2051a c2051a = new C2051a(context);
            this.f8500o = c2051a;
            c2051a.f8296b = this.f8506u;
            this.f8501p = new C1322a(this.f8489d);
            this.f8502q = new C0410a(this.f8489d);
        }
        this.f8492g.m3234a();
    }

    /* renamed from: j */
    public final boolean m5310j() {
        boolean m2773d = this.f8499n.m2773d();
        boolean z5 = this.f8498m.f4391r;
        boolean m3565a = this.f8495j.f6479d.m3565a();
        boolean z6 = this.f8511z != null;
        Log.d("ProjectionPresenter", "isBoxConnected:" + m2773d + ",isBoxP2pConnected:" + z5 + ",hasScreenCapturePermission:" + m3565a + ",hasVirtualDisplay:" + z6);
        return m2773d && z5 && m3565a && z6;
    }

    /* renamed from: k */
    public final String m5311k(String str) {
        String str2;
        Uri.Builder builder;
        Uri.Builder builder2;
        Uri.Builder buildUpon;
        Uri.Builder appendPath;
        String str3;
        boolean m5310j = m5310j();
        boolean z5 = this.f8493h;
        if (m5310j && z5) {
            m5307g(true);
        } else {
            m5307g(false);
        }
        if (!(C1450a.m3520a(this.f8495j.f6480e.f6549a, "android.permission.RECORD_AUDIO") == 0)) {
            m5314n(10);
        }
        C1324c c1324c = this.f8492g;
        Objects.requireNonNull(c1324c);
        if (TextUtils.isEmpty(str) || !Pattern.matches("^(http(s)?://)?([\\w-]+\\.)+[\\w-]+(/[\\w- ./?%&=]*)?$", str)) {
            int m3233a = c1324c.f6163c.m3233a();
            Objects.requireNonNull(c1324c.f6163c);
            Log.d("BrowserEngines", "getDomainName index:" + m3233a);
            String str4 = "q";
            if (m3233a != 0) {
                if (m3233a == 1) {
                    str2 = "https://www.bing.com";
                    builder = Uri.parse(str2).buildUpon();
                    builder2 = builder.appendPath("search");
                    builder2.appendQueryParameter(str4, str);
                    str = builder.build().toString();
                } else {
                    if (m3233a == 2) {
                        buildUpon = Uri.parse("https://search.yahoo.com").buildUpon();
                        appendPath = buildUpon.appendPath("search");
                        str3 = "p";
                    } else if (m3233a == 3) {
                        buildUpon = Uri.parse("https://www.baidu.com").buildUpon();
                        appendPath = buildUpon.appendPath("s");
                        str3 = "wd";
                    } else if (m3233a == 4) {
                        buildUpon = Uri.parse("https://yandex.com").buildUpon();
                        appendPath = buildUpon.appendPath("search");
                        str3 = "text";
                    } else if (m3233a == 5) {
                        builder = Uri.parse("https://duckduckgo.com").buildUpon();
                        builder.appendQueryParameter("q", str);
                        str = builder.build().toString();
                    }
                    Uri.Builder builder3 = appendPath;
                    builder = buildUpon;
                    str4 = str3;
                    builder2 = builder3;
                    builder2.appendQueryParameter(str4, str);
                    str = builder.build().toString();
                }
            }
            str2 = "https://www.google.com";
            builder = Uri.parse(str2).buildUpon();
            builder2 = builder.appendPath("search");
            builder2.appendQueryParameter(str4, str);
            str = builder.build().toString();
        }
        this.f8494i = str;
        this.f8462c.post(new RunnableC2119f(this, z5, 0));
        return this.f8494i;
    }

    /* renamed from: l */
    public final void m5312l(VirtualDisplay virtualDisplay, boolean z5) {
        this.f8497l = new PresentationC0040c(this.f8504s, virtualDisplay.getDisplay(), z5, this.f8501p);
        this.f8496k.m4618a();
        m5307g(true);
    }

    /* renamed from: m */
    public final void m5313m() {
        this.f8462c.post(new RunnableC2116c(this, 0));
    }

    /* renamed from: n */
    public final void m5314n(int i6) {
        this.f8462c.post(new RunnableC2118e(this, i6, 0));
    }

    /* renamed from: o */
    public final void m5315o(boolean z5) {
        if (this.f8493h != z5) {
            this.f8493h = z5;
            if (z5) {
                this.f8496k.m4619b();
            } else {
                this.f8496k.m4620c();
            }
            WebHistoryItem currentItem = this.f8491f.copyBackForwardList().getCurrentItem();
            String url = currentItem != null ? currentItem.getUrl() : this.f8494i;
            if (TextUtils.isEmpty(url)) {
                return;
            }
            m5311k(url);
        }
    }

    /* renamed from: p */
    public final boolean m5316p(int i6) {
        if (m5310j() && this.f8493h) {
            return i6 == 25 || i6 == 24;
        }
        return false;
    }

    /* renamed from: q */
    public final void m5317q(Activity activity, int i6) {
        if (!this.f8498m.f4391r) {
            Context context = this.f8489d;
            Toast.makeText(context, context.getResources().getString(R.string.projection_please_add_device), 0).show();
            return;
        }
        if (i6 != 1 && i6 != 2 && i6 != 3) {
            if (i6 != 10) {
                return;
            }
            Objects.requireNonNull(this.f8495j.f6480e);
            C1407a.m3435b(activity, new String[]{"android.permission.RECORD_AUDIO"}, 10);
            return;
        }
        C1405b c1405b = this.f8495j;
        Objects.requireNonNull(c1405b);
        Log.d("PermissionsManager", "requestScreenCapturePermissions activity:" + activity + ",requestCode:" + i6);
        C1463b c1463b = c1405b.f6479d;
        Objects.requireNonNull(c1463b);
        Log.d("ScreenCapturePermissions", "requestScreenCapturePermissions requestCode:" + i6);
        boolean m3565a = c1463b.m3565a();
        Log.d("ScreenCapturePermissions", "requestScreenCapturePermissions requestCode:" + i6 + ",hasScreenCapturePermission:" + m3565a);
        if (m3565a) {
            c1463b.m3566b(i6);
        } else {
            activity.startActivityForResult(c1463b.f6729b.createScreenCaptureIntent(), i6);
        }
    }

    @SuppressLint({"JavascriptInterface"})
    /* renamed from: r */
    public final void m5318r(WebView webView) {
        this.f8491f = webView;
        webView.setWebViewClient(new f());
        this.f8491f.setWebChromeClient(new g());
    }
}
