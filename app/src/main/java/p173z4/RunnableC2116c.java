package p173z4;

import java.util.Iterator;
import java.util.concurrent.Executors;
import p031e2.RunnableC0801q;
import p120r4.C1324c;

/* renamed from: z4.c */
/* loaded from: classes.dex */
public final /* synthetic */ class RunnableC2116c implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f8465j;

    /* renamed from: k */
    public final /* synthetic */ C2123j f8466k;

    public /* synthetic */ RunnableC2116c(C2123j c2123j, int i6) {
        this.f8465j = i6;
        this.f8466k = c2123j;
    }

    /* JADX WARN: Type inference failed for: r0v3, types: [java.util.ArrayList, java.util.List<V>] */
    @Override // java.lang.Runnable
    public final void run() {
        switch (this.f8465j) {
            case 0:
                C2123j c2123j = this.f8466k;
                boolean m5161a = c2123j.f8500o.m5161a();
                Iterator it = c2123j.f8461b.iterator();
                while (it.hasNext()) {
                    ((InterfaceC2115b) it.next()).mo1479d(m5161a);
                }
                break;
            case 1:
                C1324c c1324c = this.f8466k.f8492g;
                if (c1324c != null) {
                    Executors.newSingleThreadExecutor().execute(new RunnableC0801q(c1324c, 1));
                    c1324c.m3238e();
                    break;
                }
                break;
            default:
                this.f8466k.m5307g(false);
                break;
        }
    }
}
