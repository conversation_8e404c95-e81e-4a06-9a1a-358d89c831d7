package p006a5;

import android.content.Intent;
import android.net.wifi.WifiManager;
import android.util.Log;
import android.view.View;
import com.liaoyuan.aicast.phone.widget.ClearEditText;
import com.liaoyuan.aicast.phone.widget.DisconnectLayout;
import com.liaoyuan.aicast.phone.wifip2p.view.WifiP2pActivity;
import p048g5.C0891c;
import p048g5.RunnableC0889a;

/* renamed from: a5.b */
/* loaded from: classes.dex */
public final /* synthetic */ class ViewOnClickListenerC0039b implements View.OnClickListener {

    /* renamed from: j */
    public final /* synthetic */ int f165j;

    /* renamed from: k */
    public final /* synthetic */ Object f166k;

    public /* synthetic */ ViewOnClickListenerC0039b(Object obj, int i6) {
        this.f165j = i6;
        this.f166k = obj;
    }

    @Override // android.view.View.OnClickListener
    public final void onClick(View view) {
        switch (this.f165j) {
            case 0:
                ((PresentationC0040c) this.f166k).f168j.m5315o(false);
                break;
            case 1:
                ((ClearEditText) this.f166k).f3510j.setText("");
                break;
            case 2:
                ((DisconnectLayout) this.f166k).f3514k.setVisibility(8);
                break;
            default:
                C0891c c0891c = ((WifiP2pActivity) this.f166k).f3541I;
                boolean m2432f = c0891c.m2432f();
                Log.d("BoxWifiP2pPresenter", "doRequestPermissions hasFineLocationPermission:" + m2432f);
                if (!m2432f) {
                    c0891c.f8462c.post(new RunnableC0889a(c0891c, 2, 0));
                    break;
                } else if (!((WifiManager) c0891c.f4501d.getApplicationContext().getSystemService("wifi")).isWifiEnabled()) {
                    Intent intent = new Intent("android.settings.WIFI_SETTINGS");
                    intent.setFlags(268435456);
                    c0891c.f4501d.startActivity(intent);
                    break;
                }
                break;
        }
    }
}
