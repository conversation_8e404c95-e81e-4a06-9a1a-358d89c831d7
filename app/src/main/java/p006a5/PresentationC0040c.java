package p006a5;

import android.app.Presentation;
import android.content.Context;
import android.util.Log;
import android.view.Display;
import android.view.View;
import android.webkit.WebView;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;
import com.liaoyuan.aicast.R;
import com.liaoyuan.aicast.phone.browser.model.BrowserInfo;
import com.liaoyuan.aicast.phone.widget.BrowserLayout;
import com.liaoyuan.aicast.phone.widget.DisconnectLayout;
import com.liaoyuan.aicast.phone.widget.TipBrowserLayout;
import com.liaoyuan.aicast.phone.widget.TipLocalBrowserLayout;
import com.liaoyuan.aicast.phone.widget.ToastBrowserLayout;
import java.util.List;
import p031e2.C0800p;
import p041f5.C0865b;
import p062i5.C0952a;
import p120r4.C1322a;
import p160x4.RunnableC2052b;
import p173z4.C2123j;
import p173z4.InterfaceC2115b;

/* renamed from: a5.c */
/* loaded from: classes.dex */
public final class PresentationC0040c extends Presentation {

    /* renamed from: v */
    public static final /* synthetic */ int f167v = 0;

    /* renamed from: j */
    public C2123j f168j;

    /* renamed from: k */
    public View f169k;

    /* renamed from: l */
    public BrowserLayout f170l;

    /* renamed from: m */
    public TipLocalBrowserLayout f171m;

    /* renamed from: n */
    public ToastBrowserLayout f172n;

    /* renamed from: o */
    public TipBrowserLayout f173o;

    /* renamed from: p */
    public DisconnectLayout f174p;

    /* renamed from: q */
    public int f175q;

    /* renamed from: r */
    public int f176r;

    /* renamed from: s */
    public C1322a f177s;

    /* renamed from: t */
    public a f178t;

    /* renamed from: u */
    public b f179u;

    /* renamed from: a5.c$a */
    public class a implements Runnable {
        public a() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            View view;
            int i6;
            View view2 = PresentationC0040c.this.f169k;
            if (view2 != null) {
                if (view2.getVisibility() == 0) {
                    view = PresentationC0040c.this.f169k;
                    i6 = 8;
                } else {
                    view = PresentationC0040c.this.f169k;
                    i6 = 0;
                }
                view.setVisibility(i6);
                if (PresentationC0040c.this.isShowing()) {
                    PresentationC0040c presentationC0040c = PresentationC0040c.this;
                    presentationC0040c.f169k.postDelayed(presentationC0040c.f178t, 33L);
                }
            }
        }
    }

    /* renamed from: a5.c$b */
    public class b implements InterfaceC2115b {
        public b() {
        }

        @Override // p173z4.InterfaceC2115b
        /* renamed from: a */
        public final void mo76a(boolean z5) {
            PresentationC0040c.this.f170l.setSelectStar(z5);
        }

        @Override // p173z4.InterfaceC2115b
        /* renamed from: c */
        public final void mo77c(boolean z5, String str) {
            BrowserLayout browserLayout;
            boolean z6;
            if (z5) {
                browserLayout = PresentationC0040c.this.f170l;
                z6 = true;
            } else {
                browserLayout = PresentationC0040c.this.f170l;
                z6 = false;
            }
            browserLayout.m1919j(z6);
        }

        @Override // p173z4.InterfaceC2115b
        /* renamed from: e */
        public final void mo78e(List<BrowserInfo> list) {
            BrowserLayout browserLayout = PresentationC0040c.this.f170l;
            browserLayout.f3483J = list;
            browserLayout.m1913d();
        }

        @Override // p173z4.InterfaceC2115b
        /* renamed from: h */
        public final void mo79h(WebView webView) {
            PresentationC0040c.this.f170l.m1917h(false, 0);
            PresentationC0040c.this.f170l.setEnableReward(webView.canGoBack());
            PresentationC0040c.this.f170l.setEnableForward(webView.canGoForward());
            PresentationC0040c.this.f170l.setEnableHome(true);
            PresentationC0040c.this.f170l.setEnableRefresh(true);
            PresentationC0040c.this.f170l.setEnableStar(true);
        }

        @Override // p173z4.InterfaceC2115b
        /* renamed from: i */
        public final void mo80i(int i6) {
            PresentationC0040c presentationC0040c = PresentationC0040c.this;
            presentationC0040c.f172n.setToast(presentationC0040c.getContext().getResources().getString(R.string.virtual_toast_phone_permissions));
            ToastBrowserLayout toastBrowserLayout = PresentationC0040c.this.f172n;
            toastBrowserLayout.setVisibility(0);
            toastBrowserLayout.removeCallbacks(toastBrowserLayout.f3531k);
            toastBrowserLayout.postDelayed(toastBrowserLayout.f3531k, 3000L);
            C0952a.m2489b(toastBrowserLayout.getContext(), R.layout.layout_browser_toast, toastBrowserLayout.getRootView());
        }

        @Override // p173z4.InterfaceC2115b
        /* renamed from: j */
        public final void mo81j(boolean z5) {
            if (!z5) {
                PresentationC0040c.this.f171m.f3529k.setVisibility(0);
                return;
            }
            PresentationC0040c presentationC0040c = PresentationC0040c.this;
            presentationC0040c.f168j.m5318r(presentationC0040c.f170l.getWebView());
            PresentationC0040c.this.f171m.f3529k.setVisibility(8);
        }

        @Override // p173z4.InterfaceC2115b
        /* renamed from: k */
        public final void mo82k(boolean z5, WebView webView, boolean z6, int i6) {
            PresentationC0040c.this.f170l.m1917h(z6, i6);
            PresentationC0040c.this.f170l.setEnableReward(webView.canGoBack());
            PresentationC0040c.this.f170l.setEnableForward(webView.canGoForward());
            PresentationC0040c.this.f170l.setEnableHome(true);
            PresentationC0040c.this.f170l.setEnableRefresh(true);
            PresentationC0040c.this.f170l.setEnableStar(true);
        }
    }

    public PresentationC0040c(Context context, Display display, boolean z5, C1322a c1322a) {
        super(context, display, R.style.FullScreenDialog);
        this.f178t = new a();
        this.f179u = new b();
        setContentView(R.layout.cast_presentation);
        this.f175q = display.getWidth();
        this.f176r = display.getHeight();
        int displayId = display.getDisplayId();
        this.f177s = c1322a;
        Log.d("ProjectionScreenPresentation", "---------------创建虚拟屏---------------horizontal:" + z5 + ",mDisplayWidth:" + this.f175q + ",mDisplayHeight:" + this.f176r + ",displayId:" + displayId);
        C2123j c2123j = C2123j.h.f8521a;
        this.f168j = c2123j;
        c2123j.m5309i(getContext());
        this.f169k = findViewById(R.id.view_point);
        this.f171m = (TipLocalBrowserLayout) findViewById(R.id.tip_local_browser_layout);
        this.f172n = (ToastBrowserLayout) findViewById(R.id.toast_browser_layout);
        this.f173o = (TipBrowserLayout) findViewById(R.id.tip_input_browser_layout);
        this.f174p = (DisconnectLayout) findViewById(R.id.tip_disconnect_layout);
        BrowserLayout browserLayout = (BrowserLayout) findViewById(R.id.bl_remote);
        this.f170l = browserLayout;
        final int i6 = 1;
        browserLayout.setAutoHideBrowserNavi(true);
        final int i7 = 0;
        this.f170l.f3491o.f3506k.setVisibility(0);
        this.f170l.f3491o.f3507l.setVisibility(0);
        this.f170l.m1918i();
        this.f170l.f3482I = true;
        int dimension = (int) getContext().getResources().getDimension(R.dimen.browser_naiv_def_siez);
        if (z5) {
            BrowserLayout browserLayout2 = this.f170l;
            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) browserLayout2.f3475B.getLayoutParams();
            layoutParams.setMargins(dimension, 0, 0, 0);
            browserLayout2.f3475B.setLayoutParams(layoutParams);
            this.f170l.m1916g(0, dimension, -1);
        } else {
            BrowserLayout browserLayout3 = this.f170l;
            RelativeLayout.LayoutParams layoutParams2 = (RelativeLayout.LayoutParams) browserLayout3.f3475B.getLayoutParams();
            layoutParams2.setMargins(0, 0, 0, dimension);
            browserLayout3.f3475B.setLayoutParams(layoutParams2);
            this.f170l.m1916g(2, -1, dimension);
        }
        this.f168j.m5318r(this.f170l.getWebView());
        this.f168j.m5302c(this.f179u);
        this.f170l.setOnClickBrowserListener(new C0041d(this));
        this.f170l.setOnItemClickListener(new C0800p(this, 6));
        this.f171m.setOnConfirmClickListener(new View.OnClickListener(this) { // from class: a5.a

            /* renamed from: k */
            public final /* synthetic */ PresentationC0040c f164k;

            {
                this.f164k = this;
            }

            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                switch (i7) {
                    case 0:
                        this.f164k.f168j.m5315o(true);
                        break;
                    default:
                        PresentationC0040c presentationC0040c = this.f164k;
                        presentationC0040c.f174p.f3514k.setVisibility(8);
                        C2123j c2123j2 = presentationC0040c.f168j;
                        C0865b c0865b = c2123j2.f8498m;
                        if (c0865b.f4391r) {
                            c0865b.m2388d();
                            c2123j2.f8498m.m2390f();
                            break;
                        }
                        break;
                }
            }
        });
        this.f173o.setOnConfirmClickListener(new ViewOnClickListenerC0039b(this, i7));
        this.f174p.setOnConfirmClickListener(new View.OnClickListener(this) { // from class: a5.a

            /* renamed from: k */
            public final /* synthetic */ PresentationC0040c f164k;

            {
                this.f164k = this;
            }

            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                switch (i6) {
                    case 0:
                        this.f164k.f168j.m5315o(true);
                        break;
                    default:
                        PresentationC0040c presentationC0040c = this.f164k;
                        presentationC0040c.f174p.f3514k.setVisibility(8);
                        C2123j c2123j2 = presentationC0040c.f168j;
                        C0865b c0865b = c2123j2.f8498m;
                        if (c0865b.f4391r) {
                            c0865b.m2388d();
                            c2123j2.f8498m.m2390f();
                            break;
                        }
                        break;
                }
            }
        });
        getWindow().getDecorView().post(new RunnableC2052b(this, i6));
        m74a();
    }

    /* renamed from: a */
    public final void m74a() {
        int[] iArr;
        C1322a c1322a = this.f177s;
        int i6 = this.f175q;
        int i7 = this.f176r;
        int m3232a = c1322a.m3232a();
        if (i6 > i7) {
            if (m3232a == 0) {
                iArr = new int[]{i6, i7};
            } else if (m3232a == 1) {
                int i8 = (int) (i7 * 1.7777778f);
                if (i8 <= i6) {
                    i6 = i8;
                }
                iArr = new int[]{i6, i7};
            } else if (m3232a == 2) {
                int i9 = (int) (i7 * 1.3333334f);
                if (i9 <= i6) {
                    i6 = i9;
                }
                iArr = new int[]{i6, i7};
            } else if (m3232a == 3) {
                int i10 = (int) (i7 * 2.0f);
                if (i10 <= i6) {
                    i6 = i10;
                }
                iArr = new int[]{i6, i7};
            } else if (m3232a == 4) {
                int i11 = (int) (i7 * 1.0f);
                if (i11 <= i6) {
                    i6 = i11;
                }
                iArr = new int[]{i6, i7};
            }
            BrowserLayout browserLayout = this.f170l;
            int i12 = iArr[0];
            int i13 = iArr[1];
            FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) browserLayout.f3489m.getLayoutParams();
            layoutParams.width = i12;
            layoutParams.height = i13;
            browserLayout.f3489m.setLayoutParams(layoutParams);
        }
        iArr = new int[]{i6, i7};
        BrowserLayout browserLayout2 = this.f170l;
        int i122 = iArr[0];
        int i132 = iArr[1];
        FrameLayout.LayoutParams layoutParams2 = (FrameLayout.LayoutParams) browserLayout2.f3489m.getLayoutParams();
        layoutParams2.width = i122;
        layoutParams2.height = i132;
        browserLayout2.f3489m.setLayoutParams(layoutParams2);
    }

    /* renamed from: b */
    public final void m75b() {
        if (isShowing()) {
            return;
        }
        try {
            show();
        } catch (Exception e6) {
            e6.printStackTrace();
            Log.d("ProjectionScreenPresentation", "showList e:" + e6.getMessage());
        }
        this.f169k.post(this.f178t);
    }

    @Override // android.app.Dialog, android.content.DialogInterface
    public final void dismiss() {
        try {
            super.dismiss();
        } catch (Exception e6) {
            e6.printStackTrace();
            Log.d("ProjectionScreenPresentation", "dismiss e:" + e6.getMessage());
        }
        this.f169k.removeCallbacks(this.f178t);
    }
}
