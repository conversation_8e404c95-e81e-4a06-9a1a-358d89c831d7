package p006a5;

import android.view.View;
import com.liaoyuan.aicast.R;
import com.liaoyuan.aicast.phone.widget.BrowserLayout;
import com.liaoyuan.aicast.phone.widget.TipBrowserLayout;

/* renamed from: a5.d */
/* loaded from: classes.dex */
public final class C0041d implements BrowserLayout.InterfaceC0669c {

    /* renamed from: a */
    public final /* synthetic */ PresentationC0040c f182a;

    public C0041d(PresentationC0040c presentationC0040c) {
        this.f182a = presentationC0040c;
    }

    @Override // com.liaoyuan.aicast.phone.widget.BrowserLayout.InterfaceC0669c
    /* renamed from: a */
    public final void mo83a() {
        TipBrowserLayout tipBrowserLayout = this.f182a.f173o;
        tipBrowserLayout.setVisibility(0);
        tipBrowserLayout.removeCallbacks(tipBrowserLayout.f3526l);
        tipBrowserLayout.postDelayed(tipBrowserLayout.f3526l, 3000L);
    }

    @Override // com.liaoyuan.aicast.phone.widget.BrowserLayout.InterfaceC0669c
    /* renamed from: b */
    public final void mo84b(View view) {
        switch (view.getId()) {
            case R.id.forward /* 2131296437 */:
                this.f182a.f168j.f8491f.goForward();
                break;
            case R.id.home /* 2131296451 */:
                if (!(this.f182a.f170l.f3487k.getVisibility() == 0)) {
                    this.f182a.f170l.m1919j(true);
                    break;
                } else {
                    this.f182a.f170l.m1919j(false);
                    break;
                }
            case R.id.refresh /* 2131296587 */:
                this.f182a.f168j.f8491f.reload();
                break;
            case R.id.reward /* 2131296589 */:
                this.f182a.f168j.f8491f.goBack();
                break;
            case R.id.send_to_phone /* 2131296634 */:
                this.f182a.f174p.f3514k.setVisibility(0);
                break;
            case R.id.star /* 2131296658 */:
                this.f182a.f168j.m5306f();
                break;
        }
    }
}
