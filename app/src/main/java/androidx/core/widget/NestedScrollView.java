package androidx.core.widget;

import android.R;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.FocusFinder;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import android.view.animation.AnimationUtils;
import android.widget.EdgeEffect;
import android.widget.FrameLayout;
import android.widget.OverScroller;
import android.widget.ScrollView;
import androidx.activity.result.C0052a;
import androidx.appcompat.app.AlertController;
import java.util.ArrayList;
import java.util.WeakHashMap;
import p021d.C0677b;
import p029e0.C0751a;
import p029e0.C0758h;
import p029e0.C0761k;
import p029e0.C0766p;
import p029e0.C0769s;
import p029e0.InterfaceC0757g;
import p029e0.InterfaceC0760j;
import p036f0.C0831b;

/* loaded from: classes.dex */
public class NestedScrollView extends FrameLayout implements InterfaceC0760j, InterfaceC0757g {

    /* renamed from: J */
    public static final C0196a f1315J = new C0196a();

    /* renamed from: K */
    public static final int[] f1316K = {R.attr.fillViewport};

    /* renamed from: A */
    public final int[] f1317A;

    /* renamed from: B */
    public final int[] f1318B;

    /* renamed from: C */
    public int f1319C;

    /* renamed from: D */
    public int f1320D;

    /* renamed from: E */
    public C0198c f1321E;

    /* renamed from: F */
    public final C0761k f1322F;

    /* renamed from: G */
    public final C0758h f1323G;

    /* renamed from: H */
    public float f1324H;

    /* renamed from: I */
    public InterfaceC0197b f1325I;

    /* renamed from: j */
    public long f1326j;

    /* renamed from: k */
    public final Rect f1327k;

    /* renamed from: l */
    public OverScroller f1328l;

    /* renamed from: m */
    public EdgeEffect f1329m;

    /* renamed from: n */
    public EdgeEffect f1330n;

    /* renamed from: o */
    public int f1331o;

    /* renamed from: p */
    public boolean f1332p;

    /* renamed from: q */
    public boolean f1333q;

    /* renamed from: r */
    public View f1334r;

    /* renamed from: s */
    public boolean f1335s;

    /* renamed from: t */
    public VelocityTracker f1336t;

    /* renamed from: u */
    public boolean f1337u;

    /* renamed from: v */
    public boolean f1338v;

    /* renamed from: w */
    public int f1339w;

    /* renamed from: x */
    public int f1340x;

    /* renamed from: y */
    public int f1341y;

    /* renamed from: z */
    public int f1342z;

    /* renamed from: androidx.core.widget.NestedScrollView$a */
    public static class C0196a extends C0751a {
        @Override // p029e0.C0751a
        /* renamed from: c */
        public final void mo612c(View view, AccessibilityEvent accessibilityEvent) {
            super.mo612c(view, accessibilityEvent);
            NestedScrollView nestedScrollView = (NestedScrollView) view;
            accessibilityEvent.setClassName(ScrollView.class.getName());
            accessibilityEvent.setScrollable(nestedScrollView.getScrollRange() > 0);
            accessibilityEvent.setScrollX(nestedScrollView.getScrollX());
            accessibilityEvent.setScrollY(nestedScrollView.getScrollY());
            accessibilityEvent.setMaxScrollX(nestedScrollView.getScrollX());
            accessibilityEvent.setMaxScrollY(nestedScrollView.getScrollRange());
        }

        @Override // p029e0.C0751a
        /* renamed from: d */
        public final void mo613d(View view, C0831b c0831b) {
            int scrollRange;
            this.f4012a.onInitializeAccessibilityNodeInfo(view, c0831b.f4255a);
            NestedScrollView nestedScrollView = (NestedScrollView) view;
            c0831b.m2325n(ScrollView.class.getName());
            if (!nestedScrollView.isEnabled() || (scrollRange = nestedScrollView.getScrollRange()) <= 0) {
                return;
            }
            c0831b.m2333v(true);
            if (nestedScrollView.getScrollY() > 0) {
                c0831b.m2315b(C0831b.a.f4260g);
                c0831b.m2315b(C0831b.a.f4264k);
            }
            if (nestedScrollView.getScrollY() < scrollRange) {
                c0831b.m2315b(C0831b.a.f4259f);
                c0831b.m2315b(C0831b.a.f4265l);
            }
        }

        @Override // p029e0.C0751a
        /* renamed from: g */
        public final boolean mo614g(View view, int i6, Bundle bundle) {
            int min;
            if (super.mo614g(view, i6, bundle)) {
                return true;
            }
            NestedScrollView nestedScrollView = (NestedScrollView) view;
            if (!nestedScrollView.isEnabled()) {
                return false;
            }
            if (i6 != 4096) {
                if (i6 == 8192 || i6 == 16908344) {
                    min = Math.max(nestedScrollView.getScrollY() - ((nestedScrollView.getHeight() - nestedScrollView.getPaddingBottom()) - nestedScrollView.getPaddingTop()), 0);
                    if (min == nestedScrollView.getScrollY()) {
                        return false;
                    }
                    nestedScrollView.m591B(0 - nestedScrollView.getScrollX(), min - nestedScrollView.getScrollY(), true);
                    return true;
                }
                if (i6 != 16908346) {
                    return false;
                }
            }
            min = Math.min(nestedScrollView.getScrollY() + ((nestedScrollView.getHeight() - nestedScrollView.getPaddingBottom()) - nestedScrollView.getPaddingTop()), nestedScrollView.getScrollRange());
            if (min == nestedScrollView.getScrollY()) {
                return false;
            }
            nestedScrollView.m591B(0 - nestedScrollView.getScrollX(), min - nestedScrollView.getScrollY(), true);
            return true;
        }
    }

    /* renamed from: androidx.core.widget.NestedScrollView$b */
    public interface InterfaceC0197b {
    }

    /* renamed from: androidx.core.widget.NestedScrollView$c */
    public static class C0198c extends View.BaseSavedState {
        public static final Parcelable.Creator<C0198c> CREATOR = new a();

        /* renamed from: j */
        public int f1343j;

        /* renamed from: androidx.core.widget.NestedScrollView$c$a */
        public class a implements Parcelable.Creator<C0198c> {
            @Override // android.os.Parcelable.Creator
            public final C0198c createFromParcel(Parcel parcel) {
                return new C0198c(parcel);
            }

            @Override // android.os.Parcelable.Creator
            public final C0198c[] newArray(int i6) {
                return new C0198c[i6];
            }
        }

        public C0198c(Parcel parcel) {
            super(parcel);
            this.f1343j = parcel.readInt();
        }

        public C0198c(Parcelable parcelable) {
            super(parcelable);
        }

        public final String toString() {
            StringBuilder m104h = C0052a.m104h("HorizontalScrollView.SavedState{");
            m104h.append(Integer.toHexString(System.identityHashCode(this)));
            m104h.append(" scrollPosition=");
            m104h.append(this.f1343j);
            m104h.append("}");
            return m104h.toString();
        }

        @Override // android.view.View.BaseSavedState, android.view.AbsSavedState, android.os.Parcelable
        public final void writeToParcel(Parcel parcel, int i6) {
            super.writeToParcel(parcel, i6);
            parcel.writeInt(this.f1343j);
        }
    }

    public NestedScrollView(Context context, AttributeSet attributeSet) {
        super(context, attributeSet, com.liaoyuan.aicast.R.attr.nestedScrollViewStyle);
        this.f1327k = new Rect();
        this.f1332p = true;
        this.f1333q = false;
        this.f1334r = null;
        this.f1335s = false;
        this.f1338v = true;
        this.f1342z = -1;
        this.f1317A = new int[2];
        this.f1318B = new int[2];
        this.f1328l = new OverScroller(getContext());
        setFocusable(true);
        setDescendantFocusability(262144);
        setWillNotDraw(false);
        ViewConfiguration viewConfiguration = ViewConfiguration.get(getContext());
        this.f1339w = viewConfiguration.getScaledTouchSlop();
        this.f1340x = viewConfiguration.getScaledMinimumFlingVelocity();
        this.f1341y = viewConfiguration.getScaledMaximumFlingVelocity();
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, f1316K, com.liaoyuan.aicast.R.attr.nestedScrollViewStyle, 0);
        setFillViewport(obtainStyledAttributes.getBoolean(0, false));
        obtainStyledAttributes.recycle();
        this.f1322F = new C0761k();
        this.f1323G = new C0758h(this);
        setNestedScrollingEnabled(true);
        C0766p.m2187t(this, f1315J);
    }

    /* renamed from: c */
    public static int m588c(int i6, int i7, int i8) {
        if (i7 >= i8 || i6 < 0) {
            return 0;
        }
        return i7 + i6 > i8 ? i8 - i7 : i6;
    }

    private float getVerticalScrollFactorCompat() {
        if (this.f1324H == 0.0f) {
            TypedValue typedValue = new TypedValue();
            Context context = getContext();
            if (!context.getTheme().resolveAttribute(R.attr.listPreferredItemHeight, typedValue, true)) {
                throw new IllegalStateException("Expected theme to define listPreferredItemHeight.");
            }
            this.f1324H = typedValue.getDimension(context.getResources().getDisplayMetrics());
        }
        return this.f1324H;
    }

    /* renamed from: s */
    public static boolean m589s(View view, View view2) {
        if (view == view2) {
            return true;
        }
        Object parent = view.getParent();
        return (parent instanceof ViewGroup) && m589s((View) parent, view2);
    }

    /* renamed from: A */
    public final void m590A(View view) {
        view.getDrawingRect(this.f1327k);
        offsetDescendantRectToMyCoords(view, this.f1327k);
        int m596d = m596d(this.f1327k);
        if (m596d != 0) {
            scrollBy(0, m596d);
        }
    }

    /* renamed from: B */
    public final void m591B(int i6, int i7, boolean z5) {
        if (getChildCount() == 0) {
            return;
        }
        if (AnimationUtils.currentAnimationTimeMillis() - this.f1326j > 250) {
            View childAt = getChildAt(0);
            FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) childAt.getLayoutParams();
            int height = childAt.getHeight() + layoutParams.topMargin + layoutParams.bottomMargin;
            int height2 = (getHeight() - getPaddingTop()) - getPaddingBottom();
            int scrollY = getScrollY();
            this.f1328l.startScroll(getScrollX(), scrollY, 0, Math.max(0, Math.min(i7 + scrollY, Math.max(0, height - height2))) - scrollY, 250);
            m610y(z5);
        } else {
            if (!this.f1328l.isFinished()) {
                m594a();
            }
            scrollBy(i6, i7);
        }
        this.f1326j = AnimationUtils.currentAnimationTimeMillis();
    }

    /* renamed from: C */
    public final boolean m592C(int i6, int i7) {
        return this.f1323G.m2162h(i6, i7);
    }

    /* renamed from: D */
    public final void m593D(int i6) {
        this.f1323G.m2163i(i6);
    }

    /* renamed from: a */
    public final void m594a() {
        this.f1328l.abortAnimation();
        m593D(1);
    }

    @Override // android.view.ViewGroup
    public final void addView(View view) {
        if (getChildCount() > 0) {
            throw new IllegalStateException("ScrollView can host only one direct child");
        }
        super.addView(view);
    }

    @Override // android.view.ViewGroup
    public final void addView(View view, int i6) {
        if (getChildCount() > 0) {
            throw new IllegalStateException("ScrollView can host only one direct child");
        }
        super.addView(view, i6);
    }

    @Override // android.view.ViewGroup
    public final void addView(View view, int i6, ViewGroup.LayoutParams layoutParams) {
        if (getChildCount() > 0) {
            throw new IllegalStateException("ScrollView can host only one direct child");
        }
        super.addView(view, i6, layoutParams);
    }

    @Override // android.view.ViewGroup, android.view.ViewManager
    public final void addView(View view, ViewGroup.LayoutParams layoutParams) {
        if (getChildCount() > 0) {
            throw new IllegalStateException("ScrollView can host only one direct child");
        }
        super.addView(view, layoutParams);
    }

    /* renamed from: b */
    public final boolean m595b(int i6) {
        View findFocus = findFocus();
        if (findFocus == this) {
            findFocus = null;
        }
        View findNextFocus = FocusFinder.getInstance().findNextFocus(this, findFocus, i6);
        int maxScrollAmount = getMaxScrollAmount();
        if (findNextFocus == null || !m605t(findNextFocus, maxScrollAmount, getHeight())) {
            if (i6 == 33 && getScrollY() < maxScrollAmount) {
                maxScrollAmount = getScrollY();
            } else if (i6 == 130 && getChildCount() > 0) {
                View childAt = getChildAt(0);
                maxScrollAmount = Math.min((childAt.getBottom() + ((FrameLayout.LayoutParams) childAt.getLayoutParams()).bottomMargin) - ((getHeight() + getScrollY()) - getPaddingBottom()), maxScrollAmount);
            }
            if (maxScrollAmount == 0) {
                return false;
            }
            if (i6 != 130) {
                maxScrollAmount = -maxScrollAmount;
            }
            m598f(maxScrollAmount);
        } else {
            findNextFocus.getDrawingRect(this.f1327k);
            offsetDescendantRectToMyCoords(findNextFocus, this.f1327k);
            m598f(m596d(this.f1327k));
            findNextFocus.requestFocus(i6);
        }
        if (findFocus != null && findFocus.isFocused() && (!m605t(findFocus, 0, getHeight()))) {
            int descendantFocusability = getDescendantFocusability();
            setDescendantFocusability(131072);
            requestFocus();
            setDescendantFocusability(descendantFocusability);
        }
        return true;
    }

    @Override // android.view.View
    public final int computeHorizontalScrollExtent() {
        return super.computeHorizontalScrollExtent();
    }

    @Override // android.view.View
    public final int computeHorizontalScrollOffset() {
        return super.computeHorizontalScrollOffset();
    }

    @Override // android.view.View
    public final int computeHorizontalScrollRange() {
        return super.computeHorizontalScrollRange();
    }

    @Override // android.view.View
    public final void computeScroll() {
        EdgeEffect edgeEffect;
        if (this.f1328l.isFinished()) {
            return;
        }
        this.f1328l.computeScrollOffset();
        int currY = this.f1328l.getCurrY();
        int i6 = currY - this.f1320D;
        this.f1320D = currY;
        int[] iArr = this.f1318B;
        boolean z5 = false;
        iArr[1] = 0;
        m597e(0, i6, iArr, null, 1);
        int i7 = i6 - this.f1318B[1];
        int scrollRange = getScrollRange();
        if (i7 != 0) {
            int scrollY = getScrollY();
            m608w(i7, getScrollX(), scrollY, scrollRange);
            int scrollY2 = getScrollY() - scrollY;
            int i8 = i7 - scrollY2;
            int[] iArr2 = this.f1318B;
            iArr2[1] = 0;
            this.f1323G.m2159e(0, scrollY2, 0, i8, this.f1317A, 1, iArr2);
            i7 = i8 - this.f1318B[1];
        }
        if (i7 != 0) {
            int overScrollMode = getOverScrollMode();
            if (overScrollMode == 0 || (overScrollMode == 1 && scrollRange > 0)) {
                z5 = true;
            }
            if (z5) {
                m600k();
                if (i7 < 0) {
                    if (this.f1329m.isFinished()) {
                        edgeEffect = this.f1329m;
                        edgeEffect.onAbsorb((int) this.f1328l.getCurrVelocity());
                    }
                } else if (this.f1330n.isFinished()) {
                    edgeEffect = this.f1330n;
                    edgeEffect.onAbsorb((int) this.f1328l.getCurrVelocity());
                }
            }
            m594a();
        }
        if (this.f1328l.isFinished()) {
            m593D(1);
        } else {
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            postInvalidateOnAnimation();
        }
    }

    @Override // android.view.View
    public final int computeVerticalScrollExtent() {
        return super.computeVerticalScrollExtent();
    }

    @Override // android.view.View
    public final int computeVerticalScrollOffset() {
        return Math.max(0, super.computeVerticalScrollOffset());
    }

    @Override // android.view.View
    public final int computeVerticalScrollRange() {
        int childCount = getChildCount();
        int height = (getHeight() - getPaddingBottom()) - getPaddingTop();
        if (childCount == 0) {
            return height;
        }
        View childAt = getChildAt(0);
        int bottom = childAt.getBottom() + ((FrameLayout.LayoutParams) childAt.getLayoutParams()).bottomMargin;
        int scrollY = getScrollY();
        int max = Math.max(0, bottom - height);
        return scrollY < 0 ? bottom - scrollY : scrollY > max ? bottom + (scrollY - max) : bottom;
    }

    /* renamed from: d */
    public final int m596d(Rect rect) {
        if (getChildCount() == 0) {
            return 0;
        }
        int height = getHeight();
        int scrollY = getScrollY();
        int i6 = scrollY + height;
        int verticalFadingEdgeLength = getVerticalFadingEdgeLength();
        if (rect.top > 0) {
            scrollY += verticalFadingEdgeLength;
        }
        View childAt = getChildAt(0);
        FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) childAt.getLayoutParams();
        int i7 = rect.bottom < (childAt.getHeight() + layoutParams.topMargin) + layoutParams.bottomMargin ? i6 - verticalFadingEdgeLength : i6;
        int i8 = rect.bottom;
        if (i8 > i7 && rect.top > scrollY) {
            return Math.min((rect.height() > height ? rect.top - scrollY : rect.bottom - i7) + 0, (childAt.getBottom() + layoutParams.bottomMargin) - i6);
        }
        if (rect.top >= scrollY || i8 >= i7) {
            return 0;
        }
        return Math.max(rect.height() > height ? 0 - (i7 - rect.bottom) : 0 - (scrollY - rect.top), -getScrollY());
    }

    @Override // android.view.ViewGroup, android.view.View
    public final boolean dispatchKeyEvent(KeyEvent keyEvent) {
        return super.dispatchKeyEvent(keyEvent) || m601l(keyEvent);
    }

    @Override // android.view.View
    public final boolean dispatchNestedFling(float f6, float f7, boolean z5) {
        return this.f1323G.m2155a(f6, f7, z5);
    }

    @Override // android.view.View
    public final boolean dispatchNestedPreFling(float f6, float f7) {
        return this.f1323G.m2156b(f6, f7);
    }

    @Override // android.view.View
    public final boolean dispatchNestedPreScroll(int i6, int i7, int[] iArr, int[] iArr2) {
        return m597e(i6, i7, iArr, iArr2, 0);
    }

    @Override // android.view.View
    public final boolean dispatchNestedScroll(int i6, int i7, int i8, int i9, int[] iArr) {
        return this.f1323G.m2159e(i6, i7, i8, i9, iArr, 0, null);
    }

    @Override // android.view.View
    public final void draw(Canvas canvas) {
        int i6;
        super.draw(canvas);
        if (this.f1329m != null) {
            int scrollY = getScrollY();
            int i7 = 0;
            if (!this.f1329m.isFinished()) {
                int save = canvas.save();
                int width = getWidth();
                int height = getHeight();
                int min = Math.min(0, scrollY);
                if (getClipToPadding()) {
                    width -= getPaddingRight() + getPaddingLeft();
                    i6 = getPaddingLeft() + 0;
                } else {
                    i6 = 0;
                }
                if (getClipToPadding()) {
                    height -= getPaddingBottom() + getPaddingTop();
                    min += getPaddingTop();
                }
                canvas.translate(i6, min);
                this.f1329m.setSize(width, height);
                if (this.f1329m.draw(canvas)) {
                    WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
                    postInvalidateOnAnimation();
                }
                canvas.restoreToCount(save);
            }
            if (this.f1330n.isFinished()) {
                return;
            }
            int save2 = canvas.save();
            int width2 = getWidth();
            int height2 = getHeight();
            int max = Math.max(getScrollRange(), scrollY) + height2;
            if (getClipToPadding()) {
                width2 -= getPaddingRight() + getPaddingLeft();
                i7 = 0 + getPaddingLeft();
            }
            if (getClipToPadding()) {
                height2 -= getPaddingBottom() + getPaddingTop();
                max -= getPaddingBottom();
            }
            canvas.translate(i7 - width2, max);
            canvas.rotate(180.0f, width2, 0.0f);
            this.f1330n.setSize(width2, height2);
            if (this.f1330n.draw(canvas)) {
                WeakHashMap<View, C0769s> weakHashMap2 = C0766p.f4041a;
                postInvalidateOnAnimation();
            }
            canvas.restoreToCount(save2);
        }
    }

    /* renamed from: e */
    public final boolean m597e(int i6, int i7, int[] iArr, int[] iArr2, int i8) {
        return this.f1323G.m2157c(i6, i7, iArr, iArr2, i8);
    }

    /* renamed from: f */
    public final void m598f(int i6) {
        if (i6 != 0) {
            if (this.f1338v) {
                m591B(0, i6, false);
            } else {
                scrollBy(0, i6);
            }
        }
    }

    /* renamed from: g */
    public final void m599g() {
        this.f1335s = false;
        m609x();
        m593D(0);
        EdgeEffect edgeEffect = this.f1329m;
        if (edgeEffect != null) {
            edgeEffect.onRelease();
            this.f1330n.onRelease();
        }
    }

    @Override // android.view.View
    public float getBottomFadingEdgeStrength() {
        if (getChildCount() == 0) {
            return 0.0f;
        }
        View childAt = getChildAt(0);
        FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) childAt.getLayoutParams();
        int verticalFadingEdgeLength = getVerticalFadingEdgeLength();
        int bottom = ((childAt.getBottom() + layoutParams.bottomMargin) - getScrollY()) - (getHeight() - getPaddingBottom());
        if (bottom < verticalFadingEdgeLength) {
            return bottom / verticalFadingEdgeLength;
        }
        return 1.0f;
    }

    public int getMaxScrollAmount() {
        return (int) (getHeight() * 0.5f);
    }

    @Override // android.view.ViewGroup
    public int getNestedScrollAxes() {
        C0761k c0761k = this.f1322F;
        return c0761k.f4037b | c0761k.f4036a;
    }

    public int getScrollRange() {
        if (getChildCount() <= 0) {
            return 0;
        }
        View childAt = getChildAt(0);
        FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) childAt.getLayoutParams();
        return Math.max(0, ((childAt.getHeight() + layoutParams.topMargin) + layoutParams.bottomMargin) - ((getHeight() - getPaddingTop()) - getPaddingBottom()));
    }

    @Override // android.view.View
    public float getTopFadingEdgeStrength() {
        if (getChildCount() == 0) {
            return 0.0f;
        }
        int verticalFadingEdgeLength = getVerticalFadingEdgeLength();
        int scrollY = getScrollY();
        if (scrollY < verticalFadingEdgeLength) {
            return scrollY / verticalFadingEdgeLength;
        }
        return 1.0f;
    }

    @Override // p029e0.InterfaceC0759i
    /* renamed from: h */
    public final void mo237h(View view, View view2, int i6, int i7) {
        this.f1322F.m2164a(i6, i7);
        m592C(2, i7);
    }

    @Override // android.view.View
    public final boolean hasNestedScrollingParent() {
        return m604r(0);
    }

    @Override // p029e0.InterfaceC0759i
    /* renamed from: i */
    public final void mo238i(View view, int i6) {
        C0761k c0761k = this.f1322F;
        if (i6 == 1) {
            c0761k.f4037b = 0;
        } else {
            c0761k.f4036a = 0;
        }
        m593D(i6);
    }

    @Override // android.view.View
    public final boolean isNestedScrollingEnabled() {
        return this.f1323G.f4034d;
    }

    @Override // p029e0.InterfaceC0759i
    /* renamed from: j */
    public final void mo239j(View view, int i6, int i7, int[] iArr, int i8) {
        m597e(i6, i7, iArr, null, i8);
    }

    /* renamed from: k */
    public final void m600k() {
        if (getOverScrollMode() == 2) {
            this.f1329m = null;
            this.f1330n = null;
        } else if (this.f1329m == null) {
            Context context = getContext();
            this.f1329m = new EdgeEffect(context);
            this.f1330n = new EdgeEffect(context);
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:25:0x0062  */
    /* JADX WARN: Removed duplicated region for block: B:8:0x0038  */
    /* renamed from: l */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean m601l(android.view.KeyEvent r7) {
        /*
            Method dump skipped, instructions count: 251
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.core.widget.NestedScrollView.m601l(android.view.KeyEvent):boolean");
    }

    @Override // p029e0.InterfaceC0760j
    /* renamed from: m */
    public final void mo242m(View view, int i6, int i7, int i8, int i9, int i10, int[] iArr) {
        m606u(i9, i10, iArr);
    }

    @Override // android.view.ViewGroup
    public final void measureChild(View view, int i6, int i7) {
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        view.measure(ViewGroup.getChildMeasureSpec(i6, getPaddingRight() + getPaddingLeft(), layoutParams.width), View.MeasureSpec.makeMeasureSpec(0, 0));
    }

    @Override // android.view.ViewGroup
    public final void measureChildWithMargins(View view, int i6, int i7, int i8, int i9) {
        ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
        view.measure(ViewGroup.getChildMeasureSpec(i6, getPaddingRight() + getPaddingLeft() + marginLayoutParams.leftMargin + marginLayoutParams.rightMargin + i7, marginLayoutParams.width), View.MeasureSpec.makeMeasureSpec(marginLayoutParams.topMargin + marginLayoutParams.bottomMargin, 0));
    }

    @Override // p029e0.InterfaceC0759i
    /* renamed from: n */
    public final void mo243n(View view, int i6, int i7, int i8, int i9, int i10) {
        m606u(i9, i10, null);
    }

    @Override // p029e0.InterfaceC0759i
    /* renamed from: o */
    public final boolean mo244o(View view, View view2, int i6, int i7) {
        return (i6 & 2) != 0;
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onAttachedToWindow() {
        super.onAttachedToWindow();
        this.f1333q = false;
    }

    @Override // android.view.View
    public final boolean onGenericMotionEvent(MotionEvent motionEvent) {
        if ((motionEvent.getSource() & 2) != 0 && motionEvent.getAction() == 8 && !this.f1335s) {
            float axisValue = motionEvent.getAxisValue(9);
            if (axisValue != 0.0f) {
                int verticalScrollFactorCompat = (int) (axisValue * getVerticalScrollFactorCompat());
                int scrollRange = getScrollRange();
                int scrollY = getScrollY();
                int i6 = scrollY - verticalScrollFactorCompat;
                if (i6 < 0) {
                    scrollRange = 0;
                } else if (i6 <= scrollRange) {
                    scrollRange = i6;
                }
                if (scrollRange != scrollY) {
                    super.scrollTo(getScrollX(), scrollRange);
                    return true;
                }
            }
        }
        return false;
    }

    /* JADX WARN: Removed duplicated region for block: B:50:0x00e3  */
    /* JADX WARN: Removed duplicated region for block: B:51:0x00e9  */
    @Override // android.view.ViewGroup
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean onInterceptTouchEvent(android.view.MotionEvent r12) {
        /*
            Method dump skipped, instructions count: 280
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.core.widget.NestedScrollView.onInterceptTouchEvent(android.view.MotionEvent):boolean");
    }

    @Override // android.widget.FrameLayout, android.view.ViewGroup, android.view.View
    public final void onLayout(boolean z5, int i6, int i7, int i8, int i9) {
        super.onLayout(z5, i6, i7, i8, i9);
        int i10 = 0;
        this.f1332p = false;
        View view = this.f1334r;
        if (view != null && m589s(view, this)) {
            m590A(this.f1334r);
        }
        this.f1334r = null;
        if (!this.f1333q) {
            if (this.f1321E != null) {
                scrollTo(getScrollX(), this.f1321E.f1343j);
                this.f1321E = null;
            }
            if (getChildCount() > 0) {
                View childAt = getChildAt(0);
                FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) childAt.getLayoutParams();
                i10 = childAt.getMeasuredHeight() + layoutParams.topMargin + layoutParams.bottomMargin;
            }
            int paddingTop = ((i9 - i7) - getPaddingTop()) - getPaddingBottom();
            int scrollY = getScrollY();
            int m588c = m588c(scrollY, paddingTop, i10);
            if (m588c != scrollY) {
                scrollTo(getScrollX(), m588c);
            }
        }
        scrollTo(getScrollX(), getScrollY());
        this.f1333q = true;
    }

    @Override // android.widget.FrameLayout, android.view.View
    public final void onMeasure(int i6, int i7) {
        super.onMeasure(i6, i7);
        if (this.f1337u && View.MeasureSpec.getMode(i7) != 0 && getChildCount() > 0) {
            View childAt = getChildAt(0);
            FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) childAt.getLayoutParams();
            int measuredHeight = childAt.getMeasuredHeight();
            int measuredHeight2 = (((getMeasuredHeight() - getPaddingTop()) - getPaddingBottom()) - layoutParams.topMargin) - layoutParams.bottomMargin;
            if (measuredHeight < measuredHeight2) {
                childAt.measure(ViewGroup.getChildMeasureSpec(i6, getPaddingRight() + getPaddingLeft() + layoutParams.leftMargin + layoutParams.rightMargin, layoutParams.width), View.MeasureSpec.makeMeasureSpec(measuredHeight2, 1073741824));
            }
        }
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final boolean onNestedFling(View view, float f6, float f7, boolean z5) {
        if (z5) {
            return false;
        }
        dispatchNestedFling(0.0f, f7, true);
        m602p((int) f7);
        return true;
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final boolean onNestedPreFling(View view, float f6, float f7) {
        return dispatchNestedPreFling(f6, f7);
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final void onNestedPreScroll(View view, int i6, int i7, int[] iArr) {
        m597e(i6, i7, iArr, null, 0);
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final void onNestedScroll(View view, int i6, int i7, int i8, int i9) {
        m606u(i9, 0, null);
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final void onNestedScrollAccepted(View view, View view2, int i6) {
        this.f1322F.m2164a(i6, 0);
        m592C(2, 0);
    }

    @Override // android.view.View
    public final void onOverScrolled(int i6, int i7, boolean z5, boolean z6) {
        super.scrollTo(i6, i7);
    }

    @Override // android.view.ViewGroup
    public final boolean onRequestFocusInDescendants(int i6, Rect rect) {
        if (i6 == 2) {
            i6 = 130;
        } else if (i6 == 1) {
            i6 = 33;
        }
        FocusFinder focusFinder = FocusFinder.getInstance();
        View findNextFocus = rect == null ? focusFinder.findNextFocus(this, null, i6) : focusFinder.findNextFocusFromRect(this, rect, i6);
        if (findNextFocus == null || (true ^ m605t(findNextFocus, 0, getHeight()))) {
            return false;
        }
        return findNextFocus.requestFocus(i6, rect);
    }

    @Override // android.view.View
    public final void onRestoreInstanceState(Parcelable parcelable) {
        if (!(parcelable instanceof C0198c)) {
            super.onRestoreInstanceState(parcelable);
            return;
        }
        C0198c c0198c = (C0198c) parcelable;
        super.onRestoreInstanceState(c0198c.getSuperState());
        this.f1321E = c0198c;
        requestLayout();
    }

    @Override // android.view.View
    public final Parcelable onSaveInstanceState() {
        C0198c c0198c = new C0198c(super.onSaveInstanceState());
        c0198c.f1343j = getScrollY();
        return c0198c;
    }

    @Override // android.view.View
    public final void onScrollChanged(int i6, int i7, int i8, int i9) {
        super.onScrollChanged(i6, i7, i8, i9);
        InterfaceC0197b interfaceC0197b = this.f1325I;
        if (interfaceC0197b != null) {
            C0677b c0677b = (C0677b) interfaceC0197b;
            AlertController.m130b(this, c0677b.f3556a, c0677b.f3557b);
        }
    }

    @Override // android.view.View
    public final void onSizeChanged(int i6, int i7, int i8, int i9) {
        super.onSizeChanged(i6, i7, i8, i9);
        View findFocus = findFocus();
        if (findFocus == null || this == findFocus || !m605t(findFocus, 0, i9)) {
            return;
        }
        findFocus.getDrawingRect(this.f1327k);
        offsetDescendantRectToMyCoords(findFocus, this.f1327k);
        m598f(m596d(this.f1327k));
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final boolean onStartNestedScroll(View view, View view2, int i6) {
        return (i6 & 2) != 0;
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final void onStopNestedScroll(View view) {
        this.f1322F.f4036a = 0;
        m593D(0);
    }

    /* JADX WARN: Code restructure failed: missing block: B:31:0x007b, code lost:
    
        if (r22.f1328l.springBack(getScrollX(), getScrollY(), 0, 0, 0, getScrollRange()) != false) goto L86;
     */
    /* JADX WARN: Code restructure failed: missing block: B:32:0x01f7, code lost:
    
        r0 = p029e0.C0766p.f4041a;
        postInvalidateOnAnimation();
     */
    /* JADX WARN: Code restructure failed: missing block: B:89:0x01f5, code lost:
    
        if (r22.f1328l.springBack(getScrollX(), getScrollY(), 0, 0, 0, getScrollRange()) != false) goto L86;
     */
    @Override // android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean onTouchEvent(android.view.MotionEvent r23) {
        /*
            Method dump skipped, instructions count: 579
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.core.widget.NestedScrollView.onTouchEvent(android.view.MotionEvent):boolean");
    }

    /* renamed from: p */
    public final void m602p(int i6) {
        if (getChildCount() > 0) {
            this.f1328l.fling(getScrollX(), getScrollY(), 0, i6, 0, 0, Integer.MIN_VALUE, Integer.MAX_VALUE, 0, 0);
            m610y(true);
        }
    }

    /* renamed from: q */
    public final boolean m603q(int i6) {
        int childCount;
        boolean z5 = i6 == 130;
        int height = getHeight();
        Rect rect = this.f1327k;
        rect.top = 0;
        rect.bottom = height;
        if (z5 && (childCount = getChildCount()) > 0) {
            View childAt = getChildAt(childCount - 1);
            this.f1327k.bottom = getPaddingBottom() + childAt.getBottom() + ((FrameLayout.LayoutParams) childAt.getLayoutParams()).bottomMargin;
            Rect rect2 = this.f1327k;
            rect2.top = rect2.bottom - height;
        }
        Rect rect3 = this.f1327k;
        return m611z(i6, rect3.top, rect3.bottom);
    }

    /* renamed from: r */
    public final boolean m604r(int i6) {
        return this.f1323G.m2161g(i6);
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final void requestChildFocus(View view, View view2) {
        if (this.f1332p) {
            this.f1334r = view2;
        } else {
            m590A(view2);
        }
        super.requestChildFocus(view, view2);
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final boolean requestChildRectangleOnScreen(View view, Rect rect, boolean z5) {
        rect.offset(view.getLeft() - view.getScrollX(), view.getTop() - view.getScrollY());
        int m596d = m596d(rect);
        boolean z6 = m596d != 0;
        if (z6) {
            if (z5) {
                scrollBy(0, m596d);
            } else {
                m591B(0, m596d, false);
            }
        }
        return z6;
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final void requestDisallowInterceptTouchEvent(boolean z5) {
        if (z5) {
            m609x();
        }
        super.requestDisallowInterceptTouchEvent(z5);
    }

    @Override // android.view.View, android.view.ViewParent
    public final void requestLayout() {
        this.f1332p = true;
        super.requestLayout();
    }

    @Override // android.view.View
    public final void scrollTo(int i6, int i7) {
        if (getChildCount() > 0) {
            View childAt = getChildAt(0);
            FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) childAt.getLayoutParams();
            int width = (getWidth() - getPaddingLeft()) - getPaddingRight();
            int width2 = childAt.getWidth() + layoutParams.leftMargin + layoutParams.rightMargin;
            int height = (getHeight() - getPaddingTop()) - getPaddingBottom();
            int height2 = childAt.getHeight() + layoutParams.topMargin + layoutParams.bottomMargin;
            int m588c = m588c(i6, width, width2);
            int m588c2 = m588c(i7, height, height2);
            if (m588c == getScrollX() && m588c2 == getScrollY()) {
                return;
            }
            super.scrollTo(m588c, m588c2);
        }
    }

    public void setFillViewport(boolean z5) {
        if (z5 != this.f1337u) {
            this.f1337u = z5;
            requestLayout();
        }
    }

    @Override // android.view.View
    public void setNestedScrollingEnabled(boolean z5) {
        C0758h c0758h = this.f1323G;
        if (c0758h.f4034d) {
            View view = c0758h.f4033c;
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            view.stopNestedScroll();
        }
        c0758h.f4034d = z5;
    }

    public void setOnScrollChangeListener(InterfaceC0197b interfaceC0197b) {
        this.f1325I = interfaceC0197b;
    }

    public void setSmoothScrollingEnabled(boolean z5) {
        this.f1338v = z5;
    }

    @Override // android.widget.FrameLayout, android.view.ViewGroup
    public final boolean shouldDelayChildPressedState() {
        return true;
    }

    @Override // android.view.View
    public final boolean startNestedScroll(int i6) {
        return m592C(i6, 0);
    }

    @Override // android.view.View
    public final void stopNestedScroll() {
        m593D(0);
    }

    /* renamed from: t */
    public final boolean m605t(View view, int i6, int i7) {
        view.getDrawingRect(this.f1327k);
        offsetDescendantRectToMyCoords(view, this.f1327k);
        return this.f1327k.bottom + i6 >= getScrollY() && this.f1327k.top - i6 <= getScrollY() + i7;
    }

    /* renamed from: u */
    public final void m606u(int i6, int i7, int[] iArr) {
        int scrollY = getScrollY();
        scrollBy(0, i6);
        int scrollY2 = getScrollY() - scrollY;
        if (iArr != null) {
            iArr[1] = iArr[1] + scrollY2;
        }
        this.f1323G.m2158d(scrollY2, i6 - scrollY2, i7, iArr);
    }

    /* renamed from: v */
    public final void m607v(MotionEvent motionEvent) {
        int actionIndex = motionEvent.getActionIndex();
        if (motionEvent.getPointerId(actionIndex) == this.f1342z) {
            int i6 = actionIndex == 0 ? 1 : 0;
            this.f1331o = (int) motionEvent.getY(i6);
            this.f1342z = motionEvent.getPointerId(i6);
            VelocityTracker velocityTracker = this.f1336t;
            if (velocityTracker != null) {
                velocityTracker.clear();
            }
        }
    }

    /* renamed from: w */
    public final boolean m608w(int i6, int i7, int i8, int i9) {
        boolean z5;
        boolean z6;
        getOverScrollMode();
        computeHorizontalScrollRange();
        computeHorizontalScrollExtent();
        computeVerticalScrollRange();
        computeVerticalScrollExtent();
        int i10 = i7 + 0;
        int i11 = i8 + i6;
        int i12 = i9 + 0;
        if (i10 <= 0 && i10 >= 0) {
            z5 = false;
        } else {
            i10 = 0;
            z5 = true;
        }
        if (i11 > i12) {
            i11 = i12;
        } else {
            if (i11 >= 0) {
                z6 = false;
                if (z6 && !m604r(1)) {
                    this.f1328l.springBack(i10, i11, 0, 0, 0, getScrollRange());
                }
                onOverScrolled(i10, i11, z5, z6);
                return z5 || z6;
            }
            i11 = 0;
        }
        z6 = true;
        if (z6) {
            this.f1328l.springBack(i10, i11, 0, 0, 0, getScrollRange());
        }
        onOverScrolled(i10, i11, z5, z6);
        if (z5) {
            return true;
        }
    }

    /* renamed from: x */
    public final void m609x() {
        VelocityTracker velocityTracker = this.f1336t;
        if (velocityTracker != null) {
            velocityTracker.recycle();
            this.f1336t = null;
        }
    }

    /* renamed from: y */
    public final void m610y(boolean z5) {
        if (z5) {
            m592C(2, 1);
        } else {
            m593D(1);
        }
        this.f1320D = getScrollY();
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        postInvalidateOnAnimation();
    }

    /* renamed from: z */
    public final boolean m611z(int i6, int i7, int i8) {
        boolean z5;
        int height = getHeight();
        int scrollY = getScrollY();
        int i9 = height + scrollY;
        boolean z6 = i6 == 33;
        ArrayList<View> focusables = getFocusables(2);
        int size = focusables.size();
        View view = null;
        boolean z7 = false;
        for (int i10 = 0; i10 < size; i10++) {
            View view2 = focusables.get(i10);
            int top = view2.getTop();
            int bottom = view2.getBottom();
            if (i7 < bottom && top < i8) {
                boolean z8 = i7 < top && bottom < i8;
                if (view == null) {
                    view = view2;
                    z7 = z8;
                } else {
                    boolean z9 = (z6 && top < view.getTop()) || (!z6 && bottom > view.getBottom());
                    if (z7) {
                        if (z8) {
                            if (!z9) {
                            }
                            view = view2;
                        }
                    } else if (z8) {
                        view = view2;
                        z7 = true;
                    } else {
                        if (!z9) {
                        }
                        view = view2;
                    }
                }
            }
        }
        if (view == null) {
            view = this;
        }
        if (i7 < scrollY || i8 > i9) {
            m598f(z6 ? i7 - scrollY : i8 - i9);
            z5 = true;
        } else {
            z5 = false;
        }
        if (view != findFocus()) {
            view.requestFocus(i6);
        }
        return z5;
    }
}
