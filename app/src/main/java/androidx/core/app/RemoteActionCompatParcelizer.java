package androidx.core.app;

import android.app.PendingIntent;
import androidx.core.graphics.drawable.IconCompat;
import java.util.Objects;
import p143v0.AbstractC1451a;
import p143v0.InterfaceC1453c;

/* loaded from: classes.dex */
public class RemoteActionCompatParcelizer {
    public static RemoteActionCompat read(AbstractC1451a abstractC1451a) {
        RemoteActionCompat remoteActionCompat = new RemoteActionCompat();
        InterfaceC1453c interfaceC1453c = remoteActionCompat.f1293a;
        if (abstractC1451a.mo3541i(1)) {
            interfaceC1453c = abstractC1451a.m3547o();
        }
        remoteActionCompat.f1293a = (IconCompat) interfaceC1453c;
        CharSequence charSequence = remoteActionCompat.f1294b;
        if (abstractC1451a.mo3541i(2)) {
            charSequence = abstractC1451a.mo3540h();
        }
        remoteActionCompat.f1294b = charSequence;
        CharSequence charSequence2 = remoteActionCompat.f1295c;
        if (abstractC1451a.mo3541i(3)) {
            charSequence2 = abstractC1451a.mo3540h();
        }
        remoteActionCompat.f1295c = charSequence2;
        remoteActionCompat.f1296d = (PendingIntent) abstractC1451a.m3545m(remoteActionCompat.f1296d, 4);
        boolean z5 = remoteActionCompat.f1297e;
        if (abstractC1451a.mo3541i(5)) {
            z5 = abstractC1451a.mo3538f();
        }
        remoteActionCompat.f1297e = z5;
        boolean z6 = remoteActionCompat.f1298f;
        if (abstractC1451a.mo3541i(6)) {
            z6 = abstractC1451a.mo3538f();
        }
        remoteActionCompat.f1298f = z6;
        return remoteActionCompat;
    }

    public static void write(RemoteActionCompat remoteActionCompat, AbstractC1451a abstractC1451a) {
        Objects.requireNonNull(abstractC1451a);
        IconCompat iconCompat = remoteActionCompat.f1293a;
        abstractC1451a.mo3548p(1);
        abstractC1451a.m3557y(iconCompat);
        CharSequence charSequence = remoteActionCompat.f1294b;
        abstractC1451a.mo3548p(2);
        abstractC1451a.mo3551s(charSequence);
        CharSequence charSequence2 = remoteActionCompat.f1295c;
        abstractC1451a.mo3548p(3);
        abstractC1451a.mo3551s(charSequence2);
        abstractC1451a.m3555w(remoteActionCompat.f1296d, 4);
        boolean z5 = remoteActionCompat.f1297e;
        abstractC1451a.mo3548p(5);
        abstractC1451a.mo3549q(z5);
        boolean z6 = remoteActionCompat.f1298f;
        abstractC1451a.mo3548p(6);
        abstractC1451a.mo3549q(z6);
    }
}
