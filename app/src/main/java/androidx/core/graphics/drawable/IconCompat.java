package androidx.core.graphics.drawable;

import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.os.Parcelable;
import androidx.versionedparcelable.CustomVersionedParcelable;

/* loaded from: classes.dex */
public class IconCompat extends CustomVersionedParcelable {

    /* renamed from: k */
    public static final PorterDuff.Mode f1304k = PorterDuff.Mode.SRC_IN;

    /* renamed from: b */
    public Object f1306b;

    /* renamed from: j */
    public String f1314j;

    /* renamed from: a */
    public int f1305a = -1;

    /* renamed from: c */
    public byte[] f1307c = null;

    /* renamed from: d */
    public Parcelable f1308d = null;

    /* renamed from: e */
    public int f1309e = 0;

    /* renamed from: f */
    public int f1310f = 0;

    /* renamed from: g */
    public ColorStateList f1311g = null;

    /* renamed from: h */
    public PorterDuff.Mode f1312h = f1304k;

    /* renamed from: i */
    public String f1313i = null;

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Removed duplicated region for block: B:35:0x00f6  */
    /* JADX WARN: Removed duplicated region for block: B:38:0x0106  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final java.lang.String toString() {
        /*
            Method dump skipped, instructions count: 314
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.core.graphics.drawable.IconCompat.toString():java.lang.String");
    }
}
