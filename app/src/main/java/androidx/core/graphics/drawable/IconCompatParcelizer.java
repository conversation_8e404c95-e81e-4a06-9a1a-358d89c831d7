package androidx.core.graphics.drawable;

import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.os.Parcelable;
import java.nio.charset.Charset;
import java.util.Objects;
import p143v0.AbstractC1451a;

/* loaded from: classes.dex */
public class IconCompatParcelizer {
    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    public static IconCompat read(AbstractC1451a abstractC1451a) {
        Parcelable parcelable;
        IconCompat iconCompat = new IconCompat();
        iconCompat.f1305a = abstractC1451a.m3543k(iconCompat.f1305a, 1);
        byte[] bArr = iconCompat.f1307c;
        if (abstractC1451a.mo3541i(2)) {
            bArr = abstractC1451a.mo3539g();
        }
        iconCompat.f1307c = bArr;
        iconCompat.f1308d = abstractC1451a.m3545m(iconCompat.f1308d, 3);
        iconCompat.f1309e = abstractC1451a.m3543k(iconCompat.f1309e, 4);
        iconCompat.f1310f = abstractC1451a.m3543k(iconCompat.f1310f, 5);
        iconCompat.f1311g = (ColorStateList) abstractC1451a.m3545m(iconCompat.f1311g, 6);
        String str = iconCompat.f1313i;
        if (abstractC1451a.mo3541i(7)) {
            str = abstractC1451a.mo3546n();
        }
        iconCompat.f1313i = str;
        String str2 = iconCompat.f1314j;
        if (abstractC1451a.mo3541i(8)) {
            str2 = abstractC1451a.mo3546n();
        }
        iconCompat.f1314j = str2;
        iconCompat.f1312h = PorterDuff.Mode.valueOf(iconCompat.f1313i);
        switch (iconCompat.f1305a) {
            case -1:
                parcelable = iconCompat.f1308d;
                if (parcelable == null) {
                    throw new IllegalArgumentException("Invalid icon");
                }
                iconCompat.f1306b = parcelable;
                return iconCompat;
            case 0:
            default:
                return iconCompat;
            case 1:
            case 5:
                parcelable = iconCompat.f1308d;
                if (parcelable == null) {
                    byte[] bArr2 = iconCompat.f1307c;
                    iconCompat.f1306b = bArr2;
                    iconCompat.f1305a = 3;
                    iconCompat.f1309e = 0;
                    iconCompat.f1310f = bArr2.length;
                    return iconCompat;
                }
                iconCompat.f1306b = parcelable;
                return iconCompat;
            case 2:
            case 4:
            case 6:
                String str3 = new String(iconCompat.f1307c, Charset.forName("UTF-16"));
                iconCompat.f1306b = str3;
                if (iconCompat.f1305a == 2 && iconCompat.f1314j == null) {
                    iconCompat.f1314j = str3.split(":", -1)[0];
                }
                return iconCompat;
            case 3:
                iconCompat.f1306b = iconCompat.f1307c;
                return iconCompat;
        }
    }

    public static void write(IconCompat iconCompat, AbstractC1451a abstractC1451a) {
        Objects.requireNonNull(abstractC1451a);
        iconCompat.f1313i = iconCompat.f1312h.name();
        switch (iconCompat.f1305a) {
            case -1:
            case 1:
            case 5:
                iconCompat.f1308d = (Parcelable) iconCompat.f1306b;
                break;
            case 2:
                iconCompat.f1307c = ((String) iconCompat.f1306b).getBytes(Charset.forName("UTF-16"));
                break;
            case 3:
                iconCompat.f1307c = (byte[]) iconCompat.f1306b;
                break;
            case 4:
            case 6:
                iconCompat.f1307c = iconCompat.f1306b.toString().getBytes(Charset.forName("UTF-16"));
                break;
        }
        int i6 = iconCompat.f1305a;
        if (-1 != i6) {
            abstractC1451a.m3553u(i6, 1);
        }
        byte[] bArr = iconCompat.f1307c;
        if (bArr != null) {
            abstractC1451a.mo3548p(2);
            abstractC1451a.mo3550r(bArr);
        }
        Parcelable parcelable = iconCompat.f1308d;
        if (parcelable != null) {
            abstractC1451a.m3555w(parcelable, 3);
        }
        int i7 = iconCompat.f1309e;
        if (i7 != 0) {
            abstractC1451a.m3553u(i7, 4);
        }
        int i8 = iconCompat.f1310f;
        if (i8 != 0) {
            abstractC1451a.m3553u(i8, 5);
        }
        ColorStateList colorStateList = iconCompat.f1311g;
        if (colorStateList != null) {
            abstractC1451a.m3555w(colorStateList, 6);
        }
        String str = iconCompat.f1313i;
        if (str != null) {
            abstractC1451a.mo3548p(7);
            abstractC1451a.mo3556x(str);
        }
        String str2 = iconCompat.f1314j;
        if (str2 != null) {
            abstractC1451a.mo3548p(8);
            abstractC1451a.mo3556x(str2);
        }
    }
}
