package androidx.core.content;

import android.annotation.SuppressLint;
import android.content.ContentProvider;
import android.content.ContentValues;
import android.content.Context;
import android.content.pm.ProviderInfo;
import android.content.res.XmlResourceParser;
import android.database.Cursor;
import android.database.MatrixCursor;
import android.net.Uri;
import android.os.Environment;
import android.os.ParcelFileDescriptor;
import android.text.TextUtils;
import android.webkit.MimeTypeMap;
import androidx.activity.result.C0052a;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import org.litepal.util.Const;
import org.xmlpull.v1.XmlPullParserException;
import p142v.C1450a;

/* loaded from: classes.dex */
public class FileProvider extends ContentProvider {

    /* renamed from: k */
    public static final String[] f1299k = {"_display_name", "_size"};

    /* renamed from: l */
    public static final File f1300l = new File("/");

    /* renamed from: m */
    public static HashMap<String, InterfaceC0194a> f1301m = new HashMap<>();

    /* renamed from: j */
    public InterfaceC0194a f1302j;

    /* renamed from: androidx.core.content.FileProvider$a */
    public interface InterfaceC0194a {
        /* renamed from: a */
        File mo587a(Uri uri);
    }

    /* renamed from: androidx.core.content.FileProvider$b */
    public static class C0195b implements InterfaceC0194a {

        /* renamed from: a */
        public final HashMap<String, File> f1303a = new HashMap<>();

        @Override // androidx.core.content.FileProvider.InterfaceC0194a
        /* renamed from: a */
        public final File mo587a(Uri uri) {
            String encodedPath = uri.getEncodedPath();
            int indexOf = encodedPath.indexOf(47, 1);
            String decode = Uri.decode(encodedPath.substring(1, indexOf));
            String decode2 = Uri.decode(encodedPath.substring(indexOf + 1));
            File file = this.f1303a.get(decode);
            if (file == null) {
                throw new IllegalArgumentException("Unable to find configured root for " + uri);
            }
            File file2 = new File(file, decode2);
            try {
                File canonicalFile = file2.getCanonicalFile();
                if (canonicalFile.getPath().startsWith(file.getPath())) {
                    return canonicalFile;
                }
                throw new SecurityException("Resolved path jumped beyond configured root");
            } catch (IOException unused) {
                throw new IllegalArgumentException("Failed to resolve canonical path for " + file2);
            }
        }
    }

    /* renamed from: a */
    public static InterfaceC0194a m586a(Context context, String str) {
        C0195b c0195b = new C0195b();
        ProviderInfo resolveContentProvider = context.getPackageManager().resolveContentProvider(str, 128);
        if (resolveContentProvider == null) {
            throw new IllegalArgumentException(C0052a.m103g("Couldn't find meta-data for provider with authority ", str));
        }
        XmlResourceParser loadXmlMetaData = resolveContentProvider.loadXmlMetaData(context.getPackageManager(), "android.support.FILE_PROVIDER_PATHS");
        if (loadXmlMetaData == null) {
            throw new IllegalArgumentException("Missing android.support.FILE_PROVIDER_PATHS meta-data");
        }
        while (true) {
            int next = loadXmlMetaData.next();
            if (next == 1) {
                return c0195b;
            }
            if (next == 2) {
                String name = loadXmlMetaData.getName();
                File file = null;
                String attributeValue = loadXmlMetaData.getAttributeValue(null, Const.TableSchema.COLUMN_NAME);
                String attributeValue2 = loadXmlMetaData.getAttributeValue(null, "path");
                if ("root-path".equals(name)) {
                    file = f1300l;
                } else if ("files-path".equals(name)) {
                    file = context.getFilesDir();
                } else if ("cache-path".equals(name)) {
                    file = context.getCacheDir();
                } else if ("external-path".equals(name)) {
                    file = Environment.getExternalStorageDirectory();
                } else if ("external-files-path".equals(name)) {
                    Object obj = C1450a.f6698a;
                    File[] m3524b = C1450a.b.m3524b(context, null);
                    if (m3524b.length > 0) {
                        file = m3524b[0];
                    }
                } else if ("external-cache-path".equals(name)) {
                    Object obj2 = C1450a.f6698a;
                    File[] m3523a = C1450a.b.m3523a(context);
                    if (m3523a.length > 0) {
                        file = m3523a[0];
                    }
                } else if ("external-media-path".equals(name)) {
                    File[] externalMediaDirs = context.getExternalMediaDirs();
                    if (externalMediaDirs.length > 0) {
                        file = externalMediaDirs[0];
                    }
                }
                if (file != null) {
                    String[] strArr = {attributeValue2};
                    for (int i6 = 0; i6 < 1; i6++) {
                        String str2 = strArr[i6];
                        if (str2 != null) {
                            file = new File(file, str2);
                        }
                    }
                    if (TextUtils.isEmpty(attributeValue)) {
                        throw new IllegalArgumentException("Name must not be empty");
                    }
                    try {
                        c0195b.f1303a.put(attributeValue, file.getCanonicalFile());
                    } catch (IOException e6) {
                        throw new IllegalArgumentException("Failed to resolve canonical path for " + file, e6);
                    }
                } else {
                    continue;
                }
            }
        }
    }

    @Override // android.content.ContentProvider
    public final void attachInfo(Context context, ProviderInfo providerInfo) {
        InterfaceC0194a interfaceC0194a;
        super.attachInfo(context, providerInfo);
        if (providerInfo.exported) {
            throw new SecurityException("Provider must not be exported");
        }
        if (!providerInfo.grantUriPermissions) {
            throw new SecurityException("Provider must grant uri permissions");
        }
        String str = providerInfo.authority.split(";")[0];
        synchronized (f1301m) {
            interfaceC0194a = f1301m.get(str);
            if (interfaceC0194a == null) {
                try {
                    try {
                        interfaceC0194a = m586a(context, str);
                        f1301m.put(str, interfaceC0194a);
                    } catch (XmlPullParserException e6) {
                        throw new IllegalArgumentException("Failed to parse android.support.FILE_PROVIDER_PATHS meta-data", e6);
                    }
                } catch (IOException e7) {
                    throw new IllegalArgumentException("Failed to parse android.support.FILE_PROVIDER_PATHS meta-data", e7);
                }
            }
        }
        this.f1302j = interfaceC0194a;
    }

    @Override // android.content.ContentProvider
    public final int delete(Uri uri, String str, String[] strArr) {
        return this.f1302j.mo587a(uri).delete() ? 1 : 0;
    }

    @Override // android.content.ContentProvider
    public final String getType(Uri uri) {
        File mo587a = this.f1302j.mo587a(uri);
        int lastIndexOf = mo587a.getName().lastIndexOf(46);
        if (lastIndexOf < 0) {
            return "application/octet-stream";
        }
        String mimeTypeFromExtension = MimeTypeMap.getSingleton().getMimeTypeFromExtension(mo587a.getName().substring(lastIndexOf + 1));
        return mimeTypeFromExtension != null ? mimeTypeFromExtension : "application/octet-stream";
    }

    @Override // android.content.ContentProvider
    public final Uri insert(Uri uri, ContentValues contentValues) {
        throw new UnsupportedOperationException("No external inserts");
    }

    @Override // android.content.ContentProvider
    public final boolean onCreate() {
        return true;
    }

    @Override // android.content.ContentProvider
    @SuppressLint({"UnknownNullness"})
    public final ParcelFileDescriptor openFile(Uri uri, String str) {
        int i6;
        File mo587a = this.f1302j.mo587a(uri);
        if ("r".equals(str)) {
            i6 = 268435456;
        } else if ("w".equals(str) || "wt".equals(str)) {
            i6 = 738197504;
        } else if ("wa".equals(str)) {
            i6 = 704643072;
        } else if ("rw".equals(str)) {
            i6 = 939524096;
        } else {
            if (!"rwt".equals(str)) {
                throw new IllegalArgumentException(C0052a.m103g("Invalid mode: ", str));
            }
            i6 = **********;
        }
        return ParcelFileDescriptor.open(mo587a, i6);
    }

    @Override // android.content.ContentProvider
    public final Cursor query(Uri uri, String[] strArr, String str, String[] strArr2, String str2) {
        int i6;
        File mo587a = this.f1302j.mo587a(uri);
        String queryParameter = uri.getQueryParameter("displayName");
        if (strArr == null) {
            strArr = f1299k;
        }
        String[] strArr3 = new String[strArr.length];
        Object[] objArr = new Object[strArr.length];
        int i7 = 0;
        for (String str3 : strArr) {
            if ("_display_name".equals(str3)) {
                strArr3[i7] = "_display_name";
                i6 = i7 + 1;
                objArr[i7] = queryParameter == null ? mo587a.getName() : queryParameter;
            } else if ("_size".equals(str3)) {
                strArr3[i7] = "_size";
                i6 = i7 + 1;
                objArr[i7] = Long.valueOf(mo587a.length());
            }
            i7 = i6;
        }
        String[] strArr4 = new String[i7];
        System.arraycopy(strArr3, 0, strArr4, 0, i7);
        Object[] objArr2 = new Object[i7];
        System.arraycopy(objArr, 0, objArr2, 0, i7);
        MatrixCursor matrixCursor = new MatrixCursor(strArr4, 1);
        matrixCursor.addRow(objArr2);
        return matrixCursor;
    }

    @Override // android.content.ContentProvider
    public final int update(Uri uri, ContentValues contentValues, String str, String[] strArr) {
        throw new UnsupportedOperationException("No external updates");
    }
}
