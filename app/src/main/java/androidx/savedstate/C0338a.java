package androidx.savedstate;

import android.annotation.SuppressLint;
import android.os.Bundle;
import androidx.activity.result.C0052a;
import androidx.lifecycle.SavedStateHandleController;
import androidx.savedstate.Recreator;
import p063j.C0955b;

@SuppressLint({"RestrictedApi"})
/* renamed from: androidx.savedstate.a */
/* loaded from: classes.dex */
public final class C0338a {

    /* renamed from: b */
    public Bundle f2176b;

    /* renamed from: c */
    public boolean f2177c;

    /* renamed from: d */
    public Recreator.C0337a f2178d;

    /* renamed from: a */
    public C0955b<String, b> f2175a = new C0955b<>();

    /* renamed from: e */
    public boolean f2179e = true;

    /* renamed from: androidx.savedstate.a$a */
    public interface a {
        /* renamed from: a */
        void mo860a(InterfaceC0340c interfaceC0340c);
    }

    /* renamed from: androidx.savedstate.a$b */
    public interface b {
        /* renamed from: a */
        Bundle mo749a();
    }

    /* renamed from: a */
    public final Bundle m1361a(String str) {
        if (!this.f2177c) {
            throw new IllegalStateException("You can consumeRestoredStateForKey only after super.onCreate of corresponding component");
        }
        Bundle bundle = this.f2176b;
        if (bundle == null) {
            return null;
        }
        Bundle bundle2 = bundle.getBundle(str);
        this.f2176b.remove(str);
        if (this.f2176b.isEmpty()) {
            this.f2176b = null;
        }
        return bundle2;
    }

    /* renamed from: b */
    public final void m1362b(String str, b bVar) {
        if (this.f2175a.mo2494i(str, bVar) != null) {
            throw new IllegalArgumentException("SavedStateProvider with the given key is already registered");
        }
    }

    /* JADX WARN: Type inference failed for: r1v7, types: [java.util.HashSet, java.util.Set<java.lang.String>] */
    /* renamed from: c */
    public final void m1363c() {
        if (!this.f2179e) {
            throw new IllegalStateException("Can not perform this action after onSaveInstanceState");
        }
        if (this.f2178d == null) {
            this.f2178d = new Recreator.C0337a(this);
        }
        try {
            SavedStateHandleController.C0247a.class.getDeclaredConstructor(new Class[0]);
            Recreator.C0337a c0337a = this.f2178d;
            c0337a.f2173a.add(SavedStateHandleController.C0247a.class.getName());
        } catch (NoSuchMethodException e6) {
            StringBuilder m104h = C0052a.m104h("Class");
            m104h.append(SavedStateHandleController.C0247a.class.getSimpleName());
            m104h.append(" must have default constructor in order to be automatically recreated");
            throw new IllegalArgumentException(m104h.toString(), e6);
        }
    }
}
