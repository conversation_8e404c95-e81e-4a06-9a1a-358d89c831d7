package androidx.savedstate;

import android.annotation.SuppressLint;
import android.os.Bundle;
import androidx.activity.result.C0052a;
import androidx.appcompat.widget.C0174y;
import androidx.lifecycle.AbstractC0251d;
import androidx.lifecycle.InterfaceC0252e;
import androidx.lifecycle.InterfaceC0254g;
import androidx.savedstate.C0338a;
import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;

@SuppressLint({"RestrictedApi"})
/* loaded from: classes.dex */
final class Recreator implements InterfaceC0252e {

    /* renamed from: a */
    public final InterfaceC0340c f2172a;

    /* renamed from: androidx.savedstate.Recreator$a */
    public static final class C0337a implements C0338a.b {

        /* renamed from: a */
        public final Set<String> f2173a = new HashSet();

        public C0337a(C0338a c0338a) {
            c0338a.m1362b("androidx.savedstate.Restarter", this);
        }

        @Override // androidx.savedstate.C0338a.b
        /* renamed from: a */
        public final Bundle mo749a() {
            Bundle bundle = new Bundle();
            bundle.putStringArrayList("classes_to_restore", new ArrayList<>(this.f2173a));
            return bundle;
        }
    }

    public Recreator(InterfaceC0340c interfaceC0340c) {
        this.f2172a = interfaceC0340c;
    }

    @Override // androidx.lifecycle.InterfaceC0252e
    /* renamed from: e */
    public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
        if (bVar != AbstractC0251d.b.ON_CREATE) {
            throw new AssertionError("Next event must be ON_CREATE");
        }
        interfaceC0254g.mo86a().mo872b(this);
        Bundle m1361a = this.f2172a.mo88c().m1361a("androidx.savedstate.Restarter");
        if (m1361a == null) {
            return;
        }
        ArrayList<String> stringArrayList = m1361a.getStringArrayList("classes_to_restore");
        if (stringArrayList == null) {
            throw new IllegalStateException("Bundle with restored state for the component \"androidx.savedstate.Restarter\" must contain list of strings by the key \"classes_to_restore\"");
        }
        Iterator<String> it = stringArrayList.iterator();
        while (it.hasNext()) {
            String next = it.next();
            try {
                Class<? extends U> asSubclass = Class.forName(next, false, Recreator.class.getClassLoader()).asSubclass(C0338a.a.class);
                try {
                    Constructor declaredConstructor = asSubclass.getDeclaredConstructor(new Class[0]);
                    declaredConstructor.setAccessible(true);
                    try {
                        ((C0338a.a) declaredConstructor.newInstance(new Object[0])).mo860a(this.f2172a);
                    } catch (Exception e6) {
                        throw new RuntimeException(C0052a.m103g("Failed to instantiate ", next), e6);
                    }
                } catch (NoSuchMethodException e7) {
                    StringBuilder m104h = C0052a.m104h("Class");
                    m104h.append(asSubclass.getSimpleName());
                    m104h.append(" must have default constructor in order to be automatically recreated");
                    throw new IllegalStateException(m104h.toString(), e7);
                }
            } catch (ClassNotFoundException e8) {
                throw new RuntimeException(C0174y.m491i("Class ", next, " wasn't found"), e8);
            }
        }
    }
}
