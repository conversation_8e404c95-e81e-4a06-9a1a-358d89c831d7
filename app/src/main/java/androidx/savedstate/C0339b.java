package androidx.savedstate;

import android.os.Bundle;
import androidx.lifecycle.AbstractC0251d;
import androidx.lifecycle.C0255h;
import androidx.lifecycle.InterfaceC0252e;
import androidx.lifecycle.InterfaceC0254g;
import androidx.savedstate.C0338a;
import java.util.Map;
import java.util.Objects;
import p063j.C0955b;

/* renamed from: androidx.savedstate.b */
/* loaded from: classes.dex */
public final class C0339b {

    /* renamed from: a */
    public final InterfaceC0340c f2180a;

    /* renamed from: b */
    public final C0338a f2181b = new C0338a();

    public C0339b(InterfaceC0340c interfaceC0340c) {
        this.f2180a = interfaceC0340c;
    }

    /* renamed from: a */
    public final void m1364a(Bundle bundle) {
        AbstractC0251d mo86a = this.f2180a.mo86a();
        if (((C0255h) mo86a).f1700b != AbstractC0251d.c.INITIALIZED) {
            throw new IllegalStateException("Restarter must be created only during owner's initialization stage");
        }
        mo86a.mo871a(new Recreator(this.f2180a));
        final C0338a c0338a = this.f2181b;
        if (c0338a.f2177c) {
            throw new IllegalStateException("SavedStateRegistry was already restored.");
        }
        if (bundle != null) {
            c0338a.f2176b = bundle.getBundle("androidx.lifecycle.BundlableSavedStateRegistry.key");
        }
        mo86a.mo871a(new InterfaceC0252e() { // from class: androidx.savedstate.SavedStateRegistry$1
            @Override // androidx.lifecycle.InterfaceC0252e
            /* renamed from: e */
            public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
                C0338a c0338a2;
                boolean z5;
                if (bVar == AbstractC0251d.b.ON_START) {
                    c0338a2 = C0338a.this;
                    z5 = true;
                } else {
                    if (bVar != AbstractC0251d.b.ON_STOP) {
                        return;
                    }
                    c0338a2 = C0338a.this;
                    z5 = false;
                }
                c0338a2.f2179e = z5;
            }
        });
        c0338a.f2177c = true;
    }

    /* renamed from: b */
    public final void m1365b(Bundle bundle) {
        C0338a c0338a = this.f2181b;
        Objects.requireNonNull(c0338a);
        Bundle bundle2 = new Bundle();
        Bundle bundle3 = c0338a.f2176b;
        if (bundle3 != null) {
            bundle2.putAll(bundle3);
        }
        C0955b<String, C0338a.b>.d m2496d = c0338a.f2175a.m2496d();
        while (m2496d.hasNext()) {
            Map.Entry entry = (Map.Entry) m2496d.next();
            bundle2.putBundle((String) entry.getKey(), ((C0338a.b) entry.getValue()).mo749a());
        }
        bundle.putBundle("androidx.lifecycle.BundlableSavedStateRegistry.key", bundle2);
    }
}
