package androidx.versionedparcelable;

import android.annotation.SuppressLint;
import android.os.Parcel;
import android.os.Parcelable;
import p143v0.C1452b;
import p143v0.InterfaceC1453c;

@SuppressLint({"BanParcelableUsage"})
/* loaded from: classes.dex */
public class ParcelImpl implements Parcelable {
    public static final Parcelable.Creator<ParcelImpl> CREATOR = new C0341a();

    /* renamed from: j */
    public final InterfaceC1453c f2182j;

    /* renamed from: androidx.versionedparcelable.ParcelImpl$a */
    public static class C0341a implements Parcelable.Creator<ParcelImpl> {
        @Override // android.os.Parcelable.Creator
        public final ParcelImpl createFromParcel(Parcel parcel) {
            return new ParcelImpl(parcel);
        }

        @Override // android.os.Parcelable.Creator
        public final ParcelImpl[] newArray(int i6) {
            return new ParcelImpl[i6];
        }
    }

    public ParcelImpl(Parcel parcel) {
        this.f2182j = new C1452b(parcel).m3547o();
    }

    @Override // android.os.Parcelable
    public final int describeContents() {
        return 0;
    }

    @Override // android.os.Parcelable
    public final void writeToParcel(Parcel parcel, int i6) {
        new C1452b(parcel).m3557y(this.f2182j);
    }
}
