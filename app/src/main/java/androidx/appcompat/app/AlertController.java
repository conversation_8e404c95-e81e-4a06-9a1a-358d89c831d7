package androidx.appcompat.app;

import android.R;
import android.content.Context;
import android.content.DialogInterface;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Message;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.ViewStub;
import android.view.Window;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import androidx.core.widget.NestedScrollView;
import java.lang.ref.WeakReference;
import p008b0.C0385m;
import p021d.DialogC0688m;

/* loaded from: classes.dex */
public final class AlertController {

    /* renamed from: A */
    public View f223A;

    /* renamed from: B */
    public ListAdapter f224B;

    /* renamed from: D */
    public int f226D;

    /* renamed from: E */
    public int f227E;

    /* renamed from: F */
    public int f228F;

    /* renamed from: G */
    public int f229G;

    /* renamed from: H */
    public int f230H;

    /* renamed from: I */
    public int f231I;

    /* renamed from: J */
    public boolean f232J;

    /* renamed from: K */
    public HandlerC0060c f233K;

    /* renamed from: a */
    public final Context f235a;

    /* renamed from: b */
    public final DialogC0688m f236b;

    /* renamed from: c */
    public final Window f237c;

    /* renamed from: d */
    public final int f238d;

    /* renamed from: e */
    public CharSequence f239e;

    /* renamed from: f */
    public CharSequence f240f;

    /* renamed from: g */
    public RecycleListView f241g;

    /* renamed from: i */
    public Button f243i;

    /* renamed from: j */
    public CharSequence f244j;

    /* renamed from: k */
    public Message f245k;

    /* renamed from: l */
    public Drawable f246l;

    /* renamed from: m */
    public Button f247m;

    /* renamed from: n */
    public CharSequence f248n;

    /* renamed from: o */
    public Message f249o;

    /* renamed from: p */
    public Drawable f250p;

    /* renamed from: q */
    public Button f251q;

    /* renamed from: r */
    public CharSequence f252r;

    /* renamed from: s */
    public Message f253s;

    /* renamed from: t */
    public Drawable f254t;

    /* renamed from: u */
    public NestedScrollView f255u;

    /* renamed from: w */
    public Drawable f257w;

    /* renamed from: x */
    public ImageView f258x;

    /* renamed from: y */
    public TextView f259y;

    /* renamed from: z */
    public TextView f260z;

    /* renamed from: h */
    public boolean f242h = false;

    /* renamed from: v */
    public int f256v = 0;

    /* renamed from: C */
    public int f225C = -1;

    /* renamed from: L */
    public final ViewOnClickListenerC0058a f234L = new ViewOnClickListenerC0058a();

    public static class RecycleListView extends ListView {

        /* renamed from: j */
        public final int f261j;

        /* renamed from: k */
        public final int f262k;

        public RecycleListView(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C0385m.f2308C);
            this.f262k = obtainStyledAttributes.getDimensionPixelOffset(0, -1);
            this.f261j = obtainStyledAttributes.getDimensionPixelOffset(1, -1);
        }
    }

    /* renamed from: androidx.appcompat.app.AlertController$a */
    public class ViewOnClickListenerC0058a implements View.OnClickListener {
        public ViewOnClickListenerC0058a() {
        }

        @Override // android.view.View.OnClickListener
        public final void onClick(View view) {
            Message message;
            Message message2;
            AlertController alertController = AlertController.this;
            Message obtain = ((view != alertController.f243i || (message2 = alertController.f245k) == null) && (view != alertController.f247m || (message2 = alertController.f249o) == null)) ? (view != alertController.f251q || (message = alertController.f253s) == null) ? null : Message.obtain(message) : Message.obtain(message2);
            if (obtain != null) {
                obtain.sendToTarget();
            }
            AlertController alertController2 = AlertController.this;
            alertController2.f233K.obtainMessage(1, alertController2.f236b).sendToTarget();
        }
    }

    /* renamed from: androidx.appcompat.app.AlertController$b */
    public static class C0059b {

        /* renamed from: a */
        public final Context f264a;

        /* renamed from: b */
        public final LayoutInflater f265b;

        /* renamed from: c */
        public Drawable f266c;

        /* renamed from: d */
        public CharSequence f267d;

        /* renamed from: e */
        public View f268e;

        /* renamed from: f */
        public CharSequence f269f;

        /* renamed from: g */
        public CharSequence f270g;

        /* renamed from: h */
        public DialogInterface.OnClickListener f271h;

        /* renamed from: i */
        public CharSequence f272i;

        /* renamed from: j */
        public DialogInterface.OnClickListener f273j;

        /* renamed from: k */
        public DialogInterface.OnKeyListener f274k;

        /* renamed from: l */
        public CharSequence[] f275l;

        /* renamed from: m */
        public ListAdapter f276m;

        /* renamed from: n */
        public DialogInterface.OnClickListener f277n;

        /* renamed from: o */
        public boolean f278o;

        /* renamed from: p */
        public int f279p = -1;

        public C0059b(Context context) {
            this.f264a = context;
            this.f265b = (LayoutInflater) context.getSystemService("layout_inflater");
        }
    }

    /* renamed from: androidx.appcompat.app.AlertController$c */
    public static final class HandlerC0060c extends Handler {

        /* renamed from: a */
        public WeakReference<DialogInterface> f280a;

        public HandlerC0060c(DialogInterface dialogInterface) {
            this.f280a = new WeakReference<>(dialogInterface);
        }

        @Override // android.os.Handler
        public final void handleMessage(Message message) {
            int i6 = message.what;
            if (i6 == -3 || i6 == -2 || i6 == -1) {
                ((DialogInterface.OnClickListener) message.obj).onClick(this.f280a.get(), message.what);
            } else {
                if (i6 != 1) {
                    return;
                }
                ((DialogInterface) message.obj).dismiss();
            }
        }
    }

    /* renamed from: androidx.appcompat.app.AlertController$d */
    public static class C0061d extends ArrayAdapter<CharSequence> {
        public C0061d(Context context, int i6, CharSequence[] charSequenceArr) {
            super(context, i6, R.id.text1, charSequenceArr);
        }

        @Override // android.widget.ArrayAdapter, android.widget.Adapter
        public final long getItemId(int i6) {
            return i6;
        }

        @Override // android.widget.BaseAdapter, android.widget.Adapter
        public final boolean hasStableIds() {
            return true;
        }
    }

    public AlertController(Context context, DialogC0688m dialogC0688m, Window window) {
        this.f235a = context;
        this.f236b = dialogC0688m;
        this.f237c = window;
        this.f233K = new HandlerC0060c(dialogC0688m);
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(null, C0385m.f2355o, com.liaoyuan.aicast.R.attr.alertDialogStyle, 0);
        this.f226D = obtainStyledAttributes.getResourceId(0, 0);
        this.f227E = obtainStyledAttributes.getResourceId(2, 0);
        this.f228F = obtainStyledAttributes.getResourceId(4, 0);
        this.f229G = obtainStyledAttributes.getResourceId(5, 0);
        this.f230H = obtainStyledAttributes.getResourceId(7, 0);
        this.f231I = obtainStyledAttributes.getResourceId(3, 0);
        this.f232J = obtainStyledAttributes.getBoolean(6, true);
        this.f238d = obtainStyledAttributes.getDimensionPixelSize(1, 0);
        obtainStyledAttributes.recycle();
        dialogC0688m.m2024a().mo1980r(1);
    }

    /* renamed from: b */
    public static void m130b(View view, View view2, View view3) {
        if (view2 != null) {
            view2.setVisibility(view.canScrollVertically(-1) ? 0 : 4);
        }
        if (view3 != null) {
            view3.setVisibility(view.canScrollVertically(1) ? 0 : 4);
        }
    }

    /* renamed from: a */
    public final void m131a(Button button) {
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) button.getLayoutParams();
        layoutParams.gravity = 1;
        layoutParams.weight = 0.5f;
        button.setLayoutParams(layoutParams);
    }

    /* renamed from: c */
    public final ViewGroup m132c(View view, View view2) {
        if (view == null) {
            if (view2 instanceof ViewStub) {
                view2 = ((ViewStub) view2).inflate();
            }
            return (ViewGroup) view2;
        }
        if (view2 != null) {
            ViewParent parent = view2.getParent();
            if (parent instanceof ViewGroup) {
                ((ViewGroup) parent).removeView(view2);
            }
        }
        if (view instanceof ViewStub) {
            view = ((ViewStub) view).inflate();
        }
        return (ViewGroup) view;
    }

    /* renamed from: d */
    public final void m133d(int i6, CharSequence charSequence, DialogInterface.OnClickListener onClickListener) {
        Message obtainMessage = onClickListener != null ? this.f233K.obtainMessage(i6, onClickListener) : null;
        if (i6 == -3) {
            this.f252r = charSequence;
            this.f253s = obtainMessage;
            this.f254t = null;
        } else if (i6 == -2) {
            this.f248n = charSequence;
            this.f249o = obtainMessage;
            this.f250p = null;
        } else {
            if (i6 != -1) {
                throw new IllegalArgumentException("Button does not exist");
            }
            this.f244j = charSequence;
            this.f245k = obtainMessage;
            this.f246l = null;
        }
    }
}
