package androidx.appcompat.app;

import android.content.Context;
import android.content.DialogInterface;
import android.graphics.drawable.Drawable;
import android.util.TypedValue;
import android.view.ContextThemeWrapper;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ListAdapter;
import android.widget.TextView;
import androidx.appcompat.app.AlertController;
import androidx.core.widget.NestedScrollView;
import com.liaoyuan.aicast.R;
import java.util.Objects;
import p021d.DialogC0688m;

/* renamed from: androidx.appcompat.app.b */
/* loaded from: classes.dex */
public final class DialogC0063b extends DialogC0688m {

    /* renamed from: l */
    public final AlertController f283l;

    /* renamed from: androidx.appcompat.app.b$a */
    public static class a {

        /* renamed from: a */
        public final AlertController.C0059b f284a;

        /* renamed from: b */
        public final int f285b;

        public a(Context context, int i6) {
            this.f284a = new AlertController.C0059b(new ContextThemeWrapper(context, DialogC0063b.m134e(context, i6)));
            this.f285b = i6;
        }

        /* renamed from: a */
        public DialogC0063b mo135a() {
            DialogC0063b dialogC0063b = new DialogC0063b(this.f284a.f264a, this.f285b);
            AlertController.C0059b c0059b = this.f284a;
            AlertController alertController = dialogC0063b.f283l;
            View view = c0059b.f268e;
            if (view != null) {
                alertController.f223A = view;
            } else {
                CharSequence charSequence = c0059b.f267d;
                if (charSequence != null) {
                    alertController.f239e = charSequence;
                    TextView textView = alertController.f259y;
                    if (textView != null) {
                        textView.setText(charSequence);
                    }
                }
                Drawable drawable = c0059b.f266c;
                if (drawable != null) {
                    alertController.f257w = drawable;
                    alertController.f256v = 0;
                    ImageView imageView = alertController.f258x;
                    if (imageView != null) {
                        imageView.setVisibility(0);
                        alertController.f258x.setImageDrawable(drawable);
                    }
                }
            }
            CharSequence charSequence2 = c0059b.f269f;
            if (charSequence2 != null) {
                alertController.f240f = charSequence2;
                TextView textView2 = alertController.f260z;
                if (textView2 != null) {
                    textView2.setText(charSequence2);
                }
            }
            CharSequence charSequence3 = c0059b.f270g;
            if (charSequence3 != null) {
                alertController.m133d(-1, charSequence3, c0059b.f271h);
            }
            CharSequence charSequence4 = c0059b.f272i;
            if (charSequence4 != null) {
                alertController.m133d(-2, charSequence4, c0059b.f273j);
            }
            if (c0059b.f275l != null || c0059b.f276m != null) {
                AlertController.RecycleListView recycleListView = (AlertController.RecycleListView) c0059b.f265b.inflate(alertController.f228F, (ViewGroup) null);
                int i6 = c0059b.f278o ? alertController.f230H : alertController.f231I;
                ListAdapter listAdapter = c0059b.f276m;
                if (listAdapter == null) {
                    listAdapter = new AlertController.C0061d(c0059b.f264a, i6, c0059b.f275l);
                }
                alertController.f224B = listAdapter;
                alertController.f225C = c0059b.f279p;
                if (c0059b.f277n != null) {
                    recycleListView.setOnItemClickListener(new C0062a(c0059b, alertController));
                }
                if (c0059b.f278o) {
                    recycleListView.setChoiceMode(1);
                }
                alertController.f241g = recycleListView;
            }
            Objects.requireNonNull(this.f284a);
            dialogC0063b.setCancelable(true);
            Objects.requireNonNull(this.f284a);
            dialogC0063b.setCanceledOnTouchOutside(true);
            Objects.requireNonNull(this.f284a);
            dialogC0063b.setOnCancelListener(null);
            Objects.requireNonNull(this.f284a);
            dialogC0063b.setOnDismissListener(null);
            DialogInterface.OnKeyListener onKeyListener = this.f284a.f274k;
            if (onKeyListener != null) {
                dialogC0063b.setOnKeyListener(onKeyListener);
            }
            return dialogC0063b;
        }

        /* renamed from: b */
        public final DialogC0063b m136b() {
            DialogC0063b mo135a = mo135a();
            mo135a.show();
            return mo135a;
        }
    }

    public DialogC0063b(Context context, int i6) {
        super(context, m134e(context, i6));
        this.f283l = new AlertController(getContext(), this, getWindow());
    }

    /* renamed from: e */
    public static int m134e(Context context, int i6) {
        if (((i6 >>> 24) & 255) >= 1) {
            return i6;
        }
        TypedValue typedValue = new TypedValue();
        context.getTheme().resolveAttribute(R.attr.alertDialogTheme, typedValue, true);
        return typedValue.resourceId;
    }

    /* JADX WARN: Removed duplicated region for block: B:110:0x0289  */
    /* JADX WARN: Removed duplicated region for block: B:113:0x0259  */
    /* JADX WARN: Removed duplicated region for block: B:115:0x0245  */
    /* JADX WARN: Removed duplicated region for block: B:48:0x0243  */
    /* JADX WARN: Removed duplicated region for block: B:55:0x0257  */
    /* JADX WARN: Removed duplicated region for block: B:61:0x026a  */
    /* JADX WARN: Removed duplicated region for block: B:73:0x029b  */
    /* JADX WARN: Removed duplicated region for block: B:86:0x02c1  */
    @Override // p021d.DialogC0688m, android.app.Dialog
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void onCreate(android.os.Bundle r17) {
        /*
            Method dump skipped, instructions count: 775
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.app.DialogC0063b.onCreate(android.os.Bundle):void");
    }

    @Override // android.app.Dialog, android.view.KeyEvent.Callback
    public final boolean onKeyDown(int i6, KeyEvent keyEvent) {
        NestedScrollView nestedScrollView = this.f283l.f255u;
        if (nestedScrollView != null && nestedScrollView.m601l(keyEvent)) {
            return true;
        }
        return super.onKeyDown(i6, keyEvent);
    }

    @Override // android.app.Dialog, android.view.KeyEvent.Callback
    public final boolean onKeyUp(int i6, KeyEvent keyEvent) {
        NestedScrollView nestedScrollView = this.f283l.f255u;
        if (nestedScrollView != null && nestedScrollView.m601l(keyEvent)) {
            return true;
        }
        return super.onKeyUp(i6, keyEvent);
    }

    @Override // p021d.DialogC0688m, android.app.Dialog
    public final void setTitle(CharSequence charSequence) {
        super.setTitle(charSequence);
        AlertController alertController = this.f283l;
        alertController.f239e = charSequence;
        TextView textView = alertController.f259y;
        if (textView != null) {
            textView.setText(charSequence);
        }
    }
}
