package androidx.appcompat.app;

import android.view.View;
import android.widget.AdapterView;
import androidx.appcompat.app.AlertController;

/* renamed from: androidx.appcompat.app.a */
/* loaded from: classes.dex */
public final class C0062a implements AdapterView.OnItemClickListener {

    /* renamed from: j */
    public final /* synthetic */ AlertController f281j;

    /* renamed from: k */
    public final /* synthetic */ AlertController.C0059b f282k;

    public C0062a(AlertController.C0059b c0059b, AlertController alertController) {
        this.f282k = c0059b;
        this.f281j = alertController;
    }

    @Override // android.widget.AdapterView.OnItemClickListener
    public final void onItemClick(AdapterView<?> adapterView, View view, int i6, long j6) {
        this.f282k.f277n.onClick(this.f281j.f236b, i6);
        if (this.f282k.f278o) {
            return;
        }
        this.f281j.f236b.dismiss();
    }
}
