package androidx.appcompat.view.menu;

import android.content.Context;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.view.ActionProvider;
import android.view.ContextMenu;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;
import android.view.ViewDebug;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import java.util.Objects;
import p028e.C0750a;
import p029e0.AbstractC0752b;
import p168z.InterfaceMenuItemC2096b;

/* renamed from: androidx.appcompat.view.menu.g */
/* loaded from: classes.dex */
public final class C0072g implements InterfaceMenuItemC2096b {

    /* renamed from: A */
    public AbstractC0752b f403A;

    /* renamed from: B */
    public MenuItem.OnActionExpandListener f404B;

    /* renamed from: a */
    public final int f406a;

    /* renamed from: b */
    public final int f407b;

    /* renamed from: c */
    public final int f408c;

    /* renamed from: d */
    public final int f409d;

    /* renamed from: e */
    public CharSequence f410e;

    /* renamed from: f */
    public CharSequence f411f;

    /* renamed from: g */
    public Intent f412g;

    /* renamed from: h */
    public char f413h;

    /* renamed from: j */
    public char f415j;

    /* renamed from: l */
    public Drawable f417l;

    /* renamed from: n */
    public C0070e f419n;

    /* renamed from: o */
    public SubMenuC0077l f420o;

    /* renamed from: p */
    public MenuItem.OnMenuItemClickListener f421p;

    /* renamed from: q */
    public CharSequence f422q;

    /* renamed from: r */
    public CharSequence f423r;

    /* renamed from: y */
    public int f430y;

    /* renamed from: z */
    public View f431z;

    /* renamed from: i */
    public int f414i = 4096;

    /* renamed from: k */
    public int f416k = 4096;

    /* renamed from: m */
    public int f418m = 0;

    /* renamed from: s */
    public ColorStateList f424s = null;

    /* renamed from: t */
    public PorterDuff.Mode f425t = null;

    /* renamed from: u */
    public boolean f426u = false;

    /* renamed from: v */
    public boolean f427v = false;

    /* renamed from: w */
    public boolean f428w = false;

    /* renamed from: x */
    public int f429x = 16;

    /* renamed from: C */
    public boolean f405C = false;

    /* renamed from: androidx.appcompat.view.menu.g$a */
    public class a implements AbstractC0752b.a {
        public a() {
        }
    }

    public C0072g(C0070e c0070e, int i6, int i7, int i8, int i9, CharSequence charSequence, int i10) {
        this.f419n = c0070e;
        this.f406a = i7;
        this.f407b = i6;
        this.f408c = i8;
        this.f409d = i9;
        this.f410e = charSequence;
        this.f430y = i10;
    }

    /* renamed from: c */
    public static void m208c(StringBuilder sb, int i6, int i7, String str) {
        if ((i6 & i7) == i7) {
            sb.append(str);
        }
    }

    @Override // p168z.InterfaceMenuItemC2096b
    /* renamed from: a */
    public final InterfaceMenuItemC2096b mo209a(AbstractC0752b abstractC0752b) {
        AbstractC0752b abstractC0752b2 = this.f403A;
        if (abstractC0752b2 != null) {
            abstractC0752b2.f4015a = null;
        }
        this.f431z = null;
        this.f403A = abstractC0752b;
        this.f419n.m195r(true);
        AbstractC0752b abstractC0752b3 = this.f403A;
        if (abstractC0752b3 != null) {
            abstractC0752b3.mo2146h(new a());
        }
        return this;
    }

    @Override // p168z.InterfaceMenuItemC2096b
    /* renamed from: b */
    public final AbstractC0752b mo210b() {
        return this.f403A;
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final boolean collapseActionView() {
        if ((this.f430y & 8) == 0) {
            return false;
        }
        if (this.f431z == null) {
            return true;
        }
        MenuItem.OnActionExpandListener onActionExpandListener = this.f404B;
        if (onActionExpandListener == null || onActionExpandListener.onMenuItemActionCollapse(this)) {
            return this.f419n.mo182e(this);
        }
        return false;
    }

    /* renamed from: d */
    public final Drawable m211d(Drawable drawable) {
        if (drawable != null && this.f428w && (this.f426u || this.f427v)) {
            drawable = drawable.mutate();
            if (this.f426u) {
                drawable.setTintList(this.f424s);
            }
            if (this.f427v) {
                drawable.setTintMode(this.f425t);
            }
            this.f428w = false;
        }
        return drawable;
    }

    /* renamed from: e */
    public final char m212e() {
        return this.f419n.mo192o() ? this.f415j : this.f413h;
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final boolean expandActionView() {
        if (!m213f()) {
            return false;
        }
        MenuItem.OnActionExpandListener onActionExpandListener = this.f404B;
        if (onActionExpandListener == null || onActionExpandListener.onMenuItemActionExpand(this)) {
            return this.f419n.mo184g(this);
        }
        return false;
    }

    /* renamed from: f */
    public final boolean m213f() {
        AbstractC0752b abstractC0752b;
        if ((this.f430y & 8) == 0) {
            return false;
        }
        if (this.f431z == null && (abstractC0752b = this.f403A) != null) {
            this.f431z = abstractC0752b.mo2142d(this);
        }
        return this.f431z != null;
    }

    /* renamed from: g */
    public final boolean m214g() {
        return (this.f429x & 32) == 32;
    }

    @Override // android.view.MenuItem
    public final ActionProvider getActionProvider() {
        throw new UnsupportedOperationException("This is not supported, use MenuItemCompat.getActionProvider()");
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final View getActionView() {
        View view = this.f431z;
        if (view != null) {
            return view;
        }
        AbstractC0752b abstractC0752b = this.f403A;
        if (abstractC0752b == null) {
            return null;
        }
        View mo2142d = abstractC0752b.mo2142d(this);
        this.f431z = mo2142d;
        return mo2142d;
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final int getAlphabeticModifiers() {
        return this.f416k;
    }

    @Override // android.view.MenuItem
    public final char getAlphabeticShortcut() {
        return this.f415j;
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final CharSequence getContentDescription() {
        return this.f422q;
    }

    @Override // android.view.MenuItem
    public final int getGroupId() {
        return this.f407b;
    }

    @Override // android.view.MenuItem
    public final Drawable getIcon() {
        Drawable drawable = this.f417l;
        if (drawable != null) {
            return m211d(drawable);
        }
        int i6 = this.f418m;
        if (i6 == 0) {
            return null;
        }
        Drawable m2138a = C0750a.m2138a(this.f419n.f376a, i6);
        this.f418m = 0;
        this.f417l = m2138a;
        return m211d(m2138a);
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final ColorStateList getIconTintList() {
        return this.f424s;
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final PorterDuff.Mode getIconTintMode() {
        return this.f425t;
    }

    @Override // android.view.MenuItem
    public final Intent getIntent() {
        return this.f412g;
    }

    @Override // android.view.MenuItem
    @ViewDebug.CapturedViewProperty
    public final int getItemId() {
        return this.f406a;
    }

    @Override // android.view.MenuItem
    public final ContextMenu.ContextMenuInfo getMenuInfo() {
        return null;
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final int getNumericModifiers() {
        return this.f414i;
    }

    @Override // android.view.MenuItem
    public final char getNumericShortcut() {
        return this.f413h;
    }

    @Override // android.view.MenuItem
    public final int getOrder() {
        return this.f408c;
    }

    @Override // android.view.MenuItem
    public final SubMenu getSubMenu() {
        return this.f420o;
    }

    @Override // android.view.MenuItem
    @ViewDebug.CapturedViewProperty
    public final CharSequence getTitle() {
        return this.f410e;
    }

    @Override // android.view.MenuItem
    public final CharSequence getTitleCondensed() {
        CharSequence charSequence = this.f411f;
        return charSequence != null ? charSequence : this.f410e;
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final CharSequence getTooltipText() {
        return this.f423r;
    }

    /* renamed from: h */
    public final boolean m215h() {
        return (this.f429x & 4) != 0;
    }

    @Override // android.view.MenuItem
    public final boolean hasSubMenu() {
        return this.f420o != null;
    }

    /* renamed from: i */
    public final InterfaceMenuItemC2096b m216i(View view) {
        int i6;
        this.f431z = view;
        this.f403A = null;
        if (view != null && view.getId() == -1 && (i6 = this.f406a) > 0) {
            view.setId(i6);
        }
        this.f419n.m194q();
        return this;
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final boolean isActionViewExpanded() {
        return this.f405C;
    }

    @Override // android.view.MenuItem
    public final boolean isCheckable() {
        return (this.f429x & 1) == 1;
    }

    @Override // android.view.MenuItem
    public final boolean isChecked() {
        return (this.f429x & 2) == 2;
    }

    @Override // android.view.MenuItem
    public final boolean isEnabled() {
        return (this.f429x & 16) != 0;
    }

    @Override // android.view.MenuItem
    public final boolean isVisible() {
        AbstractC0752b abstractC0752b = this.f403A;
        return (abstractC0752b == null || !abstractC0752b.mo2145g()) ? (this.f429x & 8) == 0 : (this.f429x & 8) == 0 && this.f403A.mo2140b();
    }

    /* renamed from: j */
    public final void m217j(boolean z5) {
        int i6 = this.f429x;
        int i7 = (z5 ? 2 : 0) | (i6 & (-3));
        this.f429x = i7;
        if (i6 != i7) {
            this.f419n.m195r(false);
        }
    }

    /* renamed from: k */
    public final void m218k(boolean z5) {
        this.f429x = z5 ? this.f429x | 32 : this.f429x & (-33);
    }

    @Override // android.view.MenuItem
    public final MenuItem setActionProvider(ActionProvider actionProvider) {
        throw new UnsupportedOperationException("This is not supported, use MenuItemCompat.setActionProvider()");
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final MenuItem setActionView(int i6) {
        Context context = this.f419n.f376a;
        m216i(LayoutInflater.from(context).inflate(i6, (ViewGroup) new LinearLayout(context), false));
        return this;
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final /* bridge */ /* synthetic */ MenuItem setActionView(View view) {
        m216i(view);
        return this;
    }

    @Override // android.view.MenuItem
    public final MenuItem setAlphabeticShortcut(char c) {
        if (this.f415j == c) {
            return this;
        }
        this.f415j = Character.toLowerCase(c);
        this.f419n.m195r(false);
        return this;
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final MenuItem setAlphabeticShortcut(char c, int i6) {
        if (this.f415j == c && this.f416k == i6) {
            return this;
        }
        this.f415j = Character.toLowerCase(c);
        this.f416k = KeyEvent.normalizeMetaState(i6);
        this.f419n.m195r(false);
        return this;
    }

    @Override // android.view.MenuItem
    public final MenuItem setCheckable(boolean z5) {
        int i6 = this.f429x;
        int i7 = (z5 ? 1 : 0) | (i6 & (-2));
        this.f429x = i7;
        if (i6 != i7) {
            this.f419n.m195r(false);
        }
        return this;
    }

    @Override // android.view.MenuItem
    public final MenuItem setChecked(boolean z5) {
        if ((this.f429x & 4) != 0) {
            C0070e c0070e = this.f419n;
            Objects.requireNonNull(c0070e);
            int groupId = getGroupId();
            int size = c0070e.f381f.size();
            c0070e.m177B();
            for (int i6 = 0; i6 < size; i6++) {
                C0072g c0072g = c0070e.f381f.get(i6);
                if (c0072g.f407b == groupId && c0072g.m215h() && c0072g.isCheckable()) {
                    c0072g.m217j(c0072g == this);
                }
            }
            c0070e.m176A();
        } else {
            m217j(z5);
        }
        return this;
    }

    @Override // android.view.MenuItem
    public final /* bridge */ /* synthetic */ MenuItem setContentDescription(CharSequence charSequence) {
        setContentDescription(charSequence);
        return this;
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final InterfaceMenuItemC2096b setContentDescription(CharSequence charSequence) {
        this.f422q = charSequence;
        this.f419n.m195r(false);
        return this;
    }

    @Override // android.view.MenuItem
    public final MenuItem setEnabled(boolean z5) {
        this.f429x = z5 ? this.f429x | 16 : this.f429x & (-17);
        this.f419n.m195r(false);
        return this;
    }

    @Override // android.view.MenuItem
    public final MenuItem setIcon(int i6) {
        this.f417l = null;
        this.f418m = i6;
        this.f428w = true;
        this.f419n.m195r(false);
        return this;
    }

    @Override // android.view.MenuItem
    public final MenuItem setIcon(Drawable drawable) {
        this.f418m = 0;
        this.f417l = drawable;
        this.f428w = true;
        this.f419n.m195r(false);
        return this;
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final MenuItem setIconTintList(ColorStateList colorStateList) {
        this.f424s = colorStateList;
        this.f426u = true;
        this.f428w = true;
        this.f419n.m195r(false);
        return this;
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final MenuItem setIconTintMode(PorterDuff.Mode mode) {
        this.f425t = mode;
        this.f427v = true;
        this.f428w = true;
        this.f419n.m195r(false);
        return this;
    }

    @Override // android.view.MenuItem
    public final MenuItem setIntent(Intent intent) {
        this.f412g = intent;
        return this;
    }

    @Override // android.view.MenuItem
    public final MenuItem setNumericShortcut(char c) {
        if (this.f413h == c) {
            return this;
        }
        this.f413h = c;
        this.f419n.m195r(false);
        return this;
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final MenuItem setNumericShortcut(char c, int i6) {
        if (this.f413h == c && this.f414i == i6) {
            return this;
        }
        this.f413h = c;
        this.f414i = KeyEvent.normalizeMetaState(i6);
        this.f419n.m195r(false);
        return this;
    }

    @Override // android.view.MenuItem
    public final MenuItem setOnActionExpandListener(MenuItem.OnActionExpandListener onActionExpandListener) {
        this.f404B = onActionExpandListener;
        return this;
    }

    @Override // android.view.MenuItem
    public final MenuItem setOnMenuItemClickListener(MenuItem.OnMenuItemClickListener onMenuItemClickListener) {
        this.f421p = onMenuItemClickListener;
        return this;
    }

    @Override // android.view.MenuItem
    public final MenuItem setShortcut(char c, char c6) {
        this.f413h = c;
        this.f415j = Character.toLowerCase(c6);
        this.f419n.m195r(false);
        return this;
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final MenuItem setShortcut(char c, char c6, int i6, int i7) {
        this.f413h = c;
        this.f414i = KeyEvent.normalizeMetaState(i6);
        this.f415j = Character.toLowerCase(c6);
        this.f416k = KeyEvent.normalizeMetaState(i7);
        this.f419n.m195r(false);
        return this;
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final void setShowAsAction(int i6) {
        int i7 = i6 & 3;
        if (i7 != 0 && i7 != 1 && i7 != 2) {
            throw new IllegalArgumentException("SHOW_AS_ACTION_ALWAYS, SHOW_AS_ACTION_IF_ROOM, and SHOW_AS_ACTION_NEVER are mutually exclusive.");
        }
        this.f430y = i6;
        this.f419n.m194q();
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final MenuItem setShowAsActionFlags(int i6) {
        setShowAsAction(i6);
        return this;
    }

    @Override // android.view.MenuItem
    public final MenuItem setTitle(int i6) {
        setTitle(this.f419n.f376a.getString(i6));
        return this;
    }

    @Override // android.view.MenuItem
    public final MenuItem setTitle(CharSequence charSequence) {
        this.f410e = charSequence;
        this.f419n.m195r(false);
        SubMenuC0077l subMenuC0077l = this.f420o;
        if (subMenuC0077l != null) {
            subMenuC0077l.setHeaderTitle(charSequence);
        }
        return this;
    }

    @Override // android.view.MenuItem
    public final MenuItem setTitleCondensed(CharSequence charSequence) {
        this.f411f = charSequence;
        this.f419n.m195r(false);
        return this;
    }

    @Override // android.view.MenuItem
    public final /* bridge */ /* synthetic */ MenuItem setTooltipText(CharSequence charSequence) {
        setTooltipText(charSequence);
        return this;
    }

    @Override // p168z.InterfaceMenuItemC2096b, android.view.MenuItem
    public final InterfaceMenuItemC2096b setTooltipText(CharSequence charSequence) {
        this.f423r = charSequence;
        this.f419n.m195r(false);
        return this;
    }

    @Override // android.view.MenuItem
    public final MenuItem setVisible(boolean z5) {
        int i6 = this.f429x;
        int i7 = (z5 ? 0 : 8) | (i6 & (-9));
        this.f429x = i7;
        if (i6 != i7) {
            C0070e c0070e = this.f419n;
            c0070e.f383h = true;
            c0070e.m195r(true);
        }
        return this;
    }

    public final String toString() {
        CharSequence charSequence = this.f410e;
        if (charSequence != null) {
            return charSequence.toString();
        }
        return null;
    }
}
