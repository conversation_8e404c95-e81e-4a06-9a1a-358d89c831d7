package androidx.appcompat.view.menu;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Parcelable;
import android.util.SparseArray;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;
import android.view.ViewConfiguration;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import p029e0.C0768r;
import p142v.C1450a;
import p168z.InterfaceMenuC2095a;

/* renamed from: androidx.appcompat.view.menu.e */
/* loaded from: classes.dex */
public class C0070e implements InterfaceMenuC2095a {

    /* renamed from: y */
    public static final int[] f375y = {1, 4, 5, 3, 2, 0};

    /* renamed from: a */
    public final Context f376a;

    /* renamed from: b */
    public final Resources f377b;

    /* renamed from: c */
    public boolean f378c;

    /* renamed from: d */
    public boolean f379d;

    /* renamed from: e */
    public a f380e;

    /* renamed from: f */
    public ArrayList<C0072g> f381f;

    /* renamed from: g */
    public ArrayList<C0072g> f382g;

    /* renamed from: h */
    public boolean f383h;

    /* renamed from: i */
    public ArrayList<C0072g> f384i;

    /* renamed from: j */
    public ArrayList<C0072g> f385j;

    /* renamed from: k */
    public boolean f386k;

    /* renamed from: m */
    public CharSequence f388m;

    /* renamed from: n */
    public Drawable f389n;

    /* renamed from: o */
    public View f390o;

    /* renamed from: v */
    public C0072g f397v;

    /* renamed from: x */
    public boolean f399x;

    /* renamed from: l */
    public int f387l = 0;

    /* renamed from: p */
    public boolean f391p = false;

    /* renamed from: q */
    public boolean f392q = false;

    /* renamed from: r */
    public boolean f393r = false;

    /* renamed from: s */
    public boolean f394s = false;

    /* renamed from: t */
    public ArrayList<C0072g> f395t = new ArrayList<>();

    /* renamed from: u */
    public CopyOnWriteArrayList<WeakReference<InterfaceC0074i>> f396u = new CopyOnWriteArrayList<>();

    /* renamed from: w */
    public boolean f398w = false;

    /* renamed from: androidx.appcompat.view.menu.e$a */
    public interface a {
        /* renamed from: a */
        boolean mo204a(C0070e c0070e, MenuItem menuItem);

        /* renamed from: b */
        void mo205b(C0070e c0070e);
    }

    /* renamed from: androidx.appcompat.view.menu.e$b */
    public interface b {
        /* renamed from: a */
        boolean mo145a(C0072g c0072g);
    }

    public C0070e(Context context) {
        boolean z5 = false;
        this.f376a = context;
        Resources resources = context.getResources();
        this.f377b = resources;
        this.f381f = new ArrayList<>();
        this.f382g = new ArrayList<>();
        this.f383h = true;
        this.f384i = new ArrayList<>();
        this.f385j = new ArrayList<>();
        this.f386k = true;
        if (resources.getConfiguration().keyboard != 1 && C0768r.m2206e(ViewConfiguration.get(context), context)) {
            z5 = true;
        }
        this.f379d = z5;
    }

    /* renamed from: A */
    public final void m176A() {
        this.f391p = false;
        if (this.f392q) {
            this.f392q = false;
            m195r(this.f393r);
        }
    }

    /* renamed from: B */
    public final void m177B() {
        if (this.f391p) {
            return;
        }
        this.f391p = true;
        this.f392q = false;
        this.f393r = false;
    }

    /* renamed from: a */
    public final MenuItem m178a(int i6, int i7, int i8, CharSequence charSequence) {
        int i9;
        int i10 = ((-65536) & i8) >> 16;
        if (i10 >= 0) {
            int[] iArr = f375y;
            if (i10 < 6) {
                int i11 = (iArr[i10] << 16) | (65535 & i8);
                C0072g c0072g = new C0072g(this, i6, i7, i8, i11, charSequence, this.f387l);
                ArrayList<C0072g> arrayList = this.f381f;
                int size = arrayList.size();
                while (true) {
                    size--;
                    if (size < 0) {
                        i9 = 0;
                        break;
                    }
                    if (arrayList.get(size).f409d <= i11) {
                        i9 = size + 1;
                        break;
                    }
                }
                arrayList.add(i9, c0072g);
                m195r(true);
                return c0072g;
            }
        }
        throw new IllegalArgumentException("order does not contain a valid category.");
    }

    @Override // android.view.Menu
    public final MenuItem add(int i6) {
        return m178a(0, 0, 0, this.f377b.getString(i6));
    }

    @Override // android.view.Menu
    public final MenuItem add(int i6, int i7, int i8, int i9) {
        return m178a(i6, i7, i8, this.f377b.getString(i9));
    }

    @Override // android.view.Menu
    public final MenuItem add(int i6, int i7, int i8, CharSequence charSequence) {
        return m178a(i6, i7, i8, charSequence);
    }

    @Override // android.view.Menu
    public final MenuItem add(CharSequence charSequence) {
        return m178a(0, 0, 0, charSequence);
    }

    @Override // android.view.Menu
    public final int addIntentOptions(int i6, int i7, int i8, ComponentName componentName, Intent[] intentArr, Intent intent, int i9, MenuItem[] menuItemArr) {
        int i10;
        PackageManager packageManager = this.f376a.getPackageManager();
        List<ResolveInfo> queryIntentActivityOptions = packageManager.queryIntentActivityOptions(componentName, intentArr, intent, 0);
        int size = queryIntentActivityOptions != null ? queryIntentActivityOptions.size() : 0;
        if ((i9 & 1) == 0) {
            removeGroup(i6);
        }
        for (int i11 = 0; i11 < size; i11++) {
            ResolveInfo resolveInfo = queryIntentActivityOptions.get(i11);
            int i12 = resolveInfo.specificIndex;
            Intent intent2 = new Intent(i12 < 0 ? intent : intentArr[i12]);
            ActivityInfo activityInfo = resolveInfo.activityInfo;
            intent2.setComponent(new ComponentName(activityInfo.applicationInfo.packageName, activityInfo.name));
            C0072g c0072g = (C0072g) m178a(i6, i7, i8, resolveInfo.loadLabel(packageManager));
            c0072g.setIcon(resolveInfo.loadIcon(packageManager));
            c0072g.setIntent(intent2);
            if (menuItemArr != null && (i10 = resolveInfo.specificIndex) >= 0) {
                menuItemArr[i10] = c0072g;
            }
        }
        return size;
    }

    @Override // android.view.Menu
    public final SubMenu addSubMenu(int i6) {
        return addSubMenu(0, 0, 0, this.f377b.getString(i6));
    }

    @Override // android.view.Menu
    public final SubMenu addSubMenu(int i6, int i7, int i8, int i9) {
        return addSubMenu(i6, i7, i8, this.f377b.getString(i9));
    }

    @Override // android.view.Menu
    public final SubMenu addSubMenu(int i6, int i7, int i8, CharSequence charSequence) {
        C0072g c0072g = (C0072g) m178a(i6, i7, i8, charSequence);
        SubMenuC0077l subMenuC0077l = new SubMenuC0077l(this.f376a, this, c0072g);
        c0072g.f420o = subMenuC0077l;
        subMenuC0077l.setHeaderTitle(c0072g.f410e);
        return subMenuC0077l;
    }

    @Override // android.view.Menu
    public final SubMenu addSubMenu(CharSequence charSequence) {
        return addSubMenu(0, 0, 0, charSequence);
    }

    /* renamed from: b */
    public final void m179b(InterfaceC0074i interfaceC0074i) {
        m180c(interfaceC0074i, this.f376a);
    }

    /* renamed from: c */
    public final void m180c(InterfaceC0074i interfaceC0074i, Context context) {
        this.f396u.add(new WeakReference<>(interfaceC0074i));
        interfaceC0074i.mo171e(context, this);
        this.f386k = true;
    }

    @Override // android.view.Menu
    public final void clear() {
        C0072g c0072g = this.f397v;
        if (c0072g != null) {
            mo182e(c0072g);
        }
        this.f381f.clear();
        m195r(true);
    }

    public final void clearHeader() {
        this.f389n = null;
        this.f388m = null;
        this.f390o = null;
        m195r(false);
    }

    @Override // android.view.Menu
    public final void close() {
        m181d(true);
    }

    /* renamed from: d */
    public final void m181d(boolean z5) {
        if (this.f394s) {
            return;
        }
        this.f394s = true;
        Iterator<WeakReference<InterfaceC0074i>> it = this.f396u.iterator();
        while (it.hasNext()) {
            WeakReference<InterfaceC0074i> next = it.next();
            InterfaceC0074i interfaceC0074i = next.get();
            if (interfaceC0074i == null) {
                this.f396u.remove(next);
            } else {
                interfaceC0074i.mo152a(this, z5);
            }
        }
        this.f394s = false;
    }

    /* renamed from: e */
    public boolean mo182e(C0072g c0072g) {
        boolean z5 = false;
        if (!this.f396u.isEmpty() && this.f397v == c0072g) {
            m177B();
            Iterator<WeakReference<InterfaceC0074i>> it = this.f396u.iterator();
            while (it.hasNext()) {
                WeakReference<InterfaceC0074i> next = it.next();
                InterfaceC0074i interfaceC0074i = next.get();
                if (interfaceC0074i == null) {
                    this.f396u.remove(next);
                } else {
                    z5 = interfaceC0074i.mo149c(c0072g);
                    if (z5) {
                        break;
                    }
                }
            }
            m176A();
            if (z5) {
                this.f397v = null;
            }
        }
        return z5;
    }

    /* renamed from: f */
    public boolean mo183f(C0070e c0070e, MenuItem menuItem) {
        a aVar = this.f380e;
        return aVar != null && aVar.mo204a(c0070e, menuItem);
    }

    @Override // android.view.Menu
    public final MenuItem findItem(int i6) {
        MenuItem findItem;
        int size = size();
        for (int i7 = 0; i7 < size; i7++) {
            C0072g c0072g = this.f381f.get(i7);
            if (c0072g.f406a == i6) {
                return c0072g;
            }
            if (c0072g.hasSubMenu() && (findItem = c0072g.f420o.findItem(i6)) != null) {
                return findItem;
            }
        }
        return null;
    }

    /* renamed from: g */
    public boolean mo184g(C0072g c0072g) {
        boolean z5 = false;
        if (this.f396u.isEmpty()) {
            return false;
        }
        m177B();
        Iterator<WeakReference<InterfaceC0074i>> it = this.f396u.iterator();
        while (it.hasNext()) {
            WeakReference<InterfaceC0074i> next = it.next();
            InterfaceC0074i interfaceC0074i = next.get();
            if (interfaceC0074i == null) {
                this.f396u.remove(next);
            } else {
                z5 = interfaceC0074i.mo151i(c0072g);
                if (z5) {
                    break;
                }
            }
        }
        m176A();
        if (z5) {
            this.f397v = c0072g;
        }
        return z5;
    }

    @Override // android.view.Menu
    public final MenuItem getItem(int i6) {
        return this.f381f.get(i6);
    }

    /* renamed from: h */
    public final C0072g m185h(int i6, KeyEvent keyEvent) {
        ArrayList<C0072g> arrayList = this.f395t;
        arrayList.clear();
        m186i(arrayList, i6, keyEvent);
        if (arrayList.isEmpty()) {
            return null;
        }
        int metaState = keyEvent.getMetaState();
        KeyCharacterMap.KeyData keyData = new KeyCharacterMap.KeyData();
        keyEvent.getKeyData(keyData);
        int size = arrayList.size();
        if (size == 1) {
            return arrayList.get(0);
        }
        boolean mo192o = mo192o();
        for (int i7 = 0; i7 < size; i7++) {
            C0072g c0072g = arrayList.get(i7);
            char c = mo192o ? c0072g.f415j : c0072g.f413h;
            char[] cArr = keyData.meta;
            if ((c == cArr[0] && (metaState & 2) == 0) || ((c == cArr[2] && (metaState & 2) != 0) || (mo192o && c == '\b' && i6 == 67))) {
                return c0072g;
            }
        }
        return null;
    }

    @Override // android.view.Menu
    public final boolean hasVisibleItems() {
        if (this.f399x) {
            return true;
        }
        int size = size();
        for (int i6 = 0; i6 < size; i6++) {
            if (this.f381f.get(i6).isVisible()) {
                return true;
            }
        }
        return false;
    }

    /* renamed from: i */
    public final void m186i(List<C0072g> list, int i6, KeyEvent keyEvent) {
        boolean mo192o = mo192o();
        int modifiers = keyEvent.getModifiers();
        KeyCharacterMap.KeyData keyData = new KeyCharacterMap.KeyData();
        if (keyEvent.getKeyData(keyData) || i6 == 67) {
            int size = this.f381f.size();
            for (int i7 = 0; i7 < size; i7++) {
                C0072g c0072g = this.f381f.get(i7);
                if (c0072g.hasSubMenu()) {
                    c0072g.f420o.m186i(list, i6, keyEvent);
                }
                char c = mo192o ? c0072g.f415j : c0072g.f413h;
                if (((modifiers & 69647) == ((mo192o ? c0072g.f416k : c0072g.f414i) & 69647)) && c != 0) {
                    char[] cArr = keyData.meta;
                    if ((c == cArr[0] || c == cArr[2] || (mo192o && c == '\b' && i6 == 67)) && c0072g.isEnabled()) {
                        list.add(c0072g);
                    }
                }
            }
        }
    }

    @Override // android.view.Menu
    public final boolean isShortcutKey(int i6, KeyEvent keyEvent) {
        return m185h(i6, keyEvent) != null;
    }

    /* renamed from: j */
    public final void m187j() {
        ArrayList<C0072g> m190m = m190m();
        if (this.f386k) {
            Iterator<WeakReference<InterfaceC0074i>> it = this.f396u.iterator();
            boolean z5 = false;
            while (it.hasNext()) {
                WeakReference<InterfaceC0074i> next = it.next();
                InterfaceC0074i interfaceC0074i = next.get();
                if (interfaceC0074i == null) {
                    this.f396u.remove(next);
                } else {
                    z5 |= interfaceC0074i.mo154d();
                }
            }
            if (z5) {
                this.f384i.clear();
                this.f385j.clear();
                int size = m190m.size();
                for (int i6 = 0; i6 < size; i6++) {
                    C0072g c0072g = m190m.get(i6);
                    (c0072g.m214g() ? this.f384i : this.f385j).add(c0072g);
                }
            } else {
                this.f384i.clear();
                this.f385j.clear();
                this.f385j.addAll(m190m());
            }
            this.f386k = false;
        }
    }

    /* renamed from: k */
    public String mo188k() {
        return "android:menu:actionviewstates";
    }

    /* renamed from: l */
    public C0070e mo189l() {
        return this;
    }

    /* renamed from: m */
    public final ArrayList<C0072g> m190m() {
        if (!this.f383h) {
            return this.f382g;
        }
        this.f382g.clear();
        int size = this.f381f.size();
        for (int i6 = 0; i6 < size; i6++) {
            C0072g c0072g = this.f381f.get(i6);
            if (c0072g.isVisible()) {
                this.f382g.add(c0072g);
            }
        }
        this.f383h = false;
        this.f386k = true;
        return this.f382g;
    }

    /* renamed from: n */
    public boolean mo191n() {
        return this.f398w;
    }

    /* renamed from: o */
    public boolean mo192o() {
        return this.f378c;
    }

    /* renamed from: p */
    public boolean mo193p() {
        return this.f379d;
    }

    @Override // android.view.Menu
    public final boolean performIdentifierAction(int i6, int i7) {
        return m197t(findItem(i6), null, i7);
    }

    @Override // android.view.Menu
    public final boolean performShortcut(int i6, KeyEvent keyEvent, int i7) {
        C0072g m185h = m185h(i6, keyEvent);
        boolean m197t = m185h != null ? m197t(m185h, null, i7) : false;
        if ((i7 & 2) != 0) {
            m181d(true);
        }
        return m197t;
    }

    /* renamed from: q */
    public final void m194q() {
        this.f386k = true;
        m195r(true);
    }

    /* renamed from: r */
    public final void m195r(boolean z5) {
        if (this.f391p) {
            this.f392q = true;
            if (z5) {
                this.f393r = true;
                return;
            }
            return;
        }
        if (z5) {
            this.f383h = true;
            this.f386k = true;
        }
        if (this.f396u.isEmpty()) {
            return;
        }
        m177B();
        Iterator<WeakReference<InterfaceC0074i>> it = this.f396u.iterator();
        while (it.hasNext()) {
            WeakReference<InterfaceC0074i> next = it.next();
            InterfaceC0074i interfaceC0074i = next.get();
            if (interfaceC0074i == null) {
                this.f396u.remove(next);
            } else {
                interfaceC0074i.mo156g();
            }
        }
        m176A();
    }

    @Override // android.view.Menu
    public final void removeGroup(int i6) {
        int size = size();
        int i7 = 0;
        while (true) {
            if (i7 >= size) {
                i7 = -1;
                break;
            } else if (this.f381f.get(i7).f407b == i6) {
                break;
            } else {
                i7++;
            }
        }
        if (i7 >= 0) {
            int size2 = this.f381f.size() - i7;
            int i8 = 0;
            while (true) {
                int i9 = i8 + 1;
                if (i8 >= size2 || this.f381f.get(i7).f407b != i6) {
                    break;
                }
                m198u(i7, false);
                i8 = i9;
            }
            m195r(true);
        }
    }

    @Override // android.view.Menu
    public final void removeItem(int i6) {
        int size = size();
        int i7 = 0;
        while (true) {
            if (i7 >= size) {
                i7 = -1;
                break;
            } else if (this.f381f.get(i7).f406a == i6) {
                break;
            } else {
                i7++;
            }
        }
        m198u(i7, true);
    }

    /* renamed from: s */
    public final boolean m196s(MenuItem menuItem, int i6) {
        return m197t(menuItem, null, i6);
    }

    @Override // android.view.Menu
    public final void setGroupCheckable(int i6, boolean z5, boolean z6) {
        int size = this.f381f.size();
        for (int i7 = 0; i7 < size; i7++) {
            C0072g c0072g = this.f381f.get(i7);
            if (c0072g.f407b == i6) {
                c0072g.f429x = (c0072g.f429x & (-5)) | (z6 ? 4 : 0);
                c0072g.setCheckable(z5);
            }
        }
    }

    @Override // android.view.Menu
    public void setGroupDividerEnabled(boolean z5) {
        this.f398w = z5;
    }

    @Override // android.view.Menu
    public final void setGroupEnabled(int i6, boolean z5) {
        int size = this.f381f.size();
        for (int i7 = 0; i7 < size; i7++) {
            C0072g c0072g = this.f381f.get(i7);
            if (c0072g.f407b == i6) {
                c0072g.setEnabled(z5);
            }
        }
    }

    @Override // android.view.Menu
    public final void setGroupVisible(int i6, boolean z5) {
        int size = this.f381f.size();
        boolean z6 = false;
        for (int i7 = 0; i7 < size; i7++) {
            C0072g c0072g = this.f381f.get(i7);
            if (c0072g.f407b == i6) {
                int i8 = c0072g.f429x;
                int i9 = (i8 & (-9)) | (z5 ? 0 : 8);
                c0072g.f429x = i9;
                if (i8 != i9) {
                    z6 = true;
                }
            }
        }
        if (z6) {
            m195r(true);
        }
    }

    @Override // android.view.Menu
    public void setQwertyMode(boolean z5) {
        this.f378c = z5;
        m195r(false);
    }

    @Override // android.view.Menu
    public final int size() {
        return this.f381f.size();
    }

    /* JADX WARN: Code restructure failed: missing block: B:18:0x005b, code lost:
    
        if (r1 != false) goto L119;
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x005d, code lost:
    
        m181d(true);
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x00cc, code lost:
    
        return r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x006d, code lost:
    
        if ((r9 & 1) == 0) goto L119;
     */
    /* JADX WARN: Code restructure failed: missing block: B:57:0x00c9, code lost:
    
        if (r1 == false) goto L119;
     */
    /* JADX WARN: Removed duplicated region for block: B:17:0x0056  */
    /* JADX WARN: Removed duplicated region for block: B:22:0x0062  */
    /* renamed from: t */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean m197t(android.view.MenuItem r7, androidx.appcompat.view.menu.InterfaceC0074i r8, int r9) {
        /*
            r6 = this;
            androidx.appcompat.view.menu.g r7 = (androidx.appcompat.view.menu.C0072g) r7
            r0 = 0
            if (r7 == 0) goto Lcd
            boolean r1 = r7.isEnabled()
            if (r1 != 0) goto Ld
            goto Lcd
        Ld:
            android.view.MenuItem$OnMenuItemClickListener r1 = r7.f421p
            r2 = 1
            if (r1 == 0) goto L19
            boolean r1 = r1.onMenuItemClick(r7)
            if (r1 == 0) goto L19
            goto L21
        L19:
            androidx.appcompat.view.menu.e r1 = r7.f419n
            boolean r1 = r1.mo183f(r1, r7)
            if (r1 == 0) goto L23
        L21:
            r1 = r2
            goto L43
        L23:
            android.content.Intent r1 = r7.f412g
            if (r1 == 0) goto L37
            androidx.appcompat.view.menu.e r3 = r7.f419n     // Catch: android.content.ActivityNotFoundException -> L2f
            android.content.Context r3 = r3.f376a     // Catch: android.content.ActivityNotFoundException -> L2f
            r3.startActivity(r1)     // Catch: android.content.ActivityNotFoundException -> L2f
            goto L21
        L2f:
            r1 = move-exception
            java.lang.String r3 = "MenuItemImpl"
            java.lang.String r4 = "Can't find activity to handle intent; ignoring"
            android.util.Log.e(r3, r4, r1)
        L37:
            e0.b r1 = r7.f403A
            if (r1 == 0) goto L42
            boolean r1 = r1.mo2143e()
            if (r1 == 0) goto L42
            goto L21
        L42:
            r1 = r0
        L43:
            e0.b r3 = r7.f403A
            if (r3 == 0) goto L4f
            boolean r4 = r3.mo2139a()
            if (r4 == 0) goto L4f
            r4 = r2
            goto L50
        L4f:
            r4 = r0
        L50:
            boolean r5 = r7.m213f()
            if (r5 == 0) goto L62
            boolean r7 = r7.expandActionView()
            r1 = r1 | r7
            if (r1 == 0) goto Lcc
        L5d:
            r6.m181d(r2)
            goto Lcc
        L62:
            boolean r5 = r7.hasSubMenu()
            if (r5 != 0) goto L70
            if (r4 == 0) goto L6b
            goto L70
        L6b:
            r7 = r9 & 1
            if (r7 != 0) goto Lcc
            goto L5d
        L70:
            r9 = r9 & 4
            if (r9 != 0) goto L77
            r6.m181d(r0)
        L77:
            boolean r9 = r7.hasSubMenu()
            if (r9 != 0) goto L8b
            androidx.appcompat.view.menu.l r9 = new androidx.appcompat.view.menu.l
            android.content.Context r5 = r6.f376a
            r9.<init>(r5, r6, r7)
            r7.f420o = r9
            java.lang.CharSequence r5 = r7.f410e
            r9.setHeaderTitle(r5)
        L8b:
            androidx.appcompat.view.menu.l r7 = r7.f420o
            if (r4 == 0) goto L92
            r3.mo2144f(r7)
        L92:
            java.util.concurrent.CopyOnWriteArrayList<java.lang.ref.WeakReference<androidx.appcompat.view.menu.i>> r9 = r6.f396u
            boolean r9 = r9.isEmpty()
            if (r9 == 0) goto L9b
            goto Lc8
        L9b:
            if (r8 == 0) goto La1
            boolean r0 = r8.mo157j(r7)
        La1:
            java.util.concurrent.CopyOnWriteArrayList<java.lang.ref.WeakReference<androidx.appcompat.view.menu.i>> r8 = r6.f396u
            java.util.Iterator r8 = r8.iterator()
        La7:
            boolean r9 = r8.hasNext()
            if (r9 == 0) goto Lc8
            java.lang.Object r9 = r8.next()
            java.lang.ref.WeakReference r9 = (java.lang.ref.WeakReference) r9
            java.lang.Object r3 = r9.get()
            androidx.appcompat.view.menu.i r3 = (androidx.appcompat.view.menu.InterfaceC0074i) r3
            if (r3 != 0) goto Lc1
            java.util.concurrent.CopyOnWriteArrayList<java.lang.ref.WeakReference<androidx.appcompat.view.menu.i>> r3 = r6.f396u
            r3.remove(r9)
            goto La7
        Lc1:
            if (r0 != 0) goto La7
            boolean r0 = r3.mo157j(r7)
            goto La7
        Lc8:
            r1 = r1 | r0
            if (r1 != 0) goto Lcc
            goto L5d
        Lcc:
            return r1
        Lcd:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.view.menu.C0070e.m197t(android.view.MenuItem, androidx.appcompat.view.menu.i, int):boolean");
    }

    /* renamed from: u */
    public final void m198u(int i6, boolean z5) {
        if (i6 < 0 || i6 >= this.f381f.size()) {
            return;
        }
        this.f381f.remove(i6);
        if (z5) {
            m195r(true);
        }
    }

    /* renamed from: v */
    public final void m199v(InterfaceC0074i interfaceC0074i) {
        Iterator<WeakReference<InterfaceC0074i>> it = this.f396u.iterator();
        while (it.hasNext()) {
            WeakReference<InterfaceC0074i> next = it.next();
            InterfaceC0074i interfaceC0074i2 = next.get();
            if (interfaceC0074i2 == null || interfaceC0074i2 == interfaceC0074i) {
                this.f396u.remove(next);
            }
        }
    }

    /* renamed from: w */
    public final void m200w(Bundle bundle) {
        MenuItem findItem;
        if (bundle == null) {
            return;
        }
        SparseArray<Parcelable> sparseParcelableArray = bundle.getSparseParcelableArray(mo188k());
        int size = size();
        for (int i6 = 0; i6 < size; i6++) {
            MenuItem item = getItem(i6);
            View actionView = item.getActionView();
            if (actionView != null && actionView.getId() != -1) {
                actionView.restoreHierarchyState(sparseParcelableArray);
            }
            if (item.hasSubMenu()) {
                ((SubMenuC0077l) item.getSubMenu()).m200w(bundle);
            }
        }
        int i7 = bundle.getInt("android:menu:expandedactionview");
        if (i7 <= 0 || (findItem = findItem(i7)) == null) {
            return;
        }
        findItem.expandActionView();
    }

    /* renamed from: x */
    public final void m201x(Bundle bundle) {
        int size = size();
        SparseArray<? extends Parcelable> sparseArray = null;
        for (int i6 = 0; i6 < size; i6++) {
            MenuItem item = getItem(i6);
            View actionView = item.getActionView();
            if (actionView != null && actionView.getId() != -1) {
                if (sparseArray == null) {
                    sparseArray = new SparseArray<>();
                }
                actionView.saveHierarchyState(sparseArray);
                if (item.isActionViewExpanded()) {
                    bundle.putInt("android:menu:expandedactionview", item.getItemId());
                }
            }
            if (item.hasSubMenu()) {
                ((SubMenuC0077l) item.getSubMenu()).m201x(bundle);
            }
        }
        if (sparseArray != null) {
            bundle.putSparseParcelableArray(mo188k(), sparseArray);
        }
    }

    /* renamed from: y */
    public void mo202y(a aVar) {
        this.f380e = aVar;
    }

    /* renamed from: z */
    public final void m203z(int i6, CharSequence charSequence, int i7, Drawable drawable, View view) {
        Resources resources = this.f377b;
        if (view != null) {
            this.f390o = view;
            this.f388m = null;
            this.f389n = null;
        } else {
            if (i6 > 0) {
                this.f388m = resources.getText(i6);
            } else if (charSequence != null) {
                this.f388m = charSequence;
            }
            if (i7 > 0) {
                Context context = this.f376a;
                Object obj = C1450a.f6698a;
                this.f389n = C1450a.c.m3527b(context, i7);
            } else if (drawable != null) {
                this.f389n = drawable;
            }
            this.f390o = null;
        }
        m195r(false);
    }
}
