package androidx.appcompat.view.menu;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.os.Parcelable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import androidx.appcompat.view.menu.C0070e;
import androidx.appcompat.view.menu.InterfaceC0075j;
import androidx.appcompat.widget.AbstractViewOnTouchListenerC0143j0;
import androidx.appcompat.widget.ActionMenuView;
import androidx.appcompat.widget.C0119b0;
import androidx.appcompat.widget.C0121c;
import androidx.appcompat.widget.C0132f1;
import p008b0.C0385m;
import p049h.InterfaceC0898f;

/* loaded from: classes.dex */
public class ActionMenuItemView extends C0119b0 implements InterfaceC0075j.a, View.OnClickListener, ActionMenuView.InterfaceC0084a {

    /* renamed from: o */
    public C0072g f286o;

    /* renamed from: p */
    public CharSequence f287p;

    /* renamed from: q */
    public Drawable f288q;

    /* renamed from: r */
    public C0070e.b f289r;

    /* renamed from: s */
    public C0064a f290s;

    /* renamed from: t */
    public AbstractC0065b f291t;

    /* renamed from: u */
    public boolean f292u;

    /* renamed from: v */
    public boolean f293v;

    /* renamed from: w */
    public int f294w;

    /* renamed from: x */
    public int f295x;

    /* renamed from: y */
    public int f296y;

    /* renamed from: androidx.appcompat.view.menu.ActionMenuItemView$a */
    public class C0064a extends AbstractViewOnTouchListenerC0143j0 {
        public C0064a() {
            super(ActionMenuItemView.this);
        }

        @Override // androidx.appcompat.widget.AbstractViewOnTouchListenerC0143j0
        /* renamed from: b */
        public final InterfaceC0898f mo143b() {
            C0121c.a aVar;
            AbstractC0065b abstractC0065b = ActionMenuItemView.this.f291t;
            if (abstractC0065b == null || (aVar = C0121c.this.f715C) == null) {
                return null;
            }
            return aVar.m219a();
        }

        @Override // androidx.appcompat.widget.AbstractViewOnTouchListenerC0143j0
        /* renamed from: c */
        public final boolean mo144c() {
            InterfaceC0898f mo143b;
            ActionMenuItemView actionMenuItemView = ActionMenuItemView.this;
            C0070e.b bVar = actionMenuItemView.f289r;
            return bVar != null && bVar.mo145a(actionMenuItemView.f286o) && (mo143b = mo143b()) != null && mo143b.mo153b();
        }
    }

    /* renamed from: androidx.appcompat.view.menu.ActionMenuItemView$b */
    public static abstract class AbstractC0065b {
    }

    public ActionMenuItemView(Context context, AttributeSet attributeSet) {
        super(context, attributeSet, 0);
        Resources resources = context.getResources();
        this.f292u = m141e();
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C0385m.f2351m, 0, 0);
        this.f294w = obtainStyledAttributes.getDimensionPixelSize(0, 0);
        obtainStyledAttributes.recycle();
        this.f296y = (int) ((resources.getDisplayMetrics().density * 32.0f) + 0.5f);
        setOnClickListener(this);
        this.f295x = -1;
        setSaveEnabled(false);
    }

    @Override // androidx.appcompat.widget.ActionMenuView.InterfaceC0084a
    /* renamed from: a */
    public final boolean mo137a() {
        return m140d();
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0075j.a
    /* renamed from: b */
    public final void mo138b(C0072g c0072g) {
        this.f286o = c0072g;
        setIcon(c0072g.getIcon());
        setTitle(c0072g.getTitleCondensed());
        setId(c0072g.f406a);
        setVisibility(c0072g.isVisible() ? 0 : 8);
        setEnabled(c0072g.isEnabled());
        if (c0072g.hasSubMenu() && this.f290s == null) {
            this.f290s = new C0064a();
        }
    }

    @Override // androidx.appcompat.widget.ActionMenuView.InterfaceC0084a
    /* renamed from: c */
    public final boolean mo139c() {
        return m140d() && this.f286o.getIcon() == null;
    }

    /* renamed from: d */
    public final boolean m140d() {
        return !TextUtils.isEmpty(getText());
    }

    /* renamed from: e */
    public final boolean m141e() {
        Configuration configuration = getContext().getResources().getConfiguration();
        int i6 = configuration.screenWidthDp;
        return i6 >= 480 || (i6 >= 640 && configuration.screenHeightDp >= 480) || configuration.orientation == 2;
    }

    /* renamed from: f */
    public final void m142f() {
        boolean z5 = true;
        boolean z6 = !TextUtils.isEmpty(this.f287p);
        if (this.f288q != null) {
            if (!((this.f286o.f430y & 4) == 4) || (!this.f292u && !this.f293v)) {
                z5 = false;
            }
        }
        boolean z7 = z6 & z5;
        setText(z7 ? this.f287p : null);
        CharSequence charSequence = this.f286o.f422q;
        if (TextUtils.isEmpty(charSequence)) {
            charSequence = z7 ? null : this.f286o.f410e;
        }
        setContentDescription(charSequence);
        CharSequence charSequence2 = this.f286o.f423r;
        if (TextUtils.isEmpty(charSequence2)) {
            C0132f1.m387a(this, z7 ? null : this.f286o.f410e);
        } else {
            C0132f1.m387a(this, charSequence2);
        }
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0075j.a
    public C0072g getItemData() {
        return this.f286o;
    }

    @Override // android.view.View.OnClickListener
    public final void onClick(View view) {
        C0070e.b bVar = this.f289r;
        if (bVar != null) {
            bVar.mo145a(this.f286o);
        }
    }

    @Override // android.widget.TextView, android.view.View
    public final void onConfigurationChanged(Configuration configuration) {
        super.onConfigurationChanged(configuration);
        this.f292u = m141e();
        m142f();
    }

    @Override // androidx.appcompat.widget.C0119b0, android.widget.TextView, android.view.View
    public final void onMeasure(int i6, int i7) {
        int i8;
        boolean m140d = m140d();
        if (m140d && (i8 = this.f295x) >= 0) {
            super.setPadding(i8, getPaddingTop(), getPaddingRight(), getPaddingBottom());
        }
        super.onMeasure(i6, i7);
        int mode = View.MeasureSpec.getMode(i6);
        int size = View.MeasureSpec.getSize(i6);
        int measuredWidth = getMeasuredWidth();
        int min = mode == Integer.MIN_VALUE ? Math.min(size, this.f294w) : this.f294w;
        if (mode != 1073741824 && this.f294w > 0 && measuredWidth < min) {
            super.onMeasure(View.MeasureSpec.makeMeasureSpec(min, 1073741824), i7);
        }
        if (m140d || this.f288q == null) {
            return;
        }
        super.setPadding((getMeasuredWidth() - this.f288q.getBounds().width()) / 2, getPaddingTop(), getPaddingRight(), getPaddingBottom());
    }

    @Override // android.widget.TextView, android.view.View
    public final void onRestoreInstanceState(Parcelable parcelable) {
        super.onRestoreInstanceState(null);
    }

    @Override // android.widget.TextView, android.view.View
    public final boolean onTouchEvent(MotionEvent motionEvent) {
        C0064a c0064a;
        if (this.f286o.hasSubMenu() && (c0064a = this.f290s) != null && c0064a.onTouch(this, motionEvent)) {
            return true;
        }
        return super.onTouchEvent(motionEvent);
    }

    public void setCheckable(boolean z5) {
    }

    public void setChecked(boolean z5) {
    }

    public void setExpandedFormat(boolean z5) {
        if (this.f293v != z5) {
            this.f293v = z5;
            C0072g c0072g = this.f286o;
            if (c0072g != null) {
                c0072g.f419n.m194q();
            }
        }
    }

    public void setIcon(Drawable drawable) {
        this.f288q = drawable;
        if (drawable != null) {
            int intrinsicWidth = drawable.getIntrinsicWidth();
            int intrinsicHeight = drawable.getIntrinsicHeight();
            int i6 = this.f296y;
            if (intrinsicWidth > i6) {
                intrinsicHeight = (int) (intrinsicHeight * (i6 / intrinsicWidth));
                intrinsicWidth = i6;
            }
            if (intrinsicHeight > i6) {
                intrinsicWidth = (int) (intrinsicWidth * (i6 / intrinsicHeight));
            } else {
                i6 = intrinsicHeight;
            }
            drawable.setBounds(0, 0, intrinsicWidth, i6);
        }
        setCompoundDrawables(drawable, null, null, null);
        m142f();
    }

    public void setItemInvoker(C0070e.b bVar) {
        this.f289r = bVar;
    }

    @Override // android.widget.TextView, android.view.View
    public final void setPadding(int i6, int i7, int i8, int i9) {
        this.f295x = i6;
        super.setPadding(i6, i7, i8, i9);
    }

    public void setPopupCallback(AbstractC0065b abstractC0065b) {
        this.f291t = abstractC0065b;
    }

    public void setTitle(CharSequence charSequence) {
        this.f287p = charSequence;
        m142f();
    }
}
