package androidx.appcompat.view.menu;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.ListAdapter;
import androidx.appcompat.app.AlertController;
import androidx.appcompat.app.DialogC0063b;
import androidx.appcompat.view.menu.InterfaceC0074i;
import androidx.appcompat.view.menu.InterfaceC0075j;
import com.liaoyuan.aicast.R;
import java.util.ArrayList;
import java.util.Objects;

/* renamed from: androidx.appcompat.view.menu.c */
/* loaded from: classes.dex */
public final class C0068c implements InterfaceC0074i, AdapterView.OnItemClickListener {

    /* renamed from: j */
    public Context f361j;

    /* renamed from: k */
    public LayoutInflater f362k;

    /* renamed from: l */
    public C0070e f363l;

    /* renamed from: m */
    public ExpandedMenuView f364m;

    /* renamed from: n */
    public InterfaceC0074i.a f365n;

    /* renamed from: o */
    public a f366o;

    /* renamed from: androidx.appcompat.view.menu.c$a */
    public class a extends BaseAdapter {

        /* renamed from: j */
        public int f367j = -1;

        public a() {
            m172b();
        }

        /* renamed from: b */
        public final void m172b() {
            C0070e c0070e = C0068c.this.f363l;
            C0072g c0072g = c0070e.f397v;
            if (c0072g != null) {
                c0070e.m187j();
                ArrayList<C0072g> arrayList = c0070e.f385j;
                int size = arrayList.size();
                for (int i6 = 0; i6 < size; i6++) {
                    if (arrayList.get(i6) == c0072g) {
                        this.f367j = i6;
                        return;
                    }
                }
            }
            this.f367j = -1;
        }

        @Override // android.widget.Adapter
        /* renamed from: c, reason: merged with bridge method [inline-methods] */
        public final C0072g getItem(int i6) {
            C0070e c0070e = C0068c.this.f363l;
            c0070e.m187j();
            ArrayList<C0072g> arrayList = c0070e.f385j;
            Objects.requireNonNull(C0068c.this);
            int i7 = i6 + 0;
            int i8 = this.f367j;
            if (i8 >= 0 && i7 >= i8) {
                i7++;
            }
            return arrayList.get(i7);
        }

        @Override // android.widget.Adapter
        public final int getCount() {
            C0070e c0070e = C0068c.this.f363l;
            c0070e.m187j();
            int size = c0070e.f385j.size();
            Objects.requireNonNull(C0068c.this);
            int i6 = size + 0;
            return this.f367j < 0 ? i6 : i6 - 1;
        }

        @Override // android.widget.Adapter
        public final long getItemId(int i6) {
            return i6;
        }

        @Override // android.widget.Adapter
        public final View getView(int i6, View view, ViewGroup viewGroup) {
            if (view == null) {
                view = C0068c.this.f362k.inflate(R.layout.abc_list_menu_item_layout, viewGroup, false);
            }
            ((InterfaceC0075j.a) view).mo138b(getItem(i6));
            return view;
        }

        @Override // android.widget.BaseAdapter
        public final void notifyDataSetChanged() {
            m172b();
            super.notifyDataSetChanged();
        }
    }

    public C0068c(Context context) {
        this.f361j = context;
        this.f362k = LayoutInflater.from(context);
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: a */
    public final void mo152a(C0070e c0070e, boolean z5) {
        InterfaceC0074i.a aVar = this.f365n;
        if (aVar != null) {
            aVar.mo206a(c0070e, z5);
        }
    }

    /* renamed from: b */
    public final ListAdapter m170b() {
        if (this.f366o == null) {
            this.f366o = new a();
        }
        return this.f366o;
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: c */
    public final boolean mo149c(C0072g c0072g) {
        return false;
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: d */
    public final boolean mo154d() {
        return false;
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: e */
    public final void mo171e(Context context, C0070e c0070e) {
        if (this.f361j != null) {
            this.f361j = context;
            if (this.f362k == null) {
                this.f362k = LayoutInflater.from(context);
            }
        }
        this.f363l = c0070e;
        a aVar = this.f366o;
        if (aVar != null) {
            aVar.notifyDataSetChanged();
        }
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: g */
    public final void mo156g() {
        a aVar = this.f366o;
        if (aVar != null) {
            aVar.notifyDataSetChanged();
        }
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: h */
    public final void mo150h(InterfaceC0074i.a aVar) {
        this.f365n = aVar;
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: i */
    public final boolean mo151i(C0072g c0072g) {
        return false;
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: j */
    public final boolean mo157j(SubMenuC0077l subMenuC0077l) {
        if (!subMenuC0077l.hasVisibleItems()) {
            return false;
        }
        DialogInterfaceOnKeyListenerC0071f dialogInterfaceOnKeyListenerC0071f = new DialogInterfaceOnKeyListenerC0071f(subMenuC0077l);
        Context context = subMenuC0077l.f376a;
        DialogC0063b.a aVar = new DialogC0063b.a(context, DialogC0063b.m134e(context, 0));
        C0068c c0068c = new C0068c(aVar.f284a.f264a);
        dialogInterfaceOnKeyListenerC0071f.f402l = c0068c;
        c0068c.f365n = dialogInterfaceOnKeyListenerC0071f;
        dialogInterfaceOnKeyListenerC0071f.f400j.m179b(c0068c);
        ListAdapter m170b = dialogInterfaceOnKeyListenerC0071f.f402l.m170b();
        AlertController.C0059b c0059b = aVar.f284a;
        c0059b.f276m = m170b;
        c0059b.f277n = dialogInterfaceOnKeyListenerC0071f;
        View view = subMenuC0077l.f390o;
        if (view != null) {
            c0059b.f268e = view;
        } else {
            c0059b.f266c = subMenuC0077l.f389n;
            c0059b.f267d = subMenuC0077l.f388m;
        }
        c0059b.f274k = dialogInterfaceOnKeyListenerC0071f;
        DialogC0063b mo135a = aVar.mo135a();
        dialogInterfaceOnKeyListenerC0071f.f401k = mo135a;
        mo135a.setOnDismissListener(dialogInterfaceOnKeyListenerC0071f);
        WindowManager.LayoutParams attributes = dialogInterfaceOnKeyListenerC0071f.f401k.getWindow().getAttributes();
        attributes.type = 1003;
        attributes.flags |= 131072;
        dialogInterfaceOnKeyListenerC0071f.f401k.show();
        InterfaceC0074i.a aVar2 = this.f365n;
        if (aVar2 == null) {
            return true;
        }
        aVar2.mo207b(subMenuC0077l);
        return true;
    }

    @Override // android.widget.AdapterView.OnItemClickListener
    public final void onItemClick(AdapterView<?> adapterView, View view, int i6, long j6) {
        this.f363l.m197t(this.f366o.getItem(i6), this, 0);
    }
}
