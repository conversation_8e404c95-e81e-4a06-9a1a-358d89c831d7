package androidx.appcompat.view.menu;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;
import androidx.appcompat.view.menu.InterfaceC0075j;
import androidx.appcompat.widget.C0123c1;
import com.liaoyuan.aicast.R;
import java.util.Objects;
import java.util.WeakHashMap;
import p008b0.C0385m;
import p029e0.C0766p;
import p029e0.C0769s;

/* loaded from: classes.dex */
public class ListMenuItemView extends LinearLayout implements InterfaceC0075j.a, AbsListView.SelectionBoundsAdjuster {

    /* renamed from: j */
    public C0072g f300j;

    /* renamed from: k */
    public ImageView f301k;

    /* renamed from: l */
    public RadioButton f302l;

    /* renamed from: m */
    public TextView f303m;

    /* renamed from: n */
    public CheckBox f304n;

    /* renamed from: o */
    public TextView f305o;

    /* renamed from: p */
    public ImageView f306p;

    /* renamed from: q */
    public ImageView f307q;

    /* renamed from: r */
    public LinearLayout f308r;

    /* renamed from: s */
    public Drawable f309s;

    /* renamed from: t */
    public int f310t;

    /* renamed from: u */
    public Context f311u;

    /* renamed from: v */
    public boolean f312v;

    /* renamed from: w */
    public Drawable f313w;

    /* renamed from: x */
    public boolean f314x;

    /* renamed from: y */
    public LayoutInflater f315y;

    /* renamed from: z */
    public boolean f316z;

    public ListMenuItemView(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        C0123c1 m336q = C0123c1.m336q(getContext(), attributeSet, C0385m.f2304A, R.attr.listMenuViewStyle);
        this.f309s = m336q.m343g(5);
        this.f310t = m336q.m348l(1, -1);
        this.f312v = m336q.m337a(7, false);
        this.f311u = context;
        this.f313w = m336q.m343g(8);
        TypedArray obtainStyledAttributes = context.getTheme().obtainStyledAttributes(null, new int[]{android.R.attr.divider}, R.attr.dropDownListViewStyle, 0);
        this.f314x = obtainStyledAttributes.hasValue(0);
        m336q.m352r();
        obtainStyledAttributes.recycle();
    }

    private LayoutInflater getInflater() {
        if (this.f315y == null) {
            this.f315y = LayoutInflater.from(getContext());
        }
        return this.f315y;
    }

    private void setSubMenuArrowVisible(boolean z5) {
        ImageView imageView = this.f306p;
        if (imageView != null) {
            imageView.setVisibility(z5 ? 0 : 8);
        }
    }

    /* renamed from: a */
    public final void m147a() {
        CheckBox checkBox = (CheckBox) getInflater().inflate(R.layout.abc_list_menu_item_checkbox, (ViewGroup) this, false);
        this.f304n = checkBox;
        LinearLayout linearLayout = this.f308r;
        if (linearLayout != null) {
            linearLayout.addView(checkBox, -1);
        } else {
            addView(checkBox, -1);
        }
    }

    @Override // android.widget.AbsListView.SelectionBoundsAdjuster
    public final void adjustListItemSelectionBounds(Rect rect) {
        ImageView imageView = this.f307q;
        if (imageView == null || imageView.getVisibility() != 0) {
            return;
        }
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) this.f307q.getLayoutParams();
        rect.top = this.f307q.getHeight() + layoutParams.topMargin + layoutParams.bottomMargin + rect.top;
    }

    /* JADX WARN: Code restructure failed: missing block: B:16:0x0047, code lost:
    
        if ((r0.f419n.mo193p() && r0.m212e() != 0) != false) goto L23;
     */
    @Override // androidx.appcompat.view.menu.InterfaceC0075j.a
    /* renamed from: b */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void mo138b(androidx.appcompat.view.menu.C0072g r11) {
        /*
            Method dump skipped, instructions count: 286
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.view.menu.ListMenuItemView.mo138b(androidx.appcompat.view.menu.g):void");
    }

    /* renamed from: c */
    public final void m148c() {
        RadioButton radioButton = (RadioButton) getInflater().inflate(R.layout.abc_list_menu_item_radio, (ViewGroup) this, false);
        this.f302l = radioButton;
        LinearLayout linearLayout = this.f308r;
        if (linearLayout != null) {
            linearLayout.addView(radioButton, -1);
        } else {
            addView(radioButton, -1);
        }
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0075j.a
    public C0072g getItemData() {
        return this.f300j;
    }

    @Override // android.view.View
    public final void onFinishInflate() {
        super.onFinishInflate();
        Drawable drawable = this.f309s;
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        setBackground(drawable);
        TextView textView = (TextView) findViewById(R.id.title);
        this.f303m = textView;
        int i6 = this.f310t;
        if (i6 != -1) {
            textView.setTextAppearance(this.f311u, i6);
        }
        this.f305o = (TextView) findViewById(R.id.shortcut);
        ImageView imageView = (ImageView) findViewById(R.id.submenuarrow);
        this.f306p = imageView;
        if (imageView != null) {
            imageView.setImageDrawable(this.f313w);
        }
        this.f307q = (ImageView) findViewById(R.id.group_divider);
        this.f308r = (LinearLayout) findViewById(R.id.content);
    }

    @Override // android.widget.LinearLayout, android.view.View
    public final void onMeasure(int i6, int i7) {
        if (this.f301k != null && this.f312v) {
            ViewGroup.LayoutParams layoutParams = getLayoutParams();
            LinearLayout.LayoutParams layoutParams2 = (LinearLayout.LayoutParams) this.f301k.getLayoutParams();
            int i8 = layoutParams.height;
            if (i8 > 0 && layoutParams2.width <= 0) {
                layoutParams2.width = i8;
            }
        }
        super.onMeasure(i6, i7);
    }

    public void setCheckable(boolean z5) {
        CompoundButton compoundButton;
        View view;
        if (!z5 && this.f302l == null && this.f304n == null) {
            return;
        }
        if (this.f300j.m215h()) {
            if (this.f302l == null) {
                m148c();
            }
            compoundButton = this.f302l;
            view = this.f304n;
        } else {
            if (this.f304n == null) {
                m147a();
            }
            compoundButton = this.f304n;
            view = this.f302l;
        }
        if (z5) {
            compoundButton.setChecked(this.f300j.isChecked());
            if (compoundButton.getVisibility() != 0) {
                compoundButton.setVisibility(0);
            }
            if (view == null || view.getVisibility() == 8) {
                return;
            }
            view.setVisibility(8);
            return;
        }
        CheckBox checkBox = this.f304n;
        if (checkBox != null) {
            checkBox.setVisibility(8);
        }
        RadioButton radioButton = this.f302l;
        if (radioButton != null) {
            radioButton.setVisibility(8);
        }
    }

    public void setChecked(boolean z5) {
        CompoundButton compoundButton;
        if (this.f300j.m215h()) {
            if (this.f302l == null) {
                m148c();
            }
            compoundButton = this.f302l;
        } else {
            if (this.f304n == null) {
                m147a();
            }
            compoundButton = this.f304n;
        }
        compoundButton.setChecked(z5);
    }

    public void setForceShowIcon(boolean z5) {
        this.f316z = z5;
        this.f312v = z5;
    }

    public void setGroupDividerEnabled(boolean z5) {
        ImageView imageView = this.f307q;
        if (imageView != null) {
            imageView.setVisibility((this.f314x || !z5) ? 8 : 0);
        }
    }

    public void setIcon(Drawable drawable) {
        Objects.requireNonNull(this.f300j.f419n);
        boolean z5 = this.f316z;
        if (z5 || this.f312v) {
            ImageView imageView = this.f301k;
            if (imageView == null && drawable == null && !this.f312v) {
                return;
            }
            if (imageView == null) {
                ImageView imageView2 = (ImageView) getInflater().inflate(R.layout.abc_list_menu_item_icon, (ViewGroup) this, false);
                this.f301k = imageView2;
                LinearLayout linearLayout = this.f308r;
                if (linearLayout != null) {
                    linearLayout.addView(imageView2, 0);
                } else {
                    addView(imageView2, 0);
                }
            }
            if (drawable == null && !this.f312v) {
                this.f301k.setVisibility(8);
                return;
            }
            ImageView imageView3 = this.f301k;
            if (!z5) {
                drawable = null;
            }
            imageView3.setImageDrawable(drawable);
            if (this.f301k.getVisibility() != 0) {
                this.f301k.setVisibility(0);
            }
        }
    }

    public void setTitle(CharSequence charSequence) {
        int i6;
        TextView textView;
        if (charSequence != null) {
            this.f303m.setText(charSequence);
            if (this.f303m.getVisibility() == 0) {
                return;
            }
            textView = this.f303m;
            i6 = 0;
        } else {
            i6 = 8;
            if (this.f303m.getVisibility() == 8) {
                return;
            } else {
                textView = this.f303m;
            }
        }
        textView.setVisibility(i6);
    }
}
