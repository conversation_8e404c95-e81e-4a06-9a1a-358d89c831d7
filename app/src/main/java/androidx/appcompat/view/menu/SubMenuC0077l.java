package androidx.appcompat.view.menu;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;
import androidx.appcompat.view.menu.C0070e;

/* renamed from: androidx.appcompat.view.menu.l */
/* loaded from: classes.dex */
public final class SubMenuC0077l extends C0070e implements SubMenu {

    /* renamed from: A */
    public C0072g f468A;

    /* renamed from: z */
    public C0070e f469z;

    public SubMenuC0077l(Context context, C0070e c0070e, C0072g c0072g) {
        super(context);
        this.f469z = c0070e;
        this.f468A = c0072g;
    }

    @Override // androidx.appcompat.view.menu.C0070e
    /* renamed from: e */
    public final boolean mo182e(C0072g c0072g) {
        return this.f469z.mo182e(c0072g);
    }

    @Override // androidx.appcompat.view.menu.C0070e
    /* renamed from: f */
    public final boolean mo183f(C0070e c0070e, MenuItem menuItem) {
        return super.mo183f(c0070e, menuItem) || this.f469z.mo183f(c0070e, menuItem);
    }

    @Override // androidx.appcompat.view.menu.C0070e
    /* renamed from: g */
    public final boolean mo184g(C0072g c0072g) {
        return this.f469z.mo184g(c0072g);
    }

    @Override // android.view.SubMenu
    public final MenuItem getItem() {
        return this.f468A;
    }

    @Override // androidx.appcompat.view.menu.C0070e
    /* renamed from: k */
    public final String mo188k() {
        C0072g c0072g = this.f468A;
        int i6 = c0072g != null ? c0072g.f406a : 0;
        if (i6 == 0) {
            return null;
        }
        return "android:menu:actionviewstates:" + i6;
    }

    @Override // androidx.appcompat.view.menu.C0070e
    /* renamed from: l */
    public final C0070e mo189l() {
        return this.f469z.mo189l();
    }

    @Override // androidx.appcompat.view.menu.C0070e
    /* renamed from: n */
    public final boolean mo191n() {
        return this.f469z.mo191n();
    }

    @Override // androidx.appcompat.view.menu.C0070e
    /* renamed from: o */
    public final boolean mo192o() {
        return this.f469z.mo192o();
    }

    @Override // androidx.appcompat.view.menu.C0070e
    /* renamed from: p */
    public final boolean mo193p() {
        return this.f469z.mo193p();
    }

    @Override // androidx.appcompat.view.menu.C0070e, android.view.Menu
    public final void setGroupDividerEnabled(boolean z5) {
        this.f469z.setGroupDividerEnabled(z5);
    }

    @Override // android.view.SubMenu
    public final SubMenu setHeaderIcon(int i6) {
        m203z(0, null, i6, null, null);
        return this;
    }

    @Override // android.view.SubMenu
    public final SubMenu setHeaderTitle(int i6) {
        m203z(i6, null, 0, null, null);
        return this;
    }

    @Override // android.view.SubMenu
    public final SubMenu setHeaderView(View view) {
        m203z(0, null, 0, null, view);
        return this;
    }

    @Override // android.view.SubMenu
    public final SubMenu setIcon(int i6) {
        this.f468A.setIcon(i6);
        return this;
    }

    @Override // android.view.SubMenu
    public final SubMenu setIcon(Drawable drawable) {
        this.f468A.setIcon(drawable);
        return this;
    }

    @Override // androidx.appcompat.view.menu.C0070e, android.view.Menu
    public final void setQwertyMode(boolean z5) {
        this.f469z.setQwertyMode(z5);
    }

    @Override // androidx.appcompat.view.menu.C0070e
    /* renamed from: y */
    public final void mo202y(C0070e.a aVar) {
        this.f469z.mo202y(aVar);
    }

    @Override // android.view.SubMenu
    public final SubMenu setHeaderIcon(Drawable drawable) {
        m203z(0, null, 0, drawable, null);
        return this;
    }

    @Override // android.view.SubMenu
    public final SubMenu setHeaderTitle(CharSequence charSequence) {
        m203z(0, charSequence, 0, null, null);
        return this;
    }
}
