package androidx.appcompat.view.menu;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Rect;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.FrameLayout;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.TextView;
import androidx.appcompat.view.menu.InterfaceC0074i;
import androidx.appcompat.widget.C0137h0;
import androidx.appcompat.widget.C0155o0;
import com.liaoyuan.aicast.R;
import java.util.Objects;
import p049h.AbstractC0896d;

/* renamed from: androidx.appcompat.view.menu.k */
/* loaded from: classes.dex */
public final class ViewOnKeyListenerC0076k extends AbstractC0896d implements PopupWindow.OnDismissListener, View.OnKeyListener {

    /* renamed from: A */
    public boolean f446A;

    /* renamed from: B */
    public int f447B;

    /* renamed from: D */
    public boolean f449D;

    /* renamed from: k */
    public final Context f450k;

    /* renamed from: l */
    public final C0070e f451l;

    /* renamed from: m */
    public final C0069d f452m;

    /* renamed from: n */
    public final boolean f453n;

    /* renamed from: o */
    public final int f454o;

    /* renamed from: p */
    public final int f455p;

    /* renamed from: q */
    public final int f456q;

    /* renamed from: r */
    public final C0155o0 f457r;

    /* renamed from: u */
    public PopupWindow.OnDismissListener f460u;

    /* renamed from: v */
    public View f461v;

    /* renamed from: w */
    public View f462w;

    /* renamed from: x */
    public InterfaceC0074i.a f463x;

    /* renamed from: y */
    public ViewTreeObserver f464y;

    /* renamed from: z */
    public boolean f465z;

    /* renamed from: s */
    public final a f458s = new a();

    /* renamed from: t */
    public final b f459t = new b();

    /* renamed from: C */
    public int f448C = 0;

    /* renamed from: androidx.appcompat.view.menu.k$a */
    public class a implements ViewTreeObserver.OnGlobalLayoutListener {
        public a() {
        }

        @Override // android.view.ViewTreeObserver.OnGlobalLayoutListener
        public final void onGlobalLayout() {
            if (ViewOnKeyListenerC0076k.this.mo153b()) {
                ViewOnKeyListenerC0076k viewOnKeyListenerC0076k = ViewOnKeyListenerC0076k.this;
                if (viewOnKeyListenerC0076k.f457r.f888G) {
                    return;
                }
                View view = viewOnKeyListenerC0076k.f462w;
                if (view == null || !view.isShown()) {
                    ViewOnKeyListenerC0076k.this.dismiss();
                } else {
                    ViewOnKeyListenerC0076k.this.f457r.mo155f();
                }
            }
        }
    }

    /* renamed from: androidx.appcompat.view.menu.k$b */
    public class b implements View.OnAttachStateChangeListener {
        public b() {
        }

        @Override // android.view.View.OnAttachStateChangeListener
        public final void onViewAttachedToWindow(View view) {
        }

        @Override // android.view.View.OnAttachStateChangeListener
        public final void onViewDetachedFromWindow(View view) {
            ViewTreeObserver viewTreeObserver = ViewOnKeyListenerC0076k.this.f464y;
            if (viewTreeObserver != null) {
                if (!viewTreeObserver.isAlive()) {
                    ViewOnKeyListenerC0076k.this.f464y = view.getViewTreeObserver();
                }
                ViewOnKeyListenerC0076k viewOnKeyListenerC0076k = ViewOnKeyListenerC0076k.this;
                viewOnKeyListenerC0076k.f464y.removeGlobalOnLayoutListener(viewOnKeyListenerC0076k.f458s);
            }
            view.removeOnAttachStateChangeListener(this);
        }
    }

    public ViewOnKeyListenerC0076k(Context context, C0070e c0070e, View view, int i6, int i7, boolean z5) {
        this.f450k = context;
        this.f451l = c0070e;
        this.f453n = z5;
        this.f452m = new C0069d(c0070e, LayoutInflater.from(context), z5, R.layout.abc_popup_menu_item_layout);
        this.f455p = i6;
        this.f456q = i7;
        Resources resources = context.getResources();
        this.f454o = Math.max(resources.getDisplayMetrics().widthPixels / 2, resources.getDimensionPixelSize(R.dimen.abc_config_prefDialogWidth));
        this.f461v = view;
        this.f457r = new C0155o0(context, i6, i7);
        c0070e.m180c(this, context);
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: a */
    public final void mo152a(C0070e c0070e, boolean z5) {
        if (c0070e != this.f451l) {
            return;
        }
        dismiss();
        InterfaceC0074i.a aVar = this.f463x;
        if (aVar != null) {
            aVar.mo206a(c0070e, z5);
        }
    }

    @Override // p049h.InterfaceC0898f
    /* renamed from: b */
    public final boolean mo153b() {
        return !this.f465z && this.f457r.mo153b();
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: d */
    public final boolean mo154d() {
        return false;
    }

    @Override // p049h.InterfaceC0898f
    public final void dismiss() {
        if (mo153b()) {
            this.f457r.dismiss();
        }
    }

    @Override // p049h.InterfaceC0898f
    /* renamed from: f */
    public final void mo155f() {
        View view;
        boolean z5 = true;
        if (!mo153b()) {
            if (this.f465z || (view = this.f461v) == null) {
                z5 = false;
            } else {
                this.f462w = view;
                this.f457r.m436u(this);
                C0155o0 c0155o0 = this.f457r;
                c0155o0.f905y = this;
                c0155o0.m435t();
                View view2 = this.f462w;
                boolean z6 = this.f464y == null;
                ViewTreeObserver viewTreeObserver = view2.getViewTreeObserver();
                this.f464y = viewTreeObserver;
                if (z6) {
                    viewTreeObserver.addOnGlobalLayoutListener(this.f458s);
                }
                view2.addOnAttachStateChangeListener(this.f459t);
                C0155o0 c0155o02 = this.f457r;
                c0155o02.f904x = view2;
                c0155o02.f901u = this.f448C;
                if (!this.f446A) {
                    this.f447B = AbstractC0896d.m2442m(this.f452m, this.f450k, this.f454o);
                    this.f446A = true;
                }
                this.f457r.m433r(this.f447B);
                this.f457r.m434s();
                C0155o0 c0155o03 = this.f457r;
                Rect rect = this.f4537j;
                Objects.requireNonNull(c0155o03);
                c0155o03.f887F = rect != null ? new Rect(rect) : null;
                this.f457r.mo155f();
                C0137h0 c0137h0 = this.f457r.f892l;
                c0137h0.setOnKeyListener(this);
                if (this.f449D && this.f451l.f388m != null) {
                    FrameLayout frameLayout = (FrameLayout) LayoutInflater.from(this.f450k).inflate(R.layout.abc_popup_menu_header_item_layout, (ViewGroup) c0137h0, false);
                    TextView textView = (TextView) frameLayout.findViewById(android.R.id.title);
                    if (textView != null) {
                        textView.setText(this.f451l.f388m);
                    }
                    frameLayout.setEnabled(false);
                    c0137h0.addHeaderView(frameLayout, null, false);
                }
                this.f457r.mo431o(this.f452m);
                this.f457r.mo155f();
            }
        }
        if (!z5) {
            throw new IllegalStateException("StandardMenuPopup cannot be used without an anchor");
        }
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: g */
    public final void mo156g() {
        this.f446A = false;
        C0069d c0069d = this.f452m;
        if (c0069d != null) {
            c0069d.notifyDataSetChanged();
        }
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: h */
    public final void mo150h(InterfaceC0074i.a aVar) {
        this.f463x = aVar;
    }

    /* JADX WARN: Removed duplicated region for block: B:16:0x0071  */
    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: j */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean mo157j(androidx.appcompat.view.menu.SubMenuC0077l r10) {
        /*
            r9 = this;
            boolean r0 = r10.hasVisibleItems()
            r1 = 0
            if (r0 == 0) goto L79
            androidx.appcompat.view.menu.h r0 = new androidx.appcompat.view.menu.h
            android.content.Context r3 = r9.f450k
            android.view.View r5 = r9.f462w
            boolean r6 = r9.f453n
            int r7 = r9.f455p
            int r8 = r9.f456q
            r2 = r0
            r4 = r10
            r2.<init>(r3, r4, r5, r6, r7, r8)
            androidx.appcompat.view.menu.i$a r2 = r9.f463x
            r0.m222d(r2)
            boolean r2 = p049h.AbstractC0896d.m2443u(r10)
            r0.f440h = r2
            h.d r3 = r0.f442j
            if (r3 == 0) goto L2a
            r3.mo161o(r2)
        L2a:
            android.widget.PopupWindow$OnDismissListener r2 = r9.f460u
            r0.f443k = r2
            r2 = 0
            r9.f460u = r2
            androidx.appcompat.view.menu.e r2 = r9.f451l
            r2.m181d(r1)
            androidx.appcompat.widget.o0 r2 = r9.f457r
            int r3 = r2.f895o
            boolean r4 = r2.f898r
            if (r4 != 0) goto L40
            r2 = r1
            goto L42
        L40:
            int r2 = r2.f896p
        L42:
            int r4 = r9.f448C
            android.view.View r5 = r9.f461v
            java.util.WeakHashMap<android.view.View, e0.s> r6 = p029e0.C0766p.f4041a
            int r5 = r5.getLayoutDirection()
            int r4 = android.view.Gravity.getAbsoluteGravity(r4, r5)
            r4 = r4 & 7
            r5 = 5
            if (r4 != r5) goto L5c
            android.view.View r4 = r9.f461v
            int r4 = r4.getWidth()
            int r3 = r3 + r4
        L5c:
            boolean r4 = r0.m220b()
            r5 = 1
            if (r4 == 0) goto L65
        L63:
            r0 = r5
            goto L6f
        L65:
            android.view.View r4 = r0.f438f
            if (r4 != 0) goto L6b
            r0 = r1
            goto L6f
        L6b:
            r0.m224f(r3, r2, r5, r5)
            goto L63
        L6f:
            if (r0 == 0) goto L79
            androidx.appcompat.view.menu.i$a r0 = r9.f463x
            if (r0 == 0) goto L78
            r0.mo207b(r10)
        L78:
            return r5
        L79:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.view.menu.ViewOnKeyListenerC0076k.mo157j(androidx.appcompat.view.menu.l):boolean");
    }

    @Override // p049h.InterfaceC0898f
    /* renamed from: k */
    public final ListView mo158k() {
        return this.f457r.f892l;
    }

    @Override // p049h.AbstractC0896d
    /* renamed from: l */
    public final void mo159l(C0070e c0070e) {
    }

    @Override // p049h.AbstractC0896d
    /* renamed from: n */
    public final void mo160n(View view) {
        this.f461v = view;
    }

    @Override // p049h.AbstractC0896d
    /* renamed from: o */
    public final void mo161o(boolean z5) {
        this.f452m.f371l = z5;
    }

    @Override // android.widget.PopupWindow.OnDismissListener
    public final void onDismiss() {
        this.f465z = true;
        this.f451l.m181d(true);
        ViewTreeObserver viewTreeObserver = this.f464y;
        if (viewTreeObserver != null) {
            if (!viewTreeObserver.isAlive()) {
                this.f464y = this.f462w.getViewTreeObserver();
            }
            this.f464y.removeGlobalOnLayoutListener(this.f458s);
            this.f464y = null;
        }
        this.f462w.removeOnAttachStateChangeListener(this.f459t);
        PopupWindow.OnDismissListener onDismissListener = this.f460u;
        if (onDismissListener != null) {
            onDismissListener.onDismiss();
        }
    }

    @Override // android.view.View.OnKeyListener
    public final boolean onKey(View view, int i6, KeyEvent keyEvent) {
        if (keyEvent.getAction() != 1 || i6 != 82) {
            return false;
        }
        dismiss();
        return true;
    }

    @Override // p049h.AbstractC0896d
    /* renamed from: p */
    public final void mo162p(int i6) {
        this.f448C = i6;
    }

    @Override // p049h.AbstractC0896d
    /* renamed from: q */
    public final void mo163q(int i6) {
        this.f457r.f895o = i6;
    }

    @Override // p049h.AbstractC0896d
    /* renamed from: r */
    public final void mo164r(PopupWindow.OnDismissListener onDismissListener) {
        this.f460u = onDismissListener;
    }

    @Override // p049h.AbstractC0896d
    /* renamed from: s */
    public final void mo165s(boolean z5) {
        this.f449D = z5;
    }

    @Override // p049h.AbstractC0896d
    /* renamed from: t */
    public final void mo166t(int i6) {
        this.f457r.m430n(i6);
    }
}
