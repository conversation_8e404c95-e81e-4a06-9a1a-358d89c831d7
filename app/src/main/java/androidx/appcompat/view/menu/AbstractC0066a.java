package androidx.appcompat.view.menu;

import android.content.Context;
import android.view.LayoutInflater;
import androidx.appcompat.view.menu.InterfaceC0074i;
import com.liaoyuan.aicast.R;

/* renamed from: androidx.appcompat.view.menu.a */
/* loaded from: classes.dex */
public abstract class AbstractC0066a implements InterfaceC0074i {

    /* renamed from: j */
    public Context f317j;

    /* renamed from: k */
    public Context f318k;

    /* renamed from: l */
    public C0070e f319l;

    /* renamed from: m */
    public LayoutInflater f320m;

    /* renamed from: n */
    public InterfaceC0074i.a f321n;

    /* renamed from: o */
    public int f322o = R.layout.abc_action_menu_layout;

    /* renamed from: p */
    public int f323p = R.layout.abc_action_menu_item_layout;

    /* renamed from: q */
    public InterfaceC0075j f324q;

    public AbstractC0066a(Context context) {
        this.f317j = context;
        this.f320m = LayoutInflater.from(context);
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: c */
    public final boolean mo149c(C0072g c0072g) {
        return false;
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: h */
    public final void mo150h(InterfaceC0074i.a aVar) {
        this.f321n = aVar;
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: i */
    public final boolean mo151i(C0072g c0072g) {
        return false;
    }
}
