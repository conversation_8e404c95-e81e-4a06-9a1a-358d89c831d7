package androidx.appcompat.view.menu;

import android.content.Context;
import android.graphics.Point;
import android.graphics.Rect;
import android.view.Display;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.PopupWindow;
import androidx.appcompat.view.menu.InterfaceC0074i;
import com.liaoyuan.aicast.R;
import java.util.WeakHashMap;
import p029e0.C0766p;
import p029e0.C0769s;
import p049h.AbstractC0896d;

/* renamed from: androidx.appcompat.view.menu.h */
/* loaded from: classes.dex */
public class C0073h {

    /* renamed from: a */
    public final Context f433a;

    /* renamed from: b */
    public final C0070e f434b;

    /* renamed from: c */
    public final boolean f435c;

    /* renamed from: d */
    public final int f436d;

    /* renamed from: e */
    public final int f437e;

    /* renamed from: f */
    public View f438f;

    /* renamed from: h */
    public boolean f440h;

    /* renamed from: i */
    public InterfaceC0074i.a f441i;

    /* renamed from: j */
    public AbstractC0896d f442j;

    /* renamed from: k */
    public PopupWindow.OnDismissListener f443k;

    /* renamed from: g */
    public int f439g = 8388611;

    /* renamed from: l */
    public final a f444l = new a();

    /* renamed from: androidx.appcompat.view.menu.h$a */
    public class a implements PopupWindow.OnDismissListener {
        public a() {
        }

        @Override // android.widget.PopupWindow.OnDismissListener
        public final void onDismiss() {
            C0073h.this.mo221c();
        }
    }

    public C0073h(Context context, C0070e c0070e, View view, boolean z5, int i6, int i7) {
        this.f433a = context;
        this.f434b = c0070e;
        this.f438f = view;
        this.f435c = z5;
        this.f436d = i6;
        this.f437e = i7;
    }

    /* renamed from: a */
    public final AbstractC0896d m219a() {
        if (this.f442j == null) {
            Display defaultDisplay = ((WindowManager) this.f433a.getSystemService("window")).getDefaultDisplay();
            Point point = new Point();
            defaultDisplay.getRealSize(point);
            AbstractC0896d viewOnKeyListenerC0067b = Math.min(point.x, point.y) >= this.f433a.getResources().getDimensionPixelSize(R.dimen.abc_cascading_menus_min_smallest_width) ? new ViewOnKeyListenerC0067b(this.f433a, this.f438f, this.f436d, this.f437e, this.f435c) : new ViewOnKeyListenerC0076k(this.f433a, this.f434b, this.f438f, this.f436d, this.f437e, this.f435c);
            viewOnKeyListenerC0067b.mo159l(this.f434b);
            viewOnKeyListenerC0067b.mo164r(this.f444l);
            viewOnKeyListenerC0067b.mo160n(this.f438f);
            viewOnKeyListenerC0067b.mo150h(this.f441i);
            viewOnKeyListenerC0067b.mo161o(this.f440h);
            viewOnKeyListenerC0067b.mo162p(this.f439g);
            this.f442j = viewOnKeyListenerC0067b;
        }
        return this.f442j;
    }

    /* renamed from: b */
    public final boolean m220b() {
        AbstractC0896d abstractC0896d = this.f442j;
        return abstractC0896d != null && abstractC0896d.mo153b();
    }

    /* renamed from: c */
    public void mo221c() {
        this.f442j = null;
        PopupWindow.OnDismissListener onDismissListener = this.f443k;
        if (onDismissListener != null) {
            onDismissListener.onDismiss();
        }
    }

    /* renamed from: d */
    public final void m222d(InterfaceC0074i.a aVar) {
        this.f441i = aVar;
        AbstractC0896d abstractC0896d = this.f442j;
        if (abstractC0896d != null) {
            abstractC0896d.mo150h(aVar);
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:7:0x0013 A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:9:0x0014  */
    /* renamed from: e */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m223e() {
        /*
            r2 = this;
            boolean r0 = r2.m220b()
            if (r0 == 0) goto L7
            goto L10
        L7:
            android.view.View r0 = r2.f438f
            r1 = 0
            if (r0 != 0) goto Ld
            goto L11
        Ld:
            r2.m224f(r1, r1, r1, r1)
        L10:
            r1 = 1
        L11:
            if (r1 == 0) goto L14
            return
        L14:
            java.lang.IllegalStateException r0 = new java.lang.IllegalStateException
            java.lang.String r1 = "MenuPopupHelper cannot be used without an anchor"
            r0.<init>(r1)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.view.menu.C0073h.m223e():void");
    }

    /* renamed from: f */
    public final void m224f(int i6, int i7, boolean z5, boolean z6) {
        AbstractC0896d m219a = m219a();
        m219a.mo165s(z6);
        if (z5) {
            int i8 = this.f439g;
            View view = this.f438f;
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            if ((Gravity.getAbsoluteGravity(i8, view.getLayoutDirection()) & 7) == 5) {
                i6 -= this.f438f.getWidth();
            }
            m219a.mo163q(i6);
            m219a.mo166t(i7);
            int i9 = (int) ((this.f433a.getResources().getDisplayMetrics().density * 48.0f) / 2.0f);
            m219a.f4537j = new Rect(i6 - i9, i7 - i9, i6 + i9, i7 + i9);
        }
        m219a.mo155f();
    }
}
