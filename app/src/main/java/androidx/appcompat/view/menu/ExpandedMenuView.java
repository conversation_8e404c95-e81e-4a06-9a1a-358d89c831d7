package androidx.appcompat.view.menu;

import android.R;
import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ListView;
import androidx.appcompat.view.menu.C0070e;
import p028e.C0750a;

/* loaded from: classes.dex */
public final class ExpandedMenuView extends ListView implements C0070e.b, InterfaceC0075j, AdapterView.OnItemClickListener {

    /* renamed from: k */
    public static final int[] f298k = {R.attr.background, R.attr.divider};

    /* renamed from: j */
    public C0070e f299j;

    public ExpandedMenuView(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        int resourceId;
        int resourceId2;
        setOnItemClickListener(this);
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, f298k, R.attr.listViewStyle, 0);
        if (obtainStyledAttributes.hasValue(0)) {
            setBackgroundDrawable((!obtainStyledAttributes.hasValue(0) || (resourceId2 = obtainStyledAttributes.getResourceId(0, 0)) == 0) ? obtainStyledAttributes.getDrawable(0) : C0750a.m2138a(context, resourceId2));
        }
        if (obtainStyledAttributes.hasValue(1)) {
            setDivider((!obtainStyledAttributes.hasValue(1) || (resourceId = obtainStyledAttributes.getResourceId(1, 0)) == 0) ? obtainStyledAttributes.getDrawable(1) : C0750a.m2138a(context, resourceId));
        }
        obtainStyledAttributes.recycle();
    }

    @Override // androidx.appcompat.view.menu.C0070e.b
    /* renamed from: a */
    public final boolean mo145a(C0072g c0072g) {
        return this.f299j.m197t(c0072g, null, 0);
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0075j
    /* renamed from: c */
    public final void mo146c(C0070e c0070e) {
        this.f299j = c0070e;
    }

    public int getWindowAnimations() {
        return 0;
    }

    @Override // android.widget.ListView, android.widget.AbsListView, android.widget.AdapterView, android.view.ViewGroup, android.view.View
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        setChildrenDrawingCacheEnabled(false);
    }

    @Override // android.widget.AdapterView.OnItemClickListener
    public final void onItemClick(AdapterView adapterView, View view, int i6, long j6) {
        mo145a((C0072g) getAdapter().getItem(i6));
    }
}
