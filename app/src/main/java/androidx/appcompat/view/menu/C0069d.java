package androidx.appcompat.view.menu;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import androidx.appcompat.view.menu.InterfaceC0075j;
import java.util.ArrayList;

/* renamed from: androidx.appcompat.view.menu.d */
/* loaded from: classes.dex */
public final class C0069d extends BaseAdapter {

    /* renamed from: j */
    public C0070e f369j;

    /* renamed from: k */
    public int f370k = -1;

    /* renamed from: l */
    public boolean f371l;

    /* renamed from: m */
    public final boolean f372m;

    /* renamed from: n */
    public final LayoutInflater f373n;

    /* renamed from: o */
    public final int f374o;

    public C0069d(C0070e c0070e, LayoutInflater layoutInflater, boolean z5, int i6) {
        this.f372m = z5;
        this.f373n = layoutInflater;
        this.f369j = c0070e;
        this.f374o = i6;
        m174b();
    }

    /* renamed from: b */
    public final void m174b() {
        C0070e c0070e = this.f369j;
        C0072g c0072g = c0070e.f397v;
        if (c0072g != null) {
            c0070e.m187j();
            ArrayList<C0072g> arrayList = c0070e.f385j;
            int size = arrayList.size();
            for (int i6 = 0; i6 < size; i6++) {
                if (arrayList.get(i6) == c0072g) {
                    this.f370k = i6;
                    return;
                }
            }
        }
        this.f370k = -1;
    }

    @Override // android.widget.Adapter
    /* renamed from: c, reason: merged with bridge method [inline-methods] */
    public final C0072g getItem(int i6) {
        ArrayList<C0072g> m190m;
        if (this.f372m) {
            C0070e c0070e = this.f369j;
            c0070e.m187j();
            m190m = c0070e.f385j;
        } else {
            m190m = this.f369j.m190m();
        }
        int i7 = this.f370k;
        if (i7 >= 0 && i6 >= i7) {
            i6++;
        }
        return m190m.get(i6);
    }

    @Override // android.widget.Adapter
    public final int getCount() {
        ArrayList<C0072g> m190m;
        if (this.f372m) {
            C0070e c0070e = this.f369j;
            c0070e.m187j();
            m190m = c0070e.f385j;
        } else {
            m190m = this.f369j.m190m();
        }
        int i6 = this.f370k;
        int size = m190m.size();
        return i6 < 0 ? size : size - 1;
    }

    @Override // android.widget.Adapter
    public final long getItemId(int i6) {
        return i6;
    }

    @Override // android.widget.Adapter
    public final View getView(int i6, View view, ViewGroup viewGroup) {
        boolean z5 = false;
        if (view == null) {
            view = this.f373n.inflate(this.f374o, viewGroup, false);
        }
        int i7 = getItem(i6).f407b;
        int i8 = i6 - 1;
        int i9 = i8 >= 0 ? getItem(i8).f407b : i7;
        ListMenuItemView listMenuItemView = (ListMenuItemView) view;
        if (this.f369j.mo191n() && i7 != i9) {
            z5 = true;
        }
        listMenuItemView.setGroupDividerEnabled(z5);
        InterfaceC0075j.a aVar = (InterfaceC0075j.a) view;
        if (this.f371l) {
            listMenuItemView.setForceShowIcon(true);
        }
        aVar.mo138b(getItem(i6));
        return view;
    }

    @Override // android.widget.BaseAdapter
    public final void notifyDataSetChanged() {
        m174b();
        super.notifyDataSetChanged();
    }
}
