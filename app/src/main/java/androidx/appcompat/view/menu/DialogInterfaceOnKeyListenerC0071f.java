package androidx.appcompat.view.menu;

import android.content.DialogInterface;
import android.view.KeyEvent;
import android.view.View;
import android.view.Window;
import androidx.appcompat.app.DialogC0063b;
import androidx.appcompat.view.menu.C0068c;
import androidx.appcompat.view.menu.InterfaceC0074i;

/* renamed from: androidx.appcompat.view.menu.f */
/* loaded from: classes.dex */
public final class DialogInterfaceOnKeyListenerC0071f implements DialogInterface.OnKeyListener, DialogInterface.OnClickListener, DialogInterface.OnDismissListener, InterfaceC0074i.a {

    /* renamed from: j */
    public C0070e f400j;

    /* renamed from: k */
    public DialogC0063b f401k;

    /* renamed from: l */
    public C0068c f402l;

    public DialogInterfaceOnKeyListenerC0071f(C0070e c0070e) {
        this.f400j = c0070e;
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i.a
    /* renamed from: a */
    public final void mo206a(C0070e c0070e, boolean z5) {
        DialogC0063b dialogC0063b;
        if ((z5 || c0070e == this.f400j) && (dialogC0063b = this.f401k) != null) {
            dialogC0063b.dismiss();
        }
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i.a
    /* renamed from: b */
    public final boolean mo207b(C0070e c0070e) {
        return false;
    }

    @Override // android.content.DialogInterface.OnClickListener
    public final void onClick(DialogInterface dialogInterface, int i6) {
        this.f400j.m196s(((C0068c.a) this.f402l.m170b()).getItem(i6), 0);
    }

    @Override // android.content.DialogInterface.OnDismissListener
    public final void onDismiss(DialogInterface dialogInterface) {
        C0068c c0068c = this.f402l;
        C0070e c0070e = this.f400j;
        InterfaceC0074i.a aVar = c0068c.f365n;
        if (aVar != null) {
            aVar.mo206a(c0070e, true);
        }
    }

    @Override // android.content.DialogInterface.OnKeyListener
    public final boolean onKey(DialogInterface dialogInterface, int i6, KeyEvent keyEvent) {
        Window window;
        View decorView;
        KeyEvent.DispatcherState keyDispatcherState;
        View decorView2;
        KeyEvent.DispatcherState keyDispatcherState2;
        if (i6 == 82 || i6 == 4) {
            if (keyEvent.getAction() == 0 && keyEvent.getRepeatCount() == 0) {
                Window window2 = this.f401k.getWindow();
                if (window2 != null && (decorView2 = window2.getDecorView()) != null && (keyDispatcherState2 = decorView2.getKeyDispatcherState()) != null) {
                    keyDispatcherState2.startTracking(keyEvent, this);
                    return true;
                }
            } else if (keyEvent.getAction() == 1 && !keyEvent.isCanceled() && (window = this.f401k.getWindow()) != null && (decorView = window.getDecorView()) != null && (keyDispatcherState = decorView.getKeyDispatcherState()) != null && keyDispatcherState.isTracking(keyEvent)) {
                this.f400j.m181d(true);
                dialogInterface.dismiss();
                return true;
            }
        }
        return this.f400j.performShortcut(i6, keyEvent, 0);
    }
}
