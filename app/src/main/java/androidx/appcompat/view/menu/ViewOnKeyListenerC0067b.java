package androidx.appcompat.view.menu;

import android.content.Context;
import android.content.res.Resources;
import android.os.Handler;
import android.os.SystemClock;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.HeaderViewListAdapter;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.PopupWindow;
import androidx.appcompat.view.menu.InterfaceC0074i;
import androidx.appcompat.widget.C0155o0;
import androidx.appcompat.widget.InterfaceC0153n0;
import com.liaoyuan.aicast.R;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.WeakHashMap;
import p029e0.C0766p;
import p029e0.C0769s;
import p049h.AbstractC0896d;

/* renamed from: androidx.appcompat.view.menu.b */
/* loaded from: classes.dex */
public final class ViewOnKeyListenerC0067b extends AbstractC0896d implements View.OnKeyListener, PopupWindow.OnDismissListener {

    /* renamed from: A */
    public boolean f325A;

    /* renamed from: B */
    public boolean f326B;

    /* renamed from: C */
    public int f327C;

    /* renamed from: D */
    public int f328D;

    /* renamed from: F */
    public boolean f330F;

    /* renamed from: G */
    public InterfaceC0074i.a f331G;

    /* renamed from: H */
    public ViewTreeObserver f332H;

    /* renamed from: I */
    public PopupWindow.OnDismissListener f333I;

    /* renamed from: J */
    public boolean f334J;

    /* renamed from: k */
    public final Context f335k;

    /* renamed from: l */
    public final int f336l;

    /* renamed from: m */
    public final int f337m;

    /* renamed from: n */
    public final int f338n;

    /* renamed from: o */
    public final boolean f339o;

    /* renamed from: p */
    public final Handler f340p;

    /* renamed from: x */
    public View f348x;

    /* renamed from: y */
    public View f349y;

    /* renamed from: z */
    public int f350z;

    /* renamed from: q */
    public final List<C0070e> f341q = new ArrayList();

    /* renamed from: r */
    public final List<d> f342r = new ArrayList();

    /* renamed from: s */
    public final a f343s = new a();

    /* renamed from: t */
    public final b f344t = new b();

    /* renamed from: u */
    public final c f345u = new c();

    /* renamed from: v */
    public int f346v = 0;

    /* renamed from: w */
    public int f347w = 0;

    /* renamed from: E */
    public boolean f329E = false;

    /* renamed from: androidx.appcompat.view.menu.b$a */
    public class a implements ViewTreeObserver.OnGlobalLayoutListener {
        public a() {
        }

        /* JADX WARN: Type inference failed for: r0v16, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
        /* JADX WARN: Type inference failed for: r0v3, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
        /* JADX WARN: Type inference failed for: r0v6, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
        @Override // android.view.ViewTreeObserver.OnGlobalLayoutListener
        public final void onGlobalLayout() {
            if (!ViewOnKeyListenerC0067b.this.mo153b() || ViewOnKeyListenerC0067b.this.f342r.size() <= 0 || ((d) ViewOnKeyListenerC0067b.this.f342r.get(0)).f358a.f888G) {
                return;
            }
            View view = ViewOnKeyListenerC0067b.this.f349y;
            if (view == null || !view.isShown()) {
                ViewOnKeyListenerC0067b.this.dismiss();
                return;
            }
            Iterator it = ViewOnKeyListenerC0067b.this.f342r.iterator();
            while (it.hasNext()) {
                ((d) it.next()).f358a.mo155f();
            }
        }
    }

    /* renamed from: androidx.appcompat.view.menu.b$b */
    public class b implements View.OnAttachStateChangeListener {
        public b() {
        }

        @Override // android.view.View.OnAttachStateChangeListener
        public final void onViewAttachedToWindow(View view) {
        }

        @Override // android.view.View.OnAttachStateChangeListener
        public final void onViewDetachedFromWindow(View view) {
            ViewTreeObserver viewTreeObserver = ViewOnKeyListenerC0067b.this.f332H;
            if (viewTreeObserver != null) {
                if (!viewTreeObserver.isAlive()) {
                    ViewOnKeyListenerC0067b.this.f332H = view.getViewTreeObserver();
                }
                ViewOnKeyListenerC0067b viewOnKeyListenerC0067b = ViewOnKeyListenerC0067b.this;
                viewOnKeyListenerC0067b.f332H.removeGlobalOnLayoutListener(viewOnKeyListenerC0067b.f343s);
            }
            view.removeOnAttachStateChangeListener(this);
        }
    }

    /* renamed from: androidx.appcompat.view.menu.b$c */
    public class c implements InterfaceC0153n0 {

        /* renamed from: androidx.appcompat.view.menu.b$c$a */
        public class a implements Runnable {

            /* renamed from: j */
            public final /* synthetic */ d f354j;

            /* renamed from: k */
            public final /* synthetic */ MenuItem f355k;

            /* renamed from: l */
            public final /* synthetic */ C0070e f356l;

            public a(d dVar, MenuItem menuItem, C0070e c0070e) {
                this.f354j = dVar;
                this.f355k = menuItem;
                this.f356l = c0070e;
            }

            @Override // java.lang.Runnable
            public final void run() {
                d dVar = this.f354j;
                if (dVar != null) {
                    ViewOnKeyListenerC0067b.this.f334J = true;
                    dVar.f359b.m181d(false);
                    ViewOnKeyListenerC0067b.this.f334J = false;
                }
                if (this.f355k.isEnabled() && this.f355k.hasSubMenu()) {
                    this.f356l.m196s(this.f355k, 4);
                }
            }
        }

        public c() {
        }

        /* JADX WARN: Type inference failed for: r0v10, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
        /* JADX WARN: Type inference failed for: r0v3, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
        /* JADX WARN: Type inference failed for: r0v6, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
        /* JADX WARN: Type inference failed for: r4v1, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
        @Override // androidx.appcompat.widget.InterfaceC0153n0
        /* renamed from: a */
        public final void mo168a(C0070e c0070e, MenuItem menuItem) {
            ViewOnKeyListenerC0067b.this.f340p.removeCallbacksAndMessages(null);
            int size = ViewOnKeyListenerC0067b.this.f342r.size();
            int i6 = 0;
            while (true) {
                if (i6 >= size) {
                    i6 = -1;
                    break;
                } else if (c0070e == ((d) ViewOnKeyListenerC0067b.this.f342r.get(i6)).f359b) {
                    break;
                } else {
                    i6++;
                }
            }
            if (i6 == -1) {
                return;
            }
            int i7 = i6 + 1;
            ViewOnKeyListenerC0067b.this.f340p.postAtTime(new a(i7 < ViewOnKeyListenerC0067b.this.f342r.size() ? (d) ViewOnKeyListenerC0067b.this.f342r.get(i7) : null, menuItem, c0070e), c0070e, SystemClock.uptimeMillis() + 200);
        }

        @Override // androidx.appcompat.widget.InterfaceC0153n0
        /* renamed from: h */
        public final void mo169h(C0070e c0070e, MenuItem menuItem) {
            ViewOnKeyListenerC0067b.this.f340p.removeCallbacksAndMessages(c0070e);
        }
    }

    /* renamed from: androidx.appcompat.view.menu.b$d */
    public static class d {

        /* renamed from: a */
        public final C0155o0 f358a;

        /* renamed from: b */
        public final C0070e f359b;

        /* renamed from: c */
        public final int f360c;

        public d(C0155o0 c0155o0, C0070e c0070e, int i6) {
            this.f358a = c0155o0;
            this.f359b = c0070e;
            this.f360c = i6;
        }
    }

    public ViewOnKeyListenerC0067b(Context context, View view, int i6, int i7, boolean z5) {
        this.f335k = context;
        this.f348x = view;
        this.f337m = i6;
        this.f338n = i7;
        this.f339o = z5;
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        this.f350z = view.getLayoutDirection() != 1 ? 1 : 0;
        Resources resources = context.getResources();
        this.f336l = Math.max(resources.getDisplayMetrics().widthPixels / 2, resources.getDimensionPixelSize(R.dimen.abc_config_prefDialogWidth));
        this.f340p = new Handler();
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    /* JADX WARN: Type inference failed for: r0v3, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    /* JADX WARN: Type inference failed for: r0v7, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    /* JADX WARN: Type inference failed for: r3v0, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    /* JADX WARN: Type inference failed for: r3v3, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    /* JADX WARN: Type inference failed for: r3v4, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    /* JADX WARN: Type inference failed for: r4v5, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    /* JADX WARN: Type inference failed for: r7v1, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: a */
    public final void mo152a(C0070e c0070e, boolean z5) {
        int i6;
        int size = this.f342r.size();
        int i7 = 0;
        while (true) {
            if (i7 >= size) {
                i7 = -1;
                break;
            } else if (c0070e == ((d) this.f342r.get(i7)).f359b) {
                break;
            } else {
                i7++;
            }
        }
        if (i7 < 0) {
            return;
        }
        int i8 = i7 + 1;
        if (i8 < this.f342r.size()) {
            ((d) this.f342r.get(i8)).f359b.m181d(false);
        }
        d dVar = (d) this.f342r.remove(i7);
        dVar.f359b.m199v(this);
        if (this.f334J) {
            dVar.f358a.f889H.setExitTransition(null);
            dVar.f358a.f889H.setAnimationStyle(0);
        }
        dVar.f358a.dismiss();
        int size2 = this.f342r.size();
        if (size2 > 0) {
            i6 = ((d) this.f342r.get(size2 - 1)).f360c;
        } else {
            View view = this.f348x;
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            i6 = view.getLayoutDirection() == 1 ? 0 : 1;
        }
        this.f350z = i6;
        if (size2 != 0) {
            if (z5) {
                ((d) this.f342r.get(0)).f359b.m181d(false);
                return;
            }
            return;
        }
        dismiss();
        InterfaceC0074i.a aVar = this.f331G;
        if (aVar != null) {
            aVar.mo206a(c0070e, true);
        }
        ViewTreeObserver viewTreeObserver = this.f332H;
        if (viewTreeObserver != null) {
            if (viewTreeObserver.isAlive()) {
                this.f332H.removeGlobalOnLayoutListener(this.f343s);
            }
            this.f332H = null;
        }
        this.f349y.removeOnAttachStateChangeListener(this.f344t);
        this.f333I.onDismiss();
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    /* JADX WARN: Type inference failed for: r0v2, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    @Override // p049h.InterfaceC0898f
    /* renamed from: b */
    public final boolean mo153b() {
        return this.f342r.size() > 0 && ((d) this.f342r.get(0)).f358a.mo153b();
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: d */
    public final boolean mo154d() {
        return false;
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    @Override // p049h.InterfaceC0898f
    public final void dismiss() {
        int size = this.f342r.size();
        if (size <= 0) {
            return;
        }
        d[] dVarArr = (d[]) this.f342r.toArray(new d[size]);
        while (true) {
            size--;
            if (size < 0) {
                return;
            }
            d dVar = dVarArr[size];
            if (dVar.f358a.mo153b()) {
                dVar.f358a.dismiss();
            }
        }
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.e>] */
    /* JADX WARN: Type inference failed for: r0v3, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.e>] */
    @Override // p049h.InterfaceC0898f
    /* renamed from: f */
    public final void mo155f() {
        if (mo153b()) {
            return;
        }
        Iterator it = this.f341q.iterator();
        while (it.hasNext()) {
            m167v((C0070e) it.next());
        }
        this.f341q.clear();
        View view = this.f348x;
        this.f349y = view;
        if (view != null) {
            boolean z5 = this.f332H == null;
            ViewTreeObserver viewTreeObserver = view.getViewTreeObserver();
            this.f332H = viewTreeObserver;
            if (z5) {
                viewTreeObserver.addOnGlobalLayoutListener(this.f343s);
            }
            this.f349y.addOnAttachStateChangeListener(this.f344t);
        }
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: g */
    public final void mo156g() {
        Iterator it = this.f342r.iterator();
        while (it.hasNext()) {
            ListAdapter adapter = ((d) it.next()).f358a.f892l.getAdapter();
            if (adapter instanceof HeaderViewListAdapter) {
                adapter = ((HeaderViewListAdapter) adapter).getWrappedAdapter();
            }
            ((C0069d) adapter).notifyDataSetChanged();
        }
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: h */
    public final void mo150h(InterfaceC0074i.a aVar) {
        this.f331G = aVar;
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: j */
    public final boolean mo157j(SubMenuC0077l subMenuC0077l) {
        Iterator it = this.f342r.iterator();
        while (it.hasNext()) {
            d dVar = (d) it.next();
            if (subMenuC0077l == dVar.f359b) {
                dVar.f358a.f892l.requestFocus();
                return true;
            }
        }
        if (!subMenuC0077l.hasVisibleItems()) {
            return false;
        }
        mo159l(subMenuC0077l);
        InterfaceC0074i.a aVar = this.f331G;
        if (aVar != null) {
            aVar.mo207b(subMenuC0077l);
        }
        return true;
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    /* JADX WARN: Type inference failed for: r0v2, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    @Override // p049h.InterfaceC0898f
    /* renamed from: k */
    public final ListView mo158k() {
        if (this.f342r.isEmpty()) {
            return null;
        }
        return ((d) this.f342r.get(r0.size() - 1)).f358a.f892l;
    }

    /* JADX WARN: Type inference failed for: r0v2, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.e>] */
    @Override // p049h.AbstractC0896d
    /* renamed from: l */
    public final void mo159l(C0070e c0070e) {
        c0070e.m180c(this, this.f335k);
        if (mo153b()) {
            m167v(c0070e);
        } else {
            this.f341q.add(c0070e);
        }
    }

    @Override // p049h.AbstractC0896d
    /* renamed from: n */
    public final void mo160n(View view) {
        if (this.f348x != view) {
            this.f348x = view;
            int i6 = this.f346v;
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            this.f347w = Gravity.getAbsoluteGravity(i6, view.getLayoutDirection());
        }
    }

    @Override // p049h.AbstractC0896d
    /* renamed from: o */
    public final void mo161o(boolean z5) {
        this.f329E = z5;
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    /* JADX WARN: Type inference failed for: r3v2, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    @Override // android.widget.PopupWindow.OnDismissListener
    public final void onDismiss() {
        d dVar;
        int size = this.f342r.size();
        int i6 = 0;
        while (true) {
            if (i6 >= size) {
                dVar = null;
                break;
            }
            dVar = (d) this.f342r.get(i6);
            if (!dVar.f358a.mo153b()) {
                break;
            } else {
                i6++;
            }
        }
        if (dVar != null) {
            dVar.f359b.m181d(false);
        }
    }

    @Override // android.view.View.OnKeyListener
    public final boolean onKey(View view, int i6, KeyEvent keyEvent) {
        if (keyEvent.getAction() != 1 || i6 != 82) {
            return false;
        }
        dismiss();
        return true;
    }

    @Override // p049h.AbstractC0896d
    /* renamed from: p */
    public final void mo162p(int i6) {
        if (this.f346v != i6) {
            this.f346v = i6;
            View view = this.f348x;
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            this.f347w = Gravity.getAbsoluteGravity(i6, view.getLayoutDirection());
        }
    }

    @Override // p049h.AbstractC0896d
    /* renamed from: q */
    public final void mo163q(int i6) {
        this.f325A = true;
        this.f327C = i6;
    }

    @Override // p049h.AbstractC0896d
    /* renamed from: r */
    public final void mo164r(PopupWindow.OnDismissListener onDismissListener) {
        this.f333I = onDismissListener;
    }

    @Override // p049h.AbstractC0896d
    /* renamed from: s */
    public final void mo165s(boolean z5) {
        this.f330F = z5;
    }

    @Override // p049h.AbstractC0896d
    /* renamed from: t */
    public final void mo166t(int i6) {
        this.f326B = true;
        this.f328D = i6;
    }

    /* JADX WARN: Code restructure failed: missing block: B:36:0x011d, code lost:
    
        if (((r9.getWidth() + r11[0]) + r4) > r12.right) goto L54;
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x0126, code lost:
    
        r9 = 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:70:0x0128, code lost:
    
        r9 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:72:0x0124, code lost:
    
        if ((r11[0] - r4) < 0) goto L53;
     */
    /* JADX WARN: Type inference failed for: r3v12, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    /* JADX WARN: Type inference failed for: r3v2, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    /* JADX WARN: Type inference failed for: r5v6, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    /* JADX WARN: Type inference failed for: r9v2, types: [java.util.ArrayList, java.util.List<androidx.appcompat.view.menu.b$d>] */
    /* renamed from: v */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m167v(androidx.appcompat.view.menu.C0070e r17) {
        /*
            Method dump skipped, instructions count: 496
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.view.menu.ViewOnKeyListenerC0067b.m167v(androidx.appcompat.view.menu.e):void");
    }
}
