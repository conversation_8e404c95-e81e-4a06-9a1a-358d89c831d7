package androidx.appcompat.widget;

import android.annotation.SuppressLint;
import android.app.PendingIntent;
import android.app.SearchableInfo;
import android.content.ActivityNotFoundException;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.database.Cursor;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.Editable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.ImageSpan;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.TouchDelegate;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputConnection;
import android.view.inputmethod.InputMethodManager;
import android.widget.AdapterView;
import android.widget.AutoCompleteTextView;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.activity.result.C0052a;
import com.liaoyuan.aicast.R;
import java.lang.reflect.Method;
import java.util.Objects;
import java.util.WeakHashMap;
import p008b0.C0385m;
import p029e0.C0766p;
import p029e0.C0769s;
import p042g.InterfaceC0869b;
import p057i0.AbstractC0941a;
import p064j0.AbstractC0956a;

/* loaded from: classes.dex */
public class SearchView extends C0146k0 implements InterfaceC0869b {

    /* renamed from: z0 */
    public static final C0104n f558z0;

    /* renamed from: A */
    public final View f559A;

    /* renamed from: B */
    public final View f560B;

    /* renamed from: C */
    public final ImageView f561C;

    /* renamed from: D */
    public final ImageView f562D;

    /* renamed from: E */
    public final ImageView f563E;

    /* renamed from: F */
    public final ImageView f564F;

    /* renamed from: G */
    public final View f565G;

    /* renamed from: H */
    public C0106p f566H;

    /* renamed from: I */
    public Rect f567I;

    /* renamed from: J */
    public Rect f568J;

    /* renamed from: K */
    public int[] f569K;

    /* renamed from: L */
    public int[] f570L;

    /* renamed from: M */
    public final ImageView f571M;

    /* renamed from: N */
    public final Drawable f572N;

    /* renamed from: O */
    public final int f573O;

    /* renamed from: P */
    public final int f574P;

    /* renamed from: Q */
    public final Intent f575Q;

    /* renamed from: R */
    public final Intent f576R;

    /* renamed from: S */
    public final CharSequence f577S;

    /* renamed from: T */
    public InterfaceC0102l f578T;

    /* renamed from: U */
    public InterfaceC0101k f579U;

    /* renamed from: V */
    public View.OnFocusChangeListener f580V;

    /* renamed from: W */
    public InterfaceC0103m f581W;

    /* renamed from: a0 */
    public View.OnClickListener f582a0;

    /* renamed from: b0 */
    public boolean f583b0;

    /* renamed from: c0 */
    public boolean f584c0;

    /* renamed from: d0 */
    public AbstractC0941a f585d0;

    /* renamed from: e0 */
    public boolean f586e0;

    /* renamed from: f0 */
    public CharSequence f587f0;

    /* renamed from: g0 */
    public boolean f588g0;

    /* renamed from: h0 */
    public boolean f589h0;

    /* renamed from: i0 */
    public int f590i0;

    /* renamed from: j0 */
    public boolean f591j0;

    /* renamed from: k0 */
    public String f592k0;

    /* renamed from: l0 */
    public CharSequence f593l0;

    /* renamed from: m0 */
    public boolean f594m0;

    /* renamed from: n0 */
    public int f595n0;

    /* renamed from: o0 */
    public SearchableInfo f596o0;

    /* renamed from: p0 */
    public Bundle f597p0;

    /* renamed from: q0 */
    public final RunnableC0092b f598q0;

    /* renamed from: r0 */
    public RunnableC0093c f599r0;

    /* renamed from: s0 */
    public final WeakHashMap<String, Drawable.ConstantState> f600s0;

    /* renamed from: t0 */
    public final ViewOnClickListenerC0096f f601t0;

    /* renamed from: u0 */
    public ViewOnKeyListenerC0097g f602u0;

    /* renamed from: v0 */
    public final C0098h f603v0;

    /* renamed from: w0 */
    public final C0099i f604w0;

    /* renamed from: x0 */
    public final C0100j f605x0;

    /* renamed from: y */
    public final SearchAutoComplete f606y;

    /* renamed from: y0 */
    public C0091a f607y0;

    /* renamed from: z */
    public final View f608z;

    public static class SearchAutoComplete extends C0124d {

        /* renamed from: m */
        public int f609m;

        /* renamed from: n */
        public SearchView f610n;

        /* renamed from: o */
        public boolean f611o;

        /* renamed from: p */
        public final RunnableC0090a f612p;

        /* renamed from: androidx.appcompat.widget.SearchView$SearchAutoComplete$a */
        public class RunnableC0090a implements Runnable {
            public RunnableC0090a() {
            }

            @Override // java.lang.Runnable
            public final void run() {
                SearchAutoComplete searchAutoComplete = SearchAutoComplete.this;
                if (searchAutoComplete.f611o) {
                    ((InputMethodManager) searchAutoComplete.getContext().getSystemService("input_method")).showSoftInput(searchAutoComplete, 0);
                    searchAutoComplete.f611o = false;
                }
            }
        }

        public SearchAutoComplete(Context context, AttributeSet attributeSet) {
            super(context, attributeSet, R.attr.autoCompleteTextViewStyle);
            this.f612p = new RunnableC0090a();
            this.f609m = getThreshold();
        }

        private int getSearchViewTextMinWidthDp() {
            Configuration configuration = getResources().getConfiguration();
            int i6 = configuration.screenWidthDp;
            int i7 = configuration.screenHeightDp;
            if (i6 >= 960 && i7 >= 720 && configuration.orientation == 2) {
                return 256;
            }
            if (i6 < 600) {
                return (i6 < 640 || i7 < 480) ? 160 : 192;
            }
            return 192;
        }

        /* renamed from: a */
        public final void m277a() {
            if (Build.VERSION.SDK_INT >= 29) {
                setInputMethodMode(1);
                if (enoughToFilter()) {
                    showDropDown();
                    return;
                }
                return;
            }
            C0104n c0104n = SearchView.f558z0;
            Objects.requireNonNull(c0104n);
            C0104n.m283a();
            Method method = c0104n.f626c;
            if (method != null) {
                try {
                    method.invoke(this, Boolean.TRUE);
                } catch (Exception unused) {
                }
            }
        }

        @Override // android.widget.AutoCompleteTextView
        public final boolean enoughToFilter() {
            return this.f609m <= 0 || super.enoughToFilter();
        }

        @Override // androidx.appcompat.widget.C0124d, android.widget.TextView, android.view.View
        public final InputConnection onCreateInputConnection(EditorInfo editorInfo) {
            InputConnection onCreateInputConnection = super.onCreateInputConnection(editorInfo);
            if (this.f611o) {
                removeCallbacks(this.f612p);
                post(this.f612p);
            }
            return onCreateInputConnection;
        }

        @Override // android.view.View
        public final void onFinishInflate() {
            super.onFinishInflate();
            setMinWidth((int) TypedValue.applyDimension(1, getSearchViewTextMinWidthDp(), getResources().getDisplayMetrics()));
        }

        @Override // android.widget.AutoCompleteTextView, android.widget.TextView, android.view.View
        public final void onFocusChanged(boolean z5, int i6, Rect rect) {
            super.onFocusChanged(z5, i6, rect);
            SearchView searchView = this.f610n;
            searchView.m276z(searchView.f584c0);
            searchView.post(searchView.f598q0);
            if (searchView.f606y.hasFocus()) {
                searchView.m264n();
            }
        }

        @Override // android.widget.AutoCompleteTextView, android.widget.TextView, android.view.View
        public final boolean onKeyPreIme(int i6, KeyEvent keyEvent) {
            if (i6 == 4) {
                if (keyEvent.getAction() == 0 && keyEvent.getRepeatCount() == 0) {
                    KeyEvent.DispatcherState keyDispatcherState = getKeyDispatcherState();
                    if (keyDispatcherState != null) {
                        keyDispatcherState.startTracking(keyEvent, this);
                    }
                    return true;
                }
                if (keyEvent.getAction() == 1) {
                    KeyEvent.DispatcherState keyDispatcherState2 = getKeyDispatcherState();
                    if (keyDispatcherState2 != null) {
                        keyDispatcherState2.handleUpEvent(keyEvent);
                    }
                    if (keyEvent.isTracking() && !keyEvent.isCanceled()) {
                        this.f610n.clearFocus();
                        setImeVisibility(false);
                        return true;
                    }
                }
            }
            return super.onKeyPreIme(i6, keyEvent);
        }

        @Override // android.widget.AutoCompleteTextView, android.widget.TextView, android.view.View
        public final void onWindowFocusChanged(boolean z5) {
            super.onWindowFocusChanged(z5);
            if (z5 && this.f610n.hasFocus() && getVisibility() == 0) {
                this.f611o = true;
                Context context = getContext();
                C0104n c0104n = SearchView.f558z0;
                if (context.getResources().getConfiguration().orientation == 2) {
                    m277a();
                }
            }
        }

        @Override // android.widget.AutoCompleteTextView
        public final void performCompletion() {
        }

        @Override // android.widget.AutoCompleteTextView
        public final void replaceText(CharSequence charSequence) {
        }

        public void setImeVisibility(boolean z5) {
            InputMethodManager inputMethodManager = (InputMethodManager) getContext().getSystemService("input_method");
            if (!z5) {
                this.f611o = false;
                removeCallbacks(this.f612p);
                inputMethodManager.hideSoftInputFromWindow(getWindowToken(), 0);
            } else {
                if (!inputMethodManager.isActive(this)) {
                    this.f611o = true;
                    return;
                }
                this.f611o = false;
                removeCallbacks(this.f612p);
                inputMethodManager.showSoftInput(this, 0);
            }
        }

        public void setSearchView(SearchView searchView) {
            this.f610n = searchView;
        }

        @Override // android.widget.AutoCompleteTextView
        public void setThreshold(int i6) {
            super.setThreshold(i6);
            this.f609m = i6;
        }
    }

    /* renamed from: androidx.appcompat.widget.SearchView$a */
    public class C0091a implements TextWatcher {
        public C0091a() {
        }

        @Override // android.text.TextWatcher
        public final void afterTextChanged(Editable editable) {
        }

        @Override // android.text.TextWatcher
        public final void beforeTextChanged(CharSequence charSequence, int i6, int i7, int i8) {
        }

        @Override // android.text.TextWatcher
        public final void onTextChanged(CharSequence charSequence, int i6, int i7, int i8) {
            SearchView searchView = SearchView.this;
            Editable text = searchView.f606y.getText();
            searchView.f593l0 = text;
            boolean z5 = !TextUtils.isEmpty(text);
            searchView.m275y(z5);
            searchView.m259A(!z5);
            searchView.m271u();
            searchView.m274x();
            if (searchView.f578T != null && !TextUtils.equals(charSequence, searchView.f592k0)) {
                InterfaceC0102l interfaceC0102l = searchView.f578T;
                charSequence.toString();
                interfaceC0102l.m279a();
            }
            searchView.f592k0 = charSequence.toString();
        }
    }

    /* renamed from: androidx.appcompat.widget.SearchView$b */
    public class RunnableC0092b implements Runnable {
        public RunnableC0092b() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            SearchView.this.m272v();
        }
    }

    /* renamed from: androidx.appcompat.widget.SearchView$c */
    public class RunnableC0093c implements Runnable {
        public RunnableC0093c() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            AbstractC0941a abstractC0941a = SearchView.this.f585d0;
            if (abstractC0941a instanceof ViewOnClickListenerC0171w0) {
                abstractC0941a.mo472c(null);
            }
        }
    }

    /* renamed from: androidx.appcompat.widget.SearchView$d */
    public class ViewOnFocusChangeListenerC0094d implements View.OnFocusChangeListener {
        public ViewOnFocusChangeListenerC0094d() {
        }

        @Override // android.view.View.OnFocusChangeListener
        public final void onFocusChange(View view, boolean z5) {
            SearchView searchView = SearchView.this;
            View.OnFocusChangeListener onFocusChangeListener = searchView.f580V;
            if (onFocusChangeListener != null) {
                onFocusChangeListener.onFocusChange(searchView, z5);
            }
        }
    }

    /* renamed from: androidx.appcompat.widget.SearchView$e */
    public class ViewOnLayoutChangeListenerC0095e implements View.OnLayoutChangeListener {
        public ViewOnLayoutChangeListenerC0095e() {
        }

        @Override // android.view.View.OnLayoutChangeListener
        public final void onLayoutChange(View view, int i6, int i7, int i8, int i9, int i10, int i11, int i12, int i13) {
            SearchView searchView = SearchView.this;
            if (searchView.f565G.getWidth() > 1) {
                Resources resources = searchView.getContext().getResources();
                int paddingLeft = searchView.f559A.getPaddingLeft();
                Rect rect = new Rect();
                boolean m414b = C0144j1.m414b(searchView);
                int dimensionPixelSize = searchView.f583b0 ? resources.getDimensionPixelSize(R.dimen.abc_dropdownitem_text_padding_left) + resources.getDimensionPixelSize(R.dimen.abc_dropdownitem_icon_width) : 0;
                searchView.f606y.getDropDownBackground().getPadding(rect);
                searchView.f606y.setDropDownHorizontalOffset(m414b ? -rect.left : paddingLeft - (rect.left + dimensionPixelSize));
                searchView.f606y.setDropDownWidth((((searchView.f565G.getWidth() + rect.left) + rect.right) + dimensionPixelSize) - paddingLeft);
            }
        }
    }

    /* renamed from: androidx.appcompat.widget.SearchView$f */
    public class ViewOnClickListenerC0096f implements View.OnClickListener {
        public ViewOnClickListenerC0096f() {
        }

        @Override // android.view.View.OnClickListener
        public final void onClick(View view) {
            SearchView searchView = SearchView.this;
            if (view == searchView.f561C) {
                searchView.m269s();
                return;
            }
            if (view == searchView.f563E) {
                searchView.m265o();
                return;
            }
            if (view == searchView.f562D) {
                searchView.m270t();
                return;
            }
            if (view != searchView.f564F) {
                if (view == searchView.f606y) {
                    searchView.m264n();
                    return;
                }
                return;
            }
            SearchableInfo searchableInfo = searchView.f596o0;
            if (searchableInfo == null) {
                return;
            }
            try {
                if (!searchableInfo.getVoiceSearchLaunchWebSearch()) {
                    if (searchableInfo.getVoiceSearchLaunchRecognizer()) {
                        searchView.getContext().startActivity(searchView.m263m(searchView.f576R, searchableInfo));
                    }
                } else {
                    Intent intent = new Intent(searchView.f575Q);
                    ComponentName searchActivity = searchableInfo.getSearchActivity();
                    intent.putExtra("calling_package", searchActivity == null ? null : searchActivity.flattenToShortString());
                    searchView.getContext().startActivity(intent);
                }
            } catch (ActivityNotFoundException unused) {
                Log.w("SearchView", "Could not find voice search activity");
            }
        }
    }

    /* renamed from: androidx.appcompat.widget.SearchView$g */
    public class ViewOnKeyListenerC0097g implements View.OnKeyListener {
        public ViewOnKeyListenerC0097g() {
        }

        @Override // android.view.View.OnKeyListener
        public final boolean onKey(View view, int i6, KeyEvent keyEvent) {
            SearchView searchView = SearchView.this;
            if (searchView.f596o0 == null) {
                return false;
            }
            if (!searchView.f606y.isPopupShowing() || SearchView.this.f606y.getListSelection() == -1) {
                if ((TextUtils.getTrimmedLength(SearchView.this.f606y.getText()) == 0) || !keyEvent.hasNoModifiers() || keyEvent.getAction() != 1 || i6 != 66) {
                    return false;
                }
                view.cancelLongPress();
                SearchView searchView2 = SearchView.this;
                searchView2.getContext().startActivity(searchView2.m262l("android.intent.action.SEARCH", null, null, searchView2.f606y.getText().toString()));
                return true;
            }
            SearchView searchView3 = SearchView.this;
            if (searchView3.f596o0 == null || searchView3.f585d0 == null || keyEvent.getAction() != 0 || !keyEvent.hasNoModifiers()) {
                return false;
            }
            if (i6 == 66 || i6 == 84 || i6 == 61) {
                return searchView3.m266p(searchView3.f606y.getListSelection());
            }
            if (i6 != 21 && i6 != 22) {
                if (i6 != 19) {
                    return false;
                }
                searchView3.f606y.getListSelection();
                return false;
            }
            searchView3.f606y.setSelection(i6 == 21 ? 0 : searchView3.f606y.length());
            searchView3.f606y.setListSelection(0);
            searchView3.f606y.clearListSelection();
            searchView3.f606y.m277a();
            return true;
        }
    }

    /* renamed from: androidx.appcompat.widget.SearchView$h */
    public class C0098h implements TextView.OnEditorActionListener {
        public C0098h() {
        }

        @Override // android.widget.TextView.OnEditorActionListener
        public final boolean onEditorAction(TextView textView, int i6, KeyEvent keyEvent) {
            SearchView.this.m270t();
            return true;
        }
    }

    /* renamed from: androidx.appcompat.widget.SearchView$i */
    public class C0099i implements AdapterView.OnItemClickListener {
        public C0099i() {
        }

        @Override // android.widget.AdapterView.OnItemClickListener
        public final void onItemClick(AdapterView<?> adapterView, View view, int i6, long j6) {
            SearchView.this.m266p(i6);
        }
    }

    /* renamed from: androidx.appcompat.widget.SearchView$j */
    public class C0100j implements AdapterView.OnItemSelectedListener {
        public C0100j() {
        }

        @Override // android.widget.AdapterView.OnItemSelectedListener
        public final void onItemSelected(AdapterView<?> adapterView, View view, int i6, long j6) {
            SearchView.this.m267q(i6);
        }

        @Override // android.widget.AdapterView.OnItemSelectedListener
        public final void onNothingSelected(AdapterView<?> adapterView) {
        }
    }

    /* renamed from: androidx.appcompat.widget.SearchView$k */
    public interface InterfaceC0101k {
        /* renamed from: a */
        boolean m278a();
    }

    /* renamed from: androidx.appcompat.widget.SearchView$l */
    public interface InterfaceC0102l {
        /* renamed from: a */
        boolean m279a();

        /* renamed from: b */
        boolean m280b();
    }

    /* renamed from: androidx.appcompat.widget.SearchView$m */
    public interface InterfaceC0103m {
        /* renamed from: a */
        boolean m281a();

        /* renamed from: b */
        boolean m282b();
    }

    /* renamed from: androidx.appcompat.widget.SearchView$n */
    public static class C0104n {

        /* renamed from: a */
        public Method f624a;

        /* renamed from: b */
        public Method f625b;

        /* renamed from: c */
        public Method f626c;

        @SuppressLint({"DiscouragedPrivateApi", "SoonBlockedPrivateApi"})
        public C0104n() {
            this.f624a = null;
            this.f625b = null;
            this.f626c = null;
            m283a();
            try {
                Method declaredMethod = AutoCompleteTextView.class.getDeclaredMethod("doBeforeTextChanged", new Class[0]);
                this.f624a = declaredMethod;
                declaredMethod.setAccessible(true);
            } catch (NoSuchMethodException unused) {
            }
            try {
                Method declaredMethod2 = AutoCompleteTextView.class.getDeclaredMethod("doAfterTextChanged", new Class[0]);
                this.f625b = declaredMethod2;
                declaredMethod2.setAccessible(true);
            } catch (NoSuchMethodException unused2) {
            }
            try {
                Method method = AutoCompleteTextView.class.getMethod("ensureImeVisible", Boolean.TYPE);
                this.f626c = method;
                method.setAccessible(true);
            } catch (NoSuchMethodException unused3) {
            }
        }

        /* renamed from: a */
        public static void m283a() {
            if (Build.VERSION.SDK_INT >= 29) {
                throw new UnsupportedClassVersionError("This function can only be used for API Level < 29.");
            }
        }
    }

    /* renamed from: androidx.appcompat.widget.SearchView$o */
    public static class C0105o extends AbstractC0956a {
        public static final Parcelable.Creator<C0105o> CREATOR = new a();

        /* renamed from: l */
        public boolean f627l;

        /* renamed from: androidx.appcompat.widget.SearchView$o$a */
        public class a implements Parcelable.ClassLoaderCreator<C0105o> {
            @Override // android.os.Parcelable.Creator
            public final Object createFromParcel(Parcel parcel) {
                return new C0105o(parcel, null);
            }

            @Override // android.os.Parcelable.Creator
            public final Object[] newArray(int i6) {
                return new C0105o[i6];
            }

            @Override // android.os.Parcelable.ClassLoaderCreator
            public final C0105o createFromParcel(Parcel parcel, ClassLoader classLoader) {
                return new C0105o(parcel, classLoader);
            }
        }

        public C0105o(Parcel parcel, ClassLoader classLoader) {
            super(parcel, classLoader);
            this.f627l = ((Boolean) parcel.readValue(null)).booleanValue();
        }

        public C0105o(Parcelable parcelable) {
            super(parcelable);
        }

        public final String toString() {
            StringBuilder m104h = C0052a.m104h("SearchView.SavedState{");
            m104h.append(Integer.toHexString(System.identityHashCode(this)));
            m104h.append(" isIconified=");
            m104h.append(this.f627l);
            m104h.append("}");
            return m104h.toString();
        }

        @Override // p064j0.AbstractC0956a, android.os.Parcelable
        public final void writeToParcel(Parcel parcel, int i6) {
            parcel.writeParcelable(this.f4731j, i6);
            parcel.writeValue(Boolean.valueOf(this.f627l));
        }
    }

    /* renamed from: androidx.appcompat.widget.SearchView$p */
    public static class C0106p extends TouchDelegate {

        /* renamed from: a */
        public final View f628a;

        /* renamed from: b */
        public final Rect f629b;

        /* renamed from: c */
        public final Rect f630c;

        /* renamed from: d */
        public final Rect f631d;

        /* renamed from: e */
        public final int f632e;

        /* renamed from: f */
        public boolean f633f;

        public C0106p(Rect rect, Rect rect2, View view) {
            super(rect, view);
            this.f632e = ViewConfiguration.get(view.getContext()).getScaledTouchSlop();
            this.f629b = new Rect();
            this.f631d = new Rect();
            this.f630c = new Rect();
            m284a(rect, rect2);
            this.f628a = view;
        }

        /* renamed from: a */
        public final void m284a(Rect rect, Rect rect2) {
            this.f629b.set(rect);
            this.f631d.set(rect);
            Rect rect3 = this.f631d;
            int i6 = this.f632e;
            rect3.inset(-i6, -i6);
            this.f630c.set(rect2);
        }

        @Override // android.view.TouchDelegate
        public final boolean onTouchEvent(MotionEvent motionEvent) {
            boolean z5;
            float f6;
            int i6;
            boolean z6;
            int x6 = (int) motionEvent.getX();
            int y2 = (int) motionEvent.getY();
            int action = motionEvent.getAction();
            boolean z7 = true;
            if (action != 0) {
                if (action == 1 || action == 2) {
                    z6 = this.f633f;
                    if (z6 && !this.f631d.contains(x6, y2)) {
                        z7 = z6;
                        z5 = false;
                    }
                } else {
                    if (action == 3) {
                        z6 = this.f633f;
                        this.f633f = false;
                    }
                    z5 = true;
                    z7 = false;
                }
                z7 = z6;
                z5 = true;
            } else {
                if (this.f629b.contains(x6, y2)) {
                    this.f633f = true;
                    z5 = true;
                }
                z5 = true;
                z7 = false;
            }
            if (!z7) {
                return false;
            }
            if (!z5 || this.f630c.contains(x6, y2)) {
                Rect rect = this.f630c;
                f6 = x6 - rect.left;
                i6 = y2 - rect.top;
            } else {
                f6 = this.f628a.getWidth() / 2;
                i6 = this.f628a.getHeight() / 2;
            }
            motionEvent.setLocation(f6, i6);
            return this.f628a.dispatchTouchEvent(motionEvent);
        }
    }

    static {
        f558z0 = Build.VERSION.SDK_INT < 29 ? new C0104n() : null;
    }

    public SearchView(Context context) {
        this(context, null);
    }

    public SearchView(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, R.attr.searchViewStyle);
    }

    public SearchView(Context context, AttributeSet attributeSet, int i6) {
        super(context, attributeSet, i6);
        this.f567I = new Rect();
        this.f568J = new Rect();
        this.f569K = new int[2];
        this.f570L = new int[2];
        this.f598q0 = new RunnableC0092b();
        this.f599r0 = new RunnableC0093c();
        this.f600s0 = new WeakHashMap<>();
        ViewOnClickListenerC0096f viewOnClickListenerC0096f = new ViewOnClickListenerC0096f();
        this.f601t0 = viewOnClickListenerC0096f;
        this.f602u0 = new ViewOnKeyListenerC0097g();
        C0098h c0098h = new C0098h();
        this.f603v0 = c0098h;
        C0099i c0099i = new C0099i();
        this.f604w0 = c0099i;
        C0100j c0100j = new C0100j();
        this.f605x0 = c0100j;
        this.f607y0 = new C0091a();
        C0123c1 c0123c1 = new C0123c1(context, context.obtainStyledAttributes(attributeSet, C0385m.f2310D, i6, 0));
        LayoutInflater.from(context).inflate(c0123c1.m348l(9, R.layout.abc_search_view), (ViewGroup) this, true);
        SearchAutoComplete searchAutoComplete = (SearchAutoComplete) findViewById(R.id.search_src_text);
        this.f606y = searchAutoComplete;
        searchAutoComplete.setSearchView(this);
        this.f608z = findViewById(R.id.search_edit_frame);
        View findViewById = findViewById(R.id.search_plate);
        this.f559A = findViewById;
        View findViewById2 = findViewById(R.id.submit_area);
        this.f560B = findViewById2;
        ImageView imageView = (ImageView) findViewById(R.id.search_button);
        this.f561C = imageView;
        ImageView imageView2 = (ImageView) findViewById(R.id.search_go_btn);
        this.f562D = imageView2;
        ImageView imageView3 = (ImageView) findViewById(R.id.search_close_btn);
        this.f563E = imageView3;
        ImageView imageView4 = (ImageView) findViewById(R.id.search_voice_btn);
        this.f564F = imageView4;
        ImageView imageView5 = (ImageView) findViewById(R.id.search_mag_icon);
        this.f571M = imageView5;
        Drawable m343g = c0123c1.m343g(10);
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        findViewById.setBackground(m343g);
        findViewById2.setBackground(c0123c1.m343g(14));
        imageView.setImageDrawable(c0123c1.m343g(13));
        imageView2.setImageDrawable(c0123c1.m343g(7));
        imageView3.setImageDrawable(c0123c1.m343g(4));
        imageView4.setImageDrawable(c0123c1.m343g(16));
        imageView5.setImageDrawable(c0123c1.m343g(13));
        this.f572N = c0123c1.m343g(12);
        C0132f1.m387a(imageView, getResources().getString(R.string.abc_searchview_description_search));
        this.f573O = c0123c1.m348l(15, R.layout.abc_search_dropdown_item_icons_2line);
        this.f574P = c0123c1.m348l(5, 0);
        imageView.setOnClickListener(viewOnClickListenerC0096f);
        imageView3.setOnClickListener(viewOnClickListenerC0096f);
        imageView2.setOnClickListener(viewOnClickListenerC0096f);
        imageView4.setOnClickListener(viewOnClickListenerC0096f);
        searchAutoComplete.setOnClickListener(viewOnClickListenerC0096f);
        searchAutoComplete.addTextChangedListener(this.f607y0);
        searchAutoComplete.setOnEditorActionListener(c0098h);
        searchAutoComplete.setOnItemClickListener(c0099i);
        searchAutoComplete.setOnItemSelectedListener(c0100j);
        searchAutoComplete.setOnKeyListener(this.f602u0);
        searchAutoComplete.setOnFocusChangeListener(new ViewOnFocusChangeListenerC0094d());
        setIconifiedByDefault(c0123c1.m337a(8, true));
        int m342f = c0123c1.m342f(1, -1);
        if (m342f != -1) {
            setMaxWidth(m342f);
        }
        this.f577S = c0123c1.m350n(6);
        this.f587f0 = c0123c1.m350n(11);
        int m346j = c0123c1.m346j(3, -1);
        if (m346j != -1) {
            setImeOptions(m346j);
        }
        int m346j2 = c0123c1.m346j(2, -1);
        if (m346j2 != -1) {
            setInputType(m346j2);
        }
        setFocusable(c0123c1.m337a(0, true));
        c0123c1.m352r();
        Intent intent = new Intent("android.speech.action.WEB_SEARCH");
        this.f575Q = intent;
        intent.addFlags(268435456);
        intent.putExtra("android.speech.extra.LANGUAGE_MODEL", "web_search");
        Intent intent2 = new Intent("android.speech.action.RECOGNIZE_SPEECH");
        this.f576R = intent2;
        intent2.addFlags(268435456);
        View findViewById3 = findViewById(searchAutoComplete.getDropDownAnchor());
        this.f565G = findViewById3;
        if (findViewById3 != null) {
            findViewById3.addOnLayoutChangeListener(new ViewOnLayoutChangeListenerC0095e());
        }
        m276z(this.f583b0);
        m273w();
    }

    private int getPreferredHeight() {
        return getContext().getResources().getDimensionPixelSize(R.dimen.abc_search_view_preferred_height);
    }

    private int getPreferredWidth() {
        return getContext().getResources().getDimensionPixelSize(R.dimen.abc_search_view_preferred_width);
    }

    private void setQuery(CharSequence charSequence) {
        this.f606y.setText(charSequence);
        this.f606y.setSelection(TextUtils.isEmpty(charSequence) ? 0 : charSequence.length());
    }

    /* renamed from: A */
    public final void m259A(boolean z5) {
        int i6 = 8;
        if (this.f591j0 && !this.f584c0 && z5) {
            this.f562D.setVisibility(8);
            i6 = 0;
        }
        this.f564F.setVisibility(i6);
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void clearFocus() {
        this.f589h0 = true;
        super.clearFocus();
        this.f606y.clearFocus();
        this.f606y.setImeVisibility(false);
        this.f589h0 = false;
    }

    @Override // p042g.InterfaceC0869b
    /* renamed from: d */
    public final void mo260d() {
        if (this.f594m0) {
            return;
        }
        this.f594m0 = true;
        int imeOptions = this.f606y.getImeOptions();
        this.f595n0 = imeOptions;
        this.f606y.setImeOptions(imeOptions | 33554432);
        this.f606y.setText("");
        setIconified(false);
    }

    @Override // p042g.InterfaceC0869b
    /* renamed from: e */
    public final void mo261e() {
        this.f606y.setText("");
        SearchAutoComplete searchAutoComplete = this.f606y;
        searchAutoComplete.setSelection(searchAutoComplete.length());
        this.f593l0 = "";
        clearFocus();
        m276z(true);
        this.f606y.setImeOptions(this.f595n0);
        this.f594m0 = false;
    }

    public int getImeOptions() {
        return this.f606y.getImeOptions();
    }

    public int getInputType() {
        return this.f606y.getInputType();
    }

    public int getMaxWidth() {
        return this.f590i0;
    }

    public CharSequence getQuery() {
        return this.f606y.getText();
    }

    public CharSequence getQueryHint() {
        CharSequence charSequence = this.f587f0;
        if (charSequence != null) {
            return charSequence;
        }
        SearchableInfo searchableInfo = this.f596o0;
        return (searchableInfo == null || searchableInfo.getHintId() == 0) ? this.f577S : getContext().getText(this.f596o0.getHintId());
    }

    public int getSuggestionCommitIconResId() {
        return this.f574P;
    }

    public int getSuggestionRowLayout() {
        return this.f573O;
    }

    public AbstractC0941a getSuggestionsAdapter() {
        return this.f585d0;
    }

    /* renamed from: l */
    public final Intent m262l(String str, Uri uri, String str2, String str3) {
        Intent intent = new Intent(str);
        intent.addFlags(268435456);
        if (uri != null) {
            intent.setData(uri);
        }
        intent.putExtra("user_query", this.f593l0);
        if (str3 != null) {
            intent.putExtra("query", str3);
        }
        if (str2 != null) {
            intent.putExtra("intent_extra_data_key", str2);
        }
        Bundle bundle = this.f597p0;
        if (bundle != null) {
            intent.putExtra("app_data", bundle);
        }
        intent.setComponent(this.f596o0.getSearchActivity());
        return intent;
    }

    /* renamed from: m */
    public final Intent m263m(Intent intent, SearchableInfo searchableInfo) {
        ComponentName searchActivity = searchableInfo.getSearchActivity();
        Intent intent2 = new Intent("android.intent.action.SEARCH");
        intent2.setComponent(searchActivity);
        PendingIntent activity = PendingIntent.getActivity(getContext(), 0, intent2, 1073741824);
        Bundle bundle = new Bundle();
        Bundle bundle2 = this.f597p0;
        if (bundle2 != null) {
            bundle.putParcelable("app_data", bundle2);
        }
        Intent intent3 = new Intent(intent);
        Resources resources = getResources();
        String string = searchableInfo.getVoiceLanguageModeId() != 0 ? resources.getString(searchableInfo.getVoiceLanguageModeId()) : "free_form";
        String string2 = searchableInfo.getVoicePromptTextId() != 0 ? resources.getString(searchableInfo.getVoicePromptTextId()) : null;
        String string3 = searchableInfo.getVoiceLanguageId() != 0 ? resources.getString(searchableInfo.getVoiceLanguageId()) : null;
        int voiceMaxResults = searchableInfo.getVoiceMaxResults() != 0 ? searchableInfo.getVoiceMaxResults() : 1;
        intent3.putExtra("android.speech.extra.LANGUAGE_MODEL", string);
        intent3.putExtra("android.speech.extra.PROMPT", string2);
        intent3.putExtra("android.speech.extra.LANGUAGE", string3);
        intent3.putExtra("android.speech.extra.MAX_RESULTS", voiceMaxResults);
        intent3.putExtra("calling_package", searchActivity != null ? searchActivity.flattenToShortString() : null);
        intent3.putExtra("android.speech.extra.RESULTS_PENDINGINTENT", activity);
        intent3.putExtra("android.speech.extra.RESULTS_PENDINGINTENT_BUNDLE", bundle);
        return intent3;
    }

    /* renamed from: n */
    public final void m264n() {
        if (Build.VERSION.SDK_INT >= 29) {
            this.f606y.refreshAutoCompleteResults();
            return;
        }
        C0104n c0104n = f558z0;
        SearchAutoComplete searchAutoComplete = this.f606y;
        Objects.requireNonNull(c0104n);
        C0104n.m283a();
        Method method = c0104n.f624a;
        if (method != null) {
            try {
                method.invoke(searchAutoComplete, new Object[0]);
            } catch (Exception unused) {
            }
        }
        C0104n c0104n2 = f558z0;
        SearchAutoComplete searchAutoComplete2 = this.f606y;
        Objects.requireNonNull(c0104n2);
        C0104n.m283a();
        Method method2 = c0104n2.f625b;
        if (method2 != null) {
            try {
                method2.invoke(searchAutoComplete2, new Object[0]);
            } catch (Exception unused2) {
            }
        }
    }

    /* renamed from: o */
    public final void m265o() {
        if (!TextUtils.isEmpty(this.f606y.getText())) {
            this.f606y.setText("");
            this.f606y.requestFocus();
            this.f606y.setImeVisibility(true);
        } else if (this.f583b0) {
            InterfaceC0101k interfaceC0101k = this.f579U;
            if (interfaceC0101k == null || !interfaceC0101k.m278a()) {
                clearFocus();
                m276z(true);
            }
        }
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onDetachedFromWindow() {
        removeCallbacks(this.f598q0);
        post(this.f599r0);
        super.onDetachedFromWindow();
    }

    @Override // androidx.appcompat.widget.C0146k0, android.view.ViewGroup, android.view.View
    public final void onLayout(boolean z5, int i6, int i7, int i8, int i9) {
        super.onLayout(z5, i6, i7, i8, i9);
        if (z5) {
            SearchAutoComplete searchAutoComplete = this.f606y;
            Rect rect = this.f567I;
            searchAutoComplete.getLocationInWindow(this.f569K);
            getLocationInWindow(this.f570L);
            int[] iArr = this.f569K;
            int i10 = iArr[1];
            int[] iArr2 = this.f570L;
            int i11 = i10 - iArr2[1];
            int i12 = iArr[0] - iArr2[0];
            rect.set(i12, i11, searchAutoComplete.getWidth() + i12, searchAutoComplete.getHeight() + i11);
            Rect rect2 = this.f568J;
            Rect rect3 = this.f567I;
            rect2.set(rect3.left, 0, rect3.right, i9 - i7);
            C0106p c0106p = this.f566H;
            if (c0106p != null) {
                c0106p.m284a(this.f568J, this.f567I);
                return;
            }
            C0106p c0106p2 = new C0106p(this.f568J, this.f567I, this.f606y);
            this.f566H = c0106p2;
            setTouchDelegate(c0106p2);
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:11:0x001d, code lost:
    
        if (r0 <= 0) goto L23;
     */
    /* JADX WARN: Removed duplicated region for block: B:15:0x0041  */
    /* JADX WARN: Removed duplicated region for block: B:19:0x0049  */
    @Override // androidx.appcompat.widget.C0146k0, android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void onMeasure(int r4, int r5) {
        /*
            r3 = this;
            boolean r0 = r3.f584c0
            if (r0 == 0) goto L8
            super.onMeasure(r4, r5)
            return
        L8:
            int r0 = android.view.View.MeasureSpec.getMode(r4)
            int r4 = android.view.View.MeasureSpec.getSize(r4)
            r1 = -2147483648(0xffffffff80000000, float:-0.0)
            r2 = 1073741824(0x40000000, float:2.0)
            if (r0 == r1) goto L2a
            if (r0 == 0) goto L20
            if (r0 == r2) goto L1b
            goto L37
        L1b:
            int r0 = r3.f590i0
            if (r0 <= 0) goto L37
            goto L2e
        L20:
            int r4 = r3.f590i0
            if (r4 <= 0) goto L25
            goto L37
        L25:
            int r4 = r3.getPreferredWidth()
            goto L37
        L2a:
            int r0 = r3.f590i0
            if (r0 <= 0) goto L2f
        L2e:
            goto L33
        L2f:
            int r0 = r3.getPreferredWidth()
        L33:
            int r4 = java.lang.Math.min(r0, r4)
        L37:
            int r0 = android.view.View.MeasureSpec.getMode(r5)
            int r5 = android.view.View.MeasureSpec.getSize(r5)
            if (r0 == r1) goto L49
            if (r0 == 0) goto L44
            goto L51
        L44:
            int r5 = r3.getPreferredHeight()
            goto L51
        L49:
            int r0 = r3.getPreferredHeight()
            int r5 = java.lang.Math.min(r0, r5)
        L51:
            int r4 = android.view.View.MeasureSpec.makeMeasureSpec(r4, r2)
            int r5 = android.view.View.MeasureSpec.makeMeasureSpec(r5, r2)
            super.onMeasure(r4, r5)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.SearchView.onMeasure(int, int):void");
    }

    @Override // android.view.View
    public final void onRestoreInstanceState(Parcelable parcelable) {
        if (!(parcelable instanceof C0105o)) {
            super.onRestoreInstanceState(parcelable);
            return;
        }
        C0105o c0105o = (C0105o) parcelable;
        super.onRestoreInstanceState(c0105o.f4731j);
        m276z(c0105o.f627l);
        requestLayout();
    }

    @Override // android.view.View
    public final Parcelable onSaveInstanceState() {
        C0105o c0105o = new C0105o(super.onSaveInstanceState());
        c0105o.f627l = this.f584c0;
        return c0105o;
    }

    @Override // android.view.View
    public final void onWindowFocusChanged(boolean z5) {
        super.onWindowFocusChanged(z5);
        post(this.f598q0);
    }

    /* renamed from: p */
    public final boolean m266p(int i6) {
        int i7;
        String m470i;
        InterfaceC0103m interfaceC0103m = this.f581W;
        if (interfaceC0103m != null && interfaceC0103m.m281a()) {
            return false;
        }
        Cursor cursor = this.f585d0.f4676l;
        if (cursor != null && cursor.moveToPosition(i6)) {
            Intent intent = null;
            try {
                int i8 = ViewOnClickListenerC0171w0.f989H;
                String m470i2 = ViewOnClickListenerC0171w0.m470i(cursor, cursor.getColumnIndex("suggest_intent_action"));
                if (m470i2 == null) {
                    m470i2 = this.f596o0.getSuggestIntentAction();
                }
                if (m470i2 == null) {
                    m470i2 = "android.intent.action.SEARCH";
                }
                String m470i3 = ViewOnClickListenerC0171w0.m470i(cursor, cursor.getColumnIndex("suggest_intent_data"));
                if (m470i3 == null) {
                    m470i3 = this.f596o0.getSuggestIntentData();
                }
                if (m470i3 != null && (m470i = ViewOnClickListenerC0171w0.m470i(cursor, cursor.getColumnIndex("suggest_intent_data_id"))) != null) {
                    m470i3 = m470i3 + "/" + Uri.encode(m470i);
                }
                intent = m262l(m470i2, m470i3 == null ? null : Uri.parse(m470i3), ViewOnClickListenerC0171w0.m470i(cursor, cursor.getColumnIndex("suggest_intent_extra_data")), ViewOnClickListenerC0171w0.m470i(cursor, cursor.getColumnIndex("suggest_intent_query")));
            } catch (RuntimeException e6) {
                try {
                    i7 = cursor.getPosition();
                } catch (RuntimeException unused) {
                    i7 = -1;
                }
                Log.w("SearchView", "Search suggestions cursor at row " + i7 + " returned exception.", e6);
            }
            if (intent != null) {
                try {
                    getContext().startActivity(intent);
                } catch (RuntimeException e7) {
                    Log.e("SearchView", "Failed launch activity: " + intent, e7);
                }
            }
        }
        this.f606y.setImeVisibility(false);
        this.f606y.dismissDropDown();
        return true;
    }

    /* renamed from: q */
    public final boolean m267q(int i6) {
        CharSequence mo473d;
        InterfaceC0103m interfaceC0103m = this.f581W;
        if (interfaceC0103m != null && interfaceC0103m.m282b()) {
            return false;
        }
        Editable text = this.f606y.getText();
        Cursor cursor = this.f585d0.f4676l;
        if (cursor == null) {
            return true;
        }
        if (!cursor.moveToPosition(i6) || (mo473d = this.f585d0.mo473d(cursor)) == null) {
            setQuery(text);
            return true;
        }
        setQuery(mo473d);
        return true;
    }

    /* renamed from: r */
    public final void m268r(CharSequence charSequence) {
        setQuery(charSequence);
    }

    @Override // android.view.ViewGroup, android.view.View
    public final boolean requestFocus(int i6, Rect rect) {
        if (this.f589h0 || !isFocusable()) {
            return false;
        }
        if (this.f584c0) {
            return super.requestFocus(i6, rect);
        }
        boolean requestFocus = this.f606y.requestFocus(i6, rect);
        if (requestFocus) {
            m276z(false);
        }
        return requestFocus;
    }

    /* renamed from: s */
    public final void m269s() {
        m276z(false);
        this.f606y.requestFocus();
        this.f606y.setImeVisibility(true);
        View.OnClickListener onClickListener = this.f582a0;
        if (onClickListener != null) {
            onClickListener.onClick(this);
        }
    }

    public void setAppSearchData(Bundle bundle) {
        this.f597p0 = bundle;
    }

    public void setIconified(boolean z5) {
        if (z5) {
            m265o();
        } else {
            m269s();
        }
    }

    public void setIconifiedByDefault(boolean z5) {
        if (this.f583b0 == z5) {
            return;
        }
        this.f583b0 = z5;
        m276z(z5);
        m273w();
    }

    public void setImeOptions(int i6) {
        this.f606y.setImeOptions(i6);
    }

    public void setInputType(int i6) {
        this.f606y.setInputType(i6);
    }

    public void setMaxWidth(int i6) {
        this.f590i0 = i6;
        requestLayout();
    }

    public void setOnCloseListener(InterfaceC0101k interfaceC0101k) {
        this.f579U = interfaceC0101k;
    }

    public void setOnQueryTextFocusChangeListener(View.OnFocusChangeListener onFocusChangeListener) {
        this.f580V = onFocusChangeListener;
    }

    public void setOnQueryTextListener(InterfaceC0102l interfaceC0102l) {
        this.f578T = interfaceC0102l;
    }

    public void setOnSearchClickListener(View.OnClickListener onClickListener) {
        this.f582a0 = onClickListener;
    }

    public void setOnSuggestionListener(InterfaceC0103m interfaceC0103m) {
        this.f581W = interfaceC0103m;
    }

    public void setQueryHint(CharSequence charSequence) {
        this.f587f0 = charSequence;
        m273w();
    }

    public void setQueryRefinementEnabled(boolean z5) {
        this.f588g0 = z5;
        AbstractC0941a abstractC0941a = this.f585d0;
        if (abstractC0941a instanceof ViewOnClickListenerC0171w0) {
            ((ViewOnClickListenerC0171w0) abstractC0941a).f1002z = z5 ? 2 : 1;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:28:0x009c, code lost:
    
        if (getContext().getPackageManager().resolveActivity(r2, 65536) != null) goto L35;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void setSearchableInfo(android.app.SearchableInfo r7) {
        /*
            r6 = this;
            r6.f596o0 = r7
            r0 = 1
            r1 = 65536(0x10000, float:9.1835E-41)
            r2 = 0
            if (r7 == 0) goto L6e
            androidx.appcompat.widget.SearchView$SearchAutoComplete r3 = r6.f606y
            int r7 = r7.getSuggestThreshold()
            r3.setThreshold(r7)
            androidx.appcompat.widget.SearchView$SearchAutoComplete r7 = r6.f606y
            android.app.SearchableInfo r3 = r6.f596o0
            int r3 = r3.getImeOptions()
            r7.setImeOptions(r3)
            android.app.SearchableInfo r7 = r6.f596o0
            int r7 = r7.getInputType()
            r3 = r7 & 15
            if (r3 != r0) goto L36
            r3 = -65537(0xfffffffffffeffff, float:NaN)
            r7 = r7 & r3
            android.app.SearchableInfo r3 = r6.f596o0
            java.lang.String r3 = r3.getSuggestAuthority()
            if (r3 == 0) goto L36
            r7 = r7 | r1
            r3 = 524288(0x80000, float:7.34684E-40)
            r7 = r7 | r3
        L36:
            androidx.appcompat.widget.SearchView$SearchAutoComplete r3 = r6.f606y
            r3.setInputType(r7)
            i0.a r7 = r6.f585d0
            if (r7 == 0) goto L42
            r7.mo472c(r2)
        L42:
            android.app.SearchableInfo r7 = r6.f596o0
            java.lang.String r7 = r7.getSuggestAuthority()
            if (r7 == 0) goto L6b
            androidx.appcompat.widget.w0 r7 = new androidx.appcompat.widget.w0
            android.content.Context r3 = r6.getContext()
            android.app.SearchableInfo r4 = r6.f596o0
            java.util.WeakHashMap<java.lang.String, android.graphics.drawable.Drawable$ConstantState> r5 = r6.f600s0
            r7.<init>(r3, r6, r4, r5)
            r6.f585d0 = r7
            androidx.appcompat.widget.SearchView$SearchAutoComplete r3 = r6.f606y
            r3.setAdapter(r7)
            i0.a r7 = r6.f585d0
            androidx.appcompat.widget.w0 r7 = (androidx.appcompat.widget.ViewOnClickListenerC0171w0) r7
            boolean r3 = r6.f588g0
            if (r3 == 0) goto L68
            r3 = 2
            goto L69
        L68:
            r3 = r0
        L69:
            r7.f1002z = r3
        L6b:
            r6.m273w()
        L6e:
            android.app.SearchableInfo r7 = r6.f596o0
            r3 = 0
            if (r7 == 0) goto L9f
            boolean r7 = r7.getVoiceSearchEnabled()
            if (r7 == 0) goto L9f
            android.app.SearchableInfo r7 = r6.f596o0
            boolean r7 = r7.getVoiceSearchLaunchWebSearch()
            if (r7 == 0) goto L84
            android.content.Intent r2 = r6.f575Q
            goto L8e
        L84:
            android.app.SearchableInfo r7 = r6.f596o0
            boolean r7 = r7.getVoiceSearchLaunchRecognizer()
            if (r7 == 0) goto L8e
            android.content.Intent r2 = r6.f576R
        L8e:
            if (r2 == 0) goto L9f
            android.content.Context r7 = r6.getContext()
            android.content.pm.PackageManager r7 = r7.getPackageManager()
            android.content.pm.ResolveInfo r7 = r7.resolveActivity(r2, r1)
            if (r7 == 0) goto L9f
            goto La0
        L9f:
            r0 = r3
        La0:
            r6.f591j0 = r0
            if (r0 == 0) goto Lab
            androidx.appcompat.widget.SearchView$SearchAutoComplete r7 = r6.f606y
            java.lang.String r0 = "nm"
            r7.setPrivateImeOptions(r0)
        Lab:
            boolean r7 = r6.f584c0
            r6.m276z(r7)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.SearchView.setSearchableInfo(android.app.SearchableInfo):void");
    }

    public void setSubmitButtonEnabled(boolean z5) {
        this.f586e0 = z5;
        m276z(this.f584c0);
    }

    public void setSuggestionsAdapter(AbstractC0941a abstractC0941a) {
        this.f585d0 = abstractC0941a;
        this.f606y.setAdapter(abstractC0941a);
    }

    /* renamed from: t */
    public final void m270t() {
        Editable text = this.f606y.getText();
        if (text == null || TextUtils.getTrimmedLength(text) <= 0) {
            return;
        }
        InterfaceC0102l interfaceC0102l = this.f578T;
        if (interfaceC0102l != null) {
            text.toString();
            if (interfaceC0102l.m280b()) {
                return;
            }
        }
        if (this.f596o0 != null) {
            getContext().startActivity(m262l("android.intent.action.SEARCH", null, null, text.toString()));
        }
        this.f606y.setImeVisibility(false);
        this.f606y.dismissDropDown();
    }

    /* renamed from: u */
    public final void m271u() {
        boolean z5 = true;
        boolean z6 = !TextUtils.isEmpty(this.f606y.getText());
        if (!z6 && (!this.f583b0 || this.f594m0)) {
            z5 = false;
        }
        this.f563E.setVisibility(z5 ? 0 : 8);
        Drawable drawable = this.f563E.getDrawable();
        if (drawable != null) {
            drawable.setState(z6 ? ViewGroup.ENABLED_STATE_SET : ViewGroup.EMPTY_STATE_SET);
        }
    }

    /* renamed from: v */
    public final void m272v() {
        int[] iArr = this.f606y.hasFocus() ? ViewGroup.FOCUSED_STATE_SET : ViewGroup.EMPTY_STATE_SET;
        Drawable background = this.f559A.getBackground();
        if (background != null) {
            background.setState(iArr);
        }
        Drawable background2 = this.f560B.getBackground();
        if (background2 != null) {
            background2.setState(iArr);
        }
        invalidate();
    }

    /* renamed from: w */
    public final void m273w() {
        CharSequence queryHint = getQueryHint();
        SearchAutoComplete searchAutoComplete = this.f606y;
        if (queryHint == null) {
            queryHint = "";
        }
        if (this.f583b0 && this.f572N != null) {
            int textSize = (int) (searchAutoComplete.getTextSize() * 1.25d);
            this.f572N.setBounds(0, 0, textSize, textSize);
            SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder("   ");
            spannableStringBuilder.setSpan(new ImageSpan(this.f572N), 1, 2, 33);
            spannableStringBuilder.append(queryHint);
            queryHint = spannableStringBuilder;
        }
        searchAutoComplete.setHint(queryHint);
    }

    /* renamed from: x */
    public final void m274x() {
        int i6 = 0;
        if (!((this.f586e0 || this.f591j0) && !this.f584c0) || (this.f562D.getVisibility() != 0 && this.f564F.getVisibility() != 0)) {
            i6 = 8;
        }
        this.f560B.setVisibility(i6);
    }

    /* JADX WARN: Code restructure failed: missing block: B:12:0x001e, code lost:
    
        if (r2.f591j0 == false) goto L19;
     */
    /* renamed from: y */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m275y(boolean r3) {
        /*
            r2 = this;
            boolean r0 = r2.f586e0
            r1 = 0
            if (r0 == 0) goto L21
            if (r0 != 0) goto Lb
            boolean r0 = r2.f591j0
            if (r0 == 0) goto L11
        Lb:
            boolean r0 = r2.f584c0
            if (r0 != 0) goto L11
            r0 = 1
            goto L12
        L11:
            r0 = r1
        L12:
            if (r0 == 0) goto L21
            boolean r0 = r2.hasFocus()
            if (r0 == 0) goto L21
            if (r3 != 0) goto L23
            boolean r3 = r2.f591j0
            if (r3 != 0) goto L21
            goto L23
        L21:
            r1 = 8
        L23:
            android.widget.ImageView r3 = r2.f562D
            r3.setVisibility(r1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.SearchView.m275y(boolean):void");
    }

    /* renamed from: z */
    public final void m276z(boolean z5) {
        this.f584c0 = z5;
        int i6 = z5 ? 0 : 8;
        boolean z6 = !TextUtils.isEmpty(this.f606y.getText());
        this.f561C.setVisibility(i6);
        m275y(z6);
        this.f608z.setVisibility(z5 ? 8 : 0);
        this.f571M.setVisibility((this.f571M.getDrawable() == null || this.f583b0) ? 8 : 0);
        m271u();
        m259A(!z6);
        m274x();
    }
}
