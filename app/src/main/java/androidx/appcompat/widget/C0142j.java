package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffColorFilter;
import android.graphics.Shader;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import android.util.Log;
import androidx.appcompat.widget.C0163s0;
import com.liaoyuan.aicast.R;
import p028e.C0750a;
import p156x.C1808a;

/* renamed from: androidx.appcompat.widget.j */
/* loaded from: classes.dex */
public final class C0142j {

    /* renamed from: b */
    public static final PorterDuff.Mode f835b = PorterDuff.Mode.SRC_IN;

    /* renamed from: c */
    public static C0142j f836c;

    /* renamed from: a */
    public C0163s0 f837a;

    /* renamed from: androidx.appcompat.widget.j$a */
    public class a implements C0163s0.c {

        /* renamed from: a */
        public final int[] f838a = {R.drawable.abc_textfield_search_default_mtrl_alpha, R.drawable.abc_textfield_default_mtrl_alpha, R.drawable.abc_ab_share_pack_mtrl_alpha};

        /* renamed from: b */
        public final int[] f839b = {R.drawable.abc_ic_commit_search_api_mtrl_alpha, R.drawable.abc_seekbar_tick_mark_material, R.drawable.abc_ic_menu_share_mtrl_alpha, R.drawable.abc_ic_menu_copy_mtrl_am_alpha, R.drawable.abc_ic_menu_cut_mtrl_alpha, R.drawable.abc_ic_menu_selectall_mtrl_alpha, R.drawable.abc_ic_menu_paste_mtrl_am_alpha};

        /* renamed from: c */
        public final int[] f840c = {R.drawable.abc_textfield_activated_mtrl_alpha, R.drawable.abc_textfield_search_activated_mtrl_alpha, R.drawable.abc_cab_background_top_mtrl_alpha, R.drawable.abc_text_cursor_material, R.drawable.abc_text_select_handle_left_mtrl, R.drawable.abc_text_select_handle_middle_mtrl, R.drawable.abc_text_select_handle_right_mtrl};

        /* renamed from: d */
        public final int[] f841d = {R.drawable.abc_popup_background_mtrl_mult, R.drawable.abc_cab_background_internal_bg, R.drawable.abc_menu_hardkey_panel_mtrl_mult};

        /* renamed from: e */
        public final int[] f842e = {R.drawable.abc_tab_indicator_material, R.drawable.abc_textfield_search_material};

        /* renamed from: f */
        public final int[] f843f = {R.drawable.abc_btn_check_material, R.drawable.abc_btn_radio_material, R.drawable.abc_btn_check_material_anim, R.drawable.abc_btn_radio_material_anim};

        /* renamed from: a */
        public final boolean m407a(int[] iArr, int i6) {
            for (int i7 : iArr) {
                if (i7 == i6) {
                    return true;
                }
            }
            return false;
        }

        /* renamed from: b */
        public final ColorStateList m408b(Context context, int i6) {
            int m482c = C0173x0.m482c(context, R.attr.colorControlHighlight);
            return new ColorStateList(new int[][]{C0173x0.f1011b, C0173x0.f1013d, C0173x0.f1012c, C0173x0.f1015f}, new int[]{C0173x0.m481b(context, R.attr.colorButtonNormal), C1808a.m4623a(m482c, i6), C1808a.m4623a(m482c, i6), i6});
        }

        /* renamed from: c */
        public final LayerDrawable m409c(C0163s0 c0163s0, Context context, int i6) {
            BitmapDrawable bitmapDrawable;
            BitmapDrawable bitmapDrawable2;
            BitmapDrawable bitmapDrawable3;
            int dimensionPixelSize = context.getResources().getDimensionPixelSize(i6);
            Drawable m446e = c0163s0.m446e(context, R.drawable.abc_star_black_48dp);
            Drawable m446e2 = c0163s0.m446e(context, R.drawable.abc_star_half_black_48dp);
            if ((m446e instanceof BitmapDrawable) && m446e.getIntrinsicWidth() == dimensionPixelSize && m446e.getIntrinsicHeight() == dimensionPixelSize) {
                bitmapDrawable = (BitmapDrawable) m446e;
                bitmapDrawable2 = new BitmapDrawable(bitmapDrawable.getBitmap());
            } else {
                Bitmap createBitmap = Bitmap.createBitmap(dimensionPixelSize, dimensionPixelSize, Bitmap.Config.ARGB_8888);
                Canvas canvas = new Canvas(createBitmap);
                m446e.setBounds(0, 0, dimensionPixelSize, dimensionPixelSize);
                m446e.draw(canvas);
                bitmapDrawable = new BitmapDrawable(createBitmap);
                bitmapDrawable2 = new BitmapDrawable(createBitmap);
            }
            bitmapDrawable2.setTileModeX(Shader.TileMode.REPEAT);
            if ((m446e2 instanceof BitmapDrawable) && m446e2.getIntrinsicWidth() == dimensionPixelSize && m446e2.getIntrinsicHeight() == dimensionPixelSize) {
                bitmapDrawable3 = (BitmapDrawable) m446e2;
            } else {
                Bitmap createBitmap2 = Bitmap.createBitmap(dimensionPixelSize, dimensionPixelSize, Bitmap.Config.ARGB_8888);
                Canvas canvas2 = new Canvas(createBitmap2);
                m446e2.setBounds(0, 0, dimensionPixelSize, dimensionPixelSize);
                m446e2.draw(canvas2);
                bitmapDrawable3 = new BitmapDrawable(createBitmap2);
            }
            LayerDrawable layerDrawable = new LayerDrawable(new Drawable[]{bitmapDrawable, bitmapDrawable3, bitmapDrawable2});
            layerDrawable.setId(0, android.R.id.background);
            layerDrawable.setId(1, android.R.id.secondaryProgress);
            layerDrawable.setId(2, android.R.id.progress);
            return layerDrawable;
        }

        /* renamed from: d */
        public final ColorStateList m410d(Context context, int i6) {
            if (i6 == R.drawable.abc_edit_text_material) {
                Object obj = C0750a.f4010a;
                return context.getColorStateList(R.color.abc_tint_edittext);
            }
            if (i6 == R.drawable.abc_switch_track_mtrl_alpha) {
                Object obj2 = C0750a.f4010a;
                return context.getColorStateList(R.color.abc_tint_switch_track);
            }
            if (i6 == R.drawable.abc_switch_thumb_material) {
                int[][] iArr = new int[3][];
                int[] iArr2 = new int[3];
                ColorStateList m483d = C0173x0.m483d(context, R.attr.colorSwitchThumbNormal);
                if (m483d == null || !m483d.isStateful()) {
                    iArr[0] = C0173x0.f1011b;
                    iArr2[0] = C0173x0.m481b(context, R.attr.colorSwitchThumbNormal);
                    iArr[1] = C0173x0.f1014e;
                    iArr2[1] = C0173x0.m482c(context, R.attr.colorControlActivated);
                    iArr[2] = C0173x0.f1015f;
                    iArr2[2] = C0173x0.m482c(context, R.attr.colorSwitchThumbNormal);
                } else {
                    iArr[0] = C0173x0.f1011b;
                    iArr2[0] = m483d.getColorForState(iArr[0], 0);
                    iArr[1] = C0173x0.f1014e;
                    iArr2[1] = C0173x0.m482c(context, R.attr.colorControlActivated);
                    iArr[2] = C0173x0.f1015f;
                    iArr2[2] = m483d.getDefaultColor();
                }
                return new ColorStateList(iArr, iArr2);
            }
            if (i6 == R.drawable.abc_btn_default_mtrl_shape) {
                return m408b(context, C0173x0.m482c(context, R.attr.colorButtonNormal));
            }
            if (i6 == R.drawable.abc_btn_borderless_material) {
                return m408b(context, 0);
            }
            if (i6 == R.drawable.abc_btn_colored_material) {
                return m408b(context, C0173x0.m482c(context, R.attr.colorAccent));
            }
            if (i6 == R.drawable.abc_spinner_mtrl_am_alpha || i6 == R.drawable.abc_spinner_textfield_background_material) {
                Object obj3 = C0750a.f4010a;
                return context.getColorStateList(R.color.abc_tint_spinner);
            }
            if (m407a(this.f839b, i6)) {
                return C0173x0.m483d(context, R.attr.colorControlNormal);
            }
            if (m407a(this.f842e, i6)) {
                Object obj4 = C0750a.f4010a;
                return context.getColorStateList(R.color.abc_tint_default);
            }
            if (m407a(this.f843f, i6)) {
                Object obj5 = C0750a.f4010a;
                return context.getColorStateList(R.color.abc_tint_btn_checkable);
            }
            if (i6 != R.drawable.abc_seekbar_thumb_material) {
                return null;
            }
            Object obj6 = C0750a.f4010a;
            return context.getColorStateList(R.color.abc_tint_seek_thumb);
        }

        /* renamed from: e */
        public final void m411e(Drawable drawable, int i6, PorterDuff.Mode mode) {
            if (C0134g0.m388a(drawable)) {
                drawable = drawable.mutate();
            }
            if (mode == null) {
                mode = C0142j.f835b;
            }
            drawable.setColorFilter(C0142j.m402c(i6, mode));
        }
    }

    /* renamed from: a */
    public static synchronized C0142j m401a() {
        C0142j c0142j;
        synchronized (C0142j.class) {
            if (f836c == null) {
                m403e();
            }
            c0142j = f836c;
        }
        return c0142j;
    }

    /* renamed from: c */
    public static synchronized PorterDuffColorFilter m402c(int i6, PorterDuff.Mode mode) {
        PorterDuffColorFilter m442g;
        synchronized (C0142j.class) {
            m442g = C0163s0.m442g(i6, mode);
        }
        return m442g;
    }

    /* renamed from: e */
    public static synchronized void m403e() {
        synchronized (C0142j.class) {
            if (f836c == null) {
                C0142j c0142j = new C0142j();
                f836c = c0142j;
                c0142j.f837a = C0163s0.m441c();
                C0163s0 c0163s0 = f836c.f837a;
                a aVar = new a();
                synchronized (c0163s0) {
                    c0163s0.f943g = aVar;
                }
            }
        }
    }

    /* renamed from: f */
    public static void m404f(Drawable drawable, C0117a1 c0117a1, int[] iArr) {
        PorterDuff.Mode mode = C0163s0.f934h;
        if (C0134g0.m388a(drawable) && drawable.mutate() != drawable) {
            Log.d("ResourceManagerInternal", "Mutated drawable is not the same instance as the input.");
            return;
        }
        boolean z5 = c0117a1.f706d;
        if (!z5 && !c0117a1.f705c) {
            drawable.clearColorFilter();
            return;
        }
        PorterDuffColorFilter porterDuffColorFilter = null;
        ColorStateList colorStateList = z5 ? c0117a1.f703a : null;
        PorterDuff.Mode mode2 = c0117a1.f705c ? c0117a1.f704b : C0163s0.f934h;
        if (colorStateList != null && mode2 != null) {
            porterDuffColorFilter = C0163s0.m442g(colorStateList.getColorForState(iArr, 0), mode2);
        }
        drawable.setColorFilter(porterDuffColorFilter);
    }

    /* renamed from: b */
    public final synchronized Drawable m405b(Context context, int i6) {
        return this.f837a.m446e(context, i6);
    }

    /* renamed from: d */
    public final synchronized ColorStateList m406d(Context context, int i6) {
        return this.f837a.m448h(context, i6);
    }
}
