package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.widget.SeekBar;
import com.liaoyuan.aicast.R;

/* renamed from: androidx.appcompat.widget.t */
/* loaded from: classes.dex */
public final class C0164t extends SeekBar {

    /* renamed from: j */
    public final C0166u f944j;

    public C0164t(Context context, AttributeSet attributeSet) {
        super(context, attributeSet, R.attr.seekBarStyle);
        C0173x0.m480a(this, getContext());
        C0166u c0166u = new C0166u(this);
        this.f944j = c0166u;
        c0166u.mo438a(attributeSet, R.attr.seekBarStyle);
    }

    @Override // android.widget.AbsSeekBar, android.widget.ProgressBar, android.view.View
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        C0166u c0166u = this.f944j;
        Drawable drawable = c0166u.f946e;
        if (drawable != null && drawable.isStateful() && drawable.setState(c0166u.f945d.getDrawableState())) {
            c0166u.f945d.invalidateDrawable(drawable);
        }
    }

    @Override // android.widget.AbsSeekBar, android.widget.ProgressBar, android.view.View
    public final void jumpDrawablesToCurrentState() {
        super.jumpDrawablesToCurrentState();
        Drawable drawable = this.f944j.f946e;
        if (drawable != null) {
            drawable.jumpToCurrentState();
        }
    }

    @Override // android.widget.AbsSeekBar, android.widget.ProgressBar, android.view.View
    public final synchronized void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        this.f944j.m454d(canvas);
    }
}
