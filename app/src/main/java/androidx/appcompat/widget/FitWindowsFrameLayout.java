package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.widget.FrameLayout;

/* loaded from: classes.dex */
public class FitWindowsFrameLayout extends FrameLayout {

    /* renamed from: j */
    public InterfaceC0140i0 f556j;

    public FitWindowsFrameLayout(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
    }

    @Override // android.view.View
    public final boolean fitSystemWindows(Rect rect) {
        InterfaceC0140i0 interfaceC0140i0 = this.f556j;
        if (interfaceC0140i0 != null) {
            interfaceC0140i0.m400a();
        }
        return super.fitSystemWindows(rect);
    }

    public void setOnFitSystemWindowsListener(InterfaceC0140i0 interfaceC0140i0) {
        this.f556j = interfaceC0140i0;
    }
}
