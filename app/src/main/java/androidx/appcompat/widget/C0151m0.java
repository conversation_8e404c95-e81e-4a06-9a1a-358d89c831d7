package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.database.DataSetObserver;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Handler;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.PopupWindow;
import java.lang.reflect.Method;
import java.util.WeakHashMap;
import p008b0.C0385m;
import p029e0.C0766p;
import p029e0.C0769s;
import p049h.InterfaceC0898f;

/* renamed from: androidx.appcompat.widget.m0 */
/* loaded from: classes.dex */
public class C0151m0 implements InterfaceC0898f {

    /* renamed from: I */
    public static Method f880I;

    /* renamed from: J */
    public static Method f881J;

    /* renamed from: D */
    public final Handler f885D;

    /* renamed from: F */
    public Rect f887F;

    /* renamed from: G */
    public boolean f888G;

    /* renamed from: H */
    public C0154o f889H;

    /* renamed from: j */
    public Context f890j;

    /* renamed from: k */
    public ListAdapter f891k;

    /* renamed from: l */
    public C0137h0 f892l;

    /* renamed from: o */
    public int f895o;

    /* renamed from: p */
    public int f896p;

    /* renamed from: r */
    public boolean f898r;

    /* renamed from: s */
    public boolean f899s;

    /* renamed from: t */
    public boolean f900t;

    /* renamed from: w */
    public b f903w;

    /* renamed from: x */
    public View f904x;

    /* renamed from: y */
    public AdapterView.OnItemClickListener f905y;

    /* renamed from: m */
    public int f893m = -2;

    /* renamed from: n */
    public int f894n = -2;

    /* renamed from: q */
    public int f897q = 1002;

    /* renamed from: u */
    public int f901u = 0;

    /* renamed from: v */
    public int f902v = Integer.MAX_VALUE;

    /* renamed from: z */
    public final e f906z = new e();

    /* renamed from: A */
    public final d f882A = new d();

    /* renamed from: B */
    public final c f883B = new c();

    /* renamed from: C */
    public final a f884C = new a();

    /* renamed from: E */
    public final Rect f886E = new Rect();

    /* renamed from: androidx.appcompat.widget.m0$a */
    public class a implements Runnable {
        public a() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            C0137h0 c0137h0 = C0151m0.this.f892l;
            if (c0137h0 != null) {
                c0137h0.setListSelectionHidden(true);
                c0137h0.requestLayout();
            }
        }
    }

    /* renamed from: androidx.appcompat.widget.m0$b */
    public class b extends DataSetObserver {
        public b() {
        }

        @Override // android.database.DataSetObserver
        public final void onChanged() {
            if (C0151m0.this.mo153b()) {
                C0151m0.this.mo155f();
            }
        }

        @Override // android.database.DataSetObserver
        public final void onInvalidated() {
            C0151m0.this.dismiss();
        }
    }

    /* renamed from: androidx.appcompat.widget.m0$c */
    public class c implements AbsListView.OnScrollListener {
        public c() {
        }

        @Override // android.widget.AbsListView.OnScrollListener
        public final void onScroll(AbsListView absListView, int i6, int i7, int i8) {
        }

        @Override // android.widget.AbsListView.OnScrollListener
        public final void onScrollStateChanged(AbsListView absListView, int i6) {
            if (i6 == 1) {
                if ((C0151m0.this.f889H.getInputMethodMode() == 2) || C0151m0.this.f889H.getContentView() == null) {
                    return;
                }
                C0151m0 c0151m0 = C0151m0.this;
                c0151m0.f885D.removeCallbacks(c0151m0.f906z);
                C0151m0.this.f906z.run();
            }
        }
    }

    /* renamed from: androidx.appcompat.widget.m0$d */
    public class d implements View.OnTouchListener {
        public d() {
        }

        @Override // android.view.View.OnTouchListener
        public final boolean onTouch(View view, MotionEvent motionEvent) {
            C0154o c0154o;
            int action = motionEvent.getAction();
            int x6 = (int) motionEvent.getX();
            int y2 = (int) motionEvent.getY();
            if (action == 0 && (c0154o = C0151m0.this.f889H) != null && c0154o.isShowing() && x6 >= 0 && x6 < C0151m0.this.f889H.getWidth() && y2 >= 0 && y2 < C0151m0.this.f889H.getHeight()) {
                C0151m0 c0151m0 = C0151m0.this;
                c0151m0.f885D.postDelayed(c0151m0.f906z, 250L);
                return false;
            }
            if (action != 1) {
                return false;
            }
            C0151m0 c0151m02 = C0151m0.this;
            c0151m02.f885D.removeCallbacks(c0151m02.f906z);
            return false;
        }
    }

    /* renamed from: androidx.appcompat.widget.m0$e */
    public class e implements Runnable {
        public e() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            C0137h0 c0137h0 = C0151m0.this.f892l;
            if (c0137h0 != null) {
                WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
                if (!c0137h0.isAttachedToWindow() || C0151m0.this.f892l.getCount() <= C0151m0.this.f892l.getChildCount()) {
                    return;
                }
                int childCount = C0151m0.this.f892l.getChildCount();
                C0151m0 c0151m0 = C0151m0.this;
                if (childCount <= c0151m0.f902v) {
                    c0151m0.f889H.setInputMethodMode(2);
                    C0151m0.this.mo155f();
                }
            }
        }
    }

    static {
        if (Build.VERSION.SDK_INT <= 28) {
            try {
                f880I = PopupWindow.class.getDeclaredMethod("setClipToScreenEnabled", Boolean.TYPE);
            } catch (NoSuchMethodException unused) {
                Log.i("ListPopupWindow", "Could not find method setClipToScreenEnabled() on PopupWindow. Oh well.");
            }
            try {
                f881J = PopupWindow.class.getDeclaredMethod("setEpicenterBounds", Rect.class);
            } catch (NoSuchMethodException unused2) {
                Log.i("ListPopupWindow", "Could not find method setEpicenterBounds(Rect) on PopupWindow. Oh well.");
            }
        }
    }

    public C0151m0(Context context, AttributeSet attributeSet, int i6, int i7) {
        this.f890j = context;
        this.f885D = new Handler(context.getMainLooper());
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C0385m.f2373x, i6, i7);
        this.f895o = obtainStyledAttributes.getDimensionPixelOffset(0, 0);
        int dimensionPixelOffset = obtainStyledAttributes.getDimensionPixelOffset(1, 0);
        this.f896p = dimensionPixelOffset;
        if (dimensionPixelOffset != 0) {
            this.f898r = true;
        }
        obtainStyledAttributes.recycle();
        C0154o c0154o = new C0154o(context, attributeSet, i6, i7);
        this.f889H = c0154o;
        c0154o.setInputMethodMode(1);
    }

    @Override // p049h.InterfaceC0898f
    /* renamed from: b */
    public final boolean mo153b() {
        return this.f889H.isShowing();
    }

    /* renamed from: c */
    public final void m425c(int i6) {
        this.f895o = i6;
    }

    /* renamed from: d */
    public final int m426d() {
        return this.f895o;
    }

    @Override // p049h.InterfaceC0898f
    public final void dismiss() {
        this.f889H.dismiss();
        this.f889H.setContentView(null);
        this.f892l = null;
        this.f885D.removeCallbacks(this.f906z);
    }

    @Override // p049h.InterfaceC0898f
    /* renamed from: f */
    public final void mo155f() {
        int i6;
        int i7;
        int paddingBottom;
        C0137h0 c0137h0;
        if (this.f892l == null) {
            C0137h0 mo432q = mo432q(this.f890j, !this.f888G);
            this.f892l = mo432q;
            mo432q.setAdapter(this.f891k);
            this.f892l.setOnItemClickListener(this.f905y);
            this.f892l.setFocusable(true);
            this.f892l.setFocusableInTouchMode(true);
            this.f892l.setOnItemSelectedListener(new C0149l0(this));
            this.f892l.setOnScrollListener(this.f883B);
            this.f889H.setContentView(this.f892l);
        }
        Drawable background = this.f889H.getBackground();
        if (background != null) {
            background.getPadding(this.f886E);
            Rect rect = this.f886E;
            int i8 = rect.top;
            i6 = rect.bottom + i8;
            if (!this.f898r) {
                this.f896p = -i8;
            }
        } else {
            this.f886E.setEmpty();
            i6 = 0;
        }
        int maxAvailableHeight = this.f889H.getMaxAvailableHeight(this.f904x, this.f896p, this.f889H.getInputMethodMode() == 2);
        if (this.f893m == -1) {
            paddingBottom = maxAvailableHeight + i6;
        } else {
            int i9 = this.f894n;
            if (i9 != -2) {
                i7 = 1073741824;
                if (i9 == -1) {
                    int i10 = this.f890j.getResources().getDisplayMetrics().widthPixels;
                    Rect rect2 = this.f886E;
                    i9 = i10 - (rect2.left + rect2.right);
                }
            } else {
                int i11 = this.f890j.getResources().getDisplayMetrics().widthPixels;
                Rect rect3 = this.f886E;
                i9 = i11 - (rect3.left + rect3.right);
                i7 = Integer.MIN_VALUE;
            }
            int m394a = this.f892l.m394a(View.MeasureSpec.makeMeasureSpec(i9, i7), maxAvailableHeight + 0);
            paddingBottom = m394a + (m394a > 0 ? this.f892l.getPaddingBottom() + this.f892l.getPaddingTop() + i6 + 0 : 0);
        }
        boolean z5 = this.f889H.getInputMethodMode() == 2;
        this.f889H.setWindowLayoutType(this.f897q);
        if (this.f889H.isShowing()) {
            View view = this.f904x;
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            if (view.isAttachedToWindow()) {
                int i12 = this.f894n;
                if (i12 == -1) {
                    i12 = -1;
                } else if (i12 == -2) {
                    i12 = this.f904x.getWidth();
                }
                int i13 = this.f893m;
                if (i13 == -1) {
                    if (!z5) {
                        paddingBottom = -1;
                    }
                    if (z5) {
                        this.f889H.setWidth(this.f894n == -1 ? -1 : 0);
                        this.f889H.setHeight(0);
                    } else {
                        this.f889H.setWidth(this.f894n == -1 ? -1 : 0);
                        this.f889H.setHeight(-1);
                    }
                } else if (i13 != -2) {
                    paddingBottom = i13;
                }
                this.f889H.setOutsideTouchable(true);
                this.f889H.update(this.f904x, this.f895o, this.f896p, i12 < 0 ? -1 : i12, paddingBottom < 0 ? -1 : paddingBottom);
                return;
            }
            return;
        }
        int i14 = this.f894n;
        if (i14 == -1) {
            i14 = -1;
        } else if (i14 == -2) {
            i14 = this.f904x.getWidth();
        }
        int i15 = this.f893m;
        if (i15 == -1) {
            paddingBottom = -1;
        } else if (i15 != -2) {
            paddingBottom = i15;
        }
        this.f889H.setWidth(i14);
        this.f889H.setHeight(paddingBottom);
        if (Build.VERSION.SDK_INT <= 28) {
            Method method = f880I;
            if (method != null) {
                try {
                    method.invoke(this.f889H, Boolean.TRUE);
                } catch (Exception unused) {
                    Log.i("ListPopupWindow", "Could not call setClipToScreenEnabled() on PopupWindow. Oh well.");
                }
            }
        } else {
            this.f889H.setIsClippedToScreen(true);
        }
        this.f889H.setOutsideTouchable(true);
        this.f889H.setTouchInterceptor(this.f882A);
        if (this.f900t) {
            this.f889H.setOverlapAnchor(this.f899s);
        }
        if (Build.VERSION.SDK_INT <= 28) {
            Method method2 = f881J;
            if (method2 != null) {
                try {
                    method2.invoke(this.f889H, this.f887F);
                } catch (Exception e6) {
                    Log.e("ListPopupWindow", "Could not invoke setEpicenterBounds on PopupWindow", e6);
                }
            }
        } else {
            this.f889H.setEpicenterBounds(this.f887F);
        }
        this.f889H.showAsDropDown(this.f904x, this.f895o, this.f896p, this.f901u);
        this.f892l.setSelection(-1);
        if ((!this.f888G || this.f892l.isInTouchMode()) && (c0137h0 = this.f892l) != null) {
            c0137h0.setListSelectionHidden(true);
            c0137h0.requestLayout();
        }
        if (this.f888G) {
            return;
        }
        this.f885D.post(this.f884C);
    }

    /* renamed from: g */
    public final int m427g() {
        if (this.f898r) {
            return this.f896p;
        }
        return 0;
    }

    /* renamed from: i */
    public final Drawable m428i() {
        return this.f889H.getBackground();
    }

    @Override // p049h.InterfaceC0898f
    /* renamed from: k */
    public final ListView mo158k() {
        return this.f892l;
    }

    /* renamed from: m */
    public final void m429m(Drawable drawable) {
        this.f889H.setBackgroundDrawable(drawable);
    }

    /* renamed from: n */
    public final void m430n(int i6) {
        this.f896p = i6;
        this.f898r = true;
    }

    /* renamed from: o */
    public void mo431o(ListAdapter listAdapter) {
        b bVar = this.f903w;
        if (bVar == null) {
            this.f903w = new b();
        } else {
            ListAdapter listAdapter2 = this.f891k;
            if (listAdapter2 != null) {
                listAdapter2.unregisterDataSetObserver(bVar);
            }
        }
        this.f891k = listAdapter;
        if (listAdapter != null) {
            listAdapter.registerDataSetObserver(this.f903w);
        }
        C0137h0 c0137h0 = this.f892l;
        if (c0137h0 != null) {
            c0137h0.setAdapter(this.f891k);
        }
    }

    /* renamed from: q */
    public C0137h0 mo432q(Context context, boolean z5) {
        return new C0137h0(context, z5);
    }

    /* renamed from: r */
    public final void m433r(int i6) {
        Drawable background = this.f889H.getBackground();
        if (background == null) {
            this.f894n = i6;
            return;
        }
        background.getPadding(this.f886E);
        Rect rect = this.f886E;
        this.f894n = rect.left + rect.right + i6;
    }

    /* renamed from: s */
    public final void m434s() {
        this.f889H.setInputMethodMode(2);
    }

    /* renamed from: t */
    public final void m435t() {
        this.f888G = true;
        this.f889H.setFocusable(true);
    }

    /* renamed from: u */
    public final void m436u(PopupWindow.OnDismissListener onDismissListener) {
        this.f889H.setOnDismissListener(onDismissListener);
    }
}
