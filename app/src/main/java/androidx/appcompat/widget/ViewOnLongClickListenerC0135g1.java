package androidx.appcompat.widget;

import android.app.Activity;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.res.Resources;
import android.graphics.Rect;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.accessibility.AccessibilityManager;
import com.liaoyuan.aicast.R;
import java.util.WeakHashMap;
import p029e0.C0766p;
import p029e0.C0768r;
import p029e0.C0769s;

/* renamed from: androidx.appcompat.widget.g1 */
/* loaded from: classes.dex */
public final class ViewOnLongClickListenerC0135g1 implements View.OnLongClickListener, View.OnHoverListener, View.OnAttachStateChangeListener {

    /* renamed from: s */
    public static ViewOnLongClickListenerC0135g1 f791s;

    /* renamed from: t */
    public static ViewOnLongClickListenerC0135g1 f792t;

    /* renamed from: j */
    public final View f793j;

    /* renamed from: k */
    public final CharSequence f794k;

    /* renamed from: l */
    public final int f795l;

    /* renamed from: m */
    public final a f796m = new a();

    /* renamed from: n */
    public final b f797n = new b();

    /* renamed from: o */
    public int f798o;

    /* renamed from: p */
    public int f799p;

    /* renamed from: q */
    public C0138h1 f800q;

    /* renamed from: r */
    public boolean f801r;

    /* renamed from: androidx.appcompat.widget.g1$a */
    public class a implements Runnable {
        public a() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            ViewOnLongClickListenerC0135g1.this.m393d(false);
        }
    }

    /* renamed from: androidx.appcompat.widget.g1$b */
    public class b implements Runnable {
        public b() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            ViewOnLongClickListenerC0135g1.this.m392b();
        }
    }

    public ViewOnLongClickListenerC0135g1(View view, CharSequence charSequence) {
        this.f793j = view;
        this.f794k = charSequence;
        this.f795l = C0768r.m2204c(ViewConfiguration.get(view.getContext()));
        m391a();
        view.setOnLongClickListener(this);
        view.setOnHoverListener(this);
    }

    /* renamed from: c */
    public static void m390c(ViewOnLongClickListenerC0135g1 viewOnLongClickListenerC0135g1) {
        ViewOnLongClickListenerC0135g1 viewOnLongClickListenerC0135g12 = f791s;
        if (viewOnLongClickListenerC0135g12 != null) {
            viewOnLongClickListenerC0135g12.f793j.removeCallbacks(viewOnLongClickListenerC0135g12.f796m);
        }
        f791s = viewOnLongClickListenerC0135g1;
        if (viewOnLongClickListenerC0135g1 != null) {
            viewOnLongClickListenerC0135g1.f793j.postDelayed(viewOnLongClickListenerC0135g1.f796m, ViewConfiguration.getLongPressTimeout());
        }
    }

    /* renamed from: a */
    public final void m391a() {
        this.f798o = Integer.MAX_VALUE;
        this.f799p = Integer.MAX_VALUE;
    }

    /* renamed from: b */
    public final void m392b() {
        if (f792t == this) {
            f792t = null;
            C0138h1 c0138h1 = this.f800q;
            if (c0138h1 != null) {
                c0138h1.m397a();
                this.f800q = null;
                m391a();
                this.f793j.removeOnAttachStateChangeListener(this);
            } else {
                Log.e("TooltipCompatHandler", "sActiveHandler.mPopup == null");
            }
        }
        if (f791s == this) {
            m390c(null);
        }
        this.f793j.removeCallbacks(this.f797n);
    }

    /* renamed from: d */
    public final void m393d(boolean z5) {
        int height;
        int i6;
        long longPressTimeout;
        View view = this.f793j;
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        if (view.isAttachedToWindow()) {
            m390c(null);
            ViewOnLongClickListenerC0135g1 viewOnLongClickListenerC0135g1 = f792t;
            if (viewOnLongClickListenerC0135g1 != null) {
                viewOnLongClickListenerC0135g1.m392b();
            }
            f792t = this;
            this.f801r = z5;
            C0138h1 c0138h1 = new C0138h1(this.f793j.getContext());
            this.f800q = c0138h1;
            View view2 = this.f793j;
            int i7 = this.f798o;
            int i8 = this.f799p;
            boolean z6 = this.f801r;
            CharSequence charSequence = this.f794k;
            if (c0138h1.f822b.getParent() != null) {
                c0138h1.m397a();
            }
            c0138h1.f823c.setText(charSequence);
            WindowManager.LayoutParams layoutParams = c0138h1.f824d;
            layoutParams.token = view2.getApplicationWindowToken();
            int dimensionPixelOffset = c0138h1.f821a.getResources().getDimensionPixelOffset(R.dimen.tooltip_precise_anchor_threshold);
            if (view2.getWidth() < dimensionPixelOffset) {
                i7 = view2.getWidth() / 2;
            }
            if (view2.getHeight() >= dimensionPixelOffset) {
                int dimensionPixelOffset2 = c0138h1.f821a.getResources().getDimensionPixelOffset(R.dimen.tooltip_precise_anchor_extra_offset);
                height = i8 + dimensionPixelOffset2;
                i6 = i8 - dimensionPixelOffset2;
            } else {
                height = view2.getHeight();
                i6 = 0;
            }
            layoutParams.gravity = 49;
            int dimensionPixelOffset3 = c0138h1.f821a.getResources().getDimensionPixelOffset(z6 ? R.dimen.tooltip_y_offset_touch : R.dimen.tooltip_y_offset_non_touch);
            View rootView = view2.getRootView();
            ViewGroup.LayoutParams layoutParams2 = rootView.getLayoutParams();
            if (!(layoutParams2 instanceof WindowManager.LayoutParams) || ((WindowManager.LayoutParams) layoutParams2).type != 2) {
                Context context = view2.getContext();
                while (true) {
                    if (!(context instanceof ContextWrapper)) {
                        break;
                    }
                    if (context instanceof Activity) {
                        rootView = ((Activity) context).getWindow().getDecorView();
                        break;
                    }
                    context = ((ContextWrapper) context).getBaseContext();
                }
            }
            if (rootView == null) {
                Log.e("TooltipPopup", "Cannot find app view");
            } else {
                rootView.getWindowVisibleDisplayFrame(c0138h1.f825e);
                Rect rect = c0138h1.f825e;
                if (rect.left < 0 && rect.top < 0) {
                    Resources resources = c0138h1.f821a.getResources();
                    int identifier = resources.getIdentifier("status_bar_height", "dimen", "android");
                    int dimensionPixelSize = identifier != 0 ? resources.getDimensionPixelSize(identifier) : 0;
                    DisplayMetrics displayMetrics = resources.getDisplayMetrics();
                    c0138h1.f825e.set(0, dimensionPixelSize, displayMetrics.widthPixels, displayMetrics.heightPixels);
                }
                rootView.getLocationOnScreen(c0138h1.f827g);
                view2.getLocationOnScreen(c0138h1.f826f);
                int[] iArr = c0138h1.f826f;
                int i9 = iArr[0];
                int[] iArr2 = c0138h1.f827g;
                iArr[0] = i9 - iArr2[0];
                iArr[1] = iArr[1] - iArr2[1];
                layoutParams.x = (iArr[0] + i7) - (rootView.getWidth() / 2);
                int makeMeasureSpec = View.MeasureSpec.makeMeasureSpec(0, 0);
                c0138h1.f822b.measure(makeMeasureSpec, makeMeasureSpec);
                int measuredHeight = c0138h1.f822b.getMeasuredHeight();
                int[] iArr3 = c0138h1.f826f;
                int i10 = ((iArr3[1] + i6) - dimensionPixelOffset3) - measuredHeight;
                int i11 = iArr3[1] + height + dimensionPixelOffset3;
                if (!z6 ? measuredHeight + i11 <= c0138h1.f825e.height() : i10 < 0) {
                    layoutParams.y = i10;
                } else {
                    layoutParams.y = i11;
                }
            }
            ((WindowManager) c0138h1.f821a.getSystemService("window")).addView(c0138h1.f822b, c0138h1.f824d);
            this.f793j.addOnAttachStateChangeListener(this);
            if (this.f801r) {
                longPressTimeout = 2500;
            } else {
                longPressTimeout = ((this.f793j.getWindowSystemUiVisibility() & 1) == 1 ? 3000L : 15000L) - ViewConfiguration.getLongPressTimeout();
            }
            this.f793j.removeCallbacks(this.f797n);
            this.f793j.postDelayed(this.f797n, longPressTimeout);
        }
    }

    @Override // android.view.View.OnHoverListener
    public final boolean onHover(View view, MotionEvent motionEvent) {
        boolean z5;
        if (this.f800q != null && this.f801r) {
            return false;
        }
        AccessibilityManager accessibilityManager = (AccessibilityManager) this.f793j.getContext().getSystemService("accessibility");
        if (accessibilityManager.isEnabled() && accessibilityManager.isTouchExplorationEnabled()) {
            return false;
        }
        int action = motionEvent.getAction();
        if (action != 7) {
            if (action == 10) {
                m391a();
                m392b();
            }
        } else if (this.f793j.isEnabled() && this.f800q == null) {
            int x6 = (int) motionEvent.getX();
            int y2 = (int) motionEvent.getY();
            if (Math.abs(x6 - this.f798o) > this.f795l || Math.abs(y2 - this.f799p) > this.f795l) {
                this.f798o = x6;
                this.f799p = y2;
                z5 = true;
            } else {
                z5 = false;
            }
            if (z5) {
                m390c(this);
            }
        }
        return false;
    }

    @Override // android.view.View.OnLongClickListener
    public final boolean onLongClick(View view) {
        this.f798o = view.getWidth() / 2;
        this.f799p = view.getHeight() / 2;
        m393d(true);
        return true;
    }

    @Override // android.view.View.OnAttachStateChangeListener
    public final void onViewAttachedToWindow(View view) {
    }

    @Override // android.view.View.OnAttachStateChangeListener
    public final void onViewDetachedFromWindow(View view) {
        m392b();
    }
}
