package androidx.appcompat.widget;

import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.widget.CompoundButton;

/* renamed from: androidx.appcompat.widget.i */
/* loaded from: classes.dex */
public final class C0139i {

    /* renamed from: a */
    public final CompoundButton f828a;

    /* renamed from: b */
    public ColorStateList f829b = null;

    /* renamed from: c */
    public PorterDuff.Mode f830c = null;

    /* renamed from: d */
    public boolean f831d = false;

    /* renamed from: e */
    public boolean f832e = false;

    /* renamed from: f */
    public boolean f833f;

    public C0139i(CompoundButton compoundButton) {
        this.f828a = compoundButton;
    }

    /* renamed from: a */
    public final void m398a() {
        Drawable buttonDrawable = this.f828a.getButtonDrawable();
        if (buttonDrawable != null) {
            if (this.f831d || this.f832e) {
                Drawable mutate = buttonDrawable.mutate();
                if (this.f831d) {
                    mutate.setTintList(this.f829b);
                }
                if (this.f832e) {
                    mutate.setTintMode(this.f830c);
                }
                if (mutate.isStateful()) {
                    mutate.setState(this.f828a.getDrawableState());
                }
                this.f828a.setButtonDrawable(mutate);
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:17:0x0058 A[Catch: all -> 0x007b, TryCatch #1 {all -> 0x007b, blocks: (B:3:0x001a, B:5:0x0021, B:8:0x0027, B:10:0x0038, B:12:0x003e, B:14:0x0044, B:15:0x0051, B:17:0x0058, B:18:0x0061, B:20:0x0068), top: B:2:0x001a }] */
    /* JADX WARN: Removed duplicated region for block: B:20:0x0068 A[Catch: all -> 0x007b, TRY_LEAVE, TryCatch #1 {all -> 0x007b, blocks: (B:3:0x001a, B:5:0x0021, B:8:0x0027, B:10:0x0038, B:12:0x003e, B:14:0x0044, B:15:0x0051, B:17:0x0058, B:18:0x0061, B:20:0x0068), top: B:2:0x001a }] */
    /* renamed from: b */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m399b(android.util.AttributeSet r8, int r9) {
        /*
            r7 = this;
            android.widget.CompoundButton r0 = r7.f828a
            android.content.Context r0 = r0.getContext()
            int[] r3 = p008b0.C0385m.f2369v
            androidx.appcompat.widget.c1 r0 = androidx.appcompat.widget.C0123c1.m336q(r0, r8, r3, r9)
            android.widget.CompoundButton r1 = r7.f828a
            android.content.Context r2 = r1.getContext()
            android.content.res.TypedArray r5 = r0.f751b
            r4 = r8
            r6 = r9
            p029e0.C0766p.m2186s(r1, r2, r3, r4, r5, r6)
            r8 = 1
            boolean r9 = r0.m351o(r8)     // Catch: java.lang.Throwable -> L7b
            r1 = 0
            if (r9 == 0) goto L35
            int r9 = r0.m348l(r8, r1)     // Catch: java.lang.Throwable -> L7b
            if (r9 == 0) goto L35
            android.widget.CompoundButton r2 = r7.f828a     // Catch: android.content.res.Resources.NotFoundException -> L35 java.lang.Throwable -> L7b
            android.content.Context r3 = r2.getContext()     // Catch: android.content.res.Resources.NotFoundException -> L35 java.lang.Throwable -> L7b
            android.graphics.drawable.Drawable r9 = p028e.C0750a.m2138a(r3, r9)     // Catch: android.content.res.Resources.NotFoundException -> L35 java.lang.Throwable -> L7b
            r2.setButtonDrawable(r9)     // Catch: android.content.res.Resources.NotFoundException -> L35 java.lang.Throwable -> L7b
            goto L36
        L35:
            r8 = r1
        L36:
            if (r8 != 0) goto L51
            boolean r8 = r0.m351o(r1)     // Catch: java.lang.Throwable -> L7b
            if (r8 == 0) goto L51
            int r8 = r0.m348l(r1, r1)     // Catch: java.lang.Throwable -> L7b
            if (r8 == 0) goto L51
            android.widget.CompoundButton r9 = r7.f828a     // Catch: java.lang.Throwable -> L7b
            android.content.Context r1 = r9.getContext()     // Catch: java.lang.Throwable -> L7b
            android.graphics.drawable.Drawable r8 = p028e.C0750a.m2138a(r1, r8)     // Catch: java.lang.Throwable -> L7b
            r9.setButtonDrawable(r8)     // Catch: java.lang.Throwable -> L7b
        L51:
            r8 = 2
            boolean r9 = r0.m351o(r8)     // Catch: java.lang.Throwable -> L7b
            if (r9 == 0) goto L61
            android.widget.CompoundButton r9 = r7.f828a     // Catch: java.lang.Throwable -> L7b
            android.content.res.ColorStateList r8 = r0.m339c(r8)     // Catch: java.lang.Throwable -> L7b
            r9.setButtonTintList(r8)     // Catch: java.lang.Throwable -> L7b
        L61:
            r8 = 3
            boolean r9 = r0.m351o(r8)     // Catch: java.lang.Throwable -> L7b
            if (r9 == 0) goto L77
            android.widget.CompoundButton r9 = r7.f828a     // Catch: java.lang.Throwable -> L7b
            r1 = -1
            int r8 = r0.m346j(r8, r1)     // Catch: java.lang.Throwable -> L7b
            r1 = 0
            android.graphics.PorterDuff$Mode r8 = androidx.appcompat.widget.C0134g0.m389b(r8, r1)     // Catch: java.lang.Throwable -> L7b
            r9.setButtonTintMode(r8)     // Catch: java.lang.Throwable -> L7b
        L77:
            r0.m352r()
            return
        L7b:
            r8 = move-exception
            r0.m352r()
            throw r8
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.C0139i.m399b(android.util.AttributeSet, int):void");
    }
}
