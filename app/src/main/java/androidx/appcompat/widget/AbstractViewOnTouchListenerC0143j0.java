package androidx.appcompat.widget;

import android.os.SystemClock;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewParent;
import p049h.InterfaceC0898f;

/* renamed from: androidx.appcompat.widget.j0 */
/* loaded from: classes.dex */
public abstract class AbstractViewOnTouchListenerC0143j0 implements View.OnTouchListener, View.OnAttachStateChangeListener {

    /* renamed from: j */
    public final float f844j;

    /* renamed from: k */
    public final int f845k;

    /* renamed from: l */
    public final int f846l;

    /* renamed from: m */
    public final View f847m;

    /* renamed from: n */
    public a f848n;

    /* renamed from: o */
    public b f849o;

    /* renamed from: p */
    public boolean f850p;

    /* renamed from: q */
    public int f851q;

    /* renamed from: r */
    public final int[] f852r = new int[2];

    /* renamed from: androidx.appcompat.widget.j0$a */
    public class a implements Runnable {
        public a() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            ViewParent parent = AbstractViewOnTouchListenerC0143j0.this.f847m.getParent();
            if (parent != null) {
                parent.requestDisallowInterceptTouchEvent(true);
            }
        }
    }

    /* renamed from: androidx.appcompat.widget.j0$b */
    public class b implements Runnable {
        public b() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            AbstractViewOnTouchListenerC0143j0 abstractViewOnTouchListenerC0143j0 = AbstractViewOnTouchListenerC0143j0.this;
            abstractViewOnTouchListenerC0143j0.m412a();
            View view = abstractViewOnTouchListenerC0143j0.f847m;
            if (view.isEnabled() && !view.isLongClickable() && abstractViewOnTouchListenerC0143j0.mo144c()) {
                view.getParent().requestDisallowInterceptTouchEvent(true);
                long uptimeMillis = SystemClock.uptimeMillis();
                MotionEvent obtain = MotionEvent.obtain(uptimeMillis, uptimeMillis, 3, 0.0f, 0.0f, 0);
                view.onTouchEvent(obtain);
                obtain.recycle();
                abstractViewOnTouchListenerC0143j0.f850p = true;
            }
        }
    }

    public AbstractViewOnTouchListenerC0143j0(View view) {
        this.f847m = view;
        view.setLongClickable(true);
        view.addOnAttachStateChangeListener(this);
        this.f844j = ViewConfiguration.get(view.getContext()).getScaledTouchSlop();
        int tapTimeout = ViewConfiguration.getTapTimeout();
        this.f845k = tapTimeout;
        this.f846l = (ViewConfiguration.getLongPressTimeout() + tapTimeout) / 2;
    }

    /* renamed from: a */
    public final void m412a() {
        b bVar = this.f849o;
        if (bVar != null) {
            this.f847m.removeCallbacks(bVar);
        }
        a aVar = this.f848n;
        if (aVar != null) {
            this.f847m.removeCallbacks(aVar);
        }
    }

    /* renamed from: b */
    public abstract InterfaceC0898f mo143b();

    /* renamed from: c */
    public abstract boolean mo144c();

    /* renamed from: d */
    public boolean mo322d() {
        InterfaceC0898f mo143b = mo143b();
        if (mo143b == null || !mo143b.mo153b()) {
            return true;
        }
        mo143b.dismiss();
        return true;
    }

    /* JADX WARN: Code restructure failed: missing block: B:40:0x0086, code lost:
    
        if (r4 != 3) goto L61;
     */
    /* JADX WARN: Removed duplicated region for block: B:59:0x0112  */
    @Override // android.view.View.OnTouchListener
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean onTouch(android.view.View r12, android.view.MotionEvent r13) {
        /*
            Method dump skipped, instructions count: 304
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.AbstractViewOnTouchListenerC0143j0.onTouch(android.view.View, android.view.MotionEvent):boolean");
    }

    @Override // android.view.View.OnAttachStateChangeListener
    public final void onViewAttachedToWindow(View view) {
    }

    @Override // android.view.View.OnAttachStateChangeListener
    public final void onViewDetachedFromWindow(View view) {
        this.f850p = false;
        this.f851q = -1;
        a aVar = this.f848n;
        if (aVar != null) {
            this.f847m.removeCallbacks(aVar);
        }
    }
}
