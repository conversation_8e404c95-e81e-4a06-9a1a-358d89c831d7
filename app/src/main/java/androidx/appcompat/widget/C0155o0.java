package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.widget.HeaderViewListAdapter;
import android.widget.ListAdapter;
import android.widget.PopupWindow;
import androidx.appcompat.view.menu.C0069d;
import androidx.appcompat.view.menu.C0070e;
import androidx.appcompat.view.menu.C0072g;
import androidx.appcompat.view.menu.ListMenuItemView;
import java.lang.reflect.Method;

/* renamed from: androidx.appcompat.widget.o0 */
/* loaded from: classes.dex */
public final class C0155o0 extends C0151m0 implements InterfaceC0153n0 {

    /* renamed from: L */
    public static Method f915L;

    /* renamed from: K */
    public InterfaceC0153n0 f916K;

    /* renamed from: androidx.appcompat.widget.o0$a */
    public static class a extends C0137h0 {

        /* renamed from: w */
        public final int f917w;

        /* renamed from: x */
        public final int f918x;

        /* renamed from: y */
        public InterfaceC0153n0 f919y;

        /* renamed from: z */
        public C0072g f920z;

        public a(Context context, boolean z5) {
            super(context, z5);
            if (1 == context.getResources().getConfiguration().getLayoutDirection()) {
                this.f917w = 21;
                this.f918x = 22;
            } else {
                this.f917w = 22;
                this.f918x = 21;
            }
        }

        @Override // androidx.appcompat.widget.C0137h0, android.view.View
        public final boolean onHoverEvent(MotionEvent motionEvent) {
            int i6;
            int pointToPosition;
            int i7;
            if (this.f919y != null) {
                ListAdapter adapter = getAdapter();
                if (adapter instanceof HeaderViewListAdapter) {
                    HeaderViewListAdapter headerViewListAdapter = (HeaderViewListAdapter) adapter;
                    i6 = headerViewListAdapter.getHeadersCount();
                    adapter = headerViewListAdapter.getWrappedAdapter();
                } else {
                    i6 = 0;
                }
                C0069d c0069d = (C0069d) adapter;
                C0072g c0072g = null;
                if (motionEvent.getAction() != 10 && (pointToPosition = pointToPosition((int) motionEvent.getX(), (int) motionEvent.getY())) != -1 && (i7 = pointToPosition - i6) >= 0 && i7 < c0069d.getCount()) {
                    c0072g = c0069d.getItem(i7);
                }
                C0072g c0072g2 = this.f920z;
                if (c0072g2 != c0072g) {
                    C0070e c0070e = c0069d.f369j;
                    if (c0072g2 != null) {
                        this.f919y.mo169h(c0070e, c0072g2);
                    }
                    this.f920z = c0072g;
                    if (c0072g != null) {
                        this.f919y.mo168a(c0070e, c0072g);
                    }
                }
            }
            return super.onHoverEvent(motionEvent);
        }

        @Override // android.widget.ListView, android.widget.AbsListView, android.view.View, android.view.KeyEvent.Callback
        public final boolean onKeyDown(int i6, KeyEvent keyEvent) {
            ListMenuItemView listMenuItemView = (ListMenuItemView) getSelectedView();
            if (listMenuItemView != null && i6 == this.f917w) {
                if (listMenuItemView.isEnabled() && listMenuItemView.getItemData().hasSubMenu()) {
                    performItemClick(listMenuItemView, getSelectedItemPosition(), getSelectedItemId());
                }
                return true;
            }
            if (listMenuItemView == null || i6 != this.f918x) {
                return super.onKeyDown(i6, keyEvent);
            }
            setSelection(-1);
            ListAdapter adapter = getAdapter();
            if (adapter instanceof HeaderViewListAdapter) {
                adapter = ((HeaderViewListAdapter) adapter).getWrappedAdapter();
            }
            ((C0069d) adapter).f369j.m181d(false);
            return true;
        }

        public void setHoverListener(InterfaceC0153n0 interfaceC0153n0) {
            this.f919y = interfaceC0153n0;
        }

        @Override // androidx.appcompat.widget.C0137h0, android.widget.AbsListView
        public /* bridge */ /* synthetic */ void setSelector(Drawable drawable) {
            super.setSelector(drawable);
        }
    }

    static {
        try {
            if (Build.VERSION.SDK_INT <= 28) {
                f915L = PopupWindow.class.getDeclaredMethod("setTouchModal", Boolean.TYPE);
            }
        } catch (NoSuchMethodException unused) {
            Log.i("MenuPopupWindow", "Could not find method setTouchModal() on PopupWindow. Oh well.");
        }
    }

    public C0155o0(Context context, int i6, int i7) {
        super(context, null, i6, i7);
    }

    @Override // androidx.appcompat.widget.InterfaceC0153n0
    /* renamed from: a */
    public final void mo168a(C0070e c0070e, MenuItem menuItem) {
        InterfaceC0153n0 interfaceC0153n0 = this.f916K;
        if (interfaceC0153n0 != null) {
            interfaceC0153n0.mo168a(c0070e, menuItem);
        }
    }

    @Override // androidx.appcompat.widget.InterfaceC0153n0
    /* renamed from: h */
    public final void mo169h(C0070e c0070e, MenuItem menuItem) {
        InterfaceC0153n0 interfaceC0153n0 = this.f916K;
        if (interfaceC0153n0 != null) {
            interfaceC0153n0.mo169h(c0070e, menuItem);
        }
    }

    @Override // androidx.appcompat.widget.C0151m0
    /* renamed from: q */
    public final C0137h0 mo432q(Context context, boolean z5) {
        a aVar = new a(context, z5);
        aVar.setHoverListener(this);
        return aVar;
    }

    /* renamed from: v */
    public final void m437v() {
        if (Build.VERSION.SDK_INT > 28) {
            this.f889H.setTouchModal(false);
            return;
        }
        Method method = f915L;
        if (method != null) {
            try {
                method.invoke(this.f889H, Boolean.FALSE);
            } catch (Exception unused) {
                Log.i("MenuPopupWindow", "Could not invoke setTouchModal() on PopupWindow. Oh well.");
            }
        }
    }
}
