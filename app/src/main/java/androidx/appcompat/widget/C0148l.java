package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.RippleDrawable;
import android.net.Uri;
import android.util.AttributeSet;
import android.widget.ImageButton;
import com.liaoyuan.aicast.R;

/* renamed from: androidx.appcompat.widget.l */
/* loaded from: classes.dex */
public class C0148l extends ImageButton {

    /* renamed from: j */
    public final C0127e f875j;

    /* renamed from: k */
    public final C0150m f876k;

    public C0148l(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, R.attr.imageButtonStyle);
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C0148l(Context context, AttributeSet attributeSet, int i6) {
        super(context, attributeSet, i6);
        C0177z0.m514a(context);
        C0173x0.m480a(this, getContext());
        C0127e c0127e = new C0127e(this);
        this.f875j = c0127e;
        c0127e.m356d(attributeSet, i6);
        C0150m c0150m = new C0150m(this);
        this.f876k = c0150m;
        c0150m.m421b(attributeSet, i6);
    }

    @Override // android.widget.ImageView, android.view.View
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        C0127e c0127e = this.f875j;
        if (c0127e != null) {
            c0127e.m353a();
        }
        C0150m c0150m = this.f876k;
        if (c0150m != null) {
            c0150m.m420a();
        }
    }

    public ColorStateList getSupportBackgroundTintList() {
        C0127e c0127e = this.f875j;
        if (c0127e != null) {
            return c0127e.m354b();
        }
        return null;
    }

    public PorterDuff.Mode getSupportBackgroundTintMode() {
        C0127e c0127e = this.f875j;
        if (c0127e != null) {
            return c0127e.m355c();
        }
        return null;
    }

    public ColorStateList getSupportImageTintList() {
        C0117a1 c0117a1;
        C0150m c0150m = this.f876k;
        if (c0150m == null || (c0117a1 = c0150m.f879b) == null) {
            return null;
        }
        return c0117a1.f703a;
    }

    public PorterDuff.Mode getSupportImageTintMode() {
        C0117a1 c0117a1;
        C0150m c0150m = this.f876k;
        if (c0150m == null || (c0117a1 = c0150m.f879b) == null) {
            return null;
        }
        return c0117a1.f704b;
    }

    @Override // android.widget.ImageView, android.view.View
    public final boolean hasOverlappingRendering() {
        return ((this.f876k.f878a.getBackground() instanceof RippleDrawable) ^ true) && super.hasOverlappingRendering();
    }

    @Override // android.view.View
    public void setBackgroundDrawable(Drawable drawable) {
        super.setBackgroundDrawable(drawable);
        C0127e c0127e = this.f875j;
        if (c0127e != null) {
            c0127e.m357e();
        }
    }

    @Override // android.view.View
    public void setBackgroundResource(int i6) {
        super.setBackgroundResource(i6);
        C0127e c0127e = this.f875j;
        if (c0127e != null) {
            c0127e.m358f(i6);
        }
    }

    @Override // android.widget.ImageView
    public void setImageBitmap(Bitmap bitmap) {
        super.setImageBitmap(bitmap);
        C0150m c0150m = this.f876k;
        if (c0150m != null) {
            c0150m.m420a();
        }
    }

    @Override // android.widget.ImageView
    public void setImageDrawable(Drawable drawable) {
        super.setImageDrawable(drawable);
        C0150m c0150m = this.f876k;
        if (c0150m != null) {
            c0150m.m420a();
        }
    }

    @Override // android.widget.ImageView
    public void setImageResource(int i6) {
        this.f876k.m422c(i6);
    }

    @Override // android.widget.ImageView
    public void setImageURI(Uri uri) {
        super.setImageURI(uri);
        C0150m c0150m = this.f876k;
        if (c0150m != null) {
            c0150m.m420a();
        }
    }

    public void setSupportBackgroundTintList(ColorStateList colorStateList) {
        C0127e c0127e = this.f875j;
        if (c0127e != null) {
            c0127e.m360h(colorStateList);
        }
    }

    public void setSupportBackgroundTintMode(PorterDuff.Mode mode) {
        C0127e c0127e = this.f875j;
        if (c0127e != null) {
            c0127e.m361i(mode);
        }
    }

    public void setSupportImageTintList(ColorStateList colorStateList) {
        C0150m c0150m = this.f876k;
        if (c0150m != null) {
            c0150m.m423d(colorStateList);
        }
    }

    public void setSupportImageTintMode(PorterDuff.Mode mode) {
        C0150m c0150m = this.f876k;
        if (c0150m != null) {
            c0150m.m424e(mode);
        }
    }
}
