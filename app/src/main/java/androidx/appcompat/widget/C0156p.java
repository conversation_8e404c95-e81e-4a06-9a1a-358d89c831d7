package androidx.appcompat.widget;

import android.R;
import android.graphics.Bitmap;
import android.graphics.BitmapShader;
import android.graphics.Shader;
import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.ClipDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.util.AttributeSet;
import android.widget.ProgressBar;
import p162y.InterfaceC2059a;

/* renamed from: androidx.appcompat.widget.p */
/* loaded from: classes.dex */
public class C0156p {

    /* renamed from: c */
    public static final int[] f921c = {R.attr.indeterminateDrawable, R.attr.progressDrawable};

    /* renamed from: a */
    public final ProgressBar f922a;

    /* renamed from: b */
    public Bitmap f923b;

    public C0156p(ProgressBar progressBar) {
        this.f922a = progressBar;
    }

    /* renamed from: a */
    public void mo438a(AttributeSet attributeSet, int i6) {
        C0123c1 m336q = C0123c1.m336q(this.f922a.getContext(), attributeSet, f921c, i6);
        Drawable m344h = m336q.m344h(0);
        if (m344h != null) {
            ProgressBar progressBar = this.f922a;
            if (m344h instanceof AnimationDrawable) {
                AnimationDrawable animationDrawable = (AnimationDrawable) m344h;
                int numberOfFrames = animationDrawable.getNumberOfFrames();
                AnimationDrawable animationDrawable2 = new AnimationDrawable();
                animationDrawable2.setOneShot(animationDrawable.isOneShot());
                for (int i7 = 0; i7 < numberOfFrames; i7++) {
                    Drawable m439b = m439b(animationDrawable.getFrame(i7), true);
                    m439b.setLevel(10000);
                    animationDrawable2.addFrame(m439b, animationDrawable.getDuration(i7));
                }
                animationDrawable2.setLevel(10000);
                m344h = animationDrawable2;
            }
            progressBar.setIndeterminateDrawable(m344h);
        }
        Drawable m344h2 = m336q.m344h(1);
        if (m344h2 != null) {
            this.f922a.setProgressDrawable(m439b(m344h2, false));
        }
        m336q.m352r();
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* renamed from: b */
    public final Drawable m439b(Drawable drawable, boolean z5) {
        if (drawable instanceof InterfaceC2059a) {
            InterfaceC2059a interfaceC2059a = (InterfaceC2059a) drawable;
            Drawable m5172a = interfaceC2059a.m5172a();
            if (m5172a != null) {
                interfaceC2059a.m5173b(m439b(m5172a, z5));
            }
        } else {
            if (drawable instanceof LayerDrawable) {
                LayerDrawable layerDrawable = (LayerDrawable) drawable;
                int numberOfLayers = layerDrawable.getNumberOfLayers();
                Drawable[] drawableArr = new Drawable[numberOfLayers];
                for (int i6 = 0; i6 < numberOfLayers; i6++) {
                    int id = layerDrawable.getId(i6);
                    drawableArr[i6] = m439b(layerDrawable.getDrawable(i6), id == 16908301 || id == 16908303);
                }
                LayerDrawable layerDrawable2 = new LayerDrawable(drawableArr);
                for (int i7 = 0; i7 < numberOfLayers; i7++) {
                    layerDrawable2.setId(i7, layerDrawable.getId(i7));
                }
                return layerDrawable2;
            }
            if (drawable instanceof BitmapDrawable) {
                BitmapDrawable bitmapDrawable = (BitmapDrawable) drawable;
                Bitmap bitmap = bitmapDrawable.getBitmap();
                if (this.f923b == null) {
                    this.f923b = bitmap;
                }
                ShapeDrawable shapeDrawable = new ShapeDrawable(new RoundRectShape(new float[]{5.0f, 5.0f, 5.0f, 5.0f, 5.0f, 5.0f, 5.0f, 5.0f}, null, null));
                shapeDrawable.getPaint().setShader(new BitmapShader(bitmap, Shader.TileMode.REPEAT, Shader.TileMode.CLAMP));
                shapeDrawable.getPaint().setColorFilter(bitmapDrawable.getPaint().getColorFilter());
                return z5 ? new ClipDrawable(shapeDrawable, 3, 1) : shapeDrawable;
            }
        }
        return drawable;
    }
}
