package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.util.SparseBooleanArray;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import androidx.appcompat.view.menu.AbstractC0066a;
import androidx.appcompat.view.menu.ActionMenuItemView;
import androidx.appcompat.view.menu.C0070e;
import androidx.appcompat.view.menu.C0072g;
import androidx.appcompat.view.menu.C0073h;
import androidx.appcompat.view.menu.InterfaceC0074i;
import androidx.appcompat.view.menu.InterfaceC0075j;
import androidx.appcompat.view.menu.SubMenuC0077l;
import androidx.appcompat.widget.ActionMenuView;
import com.liaoyuan.aicast.R;
import java.util.ArrayList;
import java.util.Objects;
import p029e0.AbstractC0752b;
import p049h.AbstractC0896d;
import p049h.InterfaceC0898f;

/* renamed from: androidx.appcompat.widget.c */
/* loaded from: classes.dex */
public final class C0121c extends AbstractC0066a {

    /* renamed from: A */
    public final SparseBooleanArray f713A;

    /* renamed from: B */
    public e f714B;

    /* renamed from: C */
    public a f715C;

    /* renamed from: D */
    public c f716D;

    /* renamed from: E */
    public b f717E;

    /* renamed from: F */
    public final f f718F;

    /* renamed from: r */
    public d f719r;

    /* renamed from: s */
    public Drawable f720s;

    /* renamed from: t */
    public boolean f721t;

    /* renamed from: u */
    public boolean f722u;

    /* renamed from: v */
    public boolean f723v;

    /* renamed from: w */
    public int f724w;

    /* renamed from: x */
    public int f725x;

    /* renamed from: y */
    public int f726y;

    /* renamed from: z */
    public boolean f727z;

    /* renamed from: androidx.appcompat.widget.c$a */
    public class a extends C0073h {
        public a(Context context, SubMenuC0077l subMenuC0077l, View view) {
            super(context, subMenuC0077l, view, false, R.attr.actionOverflowMenuStyle, 0);
            if (!subMenuC0077l.f468A.m214g()) {
                View view2 = C0121c.this.f719r;
                this.f438f = view2 == null ? (View) C0121c.this.f324q : view2;
            }
            m222d(C0121c.this.f718F);
        }

        @Override // androidx.appcompat.view.menu.C0073h
        /* renamed from: c */
        public final void mo221c() {
            C0121c.this.f715C = null;
            super.mo221c();
        }
    }

    /* renamed from: androidx.appcompat.widget.c$b */
    public class b extends ActionMenuItemView.AbstractC0065b {
        public b() {
        }
    }

    /* renamed from: androidx.appcompat.widget.c$c */
    public class c implements Runnable {

        /* renamed from: j */
        public e f730j;

        public c(e eVar) {
            this.f730j = eVar;
        }

        @Override // java.lang.Runnable
        public final void run() {
            C0070e.a aVar;
            C0070e c0070e = C0121c.this.f319l;
            if (c0070e != null && (aVar = c0070e.f380e) != null) {
                aVar.mo205b(c0070e);
            }
            View view = (View) C0121c.this.f324q;
            if (view != null && view.getWindowToken() != null) {
                e eVar = this.f730j;
                boolean z5 = true;
                if (!eVar.m220b()) {
                    if (eVar.f438f == null) {
                        z5 = false;
                    } else {
                        eVar.m224f(0, 0, false, false);
                    }
                }
                if (z5) {
                    C0121c.this.f714B = this.f730j;
                }
            }
            C0121c.this.f716D = null;
        }
    }

    /* renamed from: androidx.appcompat.widget.c$d */
    public class d extends AppCompatImageView implements ActionMenuView.InterfaceC0084a {

        /* renamed from: androidx.appcompat.widget.c$d$a */
        public class a extends AbstractViewOnTouchListenerC0143j0 {
            public a(View view) {
                super(view);
            }

            @Override // androidx.appcompat.widget.AbstractViewOnTouchListenerC0143j0
            /* renamed from: b */
            public final InterfaceC0898f mo143b() {
                e eVar = C0121c.this.f714B;
                if (eVar == null) {
                    return null;
                }
                return eVar.m219a();
            }

            @Override // androidx.appcompat.widget.AbstractViewOnTouchListenerC0143j0
            /* renamed from: c */
            public final boolean mo144c() {
                C0121c.this.m321m();
                return true;
            }

            @Override // androidx.appcompat.widget.AbstractViewOnTouchListenerC0143j0
            /* renamed from: d */
            public final boolean mo322d() {
                C0121c c0121c = C0121c.this;
                if (c0121c.f716D != null) {
                    return false;
                }
                c0121c.m319k();
                return true;
            }
        }

        public d(Context context) {
            super(context, null, R.attr.actionOverflowButtonStyle);
            setClickable(true);
            setFocusable(true);
            setVisibility(0);
            setEnabled(true);
            C0132f1.m387a(this, getContentDescription());
            setOnTouchListener(new a(this));
        }

        @Override // androidx.appcompat.widget.ActionMenuView.InterfaceC0084a
        /* renamed from: a */
        public final boolean mo137a() {
            return false;
        }

        @Override // androidx.appcompat.widget.ActionMenuView.InterfaceC0084a
        /* renamed from: c */
        public final boolean mo139c() {
            return false;
        }

        @Override // android.view.View
        public final boolean performClick() {
            if (super.performClick()) {
                return true;
            }
            playSoundEffect(0);
            C0121c.this.m321m();
            return true;
        }

        @Override // android.widget.ImageView
        public final boolean setFrame(int i6, int i7, int i8, int i9) {
            boolean frame = super.setFrame(i6, i7, i8, i9);
            Drawable drawable = getDrawable();
            Drawable background = getBackground();
            if (drawable != null && background != null) {
                int width = getWidth();
                int height = getHeight();
                int max = Math.max(width, height) / 2;
                int paddingLeft = (width + (getPaddingLeft() - getPaddingRight())) / 2;
                int paddingTop = (height + (getPaddingTop() - getPaddingBottom())) / 2;
                background.setHotspotBounds(paddingLeft - max, paddingTop - max, paddingLeft + max, paddingTop + max);
            }
            return frame;
        }
    }

    /* renamed from: androidx.appcompat.widget.c$e */
    public class e extends C0073h {
        public e(Context context, C0070e c0070e, View view) {
            super(context, c0070e, view, true, R.attr.actionOverflowMenuStyle, 0);
            this.f439g = 8388613;
            m222d(C0121c.this.f718F);
        }

        @Override // androidx.appcompat.view.menu.C0073h
        /* renamed from: c */
        public final void mo221c() {
            C0070e c0070e = C0121c.this.f319l;
            if (c0070e != null) {
                c0070e.m181d(true);
            }
            C0121c.this.f714B = null;
            super.mo221c();
        }
    }

    /* renamed from: androidx.appcompat.widget.c$f */
    public class f implements InterfaceC0074i.a {
        public f() {
        }

        @Override // androidx.appcompat.view.menu.InterfaceC0074i.a
        /* renamed from: a */
        public final void mo206a(C0070e c0070e, boolean z5) {
            if (c0070e instanceof SubMenuC0077l) {
                c0070e.mo189l().m181d(false);
            }
            InterfaceC0074i.a aVar = C0121c.this.f321n;
            if (aVar != null) {
                aVar.mo206a(c0070e, z5);
            }
        }

        @Override // androidx.appcompat.view.menu.InterfaceC0074i.a
        /* renamed from: b */
        public final boolean mo207b(C0070e c0070e) {
            C0121c c0121c = C0121c.this;
            if (c0070e == c0121c.f319l) {
                return false;
            }
            Objects.requireNonNull(((SubMenuC0077l) c0070e).f468A);
            Objects.requireNonNull(c0121c);
            InterfaceC0074i.a aVar = C0121c.this.f321n;
            if (aVar != null) {
                return aVar.mo207b(c0070e);
            }
            return false;
        }
    }

    public C0121c(Context context) {
        super(context);
        this.f713A = new SparseBooleanArray();
        this.f718F = new f();
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: a */
    public final void mo152a(C0070e c0070e, boolean z5) {
        m317b();
        InterfaceC0074i.a aVar = this.f321n;
        if (aVar != null) {
            aVar.mo206a(c0070e, z5);
        }
    }

    /* renamed from: b */
    public final boolean m317b() {
        boolean z5;
        boolean m319k = m319k();
        a aVar = this.f715C;
        if (aVar != null) {
            if (aVar.m220b()) {
                aVar.f442j.dismiss();
            }
            z5 = true;
        } else {
            z5 = false;
        }
        return m319k | z5;
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: d */
    public final boolean mo154d() {
        int i6;
        ArrayList<C0072g> arrayList;
        int i7;
        boolean z5;
        C0070e c0070e = this.f319l;
        if (c0070e != null) {
            arrayList = c0070e.m190m();
            i6 = arrayList.size();
        } else {
            i6 = 0;
            arrayList = null;
        }
        int i8 = this.f726y;
        int i9 = this.f725x;
        int makeMeasureSpec = View.MeasureSpec.makeMeasureSpec(0, 0);
        ViewGroup viewGroup = (ViewGroup) this.f324q;
        int i10 = 0;
        boolean z6 = false;
        int i11 = 0;
        int i12 = 0;
        while (true) {
            i7 = 2;
            z5 = true;
            if (i10 >= i6) {
                break;
            }
            C0072g c0072g = arrayList.get(i10);
            int i13 = c0072g.f430y;
            if ((i13 & 2) == 2) {
                i11++;
            } else if ((i13 & 1) == 1) {
                i12++;
            } else {
                z6 = true;
            }
            if (this.f727z && c0072g.f405C) {
                i8 = 0;
            }
            i10++;
        }
        if (this.f722u && (z6 || i12 + i11 > i8)) {
            i8--;
        }
        int i14 = i8 - i11;
        SparseBooleanArray sparseBooleanArray = this.f713A;
        sparseBooleanArray.clear();
        int i15 = 0;
        int i16 = 0;
        while (i15 < i6) {
            C0072g c0072g2 = arrayList.get(i15);
            int i17 = c0072g2.f430y;
            if ((i17 & 2) == i7 ? z5 : false) {
                View m318f = m318f(c0072g2, null, viewGroup);
                m318f.measure(makeMeasureSpec, makeMeasureSpec);
                int measuredWidth = m318f.getMeasuredWidth();
                i9 -= measuredWidth;
                if (i16 == 0) {
                    i16 = measuredWidth;
                }
                int i18 = c0072g2.f407b;
                if (i18 != 0) {
                    sparseBooleanArray.put(i18, z5);
                }
                c0072g2.m218k(z5);
            } else if ((i17 & 1) == z5 ? z5 : false) {
                int i19 = c0072g2.f407b;
                boolean z7 = sparseBooleanArray.get(i19);
                boolean z8 = ((i14 > 0 || z7) && i9 > 0) ? z5 : false;
                if (z8) {
                    View m318f2 = m318f(c0072g2, null, viewGroup);
                    m318f2.measure(makeMeasureSpec, makeMeasureSpec);
                    int measuredWidth2 = m318f2.getMeasuredWidth();
                    i9 -= measuredWidth2;
                    if (i16 == 0) {
                        i16 = measuredWidth2;
                    }
                    z8 &= i9 + i16 > 0;
                }
                if (z8 && i19 != 0) {
                    sparseBooleanArray.put(i19, true);
                } else if (z7) {
                    sparseBooleanArray.put(i19, false);
                    for (int i20 = 0; i20 < i15; i20++) {
                        C0072g c0072g3 = arrayList.get(i20);
                        if (c0072g3.f407b == i19) {
                            if (c0072g3.m214g()) {
                                i14++;
                            }
                            c0072g3.m218k(false);
                        }
                    }
                }
                if (z8) {
                    i14--;
                }
                c0072g2.m218k(z8);
            } else {
                c0072g2.m218k(false);
                i15++;
                i7 = 2;
                z5 = true;
            }
            i15++;
            i7 = 2;
            z5 = true;
        }
        return z5;
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: e */
    public final void mo171e(Context context, C0070e c0070e) {
        this.f318k = context;
        LayoutInflater.from(context);
        this.f319l = c0070e;
        Resources resources = context.getResources();
        if (!this.f723v) {
            this.f722u = true;
        }
        int i6 = 2;
        this.f724w = context.getResources().getDisplayMetrics().widthPixels / 2;
        Configuration configuration = context.getResources().getConfiguration();
        int i7 = configuration.screenWidthDp;
        int i8 = configuration.screenHeightDp;
        if (configuration.smallestScreenWidthDp > 600 || i7 > 600 || ((i7 > 960 && i8 > 720) || (i7 > 720 && i8 > 960))) {
            i6 = 5;
        } else if (i7 >= 500 || ((i7 > 640 && i8 > 480) || (i7 > 480 && i8 > 640))) {
            i6 = 4;
        } else if (i7 >= 360) {
            i6 = 3;
        }
        this.f726y = i6;
        int i9 = this.f724w;
        if (this.f722u) {
            if (this.f719r == null) {
                d dVar = new d(this.f317j);
                this.f719r = dVar;
                if (this.f721t) {
                    dVar.setImageDrawable(this.f720s);
                    this.f720s = null;
                    this.f721t = false;
                }
                int makeMeasureSpec = View.MeasureSpec.makeMeasureSpec(0, 0);
                this.f719r.measure(makeMeasureSpec, makeMeasureSpec);
            }
            i9 -= this.f719r.getMeasuredWidth();
        } else {
            this.f719r = null;
        }
        this.f725x = i9;
        float f6 = resources.getDisplayMetrics().density;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r5v0, types: [android.view.View] */
    /* JADX WARN: Type inference failed for: r5v4, types: [androidx.appcompat.view.menu.j$a] */
    /* JADX WARN: Type inference failed for: r5v7 */
    /* JADX WARN: Type inference failed for: r5v8 */
    /* renamed from: f */
    public final View m318f(C0072g c0072g, View view, ViewGroup viewGroup) {
        View actionView = c0072g.getActionView();
        if (actionView == null || c0072g.m213f()) {
            ActionMenuItemView actionMenuItemView = view instanceof InterfaceC0075j.a ? (InterfaceC0075j.a) view : (InterfaceC0075j.a) this.f320m.inflate(this.f323p, viewGroup, false);
            actionMenuItemView.mo138b(c0072g);
            ActionMenuItemView actionMenuItemView2 = actionMenuItemView;
            actionMenuItemView2.setItemInvoker((ActionMenuView) this.f324q);
            if (this.f717E == null) {
                this.f717E = new b();
            }
            actionMenuItemView2.setPopupCallback(this.f717E);
            actionView = actionMenuItemView;
        }
        actionView.setVisibility(c0072g.f405C ? 8 : 0);
        ActionMenuView actionMenuView = (ActionMenuView) viewGroup;
        ViewGroup.LayoutParams layoutParams = actionView.getLayoutParams();
        if (!actionMenuView.checkLayoutParams(layoutParams)) {
            actionView.setLayoutParams(actionMenuView.generateLayoutParams(layoutParams));
        }
        return actionView;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: g */
    public final void mo156g() {
        int i6;
        boolean z5;
        ViewGroup viewGroup = (ViewGroup) this.f324q;
        ArrayList<C0072g> arrayList = null;
        boolean z6 = false;
        if (viewGroup != null) {
            C0070e c0070e = this.f319l;
            if (c0070e != null) {
                c0070e.m187j();
                ArrayList<C0072g> m190m = this.f319l.m190m();
                int size = m190m.size();
                i6 = 0;
                for (int i7 = 0; i7 < size; i7++) {
                    C0072g c0072g = m190m.get(i7);
                    if (c0072g.m214g()) {
                        View childAt = viewGroup.getChildAt(i6);
                        C0072g itemData = childAt instanceof InterfaceC0075j.a ? ((InterfaceC0075j.a) childAt).getItemData() : null;
                        View m318f = m318f(c0072g, childAt, viewGroup);
                        if (c0072g != itemData) {
                            m318f.setPressed(false);
                            m318f.jumpDrawablesToCurrentState();
                        }
                        if (m318f != childAt) {
                            ViewGroup viewGroup2 = (ViewGroup) m318f.getParent();
                            if (viewGroup2 != null) {
                                viewGroup2.removeView(m318f);
                            }
                            ((ViewGroup) this.f324q).addView(m318f, i6);
                        }
                        i6++;
                    }
                }
            } else {
                i6 = 0;
            }
            while (i6 < viewGroup.getChildCount()) {
                if (viewGroup.getChildAt(i6) == this.f719r) {
                    z5 = false;
                } else {
                    viewGroup.removeViewAt(i6);
                    z5 = true;
                }
                if (!z5) {
                    i6++;
                }
            }
        }
        ((View) this.f324q).requestLayout();
        C0070e c0070e2 = this.f319l;
        if (c0070e2 != null) {
            c0070e2.m187j();
            ArrayList<C0072g> arrayList2 = c0070e2.f384i;
            int size2 = arrayList2.size();
            for (int i8 = 0; i8 < size2; i8++) {
                AbstractC0752b abstractC0752b = arrayList2.get(i8).f403A;
            }
        }
        C0070e c0070e3 = this.f319l;
        if (c0070e3 != null) {
            c0070e3.m187j();
            arrayList = c0070e3.f385j;
        }
        if (this.f722u && arrayList != null) {
            int size3 = arrayList.size();
            if (size3 == 1) {
                z6 = !arrayList.get(0).f405C;
            } else if (size3 > 0) {
                z6 = true;
            }
        }
        d dVar = this.f719r;
        if (z6) {
            if (dVar == null) {
                this.f719r = new d(this.f317j);
            }
            ViewGroup viewGroup3 = (ViewGroup) this.f719r.getParent();
            if (viewGroup3 != this.f324q) {
                if (viewGroup3 != null) {
                    viewGroup3.removeView(this.f719r);
                }
                ActionMenuView actionMenuView = (ActionMenuView) this.f324q;
                d dVar2 = this.f719r;
                ActionMenuView.C0086c generateDefaultLayoutParams = actionMenuView.generateDefaultLayoutParams();
                generateDefaultLayoutParams.f536a = true;
                actionMenuView.addView(dVar2, generateDefaultLayoutParams);
            }
        } else if (dVar != null) {
            Object parent = dVar.getParent();
            Object obj = this.f324q;
            if (parent == obj) {
                ((ViewGroup) obj).removeView(this.f719r);
            }
        }
        ((ActionMenuView) this.f324q).setOverflowReserved(this.f722u);
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // androidx.appcompat.view.menu.InterfaceC0074i
    /* renamed from: j */
    public final boolean mo157j(SubMenuC0077l subMenuC0077l) {
        boolean z5 = false;
        if (!subMenuC0077l.hasVisibleItems()) {
            return false;
        }
        SubMenuC0077l subMenuC0077l2 = subMenuC0077l;
        while (true) {
            C0070e c0070e = subMenuC0077l2.f469z;
            if (c0070e == this.f319l) {
                break;
            }
            subMenuC0077l2 = (SubMenuC0077l) c0070e;
        }
        C0072g c0072g = subMenuC0077l2.f468A;
        ViewGroup viewGroup = (ViewGroup) this.f324q;
        View view = null;
        if (viewGroup != null) {
            int childCount = viewGroup.getChildCount();
            int i6 = 0;
            while (true) {
                if (i6 >= childCount) {
                    break;
                }
                View childAt = viewGroup.getChildAt(i6);
                if ((childAt instanceof InterfaceC0075j.a) && ((InterfaceC0075j.a) childAt).getItemData() == c0072g) {
                    view = childAt;
                    break;
                }
                i6++;
            }
        }
        if (view == null) {
            return false;
        }
        Objects.requireNonNull(subMenuC0077l.f468A);
        int size = subMenuC0077l.size();
        int i7 = 0;
        while (true) {
            if (i7 >= size) {
                break;
            }
            MenuItem item = subMenuC0077l.getItem(i7);
            if (item.isVisible() && item.getIcon() != null) {
                z5 = true;
                break;
            }
            i7++;
        }
        a aVar = new a(this.f318k, subMenuC0077l, view);
        this.f715C = aVar;
        aVar.f440h = z5;
        AbstractC0896d abstractC0896d = aVar.f442j;
        if (abstractC0896d != null) {
            abstractC0896d.mo161o(z5);
        }
        this.f715C.m223e();
        InterfaceC0074i.a aVar2 = this.f321n;
        if (aVar2 != null) {
            aVar2.mo207b(subMenuC0077l);
        }
        return true;
    }

    /* renamed from: k */
    public final boolean m319k() {
        Object obj;
        c cVar = this.f716D;
        if (cVar != null && (obj = this.f324q) != null) {
            ((View) obj).removeCallbacks(cVar);
            this.f716D = null;
            return true;
        }
        e eVar = this.f714B;
        if (eVar == null) {
            return false;
        }
        if (eVar.m220b()) {
            eVar.f442j.dismiss();
        }
        return true;
    }

    /* renamed from: l */
    public final boolean m320l() {
        e eVar = this.f714B;
        return eVar != null && eVar.m220b();
    }

    /* renamed from: m */
    public final boolean m321m() {
        C0070e c0070e;
        if (!this.f722u || m320l() || (c0070e = this.f319l) == null || this.f324q == null || this.f716D != null) {
            return false;
        }
        c0070e.m187j();
        if (c0070e.f385j.isEmpty()) {
            return false;
        }
        c cVar = new c(new e(this.f318k, this.f319l, this.f719r));
        this.f716D = cVar;
        ((View) this.f324q).post(cVar);
        return true;
    }
}
