package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.ActionMode;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import com.liaoyuan.aicast.R;
import java.util.WeakHashMap;
import p008b0.C0385m;
import p029e0.C0766p;
import p029e0.C0769s;

/* loaded from: classes.dex */
public class ActionBarContainer extends FrameLayout {

    /* renamed from: j */
    public boolean f470j;

    /* renamed from: k */
    public C0169v0 f471k;

    /* renamed from: l */
    public View f472l;

    /* renamed from: m */
    public View f473m;

    /* renamed from: n */
    public Drawable f474n;

    /* renamed from: o */
    public Drawable f475o;

    /* renamed from: p */
    public Drawable f476p;

    /* renamed from: q */
    public boolean f477q;

    /* renamed from: r */
    public boolean f478r;

    /* renamed from: s */
    public int f479s;

    public ActionBarContainer(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        C0118b c0118b = new C0118b(this);
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        setBackground(c0118b);
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C0385m.f2347k);
        boolean z5 = false;
        this.f474n = obtainStyledAttributes.getDrawable(0);
        this.f475o = obtainStyledAttributes.getDrawable(2);
        this.f479s = obtainStyledAttributes.getDimensionPixelSize(13, -1);
        if (getId() == R.id.split_action_bar) {
            this.f477q = true;
            this.f476p = obtainStyledAttributes.getDrawable(1);
        }
        obtainStyledAttributes.recycle();
        if (!this.f477q ? !(this.f474n != null || this.f475o != null) : this.f476p == null) {
            z5 = true;
        }
        setWillNotDraw(z5);
    }

    /* renamed from: a */
    public final int m225a(View view) {
        FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) view.getLayoutParams();
        return view.getMeasuredHeight() + layoutParams.topMargin + layoutParams.bottomMargin;
    }

    /* renamed from: b */
    public final boolean m226b(View view) {
        return view == null || view.getVisibility() == 8 || view.getMeasuredHeight() == 0;
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        Drawable drawable = this.f474n;
        if (drawable != null && drawable.isStateful()) {
            this.f474n.setState(getDrawableState());
        }
        Drawable drawable2 = this.f475o;
        if (drawable2 != null && drawable2.isStateful()) {
            this.f475o.setState(getDrawableState());
        }
        Drawable drawable3 = this.f476p;
        if (drawable3 == null || !drawable3.isStateful()) {
            return;
        }
        this.f476p.setState(getDrawableState());
    }

    public View getTabContainer() {
        return this.f471k;
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void jumpDrawablesToCurrentState() {
        super.jumpDrawablesToCurrentState();
        Drawable drawable = this.f474n;
        if (drawable != null) {
            drawable.jumpToCurrentState();
        }
        Drawable drawable2 = this.f475o;
        if (drawable2 != null) {
            drawable2.jumpToCurrentState();
        }
        Drawable drawable3 = this.f476p;
        if (drawable3 != null) {
            drawable3.jumpToCurrentState();
        }
    }

    @Override // android.view.View
    public final void onFinishInflate() {
        super.onFinishInflate();
        this.f472l = findViewById(R.id.action_bar);
        this.f473m = findViewById(R.id.action_context_bar);
    }

    @Override // android.view.View
    public final boolean onHoverEvent(MotionEvent motionEvent) {
        super.onHoverEvent(motionEvent);
        return true;
    }

    @Override // android.view.ViewGroup
    public final boolean onInterceptTouchEvent(MotionEvent motionEvent) {
        return this.f470j || super.onInterceptTouchEvent(motionEvent);
    }

    @Override // android.widget.FrameLayout, android.view.ViewGroup, android.view.View
    public final void onLayout(boolean z5, int i6, int i7, int i8, int i9) {
        Drawable drawable;
        Drawable drawable2;
        int left;
        int top;
        int right;
        View view;
        super.onLayout(z5, i6, i7, i8, i9);
        C0169v0 c0169v0 = this.f471k;
        boolean z6 = true;
        boolean z7 = false;
        boolean z8 = (c0169v0 == null || c0169v0.getVisibility() == 8) ? false : true;
        if (c0169v0 != null && c0169v0.getVisibility() != 8) {
            int measuredHeight = getMeasuredHeight();
            FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) c0169v0.getLayoutParams();
            int measuredHeight2 = measuredHeight - c0169v0.getMeasuredHeight();
            int i10 = layoutParams.bottomMargin;
            c0169v0.layout(i6, measuredHeight2 - i10, i8, measuredHeight - i10);
        }
        if (this.f477q) {
            Drawable drawable3 = this.f476p;
            if (drawable3 != null) {
                drawable3.setBounds(0, 0, getMeasuredWidth(), getMeasuredHeight());
            }
            z6 = z7;
        } else {
            if (this.f474n != null) {
                if (this.f472l.getVisibility() == 0) {
                    drawable2 = this.f474n;
                    left = this.f472l.getLeft();
                    top = this.f472l.getTop();
                    right = this.f472l.getRight();
                    view = this.f472l;
                } else {
                    View view2 = this.f473m;
                    if (view2 == null || view2.getVisibility() != 0) {
                        this.f474n.setBounds(0, 0, 0, 0);
                        z7 = true;
                    } else {
                        drawable2 = this.f474n;
                        left = this.f473m.getLeft();
                        top = this.f473m.getTop();
                        right = this.f473m.getRight();
                        view = this.f473m;
                    }
                }
                drawable2.setBounds(left, top, right, view.getBottom());
                z7 = true;
            }
            this.f478r = z8;
            if (z8 && (drawable = this.f475o) != null) {
                drawable.setBounds(c0169v0.getLeft(), c0169v0.getTop(), c0169v0.getRight(), c0169v0.getBottom());
            }
            z6 = z7;
        }
        if (z6) {
            invalidate();
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:23:0x0055  */
    /* JADX WARN: Removed duplicated region for block: B:26:0x005a  */
    @Override // android.widget.FrameLayout, android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void onMeasure(int r4, int r5) {
        /*
            r3 = this;
            android.view.View r0 = r3.f472l
            r1 = -2147483648(0xffffffff80000000, float:-0.0)
            if (r0 != 0) goto L1c
            int r0 = android.view.View.MeasureSpec.getMode(r5)
            if (r0 != r1) goto L1c
            int r0 = r3.f479s
            if (r0 < 0) goto L1c
            int r5 = android.view.View.MeasureSpec.getSize(r5)
            int r5 = java.lang.Math.min(r0, r5)
            int r5 = android.view.View.MeasureSpec.makeMeasureSpec(r5, r1)
        L1c:
            super.onMeasure(r4, r5)
            android.view.View r4 = r3.f472l
            if (r4 != 0) goto L24
            return
        L24:
            int r4 = android.view.View.MeasureSpec.getMode(r5)
            androidx.appcompat.widget.v0 r0 = r3.f471k
            if (r0 == 0) goto L6f
            int r0 = r0.getVisibility()
            r2 = 8
            if (r0 == r2) goto L6f
            r0 = 1073741824(0x40000000, float:2.0)
            if (r4 == r0) goto L6f
            android.view.View r0 = r3.f472l
            boolean r0 = r3.m226b(r0)
            if (r0 != 0) goto L43
            android.view.View r0 = r3.f472l
            goto L4d
        L43:
            android.view.View r0 = r3.f473m
            boolean r0 = r3.m226b(r0)
            if (r0 != 0) goto L52
            android.view.View r0 = r3.f473m
        L4d:
            int r0 = r3.m225a(r0)
            goto L53
        L52:
            r0 = 0
        L53:
            if (r4 != r1) goto L5a
            int r4 = android.view.View.MeasureSpec.getSize(r5)
            goto L5d
        L5a:
            r4 = 2147483647(0x7fffffff, float:NaN)
        L5d:
            int r5 = r3.getMeasuredWidth()
            androidx.appcompat.widget.v0 r1 = r3.f471k
            int r1 = r3.m225a(r1)
            int r1 = r1 + r0
            int r4 = java.lang.Math.min(r1, r4)
            r3.setMeasuredDimension(r5, r4)
        L6f:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.ActionBarContainer.onMeasure(int, int):void");
    }

    @Override // android.view.View
    public final boolean onTouchEvent(MotionEvent motionEvent) {
        super.onTouchEvent(motionEvent);
        return true;
    }

    public void setPrimaryBackground(Drawable drawable) {
        Drawable drawable2 = this.f474n;
        if (drawable2 != null) {
            drawable2.setCallback(null);
            unscheduleDrawable(this.f474n);
        }
        this.f474n = drawable;
        if (drawable != null) {
            drawable.setCallback(this);
            View view = this.f472l;
            if (view != null) {
                this.f474n.setBounds(view.getLeft(), this.f472l.getTop(), this.f472l.getRight(), this.f472l.getBottom());
            }
        }
        boolean z5 = true;
        if (!this.f477q ? this.f474n != null || this.f475o != null : this.f476p != null) {
            z5 = false;
        }
        setWillNotDraw(z5);
        invalidate();
        invalidateOutline();
    }

    public void setSplitBackground(Drawable drawable) {
        Drawable drawable2;
        Drawable drawable3 = this.f476p;
        if (drawable3 != null) {
            drawable3.setCallback(null);
            unscheduleDrawable(this.f476p);
        }
        this.f476p = drawable;
        boolean z5 = false;
        if (drawable != null) {
            drawable.setCallback(this);
            if (this.f477q && (drawable2 = this.f476p) != null) {
                drawable2.setBounds(0, 0, getMeasuredWidth(), getMeasuredHeight());
            }
        }
        if (!this.f477q ? !(this.f474n != null || this.f475o != null) : this.f476p == null) {
            z5 = true;
        }
        setWillNotDraw(z5);
        invalidate();
        invalidateOutline();
    }

    public void setStackedBackground(Drawable drawable) {
        Drawable drawable2;
        Drawable drawable3 = this.f475o;
        if (drawable3 != null) {
            drawable3.setCallback(null);
            unscheduleDrawable(this.f475o);
        }
        this.f475o = drawable;
        if (drawable != null) {
            drawable.setCallback(this);
            if (this.f478r && (drawable2 = this.f475o) != null) {
                drawable2.setBounds(this.f471k.getLeft(), this.f471k.getTop(), this.f471k.getRight(), this.f471k.getBottom());
            }
        }
        boolean z5 = true;
        if (!this.f477q ? this.f474n != null || this.f475o != null : this.f476p != null) {
            z5 = false;
        }
        setWillNotDraw(z5);
        invalidate();
        invalidateOutline();
    }

    public void setTabContainer(C0169v0 c0169v0) {
        C0169v0 c0169v02 = this.f471k;
        if (c0169v02 != null) {
            removeView(c0169v02);
        }
        this.f471k = c0169v0;
        if (c0169v0 != null) {
            addView(c0169v0);
            ViewGroup.LayoutParams layoutParams = c0169v0.getLayoutParams();
            layoutParams.width = -1;
            layoutParams.height = -2;
            c0169v0.setAllowCollapse(false);
        }
    }

    public void setTransitioning(boolean z5) {
        this.f470j = z5;
        setDescendantFocusability(z5 ? 393216 : 262144);
    }

    @Override // android.view.View
    public void setVisibility(int i6) {
        super.setVisibility(i6);
        boolean z5 = i6 == 0;
        Drawable drawable = this.f474n;
        if (drawable != null) {
            drawable.setVisible(z5, false);
        }
        Drawable drawable2 = this.f475o;
        if (drawable2 != null) {
            drawable2.setVisible(z5, false);
        }
        Drawable drawable3 = this.f476p;
        if (drawable3 != null) {
            drawable3.setVisible(z5, false);
        }
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final ActionMode startActionModeForChild(View view, ActionMode.Callback callback) {
        return null;
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final ActionMode startActionModeForChild(View view, ActionMode.Callback callback, int i6) {
        if (i6 != 0) {
            return super.startActionModeForChild(view, callback, i6);
        }
        return null;
    }

    @Override // android.view.View
    public final boolean verifyDrawable(Drawable drawable) {
        return (drawable == this.f474n && !this.f477q) || (drawable == this.f475o && this.f478r) || ((drawable == this.f476p && this.f477q) || super.verifyDrawable(drawable));
    }
}
