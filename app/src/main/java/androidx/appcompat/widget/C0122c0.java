package androidx.appcompat.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.RectF;
import android.os.Build;
import android.text.Layout;
import android.text.StaticLayout;
import android.text.TextDirectionHeuristic;
import android.text.TextDirectionHeuristics;
import android.text.TextPaint;
import android.text.method.TransformationMethod;
import android.util.Log;
import android.util.TypedValue;
import android.widget.TextView;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.concurrent.ConcurrentHashMap;

/* renamed from: androidx.appcompat.widget.c0 */
/* loaded from: classes.dex */
public final class C0122c0 {

    /* renamed from: l */
    public static final RectF f736l = new RectF();

    /* renamed from: m */
    @SuppressLint({"BanConcurrentHashMap"})
    public static ConcurrentHashMap<String, Method> f737m = new ConcurrentHashMap<>();

    /* renamed from: n */
    @SuppressLint({"BanConcurrentHashMap"})
    public static ConcurrentHashMap<String, Field> f738n = new ConcurrentHashMap<>();

    /* renamed from: a */
    public int f739a = 0;

    /* renamed from: b */
    public boolean f740b = false;

    /* renamed from: c */
    public float f741c = -1.0f;

    /* renamed from: d */
    public float f742d = -1.0f;

    /* renamed from: e */
    public float f743e = -1.0f;

    /* renamed from: f */
    public int[] f744f = new int[0];

    /* renamed from: g */
    public boolean f745g = false;

    /* renamed from: h */
    public TextPaint f746h;

    /* renamed from: i */
    public final TextView f747i;

    /* renamed from: j */
    public final Context f748j;

    /* renamed from: k */
    public final a f749k;

    /* renamed from: androidx.appcompat.widget.c0$a */
    public static class a extends c {
        /* renamed from: b */
        public void mo333b(StaticLayout.Builder builder, TextView textView) {
            builder.setTextDirection((TextDirectionHeuristic) C0122c0.m324e(textView, "getTextDirectionHeuristic", TextDirectionHeuristics.FIRSTSTRONG_LTR));
        }
    }

    /* renamed from: androidx.appcompat.widget.c0$b */
    public static class b extends a {
        @Override // androidx.appcompat.widget.C0122c0.c
        /* renamed from: a */
        public boolean mo334a(TextView textView) {
            return textView.isHorizontallyScrollable();
        }

        @Override // androidx.appcompat.widget.C0122c0.a
        /* renamed from: b */
        public void mo333b(StaticLayout.Builder builder, TextView textView) {
            builder.setTextDirection(textView.getTextDirectionHeuristic());
        }
    }

    /* renamed from: androidx.appcompat.widget.c0$c */
    public static class c {
        /* renamed from: a */
        public boolean mo334a(TextView textView) {
            return ((Boolean) C0122c0.m324e(textView, "getHorizontallyScrolling", Boolean.FALSE)).booleanValue();
        }
    }

    public C0122c0(TextView textView) {
        this.f747i = textView;
        this.f748j = textView.getContext();
        this.f749k = Build.VERSION.SDK_INT >= 29 ? new b() : new a();
    }

    /* renamed from: d */
    public static Method m323d(String str) {
        try {
            Method method = f737m.get(str);
            if (method == null && (method = TextView.class.getDeclaredMethod(str, new Class[0])) != null) {
                method.setAccessible(true);
                f737m.put(str, method);
            }
            return method;
        } catch (Exception e6) {
            Log.w("ACTVAutoSizeHelper", "Failed to retrieve TextView#" + str + "() method", e6);
            return null;
        }
    }

    /* renamed from: e */
    public static <T> T m324e(Object obj, String str, T t) {
        try {
            return (T) m323d(str).invoke(obj, new Object[0]);
        } catch (Exception e6) {
            Log.w("ACTVAutoSizeHelper", "Failed to invoke TextView#" + str + "() method", e6);
            return t;
        }
    }

    /* renamed from: a */
    public final void m325a() {
        if (m331i() && this.f739a != 0) {
            if (this.f740b) {
                if (this.f747i.getMeasuredHeight() <= 0 || this.f747i.getMeasuredWidth() <= 0) {
                    return;
                }
                int measuredWidth = this.f749k.mo334a(this.f747i) ? 1048576 : (this.f747i.getMeasuredWidth() - this.f747i.getTotalPaddingLeft()) - this.f747i.getTotalPaddingRight();
                int height = (this.f747i.getHeight() - this.f747i.getCompoundPaddingBottom()) - this.f747i.getCompoundPaddingTop();
                if (measuredWidth <= 0 || height <= 0) {
                    return;
                }
                RectF rectF = f736l;
                synchronized (rectF) {
                    rectF.setEmpty();
                    rectF.right = measuredWidth;
                    rectF.bottom = height;
                    float m327c = m327c(rectF);
                    if (m327c != this.f747i.getTextSize()) {
                        m328f(0, m327c);
                    }
                }
            }
            this.f740b = true;
        }
    }

    /* renamed from: b */
    public final int[] m326b(int[] iArr) {
        int length = iArr.length;
        if (length == 0) {
            return iArr;
        }
        Arrays.sort(iArr);
        ArrayList arrayList = new ArrayList();
        for (int i6 : iArr) {
            if (i6 > 0 && Collections.binarySearch(arrayList, Integer.valueOf(i6)) < 0) {
                arrayList.add(Integer.valueOf(i6));
            }
        }
        if (length == arrayList.size()) {
            return iArr;
        }
        int size = arrayList.size();
        int[] iArr2 = new int[size];
        for (int i7 = 0; i7 < size; i7++) {
            iArr2[i7] = ((Integer) arrayList.get(i7)).intValue();
        }
        return iArr2;
    }

    /* renamed from: c */
    public final int m327c(RectF rectF) {
        CharSequence transformation;
        int length = this.f744f.length;
        if (length == 0) {
            throw new IllegalStateException("No available text sizes to choose from.");
        }
        int i6 = length - 1;
        int i7 = 1;
        int i8 = 0;
        while (i7 <= i6) {
            int i9 = (i7 + i6) / 2;
            int i10 = this.f744f[i9];
            CharSequence text = this.f747i.getText();
            TransformationMethod transformationMethod = this.f747i.getTransformationMethod();
            if (transformationMethod != null && (transformation = transformationMethod.getTransformation(text, this.f747i)) != null) {
                text = transformation;
            }
            int maxLines = this.f747i.getMaxLines();
            TextPaint textPaint = this.f746h;
            if (textPaint == null) {
                this.f746h = new TextPaint();
            } else {
                textPaint.reset();
            }
            this.f746h.set(this.f747i.getPaint());
            this.f746h.setTextSize(i10);
            Layout.Alignment alignment = (Layout.Alignment) m324e(this.f747i, "getLayoutAlignment", Layout.Alignment.ALIGN_NORMAL);
            StaticLayout.Builder obtain = StaticLayout.Builder.obtain(text, 0, text.length(), this.f746h, Math.round(rectF.right));
            obtain.setAlignment(alignment).setLineSpacing(this.f747i.getLineSpacingExtra(), this.f747i.getLineSpacingMultiplier()).setIncludePad(this.f747i.getIncludeFontPadding()).setBreakStrategy(this.f747i.getBreakStrategy()).setHyphenationFrequency(this.f747i.getHyphenationFrequency()).setMaxLines(maxLines == -1 ? Integer.MAX_VALUE : maxLines);
            try {
                this.f749k.mo333b(obtain, this.f747i);
            } catch (ClassCastException unused) {
                Log.w("ACTVAutoSizeHelper", "Failed to obtain TextDirectionHeuristic, auto size may be incorrect");
            }
            StaticLayout build = obtain.build();
            if ((maxLines == -1 || (build.getLineCount() <= maxLines && build.getLineEnd(build.getLineCount() - 1) == text.length())) && ((float) build.getHeight()) <= rectF.bottom) {
                int i11 = i9 + 1;
                i8 = i7;
                i7 = i11;
            } else {
                i8 = i9 - 1;
                i6 = i8;
            }
        }
        return this.f744f[i8];
    }

    /* renamed from: f */
    public final void m328f(int i6, float f6) {
        Context context = this.f748j;
        float applyDimension = TypedValue.applyDimension(i6, f6, (context == null ? Resources.getSystem() : context.getResources()).getDisplayMetrics());
        if (applyDimension != this.f747i.getPaint().getTextSize()) {
            this.f747i.getPaint().setTextSize(applyDimension);
            boolean isInLayout = this.f747i.isInLayout();
            if (this.f747i.getLayout() != null) {
                this.f740b = false;
                try {
                    Method m323d = m323d("nullLayouts");
                    if (m323d != null) {
                        m323d.invoke(this.f747i, new Object[0]);
                    }
                } catch (Exception e6) {
                    Log.w("ACTVAutoSizeHelper", "Failed to invoke TextView#nullLayouts() method", e6);
                }
                if (isInLayout) {
                    this.f747i.forceLayout();
                } else {
                    this.f747i.requestLayout();
                }
                this.f747i.invalidate();
            }
        }
    }

    /* renamed from: g */
    public final boolean m329g() {
        if (m331i() && this.f739a == 1) {
            if (!this.f745g || this.f744f.length == 0) {
                int floor = ((int) Math.floor((this.f743e - this.f742d) / this.f741c)) + 1;
                int[] iArr = new int[floor];
                for (int i6 = 0; i6 < floor; i6++) {
                    iArr[i6] = Math.round((i6 * this.f741c) + this.f742d);
                }
                this.f744f = m326b(iArr);
            }
            this.f740b = true;
        } else {
            this.f740b = false;
        }
        return this.f740b;
    }

    /* renamed from: h */
    public final boolean m330h() {
        boolean z5 = this.f744f.length > 0;
        this.f745g = z5;
        if (z5) {
            this.f739a = 1;
            this.f742d = r0[0];
            this.f743e = r0[r1 - 1];
            this.f741c = -1.0f;
        }
        return z5;
    }

    /* renamed from: i */
    public final boolean m331i() {
        return !(this.f747i instanceof C0145k);
    }

    /* renamed from: j */
    public final void m332j(float f6, float f7, float f8) {
        if (f6 <= 0.0f) {
            throw new IllegalArgumentException("Minimum auto-size text size (" + f6 + "px) is less or equal to (0px)");
        }
        if (f7 <= f6) {
            throw new IllegalArgumentException("Maximum auto-size text size (" + f7 + "px) is less or equal to minimum auto-size text size (" + f6 + "px)");
        }
        if (f8 <= 0.0f) {
            throw new IllegalArgumentException("The auto-size step granularity (" + f8 + "px) is less or equal to (0px)");
        }
        this.f739a = 1;
        this.f742d = f6;
        this.f743e = f7;
        this.f741c = f8;
        this.f745g = false;
    }
}
