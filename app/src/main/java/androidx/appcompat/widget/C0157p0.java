package androidx.appcompat.widget;

import android.view.MenuItem;
import androidx.appcompat.app.AlertController;
import androidx.appcompat.view.menu.C0070e;
import androidx.appcompat.widget.C0161r0;
import com.liaoyuan.aicast.R;
import com.liaoyuan.aicast.phone.browser.view.BrowserActivity;
import java.util.Objects;
import p031e2.C0800p;
import p081l3.C1064b;
import p127s4.DialogInterfaceOnClickListenerC1342a;
import p127s4.DialogInterfaceOnClickListenerC1343b;
import p127s4.DialogInterfaceOnClickListenerC1344c;
import p127s4.DialogInterfaceOnClickListenerC1345d;

/* renamed from: androidx.appcompat.widget.p0 */
/* loaded from: classes.dex */
public final class C0157p0 implements C0070e.a {

    /* renamed from: j */
    public final /* synthetic */ C0161r0 f924j;

    public C0157p0(C0161r0 c0161r0) {
        this.f924j = c0161r0;
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    @Override // androidx.appcompat.view.menu.C0070e.a
    /* renamed from: a */
    public final boolean mo204a(C0070e c0070e, MenuItem menuItem) {
        C0161r0.a aVar = this.f924j.f932c;
        if (aVar != null) {
            BrowserActivity browserActivity = (BrowserActivity) ((C0800p) aVar).f4181b;
            int i6 = BrowserActivity.f3456G;
            Objects.requireNonNull(browserActivity);
            switch (menuItem.getItemId()) {
                case R.id.item_clear_cache /* 2131296466 */:
                    C1064b c1064b = new C1064b(browserActivity);
                    c1064b.f284a.f267d = browserActivity.getResources().getString(R.string.online_clear_cache);
                    c1064b.f284a.f269f = browserActivity.getResources().getString(R.string.online_clear_cache_message);
                    c1064b.m2717c(browserActivity.getResources().getString(R.string.devices_dialog_cancel), DialogInterfaceOnClickListenerC1345d.f6240l);
                    c1064b.m2718d(browserActivity.getResources().getString(R.string.devices_dialog_confirm), new DialogInterfaceOnClickListenerC1342a(browserActivity, 2));
                    c1064b.mo135a();
                    c1064b.m136b().getWindow().setBackgroundDrawableResource(R.drawable.dialog_bg);
                    browserActivity.m1904w(true);
                    break;
                case R.id.item_managing_collections /* 2131296467 */:
                    browserActivity.m1904w(false);
                    browserActivity.m1905x(false);
                    break;
                case R.id.item_switch_engines /* 2131296468 */:
                    C1064b c1064b2 = new C1064b(browserActivity);
                    String[] stringArray = browserActivity.getResources().getStringArray(R.array.default_browser);
                    int m3233a = browserActivity.f3459C.f8492g.f6163c.m3233a();
                    String string = browserActivity.getResources().getString(R.string.online_item_switch_engines);
                    AlertController.C0059b c0059b = c1064b2.f284a;
                    c0059b.f267d = string;
                    DialogInterfaceOnClickListenerC1343b dialogInterfaceOnClickListenerC1343b = new DialogInterfaceOnClickListenerC1343b(browserActivity, 0);
                    c0059b.f275l = stringArray;
                    c0059b.f277n = dialogInterfaceOnClickListenerC1343b;
                    c0059b.f279p = m3233a;
                    c0059b.f278o = true;
                    c1064b2.m2717c(browserActivity.getResources().getString(R.string.devices_dialog_cancel), DialogInterfaceOnClickListenerC1344c.f6235k);
                    c1064b2.m2718d(browserActivity.getResources().getString(R.string.devices_dialog_confirm), new DialogInterfaceOnClickListenerC1343b(browserActivity, 1));
                    c1064b2.mo135a();
                    c1064b2.m136b().getWindow().setBackgroundDrawableResource(R.drawable.dialog_bg);
                    browserActivity.m1904w(true);
                    break;
                case R.id.item_switch_mute /* 2131296469 */:
                    browserActivity.f3459C.m5307g(true);
                    break;
                case R.id.item_switch_phone /* 2131296470 */:
                    browserActivity.f3459C.m5307g(false);
                    browserActivity.f3459C.m5315o(false);
                    break;
                case R.id.item_video_size /* 2131296472 */:
                    C1064b c1064b3 = new C1064b(browserActivity);
                    String[] stringArray2 = browserActivity.getResources().getStringArray(R.array.video_size);
                    int m3232a = browserActivity.f3459C.f8501p.m3232a();
                    String string2 = browserActivity.getResources().getString(R.string.online_item_video_size);
                    AlertController.C0059b c0059b2 = c1064b3.f284a;
                    c0059b2.f267d = string2;
                    DialogInterfaceOnClickListenerC1342a dialogInterfaceOnClickListenerC1342a = new DialogInterfaceOnClickListenerC1342a(browserActivity, 0);
                    c0059b2.f275l = stringArray2;
                    c0059b2.f277n = dialogInterfaceOnClickListenerC1342a;
                    c0059b2.f279p = m3232a;
                    c0059b2.f278o = true;
                    c1064b3.m2717c(browserActivity.getResources().getString(R.string.devices_dialog_cancel), DialogInterfaceOnClickListenerC1345d.f6239k);
                    c1064b3.m2718d(browserActivity.getResources().getString(R.string.devices_dialog_confirm), new DialogInterfaceOnClickListenerC1342a(browserActivity, 1));
                    c1064b3.mo135a();
                    c1064b3.m136b().getWindow().setBackgroundDrawableResource(R.drawable.dialog_bg);
                    break;
            }
        }
        return false;
    }

    @Override // androidx.appcompat.view.menu.C0070e.a
    /* renamed from: b */
    public final void mo205b(C0070e c0070e) {
    }
}
