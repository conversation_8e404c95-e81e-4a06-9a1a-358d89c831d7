package androidx.appcompat.widget;

import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import androidx.activity.result.AbstractC0055d;
import androidx.fragment.app.AbstractC0240x;
import androidx.recyclerview.widget.RecyclerView;
import p033e4.InterfaceC0810d;
import p054h4.C0935b;
import p054h4.InterfaceC0936c;

/* renamed from: androidx.appcompat.widget.y */
/* loaded from: classes.dex */
public final /* synthetic */ class C0174y implements InterfaceC0810d {

    /* renamed from: a */
    public static final /* synthetic */ C0174y f1017a = new C0174y();

    /* renamed from: b */
    public static final void m484b(int i6, View view) {
        int i7;
        if (i6 == 0) {
            throw null;
        }
        int i8 = i6 - 1;
        if (i8 == 0) {
            ViewGroup viewGroup = (ViewGroup) view.getParent();
            if (viewGroup != null) {
                if (AbstractC0240x.m791K(2)) {
                    Log.v("FragmentManager", "SpecialEffectsController: Removing view " + view + " from container " + viewGroup);
                }
                viewGroup.removeView(view);
                return;
            }
            return;
        }
        if (i8 == 1) {
            if (AbstractC0240x.m791K(2)) {
                Log.v("FragmentManager", "SpecialEffectsController: Setting view " + view + " to VISIBLE");
            }
            i7 = 0;
        } else if (i8 == 2) {
            if (AbstractC0240x.m791K(2)) {
                Log.v("FragmentManager", "SpecialEffectsController: Setting view " + view + " to GONE");
            }
            i7 = 8;
        } else {
            if (i8 != 3) {
                return;
            }
            if (AbstractC0240x.m791K(2)) {
                Log.v("FragmentManager", "SpecialEffectsController: Setting view " + view + " to INVISIBLE");
            }
            i7 = 4;
        }
        view.setVisibility(i7);
    }

    /* renamed from: c */
    public static int m485c(int i6) {
        if (i6 == 0) {
            return 2;
        }
        if (i6 == 4) {
            return 4;
        }
        if (i6 == 8) {
            return 3;
        }
        throw new IllegalArgumentException(m490h("Unknown visibility ", i6));
    }

    /* renamed from: d */
    public static int m486d(View view) {
        if (view.getAlpha() == 0.0f && view.getVisibility() == 0) {
            return 4;
        }
        return m485c(view.getVisibility());
    }

    /* renamed from: e */
    public static int m487e(int i6) {
        if (i6 == 0) {
            return 1;
        }
        if (i6 == 1) {
            return 2;
        }
        if (i6 == 2) {
            return 3;
        }
        if (i6 != 3) {
            return i6 != 4 ? 0 : 5;
        }
        return 4;
    }

    /* renamed from: f */
    public static /* synthetic */ boolean m488f(int i6) {
        if (i6 == 1 || i6 == 2 || i6 == 3) {
            return false;
        }
        if (i6 == 4 || i6 == 5) {
            return true;
        }
        throw null;
    }

    /* renamed from: g */
    public static String m489g(RecyclerView recyclerView, StringBuilder sb) {
        sb.append(recyclerView.m996A());
        return sb.toString();
    }

    /* renamed from: h */
    public static String m490h(String str, int i6) {
        return str + i6;
    }

    /* renamed from: i */
    public static String m491i(String str, String str2, String str3) {
        return str + str2 + str3;
    }

    /* renamed from: j */
    public static StringBuilder m492j(String str, String str2) {
        StringBuilder sb = new StringBuilder();
        sb.append(str);
        sb.append(str2);
        return sb;
    }

    /* renamed from: k */
    public static /* synthetic */ String m493k(int i6) {
        return i6 == 1 ? "REMOVED" : i6 == 2 ? "VISIBLE" : i6 == 3 ? "GONE" : i6 == 4 ? "INVISIBLE" : "null";
    }

    /* renamed from: l */
    public static /* synthetic */ String m494l(int i6) {
        return i6 == 1 ? "INITIALIZE" : i6 == 2 ? "RESOURCE_CACHE" : i6 == 3 ? "DATA_CACHE" : i6 == 4 ? "SOURCE" : i6 == 5 ? "ENCODE" : i6 == 6 ? "FINISHED" : "null";
    }

    @Override // p033e4.InterfaceC0810d
    /* renamed from: a */
    public Object mo495a(AbstractC0055d abstractC0055d) {
        abstractC0055d.mo122o(InterfaceC0936c.class);
        return new C0935b();
    }
}
