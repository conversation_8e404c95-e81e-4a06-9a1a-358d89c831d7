package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.widget.RadioButton;
import com.liaoyuan.aicast.R;
import java.util.Objects;
import p028e.C0750a;

/* renamed from: androidx.appcompat.widget.q */
/* loaded from: classes.dex */
public class C0158q extends RadioButton {

    /* renamed from: j */
    public final C0139i f925j;

    /* renamed from: k */
    public final C0127e f926k;

    /* renamed from: l */
    public final C0176z f927l;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C0158q(Context context, AttributeSet attributeSet) {
        super(context, attributeSet, R.attr.radioButtonStyle);
        C0177z0.m514a(context);
        C0173x0.m480a(this, getContext());
        C0139i c0139i = new C0139i(this);
        this.f925j = c0139i;
        c0139i.m399b(attributeSet, R.attr.radioButtonStyle);
        C0127e c0127e = new C0127e(this);
        this.f926k = c0127e;
        c0127e.m356d(attributeSet, R.attr.radioButtonStyle);
        C0176z c0176z = new C0176z(this);
        this.f927l = c0176z;
        c0176z.m502f(attributeSet, R.attr.radioButtonStyle);
    }

    @Override // android.widget.CompoundButton, android.widget.TextView, android.view.View
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        C0127e c0127e = this.f926k;
        if (c0127e != null) {
            c0127e.m353a();
        }
        C0176z c0176z = this.f927l;
        if (c0176z != null) {
            c0176z.m499b();
        }
    }

    @Override // android.widget.CompoundButton, android.widget.TextView
    public int getCompoundPaddingLeft() {
        int compoundPaddingLeft = super.getCompoundPaddingLeft();
        C0139i c0139i = this.f925j;
        if (c0139i != null) {
            Objects.requireNonNull(c0139i);
        }
        return compoundPaddingLeft;
    }

    public ColorStateList getSupportBackgroundTintList() {
        C0127e c0127e = this.f926k;
        if (c0127e != null) {
            return c0127e.m354b();
        }
        return null;
    }

    public PorterDuff.Mode getSupportBackgroundTintMode() {
        C0127e c0127e = this.f926k;
        if (c0127e != null) {
            return c0127e.m355c();
        }
        return null;
    }

    public ColorStateList getSupportButtonTintList() {
        C0139i c0139i = this.f925j;
        if (c0139i != null) {
            return c0139i.f829b;
        }
        return null;
    }

    public PorterDuff.Mode getSupportButtonTintMode() {
        C0139i c0139i = this.f925j;
        if (c0139i != null) {
            return c0139i.f830c;
        }
        return null;
    }

    @Override // android.view.View
    public void setBackgroundDrawable(Drawable drawable) {
        super.setBackgroundDrawable(drawable);
        C0127e c0127e = this.f926k;
        if (c0127e != null) {
            c0127e.m357e();
        }
    }

    @Override // android.view.View
    public void setBackgroundResource(int i6) {
        super.setBackgroundResource(i6);
        C0127e c0127e = this.f926k;
        if (c0127e != null) {
            c0127e.m358f(i6);
        }
    }

    @Override // android.widget.CompoundButton
    public void setButtonDrawable(int i6) {
        setButtonDrawable(C0750a.m2138a(getContext(), i6));
    }

    @Override // android.widget.CompoundButton
    public void setButtonDrawable(Drawable drawable) {
        super.setButtonDrawable(drawable);
        C0139i c0139i = this.f925j;
        if (c0139i != null) {
            if (c0139i.f833f) {
                c0139i.f833f = false;
            } else {
                c0139i.f833f = true;
                c0139i.m398a();
            }
        }
    }

    public void setSupportBackgroundTintList(ColorStateList colorStateList) {
        C0127e c0127e = this.f926k;
        if (c0127e != null) {
            c0127e.m360h(colorStateList);
        }
    }

    public void setSupportBackgroundTintMode(PorterDuff.Mode mode) {
        C0127e c0127e = this.f926k;
        if (c0127e != null) {
            c0127e.m361i(mode);
        }
    }

    public void setSupportButtonTintList(ColorStateList colorStateList) {
        C0139i c0139i = this.f925j;
        if (c0139i != null) {
            c0139i.f829b = colorStateList;
            c0139i.f831d = true;
            c0139i.m398a();
        }
    }

    public void setSupportButtonTintMode(PorterDuff.Mode mode) {
        C0139i c0139i = this.f925j;
        if (c0139i != null) {
            c0139i.f830c = mode;
            c0139i.f832e = true;
            c0139i.m398a();
        }
    }
}
