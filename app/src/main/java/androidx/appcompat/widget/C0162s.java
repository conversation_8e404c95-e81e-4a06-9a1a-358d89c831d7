package androidx.appcompat.widget;

import android.content.ClipData;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.InputContentInfo;
import p029e0.C0753c;
import p029e0.C0766p;
import p043g0.C0880e;
import p043g0.InterfaceC0879d;

/* renamed from: androidx.appcompat.widget.s */
/* loaded from: classes.dex */
public final class C0162s implements InterfaceC0879d {

    /* renamed from: a */
    public final /* synthetic */ View f933a;

    public C0162s(View view) {
        this.f933a = view;
    }

    /* renamed from: a */
    public final boolean m440a(C0880e c0880e, int i6, Bundle bundle) {
        if (Build.VERSION.SDK_INT >= 25 && (i6 & 1) != 0) {
            try {
                c0880e.f4475a.mo2422a();
                InputContentInfo inputContentInfo = (InputContentInfo) c0880e.f4475a.mo2425d();
                bundle = bundle == null ? new Bundle() : new Bundle(bundle);
                bundle.putParcelable("androidx.core.view.extra.INPUT_CONTENT_INFO", inputContentInfo);
            } catch (Exception e6) {
                Log.w("ReceiveContent", "Can't insert content from IME; requestPermission() failed", e6);
                return false;
            }
        }
        C0753c.a aVar = new C0753c.a(new ClipData(c0880e.f4475a.mo2424c(), new ClipData.Item(c0880e.f4475a.mo2426e())), 2);
        aVar.f4024d = c0880e.f4475a.mo2423b();
        aVar.f4025e = bundle;
        return C0766p.m2182o(this.f933a, new C0753c(aVar)) == null;
    }
}
