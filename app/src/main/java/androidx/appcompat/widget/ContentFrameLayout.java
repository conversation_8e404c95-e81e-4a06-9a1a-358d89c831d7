package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.widget.FrameLayout;
import androidx.appcompat.view.menu.C0070e;
import java.util.Objects;
import p021d.C0685j;
import p021d.LayoutInflaterFactory2C0683h;

/* loaded from: classes.dex */
public class ContentFrameLayout extends FrameLayout {

    /* renamed from: j */
    public TypedValue f548j;

    /* renamed from: k */
    public TypedValue f549k;

    /* renamed from: l */
    public TypedValue f550l;

    /* renamed from: m */
    public TypedValue f551m;

    /* renamed from: n */
    public TypedValue f552n;

    /* renamed from: o */
    public TypedValue f553o;

    /* renamed from: p */
    public final Rect f554p;

    /* renamed from: q */
    public InterfaceC0089a f555q;

    /* renamed from: androidx.appcompat.widget.ContentFrameLayout$a */
    public interface InterfaceC0089a {
    }

    public ContentFrameLayout(Context context, AttributeSet attributeSet) {
        super(context, attributeSet, 0);
        this.f554p = new Rect();
    }

    public TypedValue getFixedHeightMajor() {
        if (this.f552n == null) {
            this.f552n = new TypedValue();
        }
        return this.f552n;
    }

    public TypedValue getFixedHeightMinor() {
        if (this.f553o == null) {
            this.f553o = new TypedValue();
        }
        return this.f553o;
    }

    public TypedValue getFixedWidthMajor() {
        if (this.f550l == null) {
            this.f550l = new TypedValue();
        }
        return this.f550l;
    }

    public TypedValue getFixedWidthMinor() {
        if (this.f551m == null) {
            this.f551m = new TypedValue();
        }
        return this.f551m;
    }

    public TypedValue getMinWidthMajor() {
        if (this.f548j == null) {
            this.f548j = new TypedValue();
        }
        return this.f548j;
    }

    public TypedValue getMinWidthMinor() {
        if (this.f549k == null) {
            this.f549k = new TypedValue();
        }
        return this.f549k;
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onAttachedToWindow() {
        super.onAttachedToWindow();
        InterfaceC0089a interfaceC0089a = this.f555q;
        if (interfaceC0089a != null) {
            Objects.requireNonNull(interfaceC0089a);
        }
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        InterfaceC0089a interfaceC0089a = this.f555q;
        if (interfaceC0089a != null) {
            LayoutInflaterFactory2C0683h layoutInflaterFactory2C0683h = ((C0685j) interfaceC0089a).f3647a;
            InterfaceC0128e0 interfaceC0128e0 = layoutInflaterFactory2C0683h.f3608t;
            if (interfaceC0128e0 != null) {
                interfaceC0128e0.mo241l();
            }
            if (layoutInflaterFactory2C0683h.f3613y != null) {
                layoutInflaterFactory2C0683h.f3602n.getDecorView().removeCallbacks(layoutInflaterFactory2C0683h.f3614z);
                if (layoutInflaterFactory2C0683h.f3613y.isShowing()) {
                    try {
                        layoutInflaterFactory2C0683h.f3613y.dismiss();
                    } catch (IllegalArgumentException unused) {
                    }
                }
                layoutInflaterFactory2C0683h.f3613y = null;
            }
            layoutInflaterFactory2C0683h.m1994H();
            C0070e c0070e = layoutInflaterFactory2C0683h.m1999M(0).f3636h;
            if (c0070e != null) {
                c0070e.m181d(true);
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:15:0x004a  */
    /* JADX WARN: Removed duplicated region for block: B:17:0x0063  */
    /* JADX WARN: Removed duplicated region for block: B:26:0x0086  */
    /* JADX WARN: Removed duplicated region for block: B:35:0x00ab  */
    /* JADX WARN: Removed duplicated region for block: B:40:0x00b8  */
    /* JADX WARN: Removed duplicated region for block: B:43:0x00cc  */
    /* JADX WARN: Removed duplicated region for block: B:45:0x00d6  */
    /* JADX WARN: Removed duplicated region for block: B:47:0x00de  */
    /* JADX WARN: Removed duplicated region for block: B:50:? A[RETURN, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:51:0x00be  */
    /* JADX WARN: Removed duplicated region for block: B:54:0x00ae  */
    @Override // android.widget.FrameLayout, android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void onMeasure(int r14, int r15) {
        /*
            Method dump skipped, instructions count: 226
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.ContentFrameLayout.onMeasure(int, int):void");
    }

    public void setAttachListener(InterfaceC0089a interfaceC0089a) {
        this.f555q = interfaceC0089a;
    }
}
