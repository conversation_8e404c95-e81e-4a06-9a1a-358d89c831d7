package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.widget.ImageView;
import p008b0.C0385m;
import p028e.C0750a;
import p029e0.C0766p;

/* renamed from: androidx.appcompat.widget.m */
/* loaded from: classes.dex */
public final class C0150m {

    /* renamed from: a */
    public final ImageView f878a;

    /* renamed from: b */
    public C0117a1 f879b;

    public C0150m(ImageView imageView) {
        this.f878a = imageView;
    }

    /* renamed from: a */
    public final void m420a() {
        C0117a1 c0117a1;
        Drawable drawable = this.f878a.getDrawable();
        if (drawable != null) {
            int i6 = C0134g0.f790a;
        }
        if (drawable == null || (c0117a1 = this.f879b) == null) {
            return;
        }
        C0142j.m404f(drawable, c0117a1, this.f878a.getDrawableState());
    }

    /* renamed from: b */
    public final void m421b(AttributeSet attributeSet, int i6) {
        int m348l;
        Context context = this.f878a.getContext();
        int[] iArr = C0385m.f2357p;
        C0123c1 m336q = C0123c1.m336q(context, attributeSet, iArr, i6);
        ImageView imageView = this.f878a;
        C0766p.m2186s(imageView, imageView.getContext(), iArr, attributeSet, m336q.f751b, i6);
        try {
            Drawable drawable = this.f878a.getDrawable();
            if (drawable == null && (m348l = m336q.m348l(1, -1)) != -1 && (drawable = C0750a.m2138a(this.f878a.getContext(), m348l)) != null) {
                this.f878a.setImageDrawable(drawable);
            }
            if (drawable != null) {
                int i7 = C0134g0.f790a;
            }
            if (m336q.m351o(2)) {
                this.f878a.setImageTintList(m336q.m339c(2));
            }
            if (m336q.m351o(3)) {
                this.f878a.setImageTintMode(C0134g0.m389b(m336q.m346j(3, -1), null));
            }
        } finally {
            m336q.m352r();
        }
    }

    /* renamed from: c */
    public final void m422c(int i6) {
        if (i6 != 0) {
            Drawable m2138a = C0750a.m2138a(this.f878a.getContext(), i6);
            if (m2138a != null) {
                int i7 = C0134g0.f790a;
            }
            this.f878a.setImageDrawable(m2138a);
        } else {
            this.f878a.setImageDrawable(null);
        }
        m420a();
    }

    /* renamed from: d */
    public final void m423d(ColorStateList colorStateList) {
        if (this.f879b == null) {
            this.f879b = new C0117a1();
        }
        C0117a1 c0117a1 = this.f879b;
        c0117a1.f703a = colorStateList;
        c0117a1.f706d = true;
        m420a();
    }

    /* renamed from: e */
    public final void m424e(PorterDuff.Mode mode) {
        if (this.f879b == null) {
            this.f879b = new C0117a1();
        }
        C0117a1 c0117a1 = this.f879b;
        c0117a1.f704b = mode;
        c0117a1.f705c = true;
        m420a();
    }
}
