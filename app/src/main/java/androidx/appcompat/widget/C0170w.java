package androidx.appcompat.widget;

import android.R;
import android.content.Context;
import android.content.DialogInterface;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.database.DataSetObserver;
import android.graphics.PorterDuff;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.AdapterView;
import android.widget.ListAdapter;
import android.widget.PopupWindow;
import android.widget.Spinner;
import android.widget.SpinnerAdapter;
import android.widget.ThemedSpinnerAdapter;
import androidx.appcompat.app.AlertController;
import androidx.appcompat.app.DialogC0063b;
import java.util.Objects;
import java.util.WeakHashMap;
import p028e.C0750a;
import p029e0.C0766p;
import p029e0.C0769s;

/* renamed from: androidx.appcompat.widget.w */
/* loaded from: classes.dex */
public final class C0170w extends Spinner {

    /* renamed from: r */
    public static final int[] f963r = {R.attr.spinnerMode};

    /* renamed from: j */
    public final C0127e f964j;

    /* renamed from: k */
    public final Context f965k;

    /* renamed from: l */
    public C0168v f966l;

    /* renamed from: m */
    public SpinnerAdapter f967m;

    /* renamed from: n */
    public final boolean f968n;

    /* renamed from: o */
    public f f969o;

    /* renamed from: p */
    public int f970p;

    /* renamed from: q */
    public final Rect f971q;

    /* renamed from: androidx.appcompat.widget.w$a */
    public class a implements ViewTreeObserver.OnGlobalLayoutListener {
        public a() {
        }

        @Override // android.view.ViewTreeObserver.OnGlobalLayoutListener
        public final void onGlobalLayout() {
            if (!C0170w.this.getInternalPopup().mo458b()) {
                C0170w.this.m457b();
            }
            ViewTreeObserver viewTreeObserver = C0170w.this.getViewTreeObserver();
            if (viewTreeObserver != null) {
                viewTreeObserver.removeOnGlobalLayoutListener(this);
            }
        }
    }

    /* renamed from: androidx.appcompat.widget.w$b */
    public class b implements f, DialogInterface.OnClickListener {

        /* renamed from: j */
        public DialogC0063b f973j;

        /* renamed from: k */
        public ListAdapter f974k;

        /* renamed from: l */
        public CharSequence f975l;

        public b() {
        }

        @Override // androidx.appcompat.widget.C0170w.f
        /* renamed from: b */
        public final boolean mo458b() {
            DialogC0063b dialogC0063b = this.f973j;
            if (dialogC0063b != null) {
                return dialogC0063b.isShowing();
            }
            return false;
        }

        @Override // androidx.appcompat.widget.C0170w.f
        /* renamed from: c */
        public final void mo459c(int i6) {
            Log.e("AppCompatSpinner", "Cannot set horizontal offset for MODE_DIALOG, ignoring");
        }

        @Override // androidx.appcompat.widget.C0170w.f
        /* renamed from: d */
        public final int mo460d() {
            return 0;
        }

        @Override // androidx.appcompat.widget.C0170w.f
        public final void dismiss() {
            DialogC0063b dialogC0063b = this.f973j;
            if (dialogC0063b != null) {
                dialogC0063b.dismiss();
                this.f973j = null;
            }
        }

        @Override // androidx.appcompat.widget.C0170w.f
        /* renamed from: e */
        public final void mo461e(int i6, int i7) {
            if (this.f974k == null) {
                return;
            }
            Context popupContext = C0170w.this.getPopupContext();
            DialogC0063b.a aVar = new DialogC0063b.a(popupContext, DialogC0063b.m134e(popupContext, 0));
            CharSequence charSequence = this.f975l;
            if (charSequence != null) {
                aVar.f284a.f267d = charSequence;
            }
            ListAdapter listAdapter = this.f974k;
            int selectedItemPosition = C0170w.this.getSelectedItemPosition();
            AlertController.C0059b c0059b = aVar.f284a;
            c0059b.f276m = listAdapter;
            c0059b.f277n = this;
            c0059b.f279p = selectedItemPosition;
            c0059b.f278o = true;
            DialogC0063b mo135a = aVar.mo135a();
            this.f973j = mo135a;
            AlertController.RecycleListView recycleListView = mo135a.f283l.f241g;
            recycleListView.setTextDirection(i6);
            recycleListView.setTextAlignment(i7);
            this.f973j.show();
        }

        @Override // androidx.appcompat.widget.C0170w.f
        /* renamed from: g */
        public final int mo462g() {
            return 0;
        }

        @Override // androidx.appcompat.widget.C0170w.f
        /* renamed from: i */
        public final Drawable mo463i() {
            return null;
        }

        @Override // androidx.appcompat.widget.C0170w.f
        /* renamed from: j */
        public final CharSequence mo464j() {
            return this.f975l;
        }

        @Override // androidx.appcompat.widget.C0170w.f
        /* renamed from: l */
        public final void mo465l(CharSequence charSequence) {
            this.f975l = charSequence;
        }

        @Override // androidx.appcompat.widget.C0170w.f
        /* renamed from: m */
        public final void mo466m(Drawable drawable) {
            Log.e("AppCompatSpinner", "Cannot set popup background for MODE_DIALOG, ignoring");
        }

        @Override // androidx.appcompat.widget.C0170w.f
        /* renamed from: n */
        public final void mo467n(int i6) {
            Log.e("AppCompatSpinner", "Cannot set vertical offset for MODE_DIALOG, ignoring");
        }

        @Override // androidx.appcompat.widget.C0170w.f
        /* renamed from: o */
        public final void mo431o(ListAdapter listAdapter) {
            this.f974k = listAdapter;
        }

        @Override // android.content.DialogInterface.OnClickListener
        public final void onClick(DialogInterface dialogInterface, int i6) {
            C0170w.this.setSelection(i6);
            if (C0170w.this.getOnItemClickListener() != null) {
                C0170w.this.performItemClick(null, i6, this.f974k.getItemId(i6));
            }
            DialogC0063b dialogC0063b = this.f973j;
            if (dialogC0063b != null) {
                dialogC0063b.dismiss();
                this.f973j = null;
            }
        }

        @Override // androidx.appcompat.widget.C0170w.f
        /* renamed from: p */
        public final void mo468p(int i6) {
            Log.e("AppCompatSpinner", "Cannot set horizontal (original) offset for MODE_DIALOG, ignoring");
        }
    }

    /* renamed from: androidx.appcompat.widget.w$c */
    public static class c implements ListAdapter, SpinnerAdapter {

        /* renamed from: j */
        public SpinnerAdapter f977j;

        /* renamed from: k */
        public ListAdapter f978k;

        public c(SpinnerAdapter spinnerAdapter, Resources.Theme theme) {
            this.f977j = spinnerAdapter;
            if (spinnerAdapter instanceof ListAdapter) {
                this.f978k = (ListAdapter) spinnerAdapter;
            }
            if (theme != null) {
                if (spinnerAdapter instanceof ThemedSpinnerAdapter) {
                    ThemedSpinnerAdapter themedSpinnerAdapter = (ThemedSpinnerAdapter) spinnerAdapter;
                    if (themedSpinnerAdapter.getDropDownViewTheme() != theme) {
                        themedSpinnerAdapter.setDropDownViewTheme(theme);
                        return;
                    }
                    return;
                }
                if (spinnerAdapter instanceof InterfaceC0175y0) {
                    InterfaceC0175y0 interfaceC0175y0 = (InterfaceC0175y0) spinnerAdapter;
                    if (interfaceC0175y0.getDropDownViewTheme() == null) {
                        interfaceC0175y0.m496a();
                    }
                }
            }
        }

        @Override // android.widget.ListAdapter
        public final boolean areAllItemsEnabled() {
            ListAdapter listAdapter = this.f978k;
            if (listAdapter != null) {
                return listAdapter.areAllItemsEnabled();
            }
            return true;
        }

        @Override // android.widget.Adapter
        public final int getCount() {
            SpinnerAdapter spinnerAdapter = this.f977j;
            if (spinnerAdapter == null) {
                return 0;
            }
            return spinnerAdapter.getCount();
        }

        @Override // android.widget.SpinnerAdapter
        public final View getDropDownView(int i6, View view, ViewGroup viewGroup) {
            SpinnerAdapter spinnerAdapter = this.f977j;
            if (spinnerAdapter == null) {
                return null;
            }
            return spinnerAdapter.getDropDownView(i6, view, viewGroup);
        }

        @Override // android.widget.Adapter
        public final Object getItem(int i6) {
            SpinnerAdapter spinnerAdapter = this.f977j;
            if (spinnerAdapter == null) {
                return null;
            }
            return spinnerAdapter.getItem(i6);
        }

        @Override // android.widget.Adapter
        public final long getItemId(int i6) {
            SpinnerAdapter spinnerAdapter = this.f977j;
            if (spinnerAdapter == null) {
                return -1L;
            }
            return spinnerAdapter.getItemId(i6);
        }

        @Override // android.widget.Adapter
        public final int getItemViewType(int i6) {
            return 0;
        }

        @Override // android.widget.Adapter
        public final View getView(int i6, View view, ViewGroup viewGroup) {
            return getDropDownView(i6, view, viewGroup);
        }

        @Override // android.widget.Adapter
        public final int getViewTypeCount() {
            return 1;
        }

        @Override // android.widget.Adapter
        public final boolean hasStableIds() {
            SpinnerAdapter spinnerAdapter = this.f977j;
            return spinnerAdapter != null && spinnerAdapter.hasStableIds();
        }

        @Override // android.widget.Adapter
        public final boolean isEmpty() {
            return getCount() == 0;
        }

        @Override // android.widget.ListAdapter
        public final boolean isEnabled(int i6) {
            ListAdapter listAdapter = this.f978k;
            if (listAdapter != null) {
                return listAdapter.isEnabled(i6);
            }
            return true;
        }

        @Override // android.widget.Adapter
        public final void registerDataSetObserver(DataSetObserver dataSetObserver) {
            SpinnerAdapter spinnerAdapter = this.f977j;
            if (spinnerAdapter != null) {
                spinnerAdapter.registerDataSetObserver(dataSetObserver);
            }
        }

        @Override // android.widget.Adapter
        public final void unregisterDataSetObserver(DataSetObserver dataSetObserver) {
            SpinnerAdapter spinnerAdapter = this.f977j;
            if (spinnerAdapter != null) {
                spinnerAdapter.unregisterDataSetObserver(dataSetObserver);
            }
        }
    }

    /* renamed from: androidx.appcompat.widget.w$d */
    public class d extends C0151m0 implements f {

        /* renamed from: K */
        public CharSequence f979K;

        /* renamed from: L */
        public ListAdapter f980L;

        /* renamed from: M */
        public final Rect f981M;

        /* renamed from: N */
        public int f982N;

        /* renamed from: androidx.appcompat.widget.w$d$a */
        public class a implements AdapterView.OnItemClickListener {
            public a() {
            }

            @Override // android.widget.AdapterView.OnItemClickListener
            public final void onItemClick(AdapterView<?> adapterView, View view, int i6, long j6) {
                C0170w.this.setSelection(i6);
                if (C0170w.this.getOnItemClickListener() != null) {
                    d dVar = d.this;
                    C0170w.this.performItemClick(view, i6, dVar.f980L.getItemId(i6));
                }
                d.this.dismiss();
            }
        }

        /* renamed from: androidx.appcompat.widget.w$d$b */
        public class b implements ViewTreeObserver.OnGlobalLayoutListener {
            public b() {
            }

            @Override // android.view.ViewTreeObserver.OnGlobalLayoutListener
            public final void onGlobalLayout() {
                d dVar = d.this;
                C0170w c0170w = C0170w.this;
                Objects.requireNonNull(dVar);
                WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
                if (!(c0170w.isAttachedToWindow() && c0170w.getGlobalVisibleRect(dVar.f981M))) {
                    d.this.dismiss();
                } else {
                    d.this.m469v();
                    d.this.mo155f();
                }
            }
        }

        /* renamed from: androidx.appcompat.widget.w$d$c */
        public class c implements PopupWindow.OnDismissListener {

            /* renamed from: j */
            public final /* synthetic */ ViewTreeObserver.OnGlobalLayoutListener f986j;

            public c(ViewTreeObserver.OnGlobalLayoutListener onGlobalLayoutListener) {
                this.f986j = onGlobalLayoutListener;
            }

            @Override // android.widget.PopupWindow.OnDismissListener
            public final void onDismiss() {
                ViewTreeObserver viewTreeObserver = C0170w.this.getViewTreeObserver();
                if (viewTreeObserver != null) {
                    viewTreeObserver.removeGlobalOnLayoutListener(this.f986j);
                }
            }
        }

        public d(Context context, AttributeSet attributeSet, int i6) {
            super(context, attributeSet, i6, 0);
            this.f981M = new Rect();
            this.f904x = C0170w.this;
            m435t();
            this.f905y = new a();
        }

        @Override // androidx.appcompat.widget.C0170w.f
        /* renamed from: e */
        public final void mo461e(int i6, int i7) {
            ViewTreeObserver viewTreeObserver;
            boolean mo153b = mo153b();
            m469v();
            m434s();
            mo155f();
            C0137h0 c0137h0 = this.f892l;
            c0137h0.setChoiceMode(1);
            c0137h0.setTextDirection(i6);
            c0137h0.setTextAlignment(i7);
            int selectedItemPosition = C0170w.this.getSelectedItemPosition();
            C0137h0 c0137h02 = this.f892l;
            if (mo153b() && c0137h02 != null) {
                c0137h02.setListSelectionHidden(false);
                c0137h02.setSelection(selectedItemPosition);
                if (c0137h02.getChoiceMode() != 0) {
                    c0137h02.setItemChecked(selectedItemPosition, true);
                }
            }
            if (mo153b || (viewTreeObserver = C0170w.this.getViewTreeObserver()) == null) {
                return;
            }
            b bVar = new b();
            viewTreeObserver.addOnGlobalLayoutListener(bVar);
            m436u(new c(bVar));
        }

        @Override // androidx.appcompat.widget.C0170w.f
        /* renamed from: j */
        public final CharSequence mo464j() {
            return this.f979K;
        }

        @Override // androidx.appcompat.widget.C0170w.f
        /* renamed from: l */
        public final void mo465l(CharSequence charSequence) {
            this.f979K = charSequence;
        }

        @Override // androidx.appcompat.widget.C0151m0, androidx.appcompat.widget.C0170w.f
        /* renamed from: o */
        public final void mo431o(ListAdapter listAdapter) {
            super.mo431o(listAdapter);
            this.f980L = listAdapter;
        }

        @Override // androidx.appcompat.widget.C0170w.f
        /* renamed from: p */
        public final void mo468p(int i6) {
            this.f982N = i6;
        }

        /* JADX WARN: Removed duplicated region for block: B:16:0x008d  */
        /* JADX WARN: Removed duplicated region for block: B:20:0x0096  */
        /* renamed from: v */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct code enable 'Show inconsistent code' option in preferences
        */
        public final void m469v() {
            /*
                r8 = this;
                android.graphics.drawable.Drawable r0 = r8.m428i()
                r1 = 0
                if (r0 == 0) goto L26
                androidx.appcompat.widget.w r1 = androidx.appcompat.widget.C0170w.this
                android.graphics.Rect r1 = r1.f971q
                r0.getPadding(r1)
                androidx.appcompat.widget.w r0 = androidx.appcompat.widget.C0170w.this
                boolean r0 = androidx.appcompat.widget.C0144j1.m414b(r0)
                if (r0 == 0) goto L1d
                androidx.appcompat.widget.w r0 = androidx.appcompat.widget.C0170w.this
                android.graphics.Rect r0 = r0.f971q
                int r0 = r0.right
                goto L24
            L1d:
                androidx.appcompat.widget.w r0 = androidx.appcompat.widget.C0170w.this
                android.graphics.Rect r0 = r0.f971q
                int r0 = r0.left
                int r0 = -r0
            L24:
                r1 = r0
                goto L2e
            L26:
                androidx.appcompat.widget.w r0 = androidx.appcompat.widget.C0170w.this
                android.graphics.Rect r0 = r0.f971q
                r0.right = r1
                r0.left = r1
            L2e:
                androidx.appcompat.widget.w r0 = androidx.appcompat.widget.C0170w.this
                int r0 = r0.getPaddingLeft()
                androidx.appcompat.widget.w r2 = androidx.appcompat.widget.C0170w.this
                int r2 = r2.getPaddingRight()
                androidx.appcompat.widget.w r3 = androidx.appcompat.widget.C0170w.this
                int r3 = r3.getWidth()
                androidx.appcompat.widget.w r4 = androidx.appcompat.widget.C0170w.this
                int r5 = r4.f970p
                r6 = -2
                if (r5 != r6) goto L78
                android.widget.ListAdapter r5 = r8.f980L
                android.widget.SpinnerAdapter r5 = (android.widget.SpinnerAdapter) r5
                android.graphics.drawable.Drawable r6 = r8.m428i()
                int r4 = r4.m456a(r5, r6)
                androidx.appcompat.widget.w r5 = androidx.appcompat.widget.C0170w.this
                android.content.Context r5 = r5.getContext()
                android.content.res.Resources r5 = r5.getResources()
                android.util.DisplayMetrics r5 = r5.getDisplayMetrics()
                int r5 = r5.widthPixels
                androidx.appcompat.widget.w r6 = androidx.appcompat.widget.C0170w.this
                android.graphics.Rect r6 = r6.f971q
                int r7 = r6.left
                int r5 = r5 - r7
                int r6 = r6.right
                int r5 = r5 - r6
                if (r4 <= r5) goto L70
                r4 = r5
            L70:
                int r5 = r3 - r0
                int r5 = r5 - r2
                int r4 = java.lang.Math.max(r4, r5)
                goto L7e
            L78:
                r4 = -1
                if (r5 != r4) goto L82
                int r4 = r3 - r0
                int r4 = r4 - r2
            L7e:
                r8.m433r(r4)
                goto L85
            L82:
                r8.m433r(r5)
            L85:
                androidx.appcompat.widget.w r4 = androidx.appcompat.widget.C0170w.this
                boolean r4 = androidx.appcompat.widget.C0144j1.m414b(r4)
                if (r4 == 0) goto L96
                int r3 = r3 - r2
                int r0 = r8.f894n
                int r3 = r3 - r0
                int r0 = r8.f982N
                int r3 = r3 - r0
                int r3 = r3 + r1
                goto L9b
            L96:
                int r2 = r8.f982N
                int r0 = r0 + r2
                int r3 = r0 + r1
            L9b:
                r8.f895o = r3
                return
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.C0170w.d.m469v():void");
        }
    }

    /* renamed from: androidx.appcompat.widget.w$e */
    public static class e extends View.BaseSavedState {
        public static final Parcelable.Creator<e> CREATOR = new a();

        /* renamed from: j */
        public boolean f988j;

        /* renamed from: androidx.appcompat.widget.w$e$a */
        public class a implements Parcelable.Creator<e> {
            @Override // android.os.Parcelable.Creator
            public final e createFromParcel(Parcel parcel) {
                return new e(parcel);
            }

            @Override // android.os.Parcelable.Creator
            public final e[] newArray(int i6) {
                return new e[i6];
            }
        }

        public e(Parcel parcel) {
            super(parcel);
            this.f988j = parcel.readByte() != 0;
        }

        public e(Parcelable parcelable) {
            super(parcelable);
        }

        @Override // android.view.View.BaseSavedState, android.view.AbsSavedState, android.os.Parcelable
        public final void writeToParcel(Parcel parcel, int i6) {
            super.writeToParcel(parcel, i6);
            parcel.writeByte(this.f988j ? (byte) 1 : (byte) 0);
        }
    }

    /* renamed from: androidx.appcompat.widget.w$f */
    public interface f {
        /* renamed from: b */
        boolean mo458b();

        /* renamed from: c */
        void mo459c(int i6);

        /* renamed from: d */
        int mo460d();

        void dismiss();

        /* renamed from: e */
        void mo461e(int i6, int i7);

        /* renamed from: g */
        int mo462g();

        /* renamed from: i */
        Drawable mo463i();

        /* renamed from: j */
        CharSequence mo464j();

        /* renamed from: l */
        void mo465l(CharSequence charSequence);

        /* renamed from: m */
        void mo466m(Drawable drawable);

        /* renamed from: n */
        void mo467n(int i6);

        /* renamed from: o */
        void mo431o(ListAdapter listAdapter);

        /* renamed from: p */
        void mo468p(int i6);
    }

    /* JADX WARN: Code restructure failed: missing block: B:30:0x0056, code lost:
    
        if (r4 == null) goto L23;
     */
    /* JADX WARN: Removed duplicated region for block: B:35:0x00cf  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public C0170w(android.content.Context r10, android.util.AttributeSet r11, int r12) {
        /*
            r9 = this;
            r9.<init>(r10, r11, r12)
            android.graphics.Rect r0 = new android.graphics.Rect
            r0.<init>()
            r9.f971q = r0
            android.content.Context r0 = r9.getContext()
            androidx.appcompat.widget.C0173x0.m480a(r9, r0)
            int[] r0 = p008b0.C0385m.f2312E
            r1 = 0
            android.content.res.TypedArray r0 = r10.obtainStyledAttributes(r11, r0, r12, r1)
            androidx.appcompat.widget.e r2 = new androidx.appcompat.widget.e
            r2.<init>(r9)
            r9.f964j = r2
            r2 = 4
            int r2 = r0.getResourceId(r2, r1)
            if (r2 == 0) goto L2e
            g.c r3 = new g.c
            r3.<init>(r10, r2)
            r9.f965k = r3
            goto L30
        L2e:
            r9.f965k = r10
        L30:
            r2 = 0
            r3 = -1
            int[] r4 = androidx.appcompat.widget.C0170w.f963r     // Catch: java.lang.Throwable -> L49 java.lang.Exception -> L4c
            android.content.res.TypedArray r4 = r10.obtainStyledAttributes(r11, r4, r12, r1)     // Catch: java.lang.Throwable -> L49 java.lang.Exception -> L4c
            boolean r5 = r4.hasValue(r1)     // Catch: java.lang.Throwable -> L43 java.lang.Exception -> L47
            if (r5 == 0) goto L58
            int r3 = r4.getInt(r1, r1)     // Catch: java.lang.Throwable -> L43 java.lang.Exception -> L47
            goto L58
        L43:
            r10 = move-exception
            r2 = r4
            goto Lcd
        L47:
            r5 = move-exception
            goto L4f
        L49:
            r10 = move-exception
            goto Lcd
        L4c:
            r4 = move-exception
            r5 = r4
            r4 = r2
        L4f:
            java.lang.String r6 = "AppCompatSpinner"
            java.lang.String r7 = "Could not read android:spinnerMode"
            android.util.Log.i(r6, r7, r5)     // Catch: java.lang.Throwable -> L43
            if (r4 == 0) goto L5b
        L58:
            r4.recycle()
        L5b:
            r4 = 2
            r5 = 1
            if (r3 == 0) goto L95
            if (r3 == r5) goto L62
            goto La2
        L62:
            androidx.appcompat.widget.w$d r3 = new androidx.appcompat.widget.w$d
            android.content.Context r6 = r9.f965k
            r3.<init>(r6, r11, r12)
            android.content.Context r6 = r9.f965k
            int[] r7 = p008b0.C0385m.f2312E
            androidx.appcompat.widget.c1 r6 = androidx.appcompat.widget.C0123c1.m336q(r6, r11, r7, r12)
            r7 = 3
            r8 = -2
            int r7 = r6.m347k(r7, r8)
            r9.f970p = r7
            android.graphics.drawable.Drawable r7 = r6.m343g(r5)
            androidx.appcompat.widget.o r8 = r3.f889H
            r8.setBackgroundDrawable(r7)
            java.lang.String r4 = r0.getString(r4)
            r3.f979K = r4
            r6.m352r()
            r9.f969o = r3
            androidx.appcompat.widget.v r4 = new androidx.appcompat.widget.v
            r4.<init>(r9, r9, r3)
            r9.f966l = r4
            goto La2
        L95:
            androidx.appcompat.widget.w$b r3 = new androidx.appcompat.widget.w$b
            r3.<init>()
            r9.f969o = r3
            java.lang.String r4 = r0.getString(r4)
            r3.f975l = r4
        La2:
            java.lang.CharSequence[] r1 = r0.getTextArray(r1)
            if (r1 == 0) goto Lb9
            android.widget.ArrayAdapter r3 = new android.widget.ArrayAdapter
            r4 = 17367048(0x1090008, float:2.5162948E-38)
            r3.<init>(r10, r4, r1)
            r10 = 2131492983(0x7f0c0077, float:1.8609433E38)
            r3.setDropDownViewResource(r10)
            r9.setAdapter(r3)
        Lb9:
            r0.recycle()
            r9.f968n = r5
            android.widget.SpinnerAdapter r10 = r9.f967m
            if (r10 == 0) goto Lc7
            r9.setAdapter(r10)
            r9.f967m = r2
        Lc7:
            androidx.appcompat.widget.e r10 = r9.f964j
            r10.m356d(r11, r12)
            return
        Lcd:
            if (r2 == 0) goto Ld2
            r2.recycle()
        Ld2:
            throw r10
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.C0170w.<init>(android.content.Context, android.util.AttributeSet, int):void");
    }

    /* renamed from: a */
    public final int m456a(SpinnerAdapter spinnerAdapter, Drawable drawable) {
        int i6 = 0;
        if (spinnerAdapter == null) {
            return 0;
        }
        int makeMeasureSpec = View.MeasureSpec.makeMeasureSpec(getMeasuredWidth(), 0);
        int makeMeasureSpec2 = View.MeasureSpec.makeMeasureSpec(getMeasuredHeight(), 0);
        int max = Math.max(0, getSelectedItemPosition());
        int min = Math.min(spinnerAdapter.getCount(), max + 15);
        View view = null;
        int i7 = 0;
        for (int max2 = Math.max(0, max - (15 - (min - max))); max2 < min; max2++) {
            int itemViewType = spinnerAdapter.getItemViewType(max2);
            if (itemViewType != i6) {
                view = null;
                i6 = itemViewType;
            }
            view = spinnerAdapter.getView(max2, view, this);
            if (view.getLayoutParams() == null) {
                view.setLayoutParams(new ViewGroup.LayoutParams(-2, -2));
            }
            view.measure(makeMeasureSpec, makeMeasureSpec2);
            i7 = Math.max(i7, view.getMeasuredWidth());
        }
        if (drawable == null) {
            return i7;
        }
        drawable.getPadding(this.f971q);
        Rect rect = this.f971q;
        return i7 + rect.left + rect.right;
    }

    /* renamed from: b */
    public final void m457b() {
        this.f969o.mo461e(getTextDirection(), getTextAlignment());
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        C0127e c0127e = this.f964j;
        if (c0127e != null) {
            c0127e.m353a();
        }
    }

    @Override // android.widget.Spinner
    public int getDropDownHorizontalOffset() {
        f fVar = this.f969o;
        return fVar != null ? fVar.mo460d() : super.getDropDownHorizontalOffset();
    }

    @Override // android.widget.Spinner
    public int getDropDownVerticalOffset() {
        f fVar = this.f969o;
        return fVar != null ? fVar.mo462g() : super.getDropDownVerticalOffset();
    }

    @Override // android.widget.Spinner
    public int getDropDownWidth() {
        return this.f969o != null ? this.f970p : super.getDropDownWidth();
    }

    public final f getInternalPopup() {
        return this.f969o;
    }

    @Override // android.widget.Spinner
    public Drawable getPopupBackground() {
        f fVar = this.f969o;
        return fVar != null ? fVar.mo463i() : super.getPopupBackground();
    }

    @Override // android.widget.Spinner
    public Context getPopupContext() {
        return this.f965k;
    }

    @Override // android.widget.Spinner
    public CharSequence getPrompt() {
        f fVar = this.f969o;
        return fVar != null ? fVar.mo464j() : super.getPrompt();
    }

    public ColorStateList getSupportBackgroundTintList() {
        C0127e c0127e = this.f964j;
        if (c0127e != null) {
            return c0127e.m354b();
        }
        return null;
    }

    public PorterDuff.Mode getSupportBackgroundTintMode() {
        C0127e c0127e = this.f964j;
        if (c0127e != null) {
            return c0127e.m355c();
        }
        return null;
    }

    @Override // android.widget.Spinner, android.widget.AdapterView, android.view.ViewGroup, android.view.View
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        f fVar = this.f969o;
        if (fVar == null || !fVar.mo458b()) {
            return;
        }
        this.f969o.dismiss();
    }

    @Override // android.widget.Spinner, android.widget.AbsSpinner, android.view.View
    public final void onMeasure(int i6, int i7) {
        super.onMeasure(i6, i7);
        if (this.f969o == null || View.MeasureSpec.getMode(i6) != Integer.MIN_VALUE) {
            return;
        }
        setMeasuredDimension(Math.min(Math.max(getMeasuredWidth(), m456a(getAdapter(), getBackground())), View.MeasureSpec.getSize(i6)), getMeasuredHeight());
    }

    @Override // android.widget.Spinner, android.widget.AbsSpinner, android.view.View
    public final void onRestoreInstanceState(Parcelable parcelable) {
        ViewTreeObserver viewTreeObserver;
        e eVar = (e) parcelable;
        super.onRestoreInstanceState(eVar.getSuperState());
        if (!eVar.f988j || (viewTreeObserver = getViewTreeObserver()) == null) {
            return;
        }
        viewTreeObserver.addOnGlobalLayoutListener(new a());
    }

    @Override // android.widget.Spinner, android.widget.AbsSpinner, android.view.View
    public final Parcelable onSaveInstanceState() {
        e eVar = new e(super.onSaveInstanceState());
        f fVar = this.f969o;
        eVar.f988j = fVar != null && fVar.mo458b();
        return eVar;
    }

    @Override // android.widget.Spinner, android.view.View
    public final boolean onTouchEvent(MotionEvent motionEvent) {
        C0168v c0168v = this.f966l;
        if (c0168v == null || !c0168v.onTouch(this, motionEvent)) {
            return super.onTouchEvent(motionEvent);
        }
        return true;
    }

    @Override // android.widget.Spinner, android.view.View
    public final boolean performClick() {
        f fVar = this.f969o;
        if (fVar == null) {
            return super.performClick();
        }
        if (fVar.mo458b()) {
            return true;
        }
        m457b();
        return true;
    }

    @Override // android.widget.AdapterView
    public void setAdapter(SpinnerAdapter spinnerAdapter) {
        if (!this.f968n) {
            this.f967m = spinnerAdapter;
            return;
        }
        super.setAdapter(spinnerAdapter);
        if (this.f969o != null) {
            Context context = this.f965k;
            if (context == null) {
                context = getContext();
            }
            this.f969o.mo431o(new c(spinnerAdapter, context.getTheme()));
        }
    }

    @Override // android.view.View
    public void setBackgroundDrawable(Drawable drawable) {
        super.setBackgroundDrawable(drawable);
        C0127e c0127e = this.f964j;
        if (c0127e != null) {
            c0127e.m357e();
        }
    }

    @Override // android.view.View
    public void setBackgroundResource(int i6) {
        super.setBackgroundResource(i6);
        C0127e c0127e = this.f964j;
        if (c0127e != null) {
            c0127e.m358f(i6);
        }
    }

    @Override // android.widget.Spinner
    public void setDropDownHorizontalOffset(int i6) {
        f fVar = this.f969o;
        if (fVar == null) {
            super.setDropDownHorizontalOffset(i6);
        } else {
            fVar.mo468p(i6);
            this.f969o.mo459c(i6);
        }
    }

    @Override // android.widget.Spinner
    public void setDropDownVerticalOffset(int i6) {
        f fVar = this.f969o;
        if (fVar != null) {
            fVar.mo467n(i6);
        } else {
            super.setDropDownVerticalOffset(i6);
        }
    }

    @Override // android.widget.Spinner
    public void setDropDownWidth(int i6) {
        if (this.f969o != null) {
            this.f970p = i6;
        } else {
            super.setDropDownWidth(i6);
        }
    }

    @Override // android.widget.Spinner
    public void setPopupBackgroundDrawable(Drawable drawable) {
        f fVar = this.f969o;
        if (fVar != null) {
            fVar.mo466m(drawable);
        } else {
            super.setPopupBackgroundDrawable(drawable);
        }
    }

    @Override // android.widget.Spinner
    public void setPopupBackgroundResource(int i6) {
        setPopupBackgroundDrawable(C0750a.m2138a(getPopupContext(), i6));
    }

    @Override // android.widget.Spinner
    public void setPrompt(CharSequence charSequence) {
        f fVar = this.f969o;
        if (fVar != null) {
            fVar.mo465l(charSequence);
        } else {
            super.setPrompt(charSequence);
        }
    }

    public void setSupportBackgroundTintList(ColorStateList colorStateList) {
        C0127e c0127e = this.f964j;
        if (c0127e != null) {
            c0127e.m360h(colorStateList);
        }
    }

    public void setSupportBackgroundTintMode(PorterDuff.Mode mode) {
        C0127e c0127e = this.f964j;
        if (c0127e != null) {
            c0127e.m361i(mode);
        }
    }
}
