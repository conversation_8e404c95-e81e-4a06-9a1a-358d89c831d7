package androidx.appcompat.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.PorterDuff;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.LocaleList;
import android.text.TextUtils;
import android.text.method.PasswordTransformationMethod;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputConnection;
import android.widget.TextView;
import androidx.activity.result.C0052a;
import java.lang.ref.WeakReference;
import java.util.Arrays;
import java.util.Objects;
import java.util.WeakHashMap;
import p008b0.C0385m;
import p029e0.C0766p;
import p029e0.C0769s;
import p043g0.C0876a;
import p050h0.C0903d;
import p050h0.InterfaceC0901b;
import p149w.C1472d;

/* renamed from: androidx.appcompat.widget.z */
/* loaded from: classes.dex */
public final class C0176z {

    /* renamed from: a */
    public final TextView f1018a;

    /* renamed from: b */
    public C0117a1 f1019b;

    /* renamed from: c */
    public C0117a1 f1020c;

    /* renamed from: d */
    public C0117a1 f1021d;

    /* renamed from: e */
    public C0117a1 f1022e;

    /* renamed from: f */
    public C0117a1 f1023f;

    /* renamed from: g */
    public C0117a1 f1024g;

    /* renamed from: h */
    public C0117a1 f1025h;

    /* renamed from: i */
    public final C0122c0 f1026i;

    /* renamed from: j */
    public int f1027j = 0;

    /* renamed from: k */
    public int f1028k = -1;

    /* renamed from: l */
    public Typeface f1029l;

    /* renamed from: m */
    public boolean f1030m;

    /* renamed from: androidx.appcompat.widget.z$a */
    public class a extends C1472d.a {

        /* renamed from: a */
        public final /* synthetic */ int f1031a;

        /* renamed from: b */
        public final /* synthetic */ int f1032b;

        /* renamed from: c */
        public final /* synthetic */ WeakReference f1033c;

        public a(int i6, int i7, WeakReference weakReference) {
            this.f1031a = i6;
            this.f1032b = i7;
            this.f1033c = weakReference;
        }

        @Override // p149w.C1472d.a
        /* renamed from: d */
        public final void mo512d(int i6) {
        }

        @Override // p149w.C1472d.a
        /* renamed from: e */
        public final void mo513e(Typeface typeface) {
            int i6;
            if (Build.VERSION.SDK_INT >= 28 && (i6 = this.f1031a) != -1) {
                typeface = Typeface.create(typeface, i6, (this.f1032b & 2) != 0);
            }
            C0176z c0176z = C0176z.this;
            WeakReference weakReference = this.f1033c;
            if (c0176z.f1030m) {
                c0176z.f1029l = typeface;
                TextView textView = (TextView) weakReference.get();
                if (textView != null) {
                    WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
                    if (textView.isAttachedToWindow()) {
                        textView.post(new RunnableC0116a0(textView, typeface, c0176z.f1027j));
                    } else {
                        textView.setTypeface(typeface, c0176z.f1027j);
                    }
                }
            }
        }
    }

    public C0176z(TextView textView) {
        this.f1018a = textView;
        this.f1026i = new C0122c0(textView);
    }

    /* renamed from: d */
    public static C0117a1 m497d(Context context, C0142j c0142j, int i6) {
        ColorStateList m406d = c0142j.m406d(context, i6);
        if (m406d == null) {
            return null;
        }
        C0117a1 c0117a1 = new C0117a1();
        c0117a1.f706d = true;
        c0117a1.f703a = m406d;
        return c0117a1;
    }

    /* renamed from: a */
    public final void m498a(Drawable drawable, C0117a1 c0117a1) {
        if (drawable == null || c0117a1 == null) {
            return;
        }
        C0142j.m404f(drawable, c0117a1, this.f1018a.getDrawableState());
    }

    /* renamed from: b */
    public final void m499b() {
        if (this.f1019b != null || this.f1020c != null || this.f1021d != null || this.f1022e != null) {
            Drawable[] compoundDrawables = this.f1018a.getCompoundDrawables();
            m498a(compoundDrawables[0], this.f1019b);
            m498a(compoundDrawables[1], this.f1020c);
            m498a(compoundDrawables[2], this.f1021d);
            m498a(compoundDrawables[3], this.f1022e);
        }
        if (this.f1023f == null && this.f1024g == null) {
            return;
        }
        Drawable[] compoundDrawablesRelative = this.f1018a.getCompoundDrawablesRelative();
        m498a(compoundDrawablesRelative[0], this.f1023f);
        m498a(compoundDrawablesRelative[2], this.f1024g);
    }

    /* renamed from: c */
    public final void m500c() {
        this.f1026i.m325a();
    }

    /* renamed from: e */
    public final boolean m501e() {
        C0122c0 c0122c0 = this.f1026i;
        return c0122c0.m331i() && c0122c0.f739a != 0;
    }

    @SuppressLint({"NewApi"})
    /* renamed from: f */
    public final void m502f(AttributeSet attributeSet, int i6) {
        boolean z5;
        boolean z6;
        String str;
        String str2;
        int i7;
        int i8;
        int resourceId;
        Context context = this.f1018a.getContext();
        C0142j m401a = C0142j.m401a();
        int[] iArr = C0385m.f2361r;
        C0123c1 m336q = C0123c1.m336q(context, attributeSet, iArr, i6);
        TextView textView = this.f1018a;
        C0766p.m2186s(textView, textView.getContext(), iArr, attributeSet, m336q.f751b, i6);
        int m348l = m336q.m348l(0, -1);
        if (m336q.m351o(3)) {
            this.f1019b = m497d(context, m401a, m336q.m348l(3, 0));
        }
        if (m336q.m351o(1)) {
            this.f1020c = m497d(context, m401a, m336q.m348l(1, 0));
        }
        if (m336q.m351o(4)) {
            this.f1021d = m497d(context, m401a, m336q.m348l(4, 0));
        }
        if (m336q.m351o(2)) {
            this.f1022e = m497d(context, m401a, m336q.m348l(2, 0));
        }
        int i9 = Build.VERSION.SDK_INT;
        if (m336q.m351o(5)) {
            this.f1023f = m497d(context, m401a, m336q.m348l(5, 0));
        }
        if (m336q.m351o(6)) {
            this.f1024g = m497d(context, m401a, m336q.m348l(6, 0));
        }
        m336q.m352r();
        boolean z7 = this.f1018a.getTransformationMethod() instanceof PasswordTransformationMethod;
        if (m348l != -1) {
            C0123c1 c0123c1 = new C0123c1(context, context.obtainStyledAttributes(m348l, C0385m.f2314F));
            if (z7 || !c0123c1.m351o(14)) {
                z5 = false;
                z6 = false;
            } else {
                z5 = c0123c1.m337a(14, false);
                z6 = true;
            }
            m511o(context, c0123c1);
            str = c0123c1.m351o(15) ? c0123c1.m349m(15) : null;
            str2 = (i9 < 26 || !c0123c1.m351o(13)) ? null : c0123c1.m349m(13);
            c0123c1.m352r();
        } else {
            z5 = false;
            z6 = false;
            str = null;
            str2 = null;
        }
        C0123c1 c0123c12 = new C0123c1(context, context.obtainStyledAttributes(attributeSet, C0385m.f2314F, i6, 0));
        if (!z7 && c0123c12.m351o(14)) {
            z5 = c0123c12.m337a(14, false);
            z6 = true;
        }
        if (c0123c12.m351o(15)) {
            str = c0123c12.m349m(15);
        }
        if (i9 >= 26 && c0123c12.m351o(13)) {
            str2 = c0123c12.m349m(13);
        }
        String str3 = str2;
        if (i9 >= 28 && c0123c12.m351o(0) && c0123c12.m342f(0, -1) == 0) {
            this.f1018a.setTextSize(0, 0.0f);
        }
        m511o(context, c0123c12);
        c0123c12.m352r();
        if (!z7 && z6) {
            m505i(z5);
        }
        Typeface typeface = this.f1029l;
        if (typeface != null) {
            if (this.f1028k == -1) {
                this.f1018a.setTypeface(typeface, this.f1027j);
            } else {
                this.f1018a.setTypeface(typeface);
            }
        }
        if (str3 != null) {
            this.f1018a.setFontVariationSettings(str3);
        }
        if (str != null) {
            this.f1018a.setTextLocales(LocaleList.forLanguageTags(str));
        }
        C0122c0 c0122c0 = this.f1026i;
        Context context2 = c0122c0.f748j;
        int[] iArr2 = C0385m.f2363s;
        TypedArray obtainStyledAttributes = context2.obtainStyledAttributes(attributeSet, iArr2, i6, 0);
        TextView textView2 = c0122c0.f747i;
        C0766p.m2186s(textView2, textView2.getContext(), iArr2, attributeSet, obtainStyledAttributes, i6);
        if (obtainStyledAttributes.hasValue(5)) {
            c0122c0.f739a = obtainStyledAttributes.getInt(5, 0);
        }
        float dimension = obtainStyledAttributes.hasValue(4) ? obtainStyledAttributes.getDimension(4, -1.0f) : -1.0f;
        float dimension2 = obtainStyledAttributes.hasValue(2) ? obtainStyledAttributes.getDimension(2, -1.0f) : -1.0f;
        float dimension3 = obtainStyledAttributes.hasValue(1) ? obtainStyledAttributes.getDimension(1, -1.0f) : -1.0f;
        if (obtainStyledAttributes.hasValue(3) && (resourceId = obtainStyledAttributes.getResourceId(3, 0)) > 0) {
            TypedArray obtainTypedArray = obtainStyledAttributes.getResources().obtainTypedArray(resourceId);
            int length = obtainTypedArray.length();
            int[] iArr3 = new int[length];
            if (length > 0) {
                for (int i10 = 0; i10 < length; i10++) {
                    iArr3[i10] = obtainTypedArray.getDimensionPixelSize(i10, -1);
                }
                c0122c0.f744f = c0122c0.m326b(iArr3);
                c0122c0.m330h();
            }
            obtainTypedArray.recycle();
        }
        obtainStyledAttributes.recycle();
        if (!c0122c0.m331i()) {
            c0122c0.f739a = 0;
        } else if (c0122c0.f739a == 1) {
            if (!c0122c0.f745g) {
                DisplayMetrics displayMetrics = c0122c0.f748j.getResources().getDisplayMetrics();
                if (dimension2 == -1.0f) {
                    i8 = 2;
                    dimension2 = TypedValue.applyDimension(2, 12.0f, displayMetrics);
                } else {
                    i8 = 2;
                }
                if (dimension3 == -1.0f) {
                    dimension3 = TypedValue.applyDimension(i8, 112.0f, displayMetrics);
                }
                if (dimension == -1.0f) {
                    dimension = 1.0f;
                }
                c0122c0.m332j(dimension2, dimension3, dimension);
            }
            c0122c0.m329g();
        }
        if (InterfaceC0901b.f4567a) {
            C0122c0 c0122c02 = this.f1026i;
            if (c0122c02.f739a != 0) {
                int[] iArr4 = c0122c02.f744f;
                if (iArr4.length > 0) {
                    if (this.f1018a.getAutoSizeStepGranularity() != -1.0f) {
                        this.f1018a.setAutoSizeTextTypeUniformWithConfiguration(Math.round(this.f1026i.f742d), Math.round(this.f1026i.f743e), Math.round(this.f1026i.f741c), 0);
                    } else {
                        this.f1018a.setAutoSizeTextTypeUniformWithPresetSizes(iArr4, 0);
                    }
                }
            }
        }
        C0123c1 c0123c13 = new C0123c1(context, context.obtainStyledAttributes(attributeSet, C0385m.f2363s));
        int m348l2 = c0123c13.m348l(8, -1);
        Drawable m405b = m348l2 != -1 ? m401a.m405b(context, m348l2) : null;
        int m348l3 = c0123c13.m348l(13, -1);
        Drawable m405b2 = m348l3 != -1 ? m401a.m405b(context, m348l3) : null;
        int m348l4 = c0123c13.m348l(9, -1);
        Drawable m405b3 = m348l4 != -1 ? m401a.m405b(context, m348l4) : null;
        int m348l5 = c0123c13.m348l(6, -1);
        Drawable m405b4 = m348l5 != -1 ? m401a.m405b(context, m348l5) : null;
        int m348l6 = c0123c13.m348l(10, -1);
        Drawable m405b5 = m348l6 != -1 ? m401a.m405b(context, m348l6) : null;
        int m348l7 = c0123c13.m348l(7, -1);
        Drawable m405b6 = m348l7 != -1 ? m401a.m405b(context, m348l7) : null;
        if (m405b5 != null || m405b6 != null) {
            Drawable[] compoundDrawablesRelative = this.f1018a.getCompoundDrawablesRelative();
            TextView textView3 = this.f1018a;
            if (m405b5 == null) {
                m405b5 = compoundDrawablesRelative[0];
            }
            if (m405b2 == null) {
                m405b2 = compoundDrawablesRelative[1];
            }
            if (m405b6 == null) {
                m405b6 = compoundDrawablesRelative[2];
            }
            if (m405b4 == null) {
                m405b4 = compoundDrawablesRelative[3];
            }
            textView3.setCompoundDrawablesRelativeWithIntrinsicBounds(m405b5, m405b2, m405b6, m405b4);
        } else if (m405b != null || m405b2 != null || m405b3 != null || m405b4 != null) {
            Drawable[] compoundDrawablesRelative2 = this.f1018a.getCompoundDrawablesRelative();
            if (compoundDrawablesRelative2[0] == null && compoundDrawablesRelative2[2] == null) {
                Drawable[] compoundDrawables = this.f1018a.getCompoundDrawables();
                TextView textView4 = this.f1018a;
                if (m405b == null) {
                    m405b = compoundDrawables[0];
                }
                if (m405b2 == null) {
                    m405b2 = compoundDrawables[1];
                }
                if (m405b3 == null) {
                    m405b3 = compoundDrawables[2];
                }
                if (m405b4 == null) {
                    m405b4 = compoundDrawables[3];
                }
                textView4.setCompoundDrawablesWithIntrinsicBounds(m405b, m405b2, m405b3, m405b4);
            } else {
                TextView textView5 = this.f1018a;
                Drawable drawable = compoundDrawablesRelative2[0];
                if (m405b2 == null) {
                    m405b2 = compoundDrawablesRelative2[1];
                }
                Drawable drawable2 = compoundDrawablesRelative2[2];
                if (m405b4 == null) {
                    m405b4 = compoundDrawablesRelative2[3];
                }
                textView5.setCompoundDrawablesRelativeWithIntrinsicBounds(drawable, m405b2, drawable2, m405b4);
            }
        }
        if (c0123c13.m351o(11)) {
            ColorStateList m339c = c0123c13.m339c(11);
            TextView textView6 = this.f1018a;
            Objects.requireNonNull(textView6);
            textView6.setCompoundDrawableTintList(m339c);
        }
        if (c0123c13.m351o(12)) {
            i7 = -1;
            PorterDuff.Mode m389b = C0134g0.m389b(c0123c13.m346j(12, -1), null);
            TextView textView7 = this.f1018a;
            Objects.requireNonNull(textView7);
            textView7.setCompoundDrawableTintMode(m389b);
        } else {
            i7 = -1;
        }
        int m342f = c0123c13.m342f(14, i7);
        int m342f2 = c0123c13.m342f(17, i7);
        int m342f3 = c0123c13.m342f(18, i7);
        c0123c13.m352r();
        if (m342f != i7) {
            C0903d.m2451b(this.f1018a, m342f);
        }
        if (m342f2 != i7) {
            C0903d.m2452c(this.f1018a, m342f2);
        }
        if (m342f3 != i7) {
            C0903d.m2453d(this.f1018a, m342f3);
        }
    }

    /* renamed from: g */
    public final void m503g(Context context, int i6) {
        String m349m;
        C0123c1 c0123c1 = new C0123c1(context, context.obtainStyledAttributes(i6, C0385m.f2314F));
        if (c0123c1.m351o(14)) {
            m505i(c0123c1.m337a(14, false));
        }
        int i7 = Build.VERSION.SDK_INT;
        if (c0123c1.m351o(0) && c0123c1.m342f(0, -1) == 0) {
            this.f1018a.setTextSize(0, 0.0f);
        }
        m511o(context, c0123c1);
        if (i7 >= 26 && c0123c1.m351o(13) && (m349m = c0123c1.m349m(13)) != null) {
            this.f1018a.setFontVariationSettings(m349m);
        }
        c0123c1.m352r();
        Typeface typeface = this.f1029l;
        if (typeface != null) {
            this.f1018a.setTypeface(typeface, this.f1027j);
        }
    }

    /* renamed from: h */
    public final void m504h(TextView textView, InputConnection inputConnection, EditorInfo editorInfo) {
        int i6 = Build.VERSION.SDK_INT;
        if (i6 >= 30 || inputConnection == null) {
            return;
        }
        CharSequence text = textView.getText();
        if (i6 >= 30) {
            C0876a.a.m2421a(editorInfo, text);
            return;
        }
        Objects.requireNonNull(text);
        if (i6 >= 30) {
            C0876a.a.m2421a(editorInfo, text);
            return;
        }
        int i7 = editorInfo.initialSelStart;
        int i8 = editorInfo.initialSelEnd;
        int i9 = i7 > i8 ? i8 + 0 : i7 + 0;
        int i10 = i7 > i8 ? i7 - 0 : i8 + 0;
        int length = text.length();
        if (i9 >= 0 && i10 <= length) {
            int i11 = editorInfo.inputType & 4095;
            if (!(i11 == 129 || i11 == 225 || i11 == 18)) {
                if (length <= 2048) {
                    C0876a.m2420d(editorInfo, text, i9, i10);
                    return;
                }
                int i12 = i10 - i9;
                int i13 = i12 > 1024 ? 0 : i12;
                int i14 = 2048 - i13;
                int min = Math.min(text.length() - i10, i14 - Math.min(i9, (int) (i14 * 0.8d)));
                int min2 = Math.min(i9, i14 - min);
                int i15 = i9 - min2;
                if (C0876a.m2418b(text, i15, 0)) {
                    i15++;
                    min2--;
                }
                if (C0876a.m2418b(text, (i10 + min) - 1, 1)) {
                    min--;
                }
                CharSequence concat = i13 != i12 ? TextUtils.concat(text.subSequence(i15, i15 + min2), text.subSequence(i10, min + i10)) : text.subSequence(i15, min2 + i13 + min + i15);
                int i16 = min2 + 0;
                C0876a.m2420d(editorInfo, concat, i16, i13 + i16);
                return;
            }
        }
        C0876a.m2420d(editorInfo, null, 0, 0);
    }

    /* renamed from: i */
    public final void m505i(boolean z5) {
        this.f1018a.setAllCaps(z5);
    }

    /* renamed from: j */
    public final void m506j(int i6, int i7, int i8, int i9) {
        C0122c0 c0122c0 = this.f1026i;
        if (c0122c0.m331i()) {
            DisplayMetrics displayMetrics = c0122c0.f748j.getResources().getDisplayMetrics();
            c0122c0.m332j(TypedValue.applyDimension(i9, i6, displayMetrics), TypedValue.applyDimension(i9, i7, displayMetrics), TypedValue.applyDimension(i9, i8, displayMetrics));
            if (c0122c0.m329g()) {
                c0122c0.m325a();
            }
        }
    }

    /* renamed from: k */
    public final void m507k(int[] iArr, int i6) {
        C0122c0 c0122c0 = this.f1026i;
        if (c0122c0.m331i()) {
            int length = iArr.length;
            if (length > 0) {
                int[] iArr2 = new int[length];
                if (i6 == 0) {
                    iArr2 = Arrays.copyOf(iArr, length);
                } else {
                    DisplayMetrics displayMetrics = c0122c0.f748j.getResources().getDisplayMetrics();
                    for (int i7 = 0; i7 < length; i7++) {
                        iArr2[i7] = Math.round(TypedValue.applyDimension(i6, iArr[i7], displayMetrics));
                    }
                }
                c0122c0.f744f = c0122c0.m326b(iArr2);
                if (!c0122c0.m330h()) {
                    StringBuilder m104h = C0052a.m104h("None of the preset sizes is valid: ");
                    m104h.append(Arrays.toString(iArr));
                    throw new IllegalArgumentException(m104h.toString());
                }
            } else {
                c0122c0.f745g = false;
            }
            if (c0122c0.m329g()) {
                c0122c0.m325a();
            }
        }
    }

    /* renamed from: l */
    public final void m508l(int i6) {
        C0122c0 c0122c0 = this.f1026i;
        if (c0122c0.m331i()) {
            if (i6 == 0) {
                c0122c0.f739a = 0;
                c0122c0.f742d = -1.0f;
                c0122c0.f743e = -1.0f;
                c0122c0.f741c = -1.0f;
                c0122c0.f744f = new int[0];
                c0122c0.f740b = false;
                return;
            }
            if (i6 != 1) {
                throw new IllegalArgumentException(C0174y.m490h("Unknown auto-size text type: ", i6));
            }
            DisplayMetrics displayMetrics = c0122c0.f748j.getResources().getDisplayMetrics();
            c0122c0.m332j(TypedValue.applyDimension(2, 12.0f, displayMetrics), TypedValue.applyDimension(2, 112.0f, displayMetrics), 1.0f);
            if (c0122c0.m329g()) {
                c0122c0.m325a();
            }
        }
    }

    /* renamed from: m */
    public final void m509m(ColorStateList colorStateList) {
        if (this.f1025h == null) {
            this.f1025h = new C0117a1();
        }
        C0117a1 c0117a1 = this.f1025h;
        c0117a1.f703a = colorStateList;
        c0117a1.f706d = colorStateList != null;
        this.f1019b = c0117a1;
        this.f1020c = c0117a1;
        this.f1021d = c0117a1;
        this.f1022e = c0117a1;
        this.f1023f = c0117a1;
        this.f1024g = c0117a1;
    }

    /* renamed from: n */
    public final void m510n(PorterDuff.Mode mode) {
        if (this.f1025h == null) {
            this.f1025h = new C0117a1();
        }
        C0117a1 c0117a1 = this.f1025h;
        c0117a1.f704b = mode;
        c0117a1.f705c = mode != null;
        this.f1019b = c0117a1;
        this.f1020c = c0117a1;
        this.f1021d = c0117a1;
        this.f1022e = c0117a1;
        this.f1023f = c0117a1;
        this.f1024g = c0117a1;
    }

    /* renamed from: o */
    public final void m511o(Context context, C0123c1 c0123c1) {
        String m349m;
        Typeface create;
        Typeface typeface;
        this.f1027j = c0123c1.m346j(2, this.f1027j);
        int i6 = Build.VERSION.SDK_INT;
        if (i6 >= 28) {
            int m346j = c0123c1.m346j(11, -1);
            this.f1028k = m346j;
            if (m346j != -1) {
                this.f1027j = (this.f1027j & 2) | 0;
            }
        }
        if (!c0123c1.m351o(10) && !c0123c1.m351o(12)) {
            if (c0123c1.m351o(1)) {
                this.f1030m = false;
                int m346j2 = c0123c1.m346j(1, 1);
                if (m346j2 == 1) {
                    typeface = Typeface.SANS_SERIF;
                } else if (m346j2 == 2) {
                    typeface = Typeface.SERIF;
                } else if (m346j2 != 3) {
                    return;
                } else {
                    typeface = Typeface.MONOSPACE;
                }
                this.f1029l = typeface;
                return;
            }
            return;
        }
        this.f1029l = null;
        int i7 = c0123c1.m351o(12) ? 12 : 10;
        int i8 = this.f1028k;
        int i9 = this.f1027j;
        if (!context.isRestricted()) {
            try {
                Typeface m345i = c0123c1.m345i(i7, this.f1027j, new a(i8, i9, new WeakReference(this.f1018a)));
                if (m345i != null) {
                    if (i6 >= 28 && this.f1028k != -1) {
                        m345i = Typeface.create(Typeface.create(m345i, 0), this.f1028k, (this.f1027j & 2) != 0);
                    }
                    this.f1029l = m345i;
                }
                this.f1030m = this.f1029l == null;
            } catch (Resources.NotFoundException | UnsupportedOperationException unused) {
            }
        }
        if (this.f1029l != null || (m349m = c0123c1.m349m(i7)) == null) {
            return;
        }
        if (Build.VERSION.SDK_INT < 28 || this.f1028k == -1) {
            create = Typeface.create(m349m, this.f1027j);
        } else {
            create = Typeface.create(Typeface.create(m349m, 0), this.f1028k, (this.f1027j & 2) != 0);
        }
        this.f1029l = create;
    }
}
