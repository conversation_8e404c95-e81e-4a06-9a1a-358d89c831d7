package androidx.appcompat.widget;

import android.content.Context;
import android.content.ContextWrapper;

/* renamed from: androidx.appcompat.widget.z0 */
/* loaded from: classes.dex */
public final class C0177z0 extends ContextWrapper {

    /* renamed from: a */
    public static final Object f1035a = new Object();

    /* renamed from: a */
    public static Context m514a(Context context) {
        if (!(context instanceof C0177z0) && !(context.getResources() instanceof C0120b1)) {
            context.getResources();
            int i6 = C0141i1.f834a;
        }
        return context;
    }
}
