package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.widget.CheckBox;
import com.liaoyuan.aicast.R;
import java.util.Objects;
import p028e.C0750a;

/* renamed from: androidx.appcompat.widget.g */
/* loaded from: classes.dex */
public class C0133g extends CheckBox {

    /* renamed from: j */
    public final C0139i f787j;

    /* renamed from: k */
    public final C0127e f788k;

    /* renamed from: l */
    public final C0176z f789l;

    public C0133g(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, R.attr.checkboxStyle);
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C0133g(Context context, AttributeSet attributeSet, int i6) {
        super(context, attributeSet, i6);
        C0177z0.m514a(context);
        C0173x0.m480a(this, getContext());
        C0139i c0139i = new C0139i(this);
        this.f787j = c0139i;
        c0139i.m399b(attributeSet, i6);
        C0127e c0127e = new C0127e(this);
        this.f788k = c0127e;
        c0127e.m356d(attributeSet, i6);
        C0176z c0176z = new C0176z(this);
        this.f789l = c0176z;
        c0176z.m502f(attributeSet, i6);
    }

    @Override // android.widget.CompoundButton, android.widget.TextView, android.view.View
    public void drawableStateChanged() {
        super.drawableStateChanged();
        C0127e c0127e = this.f788k;
        if (c0127e != null) {
            c0127e.m353a();
        }
        C0176z c0176z = this.f789l;
        if (c0176z != null) {
            c0176z.m499b();
        }
    }

    @Override // android.widget.CompoundButton, android.widget.TextView
    public int getCompoundPaddingLeft() {
        int compoundPaddingLeft = super.getCompoundPaddingLeft();
        C0139i c0139i = this.f787j;
        if (c0139i != null) {
            Objects.requireNonNull(c0139i);
        }
        return compoundPaddingLeft;
    }

    public ColorStateList getSupportBackgroundTintList() {
        C0127e c0127e = this.f788k;
        if (c0127e != null) {
            return c0127e.m354b();
        }
        return null;
    }

    public PorterDuff.Mode getSupportBackgroundTintMode() {
        C0127e c0127e = this.f788k;
        if (c0127e != null) {
            return c0127e.m355c();
        }
        return null;
    }

    public ColorStateList getSupportButtonTintList() {
        C0139i c0139i = this.f787j;
        if (c0139i != null) {
            return c0139i.f829b;
        }
        return null;
    }

    public PorterDuff.Mode getSupportButtonTintMode() {
        C0139i c0139i = this.f787j;
        if (c0139i != null) {
            return c0139i.f830c;
        }
        return null;
    }

    @Override // android.view.View
    public void setBackgroundDrawable(Drawable drawable) {
        super.setBackgroundDrawable(drawable);
        C0127e c0127e = this.f788k;
        if (c0127e != null) {
            c0127e.m357e();
        }
    }

    @Override // android.view.View
    public void setBackgroundResource(int i6) {
        super.setBackgroundResource(i6);
        C0127e c0127e = this.f788k;
        if (c0127e != null) {
            c0127e.m358f(i6);
        }
    }

    @Override // android.widget.CompoundButton
    public void setButtonDrawable(int i6) {
        setButtonDrawable(C0750a.m2138a(getContext(), i6));
    }

    @Override // android.widget.CompoundButton
    public void setButtonDrawable(Drawable drawable) {
        super.setButtonDrawable(drawable);
        C0139i c0139i = this.f787j;
        if (c0139i != null) {
            if (c0139i.f833f) {
                c0139i.f833f = false;
            } else {
                c0139i.f833f = true;
                c0139i.m398a();
            }
        }
    }

    public void setSupportBackgroundTintList(ColorStateList colorStateList) {
        C0127e c0127e = this.f788k;
        if (c0127e != null) {
            c0127e.m360h(colorStateList);
        }
    }

    public void setSupportBackgroundTintMode(PorterDuff.Mode mode) {
        C0127e c0127e = this.f788k;
        if (c0127e != null) {
            c0127e.m361i(mode);
        }
    }

    public void setSupportButtonTintList(ColorStateList colorStateList) {
        C0139i c0139i = this.f787j;
        if (c0139i != null) {
            c0139i.f829b = colorStateList;
            c0139i.f831d = true;
            c0139i.m398a();
        }
    }

    public void setSupportButtonTintMode(PorterDuff.Mode mode) {
        C0139i c0139i = this.f787j;
        if (c0139i != null) {
            c0139i.f830c = mode;
            c0139i.f832e = true;
            c0139i.m398a();
        }
    }
}
