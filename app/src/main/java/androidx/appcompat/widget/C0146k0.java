package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import android.widget.LinearLayout;
import androidx.activity.result.C0052a;
import p008b0.C0385m;
import p028e.C0750a;
import p029e0.C0766p;

/* renamed from: androidx.appcompat.widget.k0 */
/* loaded from: classes.dex */
public class C0146k0 extends ViewGroup {

    /* renamed from: j */
    public boolean f860j;

    /* renamed from: k */
    public int f861k;

    /* renamed from: l */
    public int f862l;

    /* renamed from: m */
    public int f863m;

    /* renamed from: n */
    public int f864n;

    /* renamed from: o */
    public int f865o;

    /* renamed from: p */
    public float f866p;

    /* renamed from: q */
    public boolean f867q;

    /* renamed from: r */
    public int[] f868r;

    /* renamed from: s */
    public int[] f869s;

    /* renamed from: t */
    public Drawable f870t;

    /* renamed from: u */
    public int f871u;

    /* renamed from: v */
    public int f872v;

    /* renamed from: w */
    public int f873w;

    /* renamed from: x */
    public int f874x;

    /* renamed from: androidx.appcompat.widget.k0$a */
    public static class a extends LinearLayout.LayoutParams {
        public a(int i6, int i7) {
            super(i6, i7);
        }

        public a(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
        }

        public a(ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
        }
    }

    public C0146k0(Context context) {
        this(context, null);
    }

    public C0146k0(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, 0);
    }

    public C0146k0(Context context, AttributeSet attributeSet, int i6) {
        super(context, attributeSet, i6);
        int resourceId;
        this.f860j = true;
        this.f861k = -1;
        this.f862l = 0;
        this.f864n = 8388659;
        int[] iArr = C0385m.f2371w;
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, iArr, i6, 0);
        C0766p.m2186s(this, context, iArr, attributeSet, obtainStyledAttributes, i6);
        int i7 = obtainStyledAttributes.getInt(1, -1);
        if (i7 >= 0) {
            setOrientation(i7);
        }
        int i8 = obtainStyledAttributes.getInt(0, -1);
        if (i8 >= 0) {
            setGravity(i8);
        }
        boolean z5 = obtainStyledAttributes.getBoolean(2, true);
        if (!z5) {
            setBaselineAligned(z5);
        }
        this.f866p = obtainStyledAttributes.getFloat(4, -1.0f);
        this.f861k = obtainStyledAttributes.getInt(3, -1);
        this.f867q = obtainStyledAttributes.getBoolean(7, false);
        setDividerDrawable((!obtainStyledAttributes.hasValue(5) || (resourceId = obtainStyledAttributes.getResourceId(5, 0)) == 0) ? obtainStyledAttributes.getDrawable(5) : C0750a.m2138a(context, resourceId));
        this.f873w = obtainStyledAttributes.getInt(8, 0);
        this.f874x = obtainStyledAttributes.getDimensionPixelSize(6, 0);
        obtainStyledAttributes.recycle();
    }

    @Override // android.view.ViewGroup
    public boolean checkLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return layoutParams instanceof a;
    }

    /* renamed from: f */
    public final void m416f(Canvas canvas, int i6) {
        this.f870t.setBounds(getPaddingLeft() + this.f874x, i6, (getWidth() - getPaddingRight()) - this.f874x, this.f872v + i6);
        this.f870t.draw(canvas);
    }

    /* renamed from: g */
    public final void m417g(Canvas canvas, int i6) {
        this.f870t.setBounds(i6, getPaddingTop() + this.f874x, this.f871u + i6, (getHeight() - getPaddingBottom()) - this.f874x);
        this.f870t.draw(canvas);
    }

    @Override // android.view.View
    public int getBaseline() {
        int i6;
        if (this.f861k < 0) {
            return super.getBaseline();
        }
        int childCount = getChildCount();
        int i7 = this.f861k;
        if (childCount <= i7) {
            throw new RuntimeException("mBaselineAlignedChildIndex of LinearLayout set to an index that is out of bounds.");
        }
        View childAt = getChildAt(i7);
        int baseline = childAt.getBaseline();
        if (baseline == -1) {
            if (this.f861k == 0) {
                return -1;
            }
            throw new RuntimeException("mBaselineAlignedChildIndex of LinearLayout points to a View that doesn't know how to get its baseline.");
        }
        int i8 = this.f862l;
        if (this.f863m == 1 && (i6 = this.f864n & 112) != 48) {
            if (i6 == 16) {
                i8 += ((((getBottom() - getTop()) - getPaddingTop()) - getPaddingBottom()) - this.f865o) / 2;
            } else if (i6 == 80) {
                i8 = ((getBottom() - getTop()) - getPaddingBottom()) - this.f865o;
            }
        }
        return i8 + ((LinearLayout.LayoutParams) ((a) childAt.getLayoutParams())).topMargin + baseline;
    }

    public int getBaselineAlignedChildIndex() {
        return this.f861k;
    }

    public Drawable getDividerDrawable() {
        return this.f870t;
    }

    public int getDividerPadding() {
        return this.f874x;
    }

    public int getDividerWidth() {
        return this.f871u;
    }

    public int getGravity() {
        return this.f864n;
    }

    public int getOrientation() {
        return this.f863m;
    }

    public int getShowDividers() {
        return this.f873w;
    }

    public int getVirtualChildCount() {
        return getChildCount();
    }

    public float getWeightSum() {
        return this.f866p;
    }

    @Override // android.view.ViewGroup
    /* renamed from: h */
    public a mo250h() {
        int i6 = this.f863m;
        if (i6 == 0) {
            return new a(-2, -2);
        }
        if (i6 == 1) {
            return new a(-1, -2);
        }
        return null;
    }

    @Override // android.view.ViewGroup
    /* renamed from: i, reason: merged with bridge method [inline-methods] */
    public a generateLayoutParams(AttributeSet attributeSet) {
        return new a(getContext(), attributeSet);
    }

    @Override // android.view.ViewGroup
    /* renamed from: j */
    public a mo252j(ViewGroup.LayoutParams layoutParams) {
        return new a(layoutParams);
    }

    /* renamed from: k */
    public final boolean m418k(int i6) {
        if (i6 == 0) {
            return (this.f873w & 1) != 0;
        }
        if (i6 == getChildCount()) {
            return (this.f873w & 4) != 0;
        }
        if ((this.f873w & 2) == 0) {
            return false;
        }
        for (int i7 = i6 - 1; i7 >= 0; i7--) {
            if (getChildAt(i7).getVisibility() != 8) {
                return true;
            }
        }
        return false;
    }

    @Override // android.view.View
    public final void onDraw(Canvas canvas) {
        int right;
        int left;
        int i6;
        if (this.f870t == null) {
            return;
        }
        int i7 = 0;
        if (this.f863m == 1) {
            int virtualChildCount = getVirtualChildCount();
            while (i7 < virtualChildCount) {
                View childAt = getChildAt(i7);
                if (childAt != null && childAt.getVisibility() != 8 && m418k(i7)) {
                    m416f(canvas, (childAt.getTop() - ((LinearLayout.LayoutParams) ((a) childAt.getLayoutParams())).topMargin) - this.f872v);
                }
                i7++;
            }
            if (m418k(virtualChildCount)) {
                View childAt2 = getChildAt(virtualChildCount - 1);
                m416f(canvas, childAt2 == null ? (getHeight() - getPaddingBottom()) - this.f872v : childAt2.getBottom() + ((LinearLayout.LayoutParams) ((a) childAt2.getLayoutParams())).bottomMargin);
                return;
            }
            return;
        }
        int virtualChildCount2 = getVirtualChildCount();
        boolean m414b = C0144j1.m414b(this);
        while (i7 < virtualChildCount2) {
            View childAt3 = getChildAt(i7);
            if (childAt3 != null && childAt3.getVisibility() != 8 && m418k(i7)) {
                a aVar = (a) childAt3.getLayoutParams();
                m417g(canvas, m414b ? childAt3.getRight() + ((LinearLayout.LayoutParams) aVar).rightMargin : (childAt3.getLeft() - ((LinearLayout.LayoutParams) aVar).leftMargin) - this.f871u);
            }
            i7++;
        }
        if (m418k(virtualChildCount2)) {
            View childAt4 = getChildAt(virtualChildCount2 - 1);
            if (childAt4 != null) {
                a aVar2 = (a) childAt4.getLayoutParams();
                if (m414b) {
                    left = childAt4.getLeft();
                    i6 = ((LinearLayout.LayoutParams) aVar2).leftMargin;
                    right = (left - i6) - this.f871u;
                } else {
                    right = childAt4.getRight() + ((LinearLayout.LayoutParams) aVar2).rightMargin;
                }
            } else if (m414b) {
                right = getPaddingLeft();
            } else {
                left = getWidth();
                i6 = getPaddingRight();
                right = (left - i6) - this.f871u;
            }
            m417g(canvas, right);
        }
    }

    @Override // android.view.View
    public final void onInitializeAccessibilityEvent(AccessibilityEvent accessibilityEvent) {
        super.onInitializeAccessibilityEvent(accessibilityEvent);
        accessibilityEvent.setClassName("androidx.appcompat.widget.LinearLayoutCompat");
    }

    @Override // android.view.View
    public final void onInitializeAccessibilityNodeInfo(AccessibilityNodeInfo accessibilityNodeInfo) {
        super.onInitializeAccessibilityNodeInfo(accessibilityNodeInfo);
        accessibilityNodeInfo.setClassName("androidx.appcompat.widget.LinearLayoutCompat");
    }

    /* JADX WARN: Removed duplicated region for block: B:26:0x0098  */
    /* JADX WARN: Removed duplicated region for block: B:61:0x0150  */
    /* JADX WARN: Removed duplicated region for block: B:64:0x0159  */
    /* JADX WARN: Removed duplicated region for block: B:71:0x019b  */
    /* JADX WARN: Removed duplicated region for block: B:80:0x0189  */
    @Override // android.view.ViewGroup, android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void onLayout(boolean r26, int r27, int r28, int r29, int r30) {
        /*
            Method dump skipped, instructions count: 463
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.C0146k0.onLayout(boolean, int, int, int, int):void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:166:0x02b2, code lost:
    
        if (r13 < 0) goto L136;
     */
    /* JADX WARN: Code restructure failed: missing block: B:406:0x0731, code lost:
    
        if (r7 < 0) goto L336;
     */
    /* JADX WARN: Removed duplicated region for block: B:173:0x02ef  */
    /* JADX WARN: Removed duplicated region for block: B:237:0x055d  */
    /* JADX WARN: Removed duplicated region for block: B:242:0x0568  */
    /* JADX WARN: Removed duplicated region for block: B:258:0x0492  */
    /* JADX WARN: Removed duplicated region for block: B:261:0x04bf  */
    /* JADX WARN: Removed duplicated region for block: B:264:0x04cc  */
    /* JADX WARN: Removed duplicated region for block: B:266:0x04ee  */
    /* JADX WARN: Removed duplicated region for block: B:267:0x04da  */
    /* JADX WARN: Removed duplicated region for block: B:268:0x04c4  */
    /* JADX WARN: Removed duplicated region for block: B:269:0x0497  */
    /* JADX WARN: Removed duplicated region for block: B:293:0x05ff  */
    /* JADX WARN: Removed duplicated region for block: B:336:0x0862  */
    /* JADX WARN: Removed duplicated region for block: B:340:0x0889  */
    /* JADX WARN: Removed duplicated region for block: B:354:? A[RETURN, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:358:0x06be  */
    /* JADX WARN: Removed duplicated region for block: B:361:0x06db  */
    @Override // android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void onMeasure(int r38, int r39) {
        /*
            Method dump skipped, instructions count: 2250
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.C0146k0.onMeasure(int, int):void");
    }

    public void setBaselineAligned(boolean z5) {
        this.f860j = z5;
    }

    public void setBaselineAlignedChildIndex(int i6) {
        if (i6 >= 0 && i6 < getChildCount()) {
            this.f861k = i6;
            return;
        }
        StringBuilder m104h = C0052a.m104h("base aligned child index out of range (0, ");
        m104h.append(getChildCount());
        m104h.append(")");
        throw new IllegalArgumentException(m104h.toString());
    }

    public void setDividerDrawable(Drawable drawable) {
        if (drawable == this.f870t) {
            return;
        }
        this.f870t = drawable;
        if (drawable != null) {
            this.f871u = drawable.getIntrinsicWidth();
            this.f872v = drawable.getIntrinsicHeight();
        } else {
            this.f871u = 0;
            this.f872v = 0;
        }
        setWillNotDraw(drawable == null);
        requestLayout();
    }

    public void setDividerPadding(int i6) {
        this.f874x = i6;
    }

    public void setGravity(int i6) {
        if (this.f864n != i6) {
            if ((8388615 & i6) == 0) {
                i6 |= 8388611;
            }
            if ((i6 & 112) == 0) {
                i6 |= 48;
            }
            this.f864n = i6;
            requestLayout();
        }
    }

    public void setHorizontalGravity(int i6) {
        int i7 = i6 & 8388615;
        int i8 = this.f864n;
        if ((8388615 & i8) != i7) {
            this.f864n = i7 | ((-8388616) & i8);
            requestLayout();
        }
    }

    public void setMeasureWithLargestChildEnabled(boolean z5) {
        this.f867q = z5;
    }

    public void setOrientation(int i6) {
        if (this.f863m != i6) {
            this.f863m = i6;
            requestLayout();
        }
    }

    public void setShowDividers(int i6) {
        if (i6 != this.f873w) {
            requestLayout();
        }
        this.f873w = i6;
    }

    public void setVerticalGravity(int i6) {
        int i7 = i6 & 112;
        int i8 = this.f864n;
        if ((i8 & 112) != i7) {
            this.f864n = i7 | (i8 & (-113));
            requestLayout();
        }
    }

    public void setWeightSum(float f6) {
        this.f866p = Math.max(0.0f, f6);
    }

    @Override // android.view.ViewGroup
    public final boolean shouldDelayChildPressedState() {
        return false;
    }
}
