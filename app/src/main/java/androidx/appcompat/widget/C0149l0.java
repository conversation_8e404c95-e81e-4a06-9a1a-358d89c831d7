package androidx.appcompat.widget;

import android.view.View;
import android.widget.AdapterView;

/* renamed from: androidx.appcompat.widget.l0 */
/* loaded from: classes.dex */
public final class C0149l0 implements AdapterView.OnItemSelectedListener {

    /* renamed from: j */
    public final /* synthetic */ C0151m0 f877j;

    public C0149l0(C0151m0 c0151m0) {
        this.f877j = c0151m0;
    }

    @Override // android.widget.AdapterView.OnItemSelectedListener
    public final void onItemSelected(AdapterView<?> adapterView, View view, int i6, long j6) {
        C0137h0 c0137h0;
        if (i6 == -1 || (c0137h0 = this.f877j.f892l) == null) {
            return;
        }
        c0137h0.setListSelectionHidden(false);
    }

    @Override // android.widget.AdapterView.OnItemSelectedListener
    public final void onNothingSelected(AdapterView<?> adapterView) {
    }
}
