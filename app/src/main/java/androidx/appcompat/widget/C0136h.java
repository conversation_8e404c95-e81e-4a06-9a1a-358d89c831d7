package androidx.appcompat.widget;

import android.R;
import android.content.Context;
import android.util.AttributeSet;
import android.view.ActionMode;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputConnection;
import android.widget.CheckedTextView;
import p008b0.C0385m;
import p028e.C0750a;
import p050h0.C0903d;

/* renamed from: androidx.appcompat.widget.h */
/* loaded from: classes.dex */
public final class C0136h extends CheckedTextView {

    /* renamed from: k */
    public static final int[] f804k = {R.attr.checkMark};

    /* renamed from: j */
    public final C0176z f805j;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C0136h(Context context, AttributeSet attributeSet) {
        super(context, attributeSet, R.attr.checkedTextViewStyle);
        C0177z0.m514a(context);
        C0173x0.m480a(this, getContext());
        C0176z c0176z = new C0176z(this);
        this.f805j = c0176z;
        c0176z.m502f(attributeSet, R.attr.checkedTextViewStyle);
        c0176z.m499b();
        C0123c1 m336q = C0123c1.m336q(getContext(), attributeSet, f804k, R.attr.checkedTextViewStyle);
        setCheckMarkDrawable(m336q.m343g(0));
        m336q.m352r();
    }

    @Override // android.widget.CheckedTextView, android.widget.TextView, android.view.View
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        C0176z c0176z = this.f805j;
        if (c0176z != null) {
            c0176z.m499b();
        }
    }

    @Override // android.widget.TextView, android.view.View
    public final InputConnection onCreateInputConnection(EditorInfo editorInfo) {
        InputConnection onCreateInputConnection = super.onCreateInputConnection(editorInfo);
        C0385m.m1422n(onCreateInputConnection, editorInfo, this);
        return onCreateInputConnection;
    }

    @Override // android.widget.CheckedTextView
    public void setCheckMarkDrawable(int i6) {
        setCheckMarkDrawable(C0750a.m2138a(getContext(), i6));
    }

    @Override // android.widget.TextView
    public void setCustomSelectionActionModeCallback(ActionMode.Callback callback) {
        super.setCustomSelectionActionModeCallback(C0903d.m2455f(this, callback));
    }

    @Override // android.widget.TextView
    public final void setTextAppearance(Context context, int i6) {
        super.setTextAppearance(context, i6);
        C0176z c0176z = this.f805j;
        if (c0176z != null) {
            c0176z.m503g(context, i6);
        }
    }
}
