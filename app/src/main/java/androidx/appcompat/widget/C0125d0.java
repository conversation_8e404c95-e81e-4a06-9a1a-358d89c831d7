package androidx.appcompat.widget;

import android.R;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.widget.ToggleButton;

/* renamed from: androidx.appcompat.widget.d0 */
/* loaded from: classes.dex */
public final class C0125d0 extends ToggleButton {

    /* renamed from: j */
    public final C0127e f756j;

    /* renamed from: k */
    public final C0176z f757k;

    public C0125d0(Context context, AttributeSet attributeSet) {
        super(context, attributeSet, R.attr.buttonStyleToggle);
        C0173x0.m480a(this, getContext());
        C0127e c0127e = new C0127e(this);
        this.f756j = c0127e;
        c0127e.m356d(attributeSet, R.attr.buttonStyleToggle);
        C0176z c0176z = new C0176z(this);
        this.f757k = c0176z;
        c0176z.m502f(attributeSet, R.attr.buttonStyleToggle);
    }

    @Override // android.widget.ToggleButton, android.widget.CompoundButton, android.widget.TextView, android.view.View
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        C0127e c0127e = this.f756j;
        if (c0127e != null) {
            c0127e.m353a();
        }
        C0176z c0176z = this.f757k;
        if (c0176z != null) {
            c0176z.m499b();
        }
    }

    public ColorStateList getSupportBackgroundTintList() {
        C0127e c0127e = this.f756j;
        if (c0127e != null) {
            return c0127e.m354b();
        }
        return null;
    }

    public PorterDuff.Mode getSupportBackgroundTintMode() {
        C0127e c0127e = this.f756j;
        if (c0127e != null) {
            return c0127e.m355c();
        }
        return null;
    }

    @Override // android.widget.ToggleButton, android.view.View
    public void setBackgroundDrawable(Drawable drawable) {
        super.setBackgroundDrawable(drawable);
        C0127e c0127e = this.f756j;
        if (c0127e != null) {
            c0127e.m357e();
        }
    }

    @Override // android.view.View
    public void setBackgroundResource(int i6) {
        super.setBackgroundResource(i6);
        C0127e c0127e = this.f756j;
        if (c0127e != null) {
            c0127e.m358f(i6);
        }
    }

    public void setSupportBackgroundTintList(ColorStateList colorStateList) {
        C0127e c0127e = this.f756j;
        if (c0127e != null) {
            c0127e.m360h(colorStateList);
        }
    }

    public void setSupportBackgroundTintMode(PorterDuff.Mode mode) {
        C0127e c0127e = this.f756j;
        if (c0127e != null) {
            c0127e.m361i(mode);
        }
    }
}
