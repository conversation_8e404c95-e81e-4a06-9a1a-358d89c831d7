package androidx.appcompat.widget;

import android.R;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.ActionMode;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputConnection;
import android.widget.AutoCompleteTextView;
import p008b0.C0385m;
import p028e.C0750a;
import p050h0.C0903d;

/* renamed from: androidx.appcompat.widget.d */
/* loaded from: classes.dex */
public class C0124d extends AutoCompleteTextView {

    /* renamed from: l */
    public static final int[] f753l = {R.attr.popupBackground};

    /* renamed from: j */
    public final C0127e f754j;

    /* renamed from: k */
    public final C0176z f755k;

    public C0124d(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, com.liaoyuan.aicast.R.attr.autoCompleteTextViewStyle);
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C0124d(Context context, AttributeSet attributeSet, int i6) {
        super(context, attributeSet, com.liaoyuan.aicast.R.attr.autoCompleteTextViewStyle);
        C0177z0.m514a(context);
        C0173x0.m480a(this, getContext());
        C0123c1 m336q = C0123c1.m336q(getContext(), attributeSet, f753l, com.liaoyuan.aicast.R.attr.autoCompleteTextViewStyle);
        if (m336q.m351o(0)) {
            setDropDownBackgroundDrawable(m336q.m343g(0));
        }
        m336q.m352r();
        C0127e c0127e = new C0127e(this);
        this.f754j = c0127e;
        c0127e.m356d(attributeSet, com.liaoyuan.aicast.R.attr.autoCompleteTextViewStyle);
        C0176z c0176z = new C0176z(this);
        this.f755k = c0176z;
        c0176z.m502f(attributeSet, com.liaoyuan.aicast.R.attr.autoCompleteTextViewStyle);
        c0176z.m499b();
    }

    @Override // android.widget.TextView, android.view.View
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        C0127e c0127e = this.f754j;
        if (c0127e != null) {
            c0127e.m353a();
        }
        C0176z c0176z = this.f755k;
        if (c0176z != null) {
            c0176z.m499b();
        }
    }

    public ColorStateList getSupportBackgroundTintList() {
        C0127e c0127e = this.f754j;
        if (c0127e != null) {
            return c0127e.m354b();
        }
        return null;
    }

    public PorterDuff.Mode getSupportBackgroundTintMode() {
        C0127e c0127e = this.f754j;
        if (c0127e != null) {
            return c0127e.m355c();
        }
        return null;
    }

    @Override // android.widget.TextView, android.view.View
    public InputConnection onCreateInputConnection(EditorInfo editorInfo) {
        InputConnection onCreateInputConnection = super.onCreateInputConnection(editorInfo);
        C0385m.m1422n(onCreateInputConnection, editorInfo, this);
        return onCreateInputConnection;
    }

    @Override // android.view.View
    public void setBackgroundDrawable(Drawable drawable) {
        super.setBackgroundDrawable(drawable);
        C0127e c0127e = this.f754j;
        if (c0127e != null) {
            c0127e.m357e();
        }
    }

    @Override // android.view.View
    public void setBackgroundResource(int i6) {
        super.setBackgroundResource(i6);
        C0127e c0127e = this.f754j;
        if (c0127e != null) {
            c0127e.m358f(i6);
        }
    }

    @Override // android.widget.TextView
    public void setCustomSelectionActionModeCallback(ActionMode.Callback callback) {
        super.setCustomSelectionActionModeCallback(C0903d.m2455f(this, callback));
    }

    @Override // android.widget.AutoCompleteTextView
    public void setDropDownBackgroundResource(int i6) {
        setDropDownBackgroundDrawable(C0750a.m2138a(getContext(), i6));
    }

    public void setSupportBackgroundTintList(ColorStateList colorStateList) {
        C0127e c0127e = this.f754j;
        if (c0127e != null) {
            c0127e.m360h(colorStateList);
        }
    }

    public void setSupportBackgroundTintMode(PorterDuff.Mode mode) {
        C0127e c0127e = this.f754j;
        if (c0127e != null) {
            c0127e.m361i(mode);
        }
    }

    @Override // android.widget.TextView
    public final void setTextAppearance(Context context, int i6) {
        super.setTextAppearance(context, i6);
        C0176z c0176z = this.f755k;
        if (c0176z != null) {
            c0176z.m503g(context, i6);
        }
    }
}
