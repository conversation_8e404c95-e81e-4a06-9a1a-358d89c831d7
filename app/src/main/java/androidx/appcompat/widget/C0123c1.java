package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.TypedValue;
import p028e.C0750a;
import p149w.C1472d;

/* renamed from: androidx.appcompat.widget.c1 */
/* loaded from: classes.dex */
public final class C0123c1 {

    /* renamed from: a */
    public final Context f750a;

    /* renamed from: b */
    public final TypedArray f751b;

    /* renamed from: c */
    public TypedValue f752c;

    public C0123c1(Context context, TypedArray typedArray) {
        this.f750a = context;
        this.f751b = typedArray;
    }

    /* renamed from: p */
    public static C0123c1 m335p(Context context, AttributeSet attributeSet, int[] iArr) {
        return new C0123c1(context, context.obtainStyledAttributes(attributeSet, iArr));
    }

    /* renamed from: q */
    public static C0123c1 m336q(Context context, AttributeSet attributeSet, int[] iArr, int i6) {
        return new C0123c1(context, context.obtainStyledAttributes(attributeSet, iArr, i6, 0));
    }

    /* renamed from: a */
    public final boolean m337a(int i6, boolean z5) {
        return this.f751b.getBoolean(i6, z5);
    }

    /* renamed from: b */
    public final int m338b() {
        return this.f751b.getColor(12, 0);
    }

    /* renamed from: c */
    public final ColorStateList m339c(int i6) {
        int resourceId;
        if (this.f751b.hasValue(i6) && (resourceId = this.f751b.getResourceId(i6, 0)) != 0) {
            Context context = this.f750a;
            Object obj = C0750a.f4010a;
            ColorStateList colorStateList = context.getColorStateList(resourceId);
            if (colorStateList != null) {
                return colorStateList;
            }
        }
        return this.f751b.getColorStateList(i6);
    }

    /* renamed from: d */
    public final float m340d(int i6) {
        return this.f751b.getDimension(i6, -1.0f);
    }

    /* renamed from: e */
    public final int m341e(int i6, int i7) {
        return this.f751b.getDimensionPixelOffset(i6, i7);
    }

    /* renamed from: f */
    public final int m342f(int i6, int i7) {
        return this.f751b.getDimensionPixelSize(i6, i7);
    }

    /* renamed from: g */
    public final Drawable m343g(int i6) {
        int resourceId;
        return (!this.f751b.hasValue(i6) || (resourceId = this.f751b.getResourceId(i6, 0)) == 0) ? this.f751b.getDrawable(i6) : C0750a.m2138a(this.f750a, resourceId);
    }

    /* renamed from: h */
    public final Drawable m344h(int i6) {
        int resourceId;
        Drawable m447f;
        if (!this.f751b.hasValue(i6) || (resourceId = this.f751b.getResourceId(i6, 0)) == 0) {
            return null;
        }
        C0142j m401a = C0142j.m401a();
        Context context = this.f750a;
        synchronized (m401a) {
            m447f = m401a.f837a.m447f(context, resourceId, true);
        }
        return m447f;
    }

    /* renamed from: i */
    public final Typeface m345i(int i6, int i7, C1472d.a aVar) {
        int resourceId = this.f751b.getResourceId(i6, 0);
        if (resourceId == 0) {
            return null;
        }
        if (this.f752c == null) {
            this.f752c = new TypedValue();
        }
        Context context = this.f750a;
        TypedValue typedValue = this.f752c;
        Object obj = C1472d.f6772a;
        if (context.isRestricted()) {
            return null;
        }
        return C1472d.m3590b(context, resourceId, typedValue, i7, aVar, true, false);
    }

    /* renamed from: j */
    public final int m346j(int i6, int i7) {
        return this.f751b.getInt(i6, i7);
    }

    /* renamed from: k */
    public final int m347k(int i6, int i7) {
        return this.f751b.getLayoutDimension(i6, i7);
    }

    /* renamed from: l */
    public final int m348l(int i6, int i7) {
        return this.f751b.getResourceId(i6, i7);
    }

    /* renamed from: m */
    public final String m349m(int i6) {
        return this.f751b.getString(i6);
    }

    /* renamed from: n */
    public final CharSequence m350n(int i6) {
        return this.f751b.getText(i6);
    }

    /* renamed from: o */
    public final boolean m351o(int i6) {
        return this.f751b.hasValue(i6);
    }

    /* renamed from: r */
    public final void m352r() {
        this.f751b.recycle();
    }
}
