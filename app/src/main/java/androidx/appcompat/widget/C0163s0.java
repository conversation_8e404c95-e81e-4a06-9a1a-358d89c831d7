package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.content.res.XmlResourceParser;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffColorFilter;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.util.Xml;
import androidx.appcompat.widget.C0142j;
import com.liaoyuan.aicast.R;
import java.lang.ref.WeakReference;
import java.util.Objects;
import java.util.WeakHashMap;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;
import p077l.C1050d;
import p077l.C1051e;
import p077l.C1053g;
import p077l.C1054h;
import p136u0.C1414c;
import p142v.C1450a;

/* renamed from: androidx.appcompat.widget.s0 */
/* loaded from: classes.dex */
public final class C0163s0 {

    /* renamed from: i */
    public static C0163s0 f935i;

    /* renamed from: a */
    public WeakHashMap<Context, C1054h<ColorStateList>> f937a;

    /* renamed from: b */
    public C1053g<String, b> f938b;

    /* renamed from: c */
    public C1054h<String> f939c;

    /* renamed from: d */
    public final WeakHashMap<Context, C1050d<WeakReference<Drawable.ConstantState>>> f940d = new WeakHashMap<>(0);

    /* renamed from: e */
    public TypedValue f941e;

    /* renamed from: f */
    public boolean f942f;

    /* renamed from: g */
    public c f943g;

    /* renamed from: h */
    public static final PorterDuff.Mode f934h = PorterDuff.Mode.SRC_IN;

    /* renamed from: j */
    public static final a f936j = new a();

    /* renamed from: androidx.appcompat.widget.s0$a */
    public static class a extends C1051e<Integer, PorterDuffColorFilter> {
        public a() {
            super(6);
        }
    }

    /* renamed from: androidx.appcompat.widget.s0$b */
    public interface b {
        /* renamed from: a */
        Drawable m452a(Context context, XmlPullParser xmlPullParser, AttributeSet attributeSet, Resources.Theme theme);
    }

    /* renamed from: androidx.appcompat.widget.s0$c */
    public interface c {
    }

    /* renamed from: c */
    public static synchronized C0163s0 m441c() {
        C0163s0 c0163s0;
        synchronized (C0163s0.class) {
            if (f935i == null) {
                f935i = new C0163s0();
            }
            c0163s0 = f935i;
        }
        return c0163s0;
    }

    /* renamed from: g */
    public static synchronized PorterDuffColorFilter m442g(int i6, PorterDuff.Mode mode) {
        PorterDuffColorFilter m2677b;
        synchronized (C0163s0.class) {
            a aVar = f936j;
            Objects.requireNonNull(aVar);
            int i7 = (i6 + 31) * 31;
            m2677b = aVar.m2677b(Integer.valueOf(mode.hashCode() + i7));
            if (m2677b == null) {
                m2677b = new PorterDuffColorFilter(i6, mode);
                Objects.requireNonNull(aVar);
                aVar.m2678c(Integer.valueOf(mode.hashCode() + i7), m2677b);
            }
        }
        return m2677b;
    }

    /* renamed from: a */
    public final synchronized boolean m443a(Context context, long j6, Drawable drawable) {
        boolean z5;
        Drawable.ConstantState constantState = drawable.getConstantState();
        if (constantState != null) {
            C1050d<WeakReference<Drawable.ConstantState>> c1050d = this.f940d.get(context);
            if (c1050d == null) {
                c1050d = new C1050d<>();
                this.f940d.put(context, c1050d);
            }
            c1050d.m2672h(j6, new WeakReference<>(constantState));
            z5 = true;
        } else {
            z5 = false;
        }
        return z5;
    }

    /* renamed from: b */
    public final Drawable m444b(Context context, int i6) {
        int i7;
        if (this.f941e == null) {
            this.f941e = new TypedValue();
        }
        TypedValue typedValue = this.f941e;
        context.getResources().getValue(i6, typedValue, true);
        long j6 = (typedValue.assetCookie << 32) | typedValue.data;
        Drawable m445d = m445d(context, j6);
        if (m445d != null) {
            return m445d;
        }
        c cVar = this.f943g;
        LayerDrawable layerDrawable = null;
        if (cVar != null) {
            C0142j.a aVar = (C0142j.a) cVar;
            Objects.requireNonNull(aVar);
            if (i6 == R.drawable.abc_cab_background_top_material) {
                layerDrawable = new LayerDrawable(new Drawable[]{m446e(context, R.drawable.abc_cab_background_internal_bg), m446e(context, R.drawable.abc_cab_background_top_mtrl_alpha)});
            } else {
                if (i6 == R.drawable.abc_ratingbar_material) {
                    i7 = R.dimen.abc_star_big;
                } else if (i6 == R.drawable.abc_ratingbar_indicator_material) {
                    i7 = R.dimen.abc_star_medium;
                } else if (i6 == R.drawable.abc_ratingbar_small_material) {
                    i7 = R.dimen.abc_star_small;
                }
                layerDrawable = aVar.m409c(this, context, i7);
            }
        }
        if (layerDrawable != null) {
            layerDrawable.setChangingConfigurations(typedValue.changingConfigurations);
            m443a(context, j6, layerDrawable);
        }
        return layerDrawable;
    }

    /* renamed from: d */
    public final synchronized Drawable m445d(Context context, long j6) {
        C1050d<WeakReference<Drawable.ConstantState>> c1050d = this.f940d.get(context);
        if (c1050d == null) {
            return null;
        }
        WeakReference<Drawable.ConstantState> m2669e = c1050d.m2669e(j6, null);
        if (m2669e != null) {
            Drawable.ConstantState constantState = m2669e.get();
            if (constantState != null) {
                return constantState.newDrawable(context.getResources());
            }
            c1050d.m2673i(j6);
        }
        return null;
    }

    /* renamed from: e */
    public final synchronized Drawable m446e(Context context, int i6) {
        return m447f(context, i6, false);
    }

    /* renamed from: f */
    public final synchronized Drawable m447f(Context context, int i6, boolean z5) {
        Drawable m449i;
        if (!this.f942f) {
            boolean z6 = true;
            this.f942f = true;
            Drawable m446e = m446e(context, R.drawable.abc_vector_test);
            if (m446e != null) {
                if (!(m446e instanceof C1414c) && !"android.graphics.drawable.VectorDrawable".equals(m446e.getClass().getName())) {
                    z6 = false;
                }
            }
            this.f942f = false;
            throw new IllegalStateException("This app has been built with an incorrect configuration. Please configure your build for VectorDrawableCompat.");
        }
        m449i = m449i(context, i6);
        if (m449i == null) {
            m449i = m444b(context, i6);
        }
        if (m449i == null) {
            Object obj = C1450a.f6698a;
            m449i = C1450a.c.m3527b(context, i6);
        }
        if (m449i != null) {
            m449i = m450j(context, i6, z5, m449i);
        }
        if (m449i != null) {
            int i7 = C0134g0.f790a;
        }
        return m449i;
    }

    /* renamed from: h */
    public final synchronized ColorStateList m448h(Context context, int i6) {
        ColorStateList m2697c;
        C1054h<ColorStateList> c1054h;
        WeakHashMap<Context, C1054h<ColorStateList>> weakHashMap = this.f937a;
        ColorStateList colorStateList = null;
        m2697c = (weakHashMap == null || (c1054h = weakHashMap.get(context)) == null) ? null : c1054h.m2697c(i6, null);
        if (m2697c == null) {
            c cVar = this.f943g;
            if (cVar != null) {
                colorStateList = ((C0142j.a) cVar).m410d(context, i6);
            }
            if (colorStateList != null) {
                if (this.f937a == null) {
                    this.f937a = new WeakHashMap<>();
                }
                C1054h<ColorStateList> c1054h2 = this.f937a.get(context);
                if (c1054h2 == null) {
                    c1054h2 = new C1054h<>();
                    this.f937a.put(context, c1054h2);
                }
                c1054h2.m2695a(i6, colorStateList);
            }
            m2697c = colorStateList;
        }
        return m2697c;
    }

    /* renamed from: i */
    public final Drawable m449i(Context context, int i6) {
        int next;
        C1053g<String, b> c1053g = this.f938b;
        if (c1053g == null || c1053g.isEmpty()) {
            return null;
        }
        C1054h<String> c1054h = this.f939c;
        if (c1054h != null) {
            String m2697c = c1054h.m2697c(i6, null);
            if ("appcompat_skip_skip".equals(m2697c) || (m2697c != null && this.f938b.getOrDefault(m2697c, null) == null)) {
                return null;
            }
        } else {
            this.f939c = new C1054h<>();
        }
        if (this.f941e == null) {
            this.f941e = new TypedValue();
        }
        TypedValue typedValue = this.f941e;
        Resources resources = context.getResources();
        resources.getValue(i6, typedValue, true);
        long j6 = (typedValue.assetCookie << 32) | typedValue.data;
        Drawable m445d = m445d(context, j6);
        if (m445d != null) {
            return m445d;
        }
        CharSequence charSequence = typedValue.string;
        if (charSequence != null && charSequence.toString().endsWith(".xml")) {
            try {
                XmlResourceParser xml = resources.getXml(i6);
                AttributeSet asAttributeSet = Xml.asAttributeSet(xml);
                do {
                    next = xml.next();
                    if (next == 2) {
                        break;
                    }
                } while (next != 1);
                if (next != 2) {
                    throw new XmlPullParserException("No start tag found");
                }
                String name = xml.getName();
                this.f939c.m2695a(i6, name);
                b orDefault = this.f938b.getOrDefault(name, null);
                if (orDefault != null) {
                    m445d = orDefault.m452a(context, xml, asAttributeSet, context.getTheme());
                }
                if (m445d != null) {
                    m445d.setChangingConfigurations(typedValue.changingConfigurations);
                    m443a(context, j6, m445d);
                }
            } catch (Exception e6) {
                Log.e("ResourceManagerInternal", "Exception while inflating drawable", e6);
            }
        }
        if (m445d == null) {
            this.f939c.m2695a(i6, "appcompat_skip_skip");
        }
        return m445d;
    }

    /* renamed from: j */
    public final Drawable m450j(Context context, int i6, boolean z5, Drawable drawable) {
        ColorStateList m448h = m448h(context, i6);
        PorterDuff.Mode mode = null;
        if (m448h != null) {
            if (C0134g0.m388a(drawable)) {
                drawable = drawable.mutate();
            }
            drawable.setTintList(m448h);
            if (this.f943g != null && i6 == R.drawable.abc_switch_thumb_material) {
                mode = PorterDuff.Mode.MULTIPLY;
            }
            if (mode == null) {
                return drawable;
            }
            drawable.setTintMode(mode);
            return drawable;
        }
        c cVar = this.f943g;
        if (cVar != null) {
            C0142j.a aVar = (C0142j.a) cVar;
            boolean z6 = true;
            if (i6 == R.drawable.abc_seekbar_track_material) {
                LayerDrawable layerDrawable = (LayerDrawable) drawable;
                Drawable findDrawableByLayerId = layerDrawable.findDrawableByLayerId(android.R.id.background);
                int m482c = C0173x0.m482c(context, R.attr.colorControlNormal);
                PorterDuff.Mode mode2 = C0142j.f835b;
                aVar.m411e(findDrawableByLayerId, m482c, mode2);
                aVar.m411e(layerDrawable.findDrawableByLayerId(android.R.id.secondaryProgress), C0173x0.m482c(context, R.attr.colorControlNormal), mode2);
                aVar.m411e(layerDrawable.findDrawableByLayerId(android.R.id.progress), C0173x0.m482c(context, R.attr.colorControlActivated), mode2);
            } else if (i6 == R.drawable.abc_ratingbar_material || i6 == R.drawable.abc_ratingbar_indicator_material || i6 == R.drawable.abc_ratingbar_small_material) {
                LayerDrawable layerDrawable2 = (LayerDrawable) drawable;
                Drawable findDrawableByLayerId2 = layerDrawable2.findDrawableByLayerId(android.R.id.background);
                int m481b = C0173x0.m481b(context, R.attr.colorControlNormal);
                PorterDuff.Mode mode3 = C0142j.f835b;
                aVar.m411e(findDrawableByLayerId2, m481b, mode3);
                aVar.m411e(layerDrawable2.findDrawableByLayerId(android.R.id.secondaryProgress), C0173x0.m482c(context, R.attr.colorControlActivated), mode3);
                aVar.m411e(layerDrawable2.findDrawableByLayerId(android.R.id.progress), C0173x0.m482c(context, R.attr.colorControlActivated), mode3);
            } else {
                z6 = false;
            }
            if (z6) {
                return drawable;
            }
        }
        if (m451k(context, i6, drawable) || !z5) {
            return drawable;
        }
        return null;
    }

    /* JADX WARN: Removed duplicated region for block: B:17:? A[RETURN, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:19:0x006b  */
    /* JADX WARN: Removed duplicated region for block: B:9:0x004f  */
    /* renamed from: k */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean m451k(android.content.Context r8, int r9, android.graphics.drawable.Drawable r10) {
        /*
            r7 = this;
            androidx.appcompat.widget.s0$c r0 = r7.f943g
            r1 = 1
            r2 = 0
            if (r0 == 0) goto L6f
            androidx.appcompat.widget.j$a r0 = (androidx.appcompat.widget.C0142j.a) r0
            android.graphics.PorterDuff$Mode r3 = androidx.appcompat.widget.C0142j.f835b
            int[] r4 = r0.f838a
            boolean r4 = r0.m407a(r4, r9)
            r5 = 16842801(0x1010031, float:2.3693695E-38)
            r6 = -1
            if (r4 == 0) goto L1a
            r5 = 2130968762(0x7f0400ba, float:1.7546187E38)
            goto L46
        L1a:
            int[] r4 = r0.f840c
            boolean r4 = r0.m407a(r4, r9)
            if (r4 == 0) goto L26
            r5 = 2130968760(0x7f0400b8, float:1.7546183E38)
            goto L46
        L26:
            int[] r4 = r0.f841d
            boolean r0 = r0.m407a(r4, r9)
            if (r0 == 0) goto L31
            android.graphics.PorterDuff$Mode r3 = android.graphics.PorterDuff.Mode.MULTIPLY
            goto L46
        L31:
            r0 = 2131230764(0x7f08002c, float:1.807759E38)
            if (r9 != r0) goto L41
            r9 = 16842800(0x1010030, float:2.3693693E-38)
            r0 = 1109603123(0x42233333, float:40.8)
            int r0 = java.lang.Math.round(r0)
            goto L48
        L41:
            r0 = 2131230746(0x7f08001a, float:1.8077553E38)
            if (r9 != r0) goto L4a
        L46:
            r9 = r5
            r0 = r6
        L48:
            r4 = r1
            goto L4d
        L4a:
            r9 = r2
            r4 = r9
            r0 = r6
        L4d:
            if (r4 == 0) goto L6b
            boolean r4 = androidx.appcompat.widget.C0134g0.m388a(r10)
            if (r4 == 0) goto L59
            android.graphics.drawable.Drawable r10 = r10.mutate()
        L59:
            int r8 = androidx.appcompat.widget.C0173x0.m482c(r8, r9)
            android.graphics.PorterDuffColorFilter r8 = androidx.appcompat.widget.C0142j.m402c(r8, r3)
            r10.setColorFilter(r8)
            if (r0 == r6) goto L69
            r10.setAlpha(r0)
        L69:
            r8 = r1
            goto L6c
        L6b:
            r8 = r2
        L6c:
            if (r8 == 0) goto L6f
            goto L70
        L6f:
            r1 = r2
        L70:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.C0163s0.m451k(android.content.Context, int, android.graphics.drawable.Drawable):boolean");
    }
}
