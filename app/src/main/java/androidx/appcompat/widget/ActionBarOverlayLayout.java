package androidx.appcompat.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewPropertyAnimator;
import android.view.Window;
import android.view.WindowInsets;
import android.widget.OverScroller;
import androidx.activity.result.C0052a;
import androidx.appcompat.view.menu.InterfaceC0074i;
import com.liaoyuan.aicast.R;
import java.util.WeakHashMap;
import p021d.C0695t;
import p029e0.C0761k;
import p029e0.C0766p;
import p029e0.C0769s;
import p029e0.C0772v;
import p029e0.InterfaceC0759i;
import p029e0.InterfaceC0760j;
import p042g.C0874g;
import p156x.C1809b;

@SuppressLint({"UnknownNullness"})
/* loaded from: classes.dex */
public class ActionBarOverlayLayout extends ViewGroup implements InterfaceC0128e0, InterfaceC0759i, InterfaceC0760j {

    /* renamed from: K */
    public static final int[] f493K = {R.attr.actionBarSize, android.R.attr.windowContentOverlay};

    /* renamed from: A */
    public C0772v f494A;

    /* renamed from: B */
    public C0772v f495B;

    /* renamed from: C */
    public C0772v f496C;

    /* renamed from: D */
    public InterfaceC0082d f497D;

    /* renamed from: E */
    public OverScroller f498E;

    /* renamed from: F */
    public ViewPropertyAnimator f499F;

    /* renamed from: G */
    public final C0079a f500G;

    /* renamed from: H */
    public final RunnableC0080b f501H;

    /* renamed from: I */
    public final RunnableC0081c f502I;

    /* renamed from: J */
    public final C0761k f503J;

    /* renamed from: j */
    public int f504j;

    /* renamed from: k */
    public int f505k;

    /* renamed from: l */
    public ContentFrameLayout f506l;

    /* renamed from: m */
    public ActionBarContainer f507m;

    /* renamed from: n */
    public InterfaceC0131f0 f508n;

    /* renamed from: o */
    public Drawable f509o;

    /* renamed from: p */
    public boolean f510p;

    /* renamed from: q */
    public boolean f511q;

    /* renamed from: r */
    public boolean f512r;

    /* renamed from: s */
    public boolean f513s;

    /* renamed from: t */
    public boolean f514t;

    /* renamed from: u */
    public int f515u;

    /* renamed from: v */
    public int f516v;

    /* renamed from: w */
    public final Rect f517w;

    /* renamed from: x */
    public final Rect f518x;

    /* renamed from: y */
    public final Rect f519y;

    /* renamed from: z */
    public C0772v f520z;

    /* renamed from: androidx.appcompat.widget.ActionBarOverlayLayout$a */
    public class C0079a extends AnimatorListenerAdapter {
        public C0079a() {
        }

        @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
        public final void onAnimationCancel(Animator animator) {
            ActionBarOverlayLayout actionBarOverlayLayout = ActionBarOverlayLayout.this;
            actionBarOverlayLayout.f499F = null;
            actionBarOverlayLayout.f514t = false;
        }

        @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
        public final void onAnimationEnd(Animator animator) {
            ActionBarOverlayLayout actionBarOverlayLayout = ActionBarOverlayLayout.this;
            actionBarOverlayLayout.f499F = null;
            actionBarOverlayLayout.f514t = false;
        }
    }

    /* renamed from: androidx.appcompat.widget.ActionBarOverlayLayout$b */
    public class RunnableC0080b implements Runnable {
        public RunnableC0080b() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            ActionBarOverlayLayout.this.m246q();
            ActionBarOverlayLayout actionBarOverlayLayout = ActionBarOverlayLayout.this;
            actionBarOverlayLayout.f499F = actionBarOverlayLayout.f507m.animate().translationY(0.0f).setListener(ActionBarOverlayLayout.this.f500G);
        }
    }

    /* renamed from: androidx.appcompat.widget.ActionBarOverlayLayout$c */
    public class RunnableC0081c implements Runnable {
        public RunnableC0081c() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            ActionBarOverlayLayout.this.m246q();
            ActionBarOverlayLayout actionBarOverlayLayout = ActionBarOverlayLayout.this;
            actionBarOverlayLayout.f499F = actionBarOverlayLayout.f507m.animate().translationY(-ActionBarOverlayLayout.this.f507m.getHeight()).setListener(ActionBarOverlayLayout.this.f500G);
        }
    }

    /* renamed from: androidx.appcompat.widget.ActionBarOverlayLayout$d */
    public interface InterfaceC0082d {
    }

    /* renamed from: androidx.appcompat.widget.ActionBarOverlayLayout$e */
    public static class C0083e extends ViewGroup.MarginLayoutParams {
        public C0083e() {
            super(-1, -1);
        }

        public C0083e(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
        }

        public C0083e(ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
        }
    }

    public ActionBarOverlayLayout(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.f505k = 0;
        this.f517w = new Rect();
        this.f518x = new Rect();
        this.f519y = new Rect();
        new Rect();
        new Rect();
        new Rect();
        new Rect();
        C0772v c0772v = C0772v.f4061b;
        this.f520z = c0772v;
        this.f494A = c0772v;
        this.f495B = c0772v;
        this.f496C = c0772v;
        this.f500G = new C0079a();
        this.f501H = new RunnableC0080b();
        this.f502I = new RunnableC0081c();
        m247r(context);
        this.f503J = new C0761k();
    }

    @Override // androidx.appcompat.widget.InterfaceC0128e0
    /* renamed from: a */
    public final void mo230a(Menu menu, InterfaceC0074i.a aVar) {
        m248s();
        this.f508n.mo362a(menu, aVar);
    }

    @Override // androidx.appcompat.widget.InterfaceC0128e0
    /* renamed from: b */
    public final boolean mo231b() {
        m248s();
        return this.f508n.mo363b();
    }

    @Override // androidx.appcompat.widget.InterfaceC0128e0
    /* renamed from: c */
    public final boolean mo232c() {
        m248s();
        return this.f508n.mo364c();
    }

    @Override // android.view.ViewGroup
    public final boolean checkLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return layoutParams instanceof C0083e;
    }

    @Override // androidx.appcompat.widget.InterfaceC0128e0
    /* renamed from: d */
    public final boolean mo233d() {
        m248s();
        return this.f508n.mo365d();
    }

    @Override // android.view.View
    public final void draw(Canvas canvas) {
        int i6;
        super.draw(canvas);
        if (this.f509o == null || this.f510p) {
            return;
        }
        if (this.f507m.getVisibility() == 0) {
            i6 = (int) (this.f507m.getTranslationY() + this.f507m.getBottom() + 0.5f);
        } else {
            i6 = 0;
        }
        this.f509o.setBounds(0, i6, getWidth(), this.f509o.getIntrinsicHeight() + i6);
        this.f509o.draw(canvas);
    }

    @Override // androidx.appcompat.widget.InterfaceC0128e0
    /* renamed from: e */
    public final boolean mo234e() {
        m248s();
        return this.f508n.mo366e();
    }

    @Override // androidx.appcompat.widget.InterfaceC0128e0
    /* renamed from: f */
    public final void mo235f() {
        m248s();
        this.f508n.mo367f();
    }

    @Override // android.view.View
    public final boolean fitSystemWindows(Rect rect) {
        return super.fitSystemWindows(rect);
    }

    @Override // androidx.appcompat.widget.InterfaceC0128e0
    /* renamed from: g */
    public final boolean mo236g() {
        m248s();
        return this.f508n.mo368g();
    }

    @Override // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateDefaultLayoutParams() {
        return new C0083e();
    }

    @Override // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        return new C0083e(getContext(), attributeSet);
    }

    @Override // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return new C0083e(layoutParams);
    }

    public int getActionBarHideOffset() {
        ActionBarContainer actionBarContainer = this.f507m;
        if (actionBarContainer != null) {
            return -((int) actionBarContainer.getTranslationY());
        }
        return 0;
    }

    @Override // android.view.ViewGroup
    public int getNestedScrollAxes() {
        C0761k c0761k = this.f503J;
        return c0761k.f4037b | c0761k.f4036a;
    }

    public CharSequence getTitle() {
        m248s();
        return this.f508n.getTitle();
    }

    @Override // p029e0.InterfaceC0759i
    /* renamed from: h */
    public final void mo237h(View view, View view2, int i6, int i7) {
        if (i7 == 0) {
            onNestedScrollAccepted(view, view2, i6);
        }
    }

    @Override // p029e0.InterfaceC0759i
    /* renamed from: i */
    public final void mo238i(View view, int i6) {
        if (i6 == 0) {
            onStopNestedScroll(view);
        }
    }

    @Override // p029e0.InterfaceC0759i
    /* renamed from: j */
    public final void mo239j(View view, int i6, int i7, int[] iArr, int i8) {
        if (i8 == 0) {
            onNestedPreScroll(view, i6, i7, iArr);
        }
    }

    @Override // androidx.appcompat.widget.InterfaceC0128e0
    /* renamed from: k */
    public final void mo240k(int i6) {
        m248s();
        if (i6 == 2) {
            this.f508n.mo379r();
        } else if (i6 == 5) {
            this.f508n.mo381t();
        } else {
            if (i6 != 109) {
                return;
            }
            setOverlayMode(true);
        }
    }

    @Override // androidx.appcompat.widget.InterfaceC0128e0
    /* renamed from: l */
    public final void mo241l() {
        m248s();
        this.f508n.mo369h();
    }

    @Override // p029e0.InterfaceC0760j
    /* renamed from: m */
    public final void mo242m(View view, int i6, int i7, int i8, int i9, int i10, int[] iArr) {
        if (i10 == 0) {
            onNestedScroll(view, i6, i7, i8, i9);
        }
    }

    @Override // p029e0.InterfaceC0759i
    /* renamed from: n */
    public final void mo243n(View view, int i6, int i7, int i8, int i9, int i10) {
        if (i10 == 0) {
            onNestedScroll(view, i6, i7, i8, i9);
        }
    }

    @Override // p029e0.InterfaceC0759i
    /* renamed from: o */
    public final boolean mo244o(View view, View view2, int i6, int i7) {
        return i7 == 0 && onStartNestedScroll(view, view2, i6);
    }

    @Override // android.view.View
    public final WindowInsets onApplyWindowInsets(WindowInsets windowInsets) {
        m248s();
        C0772v m2215j = C0772v.m2215j(windowInsets, this);
        boolean m245p = m245p(this.f507m, new Rect(m2215j.m2218c(), m2215j.m2220e(), m2215j.m2219d(), m2215j.m2217b()), false);
        Rect rect = this.f517w;
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        C0766p.c.m2193b(this, m2215j, rect);
        Rect rect2 = this.f517w;
        C0772v mo2233i = m2215j.f4062a.mo2233i(rect2.left, rect2.top, rect2.right, rect2.bottom);
        this.f520z = mo2233i;
        boolean z5 = true;
        if (!this.f494A.equals(mo2233i)) {
            this.f494A = this.f520z;
            m245p = true;
        }
        if (this.f518x.equals(this.f517w)) {
            z5 = m245p;
        } else {
            this.f518x.set(this.f517w);
        }
        if (z5) {
            requestLayout();
        }
        return m2215j.f4062a.mo2243a().f4062a.mo2239c().f4062a.mo2238b().m2223i();
    }

    @Override // android.view.View
    public final void onConfigurationChanged(Configuration configuration) {
        super.onConfigurationChanged(configuration);
        m247r(getContext());
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        requestApplyInsets();
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        m246q();
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onLayout(boolean z5, int i6, int i7, int i8, int i9) {
        int childCount = getChildCount();
        int paddingLeft = getPaddingLeft();
        int paddingTop = getPaddingTop();
        for (int i10 = 0; i10 < childCount; i10++) {
            View childAt = getChildAt(i10);
            if (childAt.getVisibility() != 8) {
                C0083e c0083e = (C0083e) childAt.getLayoutParams();
                int measuredWidth = childAt.getMeasuredWidth();
                int measuredHeight = childAt.getMeasuredHeight();
                int i11 = ((ViewGroup.MarginLayoutParams) c0083e).leftMargin + paddingLeft;
                int i12 = ((ViewGroup.MarginLayoutParams) c0083e).topMargin + paddingTop;
                childAt.layout(i11, i12, measuredWidth + i11, measuredHeight + i12);
            }
        }
    }

    @Override // android.view.View
    public final void onMeasure(int i6, int i7) {
        int measuredHeight;
        C0772v mo2225b;
        m248s();
        measureChildWithMargins(this.f507m, i6, 0, i7, 0);
        C0083e c0083e = (C0083e) this.f507m.getLayoutParams();
        int max = Math.max(0, this.f507m.getMeasuredWidth() + ((ViewGroup.MarginLayoutParams) c0083e).leftMargin + ((ViewGroup.MarginLayoutParams) c0083e).rightMargin);
        int max2 = Math.max(0, this.f507m.getMeasuredHeight() + ((ViewGroup.MarginLayoutParams) c0083e).topMargin + ((ViewGroup.MarginLayoutParams) c0083e).bottomMargin);
        int combineMeasuredStates = View.combineMeasuredStates(0, this.f507m.getMeasuredState());
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        boolean z5 = (getWindowSystemUiVisibility() & 256) != 0;
        if (z5) {
            measuredHeight = this.f504j;
            if (this.f512r && this.f507m.getTabContainer() != null) {
                measuredHeight += this.f504j;
            }
        } else {
            measuredHeight = this.f507m.getVisibility() != 8 ? this.f507m.getMeasuredHeight() : 0;
        }
        this.f519y.set(this.f517w);
        C0772v c0772v = this.f520z;
        this.f495B = c0772v;
        if (this.f511q || z5) {
            C1809b m4626a = C1809b.m4626a(c0772v.m2218c(), this.f495B.m2220e() + measuredHeight, this.f495B.m2219d(), this.f495B.m2217b() + 0);
            C0772v c0772v2 = this.f495B;
            int i8 = Build.VERSION.SDK_INT;
            C0772v.d cVar = i8 >= 30 ? new C0772v.c(c0772v2) : i8 >= 29 ? new C0772v.b(c0772v2) : new C0772v.a(c0772v2);
            cVar.mo2227d(m4626a);
            mo2225b = cVar.mo2225b();
        } else {
            Rect rect = this.f519y;
            rect.top += measuredHeight;
            rect.bottom += 0;
            mo2225b = c0772v.f4062a.mo2233i(0, measuredHeight, 0, 0);
        }
        this.f495B = mo2225b;
        m245p(this.f506l, this.f519y, true);
        if (!this.f496C.equals(this.f495B)) {
            C0772v c0772v3 = this.f495B;
            this.f496C = c0772v3;
            C0766p.m2170c(this.f506l, c0772v3);
        }
        measureChildWithMargins(this.f506l, i6, 0, i7, 0);
        C0083e c0083e2 = (C0083e) this.f506l.getLayoutParams();
        int max3 = Math.max(max, this.f506l.getMeasuredWidth() + ((ViewGroup.MarginLayoutParams) c0083e2).leftMargin + ((ViewGroup.MarginLayoutParams) c0083e2).rightMargin);
        int max4 = Math.max(max2, this.f506l.getMeasuredHeight() + ((ViewGroup.MarginLayoutParams) c0083e2).topMargin + ((ViewGroup.MarginLayoutParams) c0083e2).bottomMargin);
        int combineMeasuredStates2 = View.combineMeasuredStates(combineMeasuredStates, this.f506l.getMeasuredState());
        setMeasuredDimension(View.resolveSizeAndState(Math.max(getPaddingRight() + getPaddingLeft() + max3, getSuggestedMinimumWidth()), i6, combineMeasuredStates2), View.resolveSizeAndState(Math.max(getPaddingBottom() + getPaddingTop() + max4, getSuggestedMinimumHeight()), i7, combineMeasuredStates2 << 16));
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final boolean onNestedFling(View view, float f6, float f7, boolean z5) {
        if (!this.f513s || !z5) {
            return false;
        }
        this.f498E.fling(0, 0, 0, (int) f7, 0, 0, Integer.MIN_VALUE, Integer.MAX_VALUE);
        if (this.f498E.getFinalY() > this.f507m.getHeight()) {
            m246q();
            this.f502I.run();
        } else {
            m246q();
            this.f501H.run();
        }
        this.f514t = true;
        return true;
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final boolean onNestedPreFling(View view, float f6, float f7) {
        return false;
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final void onNestedPreScroll(View view, int i6, int i7, int[] iArr) {
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final void onNestedScroll(View view, int i6, int i7, int i8, int i9) {
        int i10 = this.f515u + i7;
        this.f515u = i10;
        setActionBarHideOffset(i10);
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final void onNestedScrollAccepted(View view, View view2, int i6) {
        C0695t c0695t;
        C0874g c0874g;
        this.f503J.m2164a(i6, 0);
        this.f515u = getActionBarHideOffset();
        m246q();
        InterfaceC0082d interfaceC0082d = this.f497D;
        if (interfaceC0082d == null || (c0874g = (c0695t = (C0695t) interfaceC0082d).f3718t) == null) {
            return;
        }
        c0874g.m2414a();
        c0695t.f3718t = null;
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final boolean onStartNestedScroll(View view, View view2, int i6) {
        if ((i6 & 2) == 0 || this.f507m.getVisibility() != 0) {
            return false;
        }
        return this.f513s;
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final void onStopNestedScroll(View view) {
        if (!this.f513s || this.f514t) {
            return;
        }
        if (this.f515u <= this.f507m.getHeight()) {
            m246q();
            postDelayed(this.f501H, 600L);
        } else {
            m246q();
            postDelayed(this.f502I, 600L);
        }
    }

    @Override // android.view.View
    @Deprecated
    public final void onWindowSystemUiVisibilityChanged(int i6) {
        super.onWindowSystemUiVisibilityChanged(i6);
        m248s();
        int i7 = this.f516v ^ i6;
        this.f516v = i6;
        boolean z5 = (i6 & 4) == 0;
        boolean z6 = (i6 & 256) != 0;
        InterfaceC0082d interfaceC0082d = this.f497D;
        if (interfaceC0082d != null) {
            ((C0695t) interfaceC0082d).f3714p = !z6;
            if (z5 || !z6) {
                C0695t c0695t = (C0695t) interfaceC0082d;
                if (c0695t.f3715q) {
                    c0695t.f3715q = false;
                    c0695t.m2040u(true);
                }
            } else {
                C0695t c0695t2 = (C0695t) interfaceC0082d;
                if (!c0695t2.f3715q) {
                    c0695t2.f3715q = true;
                    c0695t2.m2040u(true);
                }
            }
        }
        if ((i7 & 256) == 0 || this.f497D == null) {
            return;
        }
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        requestApplyInsets();
    }

    @Override // android.view.View
    public final void onWindowVisibilityChanged(int i6) {
        super.onWindowVisibilityChanged(i6);
        this.f505k = i6;
        InterfaceC0082d interfaceC0082d = this.f497D;
        if (interfaceC0082d != null) {
            ((C0695t) interfaceC0082d).f3713o = i6;
        }
    }

    /* renamed from: p */
    public final boolean m245p(View view, Rect rect, boolean z5) {
        boolean z6;
        C0083e c0083e = (C0083e) view.getLayoutParams();
        int i6 = ((ViewGroup.MarginLayoutParams) c0083e).leftMargin;
        int i7 = rect.left;
        if (i6 != i7) {
            ((ViewGroup.MarginLayoutParams) c0083e).leftMargin = i7;
            z6 = true;
        } else {
            z6 = false;
        }
        int i8 = ((ViewGroup.MarginLayoutParams) c0083e).topMargin;
        int i9 = rect.top;
        if (i8 != i9) {
            ((ViewGroup.MarginLayoutParams) c0083e).topMargin = i9;
            z6 = true;
        }
        int i10 = ((ViewGroup.MarginLayoutParams) c0083e).rightMargin;
        int i11 = rect.right;
        if (i10 != i11) {
            ((ViewGroup.MarginLayoutParams) c0083e).rightMargin = i11;
            z6 = true;
        }
        if (z5) {
            int i12 = ((ViewGroup.MarginLayoutParams) c0083e).bottomMargin;
            int i13 = rect.bottom;
            if (i12 != i13) {
                ((ViewGroup.MarginLayoutParams) c0083e).bottomMargin = i13;
                return true;
            }
        }
        return z6;
    }

    /* renamed from: q */
    public final void m246q() {
        removeCallbacks(this.f501H);
        removeCallbacks(this.f502I);
        ViewPropertyAnimator viewPropertyAnimator = this.f499F;
        if (viewPropertyAnimator != null) {
            viewPropertyAnimator.cancel();
        }
    }

    /* renamed from: r */
    public final void m247r(Context context) {
        TypedArray obtainStyledAttributes = getContext().getTheme().obtainStyledAttributes(f493K);
        this.f504j = obtainStyledAttributes.getDimensionPixelSize(0, 0);
        Drawable drawable = obtainStyledAttributes.getDrawable(1);
        this.f509o = drawable;
        setWillNotDraw(drawable == null);
        obtainStyledAttributes.recycle();
        this.f510p = context.getApplicationInfo().targetSdkVersion < 19;
        this.f498E = new OverScroller(context);
    }

    /* renamed from: s */
    public final void m248s() {
        InterfaceC0131f0 wrapper;
        if (this.f506l == null) {
            this.f506l = (ContentFrameLayout) findViewById(R.id.action_bar_activity_content);
            this.f507m = (ActionBarContainer) findViewById(R.id.action_bar_container);
            KeyEvent.Callback findViewById = findViewById(R.id.action_bar);
            if (findViewById instanceof InterfaceC0131f0) {
                wrapper = (InterfaceC0131f0) findViewById;
            } else {
                if (!(findViewById instanceof Toolbar)) {
                    StringBuilder m104h = C0052a.m104h("Can't make a decor toolbar out of ");
                    m104h.append(findViewById.getClass().getSimpleName());
                    throw new IllegalStateException(m104h.toString());
                }
                wrapper = ((Toolbar) findViewById).getWrapper();
            }
            this.f508n = wrapper;
        }
    }

    public void setActionBarHideOffset(int i6) {
        m246q();
        this.f507m.setTranslationY(-Math.max(0, Math.min(i6, this.f507m.getHeight())));
    }

    public void setActionBarVisibilityCallback(InterfaceC0082d interfaceC0082d) {
        this.f497D = interfaceC0082d;
        if (getWindowToken() != null) {
            ((C0695t) this.f497D).f3713o = this.f505k;
            int i6 = this.f516v;
            if (i6 != 0) {
                onWindowSystemUiVisibilityChanged(i6);
                WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
                requestApplyInsets();
            }
        }
    }

    public void setHasNonEmbeddedTabs(boolean z5) {
        this.f512r = z5;
    }

    public void setHideOnContentScrollEnabled(boolean z5) {
        if (z5 != this.f513s) {
            this.f513s = z5;
            if (z5) {
                return;
            }
            m246q();
            setActionBarHideOffset(0);
        }
    }

    public void setIcon(int i6) {
        m248s();
        this.f508n.setIcon(i6);
    }

    public void setIcon(Drawable drawable) {
        m248s();
        this.f508n.setIcon(drawable);
    }

    public void setLogo(int i6) {
        m248s();
        this.f508n.mo373l(i6);
    }

    public void setOverlayMode(boolean z5) {
        this.f511q = z5;
        this.f510p = z5 && getContext().getApplicationInfo().targetSdkVersion < 19;
    }

    public void setShowingForActionMode(boolean z5) {
    }

    public void setUiOptions(int i6) {
    }

    @Override // androidx.appcompat.widget.InterfaceC0128e0
    public void setWindowCallback(Window.Callback callback) {
        m248s();
        this.f508n.setWindowCallback(callback);
    }

    @Override // androidx.appcompat.widget.InterfaceC0128e0
    public void setWindowTitle(CharSequence charSequence) {
        m248s();
        this.f508n.setWindowTitle(charSequence);
    }

    @Override // android.view.ViewGroup
    public final boolean shouldDelayChildPressedState() {
        return false;
    }
}
