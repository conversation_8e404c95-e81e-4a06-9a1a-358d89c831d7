package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.appcompat.widget.C0121c;
import com.liaoyuan.aicast.R;
import java.util.WeakHashMap;
import p008b0.C0385m;
import p028e.C0750a;
import p029e0.C0766p;
import p029e0.C0769s;
import p042g.AbstractC0868a;

/* loaded from: classes.dex */
public class ActionBarContextView extends AbstractC0115a {

    /* renamed from: A */
    public int f480A;

    /* renamed from: B */
    public boolean f481B;

    /* renamed from: C */
    public int f482C;

    /* renamed from: r */
    public CharSequence f483r;

    /* renamed from: s */
    public CharSequence f484s;

    /* renamed from: t */
    public View f485t;

    /* renamed from: u */
    public View f486u;

    /* renamed from: v */
    public View f487v;

    /* renamed from: w */
    public LinearLayout f488w;

    /* renamed from: x */
    public TextView f489x;

    /* renamed from: y */
    public TextView f490y;

    /* renamed from: z */
    public int f491z;

    /* renamed from: androidx.appcompat.widget.ActionBarContextView$a */
    public class ViewOnClickListenerC0078a implements View.OnClickListener {

        /* renamed from: j */
        public final /* synthetic */ AbstractC0868a f492j;

        public ViewOnClickListenerC0078a(AbstractC0868a abstractC0868a) {
            this.f492j = abstractC0868a;
        }

        @Override // android.view.View.OnClickListener
        public final void onClick(View view) {
            this.f492j.mo2041c();
        }
    }

    public ActionBarContextView(Context context, AttributeSet attributeSet) {
        super(context, attributeSet, R.attr.actionModeStyle);
        int resourceId;
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C0385m.f2353n, R.attr.actionModeStyle, 0);
        Drawable drawable = (!obtainStyledAttributes.hasValue(0) || (resourceId = obtainStyledAttributes.getResourceId(0, 0)) == 0) ? obtainStyledAttributes.getDrawable(0) : C0750a.m2138a(context, resourceId);
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        setBackground(drawable);
        this.f491z = obtainStyledAttributes.getResourceId(5, 0);
        this.f480A = obtainStyledAttributes.getResourceId(4, 0);
        this.f693n = obtainStyledAttributes.getLayoutDimension(3, 0);
        this.f482C = obtainStyledAttributes.getResourceId(2, R.layout.abc_action_mode_close_item_material);
        obtainStyledAttributes.recycle();
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0065  */
    /* JADX WARN: Removed duplicated region for block: B:13:0x007d  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x003e  */
    /* renamed from: f */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m227f(p042g.AbstractC0868a r6) {
        /*
            r5 = this;
            android.view.View r0 = r5.f485t
            r1 = 0
            if (r0 != 0) goto L16
            android.content.Context r0 = r5.getContext()
            android.view.LayoutInflater r0 = android.view.LayoutInflater.from(r0)
            int r2 = r5.f482C
            android.view.View r0 = r0.inflate(r2, r5, r1)
            r5.f485t = r0
            goto L1e
        L16:
            android.view.ViewParent r0 = r0.getParent()
            if (r0 != 0) goto L21
            android.view.View r0 = r5.f485t
        L1e:
            r5.addView(r0)
        L21:
            android.view.View r0 = r5.f485t
            r2 = 2131296319(0x7f09003f, float:1.8210551E38)
            android.view.View r0 = r0.findViewById(r2)
            r5.f486u = r0
            androidx.appcompat.widget.ActionBarContextView$a r2 = new androidx.appcompat.widget.ActionBarContextView$a
            r2.<init>(r6)
            r0.setOnClickListener(r2)
            android.view.Menu r6 = r6.mo2043e()
            androidx.appcompat.view.menu.e r6 = (androidx.appcompat.view.menu.C0070e) r6
            androidx.appcompat.widget.c r0 = r5.f692m
            if (r0 == 0) goto L41
            r0.m317b()
        L41:
            androidx.appcompat.widget.c r0 = new androidx.appcompat.widget.c
            android.content.Context r2 = r5.getContext()
            r0.<init>(r2)
            r5.f692m = r0
            r2 = 1
            r0.f722u = r2
            r0.f723v = r2
            android.view.ViewGroup$LayoutParams r0 = new android.view.ViewGroup$LayoutParams
            r2 = -2
            r3 = -1
            r0.<init>(r2, r3)
            androidx.appcompat.widget.c r2 = r5.f692m
            android.content.Context r3 = r5.f690k
            r6.m180c(r2, r3)
            androidx.appcompat.widget.c r6 = r5.f692m
            androidx.appcompat.view.menu.j r2 = r6.f324q
            if (r2 != 0) goto L79
            android.view.LayoutInflater r3 = r6.f320m
            int r4 = r6.f322o
            android.view.View r1 = r3.inflate(r4, r5, r1)
            androidx.appcompat.view.menu.j r1 = (androidx.appcompat.view.menu.InterfaceC0075j) r1
            r6.f324q = r1
            androidx.appcompat.view.menu.e r3 = r6.f319l
            r1.mo146c(r3)
            r6.mo156g()
        L79:
            androidx.appcompat.view.menu.j r1 = r6.f324q
            if (r2 == r1) goto L83
            r2 = r1
            androidx.appcompat.widget.ActionMenuView r2 = (androidx.appcompat.widget.ActionMenuView) r2
            r2.setPresenter(r6)
        L83:
            androidx.appcompat.widget.ActionMenuView r1 = (androidx.appcompat.widget.ActionMenuView) r1
            r5.f691l = r1
            r6 = 0
            java.util.WeakHashMap<android.view.View, e0.s> r2 = p029e0.C0766p.f4041a
            r1.setBackground(r6)
            androidx.appcompat.widget.ActionMenuView r6 = r5.f691l
            r5.addView(r6, r0)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.ActionBarContextView.m227f(g.a):void");
    }

    /* renamed from: g */
    public final void m228g() {
        if (this.f488w == null) {
            LayoutInflater.from(getContext()).inflate(R.layout.abc_action_bar_title_item, this);
            LinearLayout linearLayout = (LinearLayout) getChildAt(getChildCount() - 1);
            this.f488w = linearLayout;
            this.f489x = (TextView) linearLayout.findViewById(R.id.action_bar_title);
            this.f490y = (TextView) this.f488w.findViewById(R.id.action_bar_subtitle);
            if (this.f491z != 0) {
                this.f489x.setTextAppearance(getContext(), this.f491z);
            }
            if (this.f480A != 0) {
                this.f490y.setTextAppearance(getContext(), this.f480A);
            }
        }
        this.f489x.setText(this.f483r);
        this.f490y.setText(this.f484s);
        boolean z5 = !TextUtils.isEmpty(this.f483r);
        boolean z6 = !TextUtils.isEmpty(this.f484s);
        int i6 = 0;
        this.f490y.setVisibility(z6 ? 0 : 8);
        LinearLayout linearLayout2 = this.f488w;
        if (!z5 && !z6) {
            i6 = 8;
        }
        linearLayout2.setVisibility(i6);
        if (this.f488w.getParent() == null) {
            addView(this.f488w);
        }
    }

    @Override // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateDefaultLayoutParams() {
        return new ViewGroup.MarginLayoutParams(-1, -2);
    }

    @Override // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        return new ViewGroup.MarginLayoutParams(getContext(), attributeSet);
    }

    @Override // androidx.appcompat.widget.AbstractC0115a
    public /* bridge */ /* synthetic */ int getAnimatedVisibility() {
        return super.getAnimatedVisibility();
    }

    @Override // androidx.appcompat.widget.AbstractC0115a
    public /* bridge */ /* synthetic */ int getContentHeight() {
        return super.getContentHeight();
    }

    public CharSequence getSubtitle() {
        return this.f484s;
    }

    public CharSequence getTitle() {
        return this.f483r;
    }

    /* renamed from: h */
    public final void m229h() {
        removeAllViews();
        this.f487v = null;
        this.f691l = null;
        this.f692m = null;
        View view = this.f486u;
        if (view != null) {
            view.setOnClickListener(null);
        }
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        C0121c c0121c = this.f692m;
        if (c0121c != null) {
            c0121c.m319k();
            C0121c.a aVar = this.f692m.f715C;
            if (aVar == null || !aVar.m220b()) {
                return;
            }
            aVar.f442j.dismiss();
        }
    }

    @Override // android.view.View
    public final void onInitializeAccessibilityEvent(AccessibilityEvent accessibilityEvent) {
        if (accessibilityEvent.getEventType() != 32) {
            super.onInitializeAccessibilityEvent(accessibilityEvent);
            return;
        }
        accessibilityEvent.setSource(this);
        accessibilityEvent.setClassName(getClass().getName());
        accessibilityEvent.setPackageName(getContext().getPackageName());
        accessibilityEvent.setContentDescription(this.f483r);
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onLayout(boolean z5, int i6, int i7, int i8, int i9) {
        boolean m414b = C0144j1.m414b(this);
        int paddingRight = m414b ? (i8 - i6) - getPaddingRight() : getPaddingLeft();
        int paddingTop = getPaddingTop();
        int paddingTop2 = ((i9 - i7) - getPaddingTop()) - getPaddingBottom();
        View view = this.f485t;
        if (view != null && view.getVisibility() != 8) {
            ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) this.f485t.getLayoutParams();
            int i10 = m414b ? marginLayoutParams.rightMargin : marginLayoutParams.leftMargin;
            int i11 = m414b ? marginLayoutParams.leftMargin : marginLayoutParams.rightMargin;
            int i12 = m414b ? paddingRight - i10 : paddingRight + i10;
            int m312d = i12 + m312d(this.f485t, i12, paddingTop, paddingTop2, m414b);
            paddingRight = m414b ? m312d - i11 : m312d + i11;
        }
        int i13 = paddingRight;
        LinearLayout linearLayout = this.f488w;
        if (linearLayout != null && this.f487v == null && linearLayout.getVisibility() != 8) {
            i13 += m312d(this.f488w, i13, paddingTop, paddingTop2, m414b);
        }
        int i14 = i13;
        View view2 = this.f487v;
        if (view2 != null) {
            m312d(view2, i14, paddingTop, paddingTop2, m414b);
        }
        int paddingLeft = m414b ? getPaddingLeft() : (i8 - i6) - getPaddingRight();
        ActionMenuView actionMenuView = this.f691l;
        if (actionMenuView != null) {
            m312d(actionMenuView, paddingLeft, paddingTop, paddingTop2, !m414b);
        }
    }

    @Override // android.view.View
    public final void onMeasure(int i6, int i7) {
        if (View.MeasureSpec.getMode(i6) != 1073741824) {
            throw new IllegalStateException(getClass().getSimpleName() + " can only be used with android:layout_width=\"match_parent\" (or fill_parent)");
        }
        if (View.MeasureSpec.getMode(i7) == 0) {
            throw new IllegalStateException(getClass().getSimpleName() + " can only be used with android:layout_height=\"wrap_content\"");
        }
        int size = View.MeasureSpec.getSize(i6);
        int i8 = this.f693n;
        if (i8 <= 0) {
            i8 = View.MeasureSpec.getSize(i7);
        }
        int paddingBottom = getPaddingBottom() + getPaddingTop();
        int paddingLeft = (size - getPaddingLeft()) - getPaddingRight();
        int i9 = i8 - paddingBottom;
        int makeMeasureSpec = View.MeasureSpec.makeMeasureSpec(i9, Integer.MIN_VALUE);
        View view = this.f485t;
        if (view != null) {
            int m311c = m311c(view, paddingLeft, makeMeasureSpec);
            ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) this.f485t.getLayoutParams();
            paddingLeft = m311c - (marginLayoutParams.leftMargin + marginLayoutParams.rightMargin);
        }
        ActionMenuView actionMenuView = this.f691l;
        if (actionMenuView != null && actionMenuView.getParent() == this) {
            paddingLeft = m311c(this.f691l, paddingLeft, makeMeasureSpec);
        }
        LinearLayout linearLayout = this.f488w;
        if (linearLayout != null && this.f487v == null) {
            if (this.f481B) {
                this.f488w.measure(View.MeasureSpec.makeMeasureSpec(0, 0), makeMeasureSpec);
                int measuredWidth = this.f488w.getMeasuredWidth();
                boolean z5 = measuredWidth <= paddingLeft;
                if (z5) {
                    paddingLeft -= measuredWidth;
                }
                this.f488w.setVisibility(z5 ? 0 : 8);
            } else {
                paddingLeft = m311c(linearLayout, paddingLeft, makeMeasureSpec);
            }
        }
        View view2 = this.f487v;
        if (view2 != null) {
            ViewGroup.LayoutParams layoutParams = view2.getLayoutParams();
            int i10 = layoutParams.width;
            int i11 = i10 != -2 ? 1073741824 : Integer.MIN_VALUE;
            if (i10 >= 0) {
                paddingLeft = Math.min(i10, paddingLeft);
            }
            int i12 = layoutParams.height;
            int i13 = i12 == -2 ? Integer.MIN_VALUE : 1073741824;
            if (i12 >= 0) {
                i9 = Math.min(i12, i9);
            }
            this.f487v.measure(View.MeasureSpec.makeMeasureSpec(paddingLeft, i11), View.MeasureSpec.makeMeasureSpec(i9, i13));
        }
        if (this.f693n <= 0) {
            int childCount = getChildCount();
            i8 = 0;
            for (int i14 = 0; i14 < childCount; i14++) {
                int measuredHeight = getChildAt(i14).getMeasuredHeight() + paddingBottom;
                if (measuredHeight > i8) {
                    i8 = measuredHeight;
                }
            }
        }
        setMeasuredDimension(size, i8);
    }

    @Override // androidx.appcompat.widget.AbstractC0115a
    public void setContentHeight(int i6) {
        this.f693n = i6;
    }

    public void setCustomView(View view) {
        LinearLayout linearLayout;
        View view2 = this.f487v;
        if (view2 != null) {
            removeView(view2);
        }
        this.f487v = view;
        if (view != null && (linearLayout = this.f488w) != null) {
            removeView(linearLayout);
            this.f488w = null;
        }
        if (view != null) {
            addView(view);
        }
        requestLayout();
    }

    public void setSubtitle(CharSequence charSequence) {
        this.f484s = charSequence;
        m228g();
    }

    public void setTitle(CharSequence charSequence) {
        this.f483r = charSequence;
        m228g();
    }

    public void setTitleOptional(boolean z5) {
        if (z5 != this.f481B) {
            requestLayout();
        }
        this.f481B = z5;
    }

    @Override // androidx.appcompat.widget.AbstractC0115a, android.view.View
    public /* bridge */ /* synthetic */ void setVisibility(int i6) {
        super.setVisibility(i6);
    }

    @Override // android.view.ViewGroup
    public final boolean shouldDelayChildPressedState() {
        return false;
    }
}
