package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;
import com.liaoyuan.aicast.R;
import java.util.WeakHashMap;
import p008b0.C0385m;
import p029e0.C0766p;
import p029e0.C0769s;

/* loaded from: classes.dex */
public class ButtonBarLayout extends LinearLayout {

    /* renamed from: j */
    public boolean f546j;

    /* renamed from: k */
    public int f547k;

    public ButtonBarLayout(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.f547k = -1;
        int[] iArr = C0385m.f2367u;
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, iArr);
        C0766p.m2186s(this, context, iArr, attributeSet, obtainStyledAttributes, 0);
        this.f546j = obtainStyledAttributes.getBoolean(0, true);
        obtainStyledAttributes.recycle();
    }

    private void setStacked(boolean z5) {
        setOrientation(z5 ? 1 : 0);
        setGravity(z5 ? 8388613 : 80);
        View findViewById = findViewById(R.id.spacer);
        if (findViewById != null) {
            findViewById.setVisibility(z5 ? 8 : 4);
        }
        for (int childCount = getChildCount() - 2; childCount >= 0; childCount--) {
            bringChildToFront(getChildAt(childCount));
        }
    }

    /* renamed from: a */
    public final int m257a(int i6) {
        int childCount = getChildCount();
        while (i6 < childCount) {
            if (getChildAt(i6).getVisibility() == 0) {
                return i6;
            }
            i6++;
        }
        return -1;
    }

    /* renamed from: b */
    public final boolean m258b() {
        return getOrientation() == 1;
    }

    @Override // android.view.View
    public int getMinimumHeight() {
        return Math.max(0, super.getMinimumHeight());
    }

    @Override // android.widget.LinearLayout, android.view.View
    public final void onMeasure(int i6, int i7) {
        int i8;
        boolean z5;
        int size = View.MeasureSpec.getSize(i6);
        int i9 = 0;
        if (this.f546j) {
            if (size > this.f547k && m258b()) {
                setStacked(false);
            }
            this.f547k = size;
        }
        if (m258b() || View.MeasureSpec.getMode(i6) != 1073741824) {
            i8 = i6;
            z5 = false;
        } else {
            i8 = View.MeasureSpec.makeMeasureSpec(size, Integer.MIN_VALUE);
            z5 = true;
        }
        super.onMeasure(i8, i7);
        if (this.f546j && !m258b()) {
            if ((getMeasuredWidthAndState() & (-16777216)) == 16777216) {
                setStacked(true);
                z5 = true;
            }
        }
        if (z5) {
            super.onMeasure(i6, i7);
        }
        int m257a = m257a(0);
        if (m257a >= 0) {
            View childAt = getChildAt(m257a);
            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) childAt.getLayoutParams();
            int measuredHeight = childAt.getMeasuredHeight() + getPaddingTop() + layoutParams.topMargin + layoutParams.bottomMargin + 0;
            if (m258b()) {
                int m257a2 = m257a(m257a + 1);
                i9 = m257a2 >= 0 ? getChildAt(m257a2).getPaddingTop() + ((int) (getResources().getDisplayMetrics().density * 16.0f)) + measuredHeight : measuredHeight;
            } else {
                i9 = getPaddingBottom() + measuredHeight;
            }
        }
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        if (getMinimumHeight() != i9) {
            setMinimumHeight(i9);
        }
    }

    public void setAllowStacking(boolean z5) {
        if (this.f546j != z5) {
            this.f546j = z5;
            if (!z5 && getOrientation() == 1) {
                setStacked(false);
            }
            requestLayout();
        }
    }
}
