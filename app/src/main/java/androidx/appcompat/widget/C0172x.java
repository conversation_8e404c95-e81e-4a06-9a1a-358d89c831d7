package androidx.appcompat.widget;

import android.view.textclassifier.TextClassificationManager;
import android.view.textclassifier.TextClassifier;
import android.widget.TextView;
import java.util.Objects;

/* renamed from: androidx.appcompat.widget.x */
/* loaded from: classes.dex */
public final class C0172x {

    /* renamed from: a */
    public TextView f1008a;

    /* renamed from: b */
    public TextClassifier f1009b;

    public C0172x(TextView textView) {
        Objects.requireNonNull(textView);
        this.f1008a = textView;
    }

    /* renamed from: a */
    public final TextClassifier m479a() {
        TextClassifier textClassifier = this.f1009b;
        if (textClassifier != null) {
            return textClassifier;
        }
        TextClassificationManager textClassificationManager = (TextClassificationManager) this.f1008a.getContext().getSystemService(TextClassificationManager.class);
        return textClassificationManager != null ? textClassificationManager.getTextClassifier() : TextClassifier.NO_OP;
    }
}
