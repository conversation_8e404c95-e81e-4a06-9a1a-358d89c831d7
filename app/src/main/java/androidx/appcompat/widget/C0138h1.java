package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.Rect;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.TextView;
import com.liaoyuan.aicast.R;

/* renamed from: androidx.appcompat.widget.h1 */
/* loaded from: classes.dex */
public final class C0138h1 {

    /* renamed from: a */
    public final Context f821a;

    /* renamed from: b */
    public final View f822b;

    /* renamed from: c */
    public final TextView f823c;

    /* renamed from: d */
    public final WindowManager.LayoutParams f824d;

    /* renamed from: e */
    public final Rect f825e;

    /* renamed from: f */
    public final int[] f826f;

    /* renamed from: g */
    public final int[] f827g;

    public C0138h1(Context context) {
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
        this.f824d = layoutParams;
        this.f825e = new Rect();
        this.f826f = new int[2];
        this.f827g = new int[2];
        this.f821a = context;
        View inflate = LayoutInflater.from(context).inflate(R.layout.abc_tooltip, (ViewGroup) null);
        this.f822b = inflate;
        this.f823c = (TextView) inflate.findViewById(R.id.message);
        layoutParams.setTitle(C0138h1.class.getSimpleName());
        layoutParams.packageName = context.getPackageName();
        layoutParams.type = 1002;
        layoutParams.width = -2;
        layoutParams.height = -2;
        layoutParams.format = -3;
        layoutParams.windowAnimations = 2131820549;
        layoutParams.flags = 24;
    }

    /* renamed from: a */
    public final void m397a() {
        if (this.f822b.getParent() != null) {
            ((WindowManager) this.f821a.getSystemService("window")).removeView(this.f822b);
        }
    }
}
