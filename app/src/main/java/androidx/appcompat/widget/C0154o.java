package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.View;
import android.widget.PopupWindow;
import p008b0.C0385m;
import p028e.C0750a;

/* renamed from: androidx.appcompat.widget.o */
/* loaded from: classes.dex */
public final class C0154o extends PopupWindow {
    public C0154o(Context context, AttributeSet attributeSet, int i6, int i7) {
        super(context, attributeSet, i6, i7);
        int resourceId;
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C0385m.f2306B, i6, i7);
        if (obtainStyledAttributes.hasValue(2)) {
            setOverlapAnchor(obtainStyledAttributes.getBoolean(2, false));
        }
        setBackgroundDrawable((!obtainStyledAttributes.hasValue(0) || (resourceId = obtainStyledAttributes.getResourceId(0, 0)) == 0) ? obtainStyledAttributes.getDrawable(0) : C0750a.m2138a(context, resourceId));
        obtainStyledAttributes.recycle();
    }

    @Override // android.widget.PopupWindow
    public final void showAsDropDown(View view, int i6, int i7) {
        super.showAsDropDown(view, i6, i7);
    }

    @Override // android.widget.PopupWindow
    public final void showAsDropDown(View view, int i6, int i7, int i8) {
        super.showAsDropDown(view, i6, i7, i8);
    }

    @Override // android.widget.PopupWindow
    public final void update(View view, int i6, int i7, int i8, int i9) {
        super.update(view, i6, i7, i8, i9);
    }
}
