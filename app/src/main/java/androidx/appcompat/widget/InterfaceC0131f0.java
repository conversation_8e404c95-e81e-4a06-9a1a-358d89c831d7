package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.Menu;
import android.view.ViewGroup;
import android.view.Window;
import androidx.appcompat.view.menu.InterfaceC0074i;
import p029e0.C0769s;

/* renamed from: androidx.appcompat.widget.f0 */
/* loaded from: classes.dex */
public interface InterfaceC0131f0 {
    /* renamed from: a */
    void mo362a(Menu menu, InterfaceC0074i.a aVar);

    /* renamed from: b */
    boolean mo363b();

    /* renamed from: c */
    boolean mo364c();

    void collapseActionView();

    /* renamed from: d */
    boolean mo365d();

    /* renamed from: e */
    boolean mo366e();

    /* renamed from: f */
    void mo367f();

    /* renamed from: g */
    boolean mo368g();

    CharSequence getTitle();

    /* renamed from: h */
    void mo369h();

    /* renamed from: i */
    void mo370i();

    /* renamed from: j */
    int mo371j();

    /* renamed from: k */
    void mo372k(int i6);

    /* renamed from: l */
    void mo373l(int i6);

    /* renamed from: m */
    void mo374m();

    /* renamed from: n */
    ViewGroup mo375n();

    /* renamed from: o */
    Context mo376o();

    /* renamed from: p */
    void mo377p();

    /* renamed from: q */
    C0769s mo378q(int i6, long j6);

    /* renamed from: r */
    void mo379r();

    /* renamed from: s */
    boolean mo380s();

    void setIcon(int i6);

    void setIcon(Drawable drawable);

    void setTitle(CharSequence charSequence);

    void setWindowCallback(Window.Callback callback);

    void setWindowTitle(CharSequence charSequence);

    /* renamed from: t */
    void mo381t();

    /* renamed from: u */
    void mo382u(boolean z5);

    /* renamed from: v */
    void mo383v(int i6);
}
