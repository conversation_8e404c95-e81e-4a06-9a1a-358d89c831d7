package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.Configuration;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.ContextThemeWrapper;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewDebug;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import android.widget.LinearLayout;
import androidx.appcompat.view.menu.ActionMenuItemView;
import androidx.appcompat.view.menu.C0070e;
import androidx.appcompat.view.menu.C0072g;
import androidx.appcompat.view.menu.InterfaceC0074i;
import androidx.appcompat.view.menu.InterfaceC0075j;
import androidx.appcompat.widget.C0121c;
import androidx.appcompat.widget.C0146k0;
import androidx.appcompat.widget.Toolbar;
import p021d.C0692q;

/* loaded from: classes.dex */
public class ActionMenuView extends C0146k0 implements C0070e.b, InterfaceC0075j {

    /* renamed from: A */
    public int f524A;

    /* renamed from: B */
    public boolean f525B;

    /* renamed from: C */
    public C0121c f526C;

    /* renamed from: D */
    public InterfaceC0074i.a f527D;

    /* renamed from: E */
    public C0070e.a f528E;

    /* renamed from: F */
    public boolean f529F;

    /* renamed from: G */
    public int f530G;

    /* renamed from: H */
    public int f531H;

    /* renamed from: I */
    public int f532I;

    /* renamed from: J */
    public InterfaceC0088e f533J;

    /* renamed from: y */
    public C0070e f534y;

    /* renamed from: z */
    public Context f535z;

    /* renamed from: androidx.appcompat.widget.ActionMenuView$a */
    public interface InterfaceC0084a {
        /* renamed from: a */
        boolean mo137a();

        /* renamed from: c */
        boolean mo139c();
    }

    /* renamed from: androidx.appcompat.widget.ActionMenuView$b */
    public static class C0085b implements InterfaceC0074i.a {
        @Override // androidx.appcompat.view.menu.InterfaceC0074i.a
        /* renamed from: a */
        public final void mo206a(C0070e c0070e, boolean z5) {
        }

        @Override // androidx.appcompat.view.menu.InterfaceC0074i.a
        /* renamed from: b */
        public final boolean mo207b(C0070e c0070e) {
            return false;
        }
    }

    /* renamed from: androidx.appcompat.widget.ActionMenuView$c */
    public static class C0086c extends C0146k0.a {

        /* renamed from: a */
        @ViewDebug.ExportedProperty
        public boolean f536a;

        /* renamed from: b */
        @ViewDebug.ExportedProperty
        public int f537b;

        /* renamed from: c */
        @ViewDebug.ExportedProperty
        public int f538c;

        /* renamed from: d */
        @ViewDebug.ExportedProperty
        public boolean f539d;

        /* renamed from: e */
        @ViewDebug.ExportedProperty
        public boolean f540e;

        /* renamed from: f */
        public boolean f541f;

        public C0086c() {
            super(-2, -2);
            this.f536a = false;
        }

        public C0086c(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
        }

        public C0086c(ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
        }

        public C0086c(C0086c c0086c) {
            super(c0086c);
            this.f536a = c0086c.f536a;
        }
    }

    /* renamed from: androidx.appcompat.widget.ActionMenuView$d */
    public class C0087d implements C0070e.a {
        public C0087d() {
        }

        @Override // androidx.appcompat.view.menu.C0070e.a
        /* renamed from: a */
        public final boolean mo204a(C0070e c0070e, MenuItem menuItem) {
            InterfaceC0088e interfaceC0088e = ActionMenuView.this.f533J;
            if (interfaceC0088e == null) {
                return false;
            }
            Toolbar.InterfaceC0112f interfaceC0112f = Toolbar.this.f649P;
            return interfaceC0112f != null ? C0692q.this.f3675c.onMenuItemSelected(0, menuItem) : false;
        }

        @Override // androidx.appcompat.view.menu.C0070e.a
        /* renamed from: b */
        public final void mo205b(C0070e c0070e) {
            C0070e.a aVar = ActionMenuView.this.f528E;
            if (aVar != null) {
                aVar.mo205b(c0070e);
            }
        }
    }

    /* renamed from: androidx.appcompat.widget.ActionMenuView$e */
    public interface InterfaceC0088e {
    }

    public ActionMenuView(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        setBaselineAligned(false);
        float f6 = context.getResources().getDisplayMetrics().density;
        this.f531H = (int) (56.0f * f6);
        this.f532I = (int) (f6 * 4.0f);
        this.f535z = context;
        this.f524A = 0;
    }

    /* renamed from: o */
    public static int m249o(View view, int i6, int i7, int i8, int i9) {
        C0086c c0086c = (C0086c) view.getLayoutParams();
        int makeMeasureSpec = View.MeasureSpec.makeMeasureSpec(View.MeasureSpec.getSize(i8) - i9, View.MeasureSpec.getMode(i8));
        ActionMenuItemView actionMenuItemView = view instanceof ActionMenuItemView ? (ActionMenuItemView) view : null;
        boolean z5 = false;
        boolean z6 = actionMenuItemView != null && actionMenuItemView.m140d();
        int i10 = 2;
        if (i7 <= 0 || (z6 && i7 < 2)) {
            i10 = 0;
        } else {
            view.measure(View.MeasureSpec.makeMeasureSpec(i7 * i6, Integer.MIN_VALUE), makeMeasureSpec);
            int measuredWidth = view.getMeasuredWidth();
            int i11 = measuredWidth / i6;
            if (measuredWidth % i6 != 0) {
                i11++;
            }
            if (!z6 || i11 >= 2) {
                i10 = i11;
            }
        }
        if (!c0086c.f536a && z6) {
            z5 = true;
        }
        c0086c.f539d = z5;
        c0086c.f537b = i10;
        view.measure(View.MeasureSpec.makeMeasureSpec(i6 * i10, 1073741824), makeMeasureSpec);
        return i10;
    }

    @Override // androidx.appcompat.view.menu.C0070e.b
    /* renamed from: a */
    public final boolean mo145a(C0072g c0072g) {
        return this.f534y.m197t(c0072g, null, 0);
    }

    @Override // androidx.appcompat.view.menu.InterfaceC0075j
    /* renamed from: c */
    public final void mo146c(C0070e c0070e) {
        this.f534y = c0070e;
    }

    @Override // androidx.appcompat.widget.C0146k0, android.view.ViewGroup
    public final boolean checkLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return layoutParams instanceof C0086c;
    }

    @Override // android.view.View
    public final boolean dispatchPopulateAccessibilityEvent(AccessibilityEvent accessibilityEvent) {
        return false;
    }

    @Override // androidx.appcompat.widget.C0146k0, android.view.ViewGroup
    public final ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        return new C0086c(getContext(), attributeSet);
    }

    public Menu getMenu() {
        if (this.f534y == null) {
            Context context = getContext();
            C0070e c0070e = new C0070e(context);
            this.f534y = c0070e;
            c0070e.mo202y(new C0087d());
            C0121c c0121c = new C0121c(context);
            this.f526C = c0121c;
            c0121c.f722u = true;
            c0121c.f723v = true;
            InterfaceC0074i.a aVar = this.f527D;
            if (aVar == null) {
                aVar = new C0085b();
            }
            c0121c.f321n = aVar;
            this.f534y.m180c(c0121c, this.f535z);
            C0121c c0121c2 = this.f526C;
            c0121c2.f324q = this;
            this.f534y = c0121c2.f319l;
        }
        return this.f534y;
    }

    public Drawable getOverflowIcon() {
        getMenu();
        C0121c c0121c = this.f526C;
        C0121c.d dVar = c0121c.f719r;
        if (dVar != null) {
            return dVar.getDrawable();
        }
        if (c0121c.f721t) {
            return c0121c.f720s;
        }
        return null;
    }

    public int getPopupTheme() {
        return this.f524A;
    }

    public int getWindowAnimations() {
        return 0;
    }

    @Override // androidx.appcompat.widget.C0146k0
    /* renamed from: i */
    public final C0146k0.a generateLayoutParams(AttributeSet attributeSet) {
        return new C0086c(getContext(), attributeSet);
    }

    @Override // androidx.appcompat.widget.C0146k0
    /* renamed from: l, reason: merged with bridge method [inline-methods] and merged with bridge method [inline-methods] */
    public final C0086c mo250h() {
        C0086c c0086c = new C0086c();
        ((LinearLayout.LayoutParams) c0086c).gravity = 16;
        return c0086c;
    }

    @Override // androidx.appcompat.widget.C0146k0
    /* renamed from: m, reason: merged with bridge method [inline-methods] and merged with bridge method [inline-methods] */
    public final C0086c mo252j(ViewGroup.LayoutParams layoutParams) {
        if (layoutParams == null) {
            return generateDefaultLayoutParams();
        }
        C0086c c0086c = layoutParams instanceof C0086c ? new C0086c((C0086c) layoutParams) : new C0086c(layoutParams);
        if (((LinearLayout.LayoutParams) c0086c).gravity <= 0) {
            ((LinearLayout.LayoutParams) c0086c).gravity = 16;
        }
        return c0086c;
    }

    /* renamed from: n */
    public final boolean m255n(int i6) {
        boolean z5 = false;
        if (i6 == 0) {
            return false;
        }
        KeyEvent.Callback childAt = getChildAt(i6 - 1);
        KeyEvent.Callback childAt2 = getChildAt(i6);
        if (i6 < getChildCount() && (childAt instanceof InterfaceC0084a)) {
            z5 = false | ((InterfaceC0084a) childAt).mo137a();
        }
        return (i6 <= 0 || !(childAt2 instanceof InterfaceC0084a)) ? z5 : z5 | ((InterfaceC0084a) childAt2).mo139c();
    }

    @Override // android.view.View
    public final void onConfigurationChanged(Configuration configuration) {
        super.onConfigurationChanged(configuration);
        C0121c c0121c = this.f526C;
        if (c0121c != null) {
            c0121c.mo156g();
            if (this.f526C.m320l()) {
                this.f526C.m319k();
                this.f526C.m321m();
            }
        }
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        C0121c c0121c = this.f526C;
        if (c0121c != null) {
            c0121c.m317b();
        }
    }

    @Override // androidx.appcompat.widget.C0146k0, android.view.ViewGroup, android.view.View
    public final void onLayout(boolean z5, int i6, int i7, int i8, int i9) {
        int width;
        int i10;
        if (!this.f529F) {
            super.onLayout(z5, i6, i7, i8, i9);
            return;
        }
        int childCount = getChildCount();
        int i11 = (i9 - i7) / 2;
        int dividerWidth = getDividerWidth();
        int i12 = i8 - i6;
        int paddingRight = (i12 - getPaddingRight()) - getPaddingLeft();
        boolean m414b = C0144j1.m414b(this);
        int i13 = 0;
        int i14 = 0;
        for (int i15 = 0; i15 < childCount; i15++) {
            View childAt = getChildAt(i15);
            if (childAt.getVisibility() != 8) {
                C0086c c0086c = (C0086c) childAt.getLayoutParams();
                if (c0086c.f536a) {
                    int measuredWidth = childAt.getMeasuredWidth();
                    if (m255n(i15)) {
                        measuredWidth += dividerWidth;
                    }
                    int measuredHeight = childAt.getMeasuredHeight();
                    if (m414b) {
                        i10 = getPaddingLeft() + ((LinearLayout.LayoutParams) c0086c).leftMargin;
                        width = i10 + measuredWidth;
                    } else {
                        width = (getWidth() - getPaddingRight()) - ((LinearLayout.LayoutParams) c0086c).rightMargin;
                        i10 = width - measuredWidth;
                    }
                    int i16 = i11 - (measuredHeight / 2);
                    childAt.layout(i10, i16, width, measuredHeight + i16);
                    paddingRight -= measuredWidth;
                    i13 = 1;
                } else {
                    paddingRight -= (childAt.getMeasuredWidth() + ((LinearLayout.LayoutParams) c0086c).leftMargin) + ((LinearLayout.LayoutParams) c0086c).rightMargin;
                    m255n(i15);
                    i14++;
                }
            }
        }
        if (childCount == 1 && i13 == 0) {
            View childAt2 = getChildAt(0);
            int measuredWidth2 = childAt2.getMeasuredWidth();
            int measuredHeight2 = childAt2.getMeasuredHeight();
            int i17 = (i12 / 2) - (measuredWidth2 / 2);
            int i18 = i11 - (measuredHeight2 / 2);
            childAt2.layout(i17, i18, measuredWidth2 + i17, measuredHeight2 + i18);
            return;
        }
        int i19 = i14 - (i13 ^ 1);
        int max = Math.max(0, i19 > 0 ? paddingRight / i19 : 0);
        if (m414b) {
            int width2 = getWidth() - getPaddingRight();
            for (int i20 = 0; i20 < childCount; i20++) {
                View childAt3 = getChildAt(i20);
                C0086c c0086c2 = (C0086c) childAt3.getLayoutParams();
                if (childAt3.getVisibility() != 8 && !c0086c2.f536a) {
                    int i21 = width2 - ((LinearLayout.LayoutParams) c0086c2).rightMargin;
                    int measuredWidth3 = childAt3.getMeasuredWidth();
                    int measuredHeight3 = childAt3.getMeasuredHeight();
                    int i22 = i11 - (measuredHeight3 / 2);
                    childAt3.layout(i21 - measuredWidth3, i22, i21, measuredHeight3 + i22);
                    width2 = i21 - ((measuredWidth3 + ((LinearLayout.LayoutParams) c0086c2).leftMargin) + max);
                }
            }
            return;
        }
        int paddingLeft = getPaddingLeft();
        for (int i23 = 0; i23 < childCount; i23++) {
            View childAt4 = getChildAt(i23);
            C0086c c0086c3 = (C0086c) childAt4.getLayoutParams();
            if (childAt4.getVisibility() != 8 && !c0086c3.f536a) {
                int i24 = paddingLeft + ((LinearLayout.LayoutParams) c0086c3).leftMargin;
                int measuredWidth4 = childAt4.getMeasuredWidth();
                int measuredHeight4 = childAt4.getMeasuredHeight();
                int i25 = i11 - (measuredHeight4 / 2);
                childAt4.layout(i24, i25, i24 + measuredWidth4, measuredHeight4 + i25);
                paddingLeft = measuredWidth4 + ((LinearLayout.LayoutParams) c0086c3).rightMargin + max + i24;
            }
        }
    }

    /* JADX WARN: Type inference failed for: r3v33 */
    /* JADX WARN: Type inference failed for: r3v34, types: [boolean, int] */
    /* JADX WARN: Type inference failed for: r3v40 */
    @Override // androidx.appcompat.widget.C0146k0, android.view.View
    public final void onMeasure(int i6, int i7) {
        int i8;
        boolean z5;
        int i9;
        int i10;
        boolean z6;
        int i11;
        ?? r32;
        C0070e c0070e;
        boolean z7 = this.f529F;
        boolean z8 = View.MeasureSpec.getMode(i6) == 1073741824;
        this.f529F = z8;
        if (z7 != z8) {
            this.f530G = 0;
        }
        int size = View.MeasureSpec.getSize(i6);
        if (this.f529F && (c0070e = this.f534y) != null && size != this.f530G) {
            this.f530G = size;
            c0070e.m195r(true);
        }
        int childCount = getChildCount();
        if (!this.f529F || childCount <= 0) {
            for (int i12 = 0; i12 < childCount; i12++) {
                C0086c c0086c = (C0086c) getChildAt(i12).getLayoutParams();
                ((LinearLayout.LayoutParams) c0086c).rightMargin = 0;
                ((LinearLayout.LayoutParams) c0086c).leftMargin = 0;
            }
            super.onMeasure(i6, i7);
            return;
        }
        int mode = View.MeasureSpec.getMode(i7);
        int size2 = View.MeasureSpec.getSize(i6);
        int size3 = View.MeasureSpec.getSize(i7);
        int paddingRight = getPaddingRight() + getPaddingLeft();
        int paddingBottom = getPaddingBottom() + getPaddingTop();
        int childMeasureSpec = ViewGroup.getChildMeasureSpec(i7, paddingBottom, -2);
        int i13 = size2 - paddingRight;
        int i14 = this.f531H;
        int i15 = i13 / i14;
        int i16 = i13 % i14;
        if (i15 == 0) {
            setMeasuredDimension(i13, 0);
            return;
        }
        int i17 = (i16 / i15) + i14;
        int childCount2 = getChildCount();
        int i18 = 0;
        int i19 = 0;
        int i20 = 0;
        int i21 = 0;
        int i22 = 0;
        boolean z9 = false;
        long j6 = 0;
        while (i22 < childCount2) {
            View childAt = getChildAt(i22);
            int i23 = size3;
            int i24 = i13;
            if (childAt.getVisibility() != 8) {
                boolean z10 = childAt instanceof ActionMenuItemView;
                int i25 = i18 + 1;
                if (z10) {
                    int i26 = this.f532I;
                    i11 = i25;
                    r32 = 0;
                    childAt.setPadding(i26, 0, i26, 0);
                } else {
                    i11 = i25;
                    r32 = 0;
                }
                C0086c c0086c2 = (C0086c) childAt.getLayoutParams();
                c0086c2.f541f = r32;
                c0086c2.f538c = r32;
                c0086c2.f537b = r32;
                c0086c2.f539d = r32;
                ((LinearLayout.LayoutParams) c0086c2).leftMargin = r32;
                ((LinearLayout.LayoutParams) c0086c2).rightMargin = r32;
                c0086c2.f540e = z10 && ((ActionMenuItemView) childAt).m140d();
                int m249o = m249o(childAt, i17, c0086c2.f536a ? 1 : i15, childMeasureSpec, paddingBottom);
                i20 = Math.max(i20, m249o);
                if (c0086c2.f539d) {
                    i21++;
                }
                if (c0086c2.f536a) {
                    z9 = true;
                }
                i15 -= m249o;
                i19 = Math.max(i19, childAt.getMeasuredHeight());
                if (m249o == 1) {
                    j6 |= 1 << i22;
                }
                i18 = i11;
            }
            i22++;
            size3 = i23;
            i13 = i24;
        }
        int i27 = i13;
        int i28 = size3;
        boolean z11 = z9 && i18 == 2;
        boolean z12 = false;
        while (i21 > 0 && i15 > 0) {
            int i29 = Integer.MAX_VALUE;
            int i30 = 0;
            int i31 = 0;
            long j7 = 0;
            while (i30 < childCount2) {
                int i32 = i19;
                C0086c c0086c3 = (C0086c) getChildAt(i30).getLayoutParams();
                boolean z13 = z12;
                if (c0086c3.f539d) {
                    int i33 = c0086c3.f537b;
                    if (i33 < i29) {
                        j7 = 1 << i30;
                        i29 = i33;
                        i31 = 1;
                    } else if (i33 == i29) {
                        i31++;
                        j7 |= 1 << i30;
                    }
                }
                i30++;
                z12 = z13;
                i19 = i32;
            }
            i8 = i19;
            z5 = z12;
            j6 |= j7;
            if (i31 > i15) {
                break;
            }
            int i34 = i29 + 1;
            int i35 = 0;
            while (i35 < childCount2) {
                View childAt2 = getChildAt(i35);
                C0086c c0086c4 = (C0086c) childAt2.getLayoutParams();
                int i36 = i21;
                long j8 = 1 << i35;
                if ((j7 & j8) == 0) {
                    if (c0086c4.f537b == i34) {
                        j6 |= j8;
                    }
                    z6 = z11;
                } else {
                    if (z11 && c0086c4.f540e && i15 == 1) {
                        int i37 = this.f532I;
                        z6 = z11;
                        childAt2.setPadding(i37 + i17, 0, i37, 0);
                    } else {
                        z6 = z11;
                    }
                    c0086c4.f537b++;
                    c0086c4.f541f = true;
                    i15--;
                }
                i35++;
                i21 = i36;
                z11 = z6;
            }
            i19 = i8;
            z12 = true;
        }
        i8 = i19;
        z5 = z12;
        boolean z14 = !z9 && i18 == 1;
        if (i15 > 0 && j6 != 0 && (i15 < i18 - 1 || z14 || i20 > 1)) {
            float bitCount = Long.bitCount(j6);
            if (!z14) {
                if ((j6 & 1) != 0 && !((C0086c) getChildAt(0).getLayoutParams()).f540e) {
                    bitCount -= 0.5f;
                }
                int i38 = childCount2 - 1;
                if ((j6 & (1 << i38)) != 0 && !((C0086c) getChildAt(i38).getLayoutParams()).f540e) {
                    bitCount -= 0.5f;
                }
            }
            int i39 = bitCount > 0.0f ? (int) ((i15 * i17) / bitCount) : 0;
            for (int i40 = 0; i40 < childCount2; i40++) {
                if ((j6 & (1 << i40)) != 0) {
                    View childAt3 = getChildAt(i40);
                    C0086c c0086c5 = (C0086c) childAt3.getLayoutParams();
                    if (childAt3 instanceof ActionMenuItemView) {
                        c0086c5.f538c = i39;
                        c0086c5.f541f = true;
                        if (i40 == 0 && !c0086c5.f540e) {
                            ((LinearLayout.LayoutParams) c0086c5).leftMargin = (-i39) / 2;
                        }
                        z5 = true;
                    } else if (c0086c5.f536a) {
                        c0086c5.f538c = i39;
                        c0086c5.f541f = true;
                        ((LinearLayout.LayoutParams) c0086c5).rightMargin = (-i39) / 2;
                        z5 = true;
                    } else {
                        if (i40 != 0) {
                            ((LinearLayout.LayoutParams) c0086c5).leftMargin = i39 / 2;
                        }
                        if (i40 != childCount2 - 1) {
                            ((LinearLayout.LayoutParams) c0086c5).rightMargin = i39 / 2;
                        }
                    }
                }
            }
        }
        if (z5) {
            for (int i41 = 0; i41 < childCount2; i41++) {
                View childAt4 = getChildAt(i41);
                C0086c c0086c6 = (C0086c) childAt4.getLayoutParams();
                if (c0086c6.f541f) {
                    childAt4.measure(View.MeasureSpec.makeMeasureSpec((c0086c6.f537b * i17) + c0086c6.f538c, 1073741824), childMeasureSpec);
                }
            }
        }
        if (mode != 1073741824) {
            i10 = i27;
            i9 = i8;
        } else {
            i9 = i28;
            i10 = i27;
        }
        setMeasuredDimension(i10, i9);
    }

    public void setExpandedActionViewsExclusive(boolean z5) {
        this.f526C.f727z = z5;
    }

    public void setOnMenuItemClickListener(InterfaceC0088e interfaceC0088e) {
        this.f533J = interfaceC0088e;
    }

    public void setOverflowIcon(Drawable drawable) {
        getMenu();
        C0121c c0121c = this.f526C;
        C0121c.d dVar = c0121c.f719r;
        if (dVar != null) {
            dVar.setImageDrawable(drawable);
        } else {
            c0121c.f721t = true;
            c0121c.f720s = drawable;
        }
    }

    public void setOverflowReserved(boolean z5) {
        this.f525B = z5;
    }

    public void setPopupTheme(int i6) {
        if (this.f524A != i6) {
            this.f524A = i6;
            if (i6 == 0) {
                this.f535z = getContext();
            } else {
                this.f535z = new ContextThemeWrapper(getContext(), i6);
            }
        }
    }

    public void setPresenter(C0121c c0121c) {
        this.f526C = c0121c;
        c0121c.f324q = this;
        this.f534y = c0121c.f319l;
    }
}
