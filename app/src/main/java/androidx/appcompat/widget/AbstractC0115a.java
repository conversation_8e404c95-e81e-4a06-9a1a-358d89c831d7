package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.ContextThemeWrapper;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import androidx.appcompat.view.menu.C0070e;
import com.liaoyuan.aicast.R;
import p008b0.C0385m;
import p029e0.C0766p;
import p029e0.C0769s;
import p029e0.InterfaceC0770t;

/* renamed from: androidx.appcompat.widget.a */
/* loaded from: classes.dex */
public abstract class AbstractC0115a extends ViewGroup {

    /* renamed from: j */
    public final a f689j;

    /* renamed from: k */
    public final Context f690k;

    /* renamed from: l */
    public ActionMenuView f691l;

    /* renamed from: m */
    public C0121c f692m;

    /* renamed from: n */
    public int f693n;

    /* renamed from: o */
    public C0769s f694o;

    /* renamed from: p */
    public boolean f695p;

    /* renamed from: q */
    public boolean f696q;

    /* renamed from: androidx.appcompat.widget.a$a */
    public class a implements InterfaceC0770t {

        /* renamed from: j */
        public boolean f697j = false;

        /* renamed from: k */
        public int f698k;

        public a() {
        }

        @Override // p029e0.InterfaceC0770t
        /* renamed from: b */
        public final void mo314b() {
            if (this.f697j) {
                return;
            }
            AbstractC0115a abstractC0115a = AbstractC0115a.this;
            abstractC0115a.f694o = null;
            AbstractC0115a.super.setVisibility(this.f698k);
        }

        @Override // p029e0.InterfaceC0770t
        /* renamed from: f */
        public final void mo315f(View view) {
            this.f697j = true;
        }

        @Override // p029e0.InterfaceC0770t
        /* renamed from: g */
        public final void mo316g() {
            AbstractC0115a.super.setVisibility(0);
            this.f697j = false;
        }
    }

    public AbstractC0115a(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, 0);
    }

    public AbstractC0115a(Context context, AttributeSet attributeSet, int i6) {
        super(context, attributeSet, i6);
        this.f689j = new a();
        TypedValue typedValue = new TypedValue();
        if (!context.getTheme().resolveAttribute(R.attr.actionBarPopupTheme, typedValue, true) || typedValue.resourceId == 0) {
            this.f690k = context;
        } else {
            this.f690k = new ContextThemeWrapper(context, typedValue.resourceId);
        }
    }

    /* renamed from: c */
    public final int m311c(View view, int i6, int i7) {
        view.measure(View.MeasureSpec.makeMeasureSpec(i6, Integer.MIN_VALUE), i7);
        return Math.max(0, (i6 - view.getMeasuredWidth()) - 0);
    }

    /* renamed from: d */
    public final int m312d(View view, int i6, int i7, int i8, boolean z5) {
        int measuredWidth = view.getMeasuredWidth();
        int measuredHeight = view.getMeasuredHeight();
        int i9 = ((i8 - measuredHeight) / 2) + i7;
        if (z5) {
            view.layout(i6 - measuredWidth, i9, i6, measuredHeight + i9);
        } else {
            view.layout(i6, i9, i6 + measuredWidth, measuredHeight + i9);
        }
        return z5 ? -measuredWidth : measuredWidth;
    }

    /* renamed from: e */
    public final C0769s m313e(int i6, long j6) {
        C0769s c0769s = this.f694o;
        if (c0769s != null) {
            c0769s.m2208b();
        }
        if (i6 != 0) {
            C0769s m2169b = C0766p.m2169b(this);
            m2169b.m2207a(0.0f);
            m2169b.m2209c(j6);
            a aVar = this.f689j;
            AbstractC0115a.this.f694o = m2169b;
            aVar.f698k = i6;
            m2169b.m2210d(aVar);
            return m2169b;
        }
        if (getVisibility() != 0) {
            setAlpha(0.0f);
        }
        C0769s m2169b2 = C0766p.m2169b(this);
        m2169b2.m2207a(1.0f);
        m2169b2.m2209c(j6);
        a aVar2 = this.f689j;
        AbstractC0115a.this.f694o = m2169b2;
        aVar2.f698k = i6;
        m2169b2.m2210d(aVar2);
        return m2169b2;
    }

    public int getAnimatedVisibility() {
        return this.f694o != null ? this.f689j.f698k : getVisibility();
    }

    public int getContentHeight() {
        return this.f693n;
    }

    @Override // android.view.View
    public final void onConfigurationChanged(Configuration configuration) {
        super.onConfigurationChanged(configuration);
        TypedArray obtainStyledAttributes = getContext().obtainStyledAttributes(null, C0385m.f2347k, R.attr.actionBarStyle, 0);
        setContentHeight(obtainStyledAttributes.getLayoutDimension(13, 0));
        obtainStyledAttributes.recycle();
        C0121c c0121c = this.f692m;
        if (c0121c != null) {
            Configuration configuration2 = c0121c.f318k.getResources().getConfiguration();
            int i6 = configuration2.screenWidthDp;
            int i7 = configuration2.screenHeightDp;
            c0121c.f726y = (configuration2.smallestScreenWidthDp > 600 || i6 > 600 || (i6 > 960 && i7 > 720) || (i6 > 720 && i7 > 960)) ? 5 : (i6 >= 500 || (i6 > 640 && i7 > 480) || (i6 > 480 && i7 > 640)) ? 4 : i6 >= 360 ? 3 : 2;
            C0070e c0070e = c0121c.f319l;
            if (c0070e != null) {
                c0070e.m195r(true);
            }
        }
    }

    @Override // android.view.View
    public final boolean onHoverEvent(MotionEvent motionEvent) {
        int actionMasked = motionEvent.getActionMasked();
        if (actionMasked == 9) {
            this.f696q = false;
        }
        if (!this.f696q) {
            boolean onHoverEvent = super.onHoverEvent(motionEvent);
            if (actionMasked == 9 && !onHoverEvent) {
                this.f696q = true;
            }
        }
        if (actionMasked == 10 || actionMasked == 3) {
            this.f696q = false;
        }
        return true;
    }

    @Override // android.view.View
    public final boolean onTouchEvent(MotionEvent motionEvent) {
        int actionMasked = motionEvent.getActionMasked();
        if (actionMasked == 0) {
            this.f695p = false;
        }
        if (!this.f695p) {
            boolean onTouchEvent = super.onTouchEvent(motionEvent);
            if (actionMasked == 0 && !onTouchEvent) {
                this.f695p = true;
            }
        }
        if (actionMasked == 1 || actionMasked == 3) {
            this.f695p = false;
        }
        return true;
    }

    public void setContentHeight(int i6) {
        this.f693n = i6;
        requestLayout();
    }

    @Override // android.view.View
    public void setVisibility(int i6) {
        if (i6 != getVisibility()) {
            C0769s c0769s = this.f694o;
            if (c0769s != null) {
                c0769s.m2208b();
            }
            super.setVisibility(i6);
        }
    }
}
