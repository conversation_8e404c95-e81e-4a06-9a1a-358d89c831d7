package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.drawable.Drawable;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.ContextThemeWrapper;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.TextView;
import androidx.appcompat.view.menu.C0070e;
import androidx.appcompat.view.menu.C0072g;
import androidx.appcompat.view.menu.InterfaceC0074i;
import androidx.appcompat.view.menu.SubMenuC0077l;
import androidx.appcompat.widget.ActionMenuView;
import com.liaoyuan.aicast.R;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.WeakHashMap;
import p008b0.C0385m;
import p021d.AbstractC0676a;
import p028e.C0750a;
import p029e0.C0766p;
import p029e0.C0769s;
import p042g.C0873f;
import p042g.InterfaceC0869b;
import p064j0.AbstractC0956a;

/* loaded from: classes.dex */
public class Toolbar extends ViewGroup {

    /* renamed from: A */
    public int f634A;

    /* renamed from: B */
    public int f635B;

    /* renamed from: C */
    public C0167u0 f636C;

    /* renamed from: D */
    public int f637D;

    /* renamed from: E */
    public int f638E;

    /* renamed from: F */
    public int f639F;

    /* renamed from: G */
    public CharSequence f640G;

    /* renamed from: H */
    public CharSequence f641H;

    /* renamed from: I */
    public ColorStateList f642I;

    /* renamed from: J */
    public ColorStateList f643J;

    /* renamed from: K */
    public boolean f644K;

    /* renamed from: L */
    public boolean f645L;

    /* renamed from: M */
    public final ArrayList<View> f646M;

    /* renamed from: N */
    public final ArrayList<View> f647N;

    /* renamed from: O */
    public final int[] f648O;

    /* renamed from: P */
    public InterfaceC0112f f649P;

    /* renamed from: Q */
    public final C0107a f650Q;

    /* renamed from: R */
    public C0129e1 f651R;

    /* renamed from: S */
    public C0121c f652S;

    /* renamed from: T */
    public C0110d f653T;

    /* renamed from: U */
    public InterfaceC0074i.a f654U;

    /* renamed from: V */
    public C0070e.a f655V;

    /* renamed from: W */
    public boolean f656W;

    /* renamed from: a0 */
    public final RunnableC0108b f657a0;

    /* renamed from: j */
    public ActionMenuView f658j;

    /* renamed from: k */
    public C0119b0 f659k;

    /* renamed from: l */
    public C0119b0 f660l;

    /* renamed from: m */
    public C0148l f661m;

    /* renamed from: n */
    public AppCompatImageView f662n;

    /* renamed from: o */
    public Drawable f663o;

    /* renamed from: p */
    public CharSequence f664p;

    /* renamed from: q */
    public C0148l f665q;

    /* renamed from: r */
    public View f666r;

    /* renamed from: s */
    public Context f667s;

    /* renamed from: t */
    public int f668t;

    /* renamed from: u */
    public int f669u;

    /* renamed from: v */
    public int f670v;

    /* renamed from: w */
    public int f671w;

    /* renamed from: x */
    public int f672x;

    /* renamed from: y */
    public int f673y;

    /* renamed from: z */
    public int f674z;

    /* renamed from: androidx.appcompat.widget.Toolbar$a */
    public class C0107a implements ActionMenuView.InterfaceC0088e {
        public C0107a() {
        }
    }

    /* renamed from: androidx.appcompat.widget.Toolbar$b */
    public class RunnableC0108b implements Runnable {
        public RunnableC0108b() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            Toolbar.this.m306v();
        }
    }

    /* renamed from: androidx.appcompat.widget.Toolbar$c */
    public class ViewOnClickListenerC0109c implements View.OnClickListener {
        public ViewOnClickListenerC0109c() {
        }

        @Override // android.view.View.OnClickListener
        public final void onClick(View view) {
            C0110d c0110d = Toolbar.this.f653T;
            C0072g c0072g = c0110d == null ? null : c0110d.f679k;
            if (c0072g != null) {
                c0072g.collapseActionView();
            }
        }
    }

    /* renamed from: androidx.appcompat.widget.Toolbar$d */
    public class C0110d implements InterfaceC0074i {

        /* renamed from: j */
        public C0070e f678j;

        /* renamed from: k */
        public C0072g f679k;

        public C0110d() {
        }

        @Override // androidx.appcompat.view.menu.InterfaceC0074i
        /* renamed from: a */
        public final void mo152a(C0070e c0070e, boolean z5) {
        }

        @Override // androidx.appcompat.view.menu.InterfaceC0074i
        /* renamed from: c */
        public final boolean mo149c(C0072g c0072g) {
            KeyEvent.Callback callback = Toolbar.this.f666r;
            if (callback instanceof InterfaceC0869b) {
                ((InterfaceC0869b) callback).mo261e();
            }
            Toolbar toolbar = Toolbar.this;
            toolbar.removeView(toolbar.f666r);
            Toolbar toolbar2 = Toolbar.this;
            toolbar2.removeView(toolbar2.f665q);
            Toolbar toolbar3 = Toolbar.this;
            toolbar3.f666r = null;
            int size = toolbar3.f647N.size();
            while (true) {
                size--;
                if (size < 0) {
                    toolbar3.f647N.clear();
                    this.f679k = null;
                    Toolbar.this.requestLayout();
                    c0072g.f405C = false;
                    c0072g.f419n.m195r(false);
                    return true;
                }
                toolbar3.addView(toolbar3.f647N.get(size));
            }
        }

        @Override // androidx.appcompat.view.menu.InterfaceC0074i
        /* renamed from: d */
        public final boolean mo154d() {
            return false;
        }

        @Override // androidx.appcompat.view.menu.InterfaceC0074i
        /* renamed from: e */
        public final void mo171e(Context context, C0070e c0070e) {
            C0072g c0072g;
            C0070e c0070e2 = this.f678j;
            if (c0070e2 != null && (c0072g = this.f679k) != null) {
                c0070e2.mo182e(c0072g);
            }
            this.f678j = c0070e;
        }

        @Override // androidx.appcompat.view.menu.InterfaceC0074i
        /* renamed from: g */
        public final void mo156g() {
            if (this.f679k != null) {
                C0070e c0070e = this.f678j;
                boolean z5 = false;
                if (c0070e != null) {
                    int size = c0070e.size();
                    int i6 = 0;
                    while (true) {
                        if (i6 >= size) {
                            break;
                        }
                        if (this.f678j.getItem(i6) == this.f679k) {
                            z5 = true;
                            break;
                        }
                        i6++;
                    }
                }
                if (z5) {
                    return;
                }
                mo149c(this.f679k);
            }
        }

        @Override // androidx.appcompat.view.menu.InterfaceC0074i
        /* renamed from: i */
        public final boolean mo151i(C0072g c0072g) {
            Toolbar.this.m287c();
            ViewParent parent = Toolbar.this.f665q.getParent();
            Toolbar toolbar = Toolbar.this;
            if (parent != toolbar) {
                if (parent instanceof ViewGroup) {
                    ((ViewGroup) parent).removeView(toolbar.f665q);
                }
                Toolbar toolbar2 = Toolbar.this;
                toolbar2.addView(toolbar2.f665q);
            }
            Toolbar.this.f666r = c0072g.getActionView();
            this.f679k = c0072g;
            ViewParent parent2 = Toolbar.this.f666r.getParent();
            Toolbar toolbar3 = Toolbar.this;
            if (parent2 != toolbar3) {
                if (parent2 instanceof ViewGroup) {
                    ((ViewGroup) parent2).removeView(toolbar3.f666r);
                }
                Objects.requireNonNull(Toolbar.this);
                C0111e c0111e = new C0111e();
                Toolbar toolbar4 = Toolbar.this;
                c0111e.f3555a = 8388611 | (toolbar4.f671w & 112);
                c0111e.f681b = 2;
                toolbar4.f666r.setLayoutParams(c0111e);
                Toolbar toolbar5 = Toolbar.this;
                toolbar5.addView(toolbar5.f666r);
            }
            Toolbar toolbar6 = Toolbar.this;
            int childCount = toolbar6.getChildCount();
            while (true) {
                childCount--;
                if (childCount < 0) {
                    break;
                }
                View childAt = toolbar6.getChildAt(childCount);
                if (((C0111e) childAt.getLayoutParams()).f681b != 2 && childAt != toolbar6.f658j) {
                    toolbar6.removeViewAt(childCount);
                    toolbar6.f647N.add(childAt);
                }
            }
            Toolbar.this.requestLayout();
            c0072g.f405C = true;
            c0072g.f419n.m195r(false);
            KeyEvent.Callback callback = Toolbar.this.f666r;
            if (callback instanceof InterfaceC0869b) {
                ((InterfaceC0869b) callback).mo260d();
            }
            return true;
        }

        @Override // androidx.appcompat.view.menu.InterfaceC0074i
        /* renamed from: j */
        public final boolean mo157j(SubMenuC0077l subMenuC0077l) {
            return false;
        }
    }

    /* renamed from: androidx.appcompat.widget.Toolbar$e */
    public static class C0111e extends AbstractC0676a.a {

        /* renamed from: b */
        public int f681b;

        public C0111e() {
            this.f681b = 0;
            this.f3555a = 8388627;
        }

        public C0111e(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
            this.f681b = 0;
        }

        public C0111e(ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
            this.f681b = 0;
        }

        public C0111e(ViewGroup.MarginLayoutParams marginLayoutParams) {
            super(marginLayoutParams);
            this.f681b = 0;
            ((ViewGroup.MarginLayoutParams) this).leftMargin = marginLayoutParams.leftMargin;
            ((ViewGroup.MarginLayoutParams) this).topMargin = marginLayoutParams.topMargin;
            ((ViewGroup.MarginLayoutParams) this).rightMargin = marginLayoutParams.rightMargin;
            ((ViewGroup.MarginLayoutParams) this).bottomMargin = marginLayoutParams.bottomMargin;
        }

        public C0111e(C0111e c0111e) {
            super((AbstractC0676a.a) c0111e);
            this.f681b = 0;
            this.f681b = c0111e.f681b;
        }

        public C0111e(AbstractC0676a.a aVar) {
            super(aVar);
            this.f681b = 0;
        }
    }

    /* renamed from: androidx.appcompat.widget.Toolbar$f */
    public interface InterfaceC0112f {
    }

    /* renamed from: androidx.appcompat.widget.Toolbar$g */
    public static class C0113g extends AbstractC0956a {
        public static final Parcelable.Creator<C0113g> CREATOR = new a();

        /* renamed from: l */
        public int f682l;

        /* renamed from: m */
        public boolean f683m;

        /* renamed from: androidx.appcompat.widget.Toolbar$g$a */
        public class a implements Parcelable.ClassLoaderCreator<C0113g> {
            @Override // android.os.Parcelable.Creator
            public final Object createFromParcel(Parcel parcel) {
                return new C0113g(parcel, null);
            }

            @Override // android.os.Parcelable.Creator
            public final Object[] newArray(int i6) {
                return new C0113g[i6];
            }

            @Override // android.os.Parcelable.ClassLoaderCreator
            public final C0113g createFromParcel(Parcel parcel, ClassLoader classLoader) {
                return new C0113g(parcel, classLoader);
            }
        }

        public C0113g(Parcel parcel, ClassLoader classLoader) {
            super(parcel, classLoader);
            this.f682l = parcel.readInt();
            this.f683m = parcel.readInt() != 0;
        }

        public C0113g(Parcelable parcelable) {
            super(parcelable);
        }

        @Override // p064j0.AbstractC0956a, android.os.Parcelable
        public final void writeToParcel(Parcel parcel, int i6) {
            parcel.writeParcelable(this.f4731j, i6);
            parcel.writeInt(this.f682l);
            parcel.writeInt(this.f683m ? 1 : 0);
        }
    }

    public Toolbar(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, R.attr.toolbarStyle);
    }

    public Toolbar(Context context, AttributeSet attributeSet, int i6) {
        super(context, attributeSet, R.attr.toolbarStyle);
        this.f639F = 8388627;
        this.f646M = new ArrayList<>();
        this.f647N = new ArrayList<>();
        this.f648O = new int[2];
        this.f650Q = new C0107a();
        this.f657a0 = new RunnableC0108b();
        Context context2 = getContext();
        int[] iArr = C0385m.f2316G;
        C0123c1 m336q = C0123c1.m336q(context2, attributeSet, iArr, R.attr.toolbarStyle);
        C0766p.m2186s(this, context, iArr, attributeSet, m336q.f751b, R.attr.toolbarStyle);
        this.f669u = m336q.m348l(28, 0);
        this.f670v = m336q.m348l(19, 0);
        this.f639F = m336q.f751b.getInteger(0, this.f639F);
        this.f671w = m336q.f751b.getInteger(2, 48);
        int m341e = m336q.m341e(22, 0);
        m341e = m336q.m351o(27) ? m336q.m341e(27, m341e) : m341e;
        this.f635B = m341e;
        this.f634A = m341e;
        this.f674z = m341e;
        this.f673y = m341e;
        int m341e2 = m336q.m341e(25, -1);
        if (m341e2 >= 0) {
            this.f673y = m341e2;
        }
        int m341e3 = m336q.m341e(24, -1);
        if (m341e3 >= 0) {
            this.f674z = m341e3;
        }
        int m341e4 = m336q.m341e(26, -1);
        if (m341e4 >= 0) {
            this.f634A = m341e4;
        }
        int m341e5 = m336q.m341e(23, -1);
        if (m341e5 >= 0) {
            this.f635B = m341e5;
        }
        this.f672x = m336q.m342f(13, -1);
        int m341e6 = m336q.m341e(9, Integer.MIN_VALUE);
        int m341e7 = m336q.m341e(5, Integer.MIN_VALUE);
        int m342f = m336q.m342f(7, 0);
        int m342f2 = m336q.m342f(8, 0);
        m288d();
        C0167u0 c0167u0 = this.f636C;
        c0167u0.f958h = false;
        if (m342f != Integer.MIN_VALUE) {
            c0167u0.f955e = m342f;
            c0167u0.f951a = m342f;
        }
        if (m342f2 != Integer.MIN_VALUE) {
            c0167u0.f956f = m342f2;
            c0167u0.f952b = m342f2;
        }
        if (m341e6 != Integer.MIN_VALUE || m341e7 != Integer.MIN_VALUE) {
            c0167u0.m455a(m341e6, m341e7);
        }
        this.f637D = m336q.m341e(10, Integer.MIN_VALUE);
        this.f638E = m336q.m341e(6, Integer.MIN_VALUE);
        this.f663o = m336q.m343g(4);
        this.f664p = m336q.m350n(3);
        CharSequence m350n = m336q.m350n(21);
        if (!TextUtils.isEmpty(m350n)) {
            setTitle(m350n);
        }
        CharSequence m350n2 = m336q.m350n(18);
        if (!TextUtils.isEmpty(m350n2)) {
            setSubtitle(m350n2);
        }
        this.f667s = getContext();
        setPopupTheme(m336q.m348l(17, 0));
        Drawable m343g = m336q.m343g(16);
        if (m343g != null) {
            setNavigationIcon(m343g);
        }
        CharSequence m350n3 = m336q.m350n(15);
        if (!TextUtils.isEmpty(m350n3)) {
            setNavigationContentDescription(m350n3);
        }
        Drawable m343g2 = m336q.m343g(11);
        if (m343g2 != null) {
            setLogo(m343g2);
        }
        CharSequence m350n4 = m336q.m350n(12);
        if (!TextUtils.isEmpty(m350n4)) {
            setLogoDescription(m350n4);
        }
        if (m336q.m351o(29)) {
            setTitleTextColor(m336q.m339c(29));
        }
        if (m336q.m351o(20)) {
            setSubtitleTextColor(m336q.m339c(20));
        }
        if (m336q.m351o(14)) {
            getMenuInflater().inflate(m336q.m348l(14, 0), getMenu());
        }
        m336q.m352r();
    }

    private MenuInflater getMenuInflater() {
        return new C0873f(getContext());
    }

    /* renamed from: a */
    public final void m285a(List<View> list, int i6) {
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        boolean z5 = getLayoutDirection() == 1;
        int childCount = getChildCount();
        int absoluteGravity = Gravity.getAbsoluteGravity(i6, getLayoutDirection());
        list.clear();
        if (!z5) {
            for (int i7 = 0; i7 < childCount; i7++) {
                View childAt = getChildAt(i7);
                C0111e c0111e = (C0111e) childAt.getLayoutParams();
                if (c0111e.f681b == 0 && m305u(childAt) && m294j(c0111e.f3555a) == absoluteGravity) {
                    list.add(childAt);
                }
            }
            return;
        }
        for (int i8 = childCount - 1; i8 >= 0; i8--) {
            View childAt2 = getChildAt(i8);
            C0111e c0111e2 = (C0111e) childAt2.getLayoutParams();
            if (c0111e2.f681b == 0 && m305u(childAt2) && m294j(c0111e2.f3555a) == absoluteGravity) {
                list.add(childAt2);
            }
        }
    }

    /* renamed from: b */
    public final void m286b(View view, boolean z5) {
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        C0111e generateDefaultLayoutParams = layoutParams == null ? generateDefaultLayoutParams() : !checkLayoutParams(layoutParams) ? generateLayoutParams(layoutParams) : (C0111e) layoutParams;
        generateDefaultLayoutParams.f681b = 1;
        if (!z5 || this.f666r == null) {
            addView(view, generateDefaultLayoutParams);
        } else {
            view.setLayoutParams(generateDefaultLayoutParams);
            this.f647N.add(view);
        }
    }

    /* renamed from: c */
    public final void m287c() {
        if (this.f665q == null) {
            C0148l c0148l = new C0148l(getContext(), null, R.attr.toolbarNavigationButtonStyle);
            this.f665q = c0148l;
            c0148l.setImageDrawable(this.f663o);
            this.f665q.setContentDescription(this.f664p);
            C0111e c0111e = new C0111e();
            c0111e.f3555a = 8388611 | (this.f671w & 112);
            c0111e.f681b = 2;
            this.f665q.setLayoutParams(c0111e);
            this.f665q.setOnClickListener(new ViewOnClickListenerC0109c());
        }
    }

    @Override // android.view.ViewGroup
    public final boolean checkLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return super.checkLayoutParams(layoutParams) && (layoutParams instanceof C0111e);
    }

    /* renamed from: d */
    public final void m288d() {
        if (this.f636C == null) {
            this.f636C = new C0167u0();
        }
    }

    /* renamed from: e */
    public final void m289e() {
        m290f();
        ActionMenuView actionMenuView = this.f658j;
        if (actionMenuView.f534y == null) {
            C0070e c0070e = (C0070e) actionMenuView.getMenu();
            if (this.f653T == null) {
                this.f653T = new C0110d();
            }
            this.f658j.setExpandedActionViewsExclusive(true);
            c0070e.m180c(this.f653T, this.f667s);
        }
    }

    /* renamed from: f */
    public final void m290f() {
        if (this.f658j == null) {
            ActionMenuView actionMenuView = new ActionMenuView(getContext(), null);
            this.f658j = actionMenuView;
            actionMenuView.setPopupTheme(this.f668t);
            this.f658j.setOnMenuItemClickListener(this.f650Q);
            ActionMenuView actionMenuView2 = this.f658j;
            InterfaceC0074i.a aVar = this.f654U;
            C0070e.a aVar2 = this.f655V;
            actionMenuView2.f527D = aVar;
            actionMenuView2.f528E = aVar2;
            C0111e c0111e = new C0111e();
            c0111e.f3555a = 8388613 | (this.f671w & 112);
            this.f658j.setLayoutParams(c0111e);
            m286b(this.f658j, false);
        }
    }

    /* renamed from: g */
    public final void m291g() {
        if (this.f661m == null) {
            this.f661m = new C0148l(getContext(), null, R.attr.toolbarNavigationButtonStyle);
            C0111e c0111e = new C0111e();
            c0111e.f3555a = 8388611 | (this.f671w & 112);
            this.f661m.setLayoutParams(c0111e);
        }
    }

    @Override // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        return new C0111e(getContext(), attributeSet);
    }

    public CharSequence getCollapseContentDescription() {
        C0148l c0148l = this.f665q;
        if (c0148l != null) {
            return c0148l.getContentDescription();
        }
        return null;
    }

    public Drawable getCollapseIcon() {
        C0148l c0148l = this.f665q;
        if (c0148l != null) {
            return c0148l.getDrawable();
        }
        return null;
    }

    public int getContentInsetEnd() {
        C0167u0 c0167u0 = this.f636C;
        if (c0167u0 != null) {
            return c0167u0.f957g ? c0167u0.f951a : c0167u0.f952b;
        }
        return 0;
    }

    public int getContentInsetEndWithActions() {
        int i6 = this.f638E;
        return i6 != Integer.MIN_VALUE ? i6 : getContentInsetEnd();
    }

    public int getContentInsetLeft() {
        C0167u0 c0167u0 = this.f636C;
        if (c0167u0 != null) {
            return c0167u0.f951a;
        }
        return 0;
    }

    public int getContentInsetRight() {
        C0167u0 c0167u0 = this.f636C;
        if (c0167u0 != null) {
            return c0167u0.f952b;
        }
        return 0;
    }

    public int getContentInsetStart() {
        C0167u0 c0167u0 = this.f636C;
        if (c0167u0 != null) {
            return c0167u0.f957g ? c0167u0.f952b : c0167u0.f951a;
        }
        return 0;
    }

    public int getContentInsetStartWithNavigation() {
        int i6 = this.f637D;
        return i6 != Integer.MIN_VALUE ? i6 : getContentInsetStart();
    }

    public int getCurrentContentInsetEnd() {
        C0070e c0070e;
        ActionMenuView actionMenuView = this.f658j;
        return actionMenuView != null && (c0070e = actionMenuView.f534y) != null && c0070e.hasVisibleItems() ? Math.max(getContentInsetEnd(), Math.max(this.f638E, 0)) : getContentInsetEnd();
    }

    public int getCurrentContentInsetLeft() {
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        return getLayoutDirection() == 1 ? getCurrentContentInsetEnd() : getCurrentContentInsetStart();
    }

    public int getCurrentContentInsetRight() {
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        return getLayoutDirection() == 1 ? getCurrentContentInsetStart() : getCurrentContentInsetEnd();
    }

    public int getCurrentContentInsetStart() {
        return getNavigationIcon() != null ? Math.max(getContentInsetStart(), Math.max(this.f637D, 0)) : getContentInsetStart();
    }

    public Drawable getLogo() {
        AppCompatImageView appCompatImageView = this.f662n;
        if (appCompatImageView != null) {
            return appCompatImageView.getDrawable();
        }
        return null;
    }

    public CharSequence getLogoDescription() {
        AppCompatImageView appCompatImageView = this.f662n;
        if (appCompatImageView != null) {
            return appCompatImageView.getContentDescription();
        }
        return null;
    }

    public Menu getMenu() {
        m289e();
        return this.f658j.getMenu();
    }

    public CharSequence getNavigationContentDescription() {
        C0148l c0148l = this.f661m;
        if (c0148l != null) {
            return c0148l.getContentDescription();
        }
        return null;
    }

    public Drawable getNavigationIcon() {
        C0148l c0148l = this.f661m;
        if (c0148l != null) {
            return c0148l.getDrawable();
        }
        return null;
    }

    public C0121c getOuterActionMenuPresenter() {
        return this.f652S;
    }

    public Drawable getOverflowIcon() {
        m289e();
        return this.f658j.getOverflowIcon();
    }

    Context getPopupContext() {
        return this.f667s;
    }

    public int getPopupTheme() {
        return this.f668t;
    }

    public CharSequence getSubtitle() {
        return this.f641H;
    }

    public final TextView getSubtitleTextView() {
        return this.f660l;
    }

    public CharSequence getTitle() {
        return this.f640G;
    }

    public int getTitleMarginBottom() {
        return this.f635B;
    }

    public int getTitleMarginEnd() {
        return this.f674z;
    }

    public int getTitleMarginStart() {
        return this.f673y;
    }

    public int getTitleMarginTop() {
        return this.f634A;
    }

    public final TextView getTitleTextView() {
        return this.f659k;
    }

    public InterfaceC0131f0 getWrapper() {
        if (this.f651R == null) {
            this.f651R = new C0129e1(this, true);
        }
        return this.f651R;
    }

    @Override // android.view.ViewGroup
    /* renamed from: h */
    public final C0111e generateDefaultLayoutParams() {
        return new C0111e();
    }

    @Override // android.view.ViewGroup
    /* renamed from: i */
    public final C0111e generateLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return layoutParams instanceof C0111e ? new C0111e((C0111e) layoutParams) : layoutParams instanceof AbstractC0676a.a ? new C0111e((AbstractC0676a.a) layoutParams) : layoutParams instanceof ViewGroup.MarginLayoutParams ? new C0111e((ViewGroup.MarginLayoutParams) layoutParams) : new C0111e(layoutParams);
    }

    /* renamed from: j */
    public final int m294j(int i6) {
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        int layoutDirection = getLayoutDirection();
        int absoluteGravity = Gravity.getAbsoluteGravity(i6, layoutDirection) & 7;
        return (absoluteGravity == 1 || absoluteGravity == 3 || absoluteGravity == 5) ? absoluteGravity : layoutDirection == 1 ? 5 : 3;
    }

    /* renamed from: k */
    public final int m295k(View view, int i6) {
        C0111e c0111e = (C0111e) view.getLayoutParams();
        int measuredHeight = view.getMeasuredHeight();
        int i7 = i6 > 0 ? (measuredHeight - i6) / 2 : 0;
        int i8 = c0111e.f3555a & 112;
        if (i8 != 16 && i8 != 48 && i8 != 80) {
            i8 = this.f639F & 112;
        }
        if (i8 == 48) {
            return getPaddingTop() - i7;
        }
        if (i8 == 80) {
            return (((getHeight() - getPaddingBottom()) - measuredHeight) - ((ViewGroup.MarginLayoutParams) c0111e).bottomMargin) - i7;
        }
        int paddingTop = getPaddingTop();
        int paddingBottom = getPaddingBottom();
        int height = getHeight();
        int i9 = (((height - paddingTop) - paddingBottom) - measuredHeight) / 2;
        int i10 = ((ViewGroup.MarginLayoutParams) c0111e).topMargin;
        if (i9 < i10) {
            i9 = i10;
        } else {
            int i11 = (((height - paddingBottom) - measuredHeight) - i9) - paddingTop;
            int i12 = ((ViewGroup.MarginLayoutParams) c0111e).bottomMargin;
            if (i11 < i12) {
                i9 = Math.max(0, i9 - (i12 - i11));
            }
        }
        return paddingTop + i9;
    }

    /* renamed from: l */
    public final int m296l(View view) {
        ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
        return marginLayoutParams.getMarginEnd() + marginLayoutParams.getMarginStart();
    }

    /* renamed from: m */
    public final int m297m(View view) {
        ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
        return marginLayoutParams.topMargin + marginLayoutParams.bottomMargin;
    }

    /* renamed from: n */
    public final void m298n(int i6) {
        getMenuInflater().inflate(i6, getMenu());
    }

    /* renamed from: o */
    public final boolean m299o(View view) {
        return view.getParent() == this || this.f647N.contains(view);
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        removeCallbacks(this.f657a0);
    }

    @Override // android.view.View
    public final boolean onHoverEvent(MotionEvent motionEvent) {
        int actionMasked = motionEvent.getActionMasked();
        if (actionMasked == 9) {
            this.f645L = false;
        }
        if (!this.f645L) {
            boolean onHoverEvent = super.onHoverEvent(motionEvent);
            if (actionMasked == 9 && !onHoverEvent) {
                this.f645L = true;
            }
        }
        if (actionMasked == 10 || actionMasked == 3) {
            this.f645L = false;
        }
        return true;
    }

    /* JADX WARN: Removed duplicated region for block: B:114:0x0197  */
    /* JADX WARN: Removed duplicated region for block: B:119:0x012e  */
    /* JADX WARN: Removed duplicated region for block: B:120:0x0127  */
    /* JADX WARN: Removed duplicated region for block: B:121:0x0115  */
    /* JADX WARN: Removed duplicated region for block: B:122:0x00f8  */
    /* JADX WARN: Removed duplicated region for block: B:14:0x005f  */
    /* JADX WARN: Removed duplicated region for block: B:20:0x0074  */
    /* JADX WARN: Removed duplicated region for block: B:26:0x00af  */
    /* JADX WARN: Removed duplicated region for block: B:32:0x00c4  */
    /* JADX WARN: Removed duplicated region for block: B:38:0x00df  */
    /* JADX WARN: Removed duplicated region for block: B:40:0x00fd  */
    /* JADX WARN: Removed duplicated region for block: B:46:0x028a A[LOOP:0: B:45:0x0288->B:46:0x028a, LOOP_END] */
    /* JADX WARN: Removed duplicated region for block: B:50:0x02ac A[LOOP:1: B:49:0x02aa->B:50:0x02ac, LOOP_END] */
    /* JADX WARN: Removed duplicated region for block: B:54:0x02d1 A[LOOP:2: B:53:0x02cf->B:54:0x02d1, LOOP_END] */
    /* JADX WARN: Removed duplicated region for block: B:58:0x0312  */
    /* JADX WARN: Removed duplicated region for block: B:63:0x0326 A[LOOP:3: B:62:0x0324->B:63:0x0326, LOOP_END] */
    /* JADX WARN: Removed duplicated region for block: B:69:0x0124  */
    /* JADX WARN: Removed duplicated region for block: B:71:0x012b  */
    /* JADX WARN: Removed duplicated region for block: B:79:0x015f  */
    /* JADX WARN: Removed duplicated region for block: B:86:0x01a6  */
    /* JADX WARN: Removed duplicated region for block: B:98:0x0213  */
    @Override // android.view.ViewGroup, android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void onLayout(boolean r21, int r22, int r23, int r24, int r25) {
        /*
            Method dump skipped, instructions count: 827
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.Toolbar.onLayout(boolean, int, int, int, int):void");
    }

    /* JADX WARN: Removed duplicated region for block: B:52:0x0284  */
    @Override // android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void onMeasure(int r17, int r18) {
        /*
            Method dump skipped, instructions count: 649
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.Toolbar.onMeasure(int, int):void");
    }

    @Override // android.view.View
    public void onRestoreInstanceState(Parcelable parcelable) {
        MenuItem findItem;
        if (!(parcelable instanceof C0113g)) {
            super.onRestoreInstanceState(parcelable);
            return;
        }
        C0113g c0113g = (C0113g) parcelable;
        super.onRestoreInstanceState(c0113g.f4731j);
        ActionMenuView actionMenuView = this.f658j;
        C0070e c0070e = actionMenuView != null ? actionMenuView.f534y : null;
        int i6 = c0113g.f682l;
        if (i6 != 0 && this.f653T != null && c0070e != null && (findItem = c0070e.findItem(i6)) != null) {
            findItem.expandActionView();
        }
        if (c0113g.f683m) {
            removeCallbacks(this.f657a0);
            post(this.f657a0);
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:14:0x0027, code lost:
    
        if (r1 != Integer.MIN_VALUE) goto L61;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x0035, code lost:
    
        if (r1 != Integer.MIN_VALUE) goto L61;
     */
    @Override // android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void onRtlPropertiesChanged(int r3) {
        /*
            r2 = this;
            super.onRtlPropertiesChanged(r3)
            r2.m288d()
            androidx.appcompat.widget.u0 r0 = r2.f636C
            r1 = 1
            if (r3 != r1) goto Lc
            goto Ld
        Lc:
            r1 = 0
        Ld:
            boolean r3 = r0.f957g
            if (r1 != r3) goto L12
            goto L40
        L12:
            r0.f957g = r1
            boolean r3 = r0.f958h
            if (r3 == 0) goto L38
            r3 = -2147483648(0xffffffff80000000, float:-0.0)
            if (r1 == 0) goto L2a
            int r1 = r0.f954d
            if (r1 == r3) goto L21
            goto L23
        L21:
            int r1 = r0.f955e
        L23:
            r0.f951a = r1
            int r1 = r0.f953c
            if (r1 == r3) goto L3c
            goto L3e
        L2a:
            int r1 = r0.f953c
            if (r1 == r3) goto L2f
            goto L31
        L2f:
            int r1 = r0.f955e
        L31:
            r0.f951a = r1
            int r1 = r0.f954d
            if (r1 == r3) goto L3c
            goto L3e
        L38:
            int r3 = r0.f955e
            r0.f951a = r3
        L3c:
            int r1 = r0.f956f
        L3e:
            r0.f952b = r1
        L40:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.Toolbar.onRtlPropertiesChanged(int):void");
    }

    @Override // android.view.View
    public Parcelable onSaveInstanceState() {
        C0072g c0072g;
        C0113g c0113g = new C0113g(super.onSaveInstanceState());
        C0110d c0110d = this.f653T;
        if (c0110d != null && (c0072g = c0110d.f679k) != null) {
            c0113g.f682l = c0072g.f406a;
        }
        c0113g.f683m = m300p();
        return c0113g;
    }

    @Override // android.view.View
    public final boolean onTouchEvent(MotionEvent motionEvent) {
        int actionMasked = motionEvent.getActionMasked();
        if (actionMasked == 0) {
            this.f644K = false;
        }
        if (!this.f644K) {
            boolean onTouchEvent = super.onTouchEvent(motionEvent);
            if (actionMasked == 0 && !onTouchEvent) {
                this.f644K = true;
            }
        }
        if (actionMasked == 1 || actionMasked == 3) {
            this.f644K = false;
        }
        return true;
    }

    /* renamed from: p */
    public final boolean m300p() {
        ActionMenuView actionMenuView = this.f658j;
        if (actionMenuView != null) {
            C0121c c0121c = actionMenuView.f526C;
            if (c0121c != null && c0121c.m320l()) {
                return true;
            }
        }
        return false;
    }

    /* renamed from: q */
    public final int m301q(View view, int i6, int[] iArr, int i7) {
        C0111e c0111e = (C0111e) view.getLayoutParams();
        int i8 = ((ViewGroup.MarginLayoutParams) c0111e).leftMargin - iArr[0];
        int max = Math.max(0, i8) + i6;
        iArr[0] = Math.max(0, -i8);
        int m295k = m295k(view, i7);
        int measuredWidth = view.getMeasuredWidth();
        view.layout(max, m295k, max + measuredWidth, view.getMeasuredHeight() + m295k);
        return measuredWidth + ((ViewGroup.MarginLayoutParams) c0111e).rightMargin + max;
    }

    /* renamed from: r */
    public final int m302r(View view, int i6, int[] iArr, int i7) {
        C0111e c0111e = (C0111e) view.getLayoutParams();
        int i8 = ((ViewGroup.MarginLayoutParams) c0111e).rightMargin - iArr[1];
        int max = i6 - Math.max(0, i8);
        iArr[1] = Math.max(0, -i8);
        int m295k = m295k(view, i7);
        int measuredWidth = view.getMeasuredWidth();
        view.layout(max - measuredWidth, m295k, max, view.getMeasuredHeight() + m295k);
        return max - (measuredWidth + ((ViewGroup.MarginLayoutParams) c0111e).leftMargin);
    }

    /* renamed from: s */
    public final int m303s(View view, int i6, int i7, int i8, int i9, int[] iArr) {
        ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
        int i10 = marginLayoutParams.leftMargin - iArr[0];
        int i11 = marginLayoutParams.rightMargin - iArr[1];
        int max = Math.max(0, i11) + Math.max(0, i10);
        iArr[0] = Math.max(0, -i10);
        iArr[1] = Math.max(0, -i11);
        view.measure(ViewGroup.getChildMeasureSpec(i6, getPaddingRight() + getPaddingLeft() + max + i7, marginLayoutParams.width), ViewGroup.getChildMeasureSpec(i8, getPaddingBottom() + getPaddingTop() + marginLayoutParams.topMargin + marginLayoutParams.bottomMargin + i9, marginLayoutParams.height));
        return view.getMeasuredWidth() + max;
    }

    public void setCollapseContentDescription(int i6) {
        setCollapseContentDescription(i6 != 0 ? getContext().getText(i6) : null);
    }

    public void setCollapseContentDescription(CharSequence charSequence) {
        if (!TextUtils.isEmpty(charSequence)) {
            m287c();
        }
        C0148l c0148l = this.f665q;
        if (c0148l != null) {
            c0148l.setContentDescription(charSequence);
        }
    }

    public void setCollapseIcon(int i6) {
        setCollapseIcon(C0750a.m2138a(getContext(), i6));
    }

    public void setCollapseIcon(Drawable drawable) {
        if (drawable != null) {
            m287c();
            this.f665q.setImageDrawable(drawable);
        } else {
            C0148l c0148l = this.f665q;
            if (c0148l != null) {
                c0148l.setImageDrawable(this.f663o);
            }
        }
    }

    public void setCollapsible(boolean z5) {
        this.f656W = z5;
        requestLayout();
    }

    public void setContentInsetEndWithActions(int i6) {
        if (i6 < 0) {
            i6 = Integer.MIN_VALUE;
        }
        if (i6 != this.f638E) {
            this.f638E = i6;
            if (getNavigationIcon() != null) {
                requestLayout();
            }
        }
    }

    public void setContentInsetStartWithNavigation(int i6) {
        if (i6 < 0) {
            i6 = Integer.MIN_VALUE;
        }
        if (i6 != this.f637D) {
            this.f637D = i6;
            if (getNavigationIcon() != null) {
                requestLayout();
            }
        }
    }

    public void setLogo(int i6) {
        setLogo(C0750a.m2138a(getContext(), i6));
    }

    public void setLogo(Drawable drawable) {
        if (drawable != null) {
            if (this.f662n == null) {
                this.f662n = new AppCompatImageView(getContext(), null);
            }
            if (!m299o(this.f662n)) {
                m286b(this.f662n, true);
            }
        } else {
            AppCompatImageView appCompatImageView = this.f662n;
            if (appCompatImageView != null && m299o(appCompatImageView)) {
                removeView(this.f662n);
                this.f647N.remove(this.f662n);
            }
        }
        AppCompatImageView appCompatImageView2 = this.f662n;
        if (appCompatImageView2 != null) {
            appCompatImageView2.setImageDrawable(drawable);
        }
    }

    public void setLogoDescription(int i6) {
        setLogoDescription(getContext().getText(i6));
    }

    public void setLogoDescription(CharSequence charSequence) {
        if (!TextUtils.isEmpty(charSequence) && this.f662n == null) {
            this.f662n = new AppCompatImageView(getContext(), null);
        }
        AppCompatImageView appCompatImageView = this.f662n;
        if (appCompatImageView != null) {
            appCompatImageView.setContentDescription(charSequence);
        }
    }

    public void setNavigationContentDescription(int i6) {
        setNavigationContentDescription(i6 != 0 ? getContext().getText(i6) : null);
    }

    public void setNavigationContentDescription(CharSequence charSequence) {
        if (!TextUtils.isEmpty(charSequence)) {
            m291g();
        }
        C0148l c0148l = this.f661m;
        if (c0148l != null) {
            c0148l.setContentDescription(charSequence);
        }
    }

    public void setNavigationIcon(int i6) {
        setNavigationIcon(C0750a.m2138a(getContext(), i6));
    }

    public void setNavigationIcon(Drawable drawable) {
        if (drawable != null) {
            m291g();
            if (!m299o(this.f661m)) {
                m286b(this.f661m, true);
            }
        } else {
            C0148l c0148l = this.f661m;
            if (c0148l != null && m299o(c0148l)) {
                removeView(this.f661m);
                this.f647N.remove(this.f661m);
            }
        }
        C0148l c0148l2 = this.f661m;
        if (c0148l2 != null) {
            c0148l2.setImageDrawable(drawable);
        }
    }

    public void setNavigationOnClickListener(View.OnClickListener onClickListener) {
        m291g();
        this.f661m.setOnClickListener(onClickListener);
    }

    public void setOnMenuItemClickListener(InterfaceC0112f interfaceC0112f) {
        this.f649P = interfaceC0112f;
    }

    public void setOverflowIcon(Drawable drawable) {
        m289e();
        this.f658j.setOverflowIcon(drawable);
    }

    public void setPopupTheme(int i6) {
        if (this.f668t != i6) {
            this.f668t = i6;
            if (i6 == 0) {
                this.f667s = getContext();
            } else {
                this.f667s = new ContextThemeWrapper(getContext(), i6);
            }
        }
    }

    public void setSubtitle(int i6) {
        setSubtitle(getContext().getText(i6));
    }

    public void setSubtitle(CharSequence charSequence) {
        if (TextUtils.isEmpty(charSequence)) {
            C0119b0 c0119b0 = this.f660l;
            if (c0119b0 != null && m299o(c0119b0)) {
                removeView(this.f660l);
                this.f647N.remove(this.f660l);
            }
        } else {
            if (this.f660l == null) {
                Context context = getContext();
                C0119b0 c0119b02 = new C0119b0(context, null);
                this.f660l = c0119b02;
                c0119b02.setSingleLine();
                this.f660l.setEllipsize(TextUtils.TruncateAt.END);
                int i6 = this.f670v;
                if (i6 != 0) {
                    this.f660l.setTextAppearance(context, i6);
                }
                ColorStateList colorStateList = this.f643J;
                if (colorStateList != null) {
                    this.f660l.setTextColor(colorStateList);
                }
            }
            if (!m299o(this.f660l)) {
                m286b(this.f660l, true);
            }
        }
        C0119b0 c0119b03 = this.f660l;
        if (c0119b03 != null) {
            c0119b03.setText(charSequence);
        }
        this.f641H = charSequence;
    }

    public void setSubtitleTextColor(int i6) {
        setSubtitleTextColor(ColorStateList.valueOf(i6));
    }

    public void setSubtitleTextColor(ColorStateList colorStateList) {
        this.f643J = colorStateList;
        C0119b0 c0119b0 = this.f660l;
        if (c0119b0 != null) {
            c0119b0.setTextColor(colorStateList);
        }
    }

    public void setTitle(int i6) {
        setTitle(getContext().getText(i6));
    }

    public void setTitle(CharSequence charSequence) {
        if (TextUtils.isEmpty(charSequence)) {
            C0119b0 c0119b0 = this.f659k;
            if (c0119b0 != null && m299o(c0119b0)) {
                removeView(this.f659k);
                this.f647N.remove(this.f659k);
            }
        } else {
            if (this.f659k == null) {
                Context context = getContext();
                C0119b0 c0119b02 = new C0119b0(context, null);
                this.f659k = c0119b02;
                c0119b02.setSingleLine();
                this.f659k.setEllipsize(TextUtils.TruncateAt.END);
                int i6 = this.f669u;
                if (i6 != 0) {
                    this.f659k.setTextAppearance(context, i6);
                }
                ColorStateList colorStateList = this.f642I;
                if (colorStateList != null) {
                    this.f659k.setTextColor(colorStateList);
                }
            }
            if (!m299o(this.f659k)) {
                m286b(this.f659k, true);
            }
        }
        C0119b0 c0119b03 = this.f659k;
        if (c0119b03 != null) {
            c0119b03.setText(charSequence);
        }
        this.f640G = charSequence;
    }

    public void setTitleMarginBottom(int i6) {
        this.f635B = i6;
        requestLayout();
    }

    public void setTitleMarginEnd(int i6) {
        this.f674z = i6;
        requestLayout();
    }

    public void setTitleMarginStart(int i6) {
        this.f673y = i6;
        requestLayout();
    }

    public void setTitleMarginTop(int i6) {
        this.f634A = i6;
        requestLayout();
    }

    public void setTitleTextColor(int i6) {
        setTitleTextColor(ColorStateList.valueOf(i6));
    }

    public void setTitleTextColor(ColorStateList colorStateList) {
        this.f642I = colorStateList;
        C0119b0 c0119b0 = this.f659k;
        if (c0119b0 != null) {
            c0119b0.setTextColor(colorStateList);
        }
    }

    /* renamed from: t */
    public final void m304t(View view, int i6, int i7, int i8, int i9) {
        ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
        int childMeasureSpec = ViewGroup.getChildMeasureSpec(i6, getPaddingRight() + getPaddingLeft() + marginLayoutParams.leftMargin + marginLayoutParams.rightMargin + i7, marginLayoutParams.width);
        int childMeasureSpec2 = ViewGroup.getChildMeasureSpec(i8, getPaddingBottom() + getPaddingTop() + marginLayoutParams.topMargin + marginLayoutParams.bottomMargin + 0, marginLayoutParams.height);
        int mode = View.MeasureSpec.getMode(childMeasureSpec2);
        if (mode != 1073741824 && i9 >= 0) {
            if (mode != 0) {
                i9 = Math.min(View.MeasureSpec.getSize(childMeasureSpec2), i9);
            }
            childMeasureSpec2 = View.MeasureSpec.makeMeasureSpec(i9, 1073741824);
        }
        view.measure(childMeasureSpec, childMeasureSpec2);
    }

    /* renamed from: u */
    public final boolean m305u(View view) {
        return (view == null || view.getParent() != this || view.getVisibility() == 8) ? false : true;
    }

    /* renamed from: v */
    public final boolean m306v() {
        ActionMenuView actionMenuView = this.f658j;
        if (actionMenuView != null) {
            C0121c c0121c = actionMenuView.f526C;
            if (c0121c != null && c0121c.m321m()) {
                return true;
            }
        }
        return false;
    }
}
