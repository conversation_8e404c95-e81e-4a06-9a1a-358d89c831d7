package androidx.appcompat.widget;

import android.view.View;
import android.view.Window;
import p049h.C0893a;

/* renamed from: androidx.appcompat.widget.d1 */
/* loaded from: classes.dex */
public final class ViewOnClickListenerC0126d1 implements View.OnClickListener {

    /* renamed from: j */
    public final C0893a f758j;

    /* renamed from: k */
    public final /* synthetic */ C0129e1 f759k;

    public ViewOnClickListenerC0126d1(C0129e1 c0129e1) {
        this.f759k = c0129e1;
        this.f758j = new C0893a(c0129e1.f766a.getContext(), c0129e1.f774i);
    }

    @Override // android.view.View.OnClickListener
    public final void onClick(View view) {
        C0129e1 c0129e1 = this.f759k;
        Window.Callback callback = c0129e1.f777l;
        if (callback == null || !c0129e1.f778m) {
            return;
        }
        callback.onMenuItemSelected(0, this.f758j);
    }
}
