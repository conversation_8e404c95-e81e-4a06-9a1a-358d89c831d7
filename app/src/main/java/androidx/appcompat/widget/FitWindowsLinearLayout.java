package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.widget.LinearLayout;

/* loaded from: classes.dex */
public class FitWindowsLinearLayout extends LinearLayout {

    /* renamed from: j */
    public InterfaceC0140i0 f557j;

    public FitWindowsLinearLayout(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
    }

    @Override // android.view.View
    public final boolean fitSystemWindows(Rect rect) {
        InterfaceC0140i0 interfaceC0140i0 = this.f557j;
        if (interfaceC0140i0 != null) {
            interfaceC0140i0.m400a();
        }
        return super.fitSystemWindows(rect);
    }

    public void setOnFitSystemWindowsListener(InterfaceC0140i0 interfaceC0140i0) {
        this.f557j = interfaceC0140i0;
    }
}
