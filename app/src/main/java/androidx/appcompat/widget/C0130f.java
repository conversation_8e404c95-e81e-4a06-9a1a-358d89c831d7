package androidx.appcompat.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.ActionMode;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import android.widget.Button;
import com.liaoyuan.aicast.R;
import java.util.Objects;
import p050h0.C0903d;
import p050h0.InterfaceC0901b;

/* renamed from: androidx.appcompat.widget.f */
/* loaded from: classes.dex */
public class C0130f extends <PERSON><PERSON> implements InterfaceC0901b {

    /* renamed from: j */
    public final C0127e f785j;

    /* renamed from: k */
    public final C0176z f786k;

    public C0130f(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, R.attr.buttonStyle);
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C0130f(Context context, AttributeSet attributeSet, int i6) {
        super(context, attributeSet, i6);
        C0177z0.m514a(context);
        C0173x0.m480a(this, getContext());
        C0127e c0127e = new C0127e(this);
        this.f785j = c0127e;
        c0127e.m356d(attributeSet, i6);
        C0176z c0176z = new C0176z(this);
        this.f786k = c0176z;
        c0176z.m502f(attributeSet, i6);
        c0176z.m499b();
    }

    @Override // android.widget.TextView, android.view.View
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        C0127e c0127e = this.f785j;
        if (c0127e != null) {
            c0127e.m353a();
        }
        C0176z c0176z = this.f786k;
        if (c0176z != null) {
            c0176z.m499b();
        }
    }

    @Override // android.widget.TextView
    public int getAutoSizeMaxTextSize() {
        if (InterfaceC0901b.f4567a) {
            return super.getAutoSizeMaxTextSize();
        }
        C0176z c0176z = this.f786k;
        if (c0176z != null) {
            return Math.round(c0176z.f1026i.f743e);
        }
        return -1;
    }

    @Override // android.widget.TextView
    public int getAutoSizeMinTextSize() {
        if (InterfaceC0901b.f4567a) {
            return super.getAutoSizeMinTextSize();
        }
        C0176z c0176z = this.f786k;
        if (c0176z != null) {
            return Math.round(c0176z.f1026i.f742d);
        }
        return -1;
    }

    @Override // android.widget.TextView
    public int getAutoSizeStepGranularity() {
        if (InterfaceC0901b.f4567a) {
            return super.getAutoSizeStepGranularity();
        }
        C0176z c0176z = this.f786k;
        if (c0176z != null) {
            return Math.round(c0176z.f1026i.f741c);
        }
        return -1;
    }

    @Override // android.widget.TextView
    public int[] getAutoSizeTextAvailableSizes() {
        if (InterfaceC0901b.f4567a) {
            return super.getAutoSizeTextAvailableSizes();
        }
        C0176z c0176z = this.f786k;
        return c0176z != null ? c0176z.f1026i.f744f : new int[0];
    }

    @Override // android.widget.TextView
    @SuppressLint({"WrongConstant"})
    public int getAutoSizeTextType() {
        if (InterfaceC0901b.f4567a) {
            return super.getAutoSizeTextType() == 1 ? 1 : 0;
        }
        C0176z c0176z = this.f786k;
        if (c0176z != null) {
            return c0176z.f1026i.f739a;
        }
        return 0;
    }

    public ColorStateList getSupportBackgroundTintList() {
        C0127e c0127e = this.f785j;
        if (c0127e != null) {
            return c0127e.m354b();
        }
        return null;
    }

    public PorterDuff.Mode getSupportBackgroundTintMode() {
        C0127e c0127e = this.f785j;
        if (c0127e != null) {
            return c0127e.m355c();
        }
        return null;
    }

    public ColorStateList getSupportCompoundDrawablesTintList() {
        C0117a1 c0117a1 = this.f786k.f1025h;
        if (c0117a1 != null) {
            return c0117a1.f703a;
        }
        return null;
    }

    public PorterDuff.Mode getSupportCompoundDrawablesTintMode() {
        C0117a1 c0117a1 = this.f786k.f1025h;
        if (c0117a1 != null) {
            return c0117a1.f704b;
        }
        return null;
    }

    @Override // android.view.View
    public void onInitializeAccessibilityEvent(AccessibilityEvent accessibilityEvent) {
        super.onInitializeAccessibilityEvent(accessibilityEvent);
        accessibilityEvent.setClassName(Button.class.getName());
    }

    @Override // android.view.View
    public void onInitializeAccessibilityNodeInfo(AccessibilityNodeInfo accessibilityNodeInfo) {
        super.onInitializeAccessibilityNodeInfo(accessibilityNodeInfo);
        accessibilityNodeInfo.setClassName(Button.class.getName());
    }

    @Override // android.widget.TextView, android.view.View
    public void onLayout(boolean z5, int i6, int i7, int i8, int i9) {
        super.onLayout(z5, i6, i7, i8, i9);
        C0176z c0176z = this.f786k;
        if (c0176z != null) {
            Objects.requireNonNull(c0176z);
            if (InterfaceC0901b.f4567a) {
                return;
            }
            c0176z.m500c();
        }
    }

    @Override // android.widget.TextView
    public void onTextChanged(CharSequence charSequence, int i6, int i7, int i8) {
        super.onTextChanged(charSequence, i6, i7, i8);
        C0176z c0176z = this.f786k;
        if (c0176z == null || InterfaceC0901b.f4567a || !c0176z.m501e()) {
            return;
        }
        this.f786k.m500c();
    }

    @Override // android.widget.TextView
    public final void setAutoSizeTextTypeUniformWithConfiguration(int i6, int i7, int i8, int i9) {
        if (InterfaceC0901b.f4567a) {
            super.setAutoSizeTextTypeUniformWithConfiguration(i6, i7, i8, i9);
            return;
        }
        C0176z c0176z = this.f786k;
        if (c0176z != null) {
            c0176z.m506j(i6, i7, i8, i9);
        }
    }

    @Override // android.widget.TextView
    public final void setAutoSizeTextTypeUniformWithPresetSizes(int[] iArr, int i6) {
        if (InterfaceC0901b.f4567a) {
            super.setAutoSizeTextTypeUniformWithPresetSizes(iArr, i6);
            return;
        }
        C0176z c0176z = this.f786k;
        if (c0176z != null) {
            c0176z.m507k(iArr, i6);
        }
    }

    @Override // android.widget.TextView
    public void setAutoSizeTextTypeWithDefaults(int i6) {
        if (InterfaceC0901b.f4567a) {
            super.setAutoSizeTextTypeWithDefaults(i6);
            return;
        }
        C0176z c0176z = this.f786k;
        if (c0176z != null) {
            c0176z.m508l(i6);
        }
    }

    @Override // android.view.View
    public void setBackgroundDrawable(Drawable drawable) {
        super.setBackgroundDrawable(drawable);
        C0127e c0127e = this.f785j;
        if (c0127e != null) {
            c0127e.m357e();
        }
    }

    @Override // android.view.View
    public void setBackgroundResource(int i6) {
        super.setBackgroundResource(i6);
        C0127e c0127e = this.f785j;
        if (c0127e != null) {
            c0127e.m358f(i6);
        }
    }

    @Override // android.widget.TextView
    public void setCustomSelectionActionModeCallback(ActionMode.Callback callback) {
        super.setCustomSelectionActionModeCallback(C0903d.m2455f(this, callback));
    }

    public void setSupportAllCaps(boolean z5) {
        C0176z c0176z = this.f786k;
        if (c0176z != null) {
            c0176z.m505i(z5);
        }
    }

    public void setSupportBackgroundTintList(ColorStateList colorStateList) {
        C0127e c0127e = this.f785j;
        if (c0127e != null) {
            c0127e.m360h(colorStateList);
        }
    }

    public void setSupportBackgroundTintMode(PorterDuff.Mode mode) {
        C0127e c0127e = this.f785j;
        if (c0127e != null) {
            c0127e.m361i(mode);
        }
    }

    public void setSupportCompoundDrawablesTintList(ColorStateList colorStateList) {
        this.f786k.m509m(colorStateList);
        this.f786k.m499b();
    }

    public void setSupportCompoundDrawablesTintMode(PorterDuff.Mode mode) {
        this.f786k.m510n(mode);
        this.f786k.m499b();
    }

    @Override // android.widget.TextView
    public final void setTextAppearance(Context context, int i6) {
        super.setTextAppearance(context, i6);
        C0176z c0176z = this.f786k;
        if (c0176z != null) {
            c0176z.m503g(context, i6);
        }
    }

    @Override // android.widget.TextView
    public final void setTextSize(int i6, float f6) {
        boolean z5 = InterfaceC0901b.f4567a;
        if (z5) {
            super.setTextSize(i6, f6);
            return;
        }
        C0176z c0176z = this.f786k;
        if (c0176z != null) {
            Objects.requireNonNull(c0176z);
            if (z5 || c0176z.m501e()) {
                return;
            }
            c0176z.f1026i.m328f(i6, f6);
        }
    }
}
