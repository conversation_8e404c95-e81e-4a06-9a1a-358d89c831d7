package androidx.appcompat.widget;

import android.app.Activity;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.text.Editable;
import android.text.Selection;
import android.text.Spannable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.ActionMode;
import android.view.DragEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputConnection;
import android.view.textclassifier.TextClassifier;
import android.widget.EditText;
import com.liaoyuan.aicast.R;
import java.util.WeakHashMap;
import p008b0.C0385m;
import p029e0.C0753c;
import p029e0.C0766p;
import p029e0.C0769s;
import p029e0.InterfaceC0764n;
import p043g0.C0876a;
import p043g0.C0877b;
import p043g0.C0878c;
import p050h0.C0903d;
import p050h0.C0904e;

/* renamed from: androidx.appcompat.widget.k */
/* loaded from: classes.dex */
public class C0145k extends EditText implements InterfaceC0764n {

    /* renamed from: j */
    public final C0127e f856j;

    /* renamed from: k */
    public final C0176z f857k;

    /* renamed from: l */
    public final C0172x f858l;

    /* renamed from: m */
    public final C0904e f859m;

    public C0145k(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, R.attr.editTextStyle);
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C0145k(Context context, AttributeSet attributeSet, int i6) {
        super(context, attributeSet, R.attr.editTextStyle);
        C0177z0.m514a(context);
        C0173x0.m480a(this, getContext());
        C0127e c0127e = new C0127e(this);
        this.f856j = c0127e;
        c0127e.m356d(attributeSet, R.attr.editTextStyle);
        C0176z c0176z = new C0176z(this);
        this.f857k = c0176z;
        c0176z.m502f(attributeSet, R.attr.editTextStyle);
        c0176z.m499b();
        this.f858l = new C0172x(this);
        this.f859m = new C0904e();
    }

    @Override // p029e0.InterfaceC0764n
    /* renamed from: a */
    public final C0753c mo415a(C0753c c0753c) {
        return this.f859m.mo2165a(this, c0753c);
    }

    @Override // android.widget.TextView, android.view.View
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        C0127e c0127e = this.f856j;
        if (c0127e != null) {
            c0127e.m353a();
        }
        C0176z c0176z = this.f857k;
        if (c0176z != null) {
            c0176z.m499b();
        }
    }

    public ColorStateList getSupportBackgroundTintList() {
        C0127e c0127e = this.f856j;
        if (c0127e != null) {
            return c0127e.m354b();
        }
        return null;
    }

    public PorterDuff.Mode getSupportBackgroundTintMode() {
        C0127e c0127e = this.f856j;
        if (c0127e != null) {
            return c0127e.m355c();
        }
        return null;
    }

    @Override // android.widget.EditText, android.widget.TextView
    public Editable getText() {
        return Build.VERSION.SDK_INT >= 28 ? super.getText() : super.getEditableText();
    }

    @Override // android.widget.TextView
    public TextClassifier getTextClassifier() {
        C0172x c0172x;
        return (Build.VERSION.SDK_INT >= 28 || (c0172x = this.f858l) == null) ? super.getTextClassifier() : c0172x.m479a();
    }

    @Override // android.widget.TextView, android.view.View
    public InputConnection onCreateInputConnection(EditorInfo editorInfo) {
        InputConnection c0878c;
        InputConnection onCreateInputConnection = super.onCreateInputConnection(editorInfo);
        this.f857k.m504h(this, onCreateInputConnection, editorInfo);
        C0385m.m1422n(onCreateInputConnection, editorInfo, this);
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        String[] strArr = (String[]) getTag(R.id.tag_on_receive_content_mime_types);
        if (onCreateInputConnection == null || strArr == null) {
            return onCreateInputConnection;
        }
        C0876a.m2419c(editorInfo, strArr);
        C0162s c0162s = new C0162s(this);
        if (Build.VERSION.SDK_INT >= 25) {
            c0878c = new C0877b(onCreateInputConnection, c0162s);
        } else {
            if (C0876a.m2417a(editorInfo).length == 0) {
                return onCreateInputConnection;
            }
            c0878c = new C0878c(onCreateInputConnection, c0162s);
        }
        return c0878c;
    }

    @Override // android.widget.TextView, android.view.View
    public final boolean onDragEvent(DragEvent dragEvent) {
        Activity activity;
        boolean z5 = false;
        if (dragEvent.getLocalState() == null) {
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            if (((String[]) getTag(R.id.tag_on_receive_content_mime_types)) != null) {
                Context context = getContext();
                while (true) {
                    if (!(context instanceof ContextWrapper)) {
                        activity = null;
                        break;
                    }
                    if (context instanceof Activity) {
                        activity = (Activity) context;
                        break;
                    }
                    context = ((ContextWrapper) context).getBaseContext();
                }
                if (activity == null) {
                    Log.i("ReceiveContent", "Can't handle drop: no activity: view=" + this);
                } else if (dragEvent.getAction() != 1 && dragEvent.getAction() == 3) {
                    activity.requestDragAndDropPermissions(dragEvent);
                    int offsetForPosition = getOffsetForPosition(dragEvent.getX(), dragEvent.getY());
                    beginBatchEdit();
                    try {
                        Selection.setSelection((Spannable) getText(), offsetForPosition);
                        C0766p.m2182o(this, new C0753c(new C0753c.a(dragEvent.getClipData(), 3)));
                        endBatchEdit();
                        z5 = true;
                    } catch (Throwable th) {
                        endBatchEdit();
                        throw th;
                    }
                }
            }
        }
        if (z5) {
            return true;
        }
        return super.onDragEvent(dragEvent);
    }

    @Override // android.widget.EditText, android.widget.TextView
    public final boolean onTextContextMenuItem(int i6) {
        if (i6 == 16908322 || i6 == 16908337) {
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            if (((String[]) getTag(R.id.tag_on_receive_content_mime_types)) != null) {
                ClipboardManager clipboardManager = (ClipboardManager) getContext().getSystemService("clipboard");
                ClipData primaryClip = clipboardManager == null ? null : clipboardManager.getPrimaryClip();
                if (primaryClip != null && primaryClip.getItemCount() > 0) {
                    C0753c.a aVar = new C0753c.a(primaryClip, 1);
                    aVar.f4023c = i6 != 16908322 ? 1 : 0;
                    C0766p.m2182o(this, new C0753c(aVar));
                }
                r0 = 1;
            }
        }
        if (r0 != 0) {
            return true;
        }
        return super.onTextContextMenuItem(i6);
    }

    @Override // android.view.View
    public void setBackgroundDrawable(Drawable drawable) {
        super.setBackgroundDrawable(drawable);
        C0127e c0127e = this.f856j;
        if (c0127e != null) {
            c0127e.m357e();
        }
    }

    @Override // android.view.View
    public void setBackgroundResource(int i6) {
        super.setBackgroundResource(i6);
        C0127e c0127e = this.f856j;
        if (c0127e != null) {
            c0127e.m358f(i6);
        }
    }

    @Override // android.widget.TextView
    public void setCustomSelectionActionModeCallback(ActionMode.Callback callback) {
        super.setCustomSelectionActionModeCallback(C0903d.m2455f(this, callback));
    }

    public void setSupportBackgroundTintList(ColorStateList colorStateList) {
        C0127e c0127e = this.f856j;
        if (c0127e != null) {
            c0127e.m360h(colorStateList);
        }
    }

    public void setSupportBackgroundTintMode(PorterDuff.Mode mode) {
        C0127e c0127e = this.f856j;
        if (c0127e != null) {
            c0127e.m361i(mode);
        }
    }

    @Override // android.widget.TextView
    public final void setTextAppearance(Context context, int i6) {
        super.setTextAppearance(context, i6);
        C0176z c0176z = this.f857k;
        if (c0176z != null) {
            c0176z.m503g(context, i6);
        }
    }

    @Override // android.widget.TextView
    public void setTextClassifier(TextClassifier textClassifier) {
        C0172x c0172x;
        if (Build.VERSION.SDK_INT >= 28 || (c0172x = this.f858l) == null) {
            super.setTextClassifier(textClassifier);
        } else {
            c0172x.f1009b = textClassifier;
        }
    }
}
