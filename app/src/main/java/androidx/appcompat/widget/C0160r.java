package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.Bitmap;
import android.util.AttributeSet;
import android.view.View;
import android.widget.RatingBar;
import com.liaoyuan.aicast.R;

/* renamed from: androidx.appcompat.widget.r */
/* loaded from: classes.dex */
public final class C0160r extends RatingBar {

    /* renamed from: j */
    public final C0156p f929j;

    public C0160r(Context context, AttributeSet attributeSet) {
        super(context, attributeSet, R.attr.ratingBarStyle);
        C0173x0.m480a(this, getContext());
        C0156p c0156p = new C0156p(this);
        this.f929j = c0156p;
        c0156p.mo438a(attributeSet, R.attr.ratingBarStyle);
    }

    @Override // android.widget.RatingBar, android.widget.AbsSeekBar, android.widget.ProgressBar, android.view.View
    public final synchronized void onMeasure(int i6, int i7) {
        super.onMeasure(i6, i7);
        Bitmap bitmap = this.f929j.f923b;
        if (bitmap != null) {
            setMeasuredDimension(View.resolveSizeAndState(bitmap.getWidth() * getNumStars(), i6, 0), getMeasuredHeight());
        }
    }
}
