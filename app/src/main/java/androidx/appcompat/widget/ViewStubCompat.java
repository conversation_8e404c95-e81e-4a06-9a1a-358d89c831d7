package androidx.appcompat.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import java.lang.ref.WeakReference;
import p008b0.C0385m;

/* loaded from: classes.dex */
public final class ViewStubCompat extends View {

    /* renamed from: j */
    public int f684j;

    /* renamed from: k */
    public int f685k;

    /* renamed from: l */
    public WeakReference<View> f686l;

    /* renamed from: m */
    public LayoutInflater f687m;

    /* renamed from: n */
    public InterfaceC0114a f688n;

    /* renamed from: androidx.appcompat.widget.ViewStubCompat$a */
    public interface InterfaceC0114a {
        /* renamed from: a */
        void m308a();
    }

    public ViewStubCompat(Context context, AttributeSet attributeSet) {
        super(context, attributeSet, 0);
        this.f684j = 0;
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C0385m.f2319J, 0, 0);
        this.f685k = obtainStyledAttributes.getResourceId(2, -1);
        this.f684j = obtainStyledAttributes.getResourceId(1, 0);
        setId(obtainStyledAttributes.getResourceId(0, -1));
        obtainStyledAttributes.recycle();
        setVisibility(8);
        setWillNotDraw(true);
    }

    /* renamed from: a */
    public final View m307a() {
        ViewParent parent = getParent();
        if (!(parent instanceof ViewGroup)) {
            throw new IllegalStateException("ViewStub must have a non-null ViewGroup viewParent");
        }
        if (this.f684j == 0) {
            throw new IllegalArgumentException("ViewStub must have a valid layoutResource");
        }
        ViewGroup viewGroup = (ViewGroup) parent;
        LayoutInflater layoutInflater = this.f687m;
        if (layoutInflater == null) {
            layoutInflater = LayoutInflater.from(getContext());
        }
        View inflate = layoutInflater.inflate(this.f684j, viewGroup, false);
        int i6 = this.f685k;
        if (i6 != -1) {
            inflate.setId(i6);
        }
        int indexOfChild = viewGroup.indexOfChild(this);
        viewGroup.removeViewInLayout(this);
        ViewGroup.LayoutParams layoutParams = getLayoutParams();
        if (layoutParams != null) {
            viewGroup.addView(inflate, indexOfChild, layoutParams);
        } else {
            viewGroup.addView(inflate, indexOfChild);
        }
        this.f686l = new WeakReference<>(inflate);
        InterfaceC0114a interfaceC0114a = this.f688n;
        if (interfaceC0114a != null) {
            interfaceC0114a.m308a();
        }
        return inflate;
    }

    @Override // android.view.View
    public final void dispatchDraw(Canvas canvas) {
    }

    @Override // android.view.View
    @SuppressLint({"MissingSuperCall"})
    public final void draw(Canvas canvas) {
    }

    public int getInflatedId() {
        return this.f685k;
    }

    public LayoutInflater getLayoutInflater() {
        return this.f687m;
    }

    public int getLayoutResource() {
        return this.f684j;
    }

    @Override // android.view.View
    public final void onMeasure(int i6, int i7) {
        setMeasuredDimension(0, 0);
    }

    public void setInflatedId(int i6) {
        this.f685k = i6;
    }

    public void setLayoutInflater(LayoutInflater layoutInflater) {
        this.f687m = layoutInflater;
    }

    public void setLayoutResource(int i6) {
        this.f684j = i6;
    }

    public void setOnInflateListener(InterfaceC0114a interfaceC0114a) {
        this.f688n = interfaceC0114a;
    }

    @Override // android.view.View
    public void setVisibility(int i6) {
        WeakReference<View> weakReference = this.f686l;
        if (weakReference != null) {
            View view = weakReference.get();
            if (view == null) {
                throw new IllegalStateException("setVisibility called on un-referenced view");
            }
            view.setVisibility(i6);
            return;
        }
        super.setVisibility(i6);
        if (i6 == 0 || i6 == 4) {
            m307a();
        }
    }
}
