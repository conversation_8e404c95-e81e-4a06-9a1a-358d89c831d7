package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.Window;
import androidx.appcompat.view.menu.C0070e;
import androidx.appcompat.view.menu.C0072g;
import androidx.appcompat.view.menu.InterfaceC0074i;
import androidx.appcompat.widget.Toolbar;
import androidx.appcompat.widget.Toolbar.C0110d;
import com.liaoyuan.aicast.R;
import p008b0.C0385m;
import p028e.C0750a;
import p029e0.C0766p;
import p029e0.C0769s;
import p153w3.C1798e;

/* renamed from: androidx.appcompat.widget.e1 */
/* loaded from: classes.dex */
public final class C0129e1 implements InterfaceC0131f0 {

    /* renamed from: a */
    public Toolbar f766a;

    /* renamed from: b */
    public int f767b;

    /* renamed from: c */
    public C0169v0 f768c;

    /* renamed from: d */
    public View f769d;

    /* renamed from: e */
    public Drawable f770e;

    /* renamed from: f */
    public Drawable f771f;

    /* renamed from: g */
    public Drawable f772g;

    /* renamed from: h */
    public boolean f773h;

    /* renamed from: i */
    public CharSequence f774i;

    /* renamed from: j */
    public CharSequence f775j;

    /* renamed from: k */
    public CharSequence f776k;

    /* renamed from: l */
    public Window.Callback f777l;

    /* renamed from: m */
    public boolean f778m;

    /* renamed from: n */
    public C0121c f779n;

    /* renamed from: o */
    public int f780o;

    /* renamed from: p */
    public Drawable f781p;

    /* renamed from: androidx.appcompat.widget.e1$a */
    public class a extends C1798e {

        /* renamed from: O */
        public boolean f782O = false;

        /* renamed from: P */
        public final /* synthetic */ int f783P;

        public a(int i6) {
            this.f783P = i6;
        }

        @Override // p029e0.InterfaceC0770t
        /* renamed from: b */
        public final void mo314b() {
            if (this.f782O) {
                return;
            }
            C0129e1.this.f766a.setVisibility(this.f783P);
        }

        @Override // p153w3.C1798e, p029e0.InterfaceC0770t
        /* renamed from: f */
        public final void mo315f(View view) {
            this.f782O = true;
        }

        @Override // p153w3.C1798e, p029e0.InterfaceC0770t
        /* renamed from: g */
        public final void mo316g() {
            C0129e1.this.f766a.setVisibility(0);
        }
    }

    public C0129e1(Toolbar toolbar, boolean z5) {
        Drawable drawable;
        this.f780o = 0;
        this.f766a = toolbar;
        this.f774i = toolbar.getTitle();
        this.f775j = toolbar.getSubtitle();
        this.f773h = this.f774i != null;
        this.f772g = toolbar.getNavigationIcon();
        C0123c1 m336q = C0123c1.m336q(toolbar.getContext(), null, C0385m.f2347k, R.attr.actionBarStyle);
        int i6 = 15;
        this.f781p = m336q.m343g(15);
        if (z5) {
            CharSequence m350n = m336q.m350n(27);
            if (!TextUtils.isEmpty(m350n)) {
                setTitle(m350n);
            }
            CharSequence m350n2 = m336q.m350n(25);
            if (!TextUtils.isEmpty(m350n2)) {
                this.f775j = m350n2;
                if ((this.f767b & 8) != 0) {
                    this.f766a.setSubtitle(m350n2);
                }
            }
            Drawable m343g = m336q.m343g(20);
            if (m343g != null) {
                this.f771f = m343g;
                m386y();
            }
            Drawable m343g2 = m336q.m343g(17);
            if (m343g2 != null) {
                setIcon(m343g2);
            }
            if (this.f772g == null && (drawable = this.f781p) != null) {
                this.f772g = drawable;
                m385x();
            }
            mo383v(m336q.m346j(10, 0));
            int m348l = m336q.m348l(9, 0);
            if (m348l != 0) {
                View inflate = LayoutInflater.from(this.f766a.getContext()).inflate(m348l, (ViewGroup) this.f766a, false);
                View view = this.f769d;
                if (view != null && (this.f767b & 16) != 0) {
                    this.f766a.removeView(view);
                }
                this.f769d = inflate;
                if (inflate != null && (this.f767b & 16) != 0) {
                    this.f766a.addView(inflate);
                }
                mo383v(this.f767b | 16);
            }
            int m347k = m336q.m347k(13, 0);
            if (m347k > 0) {
                ViewGroup.LayoutParams layoutParams = this.f766a.getLayoutParams();
                layoutParams.height = m347k;
                this.f766a.setLayoutParams(layoutParams);
            }
            int m341e = m336q.m341e(7, -1);
            int m341e2 = m336q.m341e(3, -1);
            if (m341e >= 0 || m341e2 >= 0) {
                Toolbar toolbar2 = this.f766a;
                int max = Math.max(m341e, 0);
                int max2 = Math.max(m341e2, 0);
                toolbar2.m288d();
                toolbar2.f636C.m455a(max, max2);
            }
            int m348l2 = m336q.m348l(28, 0);
            if (m348l2 != 0) {
                Toolbar toolbar3 = this.f766a;
                Context context = toolbar3.getContext();
                toolbar3.f669u = m348l2;
                C0119b0 c0119b0 = toolbar3.f659k;
                if (c0119b0 != null) {
                    c0119b0.setTextAppearance(context, m348l2);
                }
            }
            int m348l3 = m336q.m348l(26, 0);
            if (m348l3 != 0) {
                Toolbar toolbar4 = this.f766a;
                Context context2 = toolbar4.getContext();
                toolbar4.f670v = m348l3;
                C0119b0 c0119b02 = toolbar4.f660l;
                if (c0119b02 != null) {
                    c0119b02.setTextAppearance(context2, m348l3);
                }
            }
            int m348l4 = m336q.m348l(22, 0);
            if (m348l4 != 0) {
                this.f766a.setPopupTheme(m348l4);
            }
        } else {
            if (this.f766a.getNavigationIcon() != null) {
                this.f781p = this.f766a.getNavigationIcon();
            } else {
                i6 = 11;
            }
            this.f767b = i6;
        }
        m336q.m352r();
        if (R.string.abc_action_bar_up_description != this.f780o) {
            this.f780o = R.string.abc_action_bar_up_description;
            if (TextUtils.isEmpty(this.f766a.getNavigationContentDescription())) {
                int i7 = this.f780o;
                this.f776k = i7 != 0 ? mo376o().getString(i7) : null;
                m384w();
            }
        }
        this.f776k = this.f766a.getNavigationContentDescription();
        this.f766a.setNavigationOnClickListener(new ViewOnClickListenerC0126d1(this));
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: a */
    public final void mo362a(Menu menu, InterfaceC0074i.a aVar) {
        C0072g c0072g;
        if (this.f779n == null) {
            this.f779n = new C0121c(this.f766a.getContext());
        }
        C0121c c0121c = this.f779n;
        c0121c.f321n = aVar;
        Toolbar toolbar = this.f766a;
        C0070e c0070e = (C0070e) menu;
        if (c0070e == null && toolbar.f658j == null) {
            return;
        }
        toolbar.m290f();
        C0070e c0070e2 = toolbar.f658j.f534y;
        if (c0070e2 == c0070e) {
            return;
        }
        if (c0070e2 != null) {
            c0070e2.m199v(toolbar.f652S);
            c0070e2.m199v(toolbar.f653T);
        }
        if (toolbar.f653T == null) {
            toolbar.f653T = toolbar.new C0110d();
        }
        c0121c.f727z = true;
        if (c0070e != null) {
            c0070e.m180c(c0121c, toolbar.f667s);
            c0070e.m180c(toolbar.f653T, toolbar.f667s);
        } else {
            c0121c.mo171e(toolbar.f667s, null);
            Toolbar.C0110d c0110d = toolbar.f653T;
            C0070e c0070e3 = c0110d.f678j;
            if (c0070e3 != null && (c0072g = c0110d.f679k) != null) {
                c0070e3.mo182e(c0072g);
            }
            c0110d.f678j = null;
            c0121c.mo156g();
            toolbar.f653T.mo156g();
        }
        toolbar.f658j.setPopupTheme(toolbar.f668t);
        toolbar.f658j.setPresenter(c0121c);
        toolbar.f652S = c0121c;
    }

    /* JADX WARN: Removed duplicated region for block: B:13:? A[RETURN, SYNTHETIC] */
    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: b */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean mo363b() {
        /*
            r4 = this;
            androidx.appcompat.widget.Toolbar r0 = r4.f766a
            androidx.appcompat.widget.ActionMenuView r0 = r0.f658j
            r1 = 1
            r2 = 0
            if (r0 == 0) goto L22
            androidx.appcompat.widget.c r0 = r0.f526C
            if (r0 == 0) goto L1e
            androidx.appcompat.widget.c$c r3 = r0.f716D
            if (r3 != 0) goto L19
            boolean r0 = r0.m320l()
            if (r0 == 0) goto L17
            goto L19
        L17:
            r0 = r2
            goto L1a
        L19:
            r0 = r1
        L1a:
            if (r0 == 0) goto L1e
            r0 = r1
            goto L1f
        L1e:
            r0 = r2
        L1f:
            if (r0 == 0) goto L22
            goto L23
        L22:
            r1 = r2
        L23:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.C0129e1.mo363b():boolean");
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: c */
    public final boolean mo364c() {
        return this.f766a.m300p();
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    public final void collapseActionView() {
        Toolbar.C0110d c0110d = this.f766a.f653T;
        C0072g c0072g = c0110d == null ? null : c0110d.f679k;
        if (c0072g != null) {
            c0072g.collapseActionView();
        }
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: d */
    public final boolean mo365d() {
        ActionMenuView actionMenuView = this.f766a.f658j;
        if (actionMenuView != null) {
            C0121c c0121c = actionMenuView.f526C;
            if (c0121c != null && c0121c.m319k()) {
                return true;
            }
        }
        return false;
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: e */
    public final boolean mo366e() {
        return this.f766a.m306v();
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: f */
    public final void mo367f() {
        this.f778m = true;
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: g */
    public final boolean mo368g() {
        ActionMenuView actionMenuView;
        Toolbar toolbar = this.f766a;
        return toolbar.getVisibility() == 0 && (actionMenuView = toolbar.f658j) != null && actionMenuView.f525B;
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    public final CharSequence getTitle() {
        return this.f766a.getTitle();
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: h */
    public final void mo369h() {
        C0121c c0121c;
        ActionMenuView actionMenuView = this.f766a.f658j;
        if (actionMenuView == null || (c0121c = actionMenuView.f526C) == null) {
            return;
        }
        c0121c.m317b();
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: i */
    public final void mo370i() {
        C0169v0 c0169v0 = this.f768c;
        if (c0169v0 != null) {
            ViewParent parent = c0169v0.getParent();
            Toolbar toolbar = this.f766a;
            if (parent == toolbar) {
                toolbar.removeView(this.f768c);
            }
        }
        this.f768c = null;
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: j */
    public final int mo371j() {
        return this.f767b;
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: k */
    public final void mo372k(int i6) {
        this.f766a.setVisibility(i6);
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: l */
    public final void mo373l(int i6) {
        this.f771f = i6 != 0 ? C0750a.m2138a(mo376o(), i6) : null;
        m386y();
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: m */
    public final void mo374m() {
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: n */
    public final ViewGroup mo375n() {
        return this.f766a;
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: o */
    public final Context mo376o() {
        return this.f766a.getContext();
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: p */
    public final void mo377p() {
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: q */
    public final C0769s mo378q(int i6, long j6) {
        C0769s m2169b = C0766p.m2169b(this.f766a);
        m2169b.m2207a(i6 == 0 ? 1.0f : 0.0f);
        m2169b.m2209c(j6);
        m2169b.m2210d(new a(i6));
        return m2169b;
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: r */
    public final void mo379r() {
        Log.i("ToolbarWidgetWrapper", "Progress display unsupported");
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: s */
    public final boolean mo380s() {
        Toolbar.C0110d c0110d = this.f766a.f653T;
        return (c0110d == null || c0110d.f679k == null) ? false : true;
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    public final void setIcon(int i6) {
        setIcon(i6 != 0 ? C0750a.m2138a(mo376o(), i6) : null);
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    public final void setIcon(Drawable drawable) {
        this.f770e = drawable;
        m386y();
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    public final void setTitle(CharSequence charSequence) {
        this.f773h = true;
        this.f774i = charSequence;
        if ((this.f767b & 8) != 0) {
            this.f766a.setTitle(charSequence);
        }
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    public final void setWindowCallback(Window.Callback callback) {
        this.f777l = callback;
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    public final void setWindowTitle(CharSequence charSequence) {
        if (this.f773h) {
            return;
        }
        this.f774i = charSequence;
        if ((this.f767b & 8) != 0) {
            this.f766a.setTitle(charSequence);
        }
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: t */
    public final void mo381t() {
        Log.i("ToolbarWidgetWrapper", "Progress display unsupported");
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: u */
    public final void mo382u(boolean z5) {
        this.f766a.setCollapsible(z5);
    }

    @Override // androidx.appcompat.widget.InterfaceC0131f0
    /* renamed from: v */
    public final void mo383v(int i6) {
        View view;
        CharSequence charSequence;
        Toolbar toolbar;
        int i7 = this.f767b ^ i6;
        this.f767b = i6;
        if (i7 != 0) {
            if ((i7 & 4) != 0) {
                if ((i6 & 4) != 0) {
                    m384w();
                }
                m385x();
            }
            if ((i7 & 3) != 0) {
                m386y();
            }
            if ((i7 & 8) != 0) {
                if ((i6 & 8) != 0) {
                    this.f766a.setTitle(this.f774i);
                    toolbar = this.f766a;
                    charSequence = this.f775j;
                } else {
                    charSequence = null;
                    this.f766a.setTitle((CharSequence) null);
                    toolbar = this.f766a;
                }
                toolbar.setSubtitle(charSequence);
            }
            if ((i7 & 16) == 0 || (view = this.f769d) == null) {
                return;
            }
            if ((i6 & 16) != 0) {
                this.f766a.addView(view);
            } else {
                this.f766a.removeView(view);
            }
        }
    }

    /* renamed from: w */
    public final void m384w() {
        if ((this.f767b & 4) != 0) {
            if (TextUtils.isEmpty(this.f776k)) {
                this.f766a.setNavigationContentDescription(this.f780o);
            } else {
                this.f766a.setNavigationContentDescription(this.f776k);
            }
        }
    }

    /* renamed from: x */
    public final void m385x() {
        Toolbar toolbar;
        Drawable drawable;
        if ((this.f767b & 4) != 0) {
            toolbar = this.f766a;
            drawable = this.f772g;
            if (drawable == null) {
                drawable = this.f781p;
            }
        } else {
            toolbar = this.f766a;
            drawable = null;
        }
        toolbar.setNavigationIcon(drawable);
    }

    /* renamed from: y */
    public final void m386y() {
        Drawable drawable;
        int i6 = this.f767b;
        if ((i6 & 2) == 0) {
            drawable = null;
        } else if ((i6 & 1) == 0 || (drawable = this.f771f) == null) {
            drawable = this.f770e;
        }
        this.f766a.setLogo(drawable);
    }
}
