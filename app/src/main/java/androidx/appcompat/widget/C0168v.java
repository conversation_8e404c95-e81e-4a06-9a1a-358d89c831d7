package androidx.appcompat.widget;

import android.annotation.SuppressLint;
import android.view.View;
import androidx.appcompat.widget.C0170w;
import p049h.InterfaceC0898f;

/* renamed from: androidx.appcompat.widget.v */
/* loaded from: classes.dex */
public final class C0168v extends AbstractViewOnTouchListenerC0143j0 {

    /* renamed from: s */
    public final /* synthetic */ C0170w.d f959s;

    /* renamed from: t */
    public final /* synthetic */ C0170w f960t;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C0168v(C0170w c0170w, View view, C0170w.d dVar) {
        super(view);
        this.f960t = c0170w;
        this.f959s = dVar;
    }

    @Override // androidx.appcompat.widget.AbstractViewOnTouchListenerC0143j0
    /* renamed from: b */
    public final InterfaceC0898f mo143b() {
        return this.f959s;
    }

    @Override // androidx.appcompat.widget.AbstractViewOnTouchListenerC0143j0
    @SuppressLint({"SyntheticAccessor"})
    /* renamed from: c */
    public final boolean mo144c() {
        if (this.f960t.getInternalPopup().mo458b()) {
            return true;
        }
        this.f960t.m457b();
        return true;
    }
}
