package androidx.appcompat.widget;

import android.graphics.Rect;
import android.util.Log;
import android.view.View;
import java.lang.reflect.Method;
import java.util.WeakHashMap;
import p029e0.C0766p;
import p029e0.C0769s;

/* renamed from: androidx.appcompat.widget.j1 */
/* loaded from: classes.dex */
public final class C0144j1 {

    /* renamed from: a */
    public static Method f855a;

    static {
        try {
            Method declaredMethod = View.class.getDeclaredMethod("computeFitSystemWindows", Rect.class, Rect.class);
            f855a = declaredMethod;
            if (declaredMethod.isAccessible()) {
                return;
            }
            f855a.setAccessible(true);
        } catch (NoSuchMethodException unused) {
            Log.d("ViewUtils", "Could not find method computeFitSystemWindows. Oh well.");
        }
    }

    /* renamed from: a */
    public static void m413a(View view, Rect rect, Rect rect2) {
        Method method = f855a;
        if (method != null) {
            try {
                method.invoke(view, rect, rect2);
            } catch (Exception e6) {
                Log.d("ViewUtils", "Could not invoke computeFitSystemWindows", e6);
            }
        }
    }

    /* renamed from: b */
    public static boolean m414b(View view) {
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        return view.getLayoutDirection() == 1;
    }
}
