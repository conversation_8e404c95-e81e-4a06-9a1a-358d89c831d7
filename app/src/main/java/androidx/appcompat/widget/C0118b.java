package androidx.appcompat.widget;

import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.Outline;
import android.graphics.drawable.Drawable;

/* renamed from: androidx.appcompat.widget.b */
/* loaded from: classes.dex */
public final class C0118b extends Drawable {

    /* renamed from: a */
    public final ActionBarContainer f707a;

    public C0118b(ActionBarContainer actionBarContainer) {
        this.f707a = actionBarContainer;
    }

    @Override // android.graphics.drawable.Drawable
    public final void draw(Canvas canvas) {
        ActionBarContainer actionBarContainer = this.f707a;
        if (actionBarContainer.f477q) {
            Drawable drawable = actionBarContainer.f476p;
            if (drawable != null) {
                drawable.draw(canvas);
                return;
            }
            return;
        }
        Drawable drawable2 = actionBarContainer.f474n;
        if (drawable2 != null) {
            drawable2.draw(canvas);
        }
        ActionBarContainer actionBarContainer2 = this.f707a;
        Drawable drawable3 = actionBarContainer2.f475o;
        if (drawable3 == null || !actionBarContainer2.f478r) {
            return;
        }
        drawable3.draw(canvas);
    }

    @Override // android.graphics.drawable.Drawable
    public final int getOpacity() {
        return 0;
    }

    @Override // android.graphics.drawable.Drawable
    public final void getOutline(Outline outline) {
        Drawable drawable;
        ActionBarContainer actionBarContainer = this.f707a;
        if (actionBarContainer.f477q) {
            drawable = actionBarContainer.f476p;
            if (drawable == null) {
                return;
            }
        } else {
            drawable = actionBarContainer.f474n;
            if (drawable == null) {
                return;
            }
        }
        drawable.getOutline(outline);
    }

    @Override // android.graphics.drawable.Drawable
    public final void setAlpha(int i6) {
    }

    @Override // android.graphics.drawable.Drawable
    public final void setColorFilter(ColorFilter colorFilter) {
    }
}
