package androidx.appcompat.widget;

import android.os.Build;
import android.text.TextUtils;
import android.view.View;

/* renamed from: androidx.appcompat.widget.f1 */
/* loaded from: classes.dex */
public final class C0132f1 {
    /* renamed from: a */
    public static void m387a(View view, CharSequence charSequence) {
        if (Build.VERSION.SDK_INT >= 26) {
            view.setTooltipText(charSequence);
            return;
        }
        ViewOnLongClickListenerC0135g1 viewOnLongClickListenerC0135g1 = ViewOnLongClickListenerC0135g1.f791s;
        if (viewOnLongClickListenerC0135g1 != null && viewOnLongClickListenerC0135g1.f793j == view) {
            ViewOnLongClickListenerC0135g1.m390c(null);
        }
        if (!TextUtils.isEmpty(charSequence)) {
            new ViewOnLongClickListenerC0135g1(view, charSequence);
            return;
        }
        ViewOnLongClickListenerC0135g1 viewOnLongClickListenerC0135g12 = ViewOnLongClickListenerC0135g1.f792t;
        if (viewOnLongClickListenerC0135g12 != null && viewOnLongClickListenerC0135g12.f793j == view) {
            viewOnLongClickListenerC0135g12.m392b();
        }
        view.setOnLongClickListener(null);
        view.setLongClickable(false);
        view.setOnHoverListener(null);
    }
}
