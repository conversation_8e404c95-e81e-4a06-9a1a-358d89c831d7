package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Canvas;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;
import android.widget.SeekBar;
import com.liaoyuan.aicast.R;
import java.util.WeakHashMap;
import p008b0.C0385m;
import p029e0.C0766p;
import p029e0.C0769s;

/* renamed from: androidx.appcompat.widget.u */
/* loaded from: classes.dex */
public final class C0166u extends C0156p {

    /* renamed from: d */
    public final SeekBar f945d;

    /* renamed from: e */
    public Drawable f946e;

    /* renamed from: f */
    public ColorStateList f947f;

    /* renamed from: g */
    public PorterDuff.Mode f948g;

    /* renamed from: h */
    public boolean f949h;

    /* renamed from: i */
    public boolean f950i;

    public C0166u(SeekBar seekBar) {
        super(seekBar);
        this.f947f = null;
        this.f948g = null;
        this.f949h = false;
        this.f950i = false;
        this.f945d = seekBar;
    }

    @Override // androidx.appcompat.widget.C0156p
    /* renamed from: a */
    public final void mo438a(AttributeSet attributeSet, int i6) {
        super.mo438a(attributeSet, R.attr.seekBarStyle);
        Context context = this.f945d.getContext();
        int[] iArr = C0385m.f2359q;
        C0123c1 m336q = C0123c1.m336q(context, attributeSet, iArr, R.attr.seekBarStyle);
        SeekBar seekBar = this.f945d;
        C0766p.m2186s(seekBar, seekBar.getContext(), iArr, attributeSet, m336q.f751b, R.attr.seekBarStyle);
        Drawable m344h = m336q.m344h(0);
        if (m344h != null) {
            this.f945d.setThumb(m344h);
        }
        Drawable m343g = m336q.m343g(1);
        Drawable drawable = this.f946e;
        if (drawable != null) {
            drawable.setCallback(null);
        }
        this.f946e = m343g;
        if (m343g != null) {
            m343g.setCallback(this.f945d);
            SeekBar seekBar2 = this.f945d;
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            m343g.setLayoutDirection(seekBar2.getLayoutDirection());
            if (m343g.isStateful()) {
                m343g.setState(this.f945d.getDrawableState());
            }
            m453c();
        }
        this.f945d.invalidate();
        if (m336q.m351o(3)) {
            this.f948g = C0134g0.m389b(m336q.m346j(3, -1), this.f948g);
            this.f950i = true;
        }
        if (m336q.m351o(2)) {
            this.f947f = m336q.m339c(2);
            this.f949h = true;
        }
        m336q.m352r();
        m453c();
    }

    /* renamed from: c */
    public final void m453c() {
        Drawable drawable = this.f946e;
        if (drawable != null) {
            if (this.f949h || this.f950i) {
                Drawable mutate = drawable.mutate();
                this.f946e = mutate;
                if (this.f949h) {
                    mutate.setTintList(this.f947f);
                }
                if (this.f950i) {
                    this.f946e.setTintMode(this.f948g);
                }
                if (this.f946e.isStateful()) {
                    this.f946e.setState(this.f945d.getDrawableState());
                }
            }
        }
    }

    /* renamed from: d */
    public final void m454d(Canvas canvas) {
        if (this.f946e != null) {
            int max = this.f945d.getMax();
            if (max > 1) {
                int intrinsicWidth = this.f946e.getIntrinsicWidth();
                int intrinsicHeight = this.f946e.getIntrinsicHeight();
                int i6 = intrinsicWidth >= 0 ? intrinsicWidth / 2 : 1;
                int i7 = intrinsicHeight >= 0 ? intrinsicHeight / 2 : 1;
                this.f946e.setBounds(-i6, -i7, i6, i7);
                float width = ((this.f945d.getWidth() - this.f945d.getPaddingLeft()) - this.f945d.getPaddingRight()) / max;
                int save = canvas.save();
                canvas.translate(this.f945d.getPaddingLeft(), this.f945d.getHeight() / 2);
                for (int i8 = 0; i8 <= max; i8++) {
                    this.f946e.draw(canvas);
                    canvas.translate(width, 0.0f);
                }
                canvas.restoreToCount(save);
            }
        }
    }
}
