package androidx.appcompat.widget;

import android.R;
import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import p008b0.C0385m;
import p028e.C0750a;
import p156x.C1808a;

/* renamed from: androidx.appcompat.widget.x0 */
/* loaded from: classes.dex */
public final class C0173x0 {

    /* renamed from: a */
    public static final ThreadLocal<TypedValue> f1010a = new ThreadLocal<>();

    /* renamed from: b */
    public static final int[] f1011b = {-16842910};

    /* renamed from: c */
    public static final int[] f1012c = {R.attr.state_focused};

    /* renamed from: d */
    public static final int[] f1013d = {R.attr.state_pressed};

    /* renamed from: e */
    public static final int[] f1014e = {R.attr.state_checked};

    /* renamed from: f */
    public static final int[] f1015f = new int[0];

    /* renamed from: g */
    public static final int[] f1016g = new int[1];

    /* renamed from: a */
    public static void m480a(View view, Context context) {
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(C0385m.f2365t);
        try {
            if (!obtainStyledAttributes.hasValue(117)) {
                Log.e("ThemeUtils", "View " + view.getClass() + " is an AppCompat widget that can only be used with a Theme.AppCompat theme (or descendant).");
            }
        } finally {
            obtainStyledAttributes.recycle();
        }
    }

    /* renamed from: b */
    public static int m481b(Context context, int i6) {
        ColorStateList m483d = m483d(context, i6);
        if (m483d != null && m483d.isStateful()) {
            return m483d.getColorForState(f1011b, m483d.getDefaultColor());
        }
        ThreadLocal<TypedValue> threadLocal = f1010a;
        TypedValue typedValue = threadLocal.get();
        if (typedValue == null) {
            typedValue = new TypedValue();
            threadLocal.set(typedValue);
        }
        context.getTheme().resolveAttribute(R.attr.disabledAlpha, typedValue, true);
        float f6 = typedValue.getFloat();
        return C1808a.m4625c(m482c(context, i6), Math.round(Color.alpha(r4) * f6));
    }

    /* renamed from: c */
    public static int m482c(Context context, int i6) {
        int[] iArr = f1016g;
        iArr[0] = i6;
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes((AttributeSet) null, iArr);
        try {
            return obtainStyledAttributes.getColor(0, 0);
        } finally {
            obtainStyledAttributes.recycle();
        }
    }

    /* renamed from: d */
    public static ColorStateList m483d(Context context, int i6) {
        ColorStateList colorStateList;
        int resourceId;
        int[] iArr = f1016g;
        iArr[0] = i6;
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes((AttributeSet) null, iArr);
        try {
            if (obtainStyledAttributes.hasValue(0) && (resourceId = obtainStyledAttributes.getResourceId(0, 0)) != 0) {
                Object obj = C0750a.f4010a;
                colorStateList = context.getColorStateList(resourceId);
                if (colorStateList != null) {
                    return colorStateList;
                }
            }
            colorStateList = obtainStyledAttributes.getColorStateList(0);
            return colorStateList;
        } finally {
            obtainStyledAttributes.recycle();
        }
    }
}
