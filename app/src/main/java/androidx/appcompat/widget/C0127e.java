package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;
import java.util.WeakHashMap;
import p008b0.C0385m;
import p029e0.C0766p;
import p029e0.C0769s;

/* renamed from: androidx.appcompat.widget.e */
/* loaded from: classes.dex */
public final class C0127e {

    /* renamed from: a */
    public final View f760a;

    /* renamed from: d */
    public C0117a1 f763d;

    /* renamed from: e */
    public C0117a1 f764e;

    /* renamed from: f */
    public C0117a1 f765f;

    /* renamed from: c */
    public int f762c = -1;

    /* renamed from: b */
    public final C0142j f761b = C0142j.m401a();

    public C0127e(View view) {
        this.f760a = view;
    }

    /* renamed from: a */
    public final void m353a() {
        Drawable background = this.f760a.getBackground();
        if (background != null) {
            boolean z5 = true;
            if (this.f763d != null) {
                if (this.f765f == null) {
                    this.f765f = new C0117a1();
                }
                C0117a1 c0117a1 = this.f765f;
                c0117a1.f703a = null;
                c0117a1.f706d = false;
                c0117a1.f704b = null;
                c0117a1.f705c = false;
                View view = this.f760a;
                WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
                ColorStateList backgroundTintList = view.getBackgroundTintList();
                if (backgroundTintList != null) {
                    c0117a1.f706d = true;
                    c0117a1.f703a = backgroundTintList;
                }
                PorterDuff.Mode backgroundTintMode = this.f760a.getBackgroundTintMode();
                if (backgroundTintMode != null) {
                    c0117a1.f705c = true;
                    c0117a1.f704b = backgroundTintMode;
                }
                if (c0117a1.f706d || c0117a1.f705c) {
                    C0142j.m404f(background, c0117a1, this.f760a.getDrawableState());
                } else {
                    z5 = false;
                }
                if (z5) {
                    return;
                }
            }
            C0117a1 c0117a12 = this.f764e;
            if (c0117a12 != null) {
                C0142j.m404f(background, c0117a12, this.f760a.getDrawableState());
                return;
            }
            C0117a1 c0117a13 = this.f763d;
            if (c0117a13 != null) {
                C0142j.m404f(background, c0117a13, this.f760a.getDrawableState());
            }
        }
    }

    /* renamed from: b */
    public final ColorStateList m354b() {
        C0117a1 c0117a1 = this.f764e;
        if (c0117a1 != null) {
            return c0117a1.f703a;
        }
        return null;
    }

    /* renamed from: c */
    public final PorterDuff.Mode m355c() {
        C0117a1 c0117a1 = this.f764e;
        if (c0117a1 != null) {
            return c0117a1.f704b;
        }
        return null;
    }

    /* renamed from: d */
    public final void m356d(AttributeSet attributeSet, int i6) {
        Context context = this.f760a.getContext();
        int[] iArr = C0385m.f2318I;
        C0123c1 m336q = C0123c1.m336q(context, attributeSet, iArr, i6);
        View view = this.f760a;
        C0766p.m2186s(view, view.getContext(), iArr, attributeSet, m336q.f751b, i6);
        try {
            if (m336q.m351o(0)) {
                this.f762c = m336q.m348l(0, -1);
                ColorStateList m406d = this.f761b.m406d(this.f760a.getContext(), this.f762c);
                if (m406d != null) {
                    m359g(m406d);
                }
            }
            if (m336q.m351o(1)) {
                this.f760a.setBackgroundTintList(m336q.m339c(1));
            }
            if (m336q.m351o(2)) {
                this.f760a.setBackgroundTintMode(C0134g0.m389b(m336q.m346j(2, -1), null));
            }
        } finally {
            m336q.m352r();
        }
    }

    /* renamed from: e */
    public final void m357e() {
        this.f762c = -1;
        m359g(null);
        m353a();
    }

    /* renamed from: f */
    public final void m358f(int i6) {
        this.f762c = i6;
        C0142j c0142j = this.f761b;
        m359g(c0142j != null ? c0142j.m406d(this.f760a.getContext(), i6) : null);
        m353a();
    }

    /* renamed from: g */
    public final void m359g(ColorStateList colorStateList) {
        if (colorStateList != null) {
            if (this.f763d == null) {
                this.f763d = new C0117a1();
            }
            C0117a1 c0117a1 = this.f763d;
            c0117a1.f703a = colorStateList;
            c0117a1.f706d = true;
        } else {
            this.f763d = null;
        }
        m353a();
    }

    /* renamed from: h */
    public final void m360h(ColorStateList colorStateList) {
        if (this.f764e == null) {
            this.f764e = new C0117a1();
        }
        C0117a1 c0117a1 = this.f764e;
        c0117a1.f703a = colorStateList;
        c0117a1.f706d = true;
        m353a();
    }

    /* renamed from: i */
    public final void m361i(PorterDuff.Mode mode) {
        if (this.f764e == null) {
            this.f764e = new C0117a1();
        }
        C0117a1 c0117a1 = this.f764e;
        c0117a1.f704b = mode;
        c0117a1.f705c = true;
        m353a();
    }
}
