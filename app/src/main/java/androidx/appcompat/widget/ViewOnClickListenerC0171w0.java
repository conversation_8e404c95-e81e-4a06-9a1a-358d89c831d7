package androidx.appcompat.widget;

import android.R;
import android.app.SearchableInfo;
import android.content.Context;
import android.content.pm.PackageManager;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.database.Cursor;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import java.io.FileNotFoundException;
import java.util.List;
import java.util.WeakHashMap;
import p057i0.AbstractC0943c;

/* renamed from: androidx.appcompat.widget.w0 */
/* loaded from: classes.dex */
public final class ViewOnClickListenerC0171w0 extends AbstractC0943c implements View.OnClickListener {

    /* renamed from: H */
    public static final /* synthetic */ int f989H = 0;

    /* renamed from: A */
    public ColorStateList f990A;

    /* renamed from: B */
    public int f991B;

    /* renamed from: C */
    public int f992C;

    /* renamed from: D */
    public int f993D;

    /* renamed from: E */
    public int f994E;

    /* renamed from: F */
    public int f995F;

    /* renamed from: G */
    public int f996G;

    /* renamed from: u */
    public final SearchView f997u;

    /* renamed from: v */
    public final SearchableInfo f998v;

    /* renamed from: w */
    public final Context f999w;

    /* renamed from: x */
    public final WeakHashMap<String, Drawable.ConstantState> f1000x;

    /* renamed from: y */
    public final int f1001y;

    /* renamed from: z */
    public int f1002z;

    /* renamed from: androidx.appcompat.widget.w0$a */
    public static final class a {

        /* renamed from: a */
        public final TextView f1003a;

        /* renamed from: b */
        public final TextView f1004b;

        /* renamed from: c */
        public final ImageView f1005c;

        /* renamed from: d */
        public final ImageView f1006d;

        /* renamed from: e */
        public final ImageView f1007e;

        public a(View view) {
            this.f1003a = (TextView) view.findViewById(R.id.text1);
            this.f1004b = (TextView) view.findViewById(R.id.text2);
            this.f1005c = (ImageView) view.findViewById(R.id.icon1);
            this.f1006d = (ImageView) view.findViewById(R.id.icon2);
            this.f1007e = (ImageView) view.findViewById(com.liaoyuan.aicast.R.id.edit_query);
        }
    }

    public ViewOnClickListenerC0171w0(Context context, SearchView searchView, SearchableInfo searchableInfo, WeakHashMap<String, Drawable.ConstantState> weakHashMap) {
        super(context, searchView.getSuggestionRowLayout());
        this.f1002z = 1;
        this.f991B = -1;
        this.f992C = -1;
        this.f993D = -1;
        this.f994E = -1;
        this.f995F = -1;
        this.f996G = -1;
        this.f997u = searchView;
        this.f998v = searchableInfo;
        this.f1001y = searchView.getSuggestionCommitIconResId();
        this.f999w = context;
        this.f1000x = weakHashMap;
    }

    /* renamed from: i */
    public static String m470i(Cursor cursor, int i6) {
        if (i6 == -1) {
            return null;
        }
        try {
            return cursor.getString(i6);
        } catch (Exception e6) {
            Log.e("SuggestionsAdapter", "unexpected error retrieving valid column from cursor, did the remote process die?", e6);
            return null;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:59:0x0149  */
    /* JADX WARN: Removed duplicated region for block: B:61:0x014b  */
    @Override // p057i0.AbstractC0941a
    /* renamed from: b */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void mo471b(android.view.View r19, android.database.Cursor r20) {
        /*
            Method dump skipped, instructions count: 421
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.ViewOnClickListenerC0171w0.mo471b(android.view.View, android.database.Cursor):void");
    }

    @Override // p057i0.AbstractC0941a
    /* renamed from: c */
    public final void mo472c(Cursor cursor) {
        try {
            super.mo472c(cursor);
            if (cursor != null) {
                this.f991B = cursor.getColumnIndex("suggest_text_1");
                this.f992C = cursor.getColumnIndex("suggest_text_2");
                this.f993D = cursor.getColumnIndex("suggest_text_2_url");
                this.f994E = cursor.getColumnIndex("suggest_icon_1");
                this.f995F = cursor.getColumnIndex("suggest_icon_2");
                this.f996G = cursor.getColumnIndex("suggest_flags");
            }
        } catch (Exception e6) {
            Log.e("SuggestionsAdapter", "error changing cursor and caching columns", e6);
        }
    }

    @Override // p057i0.AbstractC0941a
    /* renamed from: d */
    public final CharSequence mo473d(Cursor cursor) {
        String m470i;
        String m470i2;
        if (cursor == null) {
            return null;
        }
        String m470i3 = m470i(cursor, cursor.getColumnIndex("suggest_intent_query"));
        if (m470i3 != null) {
            return m470i3;
        }
        if (this.f998v.shouldRewriteQueryFromData() && (m470i2 = m470i(cursor, cursor.getColumnIndex("suggest_intent_data"))) != null) {
            return m470i2;
        }
        if (!this.f998v.shouldRewriteQueryFromText() || (m470i = m470i(cursor, cursor.getColumnIndex("suggest_text_1"))) == null) {
            return null;
        }
        return m470i;
    }

    @Override // p057i0.AbstractC0941a
    /* renamed from: e */
    public final View mo474e(ViewGroup viewGroup) {
        View inflate = this.f4687t.inflate(this.f4685r, viewGroup, false);
        inflate.setTag(new a(inflate));
        ((ImageView) inflate.findViewById(com.liaoyuan.aicast.R.id.edit_query)).setImageResource(this.f1001y);
        return inflate;
    }

    /* renamed from: f */
    public final Drawable m475f(Uri uri) {
        int parseInt;
        String authority = uri.getAuthority();
        if (TextUtils.isEmpty(authority)) {
            throw new FileNotFoundException("No authority: " + uri);
        }
        try {
            Resources resourcesForApplication = this.f999w.getPackageManager().getResourcesForApplication(authority);
            List<String> pathSegments = uri.getPathSegments();
            if (pathSegments == null) {
                throw new FileNotFoundException("No path: " + uri);
            }
            int size = pathSegments.size();
            if (size == 1) {
                try {
                    parseInt = Integer.parseInt(pathSegments.get(0));
                } catch (NumberFormatException unused) {
                    throw new FileNotFoundException("Single path segment is not a resource ID: " + uri);
                }
            } else {
                if (size != 2) {
                    throw new FileNotFoundException("More than two path segments: " + uri);
                }
                parseInt = resourcesForApplication.getIdentifier(pathSegments.get(1), pathSegments.get(0), authority);
            }
            if (parseInt != 0) {
                return resourcesForApplication.getDrawable(parseInt);
            }
            throw new FileNotFoundException("No resource found for: " + uri);
        } catch (PackageManager.NameNotFoundException unused2) {
            throw new FileNotFoundException("No package found for authority: " + uri);
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:41:0x0132  */
    /* renamed from: g */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final android.graphics.drawable.Drawable m476g(java.lang.String r8) {
        /*
            Method dump skipped, instructions count: 316
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.ViewOnClickListenerC0171w0.m476g(java.lang.String):android.graphics.drawable.Drawable");
    }

    @Override // p057i0.AbstractC0941a, android.widget.BaseAdapter, android.widget.SpinnerAdapter
    public final View getDropDownView(int i6, View view, ViewGroup viewGroup) {
        try {
            return super.getDropDownView(i6, view, viewGroup);
        } catch (RuntimeException e6) {
            Log.w("SuggestionsAdapter", "Search suggestions cursor threw exception.", e6);
            View inflate = this.f4687t.inflate(this.f4686s, viewGroup, false);
            if (inflate != null) {
                ((a) inflate.getTag()).f1003a.setText(e6.toString());
            }
            return inflate;
        }
    }

    @Override // p057i0.AbstractC0941a, android.widget.Adapter
    public final View getView(int i6, View view, ViewGroup viewGroup) {
        try {
            return super.getView(i6, view, viewGroup);
        } catch (RuntimeException e6) {
            Log.w("SuggestionsAdapter", "Search suggestions cursor threw exception.", e6);
            View mo474e = mo474e(viewGroup);
            ((a) mo474e.getTag()).f1003a.setText(e6.toString());
            return mo474e;
        }
    }

    /* renamed from: h */
    public final Cursor m477h(SearchableInfo searchableInfo, String str) {
        String suggestAuthority;
        String[] strArr = null;
        if (searchableInfo == null || (suggestAuthority = searchableInfo.getSuggestAuthority()) == null) {
            return null;
        }
        Uri.Builder fragment = new Uri.Builder().scheme("content").authority(suggestAuthority).query("").fragment("");
        String suggestPath = searchableInfo.getSuggestPath();
        if (suggestPath != null) {
            fragment.appendEncodedPath(suggestPath);
        }
        fragment.appendPath("search_suggest_query");
        String suggestSelection = searchableInfo.getSuggestSelection();
        if (suggestSelection != null) {
            strArr = new String[]{str};
        } else {
            fragment.appendPath(str);
        }
        fragment.appendQueryParameter("limit", String.valueOf(50));
        return this.f999w.getContentResolver().query(fragment.build(), null, suggestSelection, strArr, null);
    }

    @Override // android.widget.BaseAdapter, android.widget.Adapter
    public final boolean hasStableIds() {
        return false;
    }

    /* renamed from: j */
    public final void m478j(ImageView imageView, Drawable drawable, int i6) {
        imageView.setImageDrawable(drawable);
        if (drawable == null) {
            imageView.setVisibility(i6);
            return;
        }
        imageView.setVisibility(0);
        drawable.setVisible(false, false);
        drawable.setVisible(true, false);
    }

    @Override // android.widget.BaseAdapter
    public final void notifyDataSetChanged() {
        super.notifyDataSetChanged();
        Cursor cursor = this.f4676l;
        Bundle extras = cursor != null ? cursor.getExtras() : null;
        if (extras != null) {
            extras.getBoolean("in_progress");
        }
    }

    @Override // android.widget.BaseAdapter
    public final void notifyDataSetInvalidated() {
        super.notifyDataSetInvalidated();
        Cursor cursor = this.f4676l;
        Bundle extras = cursor != null ? cursor.getExtras() : null;
        if (extras != null) {
            extras.getBoolean("in_progress");
        }
    }

    @Override // android.view.View.OnClickListener
    public final void onClick(View view) {
        Object tag = view.getTag();
        if (tag instanceof CharSequence) {
            this.f997u.m268r((CharSequence) tag);
        }
    }
}
