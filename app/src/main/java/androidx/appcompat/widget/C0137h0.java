package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.ListAdapter;
import android.widget.ListView;
import com.liaoyuan.aicast.R;
import java.lang.reflect.Field;
import p035f.C0829a;
import p050h0.C0902c;

/* renamed from: androidx.appcompat.widget.h0 */
/* loaded from: classes.dex */
public class C0137h0 extends ListView {

    /* renamed from: j */
    public final Rect f806j;

    /* renamed from: k */
    public int f807k;

    /* renamed from: l */
    public int f808l;

    /* renamed from: m */
    public int f809m;

    /* renamed from: n */
    public int f810n;

    /* renamed from: o */
    public int f811o;

    /* renamed from: p */
    public Field f812p;

    /* renamed from: q */
    public a f813q;

    /* renamed from: r */
    public boolean f814r;

    /* renamed from: s */
    public boolean f815s;

    /* renamed from: t */
    public boolean f816t;

    /* renamed from: u */
    public C0902c f817u;

    /* renamed from: v */
    public b f818v;

    /* renamed from: androidx.appcompat.widget.h0$a */
    public static class a extends C0829a {

        /* renamed from: k */
        public boolean f819k;

        public a(Drawable drawable) {
            super(drawable);
            this.f819k = true;
        }

        @Override // p035f.C0829a, android.graphics.drawable.Drawable
        public final void draw(Canvas canvas) {
            if (this.f819k) {
                super.draw(canvas);
            }
        }

        @Override // p035f.C0829a, android.graphics.drawable.Drawable
        public final void setHotspot(float f6, float f7) {
            if (this.f819k) {
                super.setHotspot(f6, f7);
            }
        }

        @Override // p035f.C0829a, android.graphics.drawable.Drawable
        public final void setHotspotBounds(int i6, int i7, int i8, int i9) {
            if (this.f819k) {
                super.setHotspotBounds(i6, i7, i8, i9);
            }
        }

        @Override // p035f.C0829a, android.graphics.drawable.Drawable
        public final boolean setState(int[] iArr) {
            if (this.f819k) {
                return super.setState(iArr);
            }
            return false;
        }

        @Override // p035f.C0829a, android.graphics.drawable.Drawable
        public final boolean setVisible(boolean z5, boolean z6) {
            if (this.f819k) {
                return super.setVisible(z5, z6);
            }
            return false;
        }
    }

    /* renamed from: androidx.appcompat.widget.h0$b */
    public class b implements Runnable {
        public b() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            C0137h0 c0137h0 = C0137h0.this;
            c0137h0.f818v = null;
            c0137h0.drawableStateChanged();
        }
    }

    public C0137h0(Context context, boolean z5) {
        super(context, null, R.attr.dropDownListViewStyle);
        this.f806j = new Rect();
        this.f807k = 0;
        this.f808l = 0;
        this.f809m = 0;
        this.f810n = 0;
        this.f815s = z5;
        setCacheColorHint(0);
        try {
            Field declaredField = AbsListView.class.getDeclaredField("mIsChildViewEnabled");
            this.f812p = declaredField;
            declaredField.setAccessible(true);
        } catch (NoSuchFieldException e6) {
            e6.printStackTrace();
        }
    }

    private void setSelectorEnabled(boolean z5) {
        a aVar = this.f813q;
        if (aVar != null) {
            aVar.f819k = z5;
        }
    }

    /* renamed from: a */
    public final int m394a(int i6, int i7) {
        int listPaddingTop = getListPaddingTop();
        int listPaddingBottom = getListPaddingBottom();
        int dividerHeight = getDividerHeight();
        Drawable divider = getDivider();
        ListAdapter adapter = getAdapter();
        int i8 = listPaddingTop + listPaddingBottom;
        if (adapter == null) {
            return i8;
        }
        if (dividerHeight <= 0 || divider == null) {
            dividerHeight = 0;
        }
        int count = adapter.getCount();
        int i9 = 0;
        View view = null;
        for (int i10 = 0; i10 < count; i10++) {
            int itemViewType = adapter.getItemViewType(i10);
            if (itemViewType != i9) {
                view = null;
                i9 = itemViewType;
            }
            view = adapter.getView(i10, view, this);
            ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
            if (layoutParams == null) {
                layoutParams = generateDefaultLayoutParams();
                view.setLayoutParams(layoutParams);
            }
            int i11 = layoutParams.height;
            view.measure(i6, i11 > 0 ? View.MeasureSpec.makeMeasureSpec(i11, 1073741824) : View.MeasureSpec.makeMeasureSpec(0, 0));
            view.forceLayout();
            if (i10 > 0) {
                i8 += dividerHeight;
            }
            i8 += view.getMeasuredHeight();
            if (i8 >= i7) {
                return i7;
            }
        }
        return i8;
    }

    /* JADX WARN: Removed duplicated region for block: B:11:0x0144  */
    /* JADX WARN: Removed duplicated region for block: B:17:0x015a  */
    /* JADX WARN: Removed duplicated region for block: B:25:0x013f  */
    /* JADX WARN: Removed duplicated region for block: B:9:0x0128 A[ADDED_TO_REGION] */
    /* renamed from: b */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean m395b(android.view.MotionEvent r17, int r18) {
        /*
            Method dump skipped, instructions count: 360
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.C0137h0.m395b(android.view.MotionEvent, int):boolean");
    }

    /* renamed from: c */
    public final void m396c() {
        Drawable selector = getSelector();
        if (selector != null && this.f816t && isPressed()) {
            selector.setState(getDrawableState());
        }
    }

    @Override // android.widget.ListView, android.widget.AbsListView, android.view.ViewGroup, android.view.View
    public final void dispatchDraw(Canvas canvas) {
        Drawable selector;
        if (!this.f806j.isEmpty() && (selector = getSelector()) != null) {
            selector.setBounds(this.f806j);
            selector.draw(canvas);
        }
        super.dispatchDraw(canvas);
    }

    @Override // android.widget.AbsListView, android.view.ViewGroup, android.view.View
    public final void drawableStateChanged() {
        if (this.f818v != null) {
            return;
        }
        super.drawableStateChanged();
        setSelectorEnabled(true);
        m396c();
    }

    @Override // android.view.ViewGroup, android.view.View
    public final boolean hasFocus() {
        return this.f815s || super.hasFocus();
    }

    @Override // android.view.View
    public final boolean hasWindowFocus() {
        return this.f815s || super.hasWindowFocus();
    }

    @Override // android.view.View
    public final boolean isFocused() {
        return this.f815s || super.isFocused();
    }

    @Override // android.view.View
    public final boolean isInTouchMode() {
        return (this.f815s && this.f814r) || super.isInTouchMode();
    }

    @Override // android.widget.ListView, android.widget.AbsListView, android.widget.AdapterView, android.view.ViewGroup, android.view.View
    public final void onDetachedFromWindow() {
        this.f818v = null;
        super.onDetachedFromWindow();
    }

    @Override // android.view.View
    public boolean onHoverEvent(MotionEvent motionEvent) {
        if (Build.VERSION.SDK_INT < 26) {
            return super.onHoverEvent(motionEvent);
        }
        int actionMasked = motionEvent.getActionMasked();
        if (actionMasked == 10 && this.f818v == null) {
            b bVar = new b();
            this.f818v = bVar;
            post(bVar);
        }
        boolean onHoverEvent = super.onHoverEvent(motionEvent);
        if (actionMasked == 9 || actionMasked == 7) {
            int pointToPosition = pointToPosition((int) motionEvent.getX(), (int) motionEvent.getY());
            if (pointToPosition != -1 && pointToPosition != getSelectedItemPosition()) {
                View childAt = getChildAt(pointToPosition - getFirstVisiblePosition());
                if (childAt.isEnabled()) {
                    setSelectionFromTop(pointToPosition, childAt.getTop() - getTop());
                }
                m396c();
            }
        } else {
            setSelection(-1);
        }
        return onHoverEvent;
    }

    @Override // android.widget.AbsListView, android.view.View
    public final boolean onTouchEvent(MotionEvent motionEvent) {
        if (motionEvent.getAction() == 0) {
            this.f811o = pointToPosition((int) motionEvent.getX(), (int) motionEvent.getY());
        }
        b bVar = this.f818v;
        if (bVar != null) {
            C0137h0 c0137h0 = C0137h0.this;
            c0137h0.f818v = null;
            c0137h0.removeCallbacks(bVar);
        }
        return super.onTouchEvent(motionEvent);
    }

    public void setListSelectionHidden(boolean z5) {
        this.f814r = z5;
    }

    @Override // android.widget.AbsListView
    public void setSelector(Drawable drawable) {
        a aVar = drawable != null ? new a(drawable) : null;
        this.f813q = aVar;
        super.setSelector(aVar);
        Rect rect = new Rect();
        if (drawable != null) {
            drawable.getPadding(rect);
        }
        this.f807k = rect.left;
        this.f808l = rect.top;
        this.f809m = rect.right;
        this.f810n = rect.bottom;
    }
}
