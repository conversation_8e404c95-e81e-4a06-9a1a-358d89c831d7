package androidx.appcompat.widget;

import android.R;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.text.TextDirectionHeuristic;
import android.text.TextDirectionHeuristics;
import android.util.AttributeSet;
import android.view.ActionMode;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputConnection;
import android.view.textclassifier.TextClassifier;
import android.widget.TextView;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import p008b0.C0385m;
import p014c0.C0412b;
import p028e.C0750a;
import p050h0.C0903d;
import p050h0.InterfaceC0901b;
import p156x.C1810c;
import p156x.C1816i;

/* renamed from: androidx.appcompat.widget.b0 */
/* loaded from: classes.dex */
public class C0119b0 extends TextView implements InterfaceC0901b {

    /* renamed from: j */
    public final C0127e f708j;

    /* renamed from: k */
    public final C0176z f709k;

    /* renamed from: l */
    public final C0172x f710l;

    /* renamed from: m */
    public boolean f711m;

    /* renamed from: n */
    public Future<C0412b> f712n;

    public C0119b0(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, R.attr.textViewStyle);
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C0119b0(Context context, AttributeSet attributeSet, int i6) {
        super(context, attributeSet, i6);
        C0177z0.m514a(context);
        this.f711m = false;
        C0173x0.m480a(this, getContext());
        C0127e c0127e = new C0127e(this);
        this.f708j = c0127e;
        c0127e.m356d(attributeSet, i6);
        C0176z c0176z = new C0176z(this);
        this.f709k = c0176z;
        c0176z.m502f(attributeSet, i6);
        c0176z.m499b();
        this.f710l = new C0172x(this);
    }

    @Override // android.widget.TextView, android.view.View
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        C0127e c0127e = this.f708j;
        if (c0127e != null) {
            c0127e.m353a();
        }
        C0176z c0176z = this.f709k;
        if (c0176z != null) {
            c0176z.m499b();
        }
    }

    @Override // android.widget.TextView
    public int getAutoSizeMaxTextSize() {
        if (InterfaceC0901b.f4567a) {
            return super.getAutoSizeMaxTextSize();
        }
        C0176z c0176z = this.f709k;
        if (c0176z != null) {
            return Math.round(c0176z.f1026i.f743e);
        }
        return -1;
    }

    @Override // android.widget.TextView
    public int getAutoSizeMinTextSize() {
        if (InterfaceC0901b.f4567a) {
            return super.getAutoSizeMinTextSize();
        }
        C0176z c0176z = this.f709k;
        if (c0176z != null) {
            return Math.round(c0176z.f1026i.f742d);
        }
        return -1;
    }

    @Override // android.widget.TextView
    public int getAutoSizeStepGranularity() {
        if (InterfaceC0901b.f4567a) {
            return super.getAutoSizeStepGranularity();
        }
        C0176z c0176z = this.f709k;
        if (c0176z != null) {
            return Math.round(c0176z.f1026i.f741c);
        }
        return -1;
    }

    @Override // android.widget.TextView
    public int[] getAutoSizeTextAvailableSizes() {
        if (InterfaceC0901b.f4567a) {
            return super.getAutoSizeTextAvailableSizes();
        }
        C0176z c0176z = this.f709k;
        return c0176z != null ? c0176z.f1026i.f744f : new int[0];
    }

    @Override // android.widget.TextView
    @SuppressLint({"WrongConstant"})
    public int getAutoSizeTextType() {
        if (InterfaceC0901b.f4567a) {
            return super.getAutoSizeTextType() == 1 ? 1 : 0;
        }
        C0176z c0176z = this.f709k;
        if (c0176z != null) {
            return c0176z.f1026i.f739a;
        }
        return 0;
    }

    @Override // android.widget.TextView
    public int getFirstBaselineToTopHeight() {
        return getPaddingTop() - getPaint().getFontMetricsInt().top;
    }

    @Override // android.widget.TextView
    public int getLastBaselineToBottomHeight() {
        return getPaddingBottom() + getPaint().getFontMetricsInt().bottom;
    }

    public ColorStateList getSupportBackgroundTintList() {
        C0127e c0127e = this.f708j;
        if (c0127e != null) {
            return c0127e.m354b();
        }
        return null;
    }

    public PorterDuff.Mode getSupportBackgroundTintMode() {
        C0127e c0127e = this.f708j;
        if (c0127e != null) {
            return c0127e.m355c();
        }
        return null;
    }

    public ColorStateList getSupportCompoundDrawablesTintList() {
        C0117a1 c0117a1 = this.f709k.f1025h;
        if (c0117a1 != null) {
            return c0117a1.f703a;
        }
        return null;
    }

    public PorterDuff.Mode getSupportCompoundDrawablesTintMode() {
        C0117a1 c0117a1 = this.f709k.f1025h;
        if (c0117a1 != null) {
            return c0117a1.f704b;
        }
        return null;
    }

    @Override // android.widget.TextView
    public CharSequence getText() {
        Future<C0412b> future = this.f712n;
        if (future != null) {
            try {
                this.f712n = null;
                C0903d.m2454e(this, future.get());
            } catch (InterruptedException | ExecutionException unused) {
            }
        }
        return super.getText();
    }

    @Override // android.widget.TextView
    public TextClassifier getTextClassifier() {
        C0172x c0172x;
        return (Build.VERSION.SDK_INT >= 28 || (c0172x = this.f710l) == null) ? super.getTextClassifier() : c0172x.m479a();
    }

    public C0412b.a getTextMetricsParamsCompat() {
        return C0903d.m2450a(this);
    }

    @Override // android.widget.TextView, android.view.View
    public final InputConnection onCreateInputConnection(EditorInfo editorInfo) {
        InputConnection onCreateInputConnection = super.onCreateInputConnection(editorInfo);
        this.f709k.m504h(this, onCreateInputConnection, editorInfo);
        C0385m.m1422n(onCreateInputConnection, editorInfo, this);
        return onCreateInputConnection;
    }

    @Override // android.widget.TextView, android.view.View
    public final void onLayout(boolean z5, int i6, int i7, int i8, int i9) {
        super.onLayout(z5, i6, i7, i8, i9);
        C0176z c0176z = this.f709k;
        if (c0176z != null) {
            Objects.requireNonNull(c0176z);
            if (InterfaceC0901b.f4567a) {
                return;
            }
            c0176z.m500c();
        }
    }

    @Override // android.widget.TextView, android.view.View
    public void onMeasure(int i6, int i7) {
        Future<C0412b> future = this.f712n;
        if (future != null) {
            try {
                this.f712n = null;
                C0903d.m2454e(this, future.get());
            } catch (InterruptedException | ExecutionException unused) {
            }
        }
        super.onMeasure(i6, i7);
    }

    @Override // android.widget.TextView
    public final void onTextChanged(CharSequence charSequence, int i6, int i7, int i8) {
        super.onTextChanged(charSequence, i6, i7, i8);
        C0176z c0176z = this.f709k;
        if (c0176z == null || InterfaceC0901b.f4567a || !c0176z.m501e()) {
            return;
        }
        this.f709k.m500c();
    }

    @Override // android.widget.TextView
    public final void setAutoSizeTextTypeUniformWithConfiguration(int i6, int i7, int i8, int i9) {
        if (InterfaceC0901b.f4567a) {
            super.setAutoSizeTextTypeUniformWithConfiguration(i6, i7, i8, i9);
            return;
        }
        C0176z c0176z = this.f709k;
        if (c0176z != null) {
            c0176z.m506j(i6, i7, i8, i9);
        }
    }

    @Override // android.widget.TextView
    public final void setAutoSizeTextTypeUniformWithPresetSizes(int[] iArr, int i6) {
        if (InterfaceC0901b.f4567a) {
            super.setAutoSizeTextTypeUniformWithPresetSizes(iArr, i6);
            return;
        }
        C0176z c0176z = this.f709k;
        if (c0176z != null) {
            c0176z.m507k(iArr, i6);
        }
    }

    @Override // android.widget.TextView
    public void setAutoSizeTextTypeWithDefaults(int i6) {
        if (InterfaceC0901b.f4567a) {
            super.setAutoSizeTextTypeWithDefaults(i6);
            return;
        }
        C0176z c0176z = this.f709k;
        if (c0176z != null) {
            c0176z.m508l(i6);
        }
    }

    @Override // android.view.View
    public void setBackgroundDrawable(Drawable drawable) {
        super.setBackgroundDrawable(drawable);
        C0127e c0127e = this.f708j;
        if (c0127e != null) {
            c0127e.m357e();
        }
    }

    @Override // android.view.View
    public void setBackgroundResource(int i6) {
        super.setBackgroundResource(i6);
        C0127e c0127e = this.f708j;
        if (c0127e != null) {
            c0127e.m358f(i6);
        }
    }

    @Override // android.widget.TextView
    public final void setCompoundDrawables(Drawable drawable, Drawable drawable2, Drawable drawable3, Drawable drawable4) {
        super.setCompoundDrawables(drawable, drawable2, drawable3, drawable4);
        C0176z c0176z = this.f709k;
        if (c0176z != null) {
            c0176z.m499b();
        }
    }

    @Override // android.widget.TextView
    public final void setCompoundDrawablesRelative(Drawable drawable, Drawable drawable2, Drawable drawable3, Drawable drawable4) {
        super.setCompoundDrawablesRelative(drawable, drawable2, drawable3, drawable4);
        C0176z c0176z = this.f709k;
        if (c0176z != null) {
            c0176z.m499b();
        }
    }

    @Override // android.widget.TextView
    public final void setCompoundDrawablesRelativeWithIntrinsicBounds(int i6, int i7, int i8, int i9) {
        Context context = getContext();
        setCompoundDrawablesRelativeWithIntrinsicBounds(i6 != 0 ? C0750a.m2138a(context, i6) : null, i7 != 0 ? C0750a.m2138a(context, i7) : null, i8 != 0 ? C0750a.m2138a(context, i8) : null, i9 != 0 ? C0750a.m2138a(context, i9) : null);
        C0176z c0176z = this.f709k;
        if (c0176z != null) {
            c0176z.m499b();
        }
    }

    @Override // android.widget.TextView
    public final void setCompoundDrawablesWithIntrinsicBounds(int i6, int i7, int i8, int i9) {
        Context context = getContext();
        setCompoundDrawablesWithIntrinsicBounds(i6 != 0 ? C0750a.m2138a(context, i6) : null, i7 != 0 ? C0750a.m2138a(context, i7) : null, i8 != 0 ? C0750a.m2138a(context, i8) : null, i9 != 0 ? C0750a.m2138a(context, i9) : null);
        C0176z c0176z = this.f709k;
        if (c0176z != null) {
            c0176z.m499b();
        }
    }

    @Override // android.widget.TextView
    public void setCustomSelectionActionModeCallback(ActionMode.Callback callback) {
        super.setCustomSelectionActionModeCallback(C0903d.m2455f(this, callback));
    }

    @Override // android.widget.TextView
    public void setFirstBaselineToTopHeight(int i6) {
        if (Build.VERSION.SDK_INT >= 28) {
            super.setFirstBaselineToTopHeight(i6);
        } else {
            C0903d.m2451b(this, i6);
        }
    }

    @Override // android.widget.TextView
    public void setLastBaselineToBottomHeight(int i6) {
        if (Build.VERSION.SDK_INT >= 28) {
            super.setLastBaselineToBottomHeight(i6);
        } else {
            C0903d.m2452c(this, i6);
        }
    }

    @Override // android.widget.TextView
    public void setLineHeight(int i6) {
        C0903d.m2453d(this, i6);
    }

    public void setPrecomputedText(C0412b c0412b) {
        C0903d.m2454e(this, c0412b);
    }

    public void setSupportBackgroundTintList(ColorStateList colorStateList) {
        C0127e c0127e = this.f708j;
        if (c0127e != null) {
            c0127e.m360h(colorStateList);
        }
    }

    public void setSupportBackgroundTintMode(PorterDuff.Mode mode) {
        C0127e c0127e = this.f708j;
        if (c0127e != null) {
            c0127e.m361i(mode);
        }
    }

    public void setSupportCompoundDrawablesTintList(ColorStateList colorStateList) {
        this.f709k.m509m(colorStateList);
        this.f709k.m499b();
    }

    public void setSupportCompoundDrawablesTintMode(PorterDuff.Mode mode) {
        this.f709k.m510n(mode);
        this.f709k.m499b();
    }

    @Override // android.widget.TextView
    public void setTextAppearance(Context context, int i6) {
        super.setTextAppearance(context, i6);
        C0176z c0176z = this.f709k;
        if (c0176z != null) {
            c0176z.m503g(context, i6);
        }
    }

    @Override // android.widget.TextView
    public void setTextClassifier(TextClassifier textClassifier) {
        C0172x c0172x;
        if (Build.VERSION.SDK_INT >= 28 || (c0172x = this.f710l) == null) {
            super.setTextClassifier(textClassifier);
        } else {
            c0172x.f1009b = textClassifier;
        }
    }

    public void setTextFuture(Future<C0412b> future) {
        this.f712n = future;
        if (future != null) {
            requestLayout();
        }
    }

    public void setTextMetricsParamsCompat(C0412b.a aVar) {
        TextDirectionHeuristic textDirectionHeuristic = aVar.f2470b;
        int i6 = 1;
        if (textDirectionHeuristic != TextDirectionHeuristics.FIRSTSTRONG_RTL && textDirectionHeuristic != TextDirectionHeuristics.FIRSTSTRONG_LTR) {
            if (textDirectionHeuristic == TextDirectionHeuristics.ANYRTL_LTR) {
                i6 = 2;
            } else if (textDirectionHeuristic == TextDirectionHeuristics.LTR) {
                i6 = 3;
            } else if (textDirectionHeuristic == TextDirectionHeuristics.RTL) {
                i6 = 4;
            } else if (textDirectionHeuristic == TextDirectionHeuristics.LOCALE) {
                i6 = 5;
            } else if (textDirectionHeuristic == TextDirectionHeuristics.FIRSTSTRONG_LTR) {
                i6 = 6;
            } else if (textDirectionHeuristic == TextDirectionHeuristics.FIRSTSTRONG_RTL) {
                i6 = 7;
            }
        }
        setTextDirection(i6);
        getPaint().set(aVar.f2469a);
        setBreakStrategy(aVar.f2471c);
        setHyphenationFrequency(aVar.f2472d);
    }

    @Override // android.widget.TextView
    public final void setTextSize(int i6, float f6) {
        boolean z5 = InterfaceC0901b.f4567a;
        if (z5) {
            super.setTextSize(i6, f6);
            return;
        }
        C0176z c0176z = this.f709k;
        if (c0176z != null) {
            Objects.requireNonNull(c0176z);
            if (z5 || c0176z.m501e()) {
                return;
            }
            c0176z.f1026i.m328f(i6, f6);
        }
    }

    @Override // android.widget.TextView
    public final void setTypeface(Typeface typeface, int i6) {
        if (this.f711m) {
            return;
        }
        Typeface typeface2 = null;
        if (typeface != null && i6 > 0) {
            Context context = getContext();
            C1816i c1816i = C1810c.f7482a;
            if (context == null) {
                throw new IllegalArgumentException("Context cannot be null");
            }
            typeface2 = Typeface.create(typeface, i6);
        }
        this.f711m = true;
        if (typeface2 != null) {
            typeface = typeface2;
        }
        try {
            super.setTypeface(typeface, i6);
        } finally {
            this.f711m = false;
        }
    }

    @Override // android.widget.TextView
    public final void setCompoundDrawablesRelativeWithIntrinsicBounds(Drawable drawable, Drawable drawable2, Drawable drawable3, Drawable drawable4) {
        super.setCompoundDrawablesRelativeWithIntrinsicBounds(drawable, drawable2, drawable3, drawable4);
        C0176z c0176z = this.f709k;
        if (c0176z != null) {
            c0176z.m499b();
        }
    }

    @Override // android.widget.TextView
    public final void setCompoundDrawablesWithIntrinsicBounds(Drawable drawable, Drawable drawable2, Drawable drawable3, Drawable drawable4) {
        super.setCompoundDrawablesWithIntrinsicBounds(drawable, drawable2, drawable3, drawable4);
        C0176z c0176z = this.f709k;
        if (c0176z != null) {
            c0176z.m499b();
        }
    }
}
