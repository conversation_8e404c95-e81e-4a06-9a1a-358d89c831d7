package androidx.lifecycle;

import androidx.lifecycle.AbstractC0251d;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
/* renamed from: androidx.lifecycle.n */
/* loaded from: classes.dex */
public @interface InterfaceC0261n {
    AbstractC0251d.b value();
}
