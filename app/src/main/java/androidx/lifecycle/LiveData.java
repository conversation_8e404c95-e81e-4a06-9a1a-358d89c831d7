package androidx.lifecycle;

import android.util.Log;
import android.view.View;
import androidx.appcompat.widget.C0174y;
import androidx.fragment.app.AbstractC0240x;
import androidx.fragment.app.DialogInterfaceOnCancelListenerC0221l;
import androidx.lifecycle.AbstractC0251d;
import java.util.Map;
import java.util.Objects;
import p056i.C0939a;
import p063j.C0955b;

/* loaded from: classes.dex */
public abstract class LiveData<T> {

    /* renamed from: j */
    public static final Object f1662j = new Object();

    /* renamed from: a */
    public final Object f1663a = new Object();

    /* renamed from: b */
    public C0955b<InterfaceC0260m<? super T>, LiveData<T>.AbstractC0245b> f1664b = new C0955b<>();

    /* renamed from: c */
    public int f1665c = 0;

    /* renamed from: d */
    public boolean f1666d;

    /* renamed from: e */
    public volatile Object f1667e;

    /* renamed from: f */
    public volatile Object f1668f;

    /* renamed from: g */
    public int f1669g;

    /* renamed from: h */
    public boolean f1670h;

    /* renamed from: i */
    public boolean f1671i;

    public class LifecycleBoundObserver extends LiveData<T>.AbstractC0245b implements InterfaceC0252e {

        /* renamed from: e */
        public final InterfaceC0254g f1672e;

        /* renamed from: f */
        public final /* synthetic */ LiveData f1673f;

        @Override // androidx.lifecycle.InterfaceC0252e
        /* renamed from: e */
        public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
            AbstractC0251d.c cVar = ((C0255h) this.f1672e.mo86a()).f1700b;
            if (cVar == AbstractC0251d.c.DESTROYED) {
                this.f1673f.mo856g(this.f1674a);
                return;
            }
            AbstractC0251d.c cVar2 = null;
            while (cVar2 != cVar) {
                m859g(((C0255h) this.f1672e.mo86a()).f1700b.m875d(AbstractC0251d.c.STARTED));
                cVar2 = cVar;
                cVar = ((C0255h) this.f1672e.mo86a()).f1700b;
            }
        }

        @Override // androidx.lifecycle.LiveData.AbstractC0245b
        /* renamed from: h */
        public final void mo857h() {
            this.f1672e.mo86a().mo872b(this);
        }

        @Override // androidx.lifecycle.LiveData.AbstractC0245b
        /* renamed from: i */
        public final boolean mo858i() {
            return ((C0255h) this.f1672e.mo86a()).f1700b.m875d(AbstractC0251d.c.STARTED);
        }
    }

    /* renamed from: androidx.lifecycle.LiveData$a */
    public class C0244a extends LiveData<T>.AbstractC0245b {
        public C0244a(LiveData liveData, InterfaceC0260m<? super T> interfaceC0260m) {
            super(interfaceC0260m);
        }

        @Override // androidx.lifecycle.LiveData.AbstractC0245b
        /* renamed from: i */
        public final boolean mo858i() {
            return true;
        }
    }

    /* renamed from: androidx.lifecycle.LiveData$b */
    public abstract class AbstractC0245b {

        /* renamed from: a */
        public final InterfaceC0260m<? super T> f1674a;

        /* renamed from: b */
        public boolean f1675b;

        /* renamed from: c */
        public int f1676c = -1;

        public AbstractC0245b(InterfaceC0260m<? super T> interfaceC0260m) {
            this.f1674a = interfaceC0260m;
        }

        /* renamed from: g */
        public final void m859g(boolean z5) {
            if (z5 == this.f1675b) {
                return;
            }
            this.f1675b = z5;
            LiveData liveData = LiveData.this;
            int i6 = z5 ? 1 : -1;
            int i7 = liveData.f1665c;
            liveData.f1665c = i6 + i7;
            if (!liveData.f1666d) {
                liveData.f1666d = true;
                while (true) {
                    try {
                        int i8 = liveData.f1665c;
                        if (i7 == i8) {
                            break;
                        }
                        boolean z6 = i7 == 0 && i8 > 0;
                        boolean z7 = i7 > 0 && i8 == 0;
                        if (z6) {
                            liveData.mo854e();
                        } else if (z7) {
                            liveData.mo855f();
                        }
                        i7 = i8;
                    } finally {
                        liveData.f1666d = false;
                    }
                }
            }
            if (this.f1675b) {
                LiveData.this.m852c(this);
            }
        }

        /* renamed from: h */
        public void mo857h() {
        }

        /* renamed from: i */
        public abstract boolean mo858i();
    }

    public LiveData() {
        Object obj = f1662j;
        this.f1668f = obj;
        this.f1667e = obj;
        this.f1669g = -1;
    }

    /* renamed from: a */
    public static void m850a(String str) {
        if (!C0939a.m2482r().m2483s()) {
            throw new IllegalStateException(C0174y.m491i("Cannot invoke ", str, " on a background thread"));
        }
    }

    /* renamed from: b */
    public final void m851b(LiveData<T>.AbstractC0245b abstractC0245b) {
        if (abstractC0245b.f1675b) {
            if (!abstractC0245b.mo858i()) {
                abstractC0245b.m859g(false);
                return;
            }
            int i6 = abstractC0245b.f1676c;
            int i7 = this.f1669g;
            if (i6 >= i7) {
                return;
            }
            abstractC0245b.f1676c = i7;
            InterfaceC0260m<? super T> interfaceC0260m = abstractC0245b.f1674a;
            Object obj = this.f1667e;
            DialogInterfaceOnCancelListenerC0221l.d dVar = (DialogInterfaceOnCancelListenerC0221l.d) interfaceC0260m;
            Objects.requireNonNull(dVar);
            if (((InterfaceC0254g) obj) != null) {
                DialogInterfaceOnCancelListenerC0221l dialogInterfaceOnCancelListenerC0221l = DialogInterfaceOnCancelListenerC0221l.this;
                if (dialogInterfaceOnCancelListenerC0221l.f1477h0) {
                    View m722V = dialogInterfaceOnCancelListenerC0221l.m722V();
                    if (m722V.getParent() != null) {
                        throw new IllegalStateException("DialogFragment can not be attached to a container view");
                    }
                    if (DialogInterfaceOnCancelListenerC0221l.this.f1481l0 != null) {
                        if (AbstractC0240x.m791K(3)) {
                            Log.d("FragmentManager", "DialogFragment " + dVar + " setting the content view on " + DialogInterfaceOnCancelListenerC0221l.this.f1481l0);
                        }
                        DialogInterfaceOnCancelListenerC0221l.this.f1481l0.setContentView(m722V);
                    }
                }
            }
        }
    }

    /* renamed from: c */
    public final void m852c(LiveData<T>.AbstractC0245b abstractC0245b) {
        if (this.f1670h) {
            this.f1671i = true;
            return;
        }
        this.f1670h = true;
        do {
            this.f1671i = false;
            if (abstractC0245b != null) {
                m851b(abstractC0245b);
                abstractC0245b = null;
            } else {
                C0955b<InterfaceC0260m<? super T>, LiveData<T>.AbstractC0245b>.d m2496d = this.f1664b.m2496d();
                while (m2496d.hasNext()) {
                    m851b((AbstractC0245b) ((Map.Entry) m2496d.next()).getValue());
                    if (this.f1671i) {
                        break;
                    }
                }
            }
        } while (this.f1671i);
        this.f1670h = false;
    }

    /* renamed from: d */
    public final void m853d(InterfaceC0260m<? super T> interfaceC0260m) {
        m850a("observeForever");
        C0244a c0244a = new C0244a(this, interfaceC0260m);
        LiveData<T>.AbstractC0245b mo2494i = this.f1664b.mo2494i(interfaceC0260m, c0244a);
        if (mo2494i instanceof LifecycleBoundObserver) {
            throw new IllegalArgumentException("Cannot add the same observer with different lifecycles");
        }
        if (mo2494i != null) {
            return;
        }
        c0244a.m859g(true);
    }

    /* renamed from: e */
    public void mo854e() {
    }

    /* renamed from: f */
    public void mo855f() {
    }

    /* renamed from: g */
    public void mo856g(InterfaceC0260m<? super T> interfaceC0260m) {
        m850a("removeObserver");
        LiveData<T>.AbstractC0245b mo2495j = this.f1664b.mo2495j(interfaceC0260m);
        if (mo2495j == null) {
            return;
        }
        mo2495j.mo857h();
        mo2495j.m859g(false);
    }
}
