package androidx.lifecycle;

import androidx.activity.result.C0052a;
import java.util.Objects;

/* renamed from: androidx.lifecycle.q */
/* loaded from: classes.dex */
public final class C0264q {

    /* renamed from: a */
    public final a f1713a;

    /* renamed from: b */
    public final C0265r f1714b;

    /* renamed from: androidx.lifecycle.q$a */
    public interface a {
        /* renamed from: a */
        AbstractC0263p mo627a();
    }

    /* renamed from: androidx.lifecycle.q$b */
    public static abstract class b extends c implements a {
        @Override // androidx.lifecycle.C0264q.a
        /* renamed from: a */
        public final AbstractC0263p mo627a() {
            throw new UnsupportedOperationException("create(String, Class<?>) must be called on implementaions of KeyedFactory");
        }

        /* renamed from: b */
        public abstract AbstractC0263p m895b();
    }

    /* renamed from: androidx.lifecycle.q$c */
    public static class c {
    }

    public C0264q(C0265r c0265r, a aVar) {
        this.f1713a = aVar;
        this.f1714b = c0265r;
    }

    /* renamed from: a */
    public final <T extends AbstractC0263p> T m894a(Class<T> cls) {
        String canonicalName = cls.getCanonicalName();
        if (canonicalName == null) {
            throw new IllegalArgumentException("Local and anonymous classes can not be ViewModels");
        }
        String m103g = C0052a.m103g("androidx.lifecycle.ViewModelProvider.DefaultKey:", canonicalName);
        T t = (T) this.f1714b.f1715a.get(m103g);
        if (cls.isInstance(t)) {
            Object obj = this.f1713a;
            if (obj instanceof c) {
                Objects.requireNonNull((c) obj);
            }
        } else {
            a aVar = this.f1713a;
            t = (T) (aVar instanceof b ? ((b) aVar).m895b() : aVar.mo627a());
            AbstractC0263p put = this.f1714b.f1715a.put(m103g, t);
            if (put != null) {
                put.mo625a();
            }
        }
        return t;
    }
}
