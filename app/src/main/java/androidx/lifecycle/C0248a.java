package androidx.lifecycle;

import androidx.activity.result.C0052a;
import androidx.lifecycle.AbstractC0251d;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/* renamed from: androidx.lifecycle.a */
/* loaded from: classes.dex */
public final class C0248a {

    /* renamed from: c */
    public static C0248a f1684c = new C0248a();

    /* renamed from: a */
    public final Map<Class<?>, a> f1685a = new HashMap();

    /* renamed from: b */
    public final Map<Class<?>, Boolean> f1686b = new HashMap();

    /* renamed from: androidx.lifecycle.a$a */
    public static class a {

        /* renamed from: a */
        public final Map<AbstractC0251d.b, List<b>> f1687a = new HashMap();

        /* renamed from: b */
        public final Map<b, AbstractC0251d.b> f1688b;

        /* JADX WARN: Type inference failed for: r2v0, types: [java.util.HashMap, java.util.Map<androidx.lifecycle.d$b, java.util.List<androidx.lifecycle.a$b>>] */
        /* JADX WARN: Type inference failed for: r3v0, types: [java.util.HashMap, java.util.Map<androidx.lifecycle.d$b, java.util.List<androidx.lifecycle.a$b>>] */
        public a(Map<b, AbstractC0251d.b> map) {
            this.f1688b = map;
            for (Map.Entry<b, AbstractC0251d.b> entry : map.entrySet()) {
                AbstractC0251d.b value = entry.getValue();
                List list = (List) this.f1687a.get(value);
                if (list == null) {
                    list = new ArrayList();
                    this.f1687a.put(value, list);
                }
                list.add(entry.getKey());
            }
        }

        /* renamed from: a */
        public static void m864a(List<b> list, InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar, Object obj) {
            if (list != null) {
                for (int size = list.size() - 1; size >= 0; size--) {
                    b bVar2 = list.get(size);
                    Objects.requireNonNull(bVar2);
                    try {
                        int i6 = bVar2.f1689a;
                        if (i6 == 0) {
                            bVar2.f1690b.invoke(obj, new Object[0]);
                        } else if (i6 == 1) {
                            bVar2.f1690b.invoke(obj, interfaceC0254g);
                        } else if (i6 == 2) {
                            bVar2.f1690b.invoke(obj, interfaceC0254g, bVar);
                        }
                    } catch (IllegalAccessException e6) {
                        throw new RuntimeException(e6);
                    } catch (InvocationTargetException e7) {
                        throw new RuntimeException("Failed to call observer method", e7.getCause());
                    }
                }
            }
        }
    }

    /* renamed from: androidx.lifecycle.a$b */
    public static final class b {

        /* renamed from: a */
        public final int f1689a;

        /* renamed from: b */
        public final Method f1690b;

        public b(int i6, Method method) {
            this.f1689a = i6;
            this.f1690b = method;
            method.setAccessible(true);
        }

        public final boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (!(obj instanceof b)) {
                return false;
            }
            b bVar = (b) obj;
            return this.f1689a == bVar.f1689a && this.f1690b.getName().equals(bVar.f1690b.getName());
        }

        public final int hashCode() {
            return this.f1690b.getName().hashCode() + (this.f1689a * 31);
        }
    }

    /* JADX WARN: Type inference failed for: r0v4, types: [java.util.HashMap, java.util.Map<java.lang.Class<?>, androidx.lifecycle.a$a>] */
    /* JADX WARN: Type inference failed for: r0v5, types: [java.util.HashMap, java.util.Map<java.lang.Class<?>, java.lang.Boolean>] */
    /* renamed from: a */
    public final a m861a(Class<?> cls, Method[] methodArr) {
        int i6;
        Class<?> superclass = cls.getSuperclass();
        HashMap hashMap = new HashMap();
        if (superclass != null) {
            hashMap.putAll(m862b(superclass).f1688b);
        }
        for (Class<?> cls2 : cls.getInterfaces()) {
            for (Map.Entry<b, AbstractC0251d.b> entry : m862b(cls2).f1688b.entrySet()) {
                m863c(hashMap, entry.getKey(), entry.getValue(), cls);
            }
        }
        if (methodArr == null) {
            try {
                methodArr = cls.getDeclaredMethods();
            } catch (NoClassDefFoundError e6) {
                throw new IllegalArgumentException("The observer class has some methods that use newer APIs which are not available in the current OS version. Lifecycles cannot access even other methods so you should make sure that your observer classes only access framework classes that are available in your min API level OR use lifecycle:compiler annotation processor.", e6);
            }
        }
        boolean z5 = false;
        for (Method method : methodArr) {
            InterfaceC0261n interfaceC0261n = (InterfaceC0261n) method.getAnnotation(InterfaceC0261n.class);
            if (interfaceC0261n != null) {
                Class<?>[] parameterTypes = method.getParameterTypes();
                if (parameterTypes.length <= 0) {
                    i6 = 0;
                } else {
                    if (!parameterTypes[0].isAssignableFrom(InterfaceC0254g.class)) {
                        throw new IllegalArgumentException("invalid parameter type. Must be one and instanceof LifecycleOwner");
                    }
                    i6 = 1;
                }
                AbstractC0251d.b value = interfaceC0261n.value();
                if (parameterTypes.length > 1) {
                    if (!parameterTypes[1].isAssignableFrom(AbstractC0251d.b.class)) {
                        throw new IllegalArgumentException("invalid parameter type. second arg must be an event");
                    }
                    if (value != AbstractC0251d.b.ON_ANY) {
                        throw new IllegalArgumentException("Second arg is supported only for ON_ANY value");
                    }
                    i6 = 2;
                }
                if (parameterTypes.length > 2) {
                    throw new IllegalArgumentException("cannot have more than 2 params");
                }
                m863c(hashMap, new b(i6, method), value, cls);
                z5 = true;
            }
        }
        a aVar = new a(hashMap);
        this.f1685a.put(cls, aVar);
        this.f1686b.put(cls, Boolean.valueOf(z5));
        return aVar;
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.HashMap, java.util.Map<java.lang.Class<?>, androidx.lifecycle.a$a>] */
    /* renamed from: b */
    public final a m862b(Class<?> cls) {
        a aVar = (a) this.f1685a.get(cls);
        return aVar != null ? aVar : m861a(cls, null);
    }

    /* renamed from: c */
    public final void m863c(Map<b, AbstractC0251d.b> map, b bVar, AbstractC0251d.b bVar2, Class<?> cls) {
        AbstractC0251d.b bVar3 = map.get(bVar);
        if (bVar3 == null || bVar2 == bVar3) {
            if (bVar3 == null) {
                map.put(bVar, bVar2);
                return;
            }
            return;
        }
        Method method = bVar.f1690b;
        StringBuilder m104h = C0052a.m104h("Method ");
        m104h.append(method.getName());
        m104h.append(" in ");
        m104h.append(cls.getName());
        m104h.append(" already declared with different @OnLifecycleEvent value: previous value ");
        m104h.append(bVar3);
        m104h.append(", new value ");
        m104h.append(bVar2);
        throw new IllegalArgumentException(m104h.toString());
    }
}
