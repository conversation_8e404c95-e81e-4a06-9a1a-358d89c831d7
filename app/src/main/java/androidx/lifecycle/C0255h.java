package androidx.lifecycle;

import android.annotation.SuppressLint;
import androidx.activity.result.C0052a;
import androidx.appcompat.widget.C0174y;
import androidx.lifecycle.AbstractC0251d;
import java.lang.ref.WeakReference;
import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import p056i.C0939a;
import p063j.C0954a;
import p063j.C0955b;

/* renamed from: androidx.lifecycle.h */
/* loaded from: classes.dex */
public final class C0255h extends AbstractC0251d {

    /* renamed from: c */
    public final WeakReference<InterfaceC0254g> f1701c;

    /* renamed from: a */
    public C0954a<InterfaceC0253f, a> f1699a = new C0954a<>();

    /* renamed from: d */
    public int f1702d = 0;

    /* renamed from: e */
    public boolean f1703e = false;

    /* renamed from: f */
    public boolean f1704f = false;

    /* renamed from: g */
    public ArrayList<AbstractC0251d.c> f1705g = new ArrayList<>();

    /* renamed from: b */
    public AbstractC0251d.c f1700b = AbstractC0251d.c.INITIALIZED;

    /* renamed from: h */
    public final boolean f1706h = true;

    /* renamed from: androidx.lifecycle.h$a */
    public static class a {

        /* renamed from: a */
        public AbstractC0251d.c f1707a;

        /* renamed from: b */
        public InterfaceC0252e f1708b;

        /* JADX WARN: Type inference failed for: r1v2, types: [java.util.HashMap, java.util.Map<java.lang.Class<?>, java.util.List<java.lang.reflect.Constructor<? extends androidx.lifecycle.c>>>] */
        public a(InterfaceC0253f interfaceC0253f, AbstractC0251d.c cVar) {
            InterfaceC0252e reflectiveGenericLifecycleObserver;
            Map<Class<?>, Integer> map = C0257j.f1709a;
            boolean z5 = interfaceC0253f instanceof InterfaceC0252e;
            boolean z6 = interfaceC0253f instanceof InterfaceC0249b;
            if (z5 && z6) {
                reflectiveGenericLifecycleObserver = new FullLifecycleObserverAdapter((InterfaceC0249b) interfaceC0253f, (InterfaceC0252e) interfaceC0253f);
            } else if (z6) {
                reflectiveGenericLifecycleObserver = new FullLifecycleObserverAdapter((InterfaceC0249b) interfaceC0253f, null);
            } else if (z5) {
                reflectiveGenericLifecycleObserver = (InterfaceC0252e) interfaceC0253f;
            } else {
                Class<?> cls = interfaceC0253f.getClass();
                if (C0257j.m888c(cls) == 2) {
                    List list = (List) C0257j.f1710b.get(cls);
                    if (list.size() == 1) {
                        reflectiveGenericLifecycleObserver = new SingleGeneratedAdapterObserver(C0257j.m886a((Constructor) list.get(0), interfaceC0253f));
                    } else {
                        InterfaceC0250c[] interfaceC0250cArr = new InterfaceC0250c[list.size()];
                        for (int i6 = 0; i6 < list.size(); i6++) {
                            interfaceC0250cArr[i6] = C0257j.m886a((Constructor) list.get(i6), interfaceC0253f);
                        }
                        reflectiveGenericLifecycleObserver = new CompositeGeneratedAdaptersObserver(interfaceC0250cArr);
                    }
                } else {
                    reflectiveGenericLifecycleObserver = new ReflectiveGenericLifecycleObserver(interfaceC0253f);
                }
            }
            this.f1708b = reflectiveGenericLifecycleObserver;
            this.f1707a = cVar;
        }

        /* renamed from: a */
        public final void m885a(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
            AbstractC0251d.c m874d = bVar.m874d();
            this.f1707a = C0255h.m876f(this.f1707a, m874d);
            this.f1708b.mo93e(interfaceC0254g, bVar);
            this.f1707a = m874d;
        }
    }

    public C0255h(InterfaceC0254g interfaceC0254g) {
        this.f1701c = new WeakReference<>(interfaceC0254g);
    }

    /* renamed from: f */
    public static AbstractC0251d.c m876f(AbstractC0251d.c cVar, AbstractC0251d.c cVar2) {
        return (cVar2 == null || cVar2.compareTo(cVar) >= 0) ? cVar : cVar2;
    }

    @Override // androidx.lifecycle.AbstractC0251d
    /* renamed from: a */
    public final void mo871a(InterfaceC0253f interfaceC0253f) {
        InterfaceC0254g interfaceC0254g;
        m878d("addObserver");
        AbstractC0251d.c cVar = this.f1700b;
        AbstractC0251d.c cVar2 = AbstractC0251d.c.DESTROYED;
        if (cVar != cVar2) {
            cVar2 = AbstractC0251d.c.INITIALIZED;
        }
        a aVar = new a(interfaceC0253f, cVar2);
        if (this.f1699a.mo2494i(interfaceC0253f, aVar) == null && (interfaceC0254g = this.f1701c.get()) != null) {
            boolean z5 = this.f1702d != 0 || this.f1703e;
            AbstractC0251d.c m877c = m877c(interfaceC0253f);
            this.f1702d++;
            while (aVar.f1707a.compareTo(m877c) < 0 && this.f1699a.contains(interfaceC0253f)) {
                m882i(aVar.f1707a);
                AbstractC0251d.b m873e = AbstractC0251d.b.m873e(aVar.f1707a);
                if (m873e == null) {
                    StringBuilder m104h = C0052a.m104h("no event up from ");
                    m104h.append(aVar.f1707a);
                    throw new IllegalStateException(m104h.toString());
                }
                aVar.m885a(interfaceC0254g, m873e);
                m881h();
                m877c = m877c(interfaceC0253f);
            }
            if (!z5) {
                m884k();
            }
            this.f1702d--;
        }
    }

    @Override // androidx.lifecycle.AbstractC0251d
    /* renamed from: b */
    public final void mo872b(InterfaceC0253f interfaceC0253f) {
        m878d("removeObserver");
        this.f1699a.mo2495j(interfaceC0253f);
    }

    /* renamed from: c */
    public final AbstractC0251d.c m877c(InterfaceC0253f interfaceC0253f) {
        C0954a<InterfaceC0253f, a> c0954a = this.f1699a;
        AbstractC0251d.c cVar = null;
        C0955b.c<InterfaceC0253f, a> cVar2 = c0954a.contains(interfaceC0253f) ? c0954a.f4716n.get(interfaceC0253f).f4724m : null;
        AbstractC0251d.c cVar3 = cVar2 != null ? cVar2.f4722k.f1707a : null;
        if (!this.f1705g.isEmpty()) {
            cVar = this.f1705g.get(r0.size() - 1);
        }
        return m876f(m876f(this.f1700b, cVar3), cVar);
    }

    @SuppressLint({"RestrictedApi"})
    /* renamed from: d */
    public final void m878d(String str) {
        if (this.f1706h && !C0939a.m2482r().m2483s()) {
            throw new IllegalStateException(C0174y.m491i("Method ", str, " must be called on the main thread"));
        }
    }

    /* renamed from: e */
    public final void m879e(AbstractC0251d.b bVar) {
        m878d("handleLifecycleEvent");
        m880g(bVar.m874d());
    }

    /* renamed from: g */
    public final void m880g(AbstractC0251d.c cVar) {
        if (this.f1700b == cVar) {
            return;
        }
        this.f1700b = cVar;
        if (this.f1703e || this.f1702d != 0) {
            this.f1704f = true;
            return;
        }
        this.f1703e = true;
        m884k();
        this.f1703e = false;
    }

    /* renamed from: h */
    public final void m881h() {
        this.f1705g.remove(r0.size() - 1);
    }

    /* renamed from: i */
    public final void m882i(AbstractC0251d.c cVar) {
        this.f1705g.add(cVar);
    }

    /* renamed from: j */
    public final void m883j() {
        AbstractC0251d.c cVar = AbstractC0251d.c.CREATED;
        m878d("setCurrentState");
        m880g(cVar);
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* renamed from: k */
    public final void m884k() {
        InterfaceC0254g interfaceC0254g = this.f1701c.get();
        if (interfaceC0254g == null) {
            throw new IllegalStateException("LifecycleOwner of this LifecycleRegistry is alreadygarbage collected. It is too late to change lifecycle state.");
        }
        while (true) {
            C0954a<InterfaceC0253f, a> c0954a = this.f1699a;
            boolean z5 = true;
            if (c0954a.f4720m != 0) {
                AbstractC0251d.c cVar = c0954a.f4717j.f4722k.f1707a;
                AbstractC0251d.c cVar2 = c0954a.f4718k.f4722k.f1707a;
                if (cVar != cVar2 || this.f1700b != cVar2) {
                    z5 = false;
                }
            }
            this.f1704f = false;
            if (z5) {
                return;
            }
            if (this.f1700b.compareTo(c0954a.f4717j.f4722k.f1707a) < 0) {
                C0954a<InterfaceC0253f, a> c0954a2 = this.f1699a;
                C0955b.b bVar = new C0955b.b(c0954a2.f4718k, c0954a2.f4717j);
                c0954a2.f4719l.put(bVar, Boolean.FALSE);
                while (bVar.hasNext() && !this.f1704f) {
                    Map.Entry entry = (Map.Entry) bVar.next();
                    a aVar = (a) entry.getValue();
                    while (aVar.f1707a.compareTo(this.f1700b) > 0 && !this.f1704f && this.f1699a.contains(entry.getKey())) {
                        int ordinal = aVar.f1707a.ordinal();
                        AbstractC0251d.b bVar2 = ordinal != 2 ? ordinal != 3 ? ordinal != 4 ? null : AbstractC0251d.b.ON_PAUSE : AbstractC0251d.b.ON_STOP : AbstractC0251d.b.ON_DESTROY;
                        if (bVar2 == null) {
                            StringBuilder m104h = C0052a.m104h("no event down from ");
                            m104h.append(aVar.f1707a);
                            throw new IllegalStateException(m104h.toString());
                        }
                        m882i(bVar2.m874d());
                        aVar.m885a(interfaceC0254g, bVar2);
                        m881h();
                    }
                }
            }
            C0955b.c<InterfaceC0253f, a> cVar3 = this.f1699a.f4718k;
            if (!this.f1704f && cVar3 != null && this.f1700b.compareTo(cVar3.f4722k.f1707a) > 0) {
                C0955b<InterfaceC0253f, a>.d m2496d = this.f1699a.m2496d();
                while (m2496d.hasNext() && !this.f1704f) {
                    Map.Entry entry2 = (Map.Entry) m2496d.next();
                    a aVar2 = (a) entry2.getValue();
                    while (aVar2.f1707a.compareTo(this.f1700b) < 0 && !this.f1704f && this.f1699a.contains(entry2.getKey())) {
                        m882i(aVar2.f1707a);
                        AbstractC0251d.b m873e = AbstractC0251d.b.m873e(aVar2.f1707a);
                        if (m873e == null) {
                            StringBuilder m104h2 = C0052a.m104h("no event up from ");
                            m104h2.append(aVar2.f1707a);
                            throw new IllegalStateException(m104h2.toString());
                        }
                        aVar2.m885a(interfaceC0254g, m873e);
                        m881h();
                    }
                }
            }
        }
    }
}
