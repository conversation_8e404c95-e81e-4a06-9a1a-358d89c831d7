package androidx.lifecycle;

import androidx.lifecycle.AbstractC0251d;

/* loaded from: classes.dex */
class FullLifecycleObserverAdapter implements InterfaceC0252e {

    /* renamed from: a */
    public final InterfaceC0249b f1659a;

    /* renamed from: b */
    public final InterfaceC0252e f1660b;

    /* renamed from: androidx.lifecycle.FullLifecycleObserverAdapter$a */
    public static /* synthetic */ class C0243a {

        /* renamed from: a */
        public static final /* synthetic */ int[] f1661a;

        static {
            int[] iArr = new int[AbstractC0251d.b.values().length];
            f1661a = iArr;
            try {
                iArr[AbstractC0251d.b.ON_CREATE.ordinal()] = 1;
            } catch (NoSuchFieldError unused) {
            }
            try {
                f1661a[AbstractC0251d.b.ON_START.ordinal()] = 2;
            } catch (NoSuchFieldError unused2) {
            }
            try {
                f1661a[AbstractC0251d.b.ON_RESUME.ordinal()] = 3;
            } catch (NoSuchFieldError unused3) {
            }
            try {
                f1661a[AbstractC0251d.b.ON_PAUSE.ordinal()] = 4;
            } catch (NoSuchFieldError unused4) {
            }
            try {
                f1661a[AbstractC0251d.b.ON_STOP.ordinal()] = 5;
            } catch (NoSuchFieldError unused5) {
            }
            try {
                f1661a[AbstractC0251d.b.ON_DESTROY.ordinal()] = 6;
            } catch (NoSuchFieldError unused6) {
            }
            try {
                f1661a[AbstractC0251d.b.ON_ANY.ordinal()] = 7;
            } catch (NoSuchFieldError unused7) {
            }
        }
    }

    public FullLifecycleObserverAdapter(InterfaceC0249b interfaceC0249b, InterfaceC0252e interfaceC0252e) {
        this.f1659a = interfaceC0249b;
        this.f1660b = interfaceC0252e;
    }

    @Override // androidx.lifecycle.InterfaceC0252e
    /* renamed from: e */
    public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
        switch (C0243a.f1661a[bVar.ordinal()]) {
            case 1:
                this.f1659a.onCreate();
                break;
            case 2:
                this.f1659a.m866b();
                break;
            case 3:
                this.f1659a.m868d();
                break;
            case 4:
                this.f1659a.m869f();
                break;
            case 5:
                this.f1659a.m865a();
                break;
            case 6:
                this.f1659a.m867c();
                break;
            case 7:
                throw new IllegalArgumentException("ON_ANY must not been send by anybody");
        }
        InterfaceC0252e interfaceC0252e = this.f1660b;
        if (interfaceC0252e != null) {
            interfaceC0252e.mo93e(interfaceC0254g, bVar);
        }
    }
}
