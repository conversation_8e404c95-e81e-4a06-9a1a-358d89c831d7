package androidx.lifecycle;

import java.util.HashMap;
import java.util.concurrent.Callable;

/* renamed from: androidx.lifecycle.k */
/* loaded from: classes.dex */
public final class C0258k {

    /* renamed from: a */
    public HashMap f1711a;

    public C0258k(int i6) {
        if (i6 != 1) {
            this.f1711a = new HashMap();
        } else {
            this.f1711a = new HashMap();
        }
    }

    /* renamed from: a */
    public final void m889a(String str, Callable callable) {
        this.f1711a.put(str, callable);
    }
}
