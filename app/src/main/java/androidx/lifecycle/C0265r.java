package androidx.lifecycle;

import java.io.Closeable;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/* renamed from: androidx.lifecycle.r */
/* loaded from: classes.dex */
public final class C0265r {

    /* renamed from: a */
    public final HashMap<String, AbstractC0263p> f1715a = new HashMap<>();

    /* JADX WARN: Type inference failed for: r3v0, types: [java.util.HashMap, java.util.Map<java.lang.String, java.lang.Object>] */
    /* renamed from: a */
    public final void m896a() {
        for (AbstractC0263p abstractC0263p : this.f1715a.values()) {
            Map<String, Object> map = abstractC0263p.f1712a;
            if (map != null) {
                synchronized (map) {
                    for (Object obj : abstractC0263p.f1712a.values()) {
                        if (obj instanceof Closeable) {
                            try {
                                ((Closeable) obj).close();
                            } catch (IOException e6) {
                                throw new RuntimeException(e6);
                            }
                        }
                    }
                }
            }
            abstractC0263p.mo625a();
        }
        this.f1715a.clear();
    }
}
