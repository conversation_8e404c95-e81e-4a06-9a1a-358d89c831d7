package androidx.lifecycle;

import androidx.lifecycle.AbstractC0251d;
import androidx.lifecycle.C0248a;
import java.util.List;

/* loaded from: classes.dex */
class ReflectiveGenericLifecycleObserver implements InterfaceC0252e {

    /* renamed from: a */
    public final Object f1678a;

    /* renamed from: b */
    public final C0248a.a f1679b;

    public ReflectiveGenericLifecycleObserver(Object obj) {
        this.f1678a = obj;
        this.f1679b = C0248a.f1684c.m862b(obj.getClass());
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [java.util.HashMap, java.util.Map<androidx.lifecycle.d$b, java.util.List<androidx.lifecycle.a$b>>] */
    /* JADX WARN: Type inference failed for: r2v0, types: [java.util.HashMap, java.util.Map<androidx.lifecycle.d$b, java.util.List<androidx.lifecycle.a$b>>] */
    @Override // androidx.lifecycle.InterfaceC0252e
    /* renamed from: e */
    public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
        C0248a.a aVar = this.f1679b;
        Object obj = this.f1678a;
        C0248a.a.m864a((List) aVar.f1687a.get(bVar), interfaceC0254g, bVar, obj);
        C0248a.a.m864a((List) aVar.f1687a.get(AbstractC0251d.b.ON_ANY), interfaceC0254g, bVar, obj);
    }
}
