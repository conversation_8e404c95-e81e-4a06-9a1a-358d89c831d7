package androidx.lifecycle;

import androidx.lifecycle.AbstractC0251d;
import androidx.savedstate.C0338a;
import androidx.savedstate.InterfaceC0340c;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Objects;

/* loaded from: classes.dex */
public final class SavedStateHandleController implements InterfaceC0252e {

    /* renamed from: a */
    public boolean f1680a;

    /* renamed from: androidx.lifecycle.SavedStateHandleController$1 */
    class C02461 implements InterfaceC0252e {

        /* renamed from: a */
        public final /* synthetic */ AbstractC0251d f1681a;

        /* renamed from: b */
        public final /* synthetic */ C0338a f1682b;

        @Override // androidx.lifecycle.InterfaceC0252e
        /* renamed from: e */
        public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
            if (bVar == AbstractC0251d.b.ON_START) {
                this.f1681a.mo872b(this);
                this.f1682b.m1363c();
            }
        }
    }

    /* renamed from: androidx.lifecycle.SavedStateHandleController$a */
    public static final class C0247a implements C0338a.a {
        /* JADX WARN: Type inference failed for: r3v7, types: [java.util.HashMap, java.util.Map<java.lang.String, java.lang.Object>] */
        @Override // androidx.savedstate.C0338a.a
        /* renamed from: a */
        public final void mo860a(InterfaceC0340c interfaceC0340c) {
            Object obj;
            boolean z5;
            if (!(interfaceC0340c instanceof InterfaceC0266s)) {
                throw new IllegalStateException("Internal error: OnRecreation should be registered only on componentsthat implement ViewModelStoreOwner");
            }
            C0265r mo90g = ((InterfaceC0266s) interfaceC0340c).mo90g();
            C0338a mo88c = interfaceC0340c.mo88c();
            Objects.requireNonNull(mo90g);
            Iterator it = new HashSet(mo90g.f1715a.keySet()).iterator();
            while (it.hasNext()) {
                AbstractC0263p abstractC0263p = mo90g.f1715a.get((String) it.next());
                AbstractC0251d mo86a = interfaceC0340c.mo86a();
                Map<String, Object> map = abstractC0263p.f1712a;
                if (map == null) {
                    obj = null;
                } else {
                    synchronized (map) {
                        obj = abstractC0263p.f1712a.get("androidx.lifecycle.savedstate.vm.tag");
                    }
                }
                SavedStateHandleController savedStateHandleController = (SavedStateHandleController) obj;
                if (savedStateHandleController != null && !(z5 = savedStateHandleController.f1680a)) {
                    if (z5) {
                        throw new IllegalStateException("Already attached to lifecycleOwner");
                    }
                    savedStateHandleController.f1680a = true;
                    mo86a.mo871a(savedStateHandleController);
                    throw null;
                }
            }
            if (new HashSet(mo90g.f1715a.keySet()).isEmpty()) {
                return;
            }
            mo88c.m1363c();
        }
    }

    @Override // androidx.lifecycle.InterfaceC0252e
    /* renamed from: e */
    public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
        if (bVar == AbstractC0251d.b.ON_DESTROY) {
            this.f1680a = false;
            interfaceC0254g.mo86a().mo872b(this);
        }
    }
}
