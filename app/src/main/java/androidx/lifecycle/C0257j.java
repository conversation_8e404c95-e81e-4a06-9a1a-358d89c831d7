package androidx.lifecycle;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/* renamed from: androidx.lifecycle.j */
/* loaded from: classes.dex */
public final class C0257j {

    /* renamed from: a */
    public static Map<Class<?>, Integer> f1709a = new HashMap();

    /* renamed from: b */
    public static Map<Class<?>, List<Constructor<? extends InterfaceC0250c>>> f1710b = new HashMap();

    /* renamed from: a */
    public static InterfaceC0250c m886a(Constructor<? extends InterfaceC0250c> constructor, Object obj) {
        try {
            return constructor.newInstance(obj);
        } catch (IllegalAccessException e6) {
            throw new RuntimeException(e6);
        } catch (InstantiationException e7) {
            throw new RuntimeException(e7);
        } catch (InvocationTargetException e8) {
            throw new RuntimeException(e8);
        }
    }

    /* renamed from: b */
    public static String m887b(String str) {
        return str.replace(".", "_") + "_LifecycleAdapter";
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.HashMap, java.util.Map<java.lang.Class<?>, java.lang.Integer>] */
    /* JADX WARN: Type inference failed for: r0v10 */
    /* JADX WARN: Type inference failed for: r0v11 */
    /* JADX WARN: Type inference failed for: r0v4, types: [java.util.HashMap, java.util.Map<java.lang.Class<?>, java.lang.Integer>] */
    /* JADX WARN: Type inference failed for: r0v8, types: [java.util.HashMap] */
    /* JADX WARN: Type inference failed for: r4v3, types: [java.util.HashMap, java.util.Map<java.lang.Class<?>, java.lang.Boolean>] */
    /* JADX WARN: Type inference failed for: r6v0, types: [java.util.HashMap, java.util.Map<java.lang.Class<?>, java.lang.Boolean>] */
    /* JADX WARN: Type inference failed for: r6v8, types: [java.util.HashMap, java.util.Map<java.lang.Class<?>, java.util.List<java.lang.reflect.Constructor<? extends androidx.lifecycle.c>>>] */
    /* JADX WARN: Type inference failed for: r9v6, types: [java.util.HashMap, java.util.Map<java.lang.Class<?>, java.util.List<java.lang.reflect.Constructor<? extends androidx.lifecycle.c>>>] */
    /* renamed from: c */
    public static int m888c(Class<?> cls) {
        Constructor<?> constructor;
        boolean z5;
        List arrayList;
        ?? r0;
        Integer num = (Integer) f1709a.get(cls);
        if (num != null) {
            return num.intValue();
        }
        int i6 = 1;
        if (cls.getCanonicalName() != null) {
            try {
                Package r42 = cls.getPackage();
                String canonicalName = cls.getCanonicalName();
                String name = r42 != null ? r42.getName() : "";
                if (!name.isEmpty()) {
                    canonicalName = canonicalName.substring(name.length() + 1);
                }
                String m887b = m887b(canonicalName);
                if (!name.isEmpty()) {
                    m887b = name + "." + m887b;
                }
                constructor = Class.forName(m887b).getDeclaredConstructor(cls);
                if (!constructor.isAccessible()) {
                    constructor.setAccessible(true);
                }
            } catch (ClassNotFoundException unused) {
                constructor = null;
            } catch (NoSuchMethodException e6) {
                throw new RuntimeException(e6);
            }
            if (constructor != null) {
                Map<Class<?>, List<Constructor<? extends InterfaceC0250c>>> map = f1710b;
                arrayList = Collections.singletonList(constructor);
                r0 = map;
            } else {
                C0248a c0248a = C0248a.f1684c;
                Boolean bool = (Boolean) c0248a.f1686b.get(cls);
                if (bool != null) {
                    z5 = bool.booleanValue();
                } else {
                    try {
                        Method[] declaredMethods = cls.getDeclaredMethods();
                        int length = declaredMethods.length;
                        int i7 = 0;
                        while (true) {
                            if (i7 >= length) {
                                c0248a.f1686b.put(cls, Boolean.FALSE);
                                z5 = false;
                                break;
                            }
                            if (((InterfaceC0261n) declaredMethods[i7].getAnnotation(InterfaceC0261n.class)) != null) {
                                c0248a.m861a(cls, declaredMethods);
                                z5 = true;
                                break;
                            }
                            i7++;
                        }
                    } catch (NoClassDefFoundError e7) {
                        throw new IllegalArgumentException("The observer class has some methods that use newer APIs which are not available in the current OS version. Lifecycles cannot access even other methods so you should make sure that your observer classes only access framework classes that are available in your min API level OR use lifecycle:compiler annotation processor.", e7);
                    }
                }
                if (!z5) {
                    Class<? super Object> superclass = cls.getSuperclass();
                    if (superclass != null && InterfaceC0253f.class.isAssignableFrom(superclass)) {
                        arrayList = m888c(superclass) != 1 ? new ArrayList((Collection) f1710b.get(superclass)) : null;
                    }
                    Class<?>[] interfaces = cls.getInterfaces();
                    int length2 = interfaces.length;
                    int i8 = 0;
                    while (true) {
                        if (i8 < length2) {
                            Class<?> cls2 = interfaces[i8];
                            if (cls2 != null && InterfaceC0253f.class.isAssignableFrom(cls2)) {
                                if (m888c(cls2) == 1) {
                                    break;
                                }
                                if (arrayList == null) {
                                    arrayList = new ArrayList();
                                }
                                arrayList.addAll((Collection) f1710b.get(cls2));
                            }
                            i8++;
                        } else if (arrayList != null) {
                            r0 = f1710b;
                        }
                    }
                }
            }
            r0.put(cls, arrayList);
            i6 = 2;
        }
        f1709a.put(cls, Integer.valueOf(i6));
        return i6;
    }
}
