package androidx.lifecycle;

import androidx.lifecycle.AbstractC0251d;
import java.util.HashMap;

/* loaded from: classes.dex */
class CompositeGeneratedAdaptersObserver implements InterfaceC0252e {

    /* renamed from: a */
    public final InterfaceC0250c[] f1658a;

    public CompositeGeneratedAdaptersObserver(InterfaceC0250c[] interfaceC0250cArr) {
        this.f1658a = interfaceC0250cArr;
    }

    @Override // androidx.lifecycle.InterfaceC0252e
    /* renamed from: e */
    public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
        new HashMap();
        for (InterfaceC0250c interfaceC0250c : this.f1658a) {
            interfaceC0250c.m870a();
        }
        for (InterfaceC0250c interfaceC0250c2 : this.f1658a) {
            interfaceC0250c2.m870a();
        }
    }
}
