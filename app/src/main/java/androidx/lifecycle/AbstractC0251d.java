package androidx.lifecycle;

import java.util.concurrent.atomic.AtomicReference;

/* renamed from: androidx.lifecycle.d */
/* loaded from: classes.dex */
public abstract class AbstractC0251d {

    /* renamed from: androidx.lifecycle.d$a */
    public static /* synthetic */ class a {

        /* renamed from: a */
        public static final /* synthetic */ int[] f1691a;

        /* renamed from: b */
        public static final /* synthetic */ int[] f1692b;

        static {
            int[] iArr = new int[b.values().length];
            f1692b = iArr;
            try {
                iArr[b.ON_CREATE.ordinal()] = 1;
            } catch (NoSuchFieldError unused) {
            }
            try {
                f1692b[b.ON_STOP.ordinal()] = 2;
            } catch (NoSuchFieldError unused2) {
            }
            try {
                f1692b[b.ON_START.ordinal()] = 3;
            } catch (NoSuchFieldError unused3) {
            }
            try {
                f1692b[b.ON_PAUSE.ordinal()] = 4;
            } catch (NoSuchFieldError unused4) {
            }
            try {
                f1692b[b.ON_RESUME.ordinal()] = 5;
            } catch (NoSuchFieldError unused5) {
            }
            try {
                f1692b[b.ON_DESTROY.ordinal()] = 6;
            } catch (NoSuchFieldError unused6) {
            }
            try {
                f1692b[b.ON_ANY.ordinal()] = 7;
            } catch (NoSuchFieldError unused7) {
            }
            int[] iArr2 = new int[c.values().length];
            f1691a = iArr2;
            try {
                iArr2[2] = 1;
            } catch (NoSuchFieldError unused8) {
            }
            try {
                f1691a[3] = 2;
            } catch (NoSuchFieldError unused9) {
            }
            try {
                f1691a[4] = 3;
            } catch (NoSuchFieldError unused10) {
            }
            try {
                f1691a[0] = 4;
            } catch (NoSuchFieldError unused11) {
            }
            try {
                f1691a[1] = 5;
            } catch (NoSuchFieldError unused12) {
            }
        }
    }

    /* renamed from: androidx.lifecycle.d$b */
    public enum b {
        ON_CREATE,
        ON_START,
        ON_RESUME,
        ON_PAUSE,
        ON_STOP,
        ON_DESTROY,
        ON_ANY;

        /* renamed from: e */
        public static b m873e(c cVar) {
            int ordinal = cVar.ordinal();
            if (ordinal == 1) {
                return ON_CREATE;
            }
            if (ordinal == 2) {
                return ON_START;
            }
            if (ordinal != 3) {
                return null;
            }
            return ON_RESUME;
        }

        /* renamed from: d */
        public final c m874d() {
            switch (a.f1692b[ordinal()]) {
                case 1:
                case 2:
                    return c.CREATED;
                case 3:
                case 4:
                    return c.STARTED;
                case 5:
                    return c.RESUMED;
                case 6:
                    return c.DESTROYED;
                default:
                    throw new IllegalArgumentException(this + " has no target state");
            }
        }
    }

    /* renamed from: androidx.lifecycle.d$c */
    public enum c {
        DESTROYED,
        INITIALIZED,
        CREATED,
        STARTED,
        RESUMED;

        /* renamed from: d */
        public final boolean m875d(c cVar) {
            return compareTo(cVar) >= 0;
        }
    }

    public AbstractC0251d() {
        new AtomicReference();
    }

    /* renamed from: a */
    public abstract void mo871a(InterfaceC0253f interfaceC0253f);

    /* renamed from: b */
    public abstract void mo872b(InterfaceC0253f interfaceC0253f);
}
