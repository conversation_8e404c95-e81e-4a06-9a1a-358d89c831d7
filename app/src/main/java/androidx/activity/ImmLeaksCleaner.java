package androidx.activity;

import android.app.Activity;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import androidx.lifecycle.AbstractC0251d;
import androidx.lifecycle.InterfaceC0252e;
import androidx.lifecycle.InterfaceC0254g;
import java.lang.reflect.Field;

/* loaded from: classes.dex */
final class ImmLeaksCleaner implements InterfaceC0252e {

    /* renamed from: b */
    public static int f194b;

    /* renamed from: c */
    public static Field f195c;

    /* renamed from: d */
    public static Field f196d;

    /* renamed from: e */
    public static Field f197e;

    /* renamed from: a */
    public Activity f198a;

    @Override // androidx.lifecycle.InterfaceC0252e
    /* renamed from: e */
    public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
        if (bVar != AbstractC0251d.b.ON_DESTROY) {
            return;
        }
        if (f194b == 0) {
            try {
                f194b = 2;
                Field declaredField = InputMethodManager.class.getDeclaredField("mServedView");
                f196d = declaredField;
                declaredField.setAccessible(true);
                Field declaredField2 = InputMethodManager.class.getDeclaredField("mNextServedView");
                f197e = declaredField2;
                declaredField2.setAccessible(true);
                Field declaredField3 = InputMethodManager.class.getDeclaredField("mH");
                f195c = declaredField3;
                declaredField3.setAccessible(true);
                f194b = 1;
            } catch (NoSuchFieldException unused) {
            }
        }
        if (f194b == 1) {
            InputMethodManager inputMethodManager = (InputMethodManager) this.f198a.getSystemService("input_method");
            try {
                Object obj = f195c.get(inputMethodManager);
                if (obj == null) {
                    return;
                }
                synchronized (obj) {
                    try {
                        try {
                            View view = (View) f196d.get(inputMethodManager);
                            if (view == null) {
                                return;
                            }
                            if (view.isAttachedToWindow()) {
                                return;
                            }
                            try {
                                f197e.set(inputMethodManager, null);
                                inputMethodManager.isActive();
                            } catch (IllegalAccessException unused2) {
                            }
                        } catch (ClassCastException unused3) {
                        } catch (IllegalAccessException unused4) {
                        }
                    } catch (Throwable th) {
                        throw th;
                    }
                }
            } catch (IllegalAccessException unused5) {
            }
        }
    }
}
