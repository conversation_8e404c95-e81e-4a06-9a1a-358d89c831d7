package androidx.activity;

import android.annotation.SuppressLint;
import androidx.lifecycle.AbstractC0251d;
import androidx.lifecycle.C0255h;
import androidx.lifecycle.InterfaceC0252e;
import androidx.lifecycle.InterfaceC0254g;
import java.util.ArrayDeque;
import java.util.Iterator;

/* loaded from: classes.dex */
public final class OnBackPressedDispatcher {

    /* renamed from: a */
    public final Runnable f199a;

    /* renamed from: b */
    public final ArrayDeque<AbstractC0050b> f200b = new ArrayDeque<>();

    public class LifecycleOnBackPressedCancellable implements InterfaceC0252e, InterfaceC0049a {

        /* renamed from: a */
        public final AbstractC0251d f201a;

        /* renamed from: b */
        public final AbstractC0050b f202b;

        /* renamed from: c */
        public C0048a f203c;

        public LifecycleOnBackPressedCancellable(AbstractC0251d abstractC0251d, AbstractC0050b abstractC0050b) {
            this.f201a = abstractC0251d;
            this.f202b = abstractC0050b;
            abstractC0251d.mo871a(this);
        }

        @Override // androidx.activity.InterfaceC0049a
        public final void cancel() {
            this.f201a.mo872b(this);
            this.f202b.f208b.remove(this);
            C0048a c0048a = this.f203c;
            if (c0048a != null) {
                c0048a.cancel();
                this.f203c = null;
            }
        }

        @Override // androidx.lifecycle.InterfaceC0252e
        /* renamed from: e */
        public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
            if (bVar == AbstractC0251d.b.ON_START) {
                OnBackPressedDispatcher onBackPressedDispatcher = OnBackPressedDispatcher.this;
                AbstractC0050b abstractC0050b = this.f202b;
                onBackPressedDispatcher.f200b.add(abstractC0050b);
                C0048a c0048a = onBackPressedDispatcher.new C0048a(abstractC0050b);
                abstractC0050b.f208b.add(c0048a);
                this.f203c = c0048a;
                return;
            }
            if (bVar != AbstractC0251d.b.ON_STOP) {
                if (bVar == AbstractC0251d.b.ON_DESTROY) {
                    cancel();
                }
            } else {
                C0048a c0048a2 = this.f203c;
                if (c0048a2 != null) {
                    c0048a2.cancel();
                }
            }
        }
    }

    /* renamed from: androidx.activity.OnBackPressedDispatcher$a */
    public class C0048a implements InterfaceC0049a {

        /* renamed from: a */
        public final AbstractC0050b f205a;

        public C0048a(AbstractC0050b abstractC0050b) {
            this.f205a = abstractC0050b;
        }

        @Override // androidx.activity.InterfaceC0049a
        public final void cancel() {
            OnBackPressedDispatcher.this.f200b.remove(this.f205a);
            this.f205a.f208b.remove(this);
        }
    }

    public OnBackPressedDispatcher(Runnable runnable) {
        this.f199a = runnable;
    }

    @SuppressLint({"LambdaLast"})
    /* renamed from: a */
    public final void m94a(InterfaceC0254g interfaceC0254g, AbstractC0050b abstractC0050b) {
        AbstractC0251d mo86a = interfaceC0254g.mo86a();
        if (((C0255h) mo86a).f1700b == AbstractC0251d.c.DESTROYED) {
            return;
        }
        abstractC0050b.f208b.add(new LifecycleOnBackPressedCancellable(mo86a, abstractC0050b));
    }

    /* renamed from: b */
    public final void m95b() {
        Iterator<AbstractC0050b> descendingIterator = this.f200b.descendingIterator();
        while (descendingIterator.hasNext()) {
            AbstractC0050b next = descendingIterator.next();
            if (next.f207a) {
                next.mo96a();
                return;
            }
        }
        Runnable runnable = this.f199a;
        if (runnable != null) {
            runnable.run();
        }
    }
}
