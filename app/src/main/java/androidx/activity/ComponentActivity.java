package androidx.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.IntentSender;
import android.os.Bundle;
import android.os.Trace;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import androidx.activity.result.AbstractC0056e;
import androidx.activity.result.InterfaceC0057f;
import androidx.lifecycle.AbstractC0251d;
import androidx.lifecycle.C0255h;
import androidx.lifecycle.C0265r;
import androidx.lifecycle.FragmentC0262o;
import androidx.lifecycle.InterfaceC0252e;
import androidx.lifecycle.InterfaceC0254g;
import androidx.lifecycle.InterfaceC0266s;
import androidx.savedstate.C0338a;
import androidx.savedstate.C0339b;
import androidx.savedstate.InterfaceC0340c;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;
import p000a.C0000a;
import p000a.InterfaceC0001b;
import p008b0.C0385m;
import p123s0.C1333a;
import p135u.ActivityC1410d;
import p153w3.C1798e;

/* loaded from: classes.dex */
public class ComponentActivity extends ActivityC1410d implements InterfaceC0266s, InterfaceC0340c, InterfaceC0051c, InterfaceC0057f {

    /* renamed from: k */
    public final C0000a f183k = new C0000a();

    /* renamed from: l */
    public final C0255h f184l;

    /* renamed from: m */
    public final C0339b f185m;

    /* renamed from: n */
    public C0265r f186n;

    /* renamed from: o */
    public final OnBackPressedDispatcher f187o;

    /* renamed from: p */
    public final C0046b f188p;

    /* renamed from: androidx.activity.ComponentActivity$3 */
    public class C00423 implements InterfaceC0252e {
        public C00423() {
        }

        @Override // androidx.lifecycle.InterfaceC0252e
        /* renamed from: e */
        public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
            if (bVar == AbstractC0251d.b.ON_STOP) {
                Window window = ComponentActivity.this.getWindow();
                View peekDecorView = window != null ? window.peekDecorView() : null;
                if (peekDecorView != null) {
                    peekDecorView.cancelPendingInputEvents();
                }
            }
        }
    }

    /* renamed from: androidx.activity.ComponentActivity$4 */
    public class C00434 implements InterfaceC0252e {
        public C00434() {
        }

        @Override // androidx.lifecycle.InterfaceC0252e
        /* renamed from: e */
        public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
            if (bVar == AbstractC0251d.b.ON_DESTROY) {
                ComponentActivity.this.f183k.f1b = null;
                if (ComponentActivity.this.isChangingConfigurations()) {
                    return;
                }
                ComponentActivity.this.mo90g().m896a();
            }
        }
    }

    /* renamed from: androidx.activity.ComponentActivity$5 */
    public class C00445 implements InterfaceC0252e {
        public C00445() {
        }

        @Override // androidx.lifecycle.InterfaceC0252e
        /* renamed from: e */
        public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
            ComponentActivity.this.m91l();
            ComponentActivity.this.f184l.mo872b(this);
        }
    }

    /* renamed from: androidx.activity.ComponentActivity$a */
    public class RunnableC0045a implements Runnable {
        public RunnableC0045a() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            try {
                ComponentActivity.super.onBackPressed();
            } catch (IllegalStateException e6) {
                if (!TextUtils.equals(e6.getMessage(), "Can not perform this action after onSaveInstanceState")) {
                    throw e6;
                }
            }
        }
    }

    /* renamed from: androidx.activity.ComponentActivity$b */
    public class C0046b extends AbstractC0056e {
    }

    /* renamed from: androidx.activity.ComponentActivity$c */
    public static final class C0047c {

        /* renamed from: a */
        public C0265r f193a;
    }

    public ComponentActivity() {
        C0255h c0255h = new C0255h(this);
        this.f184l = c0255h;
        this.f185m = new C0339b(this);
        this.f187o = new OnBackPressedDispatcher(new RunnableC0045a());
        new AtomicInteger();
        this.f188p = new C0046b();
        c0255h.mo871a(new InterfaceC0252e() { // from class: androidx.activity.ComponentActivity.3
            public C00423() {
            }

            @Override // androidx.lifecycle.InterfaceC0252e
            /* renamed from: e */
            public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
                if (bVar == AbstractC0251d.b.ON_STOP) {
                    Window window = ComponentActivity.this.getWindow();
                    View peekDecorView = window != null ? window.peekDecorView() : null;
                    if (peekDecorView != null) {
                        peekDecorView.cancelPendingInputEvents();
                    }
                }
            }
        });
        c0255h.mo871a(new InterfaceC0252e() { // from class: androidx.activity.ComponentActivity.4
            public C00434() {
            }

            @Override // androidx.lifecycle.InterfaceC0252e
            /* renamed from: e */
            public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
                if (bVar == AbstractC0251d.b.ON_DESTROY) {
                    ComponentActivity.this.f183k.f1b = null;
                    if (ComponentActivity.this.isChangingConfigurations()) {
                        return;
                    }
                    ComponentActivity.this.mo90g().m896a();
                }
            }
        });
        c0255h.mo871a(new InterfaceC0252e() { // from class: androidx.activity.ComponentActivity.5
            public C00445() {
            }

            @Override // androidx.lifecycle.InterfaceC0252e
            /* renamed from: e */
            public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
                ComponentActivity.this.m91l();
                ComponentActivity.this.f184l.mo872b(this);
            }
        });
    }

    @Override // p135u.ActivityC1410d, androidx.lifecycle.InterfaceC0254g
    /* renamed from: a */
    public final AbstractC0251d mo86a() {
        return this.f184l;
    }

    @Override // android.app.Activity
    public void addContentView(@SuppressLint({"UnknownNullness", "MissingNullability"}) View view, @SuppressLint({"UnknownNullness", "MissingNullability"}) ViewGroup.LayoutParams layoutParams) {
        m92m();
        super.addContentView(view, layoutParams);
    }

    @Override // androidx.activity.InterfaceC0051c
    /* renamed from: b */
    public final OnBackPressedDispatcher mo87b() {
        return this.f187o;
    }

    @Override // androidx.savedstate.InterfaceC0340c
    /* renamed from: c */
    public final C0338a mo88c() {
        return this.f185m.f2181b;
    }

    @Override // androidx.activity.result.InterfaceC0057f
    /* renamed from: e */
    public final AbstractC0056e mo89e() {
        return this.f188p;
    }

    @Override // androidx.lifecycle.InterfaceC0266s
    /* renamed from: g */
    public final C0265r mo90g() {
        if (getApplication() == null) {
            throw new IllegalStateException("Your activity is not yet attached to the Application instance. You can't request ViewModel before onCreate call.");
        }
        m91l();
        return this.f186n;
    }

    /* renamed from: l */
    public final void m91l() {
        if (this.f186n == null) {
            C0047c c0047c = (C0047c) getLastNonConfigurationInstance();
            if (c0047c != null) {
                this.f186n = c0047c.f193a;
            }
            if (this.f186n == null) {
                this.f186n = new C0265r();
            }
        }
    }

    /* renamed from: m */
    public final void m92m() {
        C0385m.m1423o(getWindow().getDecorView(), this);
        C1798e.m4518J(getWindow().getDecorView(), this);
        C0385m.m1424p(getWindow().getDecorView(), this);
    }

    @Override // android.app.Activity
    @Deprecated
    public void onActivityResult(int i6, int i7, Intent intent) {
        if (this.f188p.m126b(i6, i7, intent)) {
            return;
        }
        super.onActivityResult(i6, i7, intent);
    }

    @Override // android.app.Activity
    public final void onBackPressed() {
        this.f187o.m95b();
    }

    /* JADX WARN: Type inference failed for: r0v2, types: [java.util.Set<a.b>, java.util.concurrent.CopyOnWriteArraySet] */
    @Override // p135u.ActivityC1410d, android.app.Activity
    public void onCreate(Bundle bundle) {
        this.f185m.m1364a(bundle);
        C0000a c0000a = this.f183k;
        c0000a.f1b = this;
        Iterator it = c0000a.f0a.iterator();
        while (it.hasNext()) {
            ((InterfaceC0001b) it.next()).mo0a();
        }
        super.onCreate(bundle);
        C0046b c0046b = this.f188p;
        Objects.requireNonNull(c0046b);
        if (bundle != null) {
            ArrayList<Integer> integerArrayList = bundle.getIntegerArrayList("KEY_COMPONENT_ACTIVITY_REGISTERED_RCS");
            ArrayList<String> stringArrayList = bundle.getStringArrayList("KEY_COMPONENT_ACTIVITY_REGISTERED_KEYS");
            if (stringArrayList != null && integerArrayList != null) {
                int size = stringArrayList.size();
                for (int i6 = 0; i6 < size; i6++) {
                    c0046b.m125a(integerArrayList.get(i6).intValue(), stringArrayList.get(i6));
                }
                c0046b.f215e = bundle.getStringArrayList("KEY_COMPONENT_ACTIVITY_LAUNCHED_KEYS");
                c0046b.f211a = (Random) bundle.getSerializable("KEY_COMPONENT_ACTIVITY_RANDOM_OBJECT");
                c0046b.f218h.putAll(bundle.getBundle("KEY_COMPONENT_ACTIVITY_PENDING_RESULT"));
            }
        }
        FragmentC0262o.m892c(this);
    }

    @Override // android.app.Activity
    @Deprecated
    public void onRequestPermissionsResult(int i6, String[] strArr, int[] iArr) {
        if (this.f188p.m126b(i6, -1, new Intent().putExtra("androidx.activity.result.contract.extra.PERMISSIONS", strArr).putExtra("androidx.activity.result.contract.extra.PERMISSION_GRANT_RESULTS", iArr))) {
            return;
        }
        super.onRequestPermissionsResult(i6, strArr, iArr);
    }

    @Override // android.app.Activity
    public final Object onRetainNonConfigurationInstance() {
        C0047c c0047c;
        C0265r c0265r = this.f186n;
        if (c0265r == null && (c0047c = (C0047c) getLastNonConfigurationInstance()) != null) {
            c0265r = c0047c.f193a;
        }
        if (c0265r == null) {
            return null;
        }
        C0047c c0047c2 = new C0047c();
        c0047c2.f193a = c0265r;
        return c0047c2;
    }

    /* JADX WARN: Type inference failed for: r2v0, types: [java.util.HashMap, java.util.Map<java.lang.Integer, java.lang.String>] */
    /* JADX WARN: Type inference failed for: r2v3, types: [java.util.HashMap, java.util.Map<java.lang.Integer, java.lang.String>] */
    @Override // p135u.ActivityC1410d, android.app.Activity
    public final void onSaveInstanceState(Bundle bundle) {
        C0255h c0255h = this.f184l;
        if (c0255h instanceof C0255h) {
            c0255h.m883j();
        }
        super.onSaveInstanceState(bundle);
        this.f185m.m1365b(bundle);
        C0046b c0046b = this.f188p;
        Objects.requireNonNull(c0046b);
        bundle.putIntegerArrayList("KEY_COMPONENT_ACTIVITY_REGISTERED_RCS", new ArrayList<>(c0046b.f212b.keySet()));
        bundle.putStringArrayList("KEY_COMPONENT_ACTIVITY_REGISTERED_KEYS", new ArrayList<>(c0046b.f212b.values()));
        bundle.putStringArrayList("KEY_COMPONENT_ACTIVITY_LAUNCHED_KEYS", new ArrayList<>(c0046b.f215e));
        bundle.putBundle("KEY_COMPONENT_ACTIVITY_PENDING_RESULT", (Bundle) c0046b.f218h.clone());
        bundle.putSerializable("KEY_COMPONENT_ACTIVITY_RANDOM_OBJECT", c0046b.f211a);
    }

    @Override // android.app.Activity
    public final void reportFullyDrawn() {
        try {
            if (C1333a.m3268a()) {
                Trace.beginSection("reportFullyDrawn() for " + getComponentName());
            }
            super.reportFullyDrawn();
            Trace.endSection();
        } catch (Throwable th) {
            Trace.endSection();
            throw th;
        }
    }

    @Override // android.app.Activity
    public void setContentView(int i6) {
        m92m();
        super.setContentView(i6);
    }

    @Override // android.app.Activity
    public void setContentView(@SuppressLint({"UnknownNullness", "MissingNullability"}) View view) {
        m92m();
        super.setContentView(view);
    }

    @Override // android.app.Activity
    public void setContentView(@SuppressLint({"UnknownNullness", "MissingNullability"}) View view, @SuppressLint({"UnknownNullness", "MissingNullability"}) ViewGroup.LayoutParams layoutParams) {
        m92m();
        super.setContentView(view, layoutParams);
    }

    @Override // android.app.Activity
    @Deprecated
    public final void startActivityForResult(@SuppressLint({"UnknownNullness"}) Intent intent, int i6) {
        super.startActivityForResult(intent, i6);
    }

    @Override // android.app.Activity
    @Deprecated
    public final void startActivityForResult(@SuppressLint({"UnknownNullness"}) Intent intent, int i6, Bundle bundle) {
        super.startActivityForResult(intent, i6, bundle);
    }

    @Override // android.app.Activity
    @Deprecated
    public final void startIntentSenderForResult(@SuppressLint({"UnknownNullness"}) IntentSender intentSender, int i6, Intent intent, int i7, int i8, int i9) {
        super.startIntentSenderForResult(intentSender, i6, intent, i7, i8, i9);
    }

    @Override // android.app.Activity
    @Deprecated
    public final void startIntentSenderForResult(@SuppressLint({"UnknownNullness"}) IntentSender intentSender, int i6, Intent intent, int i7, int i8, int i9, Bundle bundle) {
        super.startIntentSenderForResult(intentSender, i6, intent, i7, i8, i9, bundle);
    }
}
