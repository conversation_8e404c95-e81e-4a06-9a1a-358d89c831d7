package androidx.activity.result;

import androidx.lifecycle.AbstractC0251d;
import androidx.lifecycle.InterfaceC0252e;
import androidx.lifecycle.InterfaceC0254g;

/* loaded from: classes.dex */
class ActivityResultRegistry$1 implements InterfaceC0252e {
    @Override // androidx.lifecycle.InterfaceC0252e
    /* renamed from: e */
    public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
        if (AbstractC0251d.b.ON_START.equals(bVar)) {
            throw null;
        }
        if (AbstractC0251d.b.ON_STOP.equals(bVar)) {
            throw null;
        }
        if (AbstractC0251d.b.ON_DESTROY.equals(bVar)) {
            throw null;
        }
    }
}
