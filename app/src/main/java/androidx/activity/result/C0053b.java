package androidx.activity.result;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Parcel;
import android.os.Parcelable;

@SuppressLint({"BanParcelableUsage"})
/* renamed from: androidx.activity.result.b */
/* loaded from: classes.dex */
public final class C0053b implements Parcelable {
    public static final Parcelable.Creator<C0053b> CREATOR = new a();

    /* renamed from: j */
    public final int f209j;

    /* renamed from: k */
    public final Intent f210k;

    /* renamed from: androidx.activity.result.b$a */
    public class a implements Parcelable.Creator<C0053b> {
        @Override // android.os.Parcelable.Creator
        public final C0053b createFromParcel(Parcel parcel) {
            return new C0053b(parcel);
        }

        @Override // android.os.Parcelable.Creator
        public final C0053b[] newArray(int i6) {
            return new C0053b[i6];
        }
    }

    public C0053b(int i6, Intent intent) {
        this.f209j = i6;
        this.f210k = intent;
    }

    public C0053b(Parcel parcel) {
        this.f209j = parcel.readInt();
        this.f210k = parcel.readInt() == 0 ? null : (Intent) Intent.CREATOR.createFromParcel(parcel);
    }

    @Override // android.os.Parcelable
    public final int describeContents() {
        return 0;
    }

    public final String toString() {
        StringBuilder m104h = C0052a.m104h("ActivityResult{resultCode=");
        int i6 = this.f209j;
        m104h.append(i6 != -1 ? i6 != 0 ? String.valueOf(i6) : "RESULT_CANCELED" : "RESULT_OK");
        m104h.append(", data=");
        m104h.append(this.f210k);
        m104h.append('}');
        return m104h.toString();
    }

    @Override // android.os.Parcelable
    public final void writeToParcel(Parcel parcel, int i6) {
        parcel.writeInt(this.f209j);
        parcel.writeInt(this.f210k == null ? 0 : 1);
        Intent intent = this.f210k;
        if (intent != null) {
            intent.writeToParcel(parcel, i6);
        }
    }
}
