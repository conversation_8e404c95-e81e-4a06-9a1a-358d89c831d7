package androidx.activity.result;

import android.graphics.Path;
import android.graphics.Typeface;
import android.view.View;
import java.util.List;
import java.util.Set;
import p061i4.InterfaceC0951a;

/* renamed from: androidx.activity.result.d */
/* loaded from: classes.dex */
public abstract class AbstractC0055d {
    /* renamed from: f */
    public abstract List mo114f(List list, String str);

    /* renamed from: h */
    public Object mo115h(Class cls) {
        InterfaceC0951a mo117j = mo117j(cls);
        if (mo117j == null) {
            return null;
        }
        return mo117j.get();
    }

    /* renamed from: i */
    public abstract Path mo116i(float f6, float f7, float f8, float f9);

    /* renamed from: j */
    public abstract InterfaceC0951a mo117j(Class cls);

    /* renamed from: k */
    public abstract View mo118k(int i6);

    /* renamed from: l */
    public abstract void mo119l(int i6);

    /* renamed from: m */
    public abstract void mo120m(Typeface typeface, boolean z5);

    /* renamed from: n */
    public abstract boolean mo121n();

    /* renamed from: o */
    public Set mo122o(Class cls) {
        return (Set) mo123p(cls).get();
    }

    /* renamed from: p */
    public abstract InterfaceC0951a mo123p(Class cls);

    /* renamed from: q */
    public abstract void mo124q(Throwable th, Throwable th2);
}
