package androidx.activity.result;

import android.util.Log;
import p090n.C1094g;

/* renamed from: androidx.activity.result.a */
/* loaded from: classes.dex */
public final /* synthetic */ class C0052a {
    /* renamed from: a */
    public static int m97a(int i6) {
        if (i6 == 0) {
            return 1;
        }
        if (i6 == 1) {
            return 2;
        }
        if (i6 == 2) {
            return 3;
        }
        if (i6 != 5) {
            return i6 != 6 ? 0 : 5;
        }
        return 4;
    }

    /* renamed from: b */
    public static int[] m98b() {
        return C1094g.m2841e(11);
    }

    /* renamed from: c */
    public static int m99c(int i6) {
        if (i6 == 0) {
            return 1;
        }
        if (i6 == 1) {
            return 2;
        }
        if (i6 == 2) {
            return 3;
        }
        if (i6 != 3) {
            return i6 != 4 ? 0 : 5;
        }
        return 4;
    }

    /* renamed from: d */
    public static int m100d(int i6) {
        switch (i6) {
            case 0:
                return 1;
            case 1:
                return 2;
            case 2:
                return 3;
            case 3:
                return 4;
            case 4:
                return 5;
            case 5:
                return 6;
            case 6:
                return 7;
            default:
                return 0;
        }
    }

    /* renamed from: e */
    public static /* synthetic */ int m101e(int i6) {
        if (i6 == 1) {
            return 0;
        }
        if (i6 == 2) {
            return 1;
        }
        if (i6 == 3) {
            return 2;
        }
        if (i6 == 4) {
            return 3;
        }
        if (i6 == 5) {
            return 7;
        }
        if (i6 == 6) {
            return 8;
        }
        if (i6 == 7) {
            return 9;
        }
        if (i6 == 8) {
            return 10;
        }
        if (i6 == 9) {
            return 11;
        }
        if (i6 == 10) {
            return 12;
        }
        if (i6 == 11) {
            return 13;
        }
        throw null;
    }

    /* renamed from: f */
    public static /* synthetic */ int m102f(int i6) {
        if (i6 == 1) {
            return 0;
        }
        if (i6 == 2) {
            return 1;
        }
        if (i6 == 3) {
            return 2;
        }
        if (i6 == 4) {
            return 5;
        }
        if (i6 == 5) {
            return 6;
        }
        throw null;
    }

    /* renamed from: g */
    public static String m103g(String str, String str2) {
        return str + str2;
    }

    /* renamed from: h */
    public static StringBuilder m104h(String str) {
        StringBuilder sb = new StringBuilder();
        sb.append(str);
        return sb;
    }

    /* renamed from: i */
    public static void m105i(String str, int i6, String str2) {
        Log.d(str2, str + i6);
    }

    /* renamed from: j */
    public static /* synthetic */ String m106j(int i6) {
        if (i6 == 1) {
            return "UNKNOWN_COMPARISON_TYPE";
        }
        if (i6 == 2) {
            return "LESS_THAN";
        }
        if (i6 == 3) {
            return "GREATER_THAN";
        }
        if (i6 == 4) {
            return "EQUAL";
        }
        if (i6 == 5) {
            return "BETWEEN";
        }
        throw null;
    }

    /* renamed from: k */
    public static /* synthetic */ String m107k(int i6) {
        if (i6 == 1) {
            return "UNKNOWN_MATCH_TYPE";
        }
        if (i6 == 2) {
            return "REGEXP";
        }
        if (i6 == 3) {
            return "BEGINS_WITH";
        }
        if (i6 == 4) {
            return "ENDS_WITH";
        }
        if (i6 == 5) {
            return "PARTIAL";
        }
        if (i6 == 6) {
            return "EXACT";
        }
        if (i6 == 7) {
            return "IN_LIST";
        }
        throw null;
    }

    /* renamed from: l */
    public static /* synthetic */ String m108l(int i6) {
        return i6 == 1 ? "NONE" : i6 == 2 ? "ADDING" : i6 == 3 ? "REMOVING" : "null";
    }

    /* renamed from: m */
    public static /* synthetic */ String m109m(int i6) {
        return i6 == 1 ? "OK" : i6 == 2 ? "TRANSIENT_ERROR" : i6 == 3 ? "FATAL_ERROR" : "null";
    }

    /* renamed from: n */
    public static /* synthetic */ String m110n(int i6) {
        return i6 == 1 ? "INITIALIZE" : i6 == 2 ? "SWITCH_TO_SOURCE_SERVICE" : i6 == 3 ? "DECODE_DATA" : "null";
    }

    /* renamed from: o */
    public static /* synthetic */ String m111o(int i6) {
        return i6 == 1 ? "UNKNOWN" : i6 == 2 ? "HORIZONTAL_DIMENSION" : i6 == 3 ? "VERTICAL_DIMENSION" : i6 == 4 ? "LEFT" : i6 == 5 ? "RIGHT" : i6 == 6 ? "TOP" : i6 == 7 ? "BOTTOM" : i6 == 8 ? "BASELINE" : "null";
    }

    /* renamed from: p */
    public static /* synthetic */ String m112p(int i6) {
        return i6 == 1 ? "NO_ERROR" : i6 == 2 ? "PROTOCOL_ERROR" : i6 == 3 ? "INTERNAL_ERROR" : i6 == 4 ? "FLOW_CONTROL_ERROR" : i6 == 5 ? "REFUSED_STREAM" : i6 == 6 ? "CANCEL" : i6 == 7 ? "COMPRESSION_ERROR" : i6 == 8 ? "CONNECT_ERROR" : i6 == 9 ? "ENHANCE_YOUR_CALM" : i6 == 10 ? "INADEQUATE_SECURITY" : i6 == 11 ? "HTTP_1_1_REQUIRED" : "null";
    }
}
