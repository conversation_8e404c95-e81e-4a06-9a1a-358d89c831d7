package androidx.activity.result;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import p007b.AbstractC0370a;

/* renamed from: androidx.activity.result.e */
/* loaded from: classes.dex */
public abstract class AbstractC0056e {

    /* renamed from: a */
    public Random f211a = new Random();

    /* renamed from: b */
    public final Map<Integer, String> f212b = new HashMap();

    /* renamed from: c */
    public final Map<String, Integer> f213c = new HashMap();

    /* renamed from: d */
    public final Map<String, c> f214d = new HashMap();

    /* renamed from: e */
    public ArrayList<String> f215e = new ArrayList<>();

    /* renamed from: f */
    public final transient Map<String, b<?>> f216f = new HashMap();

    /* renamed from: g */
    public final Map<String, Object> f217g = new HashMap();

    /* renamed from: h */
    public final Bundle f218h = new Bundle();

    /* renamed from: androidx.activity.result.e$a */
    public class a extends AbstractC0055d {

        /* renamed from: a */
        public final /* synthetic */ String f219a;

        public a(String str) {
            this.f219a = str;
        }

        /* renamed from: r */
        public final void m129r() {
            AbstractC0056e.this.m128d(this.f219a);
        }
    }

    /* renamed from: androidx.activity.result.e$b */
    public static class b<O> {

        /* renamed from: a */
        public final InterfaceC0054c<O> f221a;

        /* renamed from: b */
        public final AbstractC0370a<?, O> f222b;

        public b(InterfaceC0054c<O> interfaceC0054c, AbstractC0370a<?, O> abstractC0370a) {
            this.f221a = interfaceC0054c;
            this.f222b = abstractC0370a;
        }
    }

    /* renamed from: androidx.activity.result.e$c */
    public static class c {
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.HashMap, java.util.Map<java.lang.Integer, java.lang.String>] */
    /* JADX WARN: Type inference failed for: r0v1, types: [java.util.HashMap, java.util.Map<java.lang.String, java.lang.Integer>] */
    /* renamed from: a */
    public final void m125a(int i6, String str) {
        this.f212b.put(Integer.valueOf(i6), str);
        this.f213c.put(str, Integer.valueOf(i6));
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.HashMap, java.util.Map<java.lang.Integer, java.lang.String>] */
    /* JADX WARN: Type inference failed for: r0v2, types: [java.util.HashMap, java.util.Map<java.lang.String, androidx.activity.result.e$b<?>>] */
    /* JADX WARN: Type inference failed for: r0v5, types: [java.util.HashMap, java.util.Map<java.lang.String, java.lang.Object>] */
    /* renamed from: b */
    public final boolean m126b(int i6, int i7, Intent intent) {
        InterfaceC0054c<O> interfaceC0054c;
        String str = (String) this.f212b.get(Integer.valueOf(i6));
        if (str == null) {
            return false;
        }
        this.f215e.remove(str);
        b bVar = (b) this.f216f.get(str);
        if (bVar != null && (interfaceC0054c = bVar.f221a) != 0) {
            interfaceC0054c.mo113a(bVar.f222b.mo849a(i7, intent));
            return true;
        }
        this.f217g.remove(str);
        this.f218h.putParcelable(str, new C0053b(i7, intent));
        return true;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.HashMap, java.util.Map<java.lang.String, java.lang.Integer>] */
    /* JADX WARN: Type inference failed for: r0v10, types: [java.util.HashMap, java.util.Map<java.lang.String, java.lang.Object>] */
    /* JADX WARN: Type inference failed for: r0v16, types: [java.util.HashMap, java.util.Map<java.lang.String, java.lang.Object>] */
    /* JADX WARN: Type inference failed for: r0v9, types: [java.util.HashMap, java.util.Map<java.lang.String, androidx.activity.result.e$b<?>>] */
    /* JADX WARN: Type inference failed for: r1v4, types: [java.util.HashMap, java.util.Map<java.lang.String, java.lang.Object>] */
    /* JADX WARN: Type inference failed for: r3v0, types: [java.util.HashMap, java.util.Map<java.lang.Integer, java.lang.String>] */
    /* renamed from: c */
    public final <I, O> AbstractC0055d m127c(String str, AbstractC0370a<I, O> abstractC0370a, InterfaceC0054c<O> interfaceC0054c) {
        int i6;
        Integer num = (Integer) this.f213c.get(str);
        if (num != null) {
            num.intValue();
        } else {
            int nextInt = this.f211a.nextInt(2147418112);
            while (true) {
                i6 = nextInt + 65536;
                if (!this.f212b.containsKey(Integer.valueOf(i6))) {
                    break;
                }
                nextInt = this.f211a.nextInt(2147418112);
            }
            m125a(i6, str);
        }
        this.f216f.put(str, new b(interfaceC0054c, abstractC0370a));
        if (this.f217g.containsKey(str)) {
            Object obj = this.f217g.get(str);
            this.f217g.remove(str);
            interfaceC0054c.mo113a(obj);
        }
        C0053b c0053b = (C0053b) this.f218h.getParcelable(str);
        if (c0053b != null) {
            this.f218h.remove(str);
            interfaceC0054c.mo113a(abstractC0370a.mo849a(c0053b.f209j, c0053b.f210k));
        }
        return new a(str);
    }

    /* JADX WARN: Type inference failed for: r0v13, types: [java.util.HashMap, java.util.Map<java.lang.String, java.lang.Object>] */
    /* JADX WARN: Type inference failed for: r0v14, types: [java.util.HashMap, java.util.Map<java.lang.String, java.lang.Integer>] */
    /* JADX WARN: Type inference failed for: r0v2, types: [java.util.HashMap, java.util.Map<java.lang.String, androidx.activity.result.e$b<?>>] */
    /* JADX WARN: Type inference failed for: r0v3, types: [java.util.HashMap, java.util.Map<java.lang.String, java.lang.Object>] */
    /* JADX WARN: Type inference failed for: r0v7, types: [java.util.HashMap, java.util.Map<java.lang.String, androidx.activity.result.e$c>] */
    /* JADX WARN: Type inference failed for: r1v3, types: [java.util.HashMap, java.util.Map<java.lang.Integer, java.lang.String>] */
    /* JADX WARN: Type inference failed for: r4v0, types: [java.util.HashMap, java.util.Map<java.lang.String, java.lang.Object>] */
    /* renamed from: d */
    public final void m128d(String str) {
        Integer num;
        if (!this.f215e.contains(str) && (num = (Integer) this.f213c.remove(str)) != null) {
            this.f212b.remove(num);
        }
        this.f216f.remove(str);
        if (this.f217g.containsKey(str)) {
            Log.w("ActivityResultRegistry", "Dropping pending result for request " + str + ": " + this.f217g.get(str));
            this.f217g.remove(str);
        }
        if (this.f218h.containsKey(str)) {
            Log.w("ActivityResultRegistry", "Dropping pending result for request " + str + ": " + this.f218h.getParcelable(str));
            this.f218h.remove(str);
        }
        if (((c) this.f214d.get(str)) != null) {
            throw null;
        }
    }
}
