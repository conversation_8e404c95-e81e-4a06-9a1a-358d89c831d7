package androidx.recyclerview.widget;

import androidx.recyclerview.widget.C0326p;
import java.util.ArrayList;
import java.util.List;
import p022d0.C0699d;
import p022d0.InterfaceC0698c;

/* renamed from: androidx.recyclerview.widget.a */
/* loaded from: classes.dex */
public final class C0308a implements C0326p.a {

    /* renamed from: d */
    public final a f2010d;

    /* renamed from: a */
    public InterfaceC0698c<b> f2007a = new C0699d(30);

    /* renamed from: b */
    public final ArrayList<b> f2008b = new ArrayList<>();

    /* renamed from: c */
    public final ArrayList<b> f2009c = new ArrayList<>();

    /* renamed from: f */
    public int f2012f = 0;

    /* renamed from: e */
    public final C0326p f2011e = new C0326p(this);

    /* renamed from: androidx.recyclerview.widget.a$a */
    public interface a {
    }

    /* renamed from: androidx.recyclerview.widget.a$b */
    public static class b {

        /* renamed from: a */
        public int f2013a;

        /* renamed from: b */
        public int f2014b;

        /* renamed from: c */
        public Object f2015c;

        /* renamed from: d */
        public int f2016d;

        public b(int i6, int i7, int i8, Object obj) {
            this.f2013a = i6;
            this.f2014b = i7;
            this.f2016d = i8;
            this.f2015c = obj;
        }

        public final boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null || b.class != obj.getClass()) {
                return false;
            }
            b bVar = (b) obj;
            int i6 = this.f2013a;
            if (i6 != bVar.f2013a) {
                return false;
            }
            if (i6 == 8 && Math.abs(this.f2016d - this.f2014b) == 1 && this.f2016d == bVar.f2014b && this.f2014b == bVar.f2016d) {
                return true;
            }
            if (this.f2016d != bVar.f2016d || this.f2014b != bVar.f2014b) {
                return false;
            }
            Object obj2 = this.f2015c;
            Object obj3 = bVar.f2015c;
            if (obj2 != null) {
                if (!obj2.equals(obj3)) {
                    return false;
                }
            } else if (obj3 != null) {
                return false;
            }
            return true;
        }

        public final int hashCode() {
            return (((this.f2013a * 31) + this.f2014b) * 31) + this.f2016d;
        }

        public final String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append(Integer.toHexString(System.identityHashCode(this)));
            sb.append("[");
            int i6 = this.f2013a;
            sb.append(i6 != 1 ? i6 != 2 ? i6 != 4 ? i6 != 8 ? "??" : "mv" : "up" : "rm" : "add");
            sb.append(",s:");
            sb.append(this.f2014b);
            sb.append("c:");
            sb.append(this.f2016d);
            sb.append(",p:");
            sb.append(this.f2015c);
            sb.append("]");
            return sb.toString();
        }
    }

    public C0308a(a aVar) {
        this.f2010d = aVar;
    }

    /* renamed from: a */
    public final boolean m1250a(int i6) {
        int size = this.f2009c.size();
        for (int i7 = 0; i7 < size; i7++) {
            b bVar = this.f2009c.get(i7);
            int i8 = bVar.f2013a;
            if (i8 == 8) {
                if (m1255f(bVar.f2016d, i7 + 1) == i6) {
                    return true;
                }
            } else if (i8 == 1) {
                int i9 = bVar.f2014b;
                int i10 = bVar.f2016d + i9;
                while (i9 < i10) {
                    if (m1255f(i9, i7 + 1) == i6) {
                        return true;
                    }
                    i9++;
                }
            } else {
                continue;
            }
        }
        return false;
    }

    /* renamed from: b */
    public final void m1251b() {
        int size = this.f2009c.size();
        for (int i6 = 0; i6 < size; i6++) {
            ((C0333w) this.f2010d).m1346a(this.f2009c.get(i6));
        }
        m1261l(this.f2009c);
        this.f2012f = 0;
    }

    /* renamed from: c */
    public final void m1252c() {
        m1251b();
        int size = this.f2008b.size();
        for (int i6 = 0; i6 < size; i6++) {
            b bVar = this.f2008b.get(i6);
            int i7 = bVar.f2013a;
            if (i7 == 1) {
                ((C0333w) this.f2010d).m1346a(bVar);
                ((C0333w) this.f2010d).m1349d(bVar.f2014b, bVar.f2016d);
            } else if (i7 == 2) {
                ((C0333w) this.f2010d).m1346a(bVar);
                a aVar = this.f2010d;
                int i8 = bVar.f2014b;
                int i9 = bVar.f2016d;
                C0333w c0333w = (C0333w) aVar;
                c0333w.f2166a.m1011R(i8, i9, true);
                RecyclerView recyclerView = c0333w.f2166a;
                recyclerView.f1827s0 = true;
                recyclerView.f1821p0.f1942c += i9;
            } else if (i7 == 4) {
                ((C0333w) this.f2010d).m1346a(bVar);
                ((C0333w) this.f2010d).m1348c(bVar.f2014b, bVar.f2016d, bVar.f2015c);
            } else if (i7 == 8) {
                ((C0333w) this.f2010d).m1346a(bVar);
                ((C0333w) this.f2010d).m1350e(bVar.f2014b, bVar.f2016d);
            }
        }
        m1261l(this.f2008b);
        this.f2012f = 0;
    }

    /* renamed from: d */
    public final void m1253d(b bVar) {
        int i6;
        int i7 = bVar.f2013a;
        if (i7 == 1 || i7 == 8) {
            throw new IllegalArgumentException("should not dispatch add or move for pre layout");
        }
        int m1262m = m1262m(bVar.f2014b, i7);
        int i8 = bVar.f2014b;
        int i9 = bVar.f2013a;
        if (i9 == 2) {
            i6 = 0;
        } else {
            if (i9 != 4) {
                throw new IllegalArgumentException("op should be remove or update." + bVar);
            }
            i6 = 1;
        }
        int i10 = 1;
        for (int i11 = 1; i11 < bVar.f2016d; i11++) {
            int m1262m2 = m1262m((i6 * i11) + bVar.f2014b, bVar.f2013a);
            int i12 = bVar.f2013a;
            if (i12 == 2 ? m1262m2 == m1262m : i12 == 4 && m1262m2 == m1262m + 1) {
                i10++;
            } else {
                b m1257h = m1257h(i12, m1262m, i10, bVar.f2015c);
                m1254e(m1257h, i8);
                m1257h.f2015c = null;
                this.f2007a.mo2055a(m1257h);
                if (bVar.f2013a == 4) {
                    i8 += i10;
                }
                i10 = 1;
                m1262m = m1262m2;
            }
        }
        Object obj = bVar.f2015c;
        bVar.f2015c = null;
        this.f2007a.mo2055a(bVar);
        if (i10 > 0) {
            b m1257h2 = m1257h(bVar.f2013a, m1262m, i10, obj);
            m1254e(m1257h2, i8);
            m1257h2.f2015c = null;
            this.f2007a.mo2055a(m1257h2);
        }
    }

    /* renamed from: e */
    public final void m1254e(b bVar, int i6) {
        ((C0333w) this.f2010d).m1346a(bVar);
        int i7 = bVar.f2013a;
        if (i7 != 2) {
            if (i7 != 4) {
                throw new IllegalArgumentException("only remove and update ops can be dispatched in first pass");
            }
            ((C0333w) this.f2010d).m1348c(i6, bVar.f2016d, bVar.f2015c);
            return;
        }
        a aVar = this.f2010d;
        int i8 = bVar.f2016d;
        C0333w c0333w = (C0333w) aVar;
        c0333w.f2166a.m1011R(i6, i8, true);
        RecyclerView recyclerView = c0333w.f2166a;
        recyclerView.f1827s0 = true;
        recyclerView.f1821p0.f1942c += i8;
    }

    /* renamed from: f */
    public final int m1255f(int i6, int i7) {
        int size = this.f2009c.size();
        while (i7 < size) {
            b bVar = this.f2009c.get(i7);
            int i8 = bVar.f2013a;
            if (i8 == 8) {
                int i9 = bVar.f2014b;
                if (i9 == i6) {
                    i6 = bVar.f2016d;
                } else {
                    if (i9 < i6) {
                        i6--;
                    }
                    if (bVar.f2016d <= i6) {
                        i6++;
                    }
                }
            } else {
                int i10 = bVar.f2014b;
                if (i10 > i6) {
                    continue;
                } else if (i8 == 2) {
                    int i11 = bVar.f2016d;
                    if (i6 < i10 + i11) {
                        return -1;
                    }
                    i6 -= i11;
                } else if (i8 == 1) {
                    i6 += bVar.f2016d;
                }
            }
            i7++;
        }
        return i6;
    }

    /* renamed from: g */
    public final boolean m1256g() {
        return this.f2008b.size() > 0;
    }

    /* renamed from: h */
    public final b m1257h(int i6, int i7, int i8, Object obj) {
        b mo2056b = this.f2007a.mo2056b();
        if (mo2056b == null) {
            return new b(i6, i7, i8, obj);
        }
        mo2056b.f2013a = i6;
        mo2056b.f2014b = i7;
        mo2056b.f2016d = i8;
        mo2056b.f2015c = obj;
        return mo2056b;
    }

    /* renamed from: i */
    public final void m1258i(b bVar) {
        this.f2009c.add(bVar);
        int i6 = bVar.f2013a;
        if (i6 == 1) {
            ((C0333w) this.f2010d).m1349d(bVar.f2014b, bVar.f2016d);
            return;
        }
        if (i6 == 2) {
            C0333w c0333w = (C0333w) this.f2010d;
            c0333w.f2166a.m1011R(bVar.f2014b, bVar.f2016d, false);
            c0333w.f2166a.f1827s0 = true;
            return;
        }
        if (i6 == 4) {
            ((C0333w) this.f2010d).m1348c(bVar.f2014b, bVar.f2016d, bVar.f2015c);
        } else if (i6 == 8) {
            ((C0333w) this.f2010d).m1350e(bVar.f2014b, bVar.f2016d);
        } else {
            throw new IllegalArgumentException("Unknown update op type for " + bVar);
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:67:0x0164, code lost:
    
        if (r5 > r12.f2014b) goto L316;
     */
    /* JADX WARN: Code restructure failed: missing block: B:68:0x0195, code lost:
    
        r2.set(r3, r12);
     */
    /* JADX WARN: Code restructure failed: missing block: B:69:0x019c, code lost:
    
        if (r11.f2014b == r11.f2016d) goto L320;
     */
    /* JADX WARN: Code restructure failed: missing block: B:70:0x019e, code lost:
    
        r2.set(r7, r11);
     */
    /* JADX WARN: Code restructure failed: missing block: B:71:0x01a5, code lost:
    
        if (r4 == null) goto L412;
     */
    /* JADX WARN: Code restructure failed: missing block: B:73:0x01a7, code lost:
    
        r2.add(r3, r4);
     */
    /* JADX WARN: Code restructure failed: missing block: B:76:0x01a2, code lost:
    
        r2.remove(r7);
     */
    /* JADX WARN: Code restructure failed: missing block: B:77:0x0190, code lost:
    
        r11.f2016d = r5 - r12.f2016d;
     */
    /* JADX WARN: Code restructure failed: missing block: B:89:0x018e, code lost:
    
        if (r5 >= r12.f2014b) goto L316;
     */
    /* JADX WARN: Removed duplicated region for block: B:115:0x006b  */
    /* JADX WARN: Removed duplicated region for block: B:119:0x0090  */
    /* JADX WARN: Removed duplicated region for block: B:121:0x00a7  */
    /* JADX WARN: Removed duplicated region for block: B:123:0x00ac A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:126:0x0009 A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:127:0x0094  */
    /* JADX WARN: Removed duplicated region for block: B:128:0x0070  */
    /* JADX WARN: Removed duplicated region for block: B:39:0x00db  */
    /* JADX WARN: Removed duplicated region for block: B:51:0x0105  */
    /* JADX WARN: Removed duplicated region for block: B:54:0x013b A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:90:0x0125 A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:93:0x010a  */
    /* JADX WARN: Removed duplicated region for block: B:96:0x00d6 A[SYNTHETIC] */
    /* renamed from: j */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m1259j() {
        /*
            Method dump skipped, instructions count: 696
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.C0308a.m1259j():void");
    }

    /* renamed from: k */
    public final void m1260k(b bVar) {
        bVar.f2015c = null;
        this.f2007a.mo2055a(bVar);
    }

    /* renamed from: l */
    public final void m1261l(List<b> list) {
        int size = list.size();
        for (int i6 = 0; i6 < size; i6++) {
            m1260k(list.get(i6));
        }
        list.clear();
    }

    /* renamed from: m */
    public final int m1262m(int i6, int i7) {
        int i8;
        int i9;
        int i10;
        int i11;
        int i12;
        int i13;
        for (int size = this.f2009c.size() - 1; size >= 0; size--) {
            b bVar = this.f2009c.get(size);
            int i14 = bVar.f2013a;
            if (i14 == 8) {
                int i15 = bVar.f2014b;
                int i16 = bVar.f2016d;
                if (i15 < i16) {
                    i10 = i15;
                    i9 = i16;
                } else {
                    i9 = i15;
                    i10 = i16;
                }
                if (i6 < i10 || i6 > i9) {
                    if (i6 < i15) {
                        if (i7 == 1) {
                            bVar.f2014b = i15 + 1;
                            i11 = i16 + 1;
                        } else if (i7 == 2) {
                            bVar.f2014b = i15 - 1;
                            i11 = i16 - 1;
                        }
                        bVar.f2016d = i11;
                    }
                } else if (i10 == i15) {
                    if (i7 == 1) {
                        i13 = i16 + 1;
                    } else {
                        if (i7 == 2) {
                            i13 = i16 - 1;
                        }
                        i6++;
                    }
                    bVar.f2016d = i13;
                    i6++;
                } else {
                    if (i7 == 1) {
                        i12 = i15 + 1;
                    } else {
                        if (i7 == 2) {
                            i12 = i15 - 1;
                        }
                        i6--;
                    }
                    bVar.f2014b = i12;
                    i6--;
                }
            } else {
                int i17 = bVar.f2014b;
                if (i17 > i6) {
                    if (i7 == 1) {
                        i8 = i17 + 1;
                    } else if (i7 == 2) {
                        i8 = i17 - 1;
                    }
                    bVar.f2014b = i8;
                } else if (i14 == 1) {
                    i6 -= bVar.f2016d;
                } else if (i14 == 2) {
                    i6 += bVar.f2016d;
                }
            }
        }
        for (int size2 = this.f2009c.size() - 1; size2 >= 0; size2--) {
            b bVar2 = this.f2009c.get(size2);
            if (bVar2.f2013a == 8) {
                int i18 = bVar2.f2016d;
                if (i18 != bVar2.f2014b && i18 >= 0) {
                }
                this.f2009c.remove(size2);
                m1260k(bVar2);
            } else {
                if (bVar2.f2016d > 0) {
                }
                this.f2009c.remove(size2);
                m1260k(bVar2);
            }
        }
        return i6;
    }
}
