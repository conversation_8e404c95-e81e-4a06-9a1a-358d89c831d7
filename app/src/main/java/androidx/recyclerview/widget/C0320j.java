package androidx.recyclerview.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.view.View;
import android.view.ViewPropertyAnimator;
import androidx.recyclerview.widget.C0321k;
import androidx.recyclerview.widget.RecyclerView;
import java.util.Objects;

/* renamed from: androidx.recyclerview.widget.j */
/* loaded from: classes.dex */
public final class C0320j extends AnimatorListenerAdapter {

    /* renamed from: a */
    public final /* synthetic */ C0321k.a f2063a;

    /* renamed from: b */
    public final /* synthetic */ ViewPropertyAnimator f2064b;

    /* renamed from: c */
    public final /* synthetic */ View f2065c;

    /* renamed from: d */
    public final /* synthetic */ C0321k f2066d;

    public C0320j(C0321k c0321k, C0321k.a aVar, ViewPropertyAnimator viewPropertyAnimator, View view) {
        this.f2066d = c0321k;
        this.f2063a = aVar;
        this.f2064b = viewPropertyAnimator;
        this.f2065c = view;
    }

    @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
    public final void onAnimationEnd(Animator animator) {
        this.f2064b.setListener(null);
        this.f2065c.setAlpha(1.0f);
        this.f2065c.setTranslationX(0.0f);
        this.f2065c.setTranslationY(0.0f);
        this.f2066d.m1103c(this.f2063a.f2080b);
        this.f2066d.f2078r.remove(this.f2063a.f2080b);
        this.f2066d.m1302j();
    }

    @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
    public final void onAnimationStart(Animator animator) {
        C0321k c0321k = this.f2066d;
        RecyclerView.AbstractC0277b0 abstractC0277b0 = this.f2063a.f2080b;
        Objects.requireNonNull(c0321k);
    }
}
