package androidx.recyclerview.widget;

import android.view.View;
import android.view.ViewPropertyAnimator;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Objects;

/* renamed from: androidx.recyclerview.widget.e */
/* loaded from: classes.dex */
public final class RunnableC0315e implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ ArrayList f2043j;

    /* renamed from: k */
    public final /* synthetic */ C0321k f2044k;

    public RunnableC0315e(C0321k c0321k, ArrayList arrayList) {
        this.f2044k = c0321k;
        this.f2043j = arrayList;
    }

    @Override // java.lang.Runnable
    public final void run() {
        Iterator it = this.f2043j.iterator();
        while (it.hasNext()) {
            RecyclerView.AbstractC0277b0 abstractC0277b0 = (RecyclerView.AbstractC0277b0) it.next();
            C0321k c0321k = this.f2044k;
            Objects.requireNonNull(c0321k);
            View view = abstractC0277b0.f1852a;
            ViewPropertyAnimator animate = view.animate();
            c0321k.f2075o.add(abstractC0277b0);
            animate.alpha(1.0f).setDuration(c0321k.f1875c).setListener(new C0317g(c0321k, abstractC0277b0, view, animate)).start();
        }
        this.f2043j.clear();
        this.f2044k.f2072l.remove(this.f2043j);
    }
}
