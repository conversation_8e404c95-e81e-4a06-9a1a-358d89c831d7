package androidx.recyclerview.widget;

import android.graphics.Rect;
import android.view.View;
import android.view.ViewGroup;
import androidx.recyclerview.widget.RecyclerView;
import java.util.Objects;

/* renamed from: androidx.recyclerview.widget.r */
/* loaded from: classes.dex */
public final class C0328r extends AbstractC0329s {
    public C0328r(RecyclerView.AbstractC0288m abstractC0288m) {
        super(abstractC0288m);
    }

    @Override // androidx.recyclerview.widget.AbstractC0329s
    /* renamed from: b */
    public final int mo1322b(View view) {
        return this.f2159a.m1116A(view) + ((ViewGroup.MarginLayoutParams) ((RecyclerView.C0289n) view.getLayoutParams())).bottomMargin;
    }

    @Override // androidx.recyclerview.widget.AbstractC0329s
    /* renamed from: c */
    public final int mo1323c(View view) {
        RecyclerView.C0289n c0289n = (RecyclerView.C0289n) view.getLayoutParams();
        Objects.requireNonNull(this.f2159a);
        Rect rect = ((RecyclerView.C0289n) view.getLayoutParams()).f1906b;
        return view.getMeasuredHeight() + rect.top + rect.bottom + ((ViewGroup.MarginLayoutParams) c0289n).topMargin + ((ViewGroup.MarginLayoutParams) c0289n).bottomMargin;
    }

    @Override // androidx.recyclerview.widget.AbstractC0329s
    /* renamed from: d */
    public final int mo1324d(View view) {
        RecyclerView.C0289n c0289n = (RecyclerView.C0289n) view.getLayoutParams();
        Objects.requireNonNull(this.f2159a);
        Rect rect = ((RecyclerView.C0289n) view.getLayoutParams()).f1906b;
        return view.getMeasuredWidth() + rect.left + rect.right + ((ViewGroup.MarginLayoutParams) c0289n).leftMargin + ((ViewGroup.MarginLayoutParams) c0289n).rightMargin;
    }

    @Override // androidx.recyclerview.widget.AbstractC0329s
    /* renamed from: e */
    public final int mo1325e(View view) {
        return this.f2159a.m1123E(view) - ((ViewGroup.MarginLayoutParams) ((RecyclerView.C0289n) view.getLayoutParams())).topMargin;
    }

    @Override // androidx.recyclerview.widget.AbstractC0329s
    /* renamed from: f */
    public final int mo1326f() {
        return this.f2159a.f1898q;
    }

    @Override // androidx.recyclerview.widget.AbstractC0329s
    /* renamed from: g */
    public final int mo1327g() {
        RecyclerView.AbstractC0288m abstractC0288m = this.f2159a;
        return abstractC0288m.f1898q - abstractC0288m.m1131J();
    }

    @Override // androidx.recyclerview.widget.AbstractC0329s
    /* renamed from: h */
    public final int mo1328h() {
        return this.f2159a.m1131J();
    }

    @Override // androidx.recyclerview.widget.AbstractC0329s
    /* renamed from: i */
    public final int mo1329i() {
        return this.f2159a.f1896o;
    }

    @Override // androidx.recyclerview.widget.AbstractC0329s
    /* renamed from: j */
    public final int mo1330j() {
        return this.f2159a.f1895n;
    }

    @Override // androidx.recyclerview.widget.AbstractC0329s
    /* renamed from: k */
    public final int mo1331k() {
        return this.f2159a.m1134M();
    }

    @Override // androidx.recyclerview.widget.AbstractC0329s
    /* renamed from: l */
    public final int mo1332l() {
        RecyclerView.AbstractC0288m abstractC0288m = this.f2159a;
        return (abstractC0288m.f1898q - abstractC0288m.m1134M()) - this.f2159a.m1131J();
    }

    @Override // androidx.recyclerview.widget.AbstractC0329s
    /* renamed from: n */
    public final int mo1333n(View view) {
        this.f2159a.m1136Q(view, this.f2161c);
        return this.f2161c.bottom;
    }

    @Override // androidx.recyclerview.widget.AbstractC0329s
    /* renamed from: o */
    public final int mo1334o(View view) {
        this.f2159a.m1136Q(view, this.f2161c);
        return this.f2161c.top;
    }

    @Override // androidx.recyclerview.widget.AbstractC0329s
    /* renamed from: p */
    public final void mo1335p(int i6) {
        this.f2159a.mo1139V(i6);
    }
}
