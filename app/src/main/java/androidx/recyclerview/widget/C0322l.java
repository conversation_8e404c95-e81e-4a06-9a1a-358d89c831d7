package androidx.recyclerview.widget;

import android.R;
import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.StateListDrawable;
import android.view.MotionEvent;
import android.view.View;
import androidx.recyclerview.widget.RecyclerView;
import java.util.WeakHashMap;
import p029e0.C0766p;
import p029e0.C0769s;

/* renamed from: androidx.recyclerview.widget.l */
/* loaded from: classes.dex */
public final class C0322l extends RecyclerView.AbstractC0287l implements RecyclerView.InterfaceC0292q {

    /* renamed from: D */
    public static final int[] f2090D = {R.attr.state_pressed};

    /* renamed from: E */
    public static final int[] f2091E = new int[0];

    /* renamed from: A */
    public int f2092A;

    /* renamed from: B */
    public final a f2093B;

    /* renamed from: C */
    public final b f2094C;

    /* renamed from: a */
    public final int f2095a;

    /* renamed from: b */
    public final int f2096b;

    /* renamed from: c */
    public final StateListDrawable f2097c;

    /* renamed from: d */
    public final Drawable f2098d;

    /* renamed from: e */
    public final int f2099e;

    /* renamed from: f */
    public final int f2100f;

    /* renamed from: g */
    public final StateListDrawable f2101g;

    /* renamed from: h */
    public final Drawable f2102h;

    /* renamed from: i */
    public final int f2103i;

    /* renamed from: j */
    public final int f2104j;

    /* renamed from: k */
    public int f2105k;

    /* renamed from: l */
    public int f2106l;

    /* renamed from: m */
    public float f2107m;

    /* renamed from: n */
    public int f2108n;

    /* renamed from: o */
    public int f2109o;

    /* renamed from: p */
    public float f2110p;

    /* renamed from: s */
    public RecyclerView f2113s;

    /* renamed from: z */
    public final ValueAnimator f2120z;

    /* renamed from: q */
    public int f2111q = 0;

    /* renamed from: r */
    public int f2112r = 0;

    /* renamed from: t */
    public boolean f2114t = false;

    /* renamed from: u */
    public boolean f2115u = false;

    /* renamed from: v */
    public int f2116v = 0;

    /* renamed from: w */
    public int f2117w = 0;

    /* renamed from: x */
    public final int[] f2118x = new int[2];

    /* renamed from: y */
    public final int[] f2119y = new int[2];

    /* renamed from: androidx.recyclerview.widget.l$a */
    public class a implements Runnable {
        public a() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            C0322l c0322l = C0322l.this;
            int i6 = c0322l.f2092A;
            if (i6 == 1) {
                c0322l.f2120z.cancel();
            } else if (i6 != 2) {
                return;
            }
            c0322l.f2092A = 3;
            ValueAnimator valueAnimator = c0322l.f2120z;
            valueAnimator.setFloatValues(((Float) valueAnimator.getAnimatedValue()).floatValue(), 0.0f);
            c0322l.f2120z.setDuration(500);
            c0322l.f2120z.start();
        }
    }

    /* renamed from: androidx.recyclerview.widget.l$b */
    public class b extends RecyclerView.AbstractC0293r {
        public b() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0293r
        /* renamed from: b */
        public final void mo1174b(RecyclerView recyclerView, int i6, int i7) {
            C0322l c0322l = C0322l.this;
            int computeHorizontalScrollOffset = recyclerView.computeHorizontalScrollOffset();
            int computeVerticalScrollOffset = recyclerView.computeVerticalScrollOffset();
            int computeVerticalScrollRange = c0322l.f2113s.computeVerticalScrollRange();
            int i8 = c0322l.f2112r;
            c0322l.f2114t = computeVerticalScrollRange - i8 > 0 && i8 >= c0322l.f2095a;
            int computeHorizontalScrollRange = c0322l.f2113s.computeHorizontalScrollRange();
            int i9 = c0322l.f2111q;
            boolean z5 = computeHorizontalScrollRange - i9 > 0 && i9 >= c0322l.f2095a;
            c0322l.f2115u = z5;
            boolean z6 = c0322l.f2114t;
            if (!z6 && !z5) {
                if (c0322l.f2116v != 0) {
                    c0322l.m1311k(0);
                    return;
                }
                return;
            }
            if (z6) {
                float f6 = i8;
                c0322l.f2106l = (int) ((((f6 / 2.0f) + computeVerticalScrollOffset) * f6) / computeVerticalScrollRange);
                c0322l.f2105k = Math.min(i8, (i8 * i8) / computeVerticalScrollRange);
            }
            if (c0322l.f2115u) {
                float f7 = computeHorizontalScrollOffset;
                float f8 = i9;
                c0322l.f2109o = (int) ((((f8 / 2.0f) + f7) * f8) / computeHorizontalScrollRange);
                c0322l.f2108n = Math.min(i9, (i9 * i9) / computeHorizontalScrollRange);
            }
            int i10 = c0322l.f2116v;
            if (i10 == 0 || i10 == 1) {
                c0322l.m1311k(1);
            }
        }
    }

    /* renamed from: androidx.recyclerview.widget.l$c */
    public class c extends AnimatorListenerAdapter {

        /* renamed from: a */
        public boolean f2123a = false;

        public c() {
        }

        @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
        public final void onAnimationCancel(Animator animator) {
            this.f2123a = true;
        }

        @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
        public final void onAnimationEnd(Animator animator) {
            if (this.f2123a) {
                this.f2123a = false;
                return;
            }
            if (((Float) C0322l.this.f2120z.getAnimatedValue()).floatValue() == 0.0f) {
                C0322l c0322l = C0322l.this;
                c0322l.f2092A = 0;
                c0322l.m1311k(0);
            } else {
                C0322l c0322l2 = C0322l.this;
                c0322l2.f2092A = 2;
                c0322l2.m1309i();
            }
        }
    }

    /* renamed from: androidx.recyclerview.widget.l$d */
    public class d implements ValueAnimator.AnimatorUpdateListener {
        public d() {
        }

        @Override // android.animation.ValueAnimator.AnimatorUpdateListener
        public final void onAnimationUpdate(ValueAnimator valueAnimator) {
            int floatValue = (int) (((Float) valueAnimator.getAnimatedValue()).floatValue() * 255.0f);
            C0322l.this.f2097c.setAlpha(floatValue);
            C0322l.this.f2098d.setAlpha(floatValue);
            C0322l.this.m1309i();
        }
    }

    /* JADX WARN: Type inference failed for: r7v6, types: [java.util.ArrayList, java.util.List<androidx.recyclerview.widget.RecyclerView$r>] */
    public C0322l(RecyclerView recyclerView, StateListDrawable stateListDrawable, Drawable drawable, StateListDrawable stateListDrawable2, Drawable drawable2, int i6, int i7, int i8) {
        ValueAnimator ofFloat = ValueAnimator.ofFloat(0.0f, 1.0f);
        this.f2120z = ofFloat;
        this.f2092A = 0;
        this.f2093B = new a();
        b bVar = new b();
        this.f2094C = bVar;
        this.f2097c = stateListDrawable;
        this.f2098d = drawable;
        this.f2101g = stateListDrawable2;
        this.f2102h = drawable2;
        this.f2099e = Math.max(i6, stateListDrawable.getIntrinsicWidth());
        this.f2100f = Math.max(i6, drawable.getIntrinsicWidth());
        this.f2103i = Math.max(i6, stateListDrawable2.getIntrinsicWidth());
        this.f2104j = Math.max(i6, drawable2.getIntrinsicWidth());
        this.f2095a = i7;
        this.f2096b = i8;
        stateListDrawable.setAlpha(255);
        drawable.setAlpha(255);
        ofFloat.addListener(new c());
        ofFloat.addUpdateListener(new d());
        RecyclerView recyclerView2 = this.f2113s;
        if (recyclerView2 == recyclerView) {
            return;
        }
        if (recyclerView2 != null) {
            RecyclerView.AbstractC0288m abstractC0288m = recyclerView2.f1832v;
            if (abstractC0288m != null) {
                abstractC0288m.mo961d("Cannot remove item decoration during a scroll  or layout");
            }
            recyclerView2.f1836x.remove(this);
            if (recyclerView2.f1836x.isEmpty()) {
                recyclerView2.setWillNotDraw(recyclerView2.getOverScrollMode() == 2);
            }
            recyclerView2.m1010Q();
            recyclerView2.requestLayout();
            RecyclerView recyclerView3 = this.f2113s;
            recyclerView3.f1838y.remove(this);
            if (recyclerView3.f1840z == this) {
                recyclerView3.f1840z = null;
            }
            ?? r7 = this.f2113s.f1825r0;
            if (r7 != 0) {
                r7.remove(bVar);
            }
            m1306f();
        }
        this.f2113s = recyclerView;
        if (recyclerView != null) {
            recyclerView.m1027g(this);
            this.f2113s.f1838y.add(this);
            this.f2113s.m1029h(bVar);
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.InterfaceC0292q
    /* renamed from: a */
    public final boolean mo1170a(MotionEvent motionEvent) {
        int i6 = this.f2116v;
        if (i6 == 1) {
            boolean m1308h = m1308h(motionEvent.getX(), motionEvent.getY());
            boolean m1307g = m1307g(motionEvent.getX(), motionEvent.getY());
            if (motionEvent.getAction() == 0 && (m1308h || m1307g)) {
                if (m1307g) {
                    this.f2117w = 1;
                    this.f2110p = (int) motionEvent.getX();
                } else if (m1308h) {
                    this.f2117w = 2;
                    this.f2107m = (int) motionEvent.getY();
                }
                m1311k(2);
                return true;
            }
        } else if (i6 == 2) {
            return true;
        }
        return false;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.InterfaceC0292q
    /* renamed from: b */
    public final void mo1171b() {
    }

    @Override // androidx.recyclerview.widget.RecyclerView.InterfaceC0292q
    /* renamed from: c */
    public final void mo1172c(MotionEvent motionEvent) {
        if (this.f2116v == 0) {
            return;
        }
        if (motionEvent.getAction() == 0) {
            boolean m1308h = m1308h(motionEvent.getX(), motionEvent.getY());
            boolean m1307g = m1307g(motionEvent.getX(), motionEvent.getY());
            if (m1308h || m1307g) {
                if (m1307g) {
                    this.f2117w = 1;
                    this.f2110p = (int) motionEvent.getX();
                } else if (m1308h) {
                    this.f2117w = 2;
                    this.f2107m = (int) motionEvent.getY();
                }
                m1311k(2);
                return;
            }
            return;
        }
        if (motionEvent.getAction() == 1 && this.f2116v == 2) {
            this.f2107m = 0.0f;
            this.f2110p = 0.0f;
            m1311k(1);
            this.f2117w = 0;
            return;
        }
        if (motionEvent.getAction() == 2 && this.f2116v == 2) {
            m1312l();
            if (this.f2117w == 1) {
                float x6 = motionEvent.getX();
                int[] iArr = this.f2119y;
                int i6 = this.f2096b;
                iArr[0] = i6;
                iArr[1] = this.f2111q - i6;
                float max = Math.max(iArr[0], Math.min(iArr[1], x6));
                if (Math.abs(this.f2109o - max) >= 2.0f) {
                    int m1310j = m1310j(this.f2110p, max, iArr, this.f2113s.computeHorizontalScrollRange(), this.f2113s.computeHorizontalScrollOffset(), this.f2111q);
                    if (m1310j != 0) {
                        this.f2113s.scrollBy(m1310j, 0);
                    }
                    this.f2110p = max;
                }
            }
            if (this.f2117w == 2) {
                float y2 = motionEvent.getY();
                int[] iArr2 = this.f2118x;
                int i7 = this.f2096b;
                iArr2[0] = i7;
                iArr2[1] = this.f2112r - i7;
                float max2 = Math.max(iArr2[0], Math.min(iArr2[1], y2));
                if (Math.abs(this.f2106l - max2) < 2.0f) {
                    return;
                }
                int m1310j2 = m1310j(this.f2107m, max2, iArr2, this.f2113s.computeVerticalScrollRange(), this.f2113s.computeVerticalScrollOffset(), this.f2112r);
                if (m1310j2 != 0) {
                    this.f2113s.scrollBy(0, m1310j2);
                }
                this.f2107m = max2;
            }
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0287l
    /* renamed from: e */
    public final void mo1111e(Canvas canvas) {
        if (this.f2111q != this.f2113s.getWidth() || this.f2112r != this.f2113s.getHeight()) {
            this.f2111q = this.f2113s.getWidth();
            this.f2112r = this.f2113s.getHeight();
            m1311k(0);
            return;
        }
        if (this.f2092A != 0) {
            if (this.f2114t) {
                int i6 = this.f2111q;
                int i7 = this.f2099e;
                int i8 = i6 - i7;
                int i9 = this.f2106l;
                int i10 = this.f2105k;
                int i11 = i9 - (i10 / 2);
                this.f2097c.setBounds(0, 0, i7, i10);
                this.f2098d.setBounds(0, 0, this.f2100f, this.f2112r);
                RecyclerView recyclerView = this.f2113s;
                WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
                if (recyclerView.getLayoutDirection() == 1) {
                    this.f2098d.draw(canvas);
                    canvas.translate(this.f2099e, i11);
                    canvas.scale(-1.0f, 1.0f);
                    this.f2097c.draw(canvas);
                    canvas.scale(1.0f, 1.0f);
                    i8 = this.f2099e;
                } else {
                    canvas.translate(i8, 0.0f);
                    this.f2098d.draw(canvas);
                    canvas.translate(0.0f, i11);
                    this.f2097c.draw(canvas);
                }
                canvas.translate(-i8, -i11);
            }
            if (this.f2115u) {
                int i12 = this.f2112r;
                int i13 = this.f2103i;
                int i14 = this.f2109o;
                int i15 = this.f2108n;
                this.f2101g.setBounds(0, 0, i15, i13);
                this.f2102h.setBounds(0, 0, this.f2111q, this.f2104j);
                canvas.translate(0.0f, i12 - i13);
                this.f2102h.draw(canvas);
                canvas.translate(i14 - (i15 / 2), 0.0f);
                this.f2101g.draw(canvas);
                canvas.translate(-r4, -r0);
            }
        }
    }

    /* renamed from: f */
    public final void m1306f() {
        this.f2113s.removeCallbacks(this.f2093B);
    }

    /* renamed from: g */
    public final boolean m1307g(float f6, float f7) {
        if (f7 >= this.f2112r - this.f2103i) {
            int i6 = this.f2109o;
            int i7 = this.f2108n;
            if (f6 >= i6 - (i7 / 2) && f6 <= (i7 / 2) + i6) {
                return true;
            }
        }
        return false;
    }

    /* renamed from: h */
    public final boolean m1308h(float f6, float f7) {
        RecyclerView recyclerView = this.f2113s;
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        if (recyclerView.getLayoutDirection() == 1) {
            if (f6 > this.f2099e / 2) {
                return false;
            }
        } else if (f6 < this.f2111q - this.f2099e) {
            return false;
        }
        int i6 = this.f2106l;
        int i7 = this.f2105k;
        return f7 >= ((float) (i6 - (i7 / 2))) && f7 <= ((float) ((i7 / 2) + i6));
    }

    /* renamed from: i */
    public final void m1309i() {
        this.f2113s.invalidate();
    }

    /* renamed from: j */
    public final int m1310j(float f6, float f7, int[] iArr, int i6, int i7, int i8) {
        int i9 = iArr[1] - iArr[0];
        if (i9 == 0) {
            return 0;
        }
        int i10 = i6 - i8;
        int i11 = (int) (((f7 - f6) / i9) * i10);
        int i12 = i7 + i11;
        if (i12 >= i10 || i12 < 0) {
            return 0;
        }
        return i11;
    }

    /* renamed from: k */
    public final void m1311k(int i6) {
        int i7;
        if (i6 == 2 && this.f2116v != 2) {
            this.f2097c.setState(f2090D);
            m1306f();
        }
        if (i6 == 0) {
            m1309i();
        } else {
            m1312l();
        }
        if (this.f2116v != 2 || i6 == 2) {
            i7 = i6 == 1 ? 1500 : 1200;
            this.f2116v = i6;
        }
        this.f2097c.setState(f2091E);
        m1306f();
        this.f2113s.postDelayed(this.f2093B, i7);
        this.f2116v = i6;
    }

    /* renamed from: l */
    public final void m1312l() {
        int i6 = this.f2092A;
        if (i6 != 0) {
            if (i6 != 3) {
                return;
            } else {
                this.f2120z.cancel();
            }
        }
        this.f2092A = 1;
        ValueAnimator valueAnimator = this.f2120z;
        valueAnimator.setFloatValues(((Float) valueAnimator.getAnimatedValue()).floatValue(), 1.0f);
        this.f2120z.setDuration(500L);
        this.f2120z.setStartDelay(0L);
        this.f2120z.start();
    }
}
