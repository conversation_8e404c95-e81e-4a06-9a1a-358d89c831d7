package androidx.recyclerview.widget;

import androidx.recyclerview.widget.C0321k;
import androidx.recyclerview.widget.RecyclerView;

/* renamed from: androidx.recyclerview.widget.z */
/* loaded from: classes.dex */
public abstract class AbstractC0336z extends RecyclerView.AbstractC0285j {

    /* renamed from: g */
    public boolean f2171g = true;

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0285j
    /* renamed from: a */
    public final boolean mo1102a(RecyclerView.AbstractC0277b0 abstractC0277b0, RecyclerView.AbstractC0277b0 abstractC0277b02, RecyclerView.AbstractC0285j.c cVar, RecyclerView.AbstractC0285j.c cVar2) {
        int i6;
        int i7;
        int i8 = cVar.f1879a;
        int i9 = cVar.f1880b;
        if (abstractC0277b02.m1076u()) {
            int i10 = cVar.f1879a;
            i7 = cVar.f1880b;
            i6 = i10;
        } else {
            i6 = cVar2.f1879a;
            i7 = cVar2.f1880b;
        }
        C0321k c0321k = (C0321k) this;
        if (abstractC0277b0 == abstractC0277b02) {
            return c0321k.mo1300h(abstractC0277b0, i8, i9, i6, i7);
        }
        float translationX = abstractC0277b0.f1852a.getTranslationX();
        float translationY = abstractC0277b0.f1852a.getTranslationY();
        float alpha = abstractC0277b0.f1852a.getAlpha();
        c0321k.m1305m(abstractC0277b0);
        abstractC0277b0.f1852a.setTranslationX(translationX);
        abstractC0277b0.f1852a.setTranslationY(translationY);
        abstractC0277b0.f1852a.setAlpha(alpha);
        c0321k.m1305m(abstractC0277b02);
        abstractC0277b02.f1852a.setTranslationX(-((int) ((i6 - i8) - translationX)));
        abstractC0277b02.f1852a.setTranslationY(-((int) ((i7 - i9) - translationY)));
        abstractC0277b02.f1852a.setAlpha(0.0f);
        c0321k.f2071k.add(new C0321k.a(abstractC0277b0, abstractC0277b02, i8, i9, i6, i7));
        return true;
    }

    /* renamed from: h */
    public abstract boolean mo1300h(RecyclerView.AbstractC0277b0 abstractC0277b0, int i6, int i7, int i8, int i9);
}
