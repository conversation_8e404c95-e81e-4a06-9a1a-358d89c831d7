package androidx.recyclerview.widget;

import android.content.Context;
import android.util.DisplayMetrics;
import android.view.View;
import androidx.recyclerview.widget.RecyclerView;

/* renamed from: androidx.recyclerview.widget.t */
/* loaded from: classes.dex */
public final class C0330t extends C0325o {

    /* renamed from: q */
    public final /* synthetic */ C0331u f2162q;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C0330t(C0331u c0331u, Context context) {
        super(context);
        this.f2162q = c0331u;
    }

    @Override // androidx.recyclerview.widget.C0325o, androidx.recyclerview.widget.RecyclerView.AbstractC0299x
    /* renamed from: c */
    public final void mo1191c(View view, RecyclerView.AbstractC0299x.a aVar) {
        C0331u c0331u = this.f2162q;
        int[] mo1264b = c0331u.mo1264b(c0331u.f2017a.getLayoutManager(), view);
        int i6 = mo1264b[0];
        int i7 = mo1264b[1];
        int ceil = (int) Math.ceil(mo1321g(Math.max(Math.abs(i6), Math.abs(i7))) / 0.3356d);
        if (ceil > 0) {
            aVar.m1194b(i6, i7, ceil, this.f2151j);
        }
    }

    @Override // androidx.recyclerview.widget.C0325o
    /* renamed from: f */
    public final float mo1320f(DisplayMetrics displayMetrics) {
        return 100.0f / displayMetrics.densityDpi;
    }

    @Override // androidx.recyclerview.widget.C0325o
    /* renamed from: g */
    public final int mo1321g(int i6) {
        return Math.min(100, super.mo1321g(i6));
    }
}
