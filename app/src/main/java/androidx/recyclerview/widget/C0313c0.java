package androidx.recyclerview.widget;

import androidx.recyclerview.widget.RecyclerView;
import p022d0.C0699d;
import p022d0.InterfaceC0698c;
import p077l.C1050d;
import p077l.C1053g;

/* renamed from: androidx.recyclerview.widget.c0 */
/* loaded from: classes.dex */
public final class C0313c0 {

    /* renamed from: a */
    public final C1053g<RecyclerView.AbstractC0277b0, a> f2035a = new C1053g<>();

    /* renamed from: b */
    public final C1050d<RecyclerView.AbstractC0277b0> f2036b = new C1050d<>();

    /* renamed from: androidx.recyclerview.widget.c0$a */
    public static class a {

        /* renamed from: d */
        public static InterfaceC0698c<a> f2037d = new C0699d(20);

        /* renamed from: a */
        public int f2038a;

        /* renamed from: b */
        public RecyclerView.AbstractC0285j.c f2039b;

        /* renamed from: c */
        public RecyclerView.AbstractC0285j.c f2040c;

        /* renamed from: a */
        public static a m1298a() {
            a mo2056b = f2037d.mo2056b();
            return mo2056b == null ? new a() : mo2056b;
        }

        /* renamed from: b */
        public static void m1299b(a aVar) {
            aVar.f2038a = 0;
            aVar.f2039b = null;
            aVar.f2040c = null;
            f2037d.mo2055a(aVar);
        }
    }

    /* renamed from: a */
    public final void m1292a(RecyclerView.AbstractC0277b0 abstractC0277b0) {
        a orDefault = this.f2035a.getOrDefault(abstractC0277b0, null);
        if (orDefault == null) {
            orDefault = a.m1298a();
            this.f2035a.put(abstractC0277b0, orDefault);
        }
        orDefault.f2038a |= 1;
    }

    /* renamed from: b */
    public final void m1293b(RecyclerView.AbstractC0277b0 abstractC0277b0, RecyclerView.AbstractC0285j.c cVar) {
        a orDefault = this.f2035a.getOrDefault(abstractC0277b0, null);
        if (orDefault == null) {
            orDefault = a.m1298a();
            this.f2035a.put(abstractC0277b0, orDefault);
        }
        orDefault.f2040c = cVar;
        orDefault.f2038a |= 8;
    }

    /* renamed from: c */
    public final void m1294c(RecyclerView.AbstractC0277b0 abstractC0277b0, RecyclerView.AbstractC0285j.c cVar) {
        a orDefault = this.f2035a.getOrDefault(abstractC0277b0, null);
        if (orDefault == null) {
            orDefault = a.m1298a();
            this.f2035a.put(abstractC0277b0, orDefault);
        }
        orDefault.f2039b = cVar;
        orDefault.f2038a |= 4;
    }

    /* renamed from: d */
    public final RecyclerView.AbstractC0285j.c m1295d(RecyclerView.AbstractC0277b0 abstractC0277b0, int i6) {
        a m2694l;
        RecyclerView.AbstractC0285j.c cVar;
        int m2687e = this.f2035a.m2687e(abstractC0277b0);
        if (m2687e >= 0 && (m2694l = this.f2035a.m2694l(m2687e)) != null) {
            int i7 = m2694l.f2038a;
            if ((i7 & i6) != 0) {
                int i8 = (~i6) & i7;
                m2694l.f2038a = i8;
                if (i6 == 4) {
                    cVar = m2694l.f2039b;
                } else {
                    if (i6 != 8) {
                        throw new IllegalArgumentException("Must provide flag PRE or POST");
                    }
                    cVar = m2694l.f2040c;
                }
                if ((i8 & 12) == 0) {
                    this.f2035a.mo2692j(m2687e);
                    a.m1299b(m2694l);
                }
                return cVar;
            }
        }
        return null;
    }

    /* renamed from: e */
    public final void m1296e(RecyclerView.AbstractC0277b0 abstractC0277b0) {
        a orDefault = this.f2035a.getOrDefault(abstractC0277b0, null);
        if (orDefault == null) {
            return;
        }
        orDefault.f2038a &= -2;
    }

    /* renamed from: f */
    public final void m1297f(RecyclerView.AbstractC0277b0 abstractC0277b0) {
        int m2674j = this.f2036b.m2674j() - 1;
        while (true) {
            if (m2674j < 0) {
                break;
            }
            if (abstractC0277b0 == this.f2036b.m2675k(m2674j)) {
                C1050d<RecyclerView.AbstractC0277b0> c1050d = this.f2036b;
                Object[] objArr = c1050d.f5010l;
                Object obj = objArr[m2674j];
                Object obj2 = C1050d.f5007n;
                if (obj != obj2) {
                    objArr[m2674j] = obj2;
                    c1050d.f5008j = true;
                }
            } else {
                m2674j--;
            }
        }
        a remove = this.f2035a.remove(abstractC0277b0);
        if (remove != null) {
            a.m1299b(remove);
        }
    }
}
