package androidx.recyclerview.widget;

import android.content.Context;
import android.graphics.PointF;
import android.util.DisplayMetrics;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.LinearInterpolator;
import androidx.recyclerview.widget.RecyclerView;

/* renamed from: androidx.recyclerview.widget.o */
/* loaded from: classes.dex */
public class C0325o extends RecyclerView.AbstractC0299x {

    /* renamed from: k */
    public PointF f2152k;

    /* renamed from: l */
    public final DisplayMetrics f2153l;

    /* renamed from: n */
    public float f2155n;

    /* renamed from: i */
    public final LinearInterpolator f2150i = new LinearInterpolator();

    /* renamed from: j */
    public final DecelerateInterpolator f2151j = new DecelerateInterpolator();

    /* renamed from: m */
    public boolean f2154m = false;

    /* renamed from: o */
    public int f2156o = 0;

    /* renamed from: p */
    public int f2157p = 0;

    public C0325o(Context context) {
        this.f2153l = context.getResources().getDisplayMetrics();
    }

    /* JADX WARN: Removed duplicated region for block: B:14:0x004c  */
    /* JADX WARN: Removed duplicated region for block: B:20:0x005e  */
    /* JADX WARN: Removed duplicated region for block: B:25:0x00a9  */
    /* JADX WARN: Removed duplicated region for block: B:28:? A[RETURN, SYNTHETIC] */
    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0299x
    /* renamed from: c */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void mo1191c(android.view.View r12, androidx.recyclerview.widget.RecyclerView.AbstractC0299x.a r13) {
        /*
            r11 = this;
            android.graphics.PointF r0 = r11.f2152k
            r1 = 1
            r2 = -1
            r3 = 0
            r4 = 0
            if (r0 == 0) goto L15
            float r0 = r0.x
            int r0 = (r0 > r3 ? 1 : (r0 == r3 ? 0 : -1))
            if (r0 != 0) goto Lf
            goto L15
        Lf:
            if (r0 <= 0) goto L13
            r10 = r1
            goto L16
        L13:
            r10 = r2
            goto L16
        L15:
            r10 = r4
        L16:
            androidx.recyclerview.widget.RecyclerView$m r0 = r11.f1927c
            if (r0 == 0) goto L47
            boolean r5 = r0.mo963e()
            if (r5 != 0) goto L21
            goto L47
        L21:
            android.view.ViewGroup$LayoutParams r5 = r12.getLayoutParams()
            androidx.recyclerview.widget.RecyclerView$n r5 = (androidx.recyclerview.widget.RecyclerView.C0289n) r5
            int r6 = r0.m1119C(r12)
            int r7 = r5.leftMargin
            int r6 = r6 - r7
            int r7 = r0.m1121D(r12)
            int r5 = r5.rightMargin
            int r7 = r7 + r5
            int r8 = r0.m1132K()
            int r5 = r0.f1897p
            int r0 = r0.m1133L()
            int r9 = r5 - r0
            r5 = r11
            int r0 = r5.m1319e(r6, r7, r8, r9, r10)
            goto L48
        L47:
            r0 = r4
        L48:
            android.graphics.PointF r5 = r11.f2152k
            if (r5 == 0) goto L59
            float r5 = r5.y
            int r3 = (r5 > r3 ? 1 : (r5 == r3 ? 0 : -1))
            if (r3 != 0) goto L53
            goto L59
        L53:
            if (r3 <= 0) goto L57
            r10 = r1
            goto L5a
        L57:
            r10 = r2
            goto L5a
        L59:
            r10 = r4
        L5a:
            androidx.recyclerview.widget.RecyclerView$m r1 = r11.f1927c
            if (r1 == 0) goto L8c
            boolean r2 = r1.mo964f()
            if (r2 != 0) goto L65
            goto L8c
        L65:
            android.view.ViewGroup$LayoutParams r2 = r12.getLayoutParams()
            androidx.recyclerview.widget.RecyclerView$n r2 = (androidx.recyclerview.widget.RecyclerView.C0289n) r2
            int r3 = r1.m1123E(r12)
            int r4 = r2.topMargin
            int r6 = r3 - r4
            int r12 = r1.m1116A(r12)
            int r2 = r2.bottomMargin
            int r7 = r12 + r2
            int r8 = r1.m1134M()
            int r12 = r1.f1898q
            int r1 = r1.m1131J()
            int r9 = r12 - r1
            r5 = r11
            int r4 = r5.m1319e(r6, r7, r8, r9, r10)
        L8c:
            int r12 = r0 * r0
            int r1 = r4 * r4
            int r1 = r1 + r12
            double r1 = (double) r1
            double r1 = java.lang.Math.sqrt(r1)
            int r12 = (int) r1
            int r12 = r11.mo1321g(r12)
            double r1 = (double) r12
            r5 = 4599717252057688074(0x3fd57a786c22680a, double:0.3356)
            double r1 = r1 / r5
            double r1 = java.lang.Math.ceil(r1)
            int r12 = (int) r1
            if (r12 <= 0) goto Lb0
            int r0 = -r0
            int r1 = -r4
            android.view.animation.DecelerateInterpolator r2 = r11.f2151j
            r13.m1194b(r0, r1, r12, r2)
        Lb0:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.C0325o.mo1191c(android.view.View, androidx.recyclerview.widget.RecyclerView$x$a):void");
    }

    /* renamed from: e */
    public final int m1319e(int i6, int i7, int i8, int i9, int i10) {
        if (i10 == -1) {
            return i8 - i6;
        }
        if (i10 != 0) {
            if (i10 == 1) {
                return i9 - i7;
            }
            throw new IllegalArgumentException("snap preference should be one of the constants defined in SmoothScroller, starting with SNAP_");
        }
        int i11 = i8 - i6;
        if (i11 > 0) {
            return i11;
        }
        int i12 = i9 - i7;
        if (i12 < 0) {
            return i12;
        }
        return 0;
    }

    /* renamed from: f */
    public float mo1320f(DisplayMetrics displayMetrics) {
        return 25.0f / displayMetrics.densityDpi;
    }

    /* renamed from: g */
    public int mo1321g(int i6) {
        float abs = Math.abs(i6);
        if (!this.f2154m) {
            this.f2155n = mo1320f(this.f2153l);
            this.f2154m = true;
        }
        return (int) Math.ceil(abs * this.f2155n);
    }
}
