package androidx.recyclerview.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.PointF;
import android.graphics.Rect;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import androidx.activity.result.C0052a;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RunnableC0323m;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.BitSet;
import java.util.List;
import java.util.Objects;
import p036f0.C0831b;

/* loaded from: classes.dex */
public class StaggeredGridLayoutManager extends RecyclerView.AbstractC0288m implements RecyclerView.AbstractC0299x.b {

    /* renamed from: A */
    public BitSet f1954A;

    /* renamed from: F */
    public boolean f1959F;

    /* renamed from: G */
    public boolean f1960G;

    /* renamed from: H */
    public C0306e f1961H;

    /* renamed from: L */
    public int[] f1965L;

    /* renamed from: r */
    public int f1967r;

    /* renamed from: s */
    public C0307f[] f1968s;

    /* renamed from: t */
    public AbstractC0329s f1969t;

    /* renamed from: u */
    public AbstractC0329s f1970u;

    /* renamed from: v */
    public int f1971v;

    /* renamed from: w */
    public int f1972w;

    /* renamed from: x */
    public final C0324n f1973x;

    /* renamed from: y */
    public boolean f1974y;

    /* renamed from: z */
    public boolean f1975z = false;

    /* renamed from: B */
    public int f1955B = -1;

    /* renamed from: C */
    public int f1956C = Integer.MIN_VALUE;

    /* renamed from: D */
    public C0305d f1957D = new C0305d();

    /* renamed from: E */
    public int f1958E = 2;

    /* renamed from: I */
    public final Rect f1962I = new Rect();

    /* renamed from: J */
    public final C0303b f1963J = new C0303b();

    /* renamed from: K */
    public boolean f1964K = true;

    /* renamed from: M */
    public final RunnableC0302a f1966M = new RunnableC0302a();

    /* renamed from: androidx.recyclerview.widget.StaggeredGridLayoutManager$a */
    public class RunnableC0302a implements Runnable {
        public RunnableC0302a() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            StaggeredGridLayoutManager.this.m1198L0();
        }
    }

    /* renamed from: androidx.recyclerview.widget.StaggeredGridLayoutManager$b */
    public class C0303b {

        /* renamed from: a */
        public int f1977a;

        /* renamed from: b */
        public int f1978b;

        /* renamed from: c */
        public boolean f1979c;

        /* renamed from: d */
        public boolean f1980d;

        /* renamed from: e */
        public boolean f1981e;

        /* renamed from: f */
        public int[] f1982f;

        public C0303b() {
            m1229b();
        }

        /* renamed from: a */
        public final void m1228a() {
            this.f1978b = this.f1979c ? StaggeredGridLayoutManager.this.f1969t.mo1327g() : StaggeredGridLayoutManager.this.f1969t.mo1331k();
        }

        /* renamed from: b */
        public final void m1229b() {
            this.f1977a = -1;
            this.f1978b = Integer.MIN_VALUE;
            this.f1979c = false;
            this.f1980d = false;
            this.f1981e = false;
            int[] iArr = this.f1982f;
            if (iArr != null) {
                Arrays.fill(iArr, -1);
            }
        }
    }

    /* renamed from: androidx.recyclerview.widget.StaggeredGridLayoutManager$c */
    public static class C0304c extends RecyclerView.C0289n {

        /* renamed from: e */
        public C0307f f1984e;

        public C0304c(int i6, int i7) {
            super(i6, i7);
        }

        public C0304c(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
        }

        public C0304c(ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
        }

        public C0304c(ViewGroup.MarginLayoutParams marginLayoutParams) {
            super(marginLayoutParams);
        }
    }

    /* renamed from: androidx.recyclerview.widget.StaggeredGridLayoutManager$d */
    public static class C0305d {

        /* renamed from: a */
        public int[] f1985a;

        /* renamed from: b */
        public List<a> f1986b;

        @SuppressLint({"BanParcelableUsage"})
        /* renamed from: androidx.recyclerview.widget.StaggeredGridLayoutManager$d$a */
        public static class a implements Parcelable {
            public static final Parcelable.Creator<a> CREATOR = new C2129a();

            /* renamed from: j */
            public int f1987j;

            /* renamed from: k */
            public int f1988k;

            /* renamed from: l */
            public int[] f1989l;

            /* renamed from: m */
            public boolean f1990m;

            /* renamed from: androidx.recyclerview.widget.StaggeredGridLayoutManager$d$a$a, reason: collision with other inner class name */
            public static class C2129a implements Parcelable.Creator<a> {
                @Override // android.os.Parcelable.Creator
                public final a createFromParcel(Parcel parcel) {
                    return new a(parcel);
                }

                @Override // android.os.Parcelable.Creator
                public final a[] newArray(int i6) {
                    return new a[i6];
                }
            }

            public a() {
            }

            public a(Parcel parcel) {
                this.f1987j = parcel.readInt();
                this.f1988k = parcel.readInt();
                this.f1990m = parcel.readInt() == 1;
                int readInt = parcel.readInt();
                if (readInt > 0) {
                    int[] iArr = new int[readInt];
                    this.f1989l = iArr;
                    parcel.readIntArray(iArr);
                }
            }

            @Override // android.os.Parcelable
            public final int describeContents() {
                return 0;
            }

            public final String toString() {
                StringBuilder m104h = C0052a.m104h("FullSpanItem{mPosition=");
                m104h.append(this.f1987j);
                m104h.append(", mGapDir=");
                m104h.append(this.f1988k);
                m104h.append(", mHasUnwantedGapAfter=");
                m104h.append(this.f1990m);
                m104h.append(", mGapPerSpan=");
                m104h.append(Arrays.toString(this.f1989l));
                m104h.append('}');
                return m104h.toString();
            }

            @Override // android.os.Parcelable
            public final void writeToParcel(Parcel parcel, int i6) {
                parcel.writeInt(this.f1987j);
                parcel.writeInt(this.f1988k);
                parcel.writeInt(this.f1990m ? 1 : 0);
                int[] iArr = this.f1989l;
                if (iArr == null || iArr.length <= 0) {
                    parcel.writeInt(0);
                } else {
                    parcel.writeInt(iArr.length);
                    parcel.writeIntArray(this.f1989l);
                }
            }
        }

        /* renamed from: a */
        public final void m1230a() {
            int[] iArr = this.f1985a;
            if (iArr != null) {
                Arrays.fill(iArr, -1);
            }
            this.f1986b = null;
        }

        /* renamed from: b */
        public final void m1231b(int i6) {
            int[] iArr = this.f1985a;
            if (iArr == null) {
                int[] iArr2 = new int[Math.max(i6, 10) + 1];
                this.f1985a = iArr2;
                Arrays.fill(iArr2, -1);
            } else if (i6 >= iArr.length) {
                int length = iArr.length;
                while (length <= i6) {
                    length *= 2;
                }
                int[] iArr3 = new int[length];
                this.f1985a = iArr3;
                System.arraycopy(iArr, 0, iArr3, 0, iArr.length);
                int[] iArr4 = this.f1985a;
                Arrays.fill(iArr4, iArr.length, iArr4.length, -1);
            }
        }

        /* renamed from: c */
        public final a m1232c(int i6) {
            List<a> list = this.f1986b;
            if (list == null) {
                return null;
            }
            for (int size = list.size() - 1; size >= 0; size--) {
                a aVar = this.f1986b.get(size);
                if (aVar.f1987j == i6) {
                    return aVar;
                }
            }
            return null;
        }

        /* JADX WARN: Removed duplicated region for block: B:12:0x0048  */
        /* JADX WARN: Removed duplicated region for block: B:14:0x0052  */
        /* renamed from: d */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct code enable 'Show inconsistent code' option in preferences
        */
        public final int m1233d(int r5) {
            /*
                r4 = this;
                int[] r0 = r4.f1985a
                r1 = -1
                if (r0 != 0) goto L6
                return r1
            L6:
                int r0 = r0.length
                if (r5 < r0) goto La
                return r1
            La:
                java.util.List<androidx.recyclerview.widget.StaggeredGridLayoutManager$d$a> r0 = r4.f1986b
                if (r0 != 0) goto L10
            Le:
                r0 = r1
                goto L46
            L10:
                androidx.recyclerview.widget.StaggeredGridLayoutManager$d$a r0 = r4.m1232c(r5)
                if (r0 == 0) goto L1b
                java.util.List<androidx.recyclerview.widget.StaggeredGridLayoutManager$d$a> r2 = r4.f1986b
                r2.remove(r0)
            L1b:
                java.util.List<androidx.recyclerview.widget.StaggeredGridLayoutManager$d$a> r0 = r4.f1986b
                int r0 = r0.size()
                r2 = 0
            L22:
                if (r2 >= r0) goto L34
                java.util.List<androidx.recyclerview.widget.StaggeredGridLayoutManager$d$a> r3 = r4.f1986b
                java.lang.Object r3 = r3.get(r2)
                androidx.recyclerview.widget.StaggeredGridLayoutManager$d$a r3 = (androidx.recyclerview.widget.StaggeredGridLayoutManager.C0305d.a) r3
                int r3 = r3.f1987j
                if (r3 < r5) goto L31
                goto L35
            L31:
                int r2 = r2 + 1
                goto L22
            L34:
                r2 = r1
            L35:
                if (r2 == r1) goto Le
                java.util.List<androidx.recyclerview.widget.StaggeredGridLayoutManager$d$a> r0 = r4.f1986b
                java.lang.Object r0 = r0.get(r2)
                androidx.recyclerview.widget.StaggeredGridLayoutManager$d$a r0 = (androidx.recyclerview.widget.StaggeredGridLayoutManager.C0305d.a) r0
                java.util.List<androidx.recyclerview.widget.StaggeredGridLayoutManager$d$a> r3 = r4.f1986b
                r3.remove(r2)
                int r0 = r0.f1987j
            L46:
                if (r0 != r1) goto L52
                int[] r0 = r4.f1985a
                int r2 = r0.length
                java.util.Arrays.fill(r0, r5, r2, r1)
                int[] r5 = r4.f1985a
                int r5 = r5.length
                return r5
            L52:
                int[] r2 = r4.f1985a
                int r0 = r0 + 1
                java.util.Arrays.fill(r2, r5, r0, r1)
                return r0
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.StaggeredGridLayoutManager.C0305d.m1233d(int):int");
        }

        /* renamed from: e */
        public final void m1234e(int i6, int i7) {
            int[] iArr = this.f1985a;
            if (iArr == null || i6 >= iArr.length) {
                return;
            }
            int i8 = i6 + i7;
            m1231b(i8);
            int[] iArr2 = this.f1985a;
            System.arraycopy(iArr2, i6, iArr2, i8, (iArr2.length - i6) - i7);
            Arrays.fill(this.f1985a, i6, i8, -1);
            List<a> list = this.f1986b;
            if (list == null) {
                return;
            }
            for (int size = list.size() - 1; size >= 0; size--) {
                a aVar = this.f1986b.get(size);
                int i9 = aVar.f1987j;
                if (i9 >= i6) {
                    aVar.f1987j = i9 + i7;
                }
            }
        }

        /* renamed from: f */
        public final void m1235f(int i6, int i7) {
            int[] iArr = this.f1985a;
            if (iArr == null || i6 >= iArr.length) {
                return;
            }
            int i8 = i6 + i7;
            m1231b(i8);
            int[] iArr2 = this.f1985a;
            System.arraycopy(iArr2, i8, iArr2, i6, (iArr2.length - i6) - i7);
            int[] iArr3 = this.f1985a;
            Arrays.fill(iArr3, iArr3.length - i7, iArr3.length, -1);
            List<a> list = this.f1986b;
            if (list == null) {
                return;
            }
            for (int size = list.size() - 1; size >= 0; size--) {
                a aVar = this.f1986b.get(size);
                int i9 = aVar.f1987j;
                if (i9 >= i6) {
                    if (i9 < i8) {
                        this.f1986b.remove(size);
                    } else {
                        aVar.f1987j = i9 - i7;
                    }
                }
            }
        }
    }

    @SuppressLint({"BanParcelableUsage"})
    /* renamed from: androidx.recyclerview.widget.StaggeredGridLayoutManager$e */
    public static class C0306e implements Parcelable {
        public static final Parcelable.Creator<C0306e> CREATOR = new a();

        /* renamed from: j */
        public int f1991j;

        /* renamed from: k */
        public int f1992k;

        /* renamed from: l */
        public int f1993l;

        /* renamed from: m */
        public int[] f1994m;

        /* renamed from: n */
        public int f1995n;

        /* renamed from: o */
        public int[] f1996o;

        /* renamed from: p */
        public List<C0305d.a> f1997p;

        /* renamed from: q */
        public boolean f1998q;

        /* renamed from: r */
        public boolean f1999r;

        /* renamed from: s */
        public boolean f2000s;

        /* renamed from: androidx.recyclerview.widget.StaggeredGridLayoutManager$e$a */
        public static class a implements Parcelable.Creator<C0306e> {
            @Override // android.os.Parcelable.Creator
            public final C0306e createFromParcel(Parcel parcel) {
                return new C0306e(parcel);
            }

            @Override // android.os.Parcelable.Creator
            public final C0306e[] newArray(int i6) {
                return new C0306e[i6];
            }
        }

        public C0306e() {
        }

        public C0306e(Parcel parcel) {
            this.f1991j = parcel.readInt();
            this.f1992k = parcel.readInt();
            int readInt = parcel.readInt();
            this.f1993l = readInt;
            if (readInt > 0) {
                int[] iArr = new int[readInt];
                this.f1994m = iArr;
                parcel.readIntArray(iArr);
            }
            int readInt2 = parcel.readInt();
            this.f1995n = readInt2;
            if (readInt2 > 0) {
                int[] iArr2 = new int[readInt2];
                this.f1996o = iArr2;
                parcel.readIntArray(iArr2);
            }
            this.f1998q = parcel.readInt() == 1;
            this.f1999r = parcel.readInt() == 1;
            this.f2000s = parcel.readInt() == 1;
            this.f1997p = parcel.readArrayList(C0305d.a.class.getClassLoader());
        }

        public C0306e(C0306e c0306e) {
            this.f1993l = c0306e.f1993l;
            this.f1991j = c0306e.f1991j;
            this.f1992k = c0306e.f1992k;
            this.f1994m = c0306e.f1994m;
            this.f1995n = c0306e.f1995n;
            this.f1996o = c0306e.f1996o;
            this.f1998q = c0306e.f1998q;
            this.f1999r = c0306e.f1999r;
            this.f2000s = c0306e.f2000s;
            this.f1997p = c0306e.f1997p;
        }

        @Override // android.os.Parcelable
        public final int describeContents() {
            return 0;
        }

        @Override // android.os.Parcelable
        public final void writeToParcel(Parcel parcel, int i6) {
            parcel.writeInt(this.f1991j);
            parcel.writeInt(this.f1992k);
            parcel.writeInt(this.f1993l);
            if (this.f1993l > 0) {
                parcel.writeIntArray(this.f1994m);
            }
            parcel.writeInt(this.f1995n);
            if (this.f1995n > 0) {
                parcel.writeIntArray(this.f1996o);
            }
            parcel.writeInt(this.f1998q ? 1 : 0);
            parcel.writeInt(this.f1999r ? 1 : 0);
            parcel.writeInt(this.f2000s ? 1 : 0);
            parcel.writeList(this.f1997p);
        }
    }

    /* renamed from: androidx.recyclerview.widget.StaggeredGridLayoutManager$f */
    public class C0307f {

        /* renamed from: a */
        public ArrayList<View> f2001a = new ArrayList<>();

        /* renamed from: b */
        public int f2002b = Integer.MIN_VALUE;

        /* renamed from: c */
        public int f2003c = Integer.MIN_VALUE;

        /* renamed from: d */
        public int f2004d = 0;

        /* renamed from: e */
        public final int f2005e;

        public C0307f(int i6) {
            this.f2005e = i6;
        }

        /* renamed from: a */
        public final void m1236a(View view) {
            C0304c c0304c = (C0304c) view.getLayoutParams();
            c0304c.f1984e = this;
            this.f2001a.add(view);
            this.f2003c = Integer.MIN_VALUE;
            if (this.f2001a.size() == 1) {
                this.f2002b = Integer.MIN_VALUE;
            }
            if (c0304c.m1167c() || c0304c.m1166b()) {
                this.f2004d = StaggeredGridLayoutManager.this.f1969t.mo1323c(view) + this.f2004d;
            }
        }

        /* renamed from: b */
        public final void m1237b() {
            View view = this.f2001a.get(r0.size() - 1);
            C0304c m1245j = m1245j(view);
            this.f2003c = StaggeredGridLayoutManager.this.f1969t.mo1322b(view);
            Objects.requireNonNull(m1245j);
        }

        /* renamed from: c */
        public final void m1238c() {
            View view = this.f2001a.get(0);
            C0304c m1245j = m1245j(view);
            this.f2002b = StaggeredGridLayoutManager.this.f1969t.mo1325e(view);
            Objects.requireNonNull(m1245j);
        }

        /* renamed from: d */
        public final void m1239d() {
            this.f2001a.clear();
            this.f2002b = Integer.MIN_VALUE;
            this.f2003c = Integer.MIN_VALUE;
            this.f2004d = 0;
        }

        /* renamed from: e */
        public final int m1240e() {
            int i6;
            int size;
            if (StaggeredGridLayoutManager.this.f1974y) {
                i6 = this.f2001a.size() - 1;
                size = -1;
            } else {
                i6 = 0;
                size = this.f2001a.size();
            }
            return m1242g(i6, size);
        }

        /* renamed from: f */
        public final int m1241f() {
            int size;
            int i6;
            if (StaggeredGridLayoutManager.this.f1974y) {
                size = 0;
                i6 = this.f2001a.size();
            } else {
                size = this.f2001a.size() - 1;
                i6 = -1;
            }
            return m1242g(size, i6);
        }

        /* renamed from: g */
        public final int m1242g(int i6, int i7) {
            int mo1331k = StaggeredGridLayoutManager.this.f1969t.mo1331k();
            int mo1327g = StaggeredGridLayoutManager.this.f1969t.mo1327g();
            int i8 = i7 > i6 ? 1 : -1;
            while (i6 != i7) {
                View view = this.f2001a.get(i6);
                int mo1325e = StaggeredGridLayoutManager.this.f1969t.mo1325e(view);
                int mo1322b = StaggeredGridLayoutManager.this.f1969t.mo1322b(view);
                boolean z5 = mo1325e <= mo1327g;
                boolean z6 = mo1322b >= mo1331k;
                if (z5 && z6 && (mo1325e < mo1331k || mo1322b > mo1327g)) {
                    return StaggeredGridLayoutManager.this.m1135N(view);
                }
                i6 += i8;
            }
            return -1;
        }

        /* renamed from: h */
        public final int m1243h(int i6) {
            int i7 = this.f2003c;
            if (i7 != Integer.MIN_VALUE) {
                return i7;
            }
            if (this.f2001a.size() == 0) {
                return i6;
            }
            m1237b();
            return this.f2003c;
        }

        /* renamed from: i */
        public final View m1244i(int i6, int i7) {
            View view = null;
            if (i7 != -1) {
                int size = this.f2001a.size() - 1;
                while (size >= 0) {
                    View view2 = this.f2001a.get(size);
                    StaggeredGridLayoutManager staggeredGridLayoutManager = StaggeredGridLayoutManager.this;
                    if (staggeredGridLayoutManager.f1974y && staggeredGridLayoutManager.m1135N(view2) >= i6) {
                        break;
                    }
                    StaggeredGridLayoutManager staggeredGridLayoutManager2 = StaggeredGridLayoutManager.this;
                    if ((!staggeredGridLayoutManager2.f1974y && staggeredGridLayoutManager2.m1135N(view2) <= i6) || !view2.hasFocusable()) {
                        break;
                    }
                    size--;
                    view = view2;
                }
            } else {
                int size2 = this.f2001a.size();
                int i8 = 0;
                while (i8 < size2) {
                    View view3 = this.f2001a.get(i8);
                    StaggeredGridLayoutManager staggeredGridLayoutManager3 = StaggeredGridLayoutManager.this;
                    if (staggeredGridLayoutManager3.f1974y && staggeredGridLayoutManager3.m1135N(view3) <= i6) {
                        break;
                    }
                    StaggeredGridLayoutManager staggeredGridLayoutManager4 = StaggeredGridLayoutManager.this;
                    if ((!staggeredGridLayoutManager4.f1974y && staggeredGridLayoutManager4.m1135N(view3) >= i6) || !view3.hasFocusable()) {
                        break;
                    }
                    i8++;
                    view = view3;
                }
            }
            return view;
        }

        /* renamed from: j */
        public final C0304c m1245j(View view) {
            return (C0304c) view.getLayoutParams();
        }

        /* renamed from: k */
        public final int m1246k(int i6) {
            int i7 = this.f2002b;
            if (i7 != Integer.MIN_VALUE) {
                return i7;
            }
            if (this.f2001a.size() == 0) {
                return i6;
            }
            m1238c();
            return this.f2002b;
        }

        /* renamed from: l */
        public final void m1247l() {
            int size = this.f2001a.size();
            View remove = this.f2001a.remove(size - 1);
            C0304c m1245j = m1245j(remove);
            m1245j.f1984e = null;
            if (m1245j.m1167c() || m1245j.m1166b()) {
                this.f2004d -= StaggeredGridLayoutManager.this.f1969t.mo1323c(remove);
            }
            if (size == 1) {
                this.f2002b = Integer.MIN_VALUE;
            }
            this.f2003c = Integer.MIN_VALUE;
        }

        /* renamed from: m */
        public final void m1248m() {
            View remove = this.f2001a.remove(0);
            C0304c m1245j = m1245j(remove);
            m1245j.f1984e = null;
            if (this.f2001a.size() == 0) {
                this.f2003c = Integer.MIN_VALUE;
            }
            if (m1245j.m1167c() || m1245j.m1166b()) {
                this.f2004d -= StaggeredGridLayoutManager.this.f1969t.mo1323c(remove);
            }
            this.f2002b = Integer.MIN_VALUE;
        }

        /* renamed from: n */
        public final void m1249n(View view) {
            C0304c c0304c = (C0304c) view.getLayoutParams();
            c0304c.f1984e = this;
            this.f2001a.add(0, view);
            this.f2002b = Integer.MIN_VALUE;
            if (this.f2001a.size() == 1) {
                this.f2003c = Integer.MIN_VALUE;
            }
            if (c0304c.m1167c() || c0304c.m1166b()) {
                this.f2004d = StaggeredGridLayoutManager.this.f1969t.mo1323c(view) + this.f2004d;
            }
        }
    }

    public StaggeredGridLayoutManager(Context context, AttributeSet attributeSet, int i6, int i7) {
        this.f1967r = -1;
        this.f1974y = false;
        RecyclerView.AbstractC0288m.d m1112O = RecyclerView.AbstractC0288m.m1112O(context, attributeSet, i6, i7);
        int i8 = m1112O.f1901a;
        if (i8 != 0 && i8 != 1) {
            throw new IllegalArgumentException("invalid orientation.");
        }
        mo961d(null);
        if (i8 != this.f1971v) {
            this.f1971v = i8;
            AbstractC0329s abstractC0329s = this.f1969t;
            this.f1969t = this.f1970u;
            this.f1970u = abstractC0329s;
            m1155u0();
        }
        int i9 = m1112O.f1902b;
        mo961d(null);
        if (i9 != this.f1967r) {
            this.f1957D.m1230a();
            m1155u0();
            this.f1967r = i9;
            this.f1954A = new BitSet(this.f1967r);
            this.f1968s = new C0307f[this.f1967r];
            for (int i10 = 0; i10 < this.f1967r; i10++) {
                this.f1968s[i10] = new C0307f(i10);
            }
            m1155u0();
        }
        boolean z5 = m1112O.f1903c;
        mo961d(null);
        C0306e c0306e = this.f1961H;
        if (c0306e != null && c0306e.f1998q != z5) {
            c0306e.f1998q = z5;
        }
        this.f1974y = z5;
        m1155u0();
        this.f1973x = new C0324n();
        this.f1969t = AbstractC0329s.m1336a(this, this.f1971v);
        this.f1970u = AbstractC0329s.m1336a(this, 1 - this.f1971v);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: B0 */
    public final void mo897B0(Rect rect, int i6, int i7) {
        int m1114h;
        int m1114h2;
        int m1133L = m1133L() + m1132K();
        int m1131J = m1131J() + m1134M();
        if (this.f1971v == 1) {
            m1114h2 = RecyclerView.AbstractC0288m.m1114h(i7, rect.height() + m1131J, m1128H());
            m1114h = RecyclerView.AbstractC0288m.m1114h(i6, (this.f1972w * this.f1967r) + m1133L, m1129I());
        } else {
            m1114h = RecyclerView.AbstractC0288m.m1114h(i6, rect.width() + m1133L, m1129I());
            m1114h2 = RecyclerView.AbstractC0288m.m1114h(i7, (this.f1972w * this.f1967r) + m1131J, m1128H());
        }
        m1117A0(m1114h, m1114h2);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: H0 */
    public final void mo939H0(RecyclerView recyclerView, int i6) {
        C0325o c0325o = new C0325o(recyclerView.getContext());
        c0325o.f1925a = i6;
        m1130I0(c0325o);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: J0 */
    public final boolean mo898J0() {
        return this.f1961H == null;
    }

    /* renamed from: K0 */
    public final int m1197K0(int i6) {
        if (m1157x() == 0) {
            return this.f1975z ? 1 : -1;
        }
        return (i6 < m1207U0()) != this.f1975z ? -1 : 1;
    }

    /* renamed from: L0 */
    public final boolean m1198L0() {
        int m1207U0;
        if (m1157x() != 0 && this.f1958E != 0 && this.f1890i) {
            if (this.f1975z) {
                m1207U0 = m1208V0();
                m1207U0();
            } else {
                m1207U0 = m1207U0();
                m1208V0();
            }
            if (m1207U0 == 0 && m1212Z0() != null) {
                this.f1957D.m1230a();
                this.f1889h = true;
                m1155u0();
                return true;
            }
        }
        return false;
    }

    /* renamed from: M0 */
    public final int m1199M0(RecyclerView.C0300y c0300y) {
        if (m1157x() == 0) {
            return 0;
        }
        return C0335y.m1358a(c0300y, this.f1969t, m1204R0(!this.f1964K), m1203Q0(!this.f1964K), this, this.f1964K);
    }

    /* renamed from: N0 */
    public final int m1200N0(RecyclerView.C0300y c0300y) {
        if (m1157x() == 0) {
            return 0;
        }
        return C0335y.m1359b(c0300y, this.f1969t, m1204R0(!this.f1964K), m1203Q0(!this.f1964K), this, this.f1964K, this.f1975z);
    }

    /* renamed from: O0 */
    public final int m1201O0(RecyclerView.C0300y c0300y) {
        if (m1157x() == 0) {
            return 0;
        }
        return C0335y.m1360c(c0300y, this.f1969t, m1204R0(!this.f1964K), m1203Q0(!this.f1964K), this, this.f1964K);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: P */
    public final int mo900P(RecyclerView.C0295t c0295t, RecyclerView.C0300y c0300y) {
        return this.f1971v == 0 ? this.f1967r : super.mo900P(c0295t, c0300y);
    }

    /* JADX WARN: Type inference failed for: r1v19 */
    /* JADX WARN: Type inference failed for: r1v20, types: [boolean, int] */
    /* JADX WARN: Type inference failed for: r1v35 */
    /* renamed from: P0 */
    public final int m1202P0(RecyclerView.C0295t c0295t, C0324n c0324n, RecyclerView.C0300y c0300y) {
        int i6;
        C0307f c0307f;
        ?? r12;
        int m1115y;
        boolean z5;
        int m1115y2;
        int m1246k;
        int mo1323c;
        int mo1331k;
        int mo1323c2;
        int i7;
        int i8;
        int i9;
        int i10;
        int i11;
        int i12;
        int i13 = 0;
        this.f1954A.set(0, this.f1967r, true);
        if (this.f1973x.f2149i) {
            i6 = c0324n.f2145e == 1 ? Integer.MAX_VALUE : Integer.MIN_VALUE;
        } else {
            i6 = c0324n.f2145e == 1 ? c0324n.f2147g + c0324n.f2142b : c0324n.f2146f - c0324n.f2142b;
        }
        m1224l1(c0324n.f2145e, i6);
        int mo1327g = this.f1975z ? this.f1969t.mo1327g() : this.f1969t.mo1331k();
        boolean z6 = false;
        while (true) {
            int i14 = c0324n.f2143c;
            int i15 = -1;
            if (((i14 < 0 || i14 >= c0300y.m1196b()) ? i13 : 1) == 0 || (!this.f1973x.f2149i && this.f1954A.isEmpty())) {
                break;
            }
            View view = c0295t.m1185j(c0324n.f2143c, Long.MAX_VALUE).f1852a;
            c0324n.f2143c += c0324n.f2144d;
            C0304c c0304c = (C0304c) view.getLayoutParams();
            int m1165a = c0304c.m1165a();
            int[] iArr = this.f1957D.f1985a;
            int i16 = (iArr == null || m1165a >= iArr.length) ? -1 : iArr[m1165a];
            if ((i16 == -1 ? 1 : i13) != 0) {
                if (m1216d1(c0324n.f2145e)) {
                    i11 = this.f1967r - 1;
                    i12 = -1;
                } else {
                    i15 = this.f1967r;
                    i11 = i13;
                    i12 = 1;
                }
                C0307f c0307f2 = null;
                if (c0324n.f2145e == 1) {
                    int mo1331k2 = this.f1969t.mo1331k();
                    int i17 = Integer.MAX_VALUE;
                    while (i11 != i15) {
                        C0307f c0307f3 = this.f1968s[i11];
                        int m1243h = c0307f3.m1243h(mo1331k2);
                        if (m1243h < i17) {
                            i17 = m1243h;
                            c0307f2 = c0307f3;
                        }
                        i11 += i12;
                    }
                } else {
                    int mo1327g2 = this.f1969t.mo1327g();
                    int i18 = Integer.MIN_VALUE;
                    while (i11 != i15) {
                        C0307f c0307f4 = this.f1968s[i11];
                        int m1246k2 = c0307f4.m1246k(mo1327g2);
                        if (m1246k2 > i18) {
                            c0307f2 = c0307f4;
                            i18 = m1246k2;
                        }
                        i11 += i12;
                    }
                }
                c0307f = c0307f2;
                C0305d c0305d = this.f1957D;
                c0305d.m1231b(m1165a);
                c0305d.f1985a[m1165a] = c0307f.f2005e;
            } else {
                c0307f = this.f1968s[i16];
            }
            C0307f c0307f5 = c0307f;
            c0304c.f1984e = c0307f5;
            if (c0324n.f2145e == 1) {
                m1142b(view);
                r12 = 0;
            } else {
                r12 = 0;
                m1143c(view, 0, false);
            }
            if (this.f1971v == 1) {
                m1115y = RecyclerView.AbstractC0288m.m1115y(this.f1972w, this.f1895n, r12, ((ViewGroup.MarginLayoutParams) c0304c).width, r12);
                m1115y2 = RecyclerView.AbstractC0288m.m1115y(this.f1898q, this.f1896o, m1131J() + m1134M(), ((ViewGroup.MarginLayoutParams) c0304c).height, true);
                z5 = false;
            } else {
                m1115y = RecyclerView.AbstractC0288m.m1115y(this.f1897p, this.f1895n, m1133L() + m1132K(), ((ViewGroup.MarginLayoutParams) c0304c).width, true);
                z5 = false;
                m1115y2 = RecyclerView.AbstractC0288m.m1115y(this.f1972w, this.f1896o, 0, ((ViewGroup.MarginLayoutParams) c0304c).height, false);
            }
            m1214b1(view, m1115y, m1115y2, z5);
            if (c0324n.f2145e == 1) {
                mo1323c = c0307f5.m1243h(mo1327g);
                m1246k = this.f1969t.mo1323c(view) + mo1323c;
            } else {
                m1246k = c0307f5.m1246k(mo1327g);
                mo1323c = m1246k - this.f1969t.mo1323c(view);
            }
            int i19 = c0324n.f2145e;
            C0307f c0307f6 = c0304c.f1984e;
            if (i19 == 1) {
                c0307f6.m1236a(view);
            } else {
                c0307f6.m1249n(view);
            }
            if (m1213a1() && this.f1971v == 1) {
                mo1323c2 = this.f1970u.mo1327g() - (((this.f1967r - 1) - c0307f5.f2005e) * this.f1972w);
                mo1331k = mo1323c2 - this.f1970u.mo1323c(view);
            } else {
                mo1331k = this.f1970u.mo1331k() + (c0307f5.f2005e * this.f1972w);
                mo1323c2 = this.f1970u.mo1323c(view) + mo1331k;
            }
            if (this.f1971v == 1) {
                i8 = mo1323c2;
                i7 = m1246k;
                i9 = mo1331k;
                mo1331k = mo1323c;
            } else {
                i7 = mo1323c2;
                i8 = m1246k;
                i9 = mo1323c;
            }
            m1137T(view, i9, mo1331k, i8, i7);
            m1226n1(c0307f5, this.f1973x.f2145e, i6);
            m1218f1(c0295t, this.f1973x);
            if (this.f1973x.f2148h && view.hasFocusable()) {
                i10 = 0;
                this.f1954A.set(c0307f5.f2005e, false);
            } else {
                i10 = 0;
            }
            i13 = i10;
            z6 = true;
        }
        int i20 = i13;
        if (!z6) {
            m1218f1(c0295t, this.f1973x);
        }
        int mo1331k3 = this.f1973x.f2145e == -1 ? this.f1969t.mo1331k() - m1210X0(this.f1969t.mo1331k()) : m1209W0(this.f1969t.mo1327g()) - this.f1969t.mo1327g();
        return mo1331k3 > 0 ? Math.min(c0324n.f2142b, mo1331k3) : i20;
    }

    /* renamed from: Q0 */
    public final View m1203Q0(boolean z5) {
        int mo1331k = this.f1969t.mo1331k();
        int mo1327g = this.f1969t.mo1327g();
        View view = null;
        for (int m1157x = m1157x() - 1; m1157x >= 0; m1157x--) {
            View m1156w = m1156w(m1157x);
            int mo1325e = this.f1969t.mo1325e(m1156w);
            int mo1322b = this.f1969t.mo1322b(m1156w);
            if (mo1322b > mo1331k && mo1325e < mo1327g) {
                if (mo1322b <= mo1327g || !z5) {
                    return m1156w;
                }
                if (view == null) {
                    view = m1156w;
                }
            }
        }
        return view;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: R */
    public final boolean mo946R() {
        return this.f1958E != 0;
    }

    /* renamed from: R0 */
    public final View m1204R0(boolean z5) {
        int mo1331k = this.f1969t.mo1331k();
        int mo1327g = this.f1969t.mo1327g();
        int m1157x = m1157x();
        View view = null;
        for (int i6 = 0; i6 < m1157x; i6++) {
            View m1156w = m1156w(i6);
            int mo1325e = this.f1969t.mo1325e(m1156w);
            if (this.f1969t.mo1322b(m1156w) > mo1331k && mo1325e < mo1327g) {
                if (mo1325e >= mo1331k || !z5) {
                    return m1156w;
                }
                if (view == null) {
                    view = m1156w;
                }
            }
        }
        return view;
    }

    /* renamed from: S0 */
    public final void m1205S0(RecyclerView.C0295t c0295t, RecyclerView.C0300y c0300y, boolean z5) {
        int mo1327g;
        int m1209W0 = m1209W0(Integer.MIN_VALUE);
        if (m1209W0 != Integer.MIN_VALUE && (mo1327g = this.f1969t.mo1327g() - m1209W0) > 0) {
            int i6 = mo1327g - (-m1222j1(-mo1327g, c0295t, c0300y));
            if (!z5 || i6 <= 0) {
                return;
            }
            this.f1969t.mo1335p(i6);
        }
    }

    /* renamed from: T0 */
    public final void m1206T0(RecyclerView.C0295t c0295t, RecyclerView.C0300y c0300y, boolean z5) {
        int mo1331k;
        int m1210X0 = m1210X0(Integer.MAX_VALUE);
        if (m1210X0 != Integer.MAX_VALUE && (mo1331k = m1210X0 - this.f1969t.mo1331k()) > 0) {
            int m1222j1 = mo1331k - m1222j1(mo1331k, c0295t, c0300y);
            if (!z5 || m1222j1 <= 0) {
                return;
            }
            this.f1969t.mo1335p(-m1222j1);
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: U */
    public final void mo1138U(int i6) {
        super.mo1138U(i6);
        for (int i7 = 0; i7 < this.f1967r; i7++) {
            C0307f c0307f = this.f1968s[i7];
            int i8 = c0307f.f2002b;
            if (i8 != Integer.MIN_VALUE) {
                c0307f.f2002b = i8 + i6;
            }
            int i9 = c0307f.f2003c;
            if (i9 != Integer.MIN_VALUE) {
                c0307f.f2003c = i9 + i6;
            }
        }
    }

    /* renamed from: U0 */
    public final int m1207U0() {
        if (m1157x() == 0) {
            return 0;
        }
        return m1135N(m1156w(0));
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: V */
    public final void mo1139V(int i6) {
        super.mo1139V(i6);
        for (int i7 = 0; i7 < this.f1967r; i7++) {
            C0307f c0307f = this.f1968s[i7];
            int i8 = c0307f.f2002b;
            if (i8 != Integer.MIN_VALUE) {
                c0307f.f2002b = i8 + i6;
            }
            int i9 = c0307f.f2003c;
            if (i9 != Integer.MIN_VALUE) {
                c0307f.f2003c = i9 + i6;
            }
        }
    }

    /* renamed from: V0 */
    public final int m1208V0() {
        int m1157x = m1157x();
        if (m1157x == 0) {
            return 0;
        }
        return m1135N(m1156w(m1157x - 1));
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: W */
    public final void mo952W(RecyclerView recyclerView) {
        RunnableC0302a runnableC0302a = this.f1966M;
        RecyclerView recyclerView2 = this.f1883b;
        if (recyclerView2 != null) {
            recyclerView2.removeCallbacks(runnableC0302a);
        }
        for (int i6 = 0; i6 < this.f1967r; i6++) {
            this.f1968s[i6].m1239d();
        }
        recyclerView.requestLayout();
    }

    /* renamed from: W0 */
    public final int m1209W0(int i6) {
        int m1243h = this.f1968s[0].m1243h(i6);
        for (int i7 = 1; i7 < this.f1967r; i7++) {
            int m1243h2 = this.f1968s[i7].m1243h(i6);
            if (m1243h2 > m1243h) {
                m1243h = m1243h2;
            }
        }
        return m1243h;
    }

    /* JADX WARN: Code restructure failed: missing block: B:112:0x0038, code lost:
    
        if (r8.f1971v == 1) goto L46;
     */
    /* JADX WARN: Code restructure failed: missing block: B:115:0x003d, code lost:
    
        if (r8.f1971v == 0) goto L46;
     */
    /* JADX WARN: Code restructure failed: missing block: B:119:0x004b, code lost:
    
        if (m1213a1() == false) goto L45;
     */
    /* JADX WARN: Code restructure failed: missing block: B:123:0x0057, code lost:
    
        if (m1213a1() == false) goto L46;
     */
    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: X */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final android.view.View mo901X(android.view.View r9, int r10, androidx.recyclerview.widget.RecyclerView.C0295t r11, androidx.recyclerview.widget.RecyclerView.C0300y r12) {
        /*
            Method dump skipped, instructions count: 327
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.StaggeredGridLayoutManager.mo901X(android.view.View, int, androidx.recyclerview.widget.RecyclerView$t, androidx.recyclerview.widget.RecyclerView$y):android.view.View");
    }

    /* renamed from: X0 */
    public final int m1210X0(int i6) {
        int m1246k = this.f1968s[0].m1246k(i6);
        for (int i7 = 1; i7 < this.f1967r; i7++) {
            int m1246k2 = this.f1968s[i7].m1246k(i6);
            if (m1246k2 < m1246k) {
                m1246k = m1246k2;
            }
        }
        return m1246k;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: Y */
    public final void mo955Y(AccessibilityEvent accessibilityEvent) {
        super.mo955Y(accessibilityEvent);
        if (m1157x() > 0) {
            View m1204R0 = m1204R0(false);
            View m1203Q0 = m1203Q0(false);
            if (m1204R0 == null || m1203Q0 == null) {
                return;
            }
            int m1135N = m1135N(m1204R0);
            int m1135N2 = m1135N(m1203Q0);
            if (m1135N < m1135N2) {
                accessibilityEvent.setFromIndex(m1135N);
                accessibilityEvent.setToIndex(m1135N2);
            } else {
                accessibilityEvent.setFromIndex(m1135N2);
                accessibilityEvent.setToIndex(m1135N);
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:11:0x0025  */
    /* JADX WARN: Removed duplicated region for block: B:16:0x0043 A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:18:0x0044  */
    /* JADX WARN: Removed duplicated region for block: B:27:0x003c  */
    /* renamed from: Y0 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m1211Y0(int r7, int r8, int r9) {
        /*
            r6 = this;
            boolean r0 = r6.f1975z
            if (r0 == 0) goto L9
            int r0 = r6.m1208V0()
            goto Ld
        L9:
            int r0 = r6.m1207U0()
        Ld:
            r1 = 8
            if (r9 != r1) goto L1a
            if (r7 >= r8) goto L16
            int r2 = r8 + 1
            goto L1c
        L16:
            int r2 = r7 + 1
            r3 = r8
            goto L1d
        L1a:
            int r2 = r7 + r8
        L1c:
            r3 = r7
        L1d:
            androidx.recyclerview.widget.StaggeredGridLayoutManager$d r4 = r6.f1957D
            r4.m1233d(r3)
            r4 = 1
            if (r9 == r4) goto L3c
            r5 = 2
            if (r9 == r5) goto L36
            if (r9 == r1) goto L2b
            goto L41
        L2b:
            androidx.recyclerview.widget.StaggeredGridLayoutManager$d r9 = r6.f1957D
            r9.m1235f(r7, r4)
            androidx.recyclerview.widget.StaggeredGridLayoutManager$d r7 = r6.f1957D
            r7.m1234e(r8, r4)
            goto L41
        L36:
            androidx.recyclerview.widget.StaggeredGridLayoutManager$d r9 = r6.f1957D
            r9.m1235f(r7, r8)
            goto L41
        L3c:
            androidx.recyclerview.widget.StaggeredGridLayoutManager$d r9 = r6.f1957D
            r9.m1234e(r7, r8)
        L41:
            if (r2 > r0) goto L44
            return
        L44:
            boolean r7 = r6.f1975z
            if (r7 == 0) goto L4d
            int r7 = r6.m1207U0()
            goto L51
        L4d:
            int r7 = r6.m1208V0()
        L51:
            if (r3 > r7) goto L56
            r6.m1155u0()
        L56:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.StaggeredGridLayoutManager.m1211Y0(int, int, int):void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:42:0x00b3, code lost:
    
        if (r10 == r11) goto L51;
     */
    /* JADX WARN: Code restructure failed: missing block: B:43:0x00c9, code lost:
    
        r10 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:58:0x00c7, code lost:
    
        r10 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:63:0x00c5, code lost:
    
        if (r10 == r11) goto L51;
     */
    /* renamed from: Z0 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final android.view.View m1212Z0() {
        /*
            Method dump skipped, instructions count: 237
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.StaggeredGridLayoutManager.m1212Z0():android.view.View");
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0299x.b
    /* renamed from: a */
    public final PointF mo957a(int i6) {
        int m1197K0 = m1197K0(i6);
        PointF pointF = new PointF();
        if (m1197K0 == 0) {
            return null;
        }
        if (this.f1971v == 0) {
            pointF.x = m1197K0;
            pointF.y = 0.0f;
        } else {
            pointF.x = 0.0f;
            pointF.y = m1197K0;
        }
        return pointF;
    }

    /* renamed from: a1 */
    public final boolean m1213a1() {
        return m1126G() == 1;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: b0 */
    public final void mo903b0(RecyclerView.C0295t c0295t, RecyclerView.C0300y c0300y, View view, C0831b c0831b) {
        int i6;
        int i7;
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        if (!(layoutParams instanceof C0304c)) {
            m1141a0(view, c0831b);
            return;
        }
        C0304c c0304c = (C0304c) layoutParams;
        int i8 = 1;
        int i9 = -1;
        if (this.f1971v == 0) {
            C0307f c0307f = c0304c.f1984e;
            i7 = c0307f == null ? -1 : c0307f.f2005e;
            i6 = -1;
        } else {
            C0307f c0307f2 = c0304c.f1984e;
            i6 = c0307f2 == null ? -1 : c0307f2.f2005e;
            i7 = -1;
            i9 = 1;
            i8 = -1;
        }
        c0831b.m2327p(C0831b.c.m2340a(i7, i8, i6, i9, false));
    }

    /* renamed from: b1 */
    public final void m1214b1(View view, int i6, int i7, boolean z5) {
        Rect rect = this.f1962I;
        RecyclerView recyclerView = this.f1883b;
        if (recyclerView == null) {
            rect.set(0, 0, 0, 0);
        } else {
            rect.set(recyclerView.m1005L(view));
        }
        C0304c c0304c = (C0304c) view.getLayoutParams();
        int i8 = ((ViewGroup.MarginLayoutParams) c0304c).leftMargin;
        Rect rect2 = this.f1962I;
        int m1227o1 = m1227o1(i6, i8 + rect2.left, ((ViewGroup.MarginLayoutParams) c0304c).rightMargin + rect2.right);
        int i9 = ((ViewGroup.MarginLayoutParams) c0304c).topMargin;
        Rect rect3 = this.f1962I;
        int m1227o12 = m1227o1(i7, i9 + rect3.top, ((ViewGroup.MarginLayoutParams) c0304c).bottomMargin + rect3.bottom);
        if (m1124E0(view, m1227o1, m1227o12, c0304c)) {
            view.measure(m1227o1, m1227o12);
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: c0 */
    public final void mo904c0(int i6, int i7) {
        m1211Y0(i6, i7, 1);
    }

    /* JADX WARN: Code restructure failed: missing block: B:258:0x03ee, code lost:
    
        if (m1198L0() != false) goto L251;
     */
    /* JADX WARN: Removed duplicated region for block: B:62:0x01ac  */
    /* renamed from: c1 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m1215c1(androidx.recyclerview.widget.RecyclerView.C0295t r12, androidx.recyclerview.widget.RecyclerView.C0300y r13, boolean r14) {
        /*
            Method dump skipped, instructions count: 1040
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.StaggeredGridLayoutManager.m1215c1(androidx.recyclerview.widget.RecyclerView$t, androidx.recyclerview.widget.RecyclerView$y, boolean):void");
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: d */
    public final void mo961d(String str) {
        if (this.f1961H == null) {
            super.mo961d(str);
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: d0 */
    public final void mo905d0() {
        this.f1957D.m1230a();
        m1155u0();
    }

    /* renamed from: d1 */
    public final boolean m1216d1(int i6) {
        if (this.f1971v == 0) {
            return (i6 == -1) != this.f1975z;
        }
        return ((i6 == -1) == this.f1975z) == m1213a1();
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: e */
    public final boolean mo963e() {
        return this.f1971v == 0;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: e0 */
    public final void mo906e0(int i6, int i7) {
        m1211Y0(i6, i7, 8);
    }

    /* renamed from: e1 */
    public final void m1217e1(int i6, RecyclerView.C0300y c0300y) {
        int i7;
        int m1207U0;
        if (i6 > 0) {
            m1207U0 = m1208V0();
            i7 = 1;
        } else {
            i7 = -1;
            m1207U0 = m1207U0();
        }
        this.f1973x.f2141a = true;
        m1225m1(m1207U0, c0300y);
        m1223k1(i7);
        C0324n c0324n = this.f1973x;
        c0324n.f2143c = m1207U0 + c0324n.f2144d;
        c0324n.f2142b = Math.abs(i6);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: f */
    public final boolean mo964f() {
        return this.f1971v == 1;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: f0 */
    public final void mo908f0(int i6, int i7) {
        m1211Y0(i6, i7, 2);
    }

    /* JADX WARN: Code restructure failed: missing block: B:8:0x0011, code lost:
    
        if (r6.f2145e == (-1)) goto L11;
     */
    /* renamed from: f1 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m1218f1(androidx.recyclerview.widget.RecyclerView.C0295t r5, androidx.recyclerview.widget.C0324n r6) {
        /*
            r4 = this;
            boolean r0 = r6.f2141a
            if (r0 == 0) goto L7c
            boolean r0 = r6.f2149i
            if (r0 == 0) goto La
            goto L7c
        La:
            int r0 = r6.f2142b
            r1 = -1
            if (r0 != 0) goto L1f
            int r0 = r6.f2145e
            if (r0 != r1) goto L19
        L13:
            int r6 = r6.f2147g
        L15:
            r4.m1219g1(r5, r6)
            goto L7c
        L19:
            int r6 = r6.f2146f
        L1b:
            r4.m1220h1(r5, r6)
            goto L7c
        L1f:
            int r0 = r6.f2145e
            r2 = 0
            r3 = 1
            if (r0 != r1) goto L50
            int r0 = r6.f2146f
            androidx.recyclerview.widget.StaggeredGridLayoutManager$f[] r1 = r4.f1968s
            r1 = r1[r2]
            int r1 = r1.m1246k(r0)
        L2f:
            int r2 = r4.f1967r
            if (r3 >= r2) goto L41
            androidx.recyclerview.widget.StaggeredGridLayoutManager$f[] r2 = r4.f1968s
            r2 = r2[r3]
            int r2 = r2.m1246k(r0)
            if (r2 <= r1) goto L3e
            r1 = r2
        L3e:
            int r3 = r3 + 1
            goto L2f
        L41:
            int r0 = r0 - r1
            if (r0 >= 0) goto L45
            goto L13
        L45:
            int r1 = r6.f2147g
            int r6 = r6.f2142b
            int r6 = java.lang.Math.min(r0, r6)
            int r6 = r1 - r6
            goto L15
        L50:
            int r0 = r6.f2147g
            androidx.recyclerview.widget.StaggeredGridLayoutManager$f[] r1 = r4.f1968s
            r1 = r1[r2]
            int r1 = r1.m1243h(r0)
        L5a:
            int r2 = r4.f1967r
            if (r3 >= r2) goto L6c
            androidx.recyclerview.widget.StaggeredGridLayoutManager$f[] r2 = r4.f1968s
            r2 = r2[r3]
            int r2 = r2.m1243h(r0)
            if (r2 >= r1) goto L69
            r1 = r2
        L69:
            int r3 = r3 + 1
            goto L5a
        L6c:
            int r0 = r6.f2147g
            int r1 = r1 - r0
            if (r1 >= 0) goto L72
            goto L19
        L72:
            int r0 = r6.f2146f
            int r6 = r6.f2142b
            int r6 = java.lang.Math.min(r1, r6)
            int r6 = r6 + r0
            goto L1b
        L7c:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.StaggeredGridLayoutManager.m1218f1(androidx.recyclerview.widget.RecyclerView$t, androidx.recyclerview.widget.n):void");
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: g */
    public final boolean mo910g(RecyclerView.C0289n c0289n) {
        return c0289n instanceof C0304c;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: g0 */
    public final void mo911g0(int i6, int i7) {
        m1211Y0(i6, i7, 4);
    }

    /* renamed from: g1 */
    public final void m1219g1(RecyclerView.C0295t c0295t, int i6) {
        for (int m1157x = m1157x() - 1; m1157x >= 0; m1157x--) {
            View m1156w = m1156w(m1157x);
            if (this.f1969t.mo1325e(m1156w) < i6 || this.f1969t.mo1334o(m1156w) < i6) {
                return;
            }
            C0304c c0304c = (C0304c) m1156w.getLayoutParams();
            Objects.requireNonNull(c0304c);
            if (c0304c.f1984e.f2001a.size() == 1) {
                return;
            }
            c0304c.f1984e.m1247l();
            m1150q0(m1156w, c0295t);
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: h0 */
    public final void mo912h0(RecyclerView.C0295t c0295t, RecyclerView.C0300y c0300y) {
        m1215c1(c0295t, c0300y, true);
    }

    /* renamed from: h1 */
    public final void m1220h1(RecyclerView.C0295t c0295t, int i6) {
        while (m1157x() > 0) {
            View m1156w = m1156w(0);
            if (this.f1969t.mo1322b(m1156w) > i6 || this.f1969t.mo1333n(m1156w) > i6) {
                return;
            }
            C0304c c0304c = (C0304c) m1156w.getLayoutParams();
            Objects.requireNonNull(c0304c);
            if (c0304c.f1984e.f2001a.size() == 1) {
                return;
            }
            c0304c.f1984e.m1248m();
            m1150q0(m1156w, c0295t);
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: i */
    public final void mo967i(int i6, int i7, RecyclerView.C0300y c0300y, RecyclerView.AbstractC0288m.c cVar) {
        int m1243h;
        int i8;
        if (this.f1971v != 0) {
            i6 = i7;
        }
        if (m1157x() == 0 || i6 == 0) {
            return;
        }
        m1217e1(i6, c0300y);
        int[] iArr = this.f1965L;
        if (iArr == null || iArr.length < this.f1967r) {
            this.f1965L = new int[this.f1967r];
        }
        int i9 = 0;
        for (int i10 = 0; i10 < this.f1967r; i10++) {
            C0324n c0324n = this.f1973x;
            if (c0324n.f2144d == -1) {
                m1243h = c0324n.f2146f;
                i8 = this.f1968s[i10].m1246k(m1243h);
            } else {
                m1243h = this.f1968s[i10].m1243h(c0324n.f2147g);
                i8 = this.f1973x.f2147g;
            }
            int i11 = m1243h - i8;
            if (i11 >= 0) {
                this.f1965L[i9] = i11;
                i9++;
            }
        }
        Arrays.sort(this.f1965L, 0, i9);
        for (int i12 = 0; i12 < i9; i12++) {
            int i13 = this.f1973x.f2143c;
            if (!(i13 >= 0 && i13 < c0300y.m1196b())) {
                return;
            }
            ((RunnableC0323m.b) cVar).m1316a(this.f1973x.f2143c, this.f1965L[i12]);
            C0324n c0324n2 = this.f1973x;
            c0324n2.f2143c += c0324n2.f2144d;
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: i0 */
    public final void mo913i0() {
        this.f1955B = -1;
        this.f1956C = Integer.MIN_VALUE;
        this.f1961H = null;
        this.f1963J.m1229b();
    }

    /* renamed from: i1 */
    public final void m1221i1() {
        this.f1975z = (this.f1971v == 1 || !m1213a1()) ? this.f1974y : !this.f1974y;
    }

    /* renamed from: j1 */
    public final int m1222j1(int i6, RecyclerView.C0295t c0295t, RecyclerView.C0300y c0300y) {
        if (m1157x() == 0 || i6 == 0) {
            return 0;
        }
        m1217e1(i6, c0300y);
        int m1202P0 = m1202P0(c0295t, this.f1973x, c0300y);
        if (this.f1973x.f2142b >= m1202P0) {
            i6 = i6 < 0 ? -m1202P0 : m1202P0;
        }
        this.f1969t.mo1335p(-i6);
        this.f1959F = this.f1975z;
        C0324n c0324n = this.f1973x;
        c0324n.f2142b = 0;
        m1218f1(c0295t, c0324n);
        return i6;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: k */
    public final int mo971k(RecyclerView.C0300y c0300y) {
        return m1199M0(c0300y);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: k0 */
    public final void mo972k0(Parcelable parcelable) {
        if (parcelable instanceof C0306e) {
            this.f1961H = (C0306e) parcelable;
            m1155u0();
        }
    }

    /* renamed from: k1 */
    public final void m1223k1(int i6) {
        C0324n c0324n = this.f1973x;
        c0324n.f2145e = i6;
        c0324n.f2144d = this.f1975z != (i6 == -1) ? -1 : 1;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: l */
    public final int mo914l(RecyclerView.C0300y c0300y) {
        return m1200N0(c0300y);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: l0 */
    public final Parcelable mo974l0() {
        int m1246k;
        int mo1331k;
        int[] iArr;
        C0306e c0306e = this.f1961H;
        if (c0306e != null) {
            return new C0306e(c0306e);
        }
        C0306e c0306e2 = new C0306e();
        c0306e2.f1998q = this.f1974y;
        c0306e2.f1999r = this.f1959F;
        c0306e2.f2000s = this.f1960G;
        C0305d c0305d = this.f1957D;
        if (c0305d == null || (iArr = c0305d.f1985a) == null) {
            c0306e2.f1995n = 0;
        } else {
            c0306e2.f1996o = iArr;
            c0306e2.f1995n = iArr.length;
            c0306e2.f1997p = c0305d.f1986b;
        }
        if (m1157x() > 0) {
            c0306e2.f1991j = this.f1959F ? m1208V0() : m1207U0();
            View m1203Q0 = this.f1975z ? m1203Q0(true) : m1204R0(true);
            c0306e2.f1992k = m1203Q0 != null ? m1135N(m1203Q0) : -1;
            int i6 = this.f1967r;
            c0306e2.f1993l = i6;
            c0306e2.f1994m = new int[i6];
            for (int i7 = 0; i7 < this.f1967r; i7++) {
                if (this.f1959F) {
                    m1246k = this.f1968s[i7].m1243h(Integer.MIN_VALUE);
                    if (m1246k != Integer.MIN_VALUE) {
                        mo1331k = this.f1969t.mo1327g();
                        m1246k -= mo1331k;
                        c0306e2.f1994m[i7] = m1246k;
                    } else {
                        c0306e2.f1994m[i7] = m1246k;
                    }
                } else {
                    m1246k = this.f1968s[i7].m1246k(Integer.MIN_VALUE);
                    if (m1246k != Integer.MIN_VALUE) {
                        mo1331k = this.f1969t.mo1331k();
                        m1246k -= mo1331k;
                        c0306e2.f1994m[i7] = m1246k;
                    } else {
                        c0306e2.f1994m[i7] = m1246k;
                    }
                }
            }
        } else {
            c0306e2.f1991j = -1;
            c0306e2.f1992k = -1;
            c0306e2.f1993l = 0;
        }
        return c0306e2;
    }

    /* renamed from: l1 */
    public final void m1224l1(int i6, int i7) {
        for (int i8 = 0; i8 < this.f1967r; i8++) {
            if (!this.f1968s[i8].f2001a.isEmpty()) {
                m1226n1(this.f1968s[i8], i6, i7);
            }
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: m */
    public final int mo916m(RecyclerView.C0300y c0300y) {
        return m1201O0(c0300y);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: m0 */
    public final void mo1145m0(int i6) {
        if (i6 == 0) {
            m1198L0();
        }
    }

    /* renamed from: m1 */
    public final void m1225m1(int i6, RecyclerView.C0300y c0300y) {
        int i7;
        int i8;
        int i9;
        C0324n c0324n = this.f1973x;
        boolean z5 = false;
        c0324n.f2142b = 0;
        c0324n.f2143c = i6;
        RecyclerView.AbstractC0299x abstractC0299x = this.f1888g;
        if (!(abstractC0299x != null && abstractC0299x.f1929e) || (i9 = c0300y.f1940a) == -1) {
            i7 = 0;
            i8 = 0;
        } else {
            if (this.f1975z == (i9 < i6)) {
                i7 = this.f1969t.mo1332l();
                i8 = 0;
            } else {
                i8 = this.f1969t.mo1332l();
                i7 = 0;
            }
        }
        RecyclerView recyclerView = this.f1883b;
        if (recyclerView != null && recyclerView.f1820p) {
            this.f1973x.f2146f = this.f1969t.mo1331k() - i8;
            this.f1973x.f2147g = this.f1969t.mo1327g() + i7;
        } else {
            this.f1973x.f2147g = this.f1969t.mo1326f() + i7;
            this.f1973x.f2146f = -i8;
        }
        C0324n c0324n2 = this.f1973x;
        c0324n2.f2148h = false;
        c0324n2.f2141a = true;
        if (this.f1969t.mo1329i() == 0 && this.f1969t.mo1326f() == 0) {
            z5 = true;
        }
        c0324n2.f2149i = z5;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: n */
    public final int mo976n(RecyclerView.C0300y c0300y) {
        return m1199M0(c0300y);
    }

    /* renamed from: n1 */
    public final void m1226n1(C0307f c0307f, int i6, int i7) {
        int i8 = c0307f.f2004d;
        if (i6 == -1) {
            int i9 = c0307f.f2002b;
            if (i9 == Integer.MIN_VALUE) {
                c0307f.m1238c();
                i9 = c0307f.f2002b;
            }
            if (i9 + i8 > i7) {
                return;
            }
        } else {
            int i10 = c0307f.f2003c;
            if (i10 == Integer.MIN_VALUE) {
                c0307f.m1237b();
                i10 = c0307f.f2003c;
            }
            if (i10 - i8 < i7) {
                return;
            }
        }
        this.f1954A.set(c0307f.f2005e, false);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: o */
    public final int mo917o(RecyclerView.C0300y c0300y) {
        return m1200N0(c0300y);
    }

    /* renamed from: o1 */
    public final int m1227o1(int i6, int i7, int i8) {
        if (i7 == 0 && i8 == 0) {
            return i6;
        }
        int mode = View.MeasureSpec.getMode(i6);
        return (mode == Integer.MIN_VALUE || mode == 1073741824) ? View.MeasureSpec.makeMeasureSpec(Math.max(0, (View.MeasureSpec.getSize(i6) - i7) - i8), mode) : i6;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: p */
    public final int mo918p(RecyclerView.C0300y c0300y) {
        return m1201O0(c0300y);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: t */
    public final RecyclerView.C0289n mo923t() {
        return this.f1971v == 0 ? new C0304c(-2, -1) : new C0304c(-1, -2);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: u */
    public final RecyclerView.C0289n mo925u(Context context, AttributeSet attributeSet) {
        return new C0304c(context, attributeSet);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: v */
    public final RecyclerView.C0289n mo927v(ViewGroup.LayoutParams layoutParams) {
        return layoutParams instanceof ViewGroup.MarginLayoutParams ? new C0304c((ViewGroup.MarginLayoutParams) layoutParams) : new C0304c(layoutParams);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: v0 */
    public final int mo928v0(int i6, RecyclerView.C0295t c0295t, RecyclerView.C0300y c0300y) {
        return m1222j1(i6, c0295t, c0300y);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: w0 */
    public final void mo980w0(int i6) {
        C0306e c0306e = this.f1961H;
        if (c0306e != null && c0306e.f1991j != i6) {
            c0306e.f1994m = null;
            c0306e.f1993l = 0;
            c0306e.f1991j = -1;
            c0306e.f1992k = -1;
        }
        this.f1955B = i6;
        this.f1956C = Integer.MIN_VALUE;
        m1155u0();
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: x0 */
    public final int mo931x0(int i6, RecyclerView.C0295t c0295t, RecyclerView.C0300y c0300y) {
        return m1222j1(i6, c0295t, c0300y);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: z */
    public final int mo934z(RecyclerView.C0295t c0295t, RecyclerView.C0300y c0300y) {
        return this.f1971v == 1 ? this.f1967r : super.mo934z(c0295t, c0300y);
    }
}
