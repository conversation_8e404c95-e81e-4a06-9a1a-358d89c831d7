package androidx.recyclerview.widget;

import android.graphics.Rect;
import android.view.View;
import androidx.recyclerview.widget.RecyclerView;

/* renamed from: androidx.recyclerview.widget.s */
/* loaded from: classes.dex */
public abstract class AbstractC0329s {

    /* renamed from: a */
    public final RecyclerView.AbstractC0288m f2159a;

    /* renamed from: b */
    public int f2160b = Integer.MIN_VALUE;

    /* renamed from: c */
    public final Rect f2161c = new Rect();

    public AbstractC0329s(RecyclerView.AbstractC0288m abstractC0288m) {
        this.f2159a = abstractC0288m;
    }

    /* renamed from: a */
    public static AbstractC0329s m1336a(RecyclerView.AbstractC0288m abstractC0288m, int i6) {
        if (i6 == 0) {
            return new C0327q(abstractC0288m);
        }
        if (i6 == 1) {
            return new C0328r(abstractC0288m);
        }
        throw new IllegalArgumentException("invalid orientation");
    }

    /* renamed from: b */
    public abstract int mo1322b(View view);

    /* renamed from: c */
    public abstract int mo1323c(View view);

    /* renamed from: d */
    public abstract int mo1324d(View view);

    /* renamed from: e */
    public abstract int mo1325e(View view);

    /* renamed from: f */
    public abstract int mo1326f();

    /* renamed from: g */
    public abstract int mo1327g();

    /* renamed from: h */
    public abstract int mo1328h();

    /* renamed from: i */
    public abstract int mo1329i();

    /* renamed from: j */
    public abstract int mo1330j();

    /* renamed from: k */
    public abstract int mo1331k();

    /* renamed from: l */
    public abstract int mo1332l();

    /* renamed from: m */
    public final int m1337m() {
        if (Integer.MIN_VALUE == this.f2160b) {
            return 0;
        }
        return mo1332l() - this.f2160b;
    }

    /* renamed from: n */
    public abstract int mo1333n(View view);

    /* renamed from: o */
    public abstract int mo1334o(View view);

    /* renamed from: p */
    public abstract void mo1335p(int i6);
}
