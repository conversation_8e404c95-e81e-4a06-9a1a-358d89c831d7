package androidx.recyclerview.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.PointF;
import android.graphics.Rect;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import androidx.activity.result.C0052a;
import androidx.appcompat.widget.C0174y;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RunnableC0323m;
import java.util.List;

/* loaded from: classes.dex */
public class LinearLayoutManager extends RecyclerView.AbstractC0288m implements RecyclerView.AbstractC0299x.b {

    /* renamed from: A */
    public int f1728A;

    /* renamed from: B */
    public C0273d f1729B;

    /* renamed from: C */
    public final C0270a f1730C;

    /* renamed from: D */
    public final C0271b f1731D;

    /* renamed from: E */
    public int f1732E;

    /* renamed from: F */
    public int[] f1733F;

    /* renamed from: r */
    public int f1734r;

    /* renamed from: s */
    public C0272c f1735s;

    /* renamed from: t */
    public AbstractC0329s f1736t;

    /* renamed from: u */
    public boolean f1737u;

    /* renamed from: v */
    public boolean f1738v;

    /* renamed from: w */
    public boolean f1739w;

    /* renamed from: x */
    public boolean f1740x;

    /* renamed from: y */
    public boolean f1741y;

    /* renamed from: z */
    public int f1742z;

    /* renamed from: androidx.recyclerview.widget.LinearLayoutManager$a */
    public static class C0270a {

        /* renamed from: a */
        public AbstractC0329s f1743a;

        /* renamed from: b */
        public int f1744b;

        /* renamed from: c */
        public int f1745c;

        /* renamed from: d */
        public boolean f1746d;

        /* renamed from: e */
        public boolean f1747e;

        public C0270a() {
            m984d();
        }

        /* renamed from: a */
        public final void m981a() {
            this.f1745c = this.f1746d ? this.f1743a.mo1327g() : this.f1743a.mo1331k();
        }

        /* renamed from: b */
        public final void m982b(View view, int i6) {
            if (this.f1746d) {
                this.f1745c = this.f1743a.m1337m() + this.f1743a.mo1322b(view);
            } else {
                this.f1745c = this.f1743a.mo1325e(view);
            }
            this.f1744b = i6;
        }

        /* renamed from: c */
        public final void m983c(View view, int i6) {
            int min;
            int m1337m = this.f1743a.m1337m();
            if (m1337m >= 0) {
                m982b(view, i6);
                return;
            }
            this.f1744b = i6;
            if (this.f1746d) {
                int mo1327g = (this.f1743a.mo1327g() - m1337m) - this.f1743a.mo1322b(view);
                this.f1745c = this.f1743a.mo1327g() - mo1327g;
                if (mo1327g <= 0) {
                    return;
                }
                int mo1323c = this.f1745c - this.f1743a.mo1323c(view);
                int mo1331k = this.f1743a.mo1331k();
                int min2 = mo1323c - (Math.min(this.f1743a.mo1325e(view) - mo1331k, 0) + mo1331k);
                if (min2 >= 0) {
                    return;
                }
                min = Math.min(mo1327g, -min2) + this.f1745c;
            } else {
                int mo1325e = this.f1743a.mo1325e(view);
                int mo1331k2 = mo1325e - this.f1743a.mo1331k();
                this.f1745c = mo1325e;
                if (mo1331k2 <= 0) {
                    return;
                }
                int mo1327g2 = (this.f1743a.mo1327g() - Math.min(0, (this.f1743a.mo1327g() - m1337m) - this.f1743a.mo1322b(view))) - (this.f1743a.mo1323c(view) + mo1325e);
                if (mo1327g2 >= 0) {
                    return;
                } else {
                    min = this.f1745c - Math.min(mo1331k2, -mo1327g2);
                }
            }
            this.f1745c = min;
        }

        /* renamed from: d */
        public final void m984d() {
            this.f1744b = -1;
            this.f1745c = Integer.MIN_VALUE;
            this.f1746d = false;
            this.f1747e = false;
        }

        public final String toString() {
            StringBuilder m104h = C0052a.m104h("AnchorInfo{mPosition=");
            m104h.append(this.f1744b);
            m104h.append(", mCoordinate=");
            m104h.append(this.f1745c);
            m104h.append(", mLayoutFromEnd=");
            m104h.append(this.f1746d);
            m104h.append(", mValid=");
            m104h.append(this.f1747e);
            m104h.append('}');
            return m104h.toString();
        }
    }

    /* renamed from: androidx.recyclerview.widget.LinearLayoutManager$b */
    public static class C0271b {

        /* renamed from: a */
        public int f1748a;

        /* renamed from: b */
        public boolean f1749b;

        /* renamed from: c */
        public boolean f1750c;

        /* renamed from: d */
        public boolean f1751d;
    }

    /* renamed from: androidx.recyclerview.widget.LinearLayoutManager$c */
    public static class C0272c {

        /* renamed from: b */
        public int f1753b;

        /* renamed from: c */
        public int f1754c;

        /* renamed from: d */
        public int f1755d;

        /* renamed from: e */
        public int f1756e;

        /* renamed from: f */
        public int f1757f;

        /* renamed from: g */
        public int f1758g;

        /* renamed from: j */
        public int f1761j;

        /* renamed from: l */
        public boolean f1763l;

        /* renamed from: a */
        public boolean f1752a = true;

        /* renamed from: h */
        public int f1759h = 0;

        /* renamed from: i */
        public int f1760i = 0;

        /* renamed from: k */
        public List<RecyclerView.AbstractC0277b0> f1762k = null;

        /* renamed from: a */
        public final void m985a(View view) {
            int m1165a;
            int size = this.f1762k.size();
            View view2 = null;
            int i6 = Integer.MAX_VALUE;
            for (int i7 = 0; i7 < size; i7++) {
                View view3 = this.f1762k.get(i7).f1852a;
                RecyclerView.C0289n c0289n = (RecyclerView.C0289n) view3.getLayoutParams();
                if (view3 != view && !c0289n.m1167c() && (m1165a = (c0289n.m1165a() - this.f1755d) * this.f1756e) >= 0 && m1165a < i6) {
                    view2 = view3;
                    if (m1165a == 0) {
                        break;
                    } else {
                        i6 = m1165a;
                    }
                }
            }
            this.f1755d = view2 == null ? -1 : ((RecyclerView.C0289n) view2.getLayoutParams()).m1165a();
        }

        /* renamed from: b */
        public final boolean m986b(RecyclerView.C0300y c0300y) {
            int i6 = this.f1755d;
            return i6 >= 0 && i6 < c0300y.m1196b();
        }

        /* renamed from: c */
        public final View m987c(RecyclerView.C0295t c0295t) {
            List<RecyclerView.AbstractC0277b0> list = this.f1762k;
            if (list == null) {
                View view = c0295t.m1185j(this.f1755d, Long.MAX_VALUE).f1852a;
                this.f1755d += this.f1756e;
                return view;
            }
            int size = list.size();
            for (int i6 = 0; i6 < size; i6++) {
                View view2 = this.f1762k.get(i6).f1852a;
                RecyclerView.C0289n c0289n = (RecyclerView.C0289n) view2.getLayoutParams();
                if (!c0289n.m1167c() && this.f1755d == c0289n.m1165a()) {
                    m985a(view2);
                    return view2;
                }
            }
            return null;
        }
    }

    @SuppressLint({"BanParcelableUsage"})
    /* renamed from: androidx.recyclerview.widget.LinearLayoutManager$d */
    public static class C0273d implements Parcelable {
        public static final Parcelable.Creator<C0273d> CREATOR = new a();

        /* renamed from: j */
        public int f1764j;

        /* renamed from: k */
        public int f1765k;

        /* renamed from: l */
        public boolean f1766l;

        /* renamed from: androidx.recyclerview.widget.LinearLayoutManager$d$a */
        public static class a implements Parcelable.Creator<C0273d> {
            @Override // android.os.Parcelable.Creator
            public final C0273d createFromParcel(Parcel parcel) {
                return new C0273d(parcel);
            }

            @Override // android.os.Parcelable.Creator
            public final C0273d[] newArray(int i6) {
                return new C0273d[i6];
            }
        }

        public C0273d() {
        }

        public C0273d(Parcel parcel) {
            this.f1764j = parcel.readInt();
            this.f1765k = parcel.readInt();
            this.f1766l = parcel.readInt() == 1;
        }

        public C0273d(C0273d c0273d) {
            this.f1764j = c0273d.f1764j;
            this.f1765k = c0273d.f1765k;
            this.f1766l = c0273d.f1766l;
        }

        /* renamed from: a */
        public final boolean m988a() {
            return this.f1764j >= 0;
        }

        @Override // android.os.Parcelable
        public final int describeContents() {
            return 0;
        }

        @Override // android.os.Parcelable
        public final void writeToParcel(Parcel parcel, int i6) {
            parcel.writeInt(this.f1764j);
            parcel.writeInt(this.f1765k);
            parcel.writeInt(this.f1766l ? 1 : 0);
        }
    }

    public LinearLayoutManager(int i6) {
        this.f1734r = 1;
        this.f1738v = false;
        this.f1739w = false;
        this.f1740x = false;
        this.f1741y = true;
        this.f1742z = -1;
        this.f1728A = Integer.MIN_VALUE;
        this.f1729B = null;
        this.f1730C = new C0270a();
        this.f1731D = new C0271b();
        this.f1732E = 2;
        this.f1733F = new int[2];
        m973k1(i6);
        mo961d(null);
        if (this.f1738v) {
            this.f1738v = false;
            m1155u0();
        }
    }

    public LinearLayoutManager(Context context) {
        this(1);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: F0 */
    public final boolean mo938F0() {
        boolean z5;
        if (this.f1896o != 1073741824 && this.f1895n != 1073741824) {
            int m1157x = m1157x();
            int i6 = 0;
            while (true) {
                if (i6 >= m1157x) {
                    z5 = false;
                    break;
                }
                ViewGroup.LayoutParams layoutParams = m1156w(i6).getLayoutParams();
                if (layoutParams.width < 0 && layoutParams.height < 0) {
                    z5 = true;
                    break;
                }
                i6++;
            }
            if (z5) {
                return true;
            }
        }
        return false;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: H0 */
    public void mo939H0(RecyclerView recyclerView, int i6) {
        C0325o c0325o = new C0325o(recyclerView.getContext());
        c0325o.f1925a = i6;
        m1130I0(c0325o);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: J0 */
    public boolean mo898J0() {
        return this.f1729B == null && this.f1737u == this.f1740x;
    }

    /* renamed from: K0 */
    public void mo940K0(RecyclerView.C0300y c0300y, int[] iArr) {
        int i6;
        int mo1332l = c0300y.f1940a != -1 ? this.f1736t.mo1332l() : 0;
        if (this.f1735s.f1757f == -1) {
            i6 = 0;
        } else {
            i6 = mo1332l;
            mo1332l = 0;
        }
        iArr[0] = mo1332l;
        iArr[1] = i6;
    }

    /* renamed from: L0 */
    public void mo899L0(RecyclerView.C0300y c0300y, C0272c c0272c, RecyclerView.AbstractC0288m.c cVar) {
        int i6 = c0272c.f1755d;
        if (i6 < 0 || i6 >= c0300y.m1196b()) {
            return;
        }
        ((RunnableC0323m.b) cVar).m1316a(i6, Math.max(0, c0272c.f1758g));
    }

    /* renamed from: M0 */
    public final int m941M0(RecyclerView.C0300y c0300y) {
        if (m1157x() == 0) {
            return 0;
        }
        m945Q0();
        return C0335y.m1358a(c0300y, this.f1736t, m949T0(!this.f1741y), m948S0(!this.f1741y), this, this.f1741y);
    }

    /* renamed from: N0 */
    public final int m942N0(RecyclerView.C0300y c0300y) {
        if (m1157x() == 0) {
            return 0;
        }
        m945Q0();
        return C0335y.m1359b(c0300y, this.f1736t, m949T0(!this.f1741y), m948S0(!this.f1741y), this, this.f1741y, this.f1739w);
    }

    /* renamed from: O0 */
    public final int m943O0(RecyclerView.C0300y c0300y) {
        if (m1157x() == 0) {
            return 0;
        }
        m945Q0();
        return C0335y.m1360c(c0300y, this.f1736t, m949T0(!this.f1741y), m948S0(!this.f1741y), this, this.f1741y);
    }

    /* renamed from: P0 */
    public final int m944P0(int i6) {
        return i6 != 1 ? i6 != 2 ? i6 != 17 ? i6 != 33 ? i6 != 66 ? (i6 == 130 && this.f1734r == 1) ? 1 : Integer.MIN_VALUE : this.f1734r == 0 ? 1 : Integer.MIN_VALUE : this.f1734r == 1 ? -1 : Integer.MIN_VALUE : this.f1734r == 0 ? -1 : Integer.MIN_VALUE : (this.f1734r != 1 && m962d1()) ? -1 : 1 : (this.f1734r != 1 && m962d1()) ? 1 : -1;
    }

    /* renamed from: Q0 */
    public final void m945Q0() {
        if (this.f1735s == null) {
            this.f1735s = new C0272c();
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: R */
    public final boolean mo946R() {
        return true;
    }

    /* renamed from: R0 */
    public final int m947R0(RecyclerView.C0295t c0295t, C0272c c0272c, RecyclerView.C0300y c0300y, boolean z5) {
        int i6 = c0272c.f1754c;
        int i7 = c0272c.f1758g;
        if (i7 != Integer.MIN_VALUE) {
            if (i6 < 0) {
                c0272c.f1758g = i7 + i6;
            }
            m965g1(c0295t, c0272c);
        }
        int i8 = c0272c.f1754c + c0272c.f1759h;
        C0271b c0271b = this.f1731D;
        while (true) {
            if ((!c0272c.f1763l && i8 <= 0) || !c0272c.m986b(c0300y)) {
                break;
            }
            c0271b.f1748a = 0;
            c0271b.f1749b = false;
            c0271b.f1750c = false;
            c0271b.f1751d = false;
            mo907e1(c0295t, c0300y, c0272c, c0271b);
            if (!c0271b.f1749b) {
                int i9 = c0272c.f1753b;
                int i10 = c0271b.f1748a;
                c0272c.f1753b = (c0272c.f1757f * i10) + i9;
                if (!c0271b.f1750c || c0272c.f1762k != null || !c0300y.f1946g) {
                    c0272c.f1754c -= i10;
                    i8 -= i10;
                }
                int i11 = c0272c.f1758g;
                if (i11 != Integer.MIN_VALUE) {
                    int i12 = i11 + i10;
                    c0272c.f1758g = i12;
                    int i13 = c0272c.f1754c;
                    if (i13 < 0) {
                        c0272c.f1758g = i12 + i13;
                    }
                    m965g1(c0295t, c0272c);
                }
                if (z5 && c0271b.f1751d) {
                    break;
                }
            } else {
                break;
            }
        }
        return i6 - c0272c.f1754c;
    }

    /* renamed from: S0 */
    public final View m948S0(boolean z5) {
        int m1157x;
        int i6 = -1;
        if (this.f1739w) {
            m1157x = 0;
            i6 = m1157x();
        } else {
            m1157x = m1157x() - 1;
        }
        return m954X0(m1157x, i6, z5);
    }

    /* renamed from: T0 */
    public final View m949T0(boolean z5) {
        int i6;
        int i7 = -1;
        if (this.f1739w) {
            i6 = m1157x() - 1;
        } else {
            i6 = 0;
            i7 = m1157x();
        }
        return m954X0(i6, i7, z5);
    }

    /* renamed from: U0 */
    public final int m950U0() {
        View m954X0 = m954X0(0, m1157x(), false);
        if (m954X0 == null) {
            return -1;
        }
        return m1135N(m954X0);
    }

    /* renamed from: V0 */
    public final int m951V0() {
        View m954X0 = m954X0(m1157x() - 1, -1, false);
        if (m954X0 == null) {
            return -1;
        }
        return m1135N(m954X0);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: W */
    public final void mo952W(RecyclerView recyclerView) {
    }

    /* renamed from: W0 */
    public final View m953W0(int i6, int i7) {
        int i8;
        int i9;
        m945Q0();
        if ((i7 > i6 ? (char) 1 : i7 < i6 ? (char) 65535 : (char) 0) == 0) {
            return m1156w(i6);
        }
        if (this.f1736t.mo1325e(m1156w(i6)) < this.f1736t.mo1331k()) {
            i8 = 16644;
            i9 = 16388;
        } else {
            i8 = 4161;
            i9 = 4097;
        }
        return (this.f1734r == 0 ? this.f1886e : this.f1887f).m1287a(i6, i7, i8, i9);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: X */
    public View mo901X(View view, int i6, RecyclerView.C0295t c0295t, RecyclerView.C0300y c0300y) {
        int m944P0;
        m968i1();
        if (m1157x() == 0 || (m944P0 = m944P0(i6)) == Integer.MIN_VALUE) {
            return null;
        }
        m945Q0();
        m975m1(m944P0, (int) (this.f1736t.mo1332l() * 0.33333334f), false, c0300y);
        C0272c c0272c = this.f1735s;
        c0272c.f1758g = Integer.MIN_VALUE;
        c0272c.f1752a = false;
        m947R0(c0295t, c0272c, c0300y, true);
        View m953W0 = m944P0 == -1 ? this.f1739w ? m953W0(m1157x() - 1, -1) : m953W0(0, m1157x()) : this.f1739w ? m953W0(0, m1157x()) : m953W0(m1157x() - 1, -1);
        View m960c1 = m944P0 == -1 ? m960c1() : m959b1();
        if (!m960c1.hasFocusable()) {
            return m953W0;
        }
        if (m953W0 == null) {
            return null;
        }
        return m960c1;
    }

    /* renamed from: X0 */
    public final View m954X0(int i6, int i7, boolean z5) {
        m945Q0();
        return (this.f1734r == 0 ? this.f1886e : this.f1887f).m1287a(i6, i7, z5 ? 24579 : 320, 320);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: Y */
    public final void mo955Y(AccessibilityEvent accessibilityEvent) {
        super.mo955Y(accessibilityEvent);
        if (m1157x() > 0) {
            accessibilityEvent.setFromIndex(m950U0());
            accessibilityEvent.setToIndex(m951V0());
        }
    }

    /* renamed from: Y0 */
    public View mo902Y0(RecyclerView.C0295t c0295t, RecyclerView.C0300y c0300y, int i6, int i7, int i8) {
        m945Q0();
        int mo1331k = this.f1736t.mo1331k();
        int mo1327g = this.f1736t.mo1327g();
        int i9 = i7 > i6 ? 1 : -1;
        View view = null;
        View view2 = null;
        while (i6 != i7) {
            View m1156w = m1156w(i6);
            int m1135N = m1135N(m1156w);
            if (m1135N >= 0 && m1135N < i8) {
                if (((RecyclerView.C0289n) m1156w.getLayoutParams()).m1167c()) {
                    if (view2 == null) {
                        view2 = m1156w;
                    }
                } else {
                    if (this.f1736t.mo1325e(m1156w) < mo1327g && this.f1736t.mo1322b(m1156w) >= mo1331k) {
                        return m1156w;
                    }
                    if (view == null) {
                        view = m1156w;
                    }
                }
            }
            i6 += i9;
        }
        return view != null ? view : view2;
    }

    /* renamed from: Z0 */
    public final int m956Z0(int i6, RecyclerView.C0295t c0295t, RecyclerView.C0300y c0300y, boolean z5) {
        int mo1327g;
        int mo1327g2 = this.f1736t.mo1327g() - i6;
        if (mo1327g2 <= 0) {
            return 0;
        }
        int i7 = -m970j1(-mo1327g2, c0295t, c0300y);
        int i8 = i6 + i7;
        if (!z5 || (mo1327g = this.f1736t.mo1327g() - i8) <= 0) {
            return i7;
        }
        this.f1736t.mo1335p(mo1327g);
        return mo1327g + i7;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0299x.b
    /* renamed from: a */
    public final PointF mo957a(int i6) {
        if (m1157x() == 0) {
            return null;
        }
        int i7 = (i6 < m1135N(m1156w(0))) != this.f1739w ? -1 : 1;
        return this.f1734r == 0 ? new PointF(i7, 0.0f) : new PointF(0.0f, i7);
    }

    /* renamed from: a1 */
    public final int m958a1(int i6, RecyclerView.C0295t c0295t, RecyclerView.C0300y c0300y, boolean z5) {
        int mo1331k;
        int mo1331k2 = i6 - this.f1736t.mo1331k();
        if (mo1331k2 <= 0) {
            return 0;
        }
        int i7 = -m970j1(mo1331k2, c0295t, c0300y);
        int i8 = i6 + i7;
        if (!z5 || (mo1331k = i8 - this.f1736t.mo1331k()) <= 0) {
            return i7;
        }
        this.f1736t.mo1335p(-mo1331k);
        return i7 - mo1331k;
    }

    /* renamed from: b1 */
    public final View m959b1() {
        return m1156w(this.f1739w ? 0 : m1157x() - 1);
    }

    /* renamed from: c1 */
    public final View m960c1() {
        return m1156w(this.f1739w ? m1157x() - 1 : 0);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: d */
    public final void mo961d(String str) {
        if (this.f1729B == null) {
            super.mo961d(str);
        }
    }

    /* renamed from: d1 */
    public final boolean m962d1() {
        return m1126G() == 1;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: e */
    public final boolean mo963e() {
        return this.f1734r == 0;
    }

    /* renamed from: e1 */
    public void mo907e1(RecyclerView.C0295t c0295t, RecyclerView.C0300y c0300y, C0272c c0272c, C0271b c0271b) {
        int i6;
        int i7;
        int i8;
        int i9;
        int mo1324d;
        View m987c = c0272c.m987c(c0295t);
        if (m987c == null) {
            c0271b.f1749b = true;
            return;
        }
        RecyclerView.C0289n c0289n = (RecyclerView.C0289n) m987c.getLayoutParams();
        if (c0272c.f1762k == null) {
            if (this.f1739w == (c0272c.f1757f == -1)) {
                m1142b(m987c);
            } else {
                m1143c(m987c, 0, false);
            }
        } else {
            if (this.f1739w == (c0272c.f1757f == -1)) {
                m1143c(m987c, -1, true);
            } else {
                m1143c(m987c, 0, true);
            }
        }
        RecyclerView.C0289n c0289n2 = (RecyclerView.C0289n) m987c.getLayoutParams();
        Rect m1005L = this.f1883b.m1005L(m987c);
        int i10 = m1005L.left + m1005L.right + 0;
        int i11 = m1005L.top + m1005L.bottom + 0;
        int m1115y = RecyclerView.AbstractC0288m.m1115y(this.f1897p, this.f1895n, m1133L() + m1132K() + ((ViewGroup.MarginLayoutParams) c0289n2).leftMargin + ((ViewGroup.MarginLayoutParams) c0289n2).rightMargin + i10, ((ViewGroup.MarginLayoutParams) c0289n2).width, mo963e());
        int m1115y2 = RecyclerView.AbstractC0288m.m1115y(this.f1898q, this.f1896o, m1131J() + m1134M() + ((ViewGroup.MarginLayoutParams) c0289n2).topMargin + ((ViewGroup.MarginLayoutParams) c0289n2).bottomMargin + i11, ((ViewGroup.MarginLayoutParams) c0289n2).height, mo964f());
        if (m1124E0(m987c, m1115y, m1115y2, c0289n2)) {
            m987c.measure(m1115y, m1115y2);
        }
        c0271b.f1748a = this.f1736t.mo1323c(m987c);
        if (this.f1734r == 1) {
            if (m962d1()) {
                mo1324d = this.f1897p - m1133L();
                i9 = mo1324d - this.f1736t.mo1324d(m987c);
            } else {
                i9 = m1132K();
                mo1324d = this.f1736t.mo1324d(m987c) + i9;
            }
            int i12 = c0272c.f1757f;
            int i13 = c0272c.f1753b;
            if (i12 == -1) {
                i8 = i13;
                i7 = mo1324d;
                i6 = i13 - c0271b.f1748a;
            } else {
                i6 = i13;
                i7 = mo1324d;
                i8 = c0271b.f1748a + i13;
            }
        } else {
            int m1134M = m1134M();
            int mo1324d2 = this.f1736t.mo1324d(m987c) + m1134M;
            int i14 = c0272c.f1757f;
            int i15 = c0272c.f1753b;
            if (i14 == -1) {
                i7 = i15;
                i6 = m1134M;
                i8 = mo1324d2;
                i9 = i15 - c0271b.f1748a;
            } else {
                i6 = m1134M;
                i7 = c0271b.f1748a + i15;
                i8 = mo1324d2;
                i9 = i15;
            }
        }
        m1137T(m987c, i9, i6, i7, i8);
        if (c0289n.m1167c() || c0289n.m1166b()) {
            c0271b.f1750c = true;
        }
        c0271b.f1751d = m987c.hasFocusable();
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: f */
    public final boolean mo964f() {
        return this.f1734r == 1;
    }

    /* renamed from: f1 */
    public void mo909f1(RecyclerView.C0295t c0295t, RecyclerView.C0300y c0300y, C0270a c0270a, int i6) {
    }

    /* renamed from: g1 */
    public final void m965g1(RecyclerView.C0295t c0295t, C0272c c0272c) {
        if (!c0272c.f1752a || c0272c.f1763l) {
            return;
        }
        int i6 = c0272c.f1758g;
        int i7 = c0272c.f1760i;
        if (c0272c.f1757f == -1) {
            int m1157x = m1157x();
            if (i6 < 0) {
                return;
            }
            int mo1326f = (this.f1736t.mo1326f() - i6) + i7;
            if (this.f1739w) {
                for (int i8 = 0; i8 < m1157x; i8++) {
                    View m1156w = m1156w(i8);
                    if (this.f1736t.mo1325e(m1156w) < mo1326f || this.f1736t.mo1334o(m1156w) < mo1326f) {
                        m966h1(c0295t, 0, i8);
                        return;
                    }
                }
                return;
            }
            int i9 = m1157x - 1;
            for (int i10 = i9; i10 >= 0; i10--) {
                View m1156w2 = m1156w(i10);
                if (this.f1736t.mo1325e(m1156w2) < mo1326f || this.f1736t.mo1334o(m1156w2) < mo1326f) {
                    m966h1(c0295t, i9, i10);
                    return;
                }
            }
            return;
        }
        if (i6 < 0) {
            return;
        }
        int i11 = i6 - i7;
        int m1157x2 = m1157x();
        if (!this.f1739w) {
            for (int i12 = 0; i12 < m1157x2; i12++) {
                View m1156w3 = m1156w(i12);
                if (this.f1736t.mo1322b(m1156w3) > i11 || this.f1736t.mo1333n(m1156w3) > i11) {
                    m966h1(c0295t, 0, i12);
                    return;
                }
            }
            return;
        }
        int i13 = m1157x2 - 1;
        for (int i14 = i13; i14 >= 0; i14--) {
            View m1156w4 = m1156w(i14);
            if (this.f1736t.mo1322b(m1156w4) > i11 || this.f1736t.mo1333n(m1156w4) > i11) {
                m966h1(c0295t, i13, i14);
                return;
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:137:0x0175  */
    /* JADX WARN: Removed duplicated region for block: B:152:0x0262  */
    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: h0 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void mo912h0(androidx.recyclerview.widget.RecyclerView.C0295t r17, androidx.recyclerview.widget.RecyclerView.C0300y r18) {
        /*
            Method dump skipped, instructions count: 1151
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.LinearLayoutManager.mo912h0(androidx.recyclerview.widget.RecyclerView$t, androidx.recyclerview.widget.RecyclerView$y):void");
    }

    /* renamed from: h1 */
    public final void m966h1(RecyclerView.C0295t c0295t, int i6, int i7) {
        if (i6 == i7) {
            return;
        }
        if (i7 <= i6) {
            while (i6 > i7) {
                m1152r0(i6, c0295t);
                i6--;
            }
        } else {
            for (int i8 = i7 - 1; i8 >= i6; i8--) {
                m1152r0(i8, c0295t);
            }
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: i */
    public final void mo967i(int i6, int i7, RecyclerView.C0300y c0300y, RecyclerView.AbstractC0288m.c cVar) {
        if (this.f1734r != 0) {
            i6 = i7;
        }
        if (m1157x() == 0 || i6 == 0) {
            return;
        }
        m945Q0();
        m975m1(i6 > 0 ? 1 : -1, Math.abs(i6), true, c0300y);
        mo899L0(c0300y, this.f1735s, cVar);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: i0 */
    public void mo913i0() {
        this.f1729B = null;
        this.f1742z = -1;
        this.f1728A = Integer.MIN_VALUE;
        this.f1730C.m984d();
    }

    /* renamed from: i1 */
    public final void m968i1() {
        this.f1739w = (this.f1734r == 1 || !m962d1()) ? this.f1738v : !this.f1738v;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: j */
    public final void mo969j(int i6, RecyclerView.AbstractC0288m.c cVar) {
        boolean z5;
        int i7;
        C0273d c0273d = this.f1729B;
        if (c0273d == null || !c0273d.m988a()) {
            m968i1();
            z5 = this.f1739w;
            i7 = this.f1742z;
            if (i7 == -1) {
                i7 = z5 ? i6 - 1 : 0;
            }
        } else {
            C0273d c0273d2 = this.f1729B;
            z5 = c0273d2.f1766l;
            i7 = c0273d2.f1764j;
        }
        int i8 = z5 ? -1 : 1;
        for (int i9 = 0; i9 < this.f1732E && i7 >= 0 && i7 < i6; i9++) {
            ((RunnableC0323m.b) cVar).m1316a(i7, 0);
            i7 += i8;
        }
    }

    /* renamed from: j1 */
    public final int m970j1(int i6, RecyclerView.C0295t c0295t, RecyclerView.C0300y c0300y) {
        if (m1157x() == 0 || i6 == 0) {
            return 0;
        }
        m945Q0();
        this.f1735s.f1752a = true;
        int i7 = i6 > 0 ? 1 : -1;
        int abs = Math.abs(i6);
        m975m1(i7, abs, true, c0300y);
        C0272c c0272c = this.f1735s;
        int m947R0 = m947R0(c0295t, c0272c, c0300y, false) + c0272c.f1758g;
        if (m947R0 < 0) {
            return 0;
        }
        if (abs > m947R0) {
            i6 = i7 * m947R0;
        }
        this.f1736t.mo1335p(-i6);
        this.f1735s.f1761j = i6;
        return i6;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: k */
    public final int mo971k(RecyclerView.C0300y c0300y) {
        return m941M0(c0300y);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: k0 */
    public final void mo972k0(Parcelable parcelable) {
        if (parcelable instanceof C0273d) {
            this.f1729B = (C0273d) parcelable;
            m1155u0();
        }
    }

    /* renamed from: k1 */
    public final void m973k1(int i6) {
        if (i6 != 0 && i6 != 1) {
            throw new IllegalArgumentException(C0174y.m490h("invalid orientation:", i6));
        }
        mo961d(null);
        if (i6 != this.f1734r || this.f1736t == null) {
            AbstractC0329s m1336a = AbstractC0329s.m1336a(this, i6);
            this.f1736t = m1336a;
            this.f1730C.f1743a = m1336a;
            this.f1734r = i6;
            m1155u0();
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: l */
    public int mo914l(RecyclerView.C0300y c0300y) {
        return m942N0(c0300y);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: l0 */
    public final Parcelable mo974l0() {
        C0273d c0273d = this.f1729B;
        if (c0273d != null) {
            return new C0273d(c0273d);
        }
        C0273d c0273d2 = new C0273d();
        if (m1157x() > 0) {
            m945Q0();
            boolean z5 = this.f1737u ^ this.f1739w;
            c0273d2.f1766l = z5;
            if (z5) {
                View m959b1 = m959b1();
                c0273d2.f1765k = this.f1736t.mo1327g() - this.f1736t.mo1322b(m959b1);
                c0273d2.f1764j = m1135N(m959b1);
            } else {
                View m960c1 = m960c1();
                c0273d2.f1764j = m1135N(m960c1);
                c0273d2.f1765k = this.f1736t.mo1325e(m960c1) - this.f1736t.mo1331k();
            }
        } else {
            c0273d2.f1764j = -1;
        }
        return c0273d2;
    }

    /* renamed from: l1 */
    public void mo915l1(boolean z5) {
        mo961d(null);
        if (this.f1740x == z5) {
            return;
        }
        this.f1740x = z5;
        m1155u0();
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: m */
    public int mo916m(RecyclerView.C0300y c0300y) {
        return m943O0(c0300y);
    }

    /* renamed from: m1 */
    public final void m975m1(int i6, int i7, boolean z5, RecyclerView.C0300y c0300y) {
        int mo1331k;
        this.f1735s.f1763l = this.f1736t.mo1329i() == 0 && this.f1736t.mo1326f() == 0;
        this.f1735s.f1757f = i6;
        int[] iArr = this.f1733F;
        iArr[0] = 0;
        iArr[1] = 0;
        mo940K0(c0300y, iArr);
        int max = Math.max(0, this.f1733F[0]);
        int max2 = Math.max(0, this.f1733F[1]);
        boolean z6 = i6 == 1;
        C0272c c0272c = this.f1735s;
        int i8 = z6 ? max2 : max;
        c0272c.f1759h = i8;
        if (!z6) {
            max = max2;
        }
        c0272c.f1760i = max;
        if (z6) {
            c0272c.f1759h = this.f1736t.mo1328h() + i8;
            View m959b1 = m959b1();
            C0272c c0272c2 = this.f1735s;
            c0272c2.f1756e = this.f1739w ? -1 : 1;
            int m1135N = m1135N(m959b1);
            C0272c c0272c3 = this.f1735s;
            c0272c2.f1755d = m1135N + c0272c3.f1756e;
            c0272c3.f1753b = this.f1736t.mo1322b(m959b1);
            mo1331k = this.f1736t.mo1322b(m959b1) - this.f1736t.mo1327g();
        } else {
            View m960c1 = m960c1();
            C0272c c0272c4 = this.f1735s;
            c0272c4.f1759h = this.f1736t.mo1331k() + c0272c4.f1759h;
            C0272c c0272c5 = this.f1735s;
            c0272c5.f1756e = this.f1739w ? 1 : -1;
            int m1135N2 = m1135N(m960c1);
            C0272c c0272c6 = this.f1735s;
            c0272c5.f1755d = m1135N2 + c0272c6.f1756e;
            c0272c6.f1753b = this.f1736t.mo1325e(m960c1);
            mo1331k = (-this.f1736t.mo1325e(m960c1)) + this.f1736t.mo1331k();
        }
        C0272c c0272c7 = this.f1735s;
        c0272c7.f1754c = i7;
        if (z5) {
            c0272c7.f1754c = i7 - mo1331k;
        }
        c0272c7.f1758g = mo1331k;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: n */
    public final int mo976n(RecyclerView.C0300y c0300y) {
        return m941M0(c0300y);
    }

    /* renamed from: n1 */
    public final void m977n1(int i6, int i7) {
        this.f1735s.f1754c = this.f1736t.mo1327g() - i7;
        C0272c c0272c = this.f1735s;
        c0272c.f1756e = this.f1739w ? -1 : 1;
        c0272c.f1755d = i6;
        c0272c.f1757f = 1;
        c0272c.f1753b = i7;
        c0272c.f1758g = Integer.MIN_VALUE;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: o */
    public int mo917o(RecyclerView.C0300y c0300y) {
        return m942N0(c0300y);
    }

    /* renamed from: o1 */
    public final void m978o1(int i6, int i7) {
        this.f1735s.f1754c = i7 - this.f1736t.mo1331k();
        C0272c c0272c = this.f1735s;
        c0272c.f1755d = i6;
        c0272c.f1756e = this.f1739w ? 1 : -1;
        c0272c.f1757f = -1;
        c0272c.f1753b = i7;
        c0272c.f1758g = Integer.MIN_VALUE;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: p */
    public int mo918p(RecyclerView.C0300y c0300y) {
        return m943O0(c0300y);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: s */
    public final View mo979s(int i6) {
        int m1157x = m1157x();
        if (m1157x == 0) {
            return null;
        }
        int m1135N = i6 - m1135N(m1156w(0));
        if (m1135N >= 0 && m1135N < m1157x) {
            View m1156w = m1156w(m1135N);
            if (m1135N(m1156w) == i6) {
                return m1156w;
            }
        }
        return super.mo979s(i6);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: t */
    public RecyclerView.C0289n mo923t() {
        return new RecyclerView.C0289n(-2, -2);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: v0 */
    public int mo928v0(int i6, RecyclerView.C0295t c0295t, RecyclerView.C0300y c0300y) {
        if (this.f1734r == 1) {
            return 0;
        }
        return m970j1(i6, c0295t, c0300y);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: w0 */
    public final void mo980w0(int i6) {
        this.f1742z = i6;
        this.f1728A = Integer.MIN_VALUE;
        C0273d c0273d = this.f1729B;
        if (c0273d != null) {
            c0273d.f1764j = -1;
        }
        m1155u0();
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
    /* renamed from: x0 */
    public int mo931x0(int i6, RecyclerView.C0295t c0295t, RecyclerView.C0300y c0300y) {
        if (this.f1734r == 0) {
            return 0;
        }
        return m970j1(i6, c0295t, c0300y);
    }

    public LinearLayoutManager(Context context, AttributeSet attributeSet, int i6, int i7) {
        this.f1734r = 1;
        this.f1738v = false;
        this.f1739w = false;
        this.f1740x = false;
        this.f1741y = true;
        this.f1742z = -1;
        this.f1728A = Integer.MIN_VALUE;
        this.f1729B = null;
        this.f1730C = new C0270a();
        this.f1731D = new C0271b();
        this.f1732E = 2;
        this.f1733F = new int[2];
        RecyclerView.AbstractC0288m.d m1112O = RecyclerView.AbstractC0288m.m1112O(context, attributeSet, i6, i7);
        m973k1(m1112O.f1901a);
        boolean z5 = m1112O.f1903c;
        mo961d(null);
        if (z5 != this.f1738v) {
            this.f1738v = z5;
            m1155u0();
        }
        mo915l1(m1112O.f1904d);
    }
}
