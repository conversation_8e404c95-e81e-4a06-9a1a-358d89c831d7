package androidx.recyclerview.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.view.View;
import android.view.ViewPropertyAnimator;
import androidx.recyclerview.widget.RecyclerView;
import java.util.Objects;

/* renamed from: androidx.recyclerview.widget.f */
/* loaded from: classes.dex */
public final class C0316f extends AnimatorListenerAdapter {

    /* renamed from: a */
    public final /* synthetic */ RecyclerView.AbstractC0277b0 f2045a;

    /* renamed from: b */
    public final /* synthetic */ ViewPropertyAnimator f2046b;

    /* renamed from: c */
    public final /* synthetic */ View f2047c;

    /* renamed from: d */
    public final /* synthetic */ C0321k f2048d;

    public C0316f(C0321k c0321k, RecyclerView.AbstractC0277b0 abstractC0277b0, ViewPropertyAnimator viewPropertyAnimator, View view) {
        this.f2048d = c0321k;
        this.f2045a = abstractC0277b0;
        this.f2046b = viewPropertyAnimator;
        this.f2047c = view;
    }

    @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
    public final void onAnimationEnd(Animator animator) {
        this.f2046b.setListener(null);
        this.f2047c.setAlpha(1.0f);
        this.f2048d.m1103c(this.f2045a);
        this.f2048d.f2077q.remove(this.f2045a);
        this.f2048d.m1302j();
    }

    @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
    public final void onAnimationStart(Animator animator) {
        Objects.requireNonNull(this.f2048d);
    }
}
