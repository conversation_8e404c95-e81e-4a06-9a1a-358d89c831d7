package androidx.recyclerview.widget;

import android.view.View;
import androidx.recyclerview.widget.RecyclerView;

/* renamed from: androidx.recyclerview.widget.u */
/* loaded from: classes.dex */
public class C0331u extends AbstractC0309a0 {

    /* renamed from: c */
    public C0328r f2163c;

    /* renamed from: d */
    public C0327q f2164d;

    @Override // androidx.recyclerview.widget.AbstractC0309a0
    /* renamed from: b */
    public final int[] mo1264b(RecyclerView.AbstractC0288m abstractC0288m, View view) {
        int[] iArr = new int[2];
        if (abstractC0288m.mo963e()) {
            iArr[0] = m1338e(view, m1340g(abstractC0288m));
        } else {
            iArr[0] = 0;
        }
        if (abstractC0288m.mo964f()) {
            iArr[1] = m1338e(view, m1341h(abstractC0288m));
        } else {
            iArr[1] = 0;
        }
        return iArr;
    }

    @Override // androidx.recyclerview.widget.AbstractC0309a0
    /* renamed from: c */
    public View mo1265c(RecyclerView.AbstractC0288m abstractC0288m) {
        AbstractC0329s m1340g;
        if (abstractC0288m.mo964f()) {
            m1340g = m1341h(abstractC0288m);
        } else {
            if (!abstractC0288m.mo963e()) {
                return null;
            }
            m1340g = m1340g(abstractC0288m);
        }
        return m1339f(abstractC0288m, m1340g);
    }

    /* renamed from: e */
    public final int m1338e(View view, AbstractC0329s abstractC0329s) {
        return ((abstractC0329s.mo1323c(view) / 2) + abstractC0329s.mo1325e(view)) - ((abstractC0329s.mo1332l() / 2) + abstractC0329s.mo1331k());
    }

    /* renamed from: f */
    public final View m1339f(RecyclerView.AbstractC0288m abstractC0288m, AbstractC0329s abstractC0329s) {
        int m1157x = abstractC0288m.m1157x();
        View view = null;
        if (m1157x == 0) {
            return null;
        }
        int mo1332l = (abstractC0329s.mo1332l() / 2) + abstractC0329s.mo1331k();
        int i6 = Integer.MAX_VALUE;
        for (int i7 = 0; i7 < m1157x; i7++) {
            View m1156w = abstractC0288m.m1156w(i7);
            int abs = Math.abs(((abstractC0329s.mo1323c(m1156w) / 2) + abstractC0329s.mo1325e(m1156w)) - mo1332l);
            if (abs < i6) {
                view = m1156w;
                i6 = abs;
            }
        }
        return view;
    }

    /* renamed from: g */
    public final AbstractC0329s m1340g(RecyclerView.AbstractC0288m abstractC0288m) {
        C0327q c0327q = this.f2164d;
        if (c0327q == null || c0327q.f2159a != abstractC0288m) {
            this.f2164d = new C0327q(abstractC0288m);
        }
        return this.f2164d;
    }

    /* renamed from: h */
    public final AbstractC0329s m1341h(RecyclerView.AbstractC0288m abstractC0288m) {
        C0328r c0328r = this.f2163c;
        if (c0328r == null || c0328r.f2159a != abstractC0288m) {
            this.f2163c = new C0328r(abstractC0288m);
        }
        return this.f2163c;
    }
}
