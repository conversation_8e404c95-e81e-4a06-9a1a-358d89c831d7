package androidx.recyclerview.widget;

import android.view.View;
import android.view.ViewPropertyAnimator;
import androidx.recyclerview.widget.C0321k;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Objects;

/* renamed from: androidx.recyclerview.widget.c */
/* loaded from: classes.dex */
public final class RunnableC0312c implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ ArrayList f2033j;

    /* renamed from: k */
    public final /* synthetic */ C0321k f2034k;

    public RunnableC0312c(C0321k c0321k, ArrayList arrayList) {
        this.f2034k = c0321k;
        this.f2033j = arrayList;
    }

    @Override // java.lang.Runnable
    public final void run() {
        Iterator it = this.f2033j.iterator();
        while (it.hasNext()) {
            C0321k.b bVar = (C0321k.b) it.next();
            C0321k c0321k = this.f2034k;
            RecyclerView.AbstractC0277b0 abstractC0277b0 = bVar.f2085a;
            int i6 = bVar.f2086b;
            int i7 = bVar.f2087c;
            int i8 = bVar.f2088d;
            int i9 = bVar.f2089e;
            Objects.requireNonNull(c0321k);
            View view = abstractC0277b0.f1852a;
            int i10 = i8 - i6;
            int i11 = i9 - i7;
            if (i10 != 0) {
                view.animate().translationX(0.0f);
            }
            if (i11 != 0) {
                view.animate().translationY(0.0f);
            }
            ViewPropertyAnimator animate = view.animate();
            c0321k.f2076p.add(abstractC0277b0);
            animate.setDuration(c0321k.f1877e).setListener(new C0318h(c0321k, abstractC0277b0, i10, view, i11, animate)).start();
        }
        this.f2033j.clear();
        this.f2034k.f2073m.remove(this.f2033j);
    }
}
