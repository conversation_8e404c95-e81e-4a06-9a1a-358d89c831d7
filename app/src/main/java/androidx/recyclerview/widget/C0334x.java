package androidx.recyclerview.widget;

import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import androidx.recyclerview.widget.RecyclerView;
import java.util.Map;
import java.util.WeakHashMap;
import p029e0.C0751a;
import p036f0.C0831b;
import p036f0.C0832c;

/* renamed from: androidx.recyclerview.widget.x */
/* loaded from: classes.dex */
public final class C0334x extends C0751a {

    /* renamed from: d */
    public final RecyclerView f2167d;

    /* renamed from: e */
    public final a f2168e;

    /* renamed from: androidx.recyclerview.widget.x$a */
    public static class a extends C0751a {

        /* renamed from: d */
        public final C0334x f2169d;

        /* renamed from: e */
        public Map<View, C0751a> f2170e = new WeakHashMap();

        public a(C0334x c0334x) {
            this.f2169d = c0334x;
        }

        /* JADX WARN: Type inference failed for: r0v0, types: [java.util.Map<android.view.View, e0.a>, java.util.WeakHashMap] */
        @Override // p029e0.C0751a
        /* renamed from: a */
        public final boolean mo1352a(View view, AccessibilityEvent accessibilityEvent) {
            C0751a c0751a = (C0751a) this.f2170e.get(view);
            return c0751a != null ? c0751a.mo1352a(view, accessibilityEvent) : super.mo1352a(view, accessibilityEvent);
        }

        /* JADX WARN: Type inference failed for: r0v0, types: [java.util.Map<android.view.View, e0.a>, java.util.WeakHashMap] */
        @Override // p029e0.C0751a
        /* renamed from: b */
        public final C0832c mo1353b(View view) {
            C0751a c0751a = (C0751a) this.f2170e.get(view);
            return c0751a != null ? c0751a.mo1353b(view) : super.mo1353b(view);
        }

        /* JADX WARN: Type inference failed for: r0v0, types: [java.util.Map<android.view.View, e0.a>, java.util.WeakHashMap] */
        @Override // p029e0.C0751a
        /* renamed from: c */
        public final void mo612c(View view, AccessibilityEvent accessibilityEvent) {
            C0751a c0751a = (C0751a) this.f2170e.get(view);
            if (c0751a != null) {
                c0751a.mo612c(view, accessibilityEvent);
            } else {
                super.mo612c(view, accessibilityEvent);
            }
        }

        /* JADX WARN: Type inference failed for: r0v9, types: [java.util.Map<android.view.View, e0.a>, java.util.WeakHashMap] */
        @Override // p029e0.C0751a
        /* renamed from: d */
        public final void mo613d(View view, C0831b c0831b) {
            if (!this.f2169d.m1351j() && this.f2169d.f2167d.getLayoutManager() != null) {
                this.f2169d.f2167d.getLayoutManager().m1141a0(view, c0831b);
                C0751a c0751a = (C0751a) this.f2170e.get(view);
                if (c0751a != null) {
                    c0751a.mo613d(view, c0831b);
                    return;
                }
            }
            this.f4012a.onInitializeAccessibilityNodeInfo(view, c0831b.f4255a);
        }

        /* JADX WARN: Type inference failed for: r0v0, types: [java.util.Map<android.view.View, e0.a>, java.util.WeakHashMap] */
        @Override // p029e0.C0751a
        /* renamed from: e */
        public final void mo1354e(View view, AccessibilityEvent accessibilityEvent) {
            C0751a c0751a = (C0751a) this.f2170e.get(view);
            if (c0751a != null) {
                c0751a.mo1354e(view, accessibilityEvent);
            } else {
                super.mo1354e(view, accessibilityEvent);
            }
        }

        /* JADX WARN: Type inference failed for: r0v0, types: [java.util.Map<android.view.View, e0.a>, java.util.WeakHashMap] */
        @Override // p029e0.C0751a
        /* renamed from: f */
        public final boolean mo1355f(ViewGroup viewGroup, View view, AccessibilityEvent accessibilityEvent) {
            C0751a c0751a = (C0751a) this.f2170e.get(viewGroup);
            return c0751a != null ? c0751a.mo1355f(viewGroup, view, accessibilityEvent) : super.mo1355f(viewGroup, view, accessibilityEvent);
        }

        /* JADX WARN: Type inference failed for: r0v5, types: [java.util.Map<android.view.View, e0.a>, java.util.WeakHashMap] */
        @Override // p029e0.C0751a
        /* renamed from: g */
        public final boolean mo614g(View view, int i6, Bundle bundle) {
            if (this.f2169d.m1351j() || this.f2169d.f2167d.getLayoutManager() == null) {
                return super.mo614g(view, i6, bundle);
            }
            C0751a c0751a = (C0751a) this.f2170e.get(view);
            if (c0751a != null) {
                if (c0751a.mo614g(view, i6, bundle)) {
                    return true;
                }
            } else if (super.mo614g(view, i6, bundle)) {
                return true;
            }
            RecyclerView.C0295t c0295t = this.f2169d.f2167d.getLayoutManager().f1883b.f1810k;
            return false;
        }

        /* JADX WARN: Type inference failed for: r0v0, types: [java.util.Map<android.view.View, e0.a>, java.util.WeakHashMap] */
        @Override // p029e0.C0751a
        /* renamed from: h */
        public final void mo1356h(View view, int i6) {
            C0751a c0751a = (C0751a) this.f2170e.get(view);
            if (c0751a != null) {
                c0751a.mo1356h(view, i6);
            } else {
                super.mo1356h(view, i6);
            }
        }

        /* JADX WARN: Type inference failed for: r0v0, types: [java.util.Map<android.view.View, e0.a>, java.util.WeakHashMap] */
        @Override // p029e0.C0751a
        /* renamed from: i */
        public final void mo1357i(View view, AccessibilityEvent accessibilityEvent) {
            C0751a c0751a = (C0751a) this.f2170e.get(view);
            if (c0751a != null) {
                c0751a.mo1357i(view, accessibilityEvent);
            } else {
                super.mo1357i(view, accessibilityEvent);
            }
        }
    }

    public C0334x(RecyclerView recyclerView) {
        this.f2167d = recyclerView;
        a aVar = this.f2168e;
        this.f2168e = aVar == null ? new a(this) : aVar;
    }

    @Override // p029e0.C0751a
    /* renamed from: c */
    public final void mo612c(View view, AccessibilityEvent accessibilityEvent) {
        super.mo612c(view, accessibilityEvent);
        if (!(view instanceof RecyclerView) || m1351j()) {
            return;
        }
        RecyclerView recyclerView = (RecyclerView) view;
        if (recyclerView.getLayoutManager() != null) {
            recyclerView.getLayoutManager().mo955Y(accessibilityEvent);
        }
    }

    @Override // p029e0.C0751a
    /* renamed from: d */
    public final void mo613d(View view, C0831b c0831b) {
        this.f4012a.onInitializeAccessibilityNodeInfo(view, c0831b.f4255a);
        if (m1351j() || this.f2167d.getLayoutManager() == null) {
            return;
        }
        RecyclerView.AbstractC0288m layoutManager = this.f2167d.getLayoutManager();
        RecyclerView recyclerView = layoutManager.f1883b;
        layoutManager.mo1140Z(recyclerView.f1810k, recyclerView.f1821p0, c0831b);
    }

    @Override // p029e0.C0751a
    /* renamed from: g */
    public final boolean mo614g(View view, int i6, Bundle bundle) {
        if (super.mo614g(view, i6, bundle)) {
            return true;
        }
        if (m1351j() || this.f2167d.getLayoutManager() == null) {
            return false;
        }
        RecyclerView.AbstractC0288m layoutManager = this.f2167d.getLayoutManager();
        RecyclerView recyclerView = layoutManager.f1883b;
        return layoutManager.mo1146n0(recyclerView.f1810k, recyclerView.f1821p0, i6, bundle);
    }

    /* renamed from: j */
    public final boolean m1351j() {
        return this.f2167d.m1006M();
    }
}
