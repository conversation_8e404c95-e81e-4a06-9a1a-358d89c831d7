package androidx.recyclerview.widget;

import android.view.View;
import android.view.ViewPropertyAnimator;
import androidx.recyclerview.widget.C0321k;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Objects;

/* renamed from: androidx.recyclerview.widget.d */
/* loaded from: classes.dex */
public final class RunnableC0314d implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ ArrayList f2041j;

    /* renamed from: k */
    public final /* synthetic */ C0321k f2042k;

    public RunnableC0314d(C0321k c0321k, ArrayList arrayList) {
        this.f2042k = c0321k;
        this.f2041j = arrayList;
    }

    @Override // java.lang.Runnable
    public final void run() {
        Iterator it = this.f2041j.iterator();
        while (it.hasNext()) {
            C0321k.a aVar = (C0321k.a) it.next();
            C0321k c0321k = this.f2042k;
            Objects.requireNonNull(c0321k);
            RecyclerView.AbstractC0277b0 abstractC0277b0 = aVar.f2079a;
            View view = abstractC0277b0 == null ? null : abstractC0277b0.f1852a;
            RecyclerView.AbstractC0277b0 abstractC0277b02 = aVar.f2080b;
            View view2 = abstractC0277b02 != null ? abstractC0277b02.f1852a : null;
            if (view != null) {
                ViewPropertyAnimator duration = view.animate().setDuration(c0321k.f1878f);
                c0321k.f2078r.add(aVar.f2079a);
                duration.translationX(aVar.f2083e - aVar.f2081c);
                duration.translationY(aVar.f2084f - aVar.f2082d);
                duration.alpha(0.0f).setListener(new C0319i(c0321k, aVar, duration, view)).start();
            }
            if (view2 != null) {
                ViewPropertyAnimator animate = view2.animate();
                c0321k.f2078r.add(aVar.f2080b);
                animate.translationX(0.0f).translationY(0.0f).setDuration(c0321k.f1878f).alpha(1.0f).setListener(new C0320j(c0321k, aVar, animate, view2)).start();
            }
        }
        this.f2041j.clear();
        this.f2042k.f2074n.remove(this.f2041j);
    }
}
