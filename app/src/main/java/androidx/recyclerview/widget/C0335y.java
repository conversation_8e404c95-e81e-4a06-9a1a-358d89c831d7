package androidx.recyclerview.widget;

import android.view.View;
import androidx.recyclerview.widget.RecyclerView;

/* renamed from: androidx.recyclerview.widget.y */
/* loaded from: classes.dex */
public final class C0335y {
    /* renamed from: a */
    public static int m1358a(RecyclerView.C0300y c0300y, AbstractC0329s abstractC0329s, View view, View view2, RecyclerView.AbstractC0288m abstractC0288m, boolean z5) {
        if (abstractC0288m.m1157x() == 0 || c0300y.m1196b() == 0 || view == null || view2 == null) {
            return 0;
        }
        if (!z5) {
            return Math.abs(abstractC0288m.m1135N(view) - abstractC0288m.m1135N(view2)) + 1;
        }
        return Math.min(abstractC0329s.mo1332l(), abstractC0329s.mo1322b(view2) - abstractC0329s.mo1325e(view));
    }

    /* renamed from: b */
    public static int m1359b(RecyclerView.C0300y c0300y, AbstractC0329s abstractC0329s, View view, View view2, RecyclerView.AbstractC0288m abstractC0288m, boolean z5, boolean z6) {
        if (abstractC0288m.m1157x() == 0 || c0300y.m1196b() == 0 || view == null || view2 == null) {
            return 0;
        }
        int max = z6 ? Math.max(0, (c0300y.m1196b() - Math.max(abstractC0288m.m1135N(view), abstractC0288m.m1135N(view2))) - 1) : Math.max(0, Math.min(abstractC0288m.m1135N(view), abstractC0288m.m1135N(view2)));
        if (z5) {
            return Math.round((max * (Math.abs(abstractC0329s.mo1322b(view2) - abstractC0329s.mo1325e(view)) / (Math.abs(abstractC0288m.m1135N(view) - abstractC0288m.m1135N(view2)) + 1))) + (abstractC0329s.mo1331k() - abstractC0329s.mo1325e(view)));
        }
        return max;
    }

    /* renamed from: c */
    public static int m1360c(RecyclerView.C0300y c0300y, AbstractC0329s abstractC0329s, View view, View view2, RecyclerView.AbstractC0288m abstractC0288m, boolean z5) {
        if (abstractC0288m.m1157x() == 0 || c0300y.m1196b() == 0 || view == null || view2 == null) {
            return 0;
        }
        if (!z5) {
            return c0300y.m1196b();
        }
        return (int) (((abstractC0329s.mo1322b(view2) - abstractC0329s.mo1325e(view)) / (Math.abs(abstractC0288m.m1135N(view) - abstractC0288m.m1135N(view2)) + 1)) * c0300y.m1196b());
    }
}
