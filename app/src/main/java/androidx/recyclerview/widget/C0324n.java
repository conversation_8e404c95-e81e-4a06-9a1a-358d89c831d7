package androidx.recyclerview.widget;

import androidx.activity.result.C0052a;

/* renamed from: androidx.recyclerview.widget.n */
/* loaded from: classes.dex */
public final class C0324n {

    /* renamed from: b */
    public int f2142b;

    /* renamed from: c */
    public int f2143c;

    /* renamed from: d */
    public int f2144d;

    /* renamed from: e */
    public int f2145e;

    /* renamed from: h */
    public boolean f2148h;

    /* renamed from: i */
    public boolean f2149i;

    /* renamed from: a */
    public boolean f2141a = true;

    /* renamed from: f */
    public int f2146f = 0;

    /* renamed from: g */
    public int f2147g = 0;

    public final String toString() {
        StringBuilder m104h = C0052a.m104h("LayoutState{mAvailable=");
        m104h.append(this.f2142b);
        m104h.append(", mCurrentPosition=");
        m104h.append(this.f2143c);
        m104h.append(", mItemDirection=");
        m104h.append(this.f2144d);
        m104h.append(", mLayoutDirection=");
        m104h.append(this.f2145e);
        m104h.append(", mStartLine=");
        m104h.append(this.f2146f);
        m104h.append(", mEndLine=");
        m104h.append(this.f2147g);
        m104h.append('}');
        return m104h.toString();
    }
}
