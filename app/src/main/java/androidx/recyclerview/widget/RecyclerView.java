package androidx.recyclerview.widget;

import android.R;
import android.animation.LayoutTransition;
import android.content.Context;
import android.content.res.TypedArray;
import android.database.Observable;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.RectF;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.os.SystemClock;
import android.os.Trace;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseArray;
import android.view.Display;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.ViewPropertyAnimator;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityManager;
import android.view.animation.Interpolator;
import android.widget.EdgeEffect;
import android.widget.OverScroller;
import androidx.activity.result.C0052a;
import androidx.appcompat.widget.C0174y;
import androidx.recyclerview.widget.C0308a;
import androidx.recyclerview.widget.C0310b;
import androidx.recyclerview.widget.C0311b0;
import androidx.recyclerview.widget.C0313c0;
import androidx.recyclerview.widget.C0321k;
import androidx.recyclerview.widget.C0334x;
import androidx.recyclerview.widget.RunnableC0323m;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.WeakHashMap;
import p001a0.C0003b;
import p029e0.C0751a;
import p029e0.C0758h;
import p029e0.C0766p;
import p029e0.C0769s;
import p029e0.InterfaceC0757g;
import p036f0.C0831b;
import p064j0.AbstractC0956a;
import p153w3.C1798e;

/* loaded from: classes.dex */
public class RecyclerView extends ViewGroup implements InterfaceC0757g {

    /* renamed from: G0 */
    public static final int[] f1767G0 = {R.attr.nestedScrollingEnabled};

    /* renamed from: H0 */
    public static final Class<?>[] f1768H0;

    /* renamed from: I0 */
    public static final InterpolatorC0278c f1769I0;

    /* renamed from: A */
    public boolean f1770A;

    /* renamed from: A0 */
    public final int[] f1771A0;

    /* renamed from: B */
    public boolean f1772B;

    /* renamed from: B0 */
    public final int[] f1773B0;

    /* renamed from: C */
    public boolean f1774C;

    /* renamed from: C0 */
    public final int[] f1775C0;

    /* renamed from: D */
    public int f1776D;

    /* renamed from: D0 */
    public final List<AbstractC0277b0> f1777D0;

    /* renamed from: E */
    public boolean f1778E;

    /* renamed from: E0 */
    public RunnableC0276b f1779E0;

    /* renamed from: F */
    public boolean f1780F;

    /* renamed from: F0 */
    public final C0279d f1781F0;

    /* renamed from: G */
    public boolean f1782G;

    /* renamed from: H */
    public int f1783H;

    /* renamed from: I */
    public boolean f1784I;

    /* renamed from: J */
    public final AccessibilityManager f1785J;

    /* renamed from: K */
    public List<InterfaceC0290o> f1786K;

    /* renamed from: L */
    public boolean f1787L;

    /* renamed from: M */
    public boolean f1788M;

    /* renamed from: N */
    public int f1789N;

    /* renamed from: O */
    public int f1790O;

    /* renamed from: P */
    public C0284i f1791P;

    /* renamed from: Q */
    public EdgeEffect f1792Q;

    /* renamed from: R */
    public EdgeEffect f1793R;

    /* renamed from: S */
    public EdgeEffect f1794S;

    /* renamed from: T */
    public EdgeEffect f1795T;

    /* renamed from: U */
    public AbstractC0285j f1796U;

    /* renamed from: V */
    public int f1797V;

    /* renamed from: W */
    public int f1798W;

    /* renamed from: a0 */
    public VelocityTracker f1799a0;

    /* renamed from: b0 */
    public int f1800b0;

    /* renamed from: c0 */
    public int f1801c0;

    /* renamed from: d0 */
    public int f1802d0;

    /* renamed from: e0 */
    public int f1803e0;

    /* renamed from: f0 */
    public int f1804f0;

    /* renamed from: g0 */
    public AbstractC0291p f1805g0;

    /* renamed from: h0 */
    public final int f1806h0;

    /* renamed from: i0 */
    public final int f1807i0;

    /* renamed from: j */
    public final C0297v f1808j;

    /* renamed from: j0 */
    public float f1809j0;

    /* renamed from: k */
    public final C0295t f1810k;

    /* renamed from: k0 */
    public float f1811k0;

    /* renamed from: l */
    public C0298w f1812l;

    /* renamed from: l0 */
    public boolean f1813l0;

    /* renamed from: m */
    public C0308a f1814m;

    /* renamed from: m0 */
    public final RunnableC0275a0 f1815m0;

    /* renamed from: n */
    public C0310b f1816n;

    /* renamed from: n0 */
    public RunnableC0323m f1817n0;

    /* renamed from: o */
    public final C0313c0 f1818o;

    /* renamed from: o0 */
    public RunnableC0323m.b f1819o0;

    /* renamed from: p */
    public boolean f1820p;

    /* renamed from: p0 */
    public final C0300y f1821p0;

    /* renamed from: q */
    public final RunnableC0274a f1822q;

    /* renamed from: q0 */
    public AbstractC0293r f1823q0;

    /* renamed from: r */
    public final Rect f1824r;

    /* renamed from: r0 */
    public List<AbstractC0293r> f1825r0;

    /* renamed from: s */
    public final Rect f1826s;

    /* renamed from: s0 */
    public boolean f1827s0;

    /* renamed from: t */
    public final RectF f1828t;

    /* renamed from: t0 */
    public boolean f1829t0;

    /* renamed from: u */
    public AbstractC0280e f1830u;

    /* renamed from: u0 */
    public C0286k f1831u0;

    /* renamed from: v */
    public AbstractC0288m f1832v;

    /* renamed from: v0 */
    public boolean f1833v0;

    /* renamed from: w */
    public InterfaceC0296u f1834w;

    /* renamed from: w0 */
    public C0334x f1835w0;

    /* renamed from: x */
    public final ArrayList<AbstractC0287l> f1836x;

    /* renamed from: x0 */
    public InterfaceC0283h f1837x0;

    /* renamed from: y */
    public final ArrayList<InterfaceC0292q> f1838y;

    /* renamed from: y0 */
    public final int[] f1839y0;

    /* renamed from: z */
    public InterfaceC0292q f1840z;

    /* renamed from: z0 */
    public C0758h f1841z0;

    /* renamed from: androidx.recyclerview.widget.RecyclerView$a */
    public class RunnableC0274a implements Runnable {
        public RunnableC0274a() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            RecyclerView recyclerView = RecyclerView.this;
            if (!recyclerView.f1774C || recyclerView.isLayoutRequested()) {
                return;
            }
            RecyclerView recyclerView2 = RecyclerView.this;
            if (!recyclerView2.f1770A) {
                recyclerView2.requestLayout();
            } else if (recyclerView2.f1780F) {
                recyclerView2.f1778E = true;
            } else {
                recyclerView2.m1040n();
            }
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$a0 */
    public class RunnableC0275a0 implements Runnable {

        /* renamed from: j */
        public int f1843j;

        /* renamed from: k */
        public int f1844k;

        /* renamed from: l */
        public OverScroller f1845l;

        /* renamed from: m */
        public Interpolator f1846m;

        /* renamed from: n */
        public boolean f1847n;

        /* renamed from: o */
        public boolean f1848o;

        public RunnableC0275a0() {
            InterpolatorC0278c interpolatorC0278c = RecyclerView.f1769I0;
            this.f1846m = interpolatorC0278c;
            this.f1847n = false;
            this.f1848o = false;
            this.f1845l = new OverScroller(RecyclerView.this.getContext(), interpolatorC0278c);
        }

        /* renamed from: a */
        public final void m1053a() {
            if (this.f1847n) {
                this.f1848o = true;
                return;
            }
            RecyclerView.this.removeCallbacks(this);
            RecyclerView recyclerView = RecyclerView.this;
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            recyclerView.postOnAnimation(this);
        }

        /* renamed from: b */
        public final void m1054b(int i6, int i7, int i8, Interpolator interpolator) {
            int i9;
            if (i8 == Integer.MIN_VALUE) {
                int abs = Math.abs(i6);
                int abs2 = Math.abs(i7);
                boolean z5 = abs > abs2;
                int sqrt = (int) Math.sqrt(0);
                int sqrt2 = (int) Math.sqrt((i7 * i7) + (i6 * i6));
                RecyclerView recyclerView = RecyclerView.this;
                int width = z5 ? recyclerView.getWidth() : recyclerView.getHeight();
                int i10 = width / 2;
                float f6 = width;
                float f7 = i10;
                float sin = (((float) Math.sin((Math.min(1.0f, (sqrt2 * 1.0f) / f6) - 0.5f) * 0.47123894f)) * f7) + f7;
                if (sqrt > 0) {
                    i9 = Math.round(Math.abs(sin / sqrt) * 1000.0f) * 4;
                } else {
                    if (!z5) {
                        abs = abs2;
                    }
                    i9 = (int) (((abs / f6) + 1.0f) * 300.0f);
                }
                i8 = Math.min(i9, 2000);
            }
            int i11 = i8;
            if (interpolator == null) {
                interpolator = RecyclerView.f1769I0;
            }
            if (this.f1846m != interpolator) {
                this.f1846m = interpolator;
                this.f1845l = new OverScroller(RecyclerView.this.getContext(), interpolator);
            }
            this.f1844k = 0;
            this.f1843j = 0;
            RecyclerView.this.setScrollState(2);
            this.f1845l.startScroll(0, 0, i6, i7, i11);
            m1053a();
        }

        /* renamed from: c */
        public final void m1055c() {
            RecyclerView.this.removeCallbacks(this);
            this.f1845l.abortAnimation();
        }

        @Override // java.lang.Runnable
        public final void run() {
            int i6;
            int i7;
            RecyclerView recyclerView = RecyclerView.this;
            if (recyclerView.f1832v == null) {
                m1055c();
                return;
            }
            this.f1848o = false;
            this.f1847n = true;
            recyclerView.m1040n();
            OverScroller overScroller = this.f1845l;
            if (overScroller.computeScrollOffset()) {
                int currX = overScroller.getCurrX();
                int currY = overScroller.getCurrY();
                int i8 = currX - this.f1843j;
                int i9 = currY - this.f1844k;
                this.f1843j = currX;
                this.f1844k = currY;
                RecyclerView recyclerView2 = RecyclerView.this;
                int[] iArr = recyclerView2.f1775C0;
                iArr[0] = 0;
                iArr[1] = 0;
                if (recyclerView2.m1046t(i8, i9, iArr, null, 1)) {
                    int[] iArr2 = RecyclerView.this.f1775C0;
                    i8 -= iArr2[0];
                    i9 -= iArr2[1];
                }
                if (RecyclerView.this.getOverScrollMode() != 2) {
                    RecyclerView.this.m1038m(i8, i9);
                }
                RecyclerView recyclerView3 = RecyclerView.this;
                if (recyclerView3.f1830u != null) {
                    int[] iArr3 = recyclerView3.f1775C0;
                    iArr3[0] = 0;
                    iArr3[1] = 0;
                    recyclerView3.m1023d0(i8, i9, iArr3);
                    RecyclerView recyclerView4 = RecyclerView.this;
                    int[] iArr4 = recyclerView4.f1775C0;
                    i7 = iArr4[0];
                    i6 = iArr4[1];
                    i8 -= i7;
                    i9 -= i6;
                    AbstractC0299x abstractC0299x = recyclerView4.f1832v.f1888g;
                    if (abstractC0299x != null && !abstractC0299x.f1928d && abstractC0299x.f1929e) {
                        int m1196b = recyclerView4.f1821p0.m1196b();
                        if (m1196b == 0) {
                            abstractC0299x.m1192d();
                        } else {
                            if (abstractC0299x.f1925a >= m1196b) {
                                abstractC0299x.f1925a = m1196b - 1;
                            }
                            abstractC0299x.m1190b(i7, i6);
                        }
                    }
                } else {
                    i6 = 0;
                    i7 = 0;
                }
                if (!RecyclerView.this.f1836x.isEmpty()) {
                    RecyclerView.this.invalidate();
                }
                RecyclerView recyclerView5 = RecyclerView.this;
                int[] iArr5 = recyclerView5.f1775C0;
                iArr5[0] = 0;
                iArr5[1] = 0;
                recyclerView5.m1047u(i7, i6, i8, i9, null, 1, iArr5);
                RecyclerView recyclerView6 = RecyclerView.this;
                int[] iArr6 = recyclerView6.f1775C0;
                int i10 = i8 - iArr6[0];
                int i11 = i9 - iArr6[1];
                if (i7 != 0 || i6 != 0) {
                    recyclerView6.m1048v(i7, i6);
                }
                if (!RecyclerView.this.awakenScrollBars()) {
                    RecyclerView.this.invalidate();
                }
                boolean z5 = overScroller.isFinished() || (((overScroller.getCurrX() == overScroller.getFinalX()) || i10 != 0) && ((overScroller.getCurrY() == overScroller.getFinalY()) || i11 != 0));
                RecyclerView recyclerView7 = RecyclerView.this;
                AbstractC0299x abstractC0299x2 = recyclerView7.f1832v.f1888g;
                if ((abstractC0299x2 != null && abstractC0299x2.f1928d) || !z5) {
                    m1053a();
                    RecyclerView recyclerView8 = RecyclerView.this;
                    RunnableC0323m runnableC0323m = recyclerView8.f1817n0;
                    if (runnableC0323m != null) {
                        runnableC0323m.m1313a(recyclerView8, i7, i6);
                    }
                } else {
                    if (recyclerView7.getOverScrollMode() != 2) {
                        int currVelocity = (int) overScroller.getCurrVelocity();
                        int i12 = i10 < 0 ? -currVelocity : i10 > 0 ? currVelocity : 0;
                        if (i11 < 0) {
                            currVelocity = -currVelocity;
                        } else if (i11 <= 0) {
                            currVelocity = 0;
                        }
                        RecyclerView recyclerView9 = RecyclerView.this;
                        Objects.requireNonNull(recyclerView9);
                        if (i12 < 0) {
                            recyclerView9.m1050x();
                            if (recyclerView9.f1792Q.isFinished()) {
                                recyclerView9.f1792Q.onAbsorb(-i12);
                            }
                        } else if (i12 > 0) {
                            recyclerView9.m1051y();
                            if (recyclerView9.f1794S.isFinished()) {
                                recyclerView9.f1794S.onAbsorb(i12);
                            }
                        }
                        if (currVelocity < 0) {
                            recyclerView9.m1052z();
                            if (recyclerView9.f1793R.isFinished()) {
                                recyclerView9.f1793R.onAbsorb(-currVelocity);
                            }
                        } else if (currVelocity > 0) {
                            recyclerView9.m1049w();
                            if (recyclerView9.f1795T.isFinished()) {
                                recyclerView9.f1795T.onAbsorb(currVelocity);
                            }
                        }
                        if (i12 != 0 || currVelocity != 0) {
                            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
                            recyclerView9.postInvalidateOnAnimation();
                        }
                    }
                    int[] iArr7 = RecyclerView.f1767G0;
                    RunnableC0323m.b bVar = RecyclerView.this.f1819o0;
                    int[] iArr8 = bVar.f2134c;
                    if (iArr8 != null) {
                        Arrays.fill(iArr8, -1);
                    }
                    bVar.f2135d = 0;
                }
            }
            AbstractC0299x abstractC0299x3 = RecyclerView.this.f1832v.f1888g;
            if (abstractC0299x3 != null && abstractC0299x3.f1928d) {
                abstractC0299x3.m1190b(0, 0);
            }
            this.f1847n = false;
            if (!this.f1848o) {
                RecyclerView.this.setScrollState(0);
                RecyclerView.this.m1037l0(1);
            } else {
                RecyclerView.this.removeCallbacks(this);
                RecyclerView recyclerView10 = RecyclerView.this;
                WeakHashMap<View, C0769s> weakHashMap2 = C0766p.f4041a;
                recyclerView10.postOnAnimation(this);
            }
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$b */
    public class RunnableC0276b implements Runnable {
        public RunnableC0276b() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            AbstractC0285j abstractC0285j = RecyclerView.this.f1796U;
            if (abstractC0285j != null) {
                C0321k c0321k = (C0321k) abstractC0285j;
                boolean z5 = !c0321k.f2068h.isEmpty();
                boolean z6 = !c0321k.f2070j.isEmpty();
                boolean z7 = !c0321k.f2071k.isEmpty();
                boolean z8 = !c0321k.f2069i.isEmpty();
                if (z5 || z6 || z8 || z7) {
                    Iterator<AbstractC0277b0> it = c0321k.f2068h.iterator();
                    while (it.hasNext()) {
                        AbstractC0277b0 next = it.next();
                        View view = next.f1852a;
                        ViewPropertyAnimator animate = view.animate();
                        c0321k.f2077q.add(next);
                        animate.setDuration(c0321k.f1876d).alpha(0.0f).setListener(new C0316f(c0321k, next, animate, view)).start();
                    }
                    c0321k.f2068h.clear();
                    if (z6) {
                        ArrayList<C0321k.b> arrayList = new ArrayList<>();
                        arrayList.addAll(c0321k.f2070j);
                        c0321k.f2073m.add(arrayList);
                        c0321k.f2070j.clear();
                        RunnableC0312c runnableC0312c = new RunnableC0312c(c0321k, arrayList);
                        if (z5) {
                            View view2 = arrayList.get(0).f2085a.f1852a;
                            long j6 = c0321k.f1876d;
                            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
                            view2.postOnAnimationDelayed(runnableC0312c, j6);
                        } else {
                            runnableC0312c.run();
                        }
                    }
                    if (z7) {
                        ArrayList<C0321k.a> arrayList2 = new ArrayList<>();
                        arrayList2.addAll(c0321k.f2071k);
                        c0321k.f2074n.add(arrayList2);
                        c0321k.f2071k.clear();
                        RunnableC0314d runnableC0314d = new RunnableC0314d(c0321k, arrayList2);
                        if (z5) {
                            View view3 = arrayList2.get(0).f2079a.f1852a;
                            long j7 = c0321k.f1876d;
                            WeakHashMap<View, C0769s> weakHashMap2 = C0766p.f4041a;
                            view3.postOnAnimationDelayed(runnableC0314d, j7);
                        } else {
                            runnableC0314d.run();
                        }
                    }
                    if (z8) {
                        ArrayList<AbstractC0277b0> arrayList3 = new ArrayList<>();
                        arrayList3.addAll(c0321k.f2069i);
                        c0321k.f2072l.add(arrayList3);
                        c0321k.f2069i.clear();
                        RunnableC0315e runnableC0315e = new RunnableC0315e(c0321k, arrayList3);
                        if (z5 || z6 || z7) {
                            long max = Math.max(z6 ? c0321k.f1877e : 0L, z7 ? c0321k.f1878f : 0L) + (z5 ? c0321k.f1876d : 0L);
                            View view4 = arrayList3.get(0).f1852a;
                            WeakHashMap<View, C0769s> weakHashMap3 = C0766p.f4041a;
                            view4.postOnAnimationDelayed(runnableC0315e, max);
                        } else {
                            runnableC0315e.run();
                        }
                    }
                }
            }
            RecyclerView.this.f1833v0 = false;
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$b0 */
    public static abstract class AbstractC0277b0 {

        /* renamed from: s */
        public static final List<Object> f1851s = Collections.emptyList();

        /* renamed from: a */
        public final View f1852a;

        /* renamed from: b */
        public WeakReference<RecyclerView> f1853b;

        /* renamed from: j */
        public int f1861j;

        /* renamed from: r */
        public RecyclerView f1869r;

        /* renamed from: c */
        public int f1854c = -1;

        /* renamed from: d */
        public int f1855d = -1;

        /* renamed from: e */
        public long f1856e = -1;

        /* renamed from: f */
        public int f1857f = -1;

        /* renamed from: g */
        public int f1858g = -1;

        /* renamed from: h */
        public AbstractC0277b0 f1859h = null;

        /* renamed from: i */
        public AbstractC0277b0 f1860i = null;

        /* renamed from: k */
        public List<Object> f1862k = null;

        /* renamed from: l */
        public List<Object> f1863l = null;

        /* renamed from: m */
        public int f1864m = 0;

        /* renamed from: n */
        public C0295t f1865n = null;

        /* renamed from: o */
        public boolean f1866o = false;

        /* renamed from: p */
        public int f1867p = 0;

        /* renamed from: q */
        public int f1868q = -1;

        public AbstractC0277b0(View view) {
            if (view == null) {
                throw new IllegalArgumentException("itemView may not be null");
            }
            this.f1852a = view;
        }

        /* JADX WARN: Type inference failed for: r0v3, types: [java.util.ArrayList, java.util.List<java.lang.Object>] */
        /* renamed from: a */
        public final void m1056a(Object obj) {
            if (obj == null) {
                m1057b(1024);
                return;
            }
            if ((1024 & this.f1861j) == 0) {
                if (this.f1862k == null) {
                    ArrayList arrayList = new ArrayList();
                    this.f1862k = arrayList;
                    this.f1863l = Collections.unmodifiableList(arrayList);
                }
                this.f1862k.add(obj);
            }
        }

        /* renamed from: b */
        public final void m1057b(int i6) {
            this.f1861j = i6 | this.f1861j;
        }

        /* renamed from: c */
        public final void m1058c() {
            this.f1855d = -1;
            this.f1858g = -1;
        }

        /* renamed from: d */
        public final void m1059d() {
            this.f1861j &= -33;
        }

        /* renamed from: e */
        public final int m1060e() {
            RecyclerView recyclerView = this.f1869r;
            if (recyclerView == null) {
                return -1;
            }
            return recyclerView.m1002H(this);
        }

        /* renamed from: f */
        public final int m1061f() {
            int i6 = this.f1858g;
            return i6 == -1 ? this.f1854c : i6;
        }

        /* JADX WARN: Type inference failed for: r0v3, types: [java.util.ArrayList, java.util.List<java.lang.Object>] */
        /* renamed from: g */
        public final List<Object> m1062g() {
            if ((this.f1861j & 1024) != 0) {
                return f1851s;
            }
            ?? r0 = this.f1862k;
            return (r0 == 0 || r0.size() == 0) ? f1851s : this.f1863l;
        }

        /* renamed from: h */
        public final boolean m1063h(int i6) {
            return (i6 & this.f1861j) != 0;
        }

        /* renamed from: i */
        public final boolean m1064i() {
            return (this.f1852a.getParent() == null || this.f1852a.getParent() == this.f1869r) ? false : true;
        }

        /* renamed from: j */
        public final boolean m1065j() {
            return (this.f1861j & 1) != 0;
        }

        /* renamed from: k */
        public final boolean m1066k() {
            return (this.f1861j & 4) != 0;
        }

        /* renamed from: l */
        public final boolean m1067l() {
            if ((this.f1861j & 16) == 0) {
                View view = this.f1852a;
                WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
                if (!view.hasTransientState()) {
                    return true;
                }
            }
            return false;
        }

        /* renamed from: m */
        public final boolean m1068m() {
            return (this.f1861j & 8) != 0;
        }

        /* renamed from: n */
        public final boolean m1069n() {
            return this.f1865n != null;
        }

        /* renamed from: o */
        public final boolean m1070o() {
            return (this.f1861j & 256) != 0;
        }

        /* renamed from: p */
        public final boolean m1071p() {
            return (this.f1861j & 2) != 0;
        }

        /* renamed from: q */
        public final void m1072q(int i6, boolean z5) {
            if (this.f1855d == -1) {
                this.f1855d = this.f1854c;
            }
            if (this.f1858g == -1) {
                this.f1858g = this.f1854c;
            }
            if (z5) {
                this.f1858g += i6;
            }
            this.f1854c += i6;
            if (this.f1852a.getLayoutParams() != null) {
                ((C0289n) this.f1852a.getLayoutParams()).f1907c = true;
            }
        }

        /* JADX WARN: Type inference failed for: r2v2, types: [java.util.ArrayList, java.util.List<java.lang.Object>] */
        /* renamed from: r */
        public final void m1073r() {
            this.f1861j = 0;
            this.f1854c = -1;
            this.f1855d = -1;
            this.f1856e = -1L;
            this.f1858g = -1;
            this.f1864m = 0;
            this.f1859h = null;
            this.f1860i = null;
            ?? r22 = this.f1862k;
            if (r22 != 0) {
                r22.clear();
            }
            this.f1861j &= -1025;
            this.f1867p = 0;
            this.f1868q = -1;
            RecyclerView.m995k(this);
        }

        /* renamed from: s */
        public final void m1074s(int i6, int i7) {
            this.f1861j = (i6 & i7) | (this.f1861j & (~i7));
        }

        /* renamed from: t */
        public final void m1075t(boolean z5) {
            int i6;
            int i7 = this.f1864m;
            int i8 = z5 ? i7 - 1 : i7 + 1;
            this.f1864m = i8;
            if (i8 < 0) {
                this.f1864m = 0;
                Log.e("View", "isRecyclable decremented below 0: unmatched pair of setIsRecyable() calls for " + this);
                return;
            }
            if (!z5 && i8 == 1) {
                i6 = this.f1861j | 16;
            } else if (!z5 || i8 != 0) {
                return;
            } else {
                i6 = this.f1861j & (-17);
            }
            this.f1861j = i6;
        }

        public final String toString() {
            StringBuilder m492j = C0174y.m492j(getClass().isAnonymousClass() ? "ViewHolder" : getClass().getSimpleName(), "{");
            m492j.append(Integer.toHexString(hashCode()));
            m492j.append(" position=");
            m492j.append(this.f1854c);
            m492j.append(" id=");
            m492j.append(this.f1856e);
            m492j.append(", oldPos=");
            m492j.append(this.f1855d);
            m492j.append(", pLpos:");
            m492j.append(this.f1858g);
            StringBuilder sb = new StringBuilder(m492j.toString());
            if (m1069n()) {
                sb.append(" scrap ");
                sb.append(this.f1866o ? "[changeScrap]" : "[attachedScrap]");
            }
            if (m1066k()) {
                sb.append(" invalid");
            }
            if (!m1065j()) {
                sb.append(" unbound");
            }
            boolean z5 = true;
            if ((this.f1861j & 2) != 0) {
                sb.append(" update");
            }
            if (m1068m()) {
                sb.append(" removed");
            }
            if (m1076u()) {
                sb.append(" ignored");
            }
            if (m1070o()) {
                sb.append(" tmpDetached");
            }
            if (!m1067l()) {
                StringBuilder m104h = C0052a.m104h(" not recyclable(");
                m104h.append(this.f1864m);
                m104h.append(")");
                sb.append(m104h.toString());
            }
            if ((this.f1861j & 512) == 0 && !m1066k()) {
                z5 = false;
            }
            if (z5) {
                sb.append(" undefined adapter position");
            }
            if (this.f1852a.getParent() == null) {
                sb.append(" no parent");
            }
            sb.append("}");
            return sb.toString();
        }

        /* renamed from: u */
        public final boolean m1076u() {
            return (this.f1861j & 128) != 0;
        }

        /* renamed from: v */
        public final void m1077v() {
            this.f1865n.m1186k(this);
        }

        /* renamed from: w */
        public final boolean m1078w() {
            return (this.f1861j & 32) != 0;
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$c */
    public static class InterpolatorC0278c implements Interpolator {
        @Override // android.animation.TimeInterpolator
        public final float getInterpolation(float f6) {
            float f7 = f6 - 1.0f;
            return (f7 * f7 * f7 * f7 * f7) + 1.0f;
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$d */
    public class C0279d {
        public C0279d() {
        }

        /* renamed from: a */
        public final void m1079a(AbstractC0277b0 abstractC0277b0, AbstractC0285j.c cVar, AbstractC0285j.c cVar2) {
            boolean z5;
            int i6;
            int i7;
            RecyclerView recyclerView = RecyclerView.this;
            Objects.requireNonNull(recyclerView);
            abstractC0277b0.m1075t(false);
            AbstractC0336z abstractC0336z = (AbstractC0336z) recyclerView.f1796U;
            Objects.requireNonNull(abstractC0336z);
            if (cVar == null || ((i6 = cVar.f1879a) == (i7 = cVar2.f1879a) && cVar.f1880b == cVar2.f1880b)) {
                C0321k c0321k = (C0321k) abstractC0336z;
                c0321k.m1305m(abstractC0277b0);
                abstractC0277b0.f1852a.setAlpha(0.0f);
                c0321k.f2069i.add(abstractC0277b0);
                z5 = true;
            } else {
                z5 = abstractC0336z.mo1300h(abstractC0277b0, i6, cVar.f1880b, i7, cVar2.f1880b);
            }
            if (z5) {
                recyclerView.m1015V();
            }
        }

        /* renamed from: b */
        public final void m1080b(AbstractC0277b0 abstractC0277b0, AbstractC0285j.c cVar, AbstractC0285j.c cVar2) {
            boolean z5;
            RecyclerView.this.f1810k.m1186k(abstractC0277b0);
            RecyclerView recyclerView = RecyclerView.this;
            recyclerView.m1025f(abstractC0277b0);
            abstractC0277b0.m1075t(false);
            AbstractC0336z abstractC0336z = (AbstractC0336z) recyclerView.f1796U;
            Objects.requireNonNull(abstractC0336z);
            int i6 = cVar.f1879a;
            int i7 = cVar.f1880b;
            View view = abstractC0277b0.f1852a;
            int left = cVar2 == null ? view.getLeft() : cVar2.f1879a;
            int top = cVar2 == null ? view.getTop() : cVar2.f1880b;
            if (abstractC0277b0.m1068m() || (i6 == left && i7 == top)) {
                C0321k c0321k = (C0321k) abstractC0336z;
                c0321k.m1305m(abstractC0277b0);
                c0321k.f2068h.add(abstractC0277b0);
                z5 = true;
            } else {
                view.layout(left, top, view.getWidth() + left, view.getHeight() + top);
                z5 = abstractC0336z.mo1300h(abstractC0277b0, i6, i7, left, top);
            }
            if (z5) {
                recyclerView.m1015V();
            }
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$e */
    public static abstract class AbstractC0280e<VH extends AbstractC0277b0> {

        /* renamed from: a */
        public final C0281f f1871a = new C0281f();

        /* renamed from: b */
        public boolean f1872b = false;

        /* renamed from: c */
        public abstract int mo1081c();

        /* renamed from: d */
        public long mo1082d(int i6) {
            return -1L;
        }

        /* renamed from: e */
        public final void m1083e() {
            this.f1871a.m1094b();
        }

        /* renamed from: f */
        public void mo1084f(RecyclerView recyclerView) {
        }

        /* renamed from: g */
        public abstract void mo1085g(VH vh, int i6);

        /* renamed from: h */
        public abstract AbstractC0277b0 mo1086h(ViewGroup viewGroup);

        /* renamed from: i */
        public void mo1087i(RecyclerView recyclerView) {
        }

        /* renamed from: j */
        public boolean mo1088j(VH vh) {
            return false;
        }

        /* renamed from: k */
        public void mo1089k(VH vh) {
        }

        /* renamed from: l */
        public void mo1090l(VH vh) {
        }

        /* renamed from: m */
        public final void m1091m(AbstractC0282g abstractC0282g) {
            this.f1871a.registerObserver(abstractC0282g);
        }

        /* renamed from: n */
        public final void m1092n(AbstractC0282g abstractC0282g) {
            this.f1871a.unregisterObserver(abstractC0282g);
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$f */
    public static class C0281f extends Observable<AbstractC0282g> {
        /* renamed from: a */
        public final boolean m1093a() {
            return !((Observable) this).mObservers.isEmpty();
        }

        /* renamed from: b */
        public final void m1094b() {
            for (int size = ((Observable) this).mObservers.size() - 1; size >= 0; size--) {
                ((AbstractC0282g) ((Observable) this).mObservers.get(size)).mo1096a();
            }
        }

        /* renamed from: c */
        public final void m1095c() {
            int size = ((Observable) this).mObservers.size();
            while (true) {
                size--;
                if (size < 0) {
                    return;
                } else {
                    ((AbstractC0282g) ((Observable) this).mObservers.get(size)).mo1098c();
                }
            }
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$g */
    public static abstract class AbstractC0282g {
        /* renamed from: a */
        public void mo1096a() {
        }

        /* renamed from: b */
        public void mo1097b() {
        }

        /* renamed from: c */
        public void mo1098c() {
            mo1097b();
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$h */
    public interface InterfaceC0283h {
        /* renamed from: a */
        int m1099a();
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$i */
    public static class C0284i {
        /* renamed from: a */
        public final EdgeEffect m1100a(RecyclerView recyclerView) {
            return new EdgeEffect(recyclerView.getContext());
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$j */
    public static abstract class AbstractC0285j {

        /* renamed from: a */
        public b f1873a = null;

        /* renamed from: b */
        public ArrayList<a> f1874b = new ArrayList<>();

        /* renamed from: c */
        public long f1875c = 120;

        /* renamed from: d */
        public long f1876d = 120;

        /* renamed from: e */
        public long f1877e = 250;

        /* renamed from: f */
        public long f1878f = 250;

        /* renamed from: androidx.recyclerview.widget.RecyclerView$j$a */
        public interface a {
            /* renamed from: a */
            void m1108a();
        }

        /* renamed from: androidx.recyclerview.widget.RecyclerView$j$b */
        public interface b {
        }

        /* renamed from: androidx.recyclerview.widget.RecyclerView$j$c */
        public static class c {

            /* renamed from: a */
            public int f1879a;

            /* renamed from: b */
            public int f1880b;

            /* renamed from: a */
            public final c m1109a(AbstractC0277b0 abstractC0277b0) {
                View view = abstractC0277b0.f1852a;
                this.f1879a = view.getLeft();
                this.f1880b = view.getTop();
                view.getRight();
                view.getBottom();
                return this;
            }
        }

        /* renamed from: b */
        public static int m1101b(AbstractC0277b0 abstractC0277b0) {
            int i6 = abstractC0277b0.f1861j & 14;
            if (abstractC0277b0.m1066k()) {
                return 4;
            }
            if ((i6 & 4) != 0) {
                return i6;
            }
            int i7 = abstractC0277b0.f1855d;
            int m1060e = abstractC0277b0.m1060e();
            return (i7 == -1 || m1060e == -1 || i7 == m1060e) ? i6 : i6 | 2048;
        }

        /* renamed from: a */
        public abstract boolean mo1102a(AbstractC0277b0 abstractC0277b0, AbstractC0277b0 abstractC0277b02, c cVar, c cVar2);

        /* renamed from: c */
        public final void m1103c(AbstractC0277b0 abstractC0277b0) {
            b bVar = this.f1873a;
            if (bVar != null) {
                C0286k c0286k = (C0286k) bVar;
                Objects.requireNonNull(c0286k);
                boolean z5 = true;
                abstractC0277b0.m1075t(true);
                if (abstractC0277b0.f1859h != null && abstractC0277b0.f1860i == null) {
                    abstractC0277b0.f1859h = null;
                }
                abstractC0277b0.f1860i = null;
                if ((abstractC0277b0.f1861j & 16) != 0) {
                    return;
                }
                RecyclerView recyclerView = RecyclerView.this;
                View view = abstractC0277b0.f1852a;
                recyclerView.m1032i0();
                C0310b c0310b = recyclerView.f1816n;
                int m1344c = ((C0332v) c0310b.f2021a).m1344c(view);
                if (m1344c == -1) {
                    c0310b.m1278l(view);
                } else if (c0310b.f2022b.m1282d(m1344c)) {
                    c0310b.f2022b.m1284f(m1344c);
                    c0310b.m1278l(view);
                    ((C0332v) c0310b.f2021a).m1345d(m1344c);
                } else {
                    z5 = false;
                }
                if (z5) {
                    AbstractC0277b0 m990K = RecyclerView.m990K(view);
                    recyclerView.f1810k.m1186k(m990K);
                    recyclerView.f1810k.m1183h(m990K);
                }
                recyclerView.m1035k0(!z5);
                if (z5 || !abstractC0277b0.m1070o()) {
                    return;
                }
                RecyclerView.this.removeDetachedView(abstractC0277b0.f1852a, false);
            }
        }

        /* renamed from: d */
        public final void m1104d() {
            int size = this.f1874b.size();
            for (int i6 = 0; i6 < size; i6++) {
                this.f1874b.get(i6).m1108a();
            }
            this.f1874b.clear();
        }

        /* renamed from: e */
        public abstract void mo1105e(AbstractC0277b0 abstractC0277b0);

        /* renamed from: f */
        public abstract void mo1106f();

        /* renamed from: g */
        public abstract boolean mo1107g();
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$k */
    public class C0286k implements AbstractC0285j.b {
        public C0286k() {
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$l */
    public static abstract class AbstractC0287l {
        /* renamed from: d */
        public void mo1110d(RecyclerView recyclerView) {
        }

        /* renamed from: e */
        public void mo1111e(Canvas canvas) {
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$m */
    public static abstract class AbstractC0288m {

        /* renamed from: a */
        public C0310b f1882a;

        /* renamed from: b */
        public RecyclerView f1883b;

        /* renamed from: c */
        public final a f1884c;

        /* renamed from: d */
        public final b f1885d;

        /* renamed from: e */
        public C0311b0 f1886e;

        /* renamed from: f */
        public C0311b0 f1887f;

        /* renamed from: g */
        public AbstractC0299x f1888g;

        /* renamed from: h */
        public boolean f1889h;

        /* renamed from: i */
        public boolean f1890i;

        /* renamed from: j */
        public boolean f1891j;

        /* renamed from: k */
        public boolean f1892k;

        /* renamed from: l */
        public int f1893l;

        /* renamed from: m */
        public boolean f1894m;

        /* renamed from: n */
        public int f1895n;

        /* renamed from: o */
        public int f1896o;

        /* renamed from: p */
        public int f1897p;

        /* renamed from: q */
        public int f1898q;

        /* renamed from: androidx.recyclerview.widget.RecyclerView$m$a */
        public class a implements C0311b0.b {
            public a() {
            }

            @Override // androidx.recyclerview.widget.C0311b0.b
            /* renamed from: a */
            public final View mo1160a(int i6) {
                return AbstractC0288m.this.m1156w(i6);
            }

            @Override // androidx.recyclerview.widget.C0311b0.b
            /* renamed from: b */
            public final int mo1161b() {
                AbstractC0288m abstractC0288m = AbstractC0288m.this;
                return abstractC0288m.f1897p - abstractC0288m.m1133L();
            }

            @Override // androidx.recyclerview.widget.C0311b0.b
            /* renamed from: c */
            public final int mo1162c() {
                return AbstractC0288m.this.m1132K();
            }

            @Override // androidx.recyclerview.widget.C0311b0.b
            /* renamed from: d */
            public final int mo1163d(View view) {
                return AbstractC0288m.this.m1121D(view) + ((ViewGroup.MarginLayoutParams) ((C0289n) view.getLayoutParams())).rightMargin;
            }

            @Override // androidx.recyclerview.widget.C0311b0.b
            /* renamed from: e */
            public final int mo1164e(View view) {
                return AbstractC0288m.this.m1119C(view) - ((ViewGroup.MarginLayoutParams) ((C0289n) view.getLayoutParams())).leftMargin;
            }
        }

        /* renamed from: androidx.recyclerview.widget.RecyclerView$m$b */
        public class b implements C0311b0.b {
            public b() {
            }

            @Override // androidx.recyclerview.widget.C0311b0.b
            /* renamed from: a */
            public final View mo1160a(int i6) {
                return AbstractC0288m.this.m1156w(i6);
            }

            @Override // androidx.recyclerview.widget.C0311b0.b
            /* renamed from: b */
            public final int mo1161b() {
                AbstractC0288m abstractC0288m = AbstractC0288m.this;
                return abstractC0288m.f1898q - abstractC0288m.m1131J();
            }

            @Override // androidx.recyclerview.widget.C0311b0.b
            /* renamed from: c */
            public final int mo1162c() {
                return AbstractC0288m.this.m1134M();
            }

            @Override // androidx.recyclerview.widget.C0311b0.b
            /* renamed from: d */
            public final int mo1163d(View view) {
                return AbstractC0288m.this.m1116A(view) + ((ViewGroup.MarginLayoutParams) ((C0289n) view.getLayoutParams())).bottomMargin;
            }

            @Override // androidx.recyclerview.widget.C0311b0.b
            /* renamed from: e */
            public final int mo1164e(View view) {
                return AbstractC0288m.this.m1123E(view) - ((ViewGroup.MarginLayoutParams) ((C0289n) view.getLayoutParams())).topMargin;
            }
        }

        /* renamed from: androidx.recyclerview.widget.RecyclerView$m$c */
        public interface c {
        }

        /* renamed from: androidx.recyclerview.widget.RecyclerView$m$d */
        public static class d {

            /* renamed from: a */
            public int f1901a;

            /* renamed from: b */
            public int f1902b;

            /* renamed from: c */
            public boolean f1903c;

            /* renamed from: d */
            public boolean f1904d;
        }

        public AbstractC0288m() {
            a aVar = new a();
            this.f1884c = aVar;
            b bVar = new b();
            this.f1885d = bVar;
            this.f1886e = new C0311b0(aVar);
            this.f1887f = new C0311b0(bVar);
            this.f1889h = false;
            this.f1890i = false;
            this.f1891j = true;
            this.f1892k = true;
        }

        /* renamed from: O */
        public static d m1112O(Context context, AttributeSet attributeSet, int i6, int i7) {
            d dVar = new d();
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C1798e.f7349w, i6, i7);
            dVar.f1901a = obtainStyledAttributes.getInt(0, 1);
            dVar.f1902b = obtainStyledAttributes.getInt(10, 1);
            dVar.f1903c = obtainStyledAttributes.getBoolean(9, false);
            dVar.f1904d = obtainStyledAttributes.getBoolean(11, false);
            obtainStyledAttributes.recycle();
            return dVar;
        }

        /* renamed from: S */
        public static boolean m1113S(int i6, int i7, int i8) {
            int mode = View.MeasureSpec.getMode(i7);
            int size = View.MeasureSpec.getSize(i7);
            if (i8 > 0 && i6 != i8) {
                return false;
            }
            if (mode == Integer.MIN_VALUE) {
                return size >= i6;
            }
            if (mode != 0) {
                return mode == 1073741824 && size == i6;
            }
            return true;
        }

        /* renamed from: h */
        public static int m1114h(int i6, int i7, int i8) {
            int mode = View.MeasureSpec.getMode(i6);
            int size = View.MeasureSpec.getSize(i6);
            return mode != Integer.MIN_VALUE ? mode != 1073741824 ? Math.max(i7, i8) : size : Math.min(size, Math.max(i7, i8));
        }

        /* JADX WARN: Code restructure failed: missing block: B:7:0x0017, code lost:
        
            if (r5 == 1073741824) goto L38;
         */
        /* renamed from: y */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct code enable 'Show inconsistent code' option in preferences
        */
        public static int m1115y(int r4, int r5, int r6, int r7, boolean r8) {
            /*
                int r4 = r4 - r6
                r6 = 0
                int r4 = java.lang.Math.max(r6, r4)
                r0 = -2
                r1 = -1
                r2 = -2147483648(0xffffffff80000000, float:-0.0)
                r3 = 1073741824(0x40000000, float:2.0)
                if (r8 == 0) goto L1a
                if (r7 < 0) goto L11
                goto L1c
            L11:
                if (r7 != r1) goto L2f
                if (r5 == r2) goto L20
                if (r5 == 0) goto L2f
                if (r5 == r3) goto L20
                goto L2f
            L1a:
                if (r7 < 0) goto L1e
            L1c:
                r5 = r3
                goto L31
            L1e:
                if (r7 != r1) goto L22
            L20:
                r7 = r4
                goto L31
            L22:
                if (r7 != r0) goto L2f
                if (r5 == r2) goto L2c
                if (r5 != r3) goto L29
                goto L2c
            L29:
                r7 = r4
                r5 = r6
                goto L31
            L2c:
                r7 = r4
                r5 = r2
                goto L31
            L2f:
                r5 = r6
                r7 = r5
            L31:
                int r4 = android.view.View.MeasureSpec.makeMeasureSpec(r7, r5)
                return r4
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.AbstractC0288m.m1115y(int, int, int, int, boolean):int");
        }

        /* renamed from: A */
        public final int m1116A(View view) {
            return view.getBottom() + ((C0289n) view.getLayoutParams()).f1906b.bottom;
        }

        /* renamed from: A0 */
        public final void m1117A0(int i6, int i7) {
            this.f1883b.setMeasuredDimension(i6, i7);
        }

        /* renamed from: B */
        public final void m1118B(View view, Rect rect) {
            int[] iArr = RecyclerView.f1767G0;
            C0289n c0289n = (C0289n) view.getLayoutParams();
            Rect rect2 = c0289n.f1906b;
            rect.set((view.getLeft() - rect2.left) - ((ViewGroup.MarginLayoutParams) c0289n).leftMargin, (view.getTop() - rect2.top) - ((ViewGroup.MarginLayoutParams) c0289n).topMargin, view.getRight() + rect2.right + ((ViewGroup.MarginLayoutParams) c0289n).rightMargin, view.getBottom() + rect2.bottom + ((ViewGroup.MarginLayoutParams) c0289n).bottomMargin);
        }

        /* renamed from: B0 */
        public void mo897B0(Rect rect, int i6, int i7) {
            m1117A0(m1114h(i6, m1133L() + m1132K() + rect.width(), m1129I()), m1114h(i7, m1131J() + m1134M() + rect.height(), m1128H()));
        }

        /* renamed from: C */
        public final int m1119C(View view) {
            return view.getLeft() - ((C0289n) view.getLayoutParams()).f1906b.left;
        }

        /* renamed from: C0 */
        public final void m1120C0(int i6, int i7) {
            int m1157x = m1157x();
            if (m1157x == 0) {
                this.f1883b.m1041o(i6, i7);
                return;
            }
            int i8 = Integer.MIN_VALUE;
            int i9 = Integer.MAX_VALUE;
            int i10 = Integer.MAX_VALUE;
            int i11 = Integer.MIN_VALUE;
            for (int i12 = 0; i12 < m1157x; i12++) {
                View m1156w = m1156w(i12);
                Rect rect = this.f1883b.f1824r;
                m1118B(m1156w, rect);
                int i13 = rect.left;
                if (i13 < i9) {
                    i9 = i13;
                }
                int i14 = rect.right;
                if (i14 > i8) {
                    i8 = i14;
                }
                int i15 = rect.top;
                if (i15 < i10) {
                    i10 = i15;
                }
                int i16 = rect.bottom;
                if (i16 > i11) {
                    i11 = i16;
                }
            }
            this.f1883b.f1824r.set(i9, i10, i8, i11);
            mo897B0(this.f1883b.f1824r, i6, i7);
        }

        /* renamed from: D */
        public final int m1121D(View view) {
            return view.getRight() + ((C0289n) view.getLayoutParams()).f1906b.right;
        }

        /* renamed from: D0 */
        public final void m1122D0(RecyclerView recyclerView) {
            int height;
            if (recyclerView == null) {
                this.f1883b = null;
                this.f1882a = null;
                height = 0;
                this.f1897p = 0;
            } else {
                this.f1883b = recyclerView;
                this.f1882a = recyclerView.f1816n;
                this.f1897p = recyclerView.getWidth();
                height = recyclerView.getHeight();
            }
            this.f1898q = height;
            this.f1895n = 1073741824;
            this.f1896o = 1073741824;
        }

        /* renamed from: E */
        public final int m1123E(View view) {
            return view.getTop() - ((C0289n) view.getLayoutParams()).f1906b.top;
        }

        /* renamed from: E0 */
        public final boolean m1124E0(View view, int i6, int i7, C0289n c0289n) {
            return (!view.isLayoutRequested() && this.f1891j && m1113S(view.getWidth(), i6, ((ViewGroup.MarginLayoutParams) c0289n).width) && m1113S(view.getHeight(), i7, ((ViewGroup.MarginLayoutParams) c0289n).height)) ? false : true;
        }

        /* renamed from: F */
        public final View m1125F() {
            View focusedChild;
            RecyclerView recyclerView = this.f1883b;
            if (recyclerView == null || (focusedChild = recyclerView.getFocusedChild()) == null || this.f1882a.m1277k(focusedChild)) {
                return null;
            }
            return focusedChild;
        }

        /* renamed from: F0 */
        public boolean mo938F0() {
            return false;
        }

        /* renamed from: G */
        public final int m1126G() {
            RecyclerView recyclerView = this.f1883b;
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            return recyclerView.getLayoutDirection();
        }

        /* renamed from: G0 */
        public final boolean m1127G0(View view, int i6, int i7, C0289n c0289n) {
            return (this.f1891j && m1113S(view.getMeasuredWidth(), i6, ((ViewGroup.MarginLayoutParams) c0289n).width) && m1113S(view.getMeasuredHeight(), i7, ((ViewGroup.MarginLayoutParams) c0289n).height)) ? false : true;
        }

        /* renamed from: H */
        public final int m1128H() {
            RecyclerView recyclerView = this.f1883b;
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            return recyclerView.getMinimumHeight();
        }

        /* renamed from: H0 */
        public void mo939H0(RecyclerView recyclerView, int i6) {
            Log.e("RecyclerView", "You must override smoothScrollToPosition to support smooth scrolling");
        }

        /* renamed from: I */
        public final int m1129I() {
            RecyclerView recyclerView = this.f1883b;
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            return recyclerView.getMinimumWidth();
        }

        /* renamed from: I0 */
        public final void m1130I0(AbstractC0299x abstractC0299x) {
            AbstractC0299x abstractC0299x2 = this.f1888g;
            if (abstractC0299x2 != null && abstractC0299x != abstractC0299x2 && abstractC0299x2.f1929e) {
                abstractC0299x2.m1192d();
            }
            this.f1888g = abstractC0299x;
            RecyclerView recyclerView = this.f1883b;
            recyclerView.f1815m0.m1055c();
            if (abstractC0299x.f1932h) {
                StringBuilder m104h = C0052a.m104h("An instance of ");
                m104h.append(abstractC0299x.getClass().getSimpleName());
                m104h.append(" was started more than once. Each instance of");
                m104h.append(abstractC0299x.getClass().getSimpleName());
                m104h.append(" is intended to only be used once. You should create a new instance for each use.");
                Log.w("RecyclerView", m104h.toString());
            }
            abstractC0299x.f1926b = recyclerView;
            abstractC0299x.f1927c = this;
            int i6 = abstractC0299x.f1925a;
            if (i6 == -1) {
                throw new IllegalArgumentException("Invalid target position");
            }
            recyclerView.f1821p0.f1940a = i6;
            abstractC0299x.f1929e = true;
            abstractC0299x.f1928d = true;
            abstractC0299x.f1930f = recyclerView.f1832v.mo979s(i6);
            abstractC0299x.f1926b.f1815m0.m1053a();
            abstractC0299x.f1932h = true;
        }

        /* renamed from: J */
        public final int m1131J() {
            RecyclerView recyclerView = this.f1883b;
            if (recyclerView != null) {
                return recyclerView.getPaddingBottom();
            }
            return 0;
        }

        /* renamed from: J0 */
        public boolean mo898J0() {
            return false;
        }

        /* renamed from: K */
        public final int m1132K() {
            RecyclerView recyclerView = this.f1883b;
            if (recyclerView != null) {
                return recyclerView.getPaddingLeft();
            }
            return 0;
        }

        /* renamed from: L */
        public final int m1133L() {
            RecyclerView recyclerView = this.f1883b;
            if (recyclerView != null) {
                return recyclerView.getPaddingRight();
            }
            return 0;
        }

        /* renamed from: M */
        public final int m1134M() {
            RecyclerView recyclerView = this.f1883b;
            if (recyclerView != null) {
                return recyclerView.getPaddingTop();
            }
            return 0;
        }

        /* renamed from: N */
        public final int m1135N(View view) {
            return ((C0289n) view.getLayoutParams()).m1165a();
        }

        /* renamed from: P */
        public int mo900P(C0295t c0295t, C0300y c0300y) {
            RecyclerView recyclerView = this.f1883b;
            if (recyclerView == null || recyclerView.f1830u == null || !mo964f()) {
                return 1;
            }
            return this.f1883b.f1830u.mo1081c();
        }

        /* renamed from: Q */
        public final void m1136Q(View view, Rect rect) {
            Matrix matrix;
            Rect rect2 = ((C0289n) view.getLayoutParams()).f1906b;
            rect.set(-rect2.left, -rect2.top, view.getWidth() + rect2.right, view.getHeight() + rect2.bottom);
            if (this.f1883b != null && (matrix = view.getMatrix()) != null && !matrix.isIdentity()) {
                RectF rectF = this.f1883b.f1828t;
                rectF.set(rect);
                matrix.mapRect(rectF);
                rect.set((int) Math.floor(rectF.left), (int) Math.floor(rectF.top), (int) Math.ceil(rectF.right), (int) Math.ceil(rectF.bottom));
            }
            rect.offset(view.getLeft(), view.getTop());
        }

        /* renamed from: R */
        public boolean mo946R() {
            return false;
        }

        /* renamed from: T */
        public final void m1137T(View view, int i6, int i7, int i8, int i9) {
            C0289n c0289n = (C0289n) view.getLayoutParams();
            Rect rect = c0289n.f1906b;
            view.layout(i6 + rect.left + ((ViewGroup.MarginLayoutParams) c0289n).leftMargin, i7 + rect.top + ((ViewGroup.MarginLayoutParams) c0289n).topMargin, (i8 - rect.right) - ((ViewGroup.MarginLayoutParams) c0289n).rightMargin, (i9 - rect.bottom) - ((ViewGroup.MarginLayoutParams) c0289n).bottomMargin);
        }

        /* renamed from: U */
        public void mo1138U(int i6) {
            RecyclerView recyclerView = this.f1883b;
            if (recyclerView != null) {
                int m1271e = recyclerView.f1816n.m1271e();
                for (int i7 = 0; i7 < m1271e; i7++) {
                    recyclerView.f1816n.m1270d(i7).offsetLeftAndRight(i6);
                }
            }
        }

        /* renamed from: V */
        public void mo1139V(int i6) {
            RecyclerView recyclerView = this.f1883b;
            if (recyclerView != null) {
                int m1271e = recyclerView.f1816n.m1271e();
                for (int i7 = 0; i7 < m1271e; i7++) {
                    recyclerView.f1816n.m1270d(i7).offsetTopAndBottom(i6);
                }
            }
        }

        /* renamed from: W */
        public void mo952W(RecyclerView recyclerView) {
        }

        /* renamed from: X */
        public View mo901X(View view, int i6, C0295t c0295t, C0300y c0300y) {
            return null;
        }

        /* renamed from: Y */
        public void mo955Y(AccessibilityEvent accessibilityEvent) {
            RecyclerView recyclerView = this.f1883b;
            C0295t c0295t = recyclerView.f1810k;
            C0300y c0300y = recyclerView.f1821p0;
            if (recyclerView == null || accessibilityEvent == null) {
                return;
            }
            boolean z5 = true;
            if (!recyclerView.canScrollVertically(1) && !this.f1883b.canScrollVertically(-1) && !this.f1883b.canScrollHorizontally(-1) && !this.f1883b.canScrollHorizontally(1)) {
                z5 = false;
            }
            accessibilityEvent.setScrollable(z5);
            AbstractC0280e abstractC0280e = this.f1883b.f1830u;
            if (abstractC0280e != null) {
                accessibilityEvent.setItemCount(abstractC0280e.mo1081c());
            }
        }

        /* renamed from: Z */
        public void mo1140Z(C0295t c0295t, C0300y c0300y, C0831b c0831b) {
            if (this.f1883b.canScrollVertically(-1) || this.f1883b.canScrollHorizontally(-1)) {
                c0831b.m2314a(8192);
                c0831b.m2333v(true);
            }
            if (this.f1883b.canScrollVertically(1) || this.f1883b.canScrollHorizontally(1)) {
                c0831b.m2314a(4096);
                c0831b.m2333v(true);
            }
            c0831b.m2326o(C0831b.b.m2339a(mo900P(c0295t, c0300y), mo934z(c0295t, c0300y), 0));
        }

        /* renamed from: a0 */
        public final void m1141a0(View view, C0831b c0831b) {
            AbstractC0277b0 m990K = RecyclerView.m990K(view);
            if (m990K == null || m990K.m1068m() || this.f1882a.m1277k(m990K.f1852a)) {
                return;
            }
            RecyclerView recyclerView = this.f1883b;
            mo903b0(recyclerView.f1810k, recyclerView.f1821p0, view, c0831b);
        }

        /* renamed from: b */
        public final void m1142b(View view) {
            m1143c(view, -1, false);
        }

        /* renamed from: b0 */
        public void mo903b0(C0295t c0295t, C0300y c0300y, View view, C0831b c0831b) {
            c0831b.m2327p(C0831b.c.m2340a(mo964f() ? m1135N(view) : 0, 1, mo963e() ? m1135N(view) : 0, 1, false));
        }

        /* renamed from: c */
        public final void m1143c(View view, int i6, boolean z5) {
            AbstractC0277b0 m990K = RecyclerView.m990K(view);
            if (z5 || m990K.m1068m()) {
                this.f1883b.f1818o.m1292a(m990K);
            } else {
                this.f1883b.f1818o.m1296e(m990K);
            }
            C0289n c0289n = (C0289n) view.getLayoutParams();
            if (m990K.m1078w() || m990K.m1069n()) {
                if (m990K.m1069n()) {
                    m990K.m1077v();
                } else {
                    m990K.m1059d();
                }
                this.f1882a.m1268b(view, i6, view.getLayoutParams(), false);
            } else {
                if (view.getParent() == this.f1883b) {
                    int m1276j = this.f1882a.m1276j(view);
                    if (i6 == -1) {
                        i6 = this.f1882a.m1271e();
                    }
                    if (m1276j == -1) {
                        StringBuilder m104h = C0052a.m104h("Added View has RecyclerView as parent but view is not a real child. Unfiltered index:");
                        m104h.append(this.f1883b.indexOfChild(view));
                        throw new IllegalStateException(C0174y.m489g(this.f1883b, m104h));
                    }
                    if (m1276j != i6) {
                        AbstractC0288m abstractC0288m = this.f1883b.f1832v;
                        View m1156w = abstractC0288m.m1156w(m1276j);
                        if (m1156w == null) {
                            throw new IllegalArgumentException("Cannot move a child from non-existing index:" + m1276j + abstractC0288m.f1883b.toString());
                        }
                        abstractC0288m.m1156w(m1276j);
                        abstractC0288m.f1882a.m1269c(m1276j);
                        C0289n c0289n2 = (C0289n) m1156w.getLayoutParams();
                        AbstractC0277b0 m990K2 = RecyclerView.m990K(m1156w);
                        if (m990K2.m1068m()) {
                            abstractC0288m.f1883b.f1818o.m1292a(m990K2);
                        } else {
                            abstractC0288m.f1883b.f1818o.m1296e(m990K2);
                        }
                        abstractC0288m.f1882a.m1268b(m1156w, i6, c0289n2, m990K2.m1068m());
                    }
                } else {
                    this.f1882a.m1267a(view, i6, false);
                    c0289n.f1907c = true;
                    AbstractC0299x abstractC0299x = this.f1888g;
                    if (abstractC0299x != null && abstractC0299x.f1929e) {
                        Objects.requireNonNull(abstractC0299x.f1926b);
                        AbstractC0277b0 m990K3 = RecyclerView.m990K(view);
                        if ((m990K3 != null ? m990K3.m1061f() : -1) == abstractC0299x.f1925a) {
                            abstractC0299x.f1930f = view;
                        }
                    }
                }
            }
            if (c0289n.f1908d) {
                m990K.f1852a.invalidate();
                c0289n.f1908d = false;
            }
        }

        /* renamed from: c0 */
        public void mo904c0(int i6, int i7) {
        }

        /* renamed from: d */
        public void mo961d(String str) {
            RecyclerView recyclerView = this.f1883b;
            if (recyclerView != null) {
                recyclerView.m1031i(str);
            }
        }

        /* renamed from: d0 */
        public void mo905d0() {
        }

        /* renamed from: e */
        public boolean mo963e() {
            return false;
        }

        /* renamed from: e0 */
        public void mo906e0(int i6, int i7) {
        }

        /* renamed from: f */
        public boolean mo964f() {
            return false;
        }

        /* renamed from: f0 */
        public void mo908f0(int i6, int i7) {
        }

        /* renamed from: g */
        public boolean mo910g(C0289n c0289n) {
            return c0289n != null;
        }

        /* renamed from: g0 */
        public void mo911g0(int i6, int i7) {
        }

        /* renamed from: h0 */
        public void mo912h0(C0295t c0295t, C0300y c0300y) {
            Log.e("RecyclerView", "You must override onLayoutChildren(Recycler recycler, State state) ");
        }

        /* renamed from: i */
        public void mo967i(int i6, int i7, C0300y c0300y, c cVar) {
        }

        /* renamed from: i0 */
        public void mo913i0() {
        }

        /* renamed from: j */
        public void mo969j(int i6, c cVar) {
        }

        /* renamed from: j0 */
        public final void m1144j0(int i6, int i7) {
            this.f1883b.m1041o(i6, i7);
        }

        /* renamed from: k */
        public int mo971k(C0300y c0300y) {
            return 0;
        }

        /* renamed from: k0 */
        public void mo972k0(Parcelable parcelable) {
        }

        /* renamed from: l */
        public int mo914l(C0300y c0300y) {
            return 0;
        }

        /* renamed from: l0 */
        public Parcelable mo974l0() {
            return null;
        }

        /* renamed from: m */
        public int mo916m(C0300y c0300y) {
            return 0;
        }

        /* renamed from: m0 */
        public void mo1145m0(int i6) {
        }

        /* renamed from: n */
        public int mo976n(C0300y c0300y) {
            return 0;
        }

        /* renamed from: n0 */
        public boolean mo1146n0(C0295t c0295t, C0300y c0300y, int i6, Bundle bundle) {
            int m1134M;
            int m1132K;
            RecyclerView recyclerView = this.f1883b;
            if (recyclerView == null) {
                return false;
            }
            if (i6 == 4096) {
                m1134M = recyclerView.canScrollVertically(1) ? (this.f1898q - m1134M()) - m1131J() : 0;
                if (this.f1883b.canScrollHorizontally(1)) {
                    m1132K = (this.f1897p - m1132K()) - m1133L();
                }
                m1132K = 0;
            } else if (i6 != 8192) {
                m1134M = 0;
                m1132K = 0;
            } else {
                m1134M = recyclerView.canScrollVertically(-1) ? -((this.f1898q - m1134M()) - m1131J()) : 0;
                if (this.f1883b.canScrollHorizontally(-1)) {
                    m1132K = -((this.f1897p - m1132K()) - m1133L());
                }
                m1132K = 0;
            }
            if (m1134M == 0 && m1132K == 0) {
                return false;
            }
            this.f1883b.m1028g0(m1132K, m1134M, true);
            return true;
        }

        /* renamed from: o */
        public int mo917o(C0300y c0300y) {
            return 0;
        }

        /* renamed from: o0 */
        public final void m1147o0(C0295t c0295t) {
            int m1157x = m1157x();
            while (true) {
                m1157x--;
                if (m1157x < 0) {
                    return;
                }
                if (!RecyclerView.m990K(m1156w(m1157x)).m1076u()) {
                    m1152r0(m1157x, c0295t);
                }
            }
        }

        /* renamed from: p */
        public int mo918p(C0300y c0300y) {
            return 0;
        }

        /* renamed from: p0 */
        public final void m1148p0(C0295t c0295t) {
            int size = c0295t.f1915a.size();
            for (int i6 = size - 1; i6 >= 0; i6--) {
                View view = c0295t.f1915a.get(i6).f1852a;
                AbstractC0277b0 m990K = RecyclerView.m990K(view);
                if (!m990K.m1076u()) {
                    m990K.m1075t(false);
                    if (m990K.m1070o()) {
                        this.f1883b.removeDetachedView(view, false);
                    }
                    AbstractC0285j abstractC0285j = this.f1883b.f1796U;
                    if (abstractC0285j != null) {
                        abstractC0285j.mo1105e(m990K);
                    }
                    m990K.m1075t(true);
                    AbstractC0277b0 m990K2 = RecyclerView.m990K(view);
                    m990K2.f1865n = null;
                    m990K2.f1866o = false;
                    m990K2.m1059d();
                    c0295t.m1183h(m990K2);
                }
            }
            c0295t.f1915a.clear();
            ArrayList<AbstractC0277b0> arrayList = c0295t.f1916b;
            if (arrayList != null) {
                arrayList.clear();
            }
            if (size > 0) {
                this.f1883b.invalidate();
            }
        }

        /* renamed from: q */
        public final void m1149q(C0295t c0295t) {
            int m1157x = m1157x();
            while (true) {
                m1157x--;
                if (m1157x < 0) {
                    return;
                }
                View m1156w = m1156w(m1157x);
                AbstractC0277b0 m990K = RecyclerView.m990K(m1156w);
                if (!m990K.m1076u()) {
                    if (!m990K.m1066k() || m990K.m1068m() || this.f1883b.f1830u.f1872b) {
                        m1156w(m1157x);
                        this.f1882a.m1269c(m1157x);
                        c0295t.m1184i(m1156w);
                        this.f1883b.f1818o.m1296e(m990K);
                    } else {
                        m1153s0(m1157x);
                        c0295t.m1183h(m990K);
                    }
                }
            }
        }

        /* renamed from: q0 */
        public final void m1150q0(View view, C0295t c0295t) {
            C0310b c0310b = this.f1882a;
            int m1344c = ((C0332v) c0310b.f2021a).m1344c(view);
            if (m1344c >= 0) {
                if (c0310b.f2022b.m1284f(m1344c)) {
                    c0310b.m1278l(view);
                }
                ((C0332v) c0310b.f2021a).m1345d(m1344c);
            }
            c0295t.m1182g(view);
        }

        /* renamed from: r */
        public final View m1151r(View view) {
            View m998C;
            RecyclerView recyclerView = this.f1883b;
            if (recyclerView == null || (m998C = recyclerView.m998C(view)) == null || this.f1882a.m1277k(m998C)) {
                return null;
            }
            return m998C;
        }

        /* renamed from: r0 */
        public final void m1152r0(int i6, C0295t c0295t) {
            View m1156w = m1156w(i6);
            m1153s0(i6);
            c0295t.m1182g(m1156w);
        }

        /* renamed from: s */
        public View mo979s(int i6) {
            int m1157x = m1157x();
            for (int i7 = 0; i7 < m1157x; i7++) {
                View m1156w = m1156w(i7);
                AbstractC0277b0 m990K = RecyclerView.m990K(m1156w);
                if (m990K != null && m990K.m1061f() == i6 && !m990K.m1076u() && (this.f1883b.f1821p0.f1946g || !m990K.m1068m())) {
                    return m1156w;
                }
            }
            return null;
        }

        /* renamed from: s0 */
        public final void m1153s0(int i6) {
            C0310b c0310b;
            int m1272f;
            View m1342a;
            if (m1156w(i6) == null || (m1342a = ((C0332v) c0310b.f2021a).m1342a((m1272f = (c0310b = this.f1882a).m1272f(i6)))) == null) {
                return;
            }
            if (c0310b.f2022b.m1284f(m1272f)) {
                c0310b.m1278l(m1342a);
            }
            ((C0332v) c0310b.f2021a).m1345d(m1272f);
        }

        /* renamed from: t */
        public abstract C0289n mo923t();

        /* JADX WARN: Code restructure failed: missing block: B:20:0x00b4, code lost:
        
            if (r14 == false) goto L73;
         */
        /* renamed from: t0 */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct code enable 'Show inconsistent code' option in preferences
        */
        public boolean mo1154t0(androidx.recyclerview.widget.RecyclerView r10, android.view.View r11, android.graphics.Rect r12, boolean r13, boolean r14) {
            /*
                r9 = this;
                r0 = 2
                int[] r0 = new int[r0]
                int r1 = r9.m1132K()
                int r2 = r9.m1134M()
                int r3 = r9.f1897p
                int r4 = r9.m1133L()
                int r3 = r3 - r4
                int r4 = r9.f1898q
                int r5 = r9.m1131J()
                int r4 = r4 - r5
                int r5 = r11.getLeft()
                int r6 = r12.left
                int r5 = r5 + r6
                int r6 = r11.getScrollX()
                int r5 = r5 - r6
                int r6 = r11.getTop()
                int r7 = r12.top
                int r6 = r6 + r7
                int r11 = r11.getScrollY()
                int r6 = r6 - r11
                int r11 = r12.width()
                int r11 = r11 + r5
                int r12 = r12.height()
                int r12 = r12 + r6
                int r5 = r5 - r1
                r1 = 0
                int r7 = java.lang.Math.min(r1, r5)
                int r6 = r6 - r2
                int r2 = java.lang.Math.min(r1, r6)
                int r11 = r11 - r3
                int r3 = java.lang.Math.max(r1, r11)
                int r12 = r12 - r4
                int r12 = java.lang.Math.max(r1, r12)
                int r4 = r9.m1126G()
                r8 = 1
                if (r4 != r8) goto L5f
                if (r3 == 0) goto L5a
                goto L67
            L5a:
                int r3 = java.lang.Math.max(r7, r11)
                goto L67
            L5f:
                if (r7 == 0) goto L62
                goto L66
            L62:
                int r7 = java.lang.Math.min(r5, r3)
            L66:
                r3 = r7
            L67:
                if (r2 == 0) goto L6a
                goto L6e
            L6a:
                int r2 = java.lang.Math.min(r6, r12)
            L6e:
                r0[r1] = r3
                r0[r8] = r2
                r11 = r0[r1]
                r12 = r0[r8]
                if (r14 == 0) goto Lb6
                android.view.View r14 = r10.getFocusedChild()
                if (r14 != 0) goto L7f
                goto Lb3
            L7f:
                int r0 = r9.m1132K()
                int r2 = r9.m1134M()
                int r3 = r9.f1897p
                int r4 = r9.m1133L()
                int r3 = r3 - r4
                int r4 = r9.f1898q
                int r5 = r9.m1131J()
                int r4 = r4 - r5
                androidx.recyclerview.widget.RecyclerView r5 = r9.f1883b
                android.graphics.Rect r5 = r5.f1824r
                r9.m1118B(r14, r5)
                int r14 = r5.left
                int r14 = r14 - r11
                if (r14 >= r3) goto Lb3
                int r14 = r5.right
                int r14 = r14 - r11
                if (r14 <= r0) goto Lb3
                int r14 = r5.top
                int r14 = r14 - r12
                if (r14 >= r4) goto Lb3
                int r14 = r5.bottom
                int r14 = r14 - r12
                if (r14 > r2) goto Lb1
                goto Lb3
            Lb1:
                r14 = r8
                goto Lb4
            Lb3:
                r14 = r1
            Lb4:
                if (r14 == 0) goto Lbb
            Lb6:
                if (r11 != 0) goto Lbc
                if (r12 == 0) goto Lbb
                goto Lbc
            Lbb:
                return r1
            Lbc:
                if (r13 == 0) goto Lc2
                r10.scrollBy(r11, r12)
                goto Lc5
            Lc2:
                r10.m1028g0(r11, r12, r1)
            Lc5:
                return r8
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.AbstractC0288m.mo1154t0(androidx.recyclerview.widget.RecyclerView, android.view.View, android.graphics.Rect, boolean, boolean):boolean");
        }

        /* renamed from: u */
        public C0289n mo925u(Context context, AttributeSet attributeSet) {
            return new C0289n(context, attributeSet);
        }

        /* renamed from: u0 */
        public final void m1155u0() {
            RecyclerView recyclerView = this.f1883b;
            if (recyclerView != null) {
                recyclerView.requestLayout();
            }
        }

        /* renamed from: v */
        public C0289n mo927v(ViewGroup.LayoutParams layoutParams) {
            return layoutParams instanceof C0289n ? new C0289n((C0289n) layoutParams) : layoutParams instanceof ViewGroup.MarginLayoutParams ? new C0289n((ViewGroup.MarginLayoutParams) layoutParams) : new C0289n(layoutParams);
        }

        /* renamed from: v0 */
        public int mo928v0(int i6, C0295t c0295t, C0300y c0300y) {
            return 0;
        }

        /* renamed from: w */
        public final View m1156w(int i6) {
            C0310b c0310b = this.f1882a;
            if (c0310b != null) {
                return c0310b.m1270d(i6);
            }
            return null;
        }

        /* renamed from: w0 */
        public void mo980w0(int i6) {
        }

        /* renamed from: x */
        public final int m1157x() {
            C0310b c0310b = this.f1882a;
            if (c0310b != null) {
                return c0310b.m1271e();
            }
            return 0;
        }

        /* renamed from: x0 */
        public int mo931x0(int i6, C0295t c0295t, C0300y c0300y) {
            return 0;
        }

        /* renamed from: y0 */
        public final void m1158y0(RecyclerView recyclerView) {
            m1159z0(View.MeasureSpec.makeMeasureSpec(recyclerView.getWidth(), 1073741824), View.MeasureSpec.makeMeasureSpec(recyclerView.getHeight(), 1073741824));
        }

        /* renamed from: z */
        public int mo934z(C0295t c0295t, C0300y c0300y) {
            RecyclerView recyclerView = this.f1883b;
            if (recyclerView == null || recyclerView.f1830u == null || !mo963e()) {
                return 1;
            }
            return this.f1883b.f1830u.mo1081c();
        }

        /* renamed from: z0 */
        public final void m1159z0(int i6, int i7) {
            this.f1897p = View.MeasureSpec.getSize(i6);
            int mode = View.MeasureSpec.getMode(i6);
            this.f1895n = mode;
            if (mode == 0) {
                int[] iArr = RecyclerView.f1767G0;
            }
            this.f1898q = View.MeasureSpec.getSize(i7);
            int mode2 = View.MeasureSpec.getMode(i7);
            this.f1896o = mode2;
            if (mode2 == 0) {
                int[] iArr2 = RecyclerView.f1767G0;
            }
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$n */
    public static class C0289n extends ViewGroup.MarginLayoutParams {

        /* renamed from: a */
        public AbstractC0277b0 f1905a;

        /* renamed from: b */
        public final Rect f1906b;

        /* renamed from: c */
        public boolean f1907c;

        /* renamed from: d */
        public boolean f1908d;

        public C0289n(int i6, int i7) {
            super(i6, i7);
            this.f1906b = new Rect();
            this.f1907c = true;
            this.f1908d = false;
        }

        public C0289n(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
            this.f1906b = new Rect();
            this.f1907c = true;
            this.f1908d = false;
        }

        public C0289n(ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
            this.f1906b = new Rect();
            this.f1907c = true;
            this.f1908d = false;
        }

        public C0289n(ViewGroup.MarginLayoutParams marginLayoutParams) {
            super(marginLayoutParams);
            this.f1906b = new Rect();
            this.f1907c = true;
            this.f1908d = false;
        }

        public C0289n(C0289n c0289n) {
            super((ViewGroup.LayoutParams) c0289n);
            this.f1906b = new Rect();
            this.f1907c = true;
            this.f1908d = false;
        }

        /* renamed from: a */
        public final int m1165a() {
            return this.f1905a.m1061f();
        }

        /* renamed from: b */
        public final boolean m1166b() {
            return this.f1905a.m1071p();
        }

        /* renamed from: c */
        public final boolean m1167c() {
            return this.f1905a.m1068m();
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$o */
    public interface InterfaceC0290o {
        /* renamed from: a */
        void mo1168a(View view);

        /* renamed from: b */
        void mo1169b();
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$p */
    public static abstract class AbstractC0291p {
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$q */
    public interface InterfaceC0292q {
        /* renamed from: a */
        boolean mo1170a(MotionEvent motionEvent);

        /* renamed from: b */
        void mo1171b();

        /* renamed from: c */
        void mo1172c(MotionEvent motionEvent);
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$r */
    public static abstract class AbstractC0293r {
        /* renamed from: a */
        public void mo1173a(RecyclerView recyclerView, int i6) {
        }

        /* renamed from: b */
        public void mo1174b(RecyclerView recyclerView, int i6, int i7) {
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$s */
    public static class C0294s {

        /* renamed from: a */
        public SparseArray<a> f1909a = new SparseArray<>();

        /* renamed from: b */
        public int f1910b = 0;

        /* renamed from: androidx.recyclerview.widget.RecyclerView$s$a */
        public static class a {

            /* renamed from: a */
            public final ArrayList<AbstractC0277b0> f1911a = new ArrayList<>();

            /* renamed from: b */
            public int f1912b = 5;

            /* renamed from: c */
            public long f1913c = 0;

            /* renamed from: d */
            public long f1914d = 0;
        }

        /* renamed from: a */
        public final a m1175a(int i6) {
            a aVar = this.f1909a.get(i6);
            if (aVar != null) {
                return aVar;
            }
            a aVar2 = new a();
            this.f1909a.put(i6, aVar2);
            return aVar2;
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$t */
    public final class C0295t {

        /* renamed from: a */
        public final ArrayList<AbstractC0277b0> f1915a;

        /* renamed from: b */
        public ArrayList<AbstractC0277b0> f1916b;

        /* renamed from: c */
        public final ArrayList<AbstractC0277b0> f1917c;

        /* renamed from: d */
        public final List<AbstractC0277b0> f1918d;

        /* renamed from: e */
        public int f1919e;

        /* renamed from: f */
        public int f1920f;

        /* renamed from: g */
        public C0294s f1921g;

        public C0295t() {
            ArrayList<AbstractC0277b0> arrayList = new ArrayList<>();
            this.f1915a = arrayList;
            this.f1916b = null;
            this.f1917c = new ArrayList<>();
            this.f1918d = Collections.unmodifiableList(arrayList);
            this.f1919e = 2;
            this.f1920f = 2;
        }

        /* JADX WARN: Type inference failed for: r1v7, types: [java.util.Map<android.view.View, e0.a>, java.util.WeakHashMap] */
        /* renamed from: a */
        public final void m1176a(AbstractC0277b0 abstractC0277b0, boolean z5) {
            RecyclerView.m995k(abstractC0277b0);
            View view = abstractC0277b0.f1852a;
            C0334x c0334x = RecyclerView.this.f1835w0;
            if (c0334x != null) {
                C0334x.a aVar = c0334x.f2168e;
                C0766p.m2187t(view, aVar instanceof C0334x.a ? (C0751a) aVar.f2170e.remove(view) : null);
            }
            if (z5) {
                InterfaceC0296u interfaceC0296u = RecyclerView.this.f1834w;
                if (interfaceC0296u != null) {
                    interfaceC0296u.m1188a();
                }
                AbstractC0280e abstractC0280e = RecyclerView.this.f1830u;
                if (abstractC0280e != null) {
                    abstractC0280e.mo1090l(abstractC0277b0);
                }
                RecyclerView recyclerView = RecyclerView.this;
                if (recyclerView.f1821p0 != null) {
                    recyclerView.f1818o.m1297f(abstractC0277b0);
                }
            }
            abstractC0277b0.f1869r = null;
            C0294s m1179d = m1179d();
            Objects.requireNonNull(m1179d);
            int i6 = abstractC0277b0.f1857f;
            ArrayList<AbstractC0277b0> arrayList = m1179d.m1175a(i6).f1911a;
            if (m1179d.f1909a.get(i6).f1912b <= arrayList.size()) {
                return;
            }
            abstractC0277b0.m1073r();
            arrayList.add(abstractC0277b0);
        }

        /* renamed from: b */
        public final void m1177b() {
            this.f1915a.clear();
            m1180e();
        }

        /* renamed from: c */
        public final int m1178c(int i6) {
            if (i6 >= 0 && i6 < RecyclerView.this.f1821p0.m1196b()) {
                RecyclerView recyclerView = RecyclerView.this;
                return !recyclerView.f1821p0.f1946g ? i6 : recyclerView.f1814m.m1255f(i6, 0);
            }
            StringBuilder sb = new StringBuilder();
            sb.append("invalid position ");
            sb.append(i6);
            sb.append(". State item count is ");
            sb.append(RecyclerView.this.f1821p0.m1196b());
            throw new IndexOutOfBoundsException(C0174y.m489g(RecyclerView.this, sb));
        }

        /* renamed from: d */
        public final C0294s m1179d() {
            if (this.f1921g == null) {
                this.f1921g = new C0294s();
            }
            return this.f1921g;
        }

        /* renamed from: e */
        public final void m1180e() {
            for (int size = this.f1917c.size() - 1; size >= 0; size--) {
                m1181f(size);
            }
            this.f1917c.clear();
            int[] iArr = RecyclerView.f1767G0;
            RunnableC0323m.b bVar = RecyclerView.this.f1819o0;
            int[] iArr2 = bVar.f2134c;
            if (iArr2 != null) {
                Arrays.fill(iArr2, -1);
            }
            bVar.f2135d = 0;
        }

        /* renamed from: f */
        public final void m1181f(int i6) {
            m1176a(this.f1917c.get(i6), true);
            this.f1917c.remove(i6);
        }

        /* renamed from: g */
        public final void m1182g(View view) {
            AbstractC0277b0 m990K = RecyclerView.m990K(view);
            if (m990K.m1070o()) {
                RecyclerView.this.removeDetachedView(view, false);
            }
            if (m990K.m1069n()) {
                m990K.m1077v();
            } else if (m990K.m1078w()) {
                m990K.m1059d();
            }
            m1183h(m990K);
            if (RecyclerView.this.f1796U == null || m990K.m1067l()) {
                return;
            }
            RecyclerView.this.f1796U.mo1105e(m990K);
        }

        /* JADX WARN: Removed duplicated region for block: B:56:0x009d  */
        /* renamed from: h */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct code enable 'Show inconsistent code' option in preferences
        */
        public final void m1183h(androidx.recyclerview.widget.RecyclerView.AbstractC0277b0 r7) {
            /*
                Method dump skipped, instructions count: 265
                To view this dump change 'Code comments level' option to 'DEBUG'
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.C0295t.m1183h(androidx.recyclerview.widget.RecyclerView$b0):void");
        }

        /* JADX WARN: Removed duplicated region for block: B:17:0x003d  */
        /* JADX WARN: Removed duplicated region for block: B:19:0x0043  */
        /* renamed from: i */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct code enable 'Show inconsistent code' option in preferences
        */
        public final void m1184i(android.view.View r5) {
            /*
                r4 = this;
                androidx.recyclerview.widget.RecyclerView$b0 r5 = androidx.recyclerview.widget.RecyclerView.m990K(r5)
                r0 = 12
                boolean r0 = r5.m1063h(r0)
                r1 = 0
                if (r0 != 0) goto L55
                boolean r0 = r5.m1071p()
                if (r0 == 0) goto L55
                androidx.recyclerview.widget.RecyclerView r0 = androidx.recyclerview.widget.RecyclerView.this
                androidx.recyclerview.widget.RecyclerView$j r0 = r0.f1796U
                r2 = 1
                if (r0 == 0) goto L3f
                java.util.List r3 = r5.m1062g()
                androidx.recyclerview.widget.k r0 = (androidx.recyclerview.widget.C0321k) r0
                boolean r3 = r3.isEmpty()
                if (r3 == 0) goto L39
                boolean r0 = r0.f2171g
                if (r0 == 0) goto L33
                boolean r0 = r5.m1066k()
                if (r0 == 0) goto L31
                goto L33
            L31:
                r0 = r1
                goto L34
            L33:
                r0 = r2
            L34:
                if (r0 == 0) goto L37
                goto L39
            L37:
                r0 = r1
                goto L3a
            L39:
                r0 = r2
            L3a:
                if (r0 == 0) goto L3d
                goto L3f
            L3d:
                r0 = r1
                goto L40
            L3f:
                r0 = r2
            L40:
                if (r0 == 0) goto L43
                goto L55
            L43:
                java.util.ArrayList<androidx.recyclerview.widget.RecyclerView$b0> r0 = r4.f1916b
                if (r0 != 0) goto L4e
                java.util.ArrayList r0 = new java.util.ArrayList
                r0.<init>()
                r4.f1916b = r0
            L4e:
                r5.f1865n = r4
                r5.f1866o = r2
                java.util.ArrayList<androidx.recyclerview.widget.RecyclerView$b0> r0 = r4.f1916b
                goto L82
            L55:
                boolean r0 = r5.m1066k()
                if (r0 == 0) goto L7c
                boolean r0 = r5.m1068m()
                if (r0 != 0) goto L7c
                androidx.recyclerview.widget.RecyclerView r0 = androidx.recyclerview.widget.RecyclerView.this
                androidx.recyclerview.widget.RecyclerView$e r0 = r0.f1830u
                boolean r0 = r0.f1872b
                if (r0 == 0) goto L6a
                goto L7c
            L6a:
                java.lang.IllegalArgumentException r5 = new java.lang.IllegalArgumentException
                java.lang.String r0 = "Called scrap view with an invalid view. Invalid views cannot be reused from scrap, they should rebound from recycler pool."
                java.lang.StringBuilder r0 = androidx.activity.result.C0052a.m104h(r0)
                androidx.recyclerview.widget.RecyclerView r1 = androidx.recyclerview.widget.RecyclerView.this
                java.lang.String r0 = androidx.appcompat.widget.C0174y.m489g(r1, r0)
                r5.<init>(r0)
                throw r5
            L7c:
                r5.f1865n = r4
                r5.f1866o = r1
                java.util.ArrayList<androidx.recyclerview.widget.RecyclerView$b0> r0 = r4.f1915a
            L82:
                r0.add(r5)
                return
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.C0295t.m1184i(android.view.View):void");
        }

        /* JADX WARN: Code restructure failed: missing block: B:249:0x0449, code lost:
        
            if (r7.m1066k() == false) goto L573;
         */
        /* JADX WARN: Code restructure failed: missing block: B:258:0x047d, code lost:
        
            if ((r10 == 0 || r10 + r8 < r17) == false) goto L573;
         */
        /* JADX WARN: Code restructure failed: missing block: B:58:0x01df, code lost:
        
            if (r7.f1857f != 0) goto L444;
         */
        /* JADX WARN: Removed duplicated region for block: B:122:0x023d  */
        /* JADX WARN: Removed duplicated region for block: B:20:0x0088  */
        /* JADX WARN: Removed duplicated region for block: B:221:0x040a  */
        /* JADX WARN: Removed duplicated region for block: B:230:0x053f  */
        /* JADX WARN: Removed duplicated region for block: B:234:0x0562 A[ADDED_TO_REGION] */
        /* JADX WARN: Removed duplicated region for block: B:238:0x0546  */
        /* JADX WARN: Removed duplicated region for block: B:23:0x008f  */
        /* JADX WARN: Removed duplicated region for block: B:244:0x043b  */
        /* JADX WARN: Removed duplicated region for block: B:253:0x0466  */
        /* JADX WARN: Removed duplicated region for block: B:262:0x0490  */
        /* JADX WARN: Removed duplicated region for block: B:265:0x04ad  */
        /* JADX WARN: Removed duplicated region for block: B:268:0x04c0  */
        /* JADX WARN: Removed duplicated region for block: B:271:0x04e0  */
        /* JADX WARN: Removed duplicated region for block: B:278:0x04fb  */
        /* JADX WARN: Removed duplicated region for block: B:293:0x0534  */
        /* JADX WARN: Removed duplicated region for block: B:295:0x052b  */
        /* JADX WARN: Type inference failed for: r10v16, types: [java.util.ArrayList, java.util.List<android.view.View>] */
        /* JADX WARN: Type inference failed for: r3v9, types: [java.util.ArrayList, java.util.List<java.lang.Object>] */
        /* JADX WARN: Type inference failed for: r8v9, types: [java.util.ArrayList, java.util.List<android.view.View>] */
        /* JADX WARN: Type inference failed for: r9v0, types: [java.util.Map<android.view.View, e0.a>, java.util.WeakHashMap] */
        /* renamed from: j */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct code enable 'Show inconsistent code' option in preferences
        */
        public final androidx.recyclerview.widget.RecyclerView.AbstractC0277b0 m1185j(int r16, long r17) {
            /*
                Method dump skipped, instructions count: 1433
                To view this dump change 'Code comments level' option to 'DEBUG'
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.C0295t.m1185j(int, long):androidx.recyclerview.widget.RecyclerView$b0");
        }

        /* renamed from: k */
        public final void m1186k(AbstractC0277b0 abstractC0277b0) {
            (abstractC0277b0.f1866o ? this.f1916b : this.f1915a).remove(abstractC0277b0);
            abstractC0277b0.f1865n = null;
            abstractC0277b0.f1866o = false;
            abstractC0277b0.m1059d();
        }

        /* renamed from: l */
        public final void m1187l() {
            AbstractC0288m abstractC0288m = RecyclerView.this.f1832v;
            this.f1920f = this.f1919e + (abstractC0288m != null ? abstractC0288m.f1893l : 0);
            for (int size = this.f1917c.size() - 1; size >= 0 && this.f1917c.size() > this.f1920f; size--) {
                m1181f(size);
            }
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$u */
    public interface InterfaceC0296u {
        /* renamed from: a */
        void m1188a();
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$v */
    public class C0297v extends AbstractC0282g {
        public C0297v() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0282g
        /* renamed from: a */
        public final void mo1096a() {
            RecyclerView.this.m1031i(null);
            RecyclerView recyclerView = RecyclerView.this;
            recyclerView.f1821p0.f1945f = true;
            recyclerView.m1017X(true);
            if (RecyclerView.this.f1814m.m1256g()) {
                return;
            }
            RecyclerView.this.requestLayout();
        }

        @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0282g
        /* renamed from: c */
        public final void mo1098c() {
            RecyclerView.this.m1031i(null);
            C0308a c0308a = RecyclerView.this.f1814m;
            Objects.requireNonNull(c0308a);
            c0308a.f2008b.add(c0308a.m1257h(4, 0, 1, null));
            c0308a.f2012f |= 4;
            if (c0308a.f2008b.size() == 1) {
                int[] iArr = RecyclerView.f1767G0;
                RecyclerView recyclerView = RecyclerView.this;
                if (!recyclerView.f1772B || !recyclerView.f1770A) {
                    recyclerView.f1784I = true;
                    recyclerView.requestLayout();
                } else {
                    RunnableC0274a runnableC0274a = recyclerView.f1822q;
                    WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
                    recyclerView.postOnAnimation(runnableC0274a);
                }
            }
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$w */
    public static class C0298w extends AbstractC0956a {
        public static final Parcelable.Creator<C0298w> CREATOR = new a();

        /* renamed from: l */
        public Parcelable f1924l;

        /* renamed from: androidx.recyclerview.widget.RecyclerView$w$a */
        public static class a implements Parcelable.ClassLoaderCreator<C0298w> {
            @Override // android.os.Parcelable.Creator
            public final Object createFromParcel(Parcel parcel) {
                return new C0298w(parcel, null);
            }

            @Override // android.os.Parcelable.Creator
            public final Object[] newArray(int i6) {
                return new C0298w[i6];
            }

            @Override // android.os.Parcelable.ClassLoaderCreator
            public final C0298w createFromParcel(Parcel parcel, ClassLoader classLoader) {
                return new C0298w(parcel, classLoader);
            }
        }

        public C0298w(Parcel parcel, ClassLoader classLoader) {
            super(parcel, classLoader);
            this.f1924l = parcel.readParcelable(classLoader == null ? AbstractC0288m.class.getClassLoader() : classLoader);
        }

        public C0298w(Parcelable parcelable) {
            super(parcelable);
        }

        @Override // p064j0.AbstractC0956a, android.os.Parcelable
        public final void writeToParcel(Parcel parcel, int i6) {
            parcel.writeParcelable(this.f4731j, i6);
            parcel.writeParcelable(this.f1924l, 0);
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$x */
    public static abstract class AbstractC0299x {

        /* renamed from: b */
        public RecyclerView f1926b;

        /* renamed from: c */
        public AbstractC0288m f1927c;

        /* renamed from: d */
        public boolean f1928d;

        /* renamed from: e */
        public boolean f1929e;

        /* renamed from: f */
        public View f1930f;

        /* renamed from: h */
        public boolean f1932h;

        /* renamed from: a */
        public int f1925a = -1;

        /* renamed from: g */
        public final a f1931g = new a();

        /* renamed from: androidx.recyclerview.widget.RecyclerView$x$a */
        public static class a {

            /* renamed from: d */
            public int f1936d = -1;

            /* renamed from: f */
            public boolean f1938f = false;

            /* renamed from: g */
            public int f1939g = 0;

            /* renamed from: a */
            public int f1933a = 0;

            /* renamed from: b */
            public int f1934b = 0;

            /* renamed from: c */
            public int f1935c = Integer.MIN_VALUE;

            /* renamed from: e */
            public Interpolator f1937e = null;

            /* renamed from: a */
            public final void m1193a(RecyclerView recyclerView) {
                int i6 = this.f1936d;
                if (i6 >= 0) {
                    this.f1936d = -1;
                    recyclerView.m1009P(i6);
                    this.f1938f = false;
                    return;
                }
                if (!this.f1938f) {
                    this.f1939g = 0;
                    return;
                }
                Interpolator interpolator = this.f1937e;
                if (interpolator != null && this.f1935c < 1) {
                    throw new IllegalStateException("If you provide an interpolator, you must set a positive duration");
                }
                int i7 = this.f1935c;
                if (i7 < 1) {
                    throw new IllegalStateException("Scroll duration must be a positive number");
                }
                recyclerView.f1815m0.m1054b(this.f1933a, this.f1934b, i7, interpolator);
                int i8 = this.f1939g + 1;
                this.f1939g = i8;
                if (i8 > 10) {
                    Log.e("RecyclerView", "Smooth Scroll action is being updated too frequently. Make sure you are not changing it unless necessary");
                }
                this.f1938f = false;
            }

            /* renamed from: b */
            public final void m1194b(int i6, int i7, int i8, Interpolator interpolator) {
                this.f1933a = i6;
                this.f1934b = i7;
                this.f1935c = i8;
                this.f1937e = interpolator;
                this.f1938f = true;
            }
        }

        /* renamed from: androidx.recyclerview.widget.RecyclerView$x$b */
        public interface b {
            /* renamed from: a */
            PointF mo957a(int i6);
        }

        /* renamed from: a */
        public final PointF m1189a(int i6) {
            Object obj = this.f1927c;
            if (obj instanceof b) {
                return ((b) obj).mo957a(i6);
            }
            StringBuilder m104h = C0052a.m104h("You should override computeScrollVectorForPosition when the LayoutManager does not implement ");
            m104h.append(b.class.getCanonicalName());
            Log.w("RecyclerView", m104h.toString());
            return null;
        }

        /* renamed from: b */
        public final void m1190b(int i6, int i7) {
            PointF m1189a;
            RecyclerView recyclerView = this.f1926b;
            if (this.f1925a == -1 || recyclerView == null) {
                m1192d();
            }
            if (this.f1928d && this.f1930f == null && this.f1927c != null && (m1189a = m1189a(this.f1925a)) != null) {
                float f6 = m1189a.x;
                if (f6 != 0.0f || m1189a.y != 0.0f) {
                    recyclerView.m1023d0((int) Math.signum(f6), (int) Math.signum(m1189a.y), null);
                }
            }
            this.f1928d = false;
            View view = this.f1930f;
            if (view != null) {
                Objects.requireNonNull(this.f1926b);
                AbstractC0277b0 m990K = RecyclerView.m990K(view);
                if ((m990K != null ? m990K.m1061f() : -1) == this.f1925a) {
                    View view2 = this.f1930f;
                    C0300y c0300y = recyclerView.f1821p0;
                    mo1191c(view2, this.f1931g);
                    this.f1931g.m1193a(recyclerView);
                    m1192d();
                } else {
                    Log.e("RecyclerView", "Passed over target position while smooth scrolling.");
                    this.f1930f = null;
                }
            }
            if (this.f1929e) {
                C0300y c0300y2 = recyclerView.f1821p0;
                a aVar = this.f1931g;
                C0325o c0325o = (C0325o) this;
                if (c0325o.f1926b.f1832v.m1157x() == 0) {
                    c0325o.m1192d();
                } else {
                    int i8 = c0325o.f2156o;
                    int i9 = i8 - i6;
                    if (i8 * i9 <= 0) {
                        i9 = 0;
                    }
                    c0325o.f2156o = i9;
                    int i10 = c0325o.f2157p;
                    int i11 = i10 - i7;
                    if (i10 * i11 <= 0) {
                        i11 = 0;
                    }
                    c0325o.f2157p = i11;
                    if (i9 == 0 && i11 == 0) {
                        PointF m1189a2 = c0325o.m1189a(c0325o.f1925a);
                        if (m1189a2 != null) {
                            if (m1189a2.x != 0.0f || m1189a2.y != 0.0f) {
                                float f7 = m1189a2.y;
                                float sqrt = (float) Math.sqrt((f7 * f7) + (r9 * r9));
                                float f8 = m1189a2.x / sqrt;
                                m1189a2.x = f8;
                                float f9 = m1189a2.y / sqrt;
                                m1189a2.y = f9;
                                c0325o.f2152k = m1189a2;
                                c0325o.f2156o = (int) (f8 * 10000.0f);
                                c0325o.f2157p = (int) (f9 * 10000.0f);
                                aVar.m1194b((int) (c0325o.f2156o * 1.2f), (int) (c0325o.f2157p * 1.2f), (int) (c0325o.mo1321g(10000) * 1.2f), c0325o.f2150i);
                            }
                        }
                        aVar.f1936d = c0325o.f1925a;
                        c0325o.m1192d();
                    }
                }
                a aVar2 = this.f1931g;
                boolean z5 = aVar2.f1936d >= 0;
                aVar2.m1193a(recyclerView);
                if (z5 && this.f1929e) {
                    this.f1928d = true;
                    recyclerView.f1815m0.m1053a();
                }
            }
        }

        /* renamed from: c */
        public abstract void mo1191c(View view, a aVar);

        /* renamed from: d */
        public final void m1192d() {
            if (this.f1929e) {
                this.f1929e = false;
                C0325o c0325o = (C0325o) this;
                c0325o.f2157p = 0;
                c0325o.f2156o = 0;
                c0325o.f2152k = null;
                this.f1926b.f1821p0.f1940a = -1;
                this.f1930f = null;
                this.f1925a = -1;
                this.f1928d = false;
                AbstractC0288m abstractC0288m = this.f1927c;
                if (abstractC0288m.f1888g == this) {
                    abstractC0288m.f1888g = null;
                }
                this.f1927c = null;
                this.f1926b = null;
            }
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$y */
    public static class C0300y {

        /* renamed from: a */
        public int f1940a = -1;

        /* renamed from: b */
        public int f1941b = 0;

        /* renamed from: c */
        public int f1942c = 0;

        /* renamed from: d */
        public int f1943d = 1;

        /* renamed from: e */
        public int f1944e = 0;

        /* renamed from: f */
        public boolean f1945f = false;

        /* renamed from: g */
        public boolean f1946g = false;

        /* renamed from: h */
        public boolean f1947h = false;

        /* renamed from: i */
        public boolean f1948i = false;

        /* renamed from: j */
        public boolean f1949j = false;

        /* renamed from: k */
        public boolean f1950k = false;

        /* renamed from: l */
        public int f1951l;

        /* renamed from: m */
        public long f1952m;

        /* renamed from: n */
        public int f1953n;

        /* renamed from: a */
        public final void m1195a(int i6) {
            if ((this.f1943d & i6) != 0) {
                return;
            }
            StringBuilder m104h = C0052a.m104h("Layout state should be one of ");
            m104h.append(Integer.toBinaryString(i6));
            m104h.append(" but it is ");
            m104h.append(Integer.toBinaryString(this.f1943d));
            throw new IllegalStateException(m104h.toString());
        }

        /* renamed from: b */
        public final int m1196b() {
            return this.f1946g ? this.f1941b - this.f1942c : this.f1944e;
        }

        public final String toString() {
            StringBuilder m104h = C0052a.m104h("State{mTargetPosition=");
            m104h.append(this.f1940a);
            m104h.append(", mData=");
            m104h.append((Object) null);
            m104h.append(", mItemCount=");
            m104h.append(this.f1944e);
            m104h.append(", mIsMeasuring=");
            m104h.append(this.f1948i);
            m104h.append(", mPreviousLayoutItemCount=");
            m104h.append(this.f1941b);
            m104h.append(", mDeletedInvisibleItemCountSincePreviousLayout=");
            m104h.append(this.f1942c);
            m104h.append(", mStructureChanged=");
            m104h.append(this.f1945f);
            m104h.append(", mInPreLayout=");
            m104h.append(this.f1946g);
            m104h.append(", mRunSimpleAnimations=");
            m104h.append(this.f1949j);
            m104h.append(", mRunPredictiveAnimations=");
            m104h.append(this.f1950k);
            m104h.append('}');
            return m104h.toString();
        }
    }

    /* renamed from: androidx.recyclerview.widget.RecyclerView$z */
    public static abstract class AbstractC0301z {
    }

    static {
        Class<?> cls = Integer.TYPE;
        f1768H0 = new Class[]{Context.class, AttributeSet.class, cls, cls};
        f1769I0 = new InterpolatorC0278c();
    }

    public RecyclerView(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, com.liaoyuan.aicast.R.attr.recyclerViewStyle);
    }

    /* JADX WARN: Can't wrap try/catch for region: R(13:31|(1:33)(12:71|(1:73)|35|36|37|(1:39)(1:55)|40|41|42|43|44|45)|34|35|36|37|(0)(0)|40|41|42|43|44|45) */
    /* JADX WARN: Code restructure failed: missing block: B:47:0x0253, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:50:0x0256, code lost:
    
        r0 = r4.getConstructor(new java.lang.Class[0]);
     */
    /* JADX WARN: Code restructure failed: missing block: B:52:0x026a, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:53:0x026b, code lost:
    
        r0.initCause(r0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:54:0x028b, code lost:
    
        throw new java.lang.IllegalStateException(r20.getPositionDescription() + ": Error creating LayoutManager " + r3, r0);
     */
    /* JADX WARN: Removed duplicated region for block: B:39:0x021e A[Catch: ClassCastException -> 0x028c, IllegalAccessException -> 0x02ab, InstantiationException -> 0x02ca, InvocationTargetException -> 0x02e7, ClassNotFoundException -> 0x0304, TryCatch #4 {ClassCastException -> 0x028c, ClassNotFoundException -> 0x0304, IllegalAccessException -> 0x02ab, InstantiationException -> 0x02ca, InvocationTargetException -> 0x02e7, blocks: (B:37:0x0218, B:39:0x021e, B:40:0x022b, B:43:0x0237, B:45:0x025c, B:50:0x0256, B:53:0x026b, B:54:0x028b, B:55:0x0227), top: B:36:0x0218 }] */
    /* JADX WARN: Removed duplicated region for block: B:55:0x0227 A[Catch: ClassCastException -> 0x028c, IllegalAccessException -> 0x02ab, InstantiationException -> 0x02ca, InvocationTargetException -> 0x02e7, ClassNotFoundException -> 0x0304, TryCatch #4 {ClassCastException -> 0x028c, ClassNotFoundException -> 0x0304, IllegalAccessException -> 0x02ab, InstantiationException -> 0x02ca, InvocationTargetException -> 0x02e7, blocks: (B:37:0x0218, B:39:0x021e, B:40:0x022b, B:43:0x0237, B:45:0x025c, B:50:0x0256, B:53:0x026b, B:54:0x028b, B:55:0x0227), top: B:36:0x0218 }] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public RecyclerView(android.content.Context r19, android.util.AttributeSet r20, int r21) {
        /*
            Method dump skipped, instructions count: 841
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.<init>(android.content.Context, android.util.AttributeSet, int):void");
    }

    /* renamed from: F */
    public static RecyclerView m989F(View view) {
        if (!(view instanceof ViewGroup)) {
            return null;
        }
        if (view instanceof RecyclerView) {
            return (RecyclerView) view;
        }
        ViewGroup viewGroup = (ViewGroup) view;
        int childCount = viewGroup.getChildCount();
        for (int i6 = 0; i6 < childCount; i6++) {
            RecyclerView m989F = m989F(viewGroup.getChildAt(i6));
            if (m989F != null) {
                return m989F;
            }
        }
        return null;
    }

    /* renamed from: K */
    public static AbstractC0277b0 m990K(View view) {
        if (view == null) {
            return null;
        }
        return ((C0289n) view.getLayoutParams()).f1905a;
    }

    private C0758h getScrollingChildHelper() {
        if (this.f1841z0 == null) {
            this.f1841z0 = new C0758h(this);
        }
        return this.f1841z0;
    }

    /* renamed from: k */
    public static void m995k(AbstractC0277b0 abstractC0277b0) {
        WeakReference<RecyclerView> weakReference = abstractC0277b0.f1853b;
        if (weakReference != null) {
            Object obj = weakReference.get();
            while (true) {
                for (View view = (View) obj; view != null; view = null) {
                    if (view == abstractC0277b0.f1852a) {
                        return;
                    }
                    obj = view.getParent();
                    if (obj instanceof View) {
                        break;
                    }
                }
                abstractC0277b0.f1853b = null;
                return;
            }
        }
    }

    /* renamed from: A */
    public final String m996A() {
        StringBuilder m104h = C0052a.m104h(" ");
        m104h.append(super.toString());
        m104h.append(", adapter:");
        m104h.append(this.f1830u);
        m104h.append(", layout:");
        m104h.append(this.f1832v);
        m104h.append(", context:");
        m104h.append(getContext());
        return m104h.toString();
    }

    /* renamed from: B */
    public final void m997B(C0300y c0300y) {
        if (getScrollState() != 2) {
            Objects.requireNonNull(c0300y);
            return;
        }
        OverScroller overScroller = this.f1815m0.f1845l;
        overScroller.getFinalX();
        overScroller.getCurrX();
        Objects.requireNonNull(c0300y);
        overScroller.getFinalY();
        overScroller.getCurrY();
    }

    /* JADX WARN: Code restructure failed: missing block: B:11:?, code lost:
    
        return r3;
     */
    /* renamed from: C */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final android.view.View m998C(android.view.View r3) {
        /*
            r2 = this;
        L0:
            android.view.ViewParent r0 = r3.getParent()
            if (r0 == 0) goto L10
            if (r0 == r2) goto L10
            boolean r1 = r0 instanceof android.view.View
            if (r1 == 0) goto L10
            r3 = r0
            android.view.View r3 = (android.view.View) r3
            goto L0
        L10:
            if (r0 != r2) goto L13
            goto L14
        L13:
            r3 = 0
        L14:
            return r3
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.m998C(android.view.View):android.view.View");
    }

    /* renamed from: D */
    public final boolean m999D(MotionEvent motionEvent) {
        int action = motionEvent.getAction();
        int size = this.f1838y.size();
        for (int i6 = 0; i6 < size; i6++) {
            InterfaceC0292q interfaceC0292q = this.f1838y.get(i6);
            if (interfaceC0292q.mo1170a(motionEvent) && action != 3) {
                this.f1840z = interfaceC0292q;
                return true;
            }
        }
        return false;
    }

    /* renamed from: E */
    public final void m1000E(int[] iArr) {
        int m1271e = this.f1816n.m1271e();
        if (m1271e == 0) {
            iArr[0] = -1;
            iArr[1] = -1;
            return;
        }
        int i6 = Integer.MAX_VALUE;
        int i7 = Integer.MIN_VALUE;
        for (int i8 = 0; i8 < m1271e; i8++) {
            AbstractC0277b0 m990K = m990K(this.f1816n.m1270d(i8));
            if (!m990K.m1076u()) {
                int m1061f = m990K.m1061f();
                if (m1061f < i6) {
                    i6 = m1061f;
                }
                if (m1061f > i7) {
                    i7 = m1061f;
                }
            }
        }
        iArr[0] = i6;
        iArr[1] = i7;
    }

    /* renamed from: G */
    public final AbstractC0277b0 m1001G(int i6) {
        AbstractC0277b0 abstractC0277b0 = null;
        if (this.f1787L) {
            return null;
        }
        int m1274h = this.f1816n.m1274h();
        for (int i7 = 0; i7 < m1274h; i7++) {
            AbstractC0277b0 m990K = m990K(this.f1816n.m1273g(i7));
            if (m990K != null && !m990K.m1068m() && m1002H(m990K) == i6) {
                if (!this.f1816n.m1277k(m990K.f1852a)) {
                    return m990K;
                }
                abstractC0277b0 = m990K;
            }
        }
        return abstractC0277b0;
    }

    /* renamed from: H */
    public final int m1002H(AbstractC0277b0 abstractC0277b0) {
        if (abstractC0277b0.m1063h(524) || !abstractC0277b0.m1065j()) {
            return -1;
        }
        C0308a c0308a = this.f1814m;
        int i6 = abstractC0277b0.f1854c;
        int size = c0308a.f2008b.size();
        for (int i7 = 0; i7 < size; i7++) {
            C0308a.b bVar = c0308a.f2008b.get(i7);
            int i8 = bVar.f2013a;
            if (i8 != 1) {
                if (i8 == 2) {
                    int i9 = bVar.f2014b;
                    if (i9 <= i6) {
                        int i10 = bVar.f2016d;
                        if (i9 + i10 > i6) {
                            return -1;
                        }
                        i6 -= i10;
                    } else {
                        continue;
                    }
                } else if (i8 == 8) {
                    int i11 = bVar.f2014b;
                    if (i11 == i6) {
                        i6 = bVar.f2016d;
                    } else {
                        if (i11 < i6) {
                            i6--;
                        }
                        if (bVar.f2016d <= i6) {
                            i6++;
                        }
                    }
                }
            } else if (bVar.f2014b <= i6) {
                i6 += bVar.f2016d;
            }
        }
        return i6;
    }

    /* renamed from: I */
    public final long m1003I(AbstractC0277b0 abstractC0277b0) {
        return this.f1830u.f1872b ? abstractC0277b0.f1856e : abstractC0277b0.f1854c;
    }

    /* renamed from: J */
    public final AbstractC0277b0 m1004J(View view) {
        ViewParent parent = view.getParent();
        if (parent == null || parent == this) {
            return m990K(view);
        }
        throw new IllegalArgumentException("View " + view + " is not a direct child of " + this);
    }

    /* renamed from: L */
    public final Rect m1005L(View view) {
        C0289n c0289n = (C0289n) view.getLayoutParams();
        if (!c0289n.f1907c) {
            return c0289n.f1906b;
        }
        if (this.f1821p0.f1946g && (c0289n.m1166b() || c0289n.f1905a.m1066k())) {
            return c0289n.f1906b;
        }
        Rect rect = c0289n.f1906b;
        rect.set(0, 0, 0, 0);
        int size = this.f1836x.size();
        for (int i6 = 0; i6 < size; i6++) {
            this.f1824r.set(0, 0, 0, 0);
            AbstractC0287l abstractC0287l = this.f1836x.get(i6);
            Rect rect2 = this.f1824r;
            Objects.requireNonNull(abstractC0287l);
            ((C0289n) view.getLayoutParams()).m1165a();
            rect2.set(0, 0, 0, 0);
            int i7 = rect.left;
            Rect rect3 = this.f1824r;
            rect.left = i7 + rect3.left;
            rect.top += rect3.top;
            rect.right += rect3.right;
            rect.bottom += rect3.bottom;
        }
        c0289n.f1907c = false;
        return rect;
    }

    /* renamed from: M */
    public final boolean m1006M() {
        return !this.f1774C || this.f1787L || this.f1814m.m1256g();
    }

    /* renamed from: N */
    public final void m1007N() {
        this.f1795T = null;
        this.f1793R = null;
        this.f1794S = null;
        this.f1792Q = null;
    }

    /* renamed from: O */
    public final boolean m1008O() {
        return this.f1789N > 0;
    }

    /* renamed from: P */
    public final void m1009P(int i6) {
        if (this.f1832v == null) {
            return;
        }
        setScrollState(2);
        this.f1832v.mo980w0(i6);
        awakenScrollBars();
    }

    /* renamed from: Q */
    public final void m1010Q() {
        int m1274h = this.f1816n.m1274h();
        for (int i6 = 0; i6 < m1274h; i6++) {
            ((C0289n) this.f1816n.m1273g(i6).getLayoutParams()).f1907c = true;
        }
        C0295t c0295t = this.f1810k;
        int size = c0295t.f1917c.size();
        for (int i7 = 0; i7 < size; i7++) {
            C0289n c0289n = (C0289n) c0295t.f1917c.get(i7).f1852a.getLayoutParams();
            if (c0289n != null) {
                c0289n.f1907c = true;
            }
        }
    }

    /* renamed from: R */
    public final void m1011R(int i6, int i7, boolean z5) {
        int i8 = i6 + i7;
        int m1274h = this.f1816n.m1274h();
        for (int i9 = 0; i9 < m1274h; i9++) {
            AbstractC0277b0 m990K = m990K(this.f1816n.m1273g(i9));
            if (m990K != null && !m990K.m1076u()) {
                int i10 = m990K.f1854c;
                if (i10 >= i8) {
                    m990K.m1072q(-i7, z5);
                } else if (i10 >= i6) {
                    m990K.m1057b(8);
                    m990K.m1072q(-i7, z5);
                    m990K.f1854c = i6 - 1;
                }
                this.f1821p0.f1945f = true;
            }
        }
        C0295t c0295t = this.f1810k;
        int size = c0295t.f1917c.size();
        while (true) {
            size--;
            if (size < 0) {
                requestLayout();
                return;
            }
            AbstractC0277b0 abstractC0277b0 = c0295t.f1917c.get(size);
            if (abstractC0277b0 != null) {
                int i11 = abstractC0277b0.f1854c;
                if (i11 >= i8) {
                    abstractC0277b0.m1072q(-i7, z5);
                } else if (i11 >= i6) {
                    abstractC0277b0.m1057b(8);
                    c0295t.m1181f(size);
                }
            }
        }
    }

    /* renamed from: S */
    public final void m1012S() {
        this.f1789N++;
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [java.util.ArrayList, java.util.List<androidx.recyclerview.widget.RecyclerView$b0>] */
    /* JADX WARN: Type inference failed for: r6v2, types: [java.util.ArrayList, java.util.List<androidx.recyclerview.widget.RecyclerView$b0>] */
    /* JADX WARN: Type inference failed for: r6v6, types: [java.util.ArrayList, java.util.List<androidx.recyclerview.widget.RecyclerView$b0>] */
    /* renamed from: T */
    public final void m1013T(boolean z5) {
        int i6;
        int i7 = this.f1789N - 1;
        this.f1789N = i7;
        if (i7 < 1) {
            this.f1789N = 0;
            if (z5) {
                int i8 = this.f1783H;
                this.f1783H = 0;
                if (i8 != 0) {
                    AccessibilityManager accessibilityManager = this.f1785J;
                    if (accessibilityManager != null && accessibilityManager.isEnabled()) {
                        AccessibilityEvent obtain = AccessibilityEvent.obtain();
                        obtain.setEventType(2048);
                        obtain.setContentChangeTypes(i8);
                        sendAccessibilityEventUnchecked(obtain);
                    }
                }
                for (int size = this.f1777D0.size() - 1; size >= 0; size--) {
                    AbstractC0277b0 abstractC0277b0 = (AbstractC0277b0) this.f1777D0.get(size);
                    if (abstractC0277b0.f1852a.getParent() == this && !abstractC0277b0.m1076u() && (i6 = abstractC0277b0.f1868q) != -1) {
                        View view = abstractC0277b0.f1852a;
                        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
                        view.setImportantForAccessibility(i6);
                        abstractC0277b0.f1868q = -1;
                    }
                }
                this.f1777D0.clear();
            }
        }
    }

    /* renamed from: U */
    public final void m1014U(MotionEvent motionEvent) {
        int actionIndex = motionEvent.getActionIndex();
        if (motionEvent.getPointerId(actionIndex) == this.f1798W) {
            int i6 = actionIndex == 0 ? 1 : 0;
            this.f1798W = motionEvent.getPointerId(i6);
            int x6 = (int) (motionEvent.getX(i6) + 0.5f);
            this.f1802d0 = x6;
            this.f1800b0 = x6;
            int y2 = (int) (motionEvent.getY(i6) + 0.5f);
            this.f1803e0 = y2;
            this.f1801c0 = y2;
        }
    }

    /* renamed from: V */
    public final void m1015V() {
        if (this.f1833v0 || !this.f1770A) {
            return;
        }
        RunnableC0276b runnableC0276b = this.f1779E0;
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        postOnAnimation(runnableC0276b);
        this.f1833v0 = true;
    }

    /* renamed from: W */
    public final void m1016W() {
        boolean z5;
        boolean z6 = false;
        if (this.f1787L) {
            C0308a c0308a = this.f1814m;
            c0308a.m1261l(c0308a.f2008b);
            c0308a.m1261l(c0308a.f2009c);
            c0308a.f2012f = 0;
            if (this.f1788M) {
                this.f1832v.mo905d0();
            }
        }
        if (this.f1796U != null && this.f1832v.mo898J0()) {
            this.f1814m.m1259j();
        } else {
            this.f1814m.m1252c();
        }
        boolean z7 = this.f1827s0 || this.f1829t0;
        C0300y c0300y = this.f1821p0;
        boolean z8 = this.f1774C && this.f1796U != null && ((z5 = this.f1787L) || z7 || this.f1832v.f1889h) && (!z5 || this.f1830u.f1872b);
        c0300y.f1949j = z8;
        if (z8 && z7 && !this.f1787L) {
            if (this.f1796U != null && this.f1832v.mo898J0()) {
                z6 = true;
            }
        }
        c0300y.f1950k = z6;
    }

    /* renamed from: X */
    public final void m1017X(boolean z5) {
        this.f1788M = z5 | this.f1788M;
        this.f1787L = true;
        int m1274h = this.f1816n.m1274h();
        for (int i6 = 0; i6 < m1274h; i6++) {
            AbstractC0277b0 m990K = m990K(this.f1816n.m1273g(i6));
            if (m990K != null && !m990K.m1076u()) {
                m990K.m1057b(6);
            }
        }
        m1010Q();
        C0295t c0295t = this.f1810k;
        int size = c0295t.f1917c.size();
        for (int i7 = 0; i7 < size; i7++) {
            AbstractC0277b0 abstractC0277b0 = c0295t.f1917c.get(i7);
            if (abstractC0277b0 != null) {
                abstractC0277b0.m1057b(6);
                abstractC0277b0.m1056a(null);
            }
        }
        AbstractC0280e abstractC0280e = RecyclerView.this.f1830u;
        if (abstractC0280e == null || !abstractC0280e.f1872b) {
            c0295t.m1180e();
        }
    }

    /* renamed from: Y */
    public final void m1018Y(AbstractC0277b0 abstractC0277b0, AbstractC0285j.c cVar) {
        abstractC0277b0.m1074s(0, 8192);
        if (this.f1821p0.f1947h && abstractC0277b0.m1071p() && !abstractC0277b0.m1068m() && !abstractC0277b0.m1076u()) {
            this.f1818o.f2036b.m2672h(m1003I(abstractC0277b0), abstractC0277b0);
        }
        this.f1818o.m1294c(abstractC0277b0, cVar);
    }

    /* renamed from: Z */
    public final void m1019Z() {
        AbstractC0285j abstractC0285j = this.f1796U;
        if (abstractC0285j != null) {
            abstractC0285j.mo1106f();
        }
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m != null) {
            abstractC0288m.m1147o0(this.f1810k);
            this.f1832v.m1148p0(this.f1810k);
        }
        this.f1810k.m1177b();
    }

    /* renamed from: a0 */
    public final void m1020a0(View view, View view2) {
        View view3 = view2 != null ? view2 : view;
        this.f1824r.set(0, 0, view3.getWidth(), view3.getHeight());
        ViewGroup.LayoutParams layoutParams = view3.getLayoutParams();
        if (layoutParams instanceof C0289n) {
            C0289n c0289n = (C0289n) layoutParams;
            if (!c0289n.f1907c) {
                Rect rect = c0289n.f1906b;
                Rect rect2 = this.f1824r;
                rect2.left -= rect.left;
                rect2.right += rect.right;
                rect2.top -= rect.top;
                rect2.bottom += rect.bottom;
            }
        }
        if (view2 != null) {
            offsetDescendantRectToMyCoords(view2, this.f1824r);
            offsetRectIntoDescendantCoords(view, this.f1824r);
        }
        this.f1832v.mo1154t0(this, view, this.f1824r, !this.f1774C, view2 == null);
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void addFocusables(ArrayList<View> arrayList, int i6, int i7) {
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m != null) {
            Objects.requireNonNull(abstractC0288m);
        }
        super.addFocusables(arrayList, i6, i7);
    }

    /* renamed from: b0 */
    public final void m1021b0() {
        VelocityTracker velocityTracker = this.f1799a0;
        if (velocityTracker != null) {
            velocityTracker.clear();
        }
        boolean z5 = false;
        m1037l0(0);
        EdgeEffect edgeEffect = this.f1792Q;
        if (edgeEffect != null) {
            edgeEffect.onRelease();
            z5 = this.f1792Q.isFinished();
        }
        EdgeEffect edgeEffect2 = this.f1793R;
        if (edgeEffect2 != null) {
            edgeEffect2.onRelease();
            z5 |= this.f1793R.isFinished();
        }
        EdgeEffect edgeEffect3 = this.f1794S;
        if (edgeEffect3 != null) {
            edgeEffect3.onRelease();
            z5 |= this.f1794S.isFinished();
        }
        EdgeEffect edgeEffect4 = this.f1795T;
        if (edgeEffect4 != null) {
            edgeEffect4.onRelease();
            z5 |= this.f1795T.isFinished();
        }
        if (z5) {
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            postInvalidateOnAnimation();
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:25:0x00df  */
    /* JADX WARN: Removed duplicated region for block: B:28:0x0112  */
    /* JADX WARN: Removed duplicated region for block: B:33:0x00f5  */
    /* renamed from: c0 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean m1022c0(int r19, int r20, android.view.MotionEvent r21) {
        /*
            Method dump skipped, instructions count: 317
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.m1022c0(int, int, android.view.MotionEvent):boolean");
    }

    @Override // android.view.ViewGroup
    public final boolean checkLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return (layoutParams instanceof C0289n) && this.f1832v.mo910g((C0289n) layoutParams);
    }

    @Override // android.view.View
    public final int computeHorizontalScrollExtent() {
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m != null && abstractC0288m.mo963e()) {
            return this.f1832v.mo971k(this.f1821p0);
        }
        return 0;
    }

    @Override // android.view.View
    public final int computeHorizontalScrollOffset() {
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m != null && abstractC0288m.mo963e()) {
            return this.f1832v.mo914l(this.f1821p0);
        }
        return 0;
    }

    @Override // android.view.View
    public final int computeHorizontalScrollRange() {
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m != null && abstractC0288m.mo963e()) {
            return this.f1832v.mo916m(this.f1821p0);
        }
        return 0;
    }

    @Override // android.view.View
    public final int computeVerticalScrollExtent() {
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m != null && abstractC0288m.mo964f()) {
            return this.f1832v.mo976n(this.f1821p0);
        }
        return 0;
    }

    @Override // android.view.View
    public final int computeVerticalScrollOffset() {
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m != null && abstractC0288m.mo964f()) {
            return this.f1832v.mo917o(this.f1821p0);
        }
        return 0;
    }

    @Override // android.view.View
    public final int computeVerticalScrollRange() {
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m != null && abstractC0288m.mo964f()) {
            return this.f1832v.mo918p(this.f1821p0);
        }
        return 0;
    }

    /* renamed from: d0 */
    public final void m1023d0(int i6, int i7, int[] iArr) {
        AbstractC0277b0 abstractC0277b0;
        m1032i0();
        m1012S();
        int i8 = C0003b.f5a;
        Trace.beginSection("RV Scroll");
        m997B(this.f1821p0);
        int mo928v0 = i6 != 0 ? this.f1832v.mo928v0(i6, this.f1810k, this.f1821p0) : 0;
        int mo931x0 = i7 != 0 ? this.f1832v.mo931x0(i7, this.f1810k, this.f1821p0) : 0;
        Trace.endSection();
        int m1271e = this.f1816n.m1271e();
        for (int i9 = 0; i9 < m1271e; i9++) {
            View m1270d = this.f1816n.m1270d(i9);
            AbstractC0277b0 m1004J = m1004J(m1270d);
            if (m1004J != null && (abstractC0277b0 = m1004J.f1860i) != null) {
                View view = abstractC0277b0.f1852a;
                int left = m1270d.getLeft();
                int top = m1270d.getTop();
                if (left != view.getLeft() || top != view.getTop()) {
                    view.layout(left, top, view.getWidth() + left, view.getHeight() + top);
                }
            }
        }
        m1013T(true);
        m1035k0(false);
        if (iArr != null) {
            iArr[0] = mo928v0;
            iArr[1] = mo931x0;
        }
    }

    @Override // android.view.View
    public final boolean dispatchNestedFling(float f6, float f7, boolean z5) {
        return getScrollingChildHelper().m2155a(f6, f7, z5);
    }

    @Override // android.view.View
    public final boolean dispatchNestedPreFling(float f6, float f7) {
        return getScrollingChildHelper().m2156b(f6, f7);
    }

    @Override // android.view.View
    public final boolean dispatchNestedPreScroll(int i6, int i7, int[] iArr, int[] iArr2) {
        return getScrollingChildHelper().m2157c(i6, i7, iArr, iArr2, 0);
    }

    @Override // android.view.View
    public final boolean dispatchNestedScroll(int i6, int i7, int i8, int i9, int[] iArr) {
        return getScrollingChildHelper().m2159e(i6, i7, i8, i9, iArr, 0, null);
    }

    @Override // android.view.View
    public final boolean dispatchPopulateAccessibilityEvent(AccessibilityEvent accessibilityEvent) {
        onPopulateAccessibilityEvent(accessibilityEvent);
        return true;
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void dispatchRestoreInstanceState(SparseArray<Parcelable> sparseArray) {
        dispatchThawSelfOnly(sparseArray);
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void dispatchSaveInstanceState(SparseArray<Parcelable> sparseArray) {
        dispatchFreezeSelfOnly(sparseArray);
    }

    @Override // android.view.View
    public final void draw(Canvas canvas) {
        boolean z5;
        float f6;
        float f7;
        super.draw(canvas);
        int size = this.f1836x.size();
        boolean z6 = false;
        for (int i6 = 0; i6 < size; i6++) {
            this.f1836x.get(i6).mo1111e(canvas);
        }
        EdgeEffect edgeEffect = this.f1792Q;
        if (edgeEffect == null || edgeEffect.isFinished()) {
            z5 = false;
        } else {
            int save = canvas.save();
            int paddingBottom = this.f1820p ? getPaddingBottom() : 0;
            canvas.rotate(270.0f);
            canvas.translate((-getHeight()) + paddingBottom, 0.0f);
            EdgeEffect edgeEffect2 = this.f1792Q;
            z5 = edgeEffect2 != null && edgeEffect2.draw(canvas);
            canvas.restoreToCount(save);
        }
        EdgeEffect edgeEffect3 = this.f1793R;
        if (edgeEffect3 != null && !edgeEffect3.isFinished()) {
            int save2 = canvas.save();
            if (this.f1820p) {
                canvas.translate(getPaddingLeft(), getPaddingTop());
            }
            EdgeEffect edgeEffect4 = this.f1793R;
            z5 |= edgeEffect4 != null && edgeEffect4.draw(canvas);
            canvas.restoreToCount(save2);
        }
        EdgeEffect edgeEffect5 = this.f1794S;
        if (edgeEffect5 != null && !edgeEffect5.isFinished()) {
            int save3 = canvas.save();
            int width = getWidth();
            int paddingTop = this.f1820p ? getPaddingTop() : 0;
            canvas.rotate(90.0f);
            canvas.translate(-paddingTop, -width);
            EdgeEffect edgeEffect6 = this.f1794S;
            z5 |= edgeEffect6 != null && edgeEffect6.draw(canvas);
            canvas.restoreToCount(save3);
        }
        EdgeEffect edgeEffect7 = this.f1795T;
        if (edgeEffect7 != null && !edgeEffect7.isFinished()) {
            int save4 = canvas.save();
            canvas.rotate(180.0f);
            if (this.f1820p) {
                f6 = getPaddingRight() + (-getWidth());
                f7 = getPaddingBottom() + (-getHeight());
            } else {
                f6 = -getWidth();
                f7 = -getHeight();
            }
            canvas.translate(f6, f7);
            EdgeEffect edgeEffect8 = this.f1795T;
            if (edgeEffect8 != null && edgeEffect8.draw(canvas)) {
                z6 = true;
            }
            z5 |= z6;
            canvas.restoreToCount(save4);
        }
        if ((z5 || this.f1796U == null || this.f1836x.size() <= 0 || !this.f1796U.mo1107g()) ? z5 : true) {
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            postInvalidateOnAnimation();
        }
    }

    @Override // android.view.ViewGroup
    public final boolean drawChild(Canvas canvas, View view, long j6) {
        return super.drawChild(canvas, view, j6);
    }

    /* renamed from: e0 */
    public final void m1024e0(int i6) {
        if (this.f1780F) {
            return;
        }
        m1039m0();
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m == null) {
            Log.e("RecyclerView", "Cannot scroll to position a LayoutManager set. Call setLayoutManager with a non-null argument.");
        } else {
            abstractC0288m.mo980w0(i6);
            awakenScrollBars();
        }
    }

    /* renamed from: f */
    public final void m1025f(AbstractC0277b0 abstractC0277b0) {
        View view = abstractC0277b0.f1852a;
        boolean z5 = view.getParent() == this;
        this.f1810k.m1186k(m1004J(view));
        if (abstractC0277b0.m1070o()) {
            this.f1816n.m1268b(view, -1, view.getLayoutParams(), true);
            return;
        }
        C0310b c0310b = this.f1816n;
        if (!z5) {
            c0310b.m1267a(view, -1, true);
            return;
        }
        int m1344c = ((C0332v) c0310b.f2021a).m1344c(view);
        if (m1344c >= 0) {
            c0310b.f2022b.m1286h(m1344c);
            c0310b.m1275i(view);
        } else {
            throw new IllegalArgumentException("view is not a child, cannot hide " + view);
        }
    }

    /* JADX WARN: Type inference failed for: r3v1, types: [java.util.ArrayList, java.util.List<androidx.recyclerview.widget.RecyclerView$b0>] */
    /* renamed from: f0 */
    public final boolean m1026f0(AbstractC0277b0 abstractC0277b0, int i6) {
        if (m1008O()) {
            abstractC0277b0.f1868q = i6;
            this.f1777D0.add(abstractC0277b0);
            return false;
        }
        View view = abstractC0277b0.f1852a;
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        view.setImportantForAccessibility(i6);
        return true;
    }

    /* JADX WARN: Code restructure failed: missing block: B:103:0x018c, code lost:
    
        if ((r6 * r2) < 0) goto L276;
     */
    /* JADX WARN: Code restructure failed: missing block: B:107:0x0194, code lost:
    
        if ((r6 * r2) > 0) goto L276;
     */
    /* JADX WARN: Code restructure failed: missing block: B:94:0x0164, code lost:
    
        if (r3 > 0) goto L277;
     */
    /* JADX WARN: Code restructure failed: missing block: B:97:0x017e, code lost:
    
        if (r6 > 0) goto L277;
     */
    /* JADX WARN: Code restructure failed: missing block: B:98:0x0181, code lost:
    
        if (r3 < 0) goto L277;
     */
    /* JADX WARN: Code restructure failed: missing block: B:99:0x0184, code lost:
    
        if (r6 < 0) goto L277;
     */
    /* JADX WARN: Removed duplicated region for block: B:26:0x0055  */
    /* JADX WARN: Removed duplicated region for block: B:28:0x005a  */
    /* JADX WARN: Removed duplicated region for block: B:31:0x0060  */
    /* JADX WARN: Removed duplicated region for block: B:34:0x006a  */
    /* JADX WARN: Removed duplicated region for block: B:35:0x006c  */
    /* JADX WARN: Removed duplicated region for block: B:36:0x0063  */
    /* JADX WARN: Removed duplicated region for block: B:37:0x005c  */
    /* JADX WARN: Removed duplicated region for block: B:38:0x0057  */
    /* JADX WARN: Removed duplicated region for block: B:40:0x006f  */
    /* JADX WARN: Removed duplicated region for block: B:61:0x019b  */
    /* JADX WARN: Removed duplicated region for block: B:63:? A[RETURN, SYNTHETIC] */
    @Override // android.view.ViewGroup, android.view.ViewParent
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final android.view.View focusSearch(android.view.View r14, int r15) {
        /*
            Method dump skipped, instructions count: 416
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.focusSearch(android.view.View, int):android.view.View");
    }

    /* renamed from: g */
    public final void m1027g(AbstractC0287l abstractC0287l) {
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m != null) {
            abstractC0288m.mo961d("Cannot add item decoration during a scroll  or layout");
        }
        if (this.f1836x.isEmpty()) {
            setWillNotDraw(false);
        }
        this.f1836x.add(abstractC0287l);
        m1010Q();
        requestLayout();
    }

    /* renamed from: g0 */
    public final void m1028g0(int i6, int i7, boolean z5) {
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m == null) {
            Log.e("RecyclerView", "Cannot smooth scroll without a LayoutManager set. Call setLayoutManager with a non-null argument.");
            return;
        }
        if (this.f1780F) {
            return;
        }
        if (!abstractC0288m.mo963e()) {
            i6 = 0;
        }
        if (!this.f1832v.mo964f()) {
            i7 = 0;
        }
        if (i6 == 0 && i7 == 0) {
            return;
        }
        if (z5) {
            int i8 = i6 != 0 ? 1 : 0;
            if (i7 != 0) {
                i8 |= 2;
            }
            m1034j0(i8, 1);
        }
        this.f1815m0.m1054b(i6, i7, Integer.MIN_VALUE, null);
    }

    @Override // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateDefaultLayoutParams() {
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m != null) {
            return abstractC0288m.mo923t();
        }
        throw new IllegalStateException(C0174y.m489g(this, C0052a.m104h("RecyclerView has no LayoutManager")));
    }

    @Override // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m != null) {
            return abstractC0288m.mo925u(getContext(), attributeSet);
        }
        throw new IllegalStateException(C0174y.m489g(this, C0052a.m104h("RecyclerView has no LayoutManager")));
    }

    @Override // android.view.ViewGroup, android.view.View
    public CharSequence getAccessibilityClassName() {
        return "androidx.recyclerview.widget.RecyclerView";
    }

    public AbstractC0280e getAdapter() {
        return this.f1830u;
    }

    @Override // android.view.View
    public int getBaseline() {
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m == null) {
            return super.getBaseline();
        }
        Objects.requireNonNull(abstractC0288m);
        return -1;
    }

    @Override // android.view.ViewGroup
    public final int getChildDrawingOrder(int i6, int i7) {
        InterfaceC0283h interfaceC0283h = this.f1837x0;
        return interfaceC0283h == null ? super.getChildDrawingOrder(i6, i7) : interfaceC0283h.m1099a();
    }

    @Override // android.view.ViewGroup
    public boolean getClipToPadding() {
        return this.f1820p;
    }

    public C0334x getCompatAccessibilityDelegate() {
        return this.f1835w0;
    }

    public C0284i getEdgeEffectFactory() {
        return this.f1791P;
    }

    public AbstractC0285j getItemAnimator() {
        return this.f1796U;
    }

    public int getItemDecorationCount() {
        return this.f1836x.size();
    }

    public AbstractC0288m getLayoutManager() {
        return this.f1832v;
    }

    public int getMaxFlingVelocity() {
        return this.f1807i0;
    }

    public int getMinFlingVelocity() {
        return this.f1806h0;
    }

    public long getNanoTime() {
        return System.nanoTime();
    }

    public AbstractC0291p getOnFlingListener() {
        return this.f1805g0;
    }

    public boolean getPreserveFocusAfterLayout() {
        return this.f1813l0;
    }

    public C0294s getRecycledViewPool() {
        return this.f1810k.m1179d();
    }

    public int getScrollState() {
        return this.f1797V;
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [java.util.ArrayList, java.util.List<androidx.recyclerview.widget.RecyclerView$r>] */
    /* renamed from: h */
    public final void m1029h(AbstractC0293r abstractC0293r) {
        if (this.f1825r0 == null) {
            this.f1825r0 = new ArrayList();
        }
        this.f1825r0.add(abstractC0293r);
    }

    /* renamed from: h0 */
    public final void m1030h0(int i6) {
        if (this.f1780F) {
            return;
        }
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m == null) {
            Log.e("RecyclerView", "Cannot smooth scroll without a LayoutManager set. Call setLayoutManager with a non-null argument.");
        } else {
            abstractC0288m.mo939H0(this, i6);
        }
    }

    @Override // android.view.View
    public final boolean hasNestedScrollingParent() {
        return getScrollingChildHelper().m2161g(0);
    }

    /* renamed from: i */
    public final void m1031i(String str) {
        if (m1008O()) {
            if (str != null) {
                throw new IllegalStateException(str);
            }
            throw new IllegalStateException(C0174y.m489g(this, C0052a.m104h("Cannot call this method while RecyclerView is computing a layout or scrolling")));
        }
        if (this.f1790O > 0) {
            Log.w("RecyclerView", "Cannot call this method in a scroll callback. Scroll callbacks mightbe run during a measure & layout pass where you cannot change theRecyclerView data. Any method call that might change the structureof the RecyclerView or the adapter contents should be postponed tothe next frame.", new IllegalStateException(C0174y.m489g(this, C0052a.m104h(""))));
        }
    }

    /* renamed from: i0 */
    public final void m1032i0() {
        int i6 = this.f1776D + 1;
        this.f1776D = i6;
        if (i6 != 1 || this.f1780F) {
            return;
        }
        this.f1778E = false;
    }

    @Override // android.view.View
    public final boolean isAttachedToWindow() {
        return this.f1770A;
    }

    @Override // android.view.ViewGroup
    public final boolean isLayoutSuppressed() {
        return this.f1780F;
    }

    @Override // android.view.View
    public final boolean isNestedScrollingEnabled() {
        return getScrollingChildHelper().f4034d;
    }

    /* renamed from: j */
    public final void m1033j() {
        m1021b0();
        setScrollState(0);
    }

    /* renamed from: j0 */
    public final boolean m1034j0(int i6, int i7) {
        return getScrollingChildHelper().m2162h(i6, i7);
    }

    /* renamed from: k0 */
    public final void m1035k0(boolean z5) {
        if (this.f1776D < 1) {
            this.f1776D = 1;
        }
        if (!z5 && !this.f1780F) {
            this.f1778E = false;
        }
        if (this.f1776D == 1) {
            if (z5 && this.f1778E && !this.f1780F && this.f1832v != null && this.f1830u != null) {
                m1043q();
            }
            if (!this.f1780F) {
                this.f1778E = false;
            }
        }
        this.f1776D--;
    }

    /* renamed from: l */
    public final void m1036l() {
        int m1274h = this.f1816n.m1274h();
        for (int i6 = 0; i6 < m1274h; i6++) {
            AbstractC0277b0 m990K = m990K(this.f1816n.m1273g(i6));
            if (!m990K.m1076u()) {
                m990K.m1058c();
            }
        }
        C0295t c0295t = this.f1810k;
        int size = c0295t.f1917c.size();
        for (int i7 = 0; i7 < size; i7++) {
            c0295t.f1917c.get(i7).m1058c();
        }
        int size2 = c0295t.f1915a.size();
        for (int i8 = 0; i8 < size2; i8++) {
            c0295t.f1915a.get(i8).m1058c();
        }
        ArrayList<AbstractC0277b0> arrayList = c0295t.f1916b;
        if (arrayList != null) {
            int size3 = arrayList.size();
            for (int i9 = 0; i9 < size3; i9++) {
                c0295t.f1916b.get(i9).m1058c();
            }
        }
    }

    /* renamed from: l0 */
    public final void m1037l0(int i6) {
        getScrollingChildHelper().m2163i(i6);
    }

    /* renamed from: m */
    public final void m1038m(int i6, int i7) {
        boolean z5;
        EdgeEffect edgeEffect = this.f1792Q;
        if (edgeEffect == null || edgeEffect.isFinished() || i6 <= 0) {
            z5 = false;
        } else {
            this.f1792Q.onRelease();
            z5 = this.f1792Q.isFinished();
        }
        EdgeEffect edgeEffect2 = this.f1794S;
        if (edgeEffect2 != null && !edgeEffect2.isFinished() && i6 < 0) {
            this.f1794S.onRelease();
            z5 |= this.f1794S.isFinished();
        }
        EdgeEffect edgeEffect3 = this.f1793R;
        if (edgeEffect3 != null && !edgeEffect3.isFinished() && i7 > 0) {
            this.f1793R.onRelease();
            z5 |= this.f1793R.isFinished();
        }
        EdgeEffect edgeEffect4 = this.f1795T;
        if (edgeEffect4 != null && !edgeEffect4.isFinished() && i7 < 0) {
            this.f1795T.onRelease();
            z5 |= this.f1795T.isFinished();
        }
        if (z5) {
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            postInvalidateOnAnimation();
        }
    }

    /* renamed from: m0 */
    public final void m1039m0() {
        AbstractC0299x abstractC0299x;
        setScrollState(0);
        this.f1815m0.m1055c();
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m == null || (abstractC0299x = abstractC0288m.f1888g) == null) {
            return;
        }
        abstractC0299x.m1192d();
    }

    /* renamed from: n */
    public final void m1040n() {
        if (!this.f1774C || this.f1787L) {
            int i6 = C0003b.f5a;
            Trace.beginSection("RV FullInvalidate");
            m1043q();
            Trace.endSection();
            return;
        }
        if (this.f1814m.m1256g()) {
            C0308a c0308a = this.f1814m;
            int i7 = c0308a.f2012f;
            boolean z5 = false;
            if ((4 & i7) != 0) {
                if (!((11 & i7) != 0)) {
                    int i8 = C0003b.f5a;
                    Trace.beginSection("RV PartialInvalidate");
                    m1032i0();
                    m1012S();
                    this.f1814m.m1259j();
                    if (!this.f1778E) {
                        int m1271e = this.f1816n.m1271e();
                        int i9 = 0;
                        while (true) {
                            if (i9 < m1271e) {
                                AbstractC0277b0 m990K = m990K(this.f1816n.m1270d(i9));
                                if (m990K != null && !m990K.m1076u() && m990K.m1071p()) {
                                    z5 = true;
                                    break;
                                }
                                i9++;
                            } else {
                                break;
                            }
                        }
                        if (z5) {
                            m1043q();
                        } else {
                            this.f1814m.m1251b();
                        }
                    }
                    m1035k0(true);
                    m1013T(true);
                    Trace.endSection();
                }
            }
            if (c0308a.m1256g()) {
                int i10 = C0003b.f5a;
                Trace.beginSection("RV FullInvalidate");
                m1043q();
                Trace.endSection();
            }
        }
    }

    /* renamed from: o */
    public final void m1041o(int i6, int i7) {
        int paddingRight = getPaddingRight() + getPaddingLeft();
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        setMeasuredDimension(AbstractC0288m.m1114h(i6, paddingRight, getMinimumWidth()), AbstractC0288m.m1114h(i7, getPaddingBottom() + getPaddingTop(), getMinimumHeight()));
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onAttachedToWindow() {
        super.onAttachedToWindow();
        this.f1789N = 0;
        this.f1770A = true;
        this.f1774C = this.f1774C && !isLayoutRequested();
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m != null) {
            abstractC0288m.f1890i = true;
        }
        this.f1833v0 = false;
        ThreadLocal<RunnableC0323m> threadLocal = RunnableC0323m.f2126n;
        RunnableC0323m runnableC0323m = threadLocal.get();
        this.f1817n0 = runnableC0323m;
        if (runnableC0323m == null) {
            this.f1817n0 = new RunnableC0323m();
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            Display display = getDisplay();
            float f6 = 60.0f;
            if (!isInEditMode() && display != null) {
                float refreshRate = display.getRefreshRate();
                if (refreshRate >= 30.0f) {
                    f6 = refreshRate;
                }
            }
            RunnableC0323m runnableC0323m2 = this.f1817n0;
            runnableC0323m2.f2130l = (long) (1.0E9f / f6);
            threadLocal.set(runnableC0323m2);
        }
        this.f1817n0.f2128j.add(this);
    }

    /* JADX WARN: Type inference failed for: r0v2, types: [java.util.ArrayList, java.util.List<androidx.recyclerview.widget.RecyclerView$b0>] */
    @Override // android.view.ViewGroup, android.view.View
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        AbstractC0285j abstractC0285j = this.f1796U;
        if (abstractC0285j != null) {
            abstractC0285j.mo1106f();
        }
        m1039m0();
        this.f1770A = false;
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m != null) {
            abstractC0288m.f1890i = false;
            abstractC0288m.mo952W(this);
        }
        this.f1777D0.clear();
        removeCallbacks(this.f1779E0);
        Objects.requireNonNull(this.f1818o);
        while (C0313c0.a.f2037d.mo2056b() != null) {
        }
        RunnableC0323m runnableC0323m = this.f1817n0;
        if (runnableC0323m != null) {
            runnableC0323m.f2128j.remove(this);
            this.f1817n0 = null;
        }
    }

    @Override // android.view.View
    public final void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        int size = this.f1836x.size();
        for (int i6 = 0; i6 < size; i6++) {
            this.f1836x.get(i6).mo1110d(this);
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:20:0x0068  */
    @Override // android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean onGenericMotionEvent(android.view.MotionEvent r6) {
        /*
            r5 = this;
            androidx.recyclerview.widget.RecyclerView$m r0 = r5.f1832v
            r1 = 0
            if (r0 != 0) goto L6
            return r1
        L6:
            boolean r0 = r5.f1780F
            if (r0 == 0) goto Lb
            return r1
        Lb:
            int r0 = r6.getAction()
            r2 = 8
            if (r0 != r2) goto L77
            int r0 = r6.getSource()
            r0 = r0 & 2
            r2 = 0
            if (r0 == 0) goto L3e
            androidx.recyclerview.widget.RecyclerView$m r0 = r5.f1832v
            boolean r0 = r0.mo964f()
            if (r0 == 0) goto L2c
            r0 = 9
            float r0 = r6.getAxisValue(r0)
            float r0 = -r0
            goto L2d
        L2c:
            r0 = r2
        L2d:
            androidx.recyclerview.widget.RecyclerView$m r3 = r5.f1832v
            boolean r3 = r3.mo963e()
            if (r3 == 0) goto L3c
            r3 = 10
            float r3 = r6.getAxisValue(r3)
            goto L64
        L3c:
            r3 = r2
            goto L64
        L3e:
            int r0 = r6.getSource()
            r3 = 4194304(0x400000, float:5.877472E-39)
            r0 = r0 & r3
            if (r0 == 0) goto L62
            r0 = 26
            float r0 = r6.getAxisValue(r0)
            androidx.recyclerview.widget.RecyclerView$m r3 = r5.f1832v
            boolean r3 = r3.mo964f()
            if (r3 == 0) goto L57
            float r0 = -r0
            goto L3c
        L57:
            androidx.recyclerview.widget.RecyclerView$m r3 = r5.f1832v
            boolean r3 = r3.mo963e()
            if (r3 == 0) goto L62
            r3 = r0
            r0 = r2
            goto L64
        L62:
            r0 = r2
            r3 = r0
        L64:
            int r4 = (r0 > r2 ? 1 : (r0 == r2 ? 0 : -1))
            if (r4 != 0) goto L6c
            int r2 = (r3 > r2 ? 1 : (r3 == r2 ? 0 : -1))
            if (r2 == 0) goto L77
        L6c:
            float r2 = r5.f1809j0
            float r3 = r3 * r2
            int r2 = (int) r3
            float r3 = r5.f1811k0
            float r0 = r0 * r3
            int r0 = (int) r0
            r5.m1022c0(r2, r0, r6)
        L77:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.onGenericMotionEvent(android.view.MotionEvent):boolean");
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // android.view.ViewGroup
    public boolean onInterceptTouchEvent(MotionEvent motionEvent) {
        boolean z5;
        if (this.f1780F) {
            return false;
        }
        this.f1840z = null;
        if (m999D(motionEvent)) {
            m1033j();
            return true;
        }
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m == null) {
            return false;
        }
        boolean mo963e = abstractC0288m.mo963e();
        boolean mo964f = this.f1832v.mo964f();
        if (this.f1799a0 == null) {
            this.f1799a0 = VelocityTracker.obtain();
        }
        this.f1799a0.addMovement(motionEvent);
        int actionMasked = motionEvent.getActionMasked();
        int actionIndex = motionEvent.getActionIndex();
        if (actionMasked == 0) {
            if (this.f1782G) {
                this.f1782G = false;
            }
            this.f1798W = motionEvent.getPointerId(0);
            int x6 = (int) (motionEvent.getX() + 0.5f);
            this.f1802d0 = x6;
            this.f1800b0 = x6;
            int y2 = (int) (motionEvent.getY() + 0.5f);
            this.f1803e0 = y2;
            this.f1801c0 = y2;
            if (this.f1797V == 2) {
                getParent().requestDisallowInterceptTouchEvent(true);
                setScrollState(1);
                m1037l0(1);
            }
            int[] iArr = this.f1773B0;
            iArr[1] = 0;
            iArr[0] = 0;
            int i6 = mo963e;
            if (mo964f) {
                i6 = (mo963e ? 1 : 0) | 2;
            }
            m1034j0(i6, 0);
        } else if (actionMasked == 1) {
            this.f1799a0.clear();
            m1037l0(0);
        } else if (actionMasked == 2) {
            int findPointerIndex = motionEvent.findPointerIndex(this.f1798W);
            if (findPointerIndex < 0) {
                StringBuilder m104h = C0052a.m104h("Error processing scroll; pointer index for id ");
                m104h.append(this.f1798W);
                m104h.append(" not found. Did any MotionEvents get skipped?");
                Log.e("RecyclerView", m104h.toString());
                return false;
            }
            int x7 = (int) (motionEvent.getX(findPointerIndex) + 0.5f);
            int y6 = (int) (motionEvent.getY(findPointerIndex) + 0.5f);
            if (this.f1797V != 1) {
                int i7 = x7 - this.f1800b0;
                int i8 = y6 - this.f1801c0;
                if (mo963e == 0 || Math.abs(i7) <= this.f1804f0) {
                    z5 = false;
                } else {
                    this.f1802d0 = x7;
                    z5 = true;
                }
                if (mo964f && Math.abs(i8) > this.f1804f0) {
                    this.f1803e0 = y6;
                    z5 = true;
                }
                if (z5) {
                    setScrollState(1);
                }
            }
        } else if (actionMasked == 3) {
            m1033j();
        } else if (actionMasked == 5) {
            this.f1798W = motionEvent.getPointerId(actionIndex);
            int x8 = (int) (motionEvent.getX(actionIndex) + 0.5f);
            this.f1802d0 = x8;
            this.f1800b0 = x8;
            int y7 = (int) (motionEvent.getY(actionIndex) + 0.5f);
            this.f1803e0 = y7;
            this.f1801c0 = y7;
        } else if (actionMasked == 6) {
            m1014U(motionEvent);
        }
        return this.f1797V == 1;
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onLayout(boolean z5, int i6, int i7, int i8, int i9) {
        int i10 = C0003b.f5a;
        Trace.beginSection("RV OnLayout");
        m1043q();
        Trace.endSection();
        this.f1774C = true;
    }

    @Override // android.view.View
    public final void onMeasure(int i6, int i7) {
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m == null) {
            m1041o(i6, i7);
            return;
        }
        boolean z5 = false;
        if (abstractC0288m.mo946R()) {
            int mode = View.MeasureSpec.getMode(i6);
            int mode2 = View.MeasureSpec.getMode(i7);
            this.f1832v.m1144j0(i6, i7);
            if (mode == 1073741824 && mode2 == 1073741824) {
                z5 = true;
            }
            if (z5 || this.f1830u == null) {
                return;
            }
            if (this.f1821p0.f1943d == 1) {
                m1044r();
            }
            this.f1832v.m1159z0(i6, i7);
            this.f1821p0.f1948i = true;
            m1045s();
            this.f1832v.m1120C0(i6, i7);
            if (this.f1832v.mo938F0()) {
                this.f1832v.m1159z0(View.MeasureSpec.makeMeasureSpec(getMeasuredWidth(), 1073741824), View.MeasureSpec.makeMeasureSpec(getMeasuredHeight(), 1073741824));
                this.f1821p0.f1948i = true;
                m1045s();
                this.f1832v.m1120C0(i6, i7);
                return;
            }
            return;
        }
        if (this.f1772B) {
            this.f1832v.m1144j0(i6, i7);
            return;
        }
        if (this.f1784I) {
            m1032i0();
            m1012S();
            m1016W();
            m1013T(true);
            C0300y c0300y = this.f1821p0;
            if (c0300y.f1950k) {
                c0300y.f1946g = true;
            } else {
                this.f1814m.m1252c();
                this.f1821p0.f1946g = false;
            }
            this.f1784I = false;
            m1035k0(false);
        } else if (this.f1821p0.f1950k) {
            setMeasuredDimension(getMeasuredWidth(), getMeasuredHeight());
            return;
        }
        AbstractC0280e abstractC0280e = this.f1830u;
        if (abstractC0280e != null) {
            this.f1821p0.f1944e = abstractC0280e.mo1081c();
        } else {
            this.f1821p0.f1944e = 0;
        }
        m1032i0();
        this.f1832v.m1144j0(i6, i7);
        m1035k0(false);
        this.f1821p0.f1946g = false;
    }

    @Override // android.view.ViewGroup
    public final boolean onRequestFocusInDescendants(int i6, Rect rect) {
        if (m1008O()) {
            return false;
        }
        return super.onRequestFocusInDescendants(i6, rect);
    }

    @Override // android.view.View
    public final void onRestoreInstanceState(Parcelable parcelable) {
        Parcelable parcelable2;
        if (!(parcelable instanceof C0298w)) {
            super.onRestoreInstanceState(parcelable);
            return;
        }
        C0298w c0298w = (C0298w) parcelable;
        this.f1812l = c0298w;
        super.onRestoreInstanceState(c0298w.f4731j);
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m == null || (parcelable2 = this.f1812l.f1924l) == null) {
            return;
        }
        abstractC0288m.mo972k0(parcelable2);
    }

    @Override // android.view.View
    public final Parcelable onSaveInstanceState() {
        C0298w c0298w = new C0298w(super.onSaveInstanceState());
        C0298w c0298w2 = this.f1812l;
        if (c0298w2 != null) {
            c0298w.f1924l = c0298w2.f1924l;
        } else {
            AbstractC0288m abstractC0288m = this.f1832v;
            c0298w.f1924l = abstractC0288m != null ? abstractC0288m.mo974l0() : null;
        }
        return c0298w;
    }

    @Override // android.view.View
    public final void onSizeChanged(int i6, int i7, int i8, int i9) {
        super.onSizeChanged(i6, i7, i8, i9);
        if (i6 == i8 && i7 == i9) {
            return;
        }
        m1007N();
    }

    /* JADX WARN: Code restructure failed: missing block: B:220:0x0339, code lost:
    
        if (r0 < r3) goto L492;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:110:0x03be  */
    /* JADX WARN: Removed duplicated region for block: B:193:0x033f  */
    /* JADX WARN: Removed duplicated region for block: B:195:0x0349  */
    /* JADX WARN: Removed duplicated region for block: B:196:0x0341  */
    /* JADX WARN: Removed duplicated region for block: B:37:0x03ef  */
    /* JADX WARN: Removed duplicated region for block: B:40:0x03f7  */
    /* JADX WARN: Removed duplicated region for block: B:56:0x00f8  */
    /* JADX WARN: Removed duplicated region for block: B:63:0x010c  */
    @Override // android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public boolean onTouchEvent(android.view.MotionEvent r21) {
        /*
            Method dump skipped, instructions count: 1024
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.onTouchEvent(android.view.MotionEvent):boolean");
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<androidx.recyclerview.widget.RecyclerView$o>] */
    /* JADX WARN: Type inference failed for: r2v2, types: [java.util.ArrayList, java.util.List<androidx.recyclerview.widget.RecyclerView$o>] */
    /* renamed from: p */
    public final void m1042p(View view) {
        m990K(view);
        AbstractC0280e abstractC0280e = this.f1830u;
        ?? r22 = this.f1786K;
        if (r22 == 0) {
            return;
        }
        int size = r22.size();
        while (true) {
            size--;
            if (size < 0) {
                return;
            } else {
                ((InterfaceC0290o) this.f1786K.get(size)).mo1169b();
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:148:0x02f4, code lost:
    
        if (r15.f1816n.m1277k(getFocusedChild()) == false) goto L462;
     */
    /* JADX WARN: Removed duplicated region for block: B:122:0x0279  */
    /* JADX WARN: Removed duplicated region for block: B:125:0x0282  */
    /* JADX WARN: Removed duplicated region for block: B:132:0x02b9  */
    /* JADX WARN: Removed duplicated region for block: B:147:0x02ea  */
    /* JADX WARN: Removed duplicated region for block: B:163:0x03a0  */
    /* JADX WARN: Removed duplicated region for block: B:16:0x0071  */
    /* JADX WARN: Removed duplicated region for block: B:173:0x035e  */
    /* renamed from: q */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m1043q() {
        /*
            Method dump skipped, instructions count: 962
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.m1043q():void");
    }

    /* renamed from: r */
    public final void m1044r() {
        int id;
        View m998C;
        this.f1821p0.m1195a(1);
        m997B(this.f1821p0);
        this.f1821p0.f1948i = false;
        m1032i0();
        C0313c0 c0313c0 = this.f1818o;
        c0313c0.f2035a.clear();
        c0313c0.f2036b.m2665a();
        m1012S();
        m1016W();
        View focusedChild = (this.f1813l0 && hasFocus() && this.f1830u != null) ? getFocusedChild() : null;
        AbstractC0277b0 m1004J = (focusedChild == null || (m998C = m998C(focusedChild)) == null) ? null : m1004J(m998C);
        if (m1004J == null) {
            C0300y c0300y = this.f1821p0;
            c0300y.f1952m = -1L;
            c0300y.f1951l = -1;
            c0300y.f1953n = -1;
        } else {
            C0300y c0300y2 = this.f1821p0;
            c0300y2.f1952m = this.f1830u.f1872b ? m1004J.f1856e : -1L;
            c0300y2.f1951l = this.f1787L ? -1 : m1004J.m1068m() ? m1004J.f1855d : m1004J.m1060e();
            C0300y c0300y3 = this.f1821p0;
            View view = m1004J.f1852a;
            loop3: while (true) {
                id = view.getId();
                while (!view.isFocused() && (view instanceof ViewGroup) && view.hasFocus()) {
                    view = ((ViewGroup) view).getFocusedChild();
                    if (view.getId() != -1) {
                        break;
                    }
                }
            }
            c0300y3.f1953n = id;
        }
        C0300y c0300y4 = this.f1821p0;
        c0300y4.f1947h = c0300y4.f1949j && this.f1829t0;
        this.f1829t0 = false;
        this.f1827s0 = false;
        c0300y4.f1946g = c0300y4.f1950k;
        c0300y4.f1944e = this.f1830u.mo1081c();
        m1000E(this.f1839y0);
        if (this.f1821p0.f1949j) {
            int m1271e = this.f1816n.m1271e();
            for (int i6 = 0; i6 < m1271e; i6++) {
                AbstractC0277b0 m990K = m990K(this.f1816n.m1270d(i6));
                if (!m990K.m1076u() && (!m990K.m1066k() || this.f1830u.f1872b)) {
                    AbstractC0285j abstractC0285j = this.f1796U;
                    AbstractC0285j.m1101b(m990K);
                    m990K.m1062g();
                    Objects.requireNonNull(abstractC0285j);
                    AbstractC0285j.c cVar = new AbstractC0285j.c();
                    cVar.m1109a(m990K);
                    this.f1818o.m1294c(m990K, cVar);
                    if (this.f1821p0.f1947h && m990K.m1071p() && !m990K.m1068m() && !m990K.m1076u() && !m990K.m1066k()) {
                        this.f1818o.f2036b.m2672h(m1003I(m990K), m990K);
                    }
                }
            }
        }
        if (this.f1821p0.f1950k) {
            int m1274h = this.f1816n.m1274h();
            for (int i7 = 0; i7 < m1274h; i7++) {
                AbstractC0277b0 m990K2 = m990K(this.f1816n.m1273g(i7));
                if (!m990K2.m1076u() && m990K2.f1855d == -1) {
                    m990K2.f1855d = m990K2.f1854c;
                }
            }
            C0300y c0300y5 = this.f1821p0;
            boolean z5 = c0300y5.f1945f;
            c0300y5.f1945f = false;
            this.f1832v.mo912h0(this.f1810k, c0300y5);
            this.f1821p0.f1945f = z5;
            for (int i8 = 0; i8 < this.f1816n.m1271e(); i8++) {
                AbstractC0277b0 m990K3 = m990K(this.f1816n.m1270d(i8));
                if (!m990K3.m1076u()) {
                    C0313c0.a orDefault = this.f1818o.f2035a.getOrDefault(m990K3, null);
                    if (!((orDefault == null || (orDefault.f2038a & 4) == 0) ? false : true)) {
                        AbstractC0285j.m1101b(m990K3);
                        boolean m1063h = m990K3.m1063h(8192);
                        AbstractC0285j abstractC0285j2 = this.f1796U;
                        m990K3.m1062g();
                        Objects.requireNonNull(abstractC0285j2);
                        AbstractC0285j.c cVar2 = new AbstractC0285j.c();
                        cVar2.m1109a(m990K3);
                        if (m1063h) {
                            m1018Y(m990K3, cVar2);
                        } else {
                            C0313c0 c0313c02 = this.f1818o;
                            C0313c0.a orDefault2 = c0313c02.f2035a.getOrDefault(m990K3, null);
                            if (orDefault2 == null) {
                                orDefault2 = C0313c0.a.m1298a();
                                c0313c02.f2035a.put(m990K3, orDefault2);
                            }
                            orDefault2.f2038a |= 2;
                            orDefault2.f2039b = cVar2;
                        }
                    }
                }
            }
        }
        m1036l();
        m1013T(true);
        m1035k0(false);
        this.f1821p0.f1943d = 2;
    }

    @Override // android.view.ViewGroup
    public final void removeDetachedView(View view, boolean z5) {
        AbstractC0277b0 m990K = m990K(view);
        if (m990K != null) {
            if (m990K.m1070o()) {
                m990K.f1861j &= -257;
            } else if (!m990K.m1076u()) {
                StringBuilder sb = new StringBuilder();
                sb.append("Called removeDetachedView with a view which is not flagged as tmp detached.");
                sb.append(m990K);
                throw new IllegalArgumentException(C0174y.m489g(this, sb));
            }
        }
        view.clearAnimation();
        m1042p(view);
        super.removeDetachedView(view, z5);
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final void requestChildFocus(View view, View view2) {
        AbstractC0299x abstractC0299x = this.f1832v.f1888g;
        boolean z5 = true;
        if (!(abstractC0299x != null && abstractC0299x.f1929e) && !m1008O()) {
            z5 = false;
        }
        if (!z5 && view2 != null) {
            m1020a0(view, view2);
        }
        super.requestChildFocus(view, view2);
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final boolean requestChildRectangleOnScreen(View view, Rect rect, boolean z5) {
        return this.f1832v.mo1154t0(this, view, rect, z5, false);
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final void requestDisallowInterceptTouchEvent(boolean z5) {
        int size = this.f1838y.size();
        for (int i6 = 0; i6 < size; i6++) {
            this.f1838y.get(i6).mo1171b();
        }
        super.requestDisallowInterceptTouchEvent(z5);
    }

    @Override // android.view.View, android.view.ViewParent
    public final void requestLayout() {
        if (this.f1776D != 0 || this.f1780F) {
            this.f1778E = true;
        } else {
            super.requestLayout();
        }
    }

    /* renamed from: s */
    public final void m1045s() {
        m1032i0();
        m1012S();
        this.f1821p0.m1195a(6);
        this.f1814m.m1252c();
        this.f1821p0.f1944e = this.f1830u.mo1081c();
        C0300y c0300y = this.f1821p0;
        c0300y.f1942c = 0;
        c0300y.f1946g = false;
        this.f1832v.mo912h0(this.f1810k, c0300y);
        C0300y c0300y2 = this.f1821p0;
        c0300y2.f1945f = false;
        this.f1812l = null;
        c0300y2.f1949j = c0300y2.f1949j && this.f1796U != null;
        c0300y2.f1943d = 4;
        m1013T(true);
        m1035k0(false);
    }

    @Override // android.view.View
    public final void scrollBy(int i6, int i7) {
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m == null) {
            Log.e("RecyclerView", "Cannot scroll without a LayoutManager set. Call setLayoutManager with a non-null argument.");
            return;
        }
        if (this.f1780F) {
            return;
        }
        boolean mo963e = abstractC0288m.mo963e();
        boolean mo964f = this.f1832v.mo964f();
        if (mo963e || mo964f) {
            if (!mo963e) {
                i6 = 0;
            }
            if (!mo964f) {
                i7 = 0;
            }
            m1022c0(i6, i7, null);
        }
    }

    @Override // android.view.View
    public final void scrollTo(int i6, int i7) {
        Log.w("RecyclerView", "RecyclerView does not support scrolling to an absolute position. Use scrollToPosition instead");
    }

    @Override // android.view.View, android.view.accessibility.AccessibilityEventSource
    public final void sendAccessibilityEventUnchecked(AccessibilityEvent accessibilityEvent) {
        if (m1008O()) {
            int contentChangeTypes = accessibilityEvent != null ? accessibilityEvent.getContentChangeTypes() : 0;
            this.f1783H |= contentChangeTypes != 0 ? contentChangeTypes : 0;
            r1 = 1;
        }
        if (r1 != 0) {
            return;
        }
        super.sendAccessibilityEventUnchecked(accessibilityEvent);
    }

    public void setAccessibilityDelegateCompat(C0334x c0334x) {
        this.f1835w0 = c0334x;
        C0766p.m2187t(this, c0334x);
    }

    public void setAdapter(AbstractC0280e abstractC0280e) {
        setLayoutFrozen(false);
        AbstractC0280e abstractC0280e2 = this.f1830u;
        if (abstractC0280e2 != null) {
            abstractC0280e2.m1092n(this.f1808j);
            this.f1830u.mo1087i(this);
        }
        m1019Z();
        C0308a c0308a = this.f1814m;
        c0308a.m1261l(c0308a.f2008b);
        c0308a.m1261l(c0308a.f2009c);
        c0308a.f2012f = 0;
        AbstractC0280e abstractC0280e3 = this.f1830u;
        this.f1830u = abstractC0280e;
        if (abstractC0280e != null) {
            abstractC0280e.m1091m(this.f1808j);
            abstractC0280e.mo1084f(this);
        }
        C0295t c0295t = this.f1810k;
        AbstractC0280e abstractC0280e4 = this.f1830u;
        c0295t.m1177b();
        C0294s m1179d = c0295t.m1179d();
        Objects.requireNonNull(m1179d);
        if (abstractC0280e3 != null) {
            m1179d.f1910b--;
        }
        if (m1179d.f1910b == 0) {
            for (int i6 = 0; i6 < m1179d.f1909a.size(); i6++) {
                m1179d.f1909a.valueAt(i6).f1911a.clear();
            }
        }
        if (abstractC0280e4 != null) {
            m1179d.f1910b++;
        }
        this.f1821p0.f1945f = true;
        m1017X(false);
        requestLayout();
    }

    public void setChildDrawingOrderCallback(InterfaceC0283h interfaceC0283h) {
        if (interfaceC0283h == this.f1837x0) {
            return;
        }
        this.f1837x0 = interfaceC0283h;
        setChildrenDrawingOrderEnabled(interfaceC0283h != null);
    }

    @Override // android.view.ViewGroup
    public void setClipToPadding(boolean z5) {
        if (z5 != this.f1820p) {
            m1007N();
        }
        this.f1820p = z5;
        super.setClipToPadding(z5);
        if (this.f1774C) {
            requestLayout();
        }
    }

    public void setEdgeEffectFactory(C0284i c0284i) {
        Objects.requireNonNull(c0284i);
        this.f1791P = c0284i;
        m1007N();
    }

    public void setHasFixedSize(boolean z5) {
        this.f1772B = z5;
    }

    public void setItemAnimator(AbstractC0285j abstractC0285j) {
        AbstractC0285j abstractC0285j2 = this.f1796U;
        if (abstractC0285j2 != null) {
            abstractC0285j2.mo1106f();
            this.f1796U.f1873a = null;
        }
        this.f1796U = abstractC0285j;
        if (abstractC0285j != null) {
            abstractC0285j.f1873a = this.f1831u0;
        }
    }

    public void setItemViewCacheSize(int i6) {
        C0295t c0295t = this.f1810k;
        c0295t.f1919e = i6;
        c0295t.m1187l();
    }

    @Deprecated
    public void setLayoutFrozen(boolean z5) {
        suppressLayout(z5);
    }

    /* JADX WARN: Type inference failed for: r2v2, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    /* JADX WARN: Type inference failed for: r3v4, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    /* JADX WARN: Type inference failed for: r4v1, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    public void setLayoutManager(AbstractC0288m abstractC0288m) {
        if (abstractC0288m == this.f1832v) {
            return;
        }
        m1039m0();
        if (this.f1832v != null) {
            AbstractC0285j abstractC0285j = this.f1796U;
            if (abstractC0285j != null) {
                abstractC0285j.mo1106f();
            }
            this.f1832v.m1147o0(this.f1810k);
            this.f1832v.m1148p0(this.f1810k);
            this.f1810k.m1177b();
            if (this.f1770A) {
                AbstractC0288m abstractC0288m2 = this.f1832v;
                abstractC0288m2.f1890i = false;
                abstractC0288m2.mo952W(this);
            }
            this.f1832v.m1122D0(null);
            this.f1832v = null;
        } else {
            this.f1810k.m1177b();
        }
        C0310b c0310b = this.f1816n;
        C0310b.a aVar = c0310b.f2022b;
        aVar.f2024a = 0L;
        C0310b.a aVar2 = aVar.f2025b;
        if (aVar2 != null) {
            aVar2.m1285g();
        }
        int size = c0310b.f2023c.size();
        while (true) {
            size--;
            if (size < 0) {
                break;
            }
            C0310b.b bVar = c0310b.f2021a;
            View view = (View) c0310b.f2023c.get(size);
            C0332v c0332v = (C0332v) bVar;
            Objects.requireNonNull(c0332v);
            AbstractC0277b0 m990K = m990K(view);
            if (m990K != null) {
                c0332v.f2165a.m1026f0(m990K, m990K.f1867p);
                m990K.f1867p = 0;
            }
            c0310b.f2023c.remove(size);
        }
        C0332v c0332v2 = (C0332v) c0310b.f2021a;
        int m1343b = c0332v2.m1343b();
        for (int i6 = 0; i6 < m1343b; i6++) {
            View m1342a = c0332v2.m1342a(i6);
            c0332v2.f2165a.m1042p(m1342a);
            m1342a.clearAnimation();
        }
        c0332v2.f2165a.removeAllViews();
        this.f1832v = abstractC0288m;
        if (abstractC0288m != null) {
            if (abstractC0288m.f1883b != null) {
                StringBuilder sb = new StringBuilder();
                sb.append("LayoutManager ");
                sb.append(abstractC0288m);
                sb.append(" is already attached to a RecyclerView:");
                throw new IllegalArgumentException(C0174y.m489g(abstractC0288m.f1883b, sb));
            }
            abstractC0288m.m1122D0(this);
            if (this.f1770A) {
                this.f1832v.f1890i = true;
            }
        }
        this.f1810k.m1187l();
        requestLayout();
    }

    @Override // android.view.ViewGroup
    @Deprecated
    public void setLayoutTransition(LayoutTransition layoutTransition) {
        if (layoutTransition != null) {
            throw new IllegalArgumentException("Providing a LayoutTransition into RecyclerView is not supported. Please use setItemAnimator() instead for animating changes to the items in this RecyclerView");
        }
        super.setLayoutTransition(null);
    }

    @Override // android.view.View
    public void setNestedScrollingEnabled(boolean z5) {
        C0758h scrollingChildHelper = getScrollingChildHelper();
        if (scrollingChildHelper.f4034d) {
            View view = scrollingChildHelper.f4033c;
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            view.stopNestedScroll();
        }
        scrollingChildHelper.f4034d = z5;
    }

    public void setOnFlingListener(AbstractC0291p abstractC0291p) {
        this.f1805g0 = abstractC0291p;
    }

    @Deprecated
    public void setOnScrollListener(AbstractC0293r abstractC0293r) {
        this.f1823q0 = abstractC0293r;
    }

    public void setPreserveFocusAfterLayout(boolean z5) {
        this.f1813l0 = z5;
    }

    public void setRecycledViewPool(C0294s c0294s) {
        C0295t c0295t = this.f1810k;
        if (c0295t.f1921g != null) {
            r1.f1910b--;
        }
        c0295t.f1921g = c0294s;
        if (c0294s == null || RecyclerView.this.getAdapter() == null) {
            return;
        }
        c0295t.f1921g.f1910b++;
    }

    public void setRecyclerListener(InterfaceC0296u interfaceC0296u) {
        this.f1834w = interfaceC0296u;
    }

    /* JADX WARN: Type inference failed for: r0v4, types: [java.util.ArrayList, java.util.List<androidx.recyclerview.widget.RecyclerView$r>] */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.util.ArrayList, java.util.List<androidx.recyclerview.widget.RecyclerView$r>] */
    void setScrollState(int i6) {
        AbstractC0299x abstractC0299x;
        if (i6 == this.f1797V) {
            return;
        }
        this.f1797V = i6;
        if (i6 != 2) {
            this.f1815m0.m1055c();
            AbstractC0288m abstractC0288m = this.f1832v;
            if (abstractC0288m != null && (abstractC0299x = abstractC0288m.f1888g) != null) {
                abstractC0299x.m1192d();
            }
        }
        AbstractC0288m abstractC0288m2 = this.f1832v;
        if (abstractC0288m2 != null) {
            abstractC0288m2.mo1145m0(i6);
        }
        AbstractC0293r abstractC0293r = this.f1823q0;
        if (abstractC0293r != null) {
            abstractC0293r.mo1173a(this, i6);
        }
        ?? r0 = this.f1825r0;
        if (r0 == 0) {
            return;
        }
        int size = r0.size();
        while (true) {
            size--;
            if (size < 0) {
                return;
            } else {
                ((AbstractC0293r) this.f1825r0.get(size)).mo1173a(this, i6);
            }
        }
    }

    public void setScrollingTouchSlop(int i6) {
        int scaledTouchSlop;
        ViewConfiguration viewConfiguration = ViewConfiguration.get(getContext());
        if (i6 != 0) {
            if (i6 == 1) {
                scaledTouchSlop = viewConfiguration.getScaledPagingTouchSlop();
                this.f1804f0 = scaledTouchSlop;
            } else {
                Log.w("RecyclerView", "setScrollingTouchSlop(): bad argument constant " + i6 + "; using default value");
            }
        }
        scaledTouchSlop = viewConfiguration.getScaledTouchSlop();
        this.f1804f0 = scaledTouchSlop;
    }

    public void setViewCacheExtension(AbstractC0301z abstractC0301z) {
        Objects.requireNonNull(this.f1810k);
    }

    @Override // android.view.View
    public final boolean startNestedScroll(int i6) {
        return getScrollingChildHelper().m2162h(i6, 0);
    }

    @Override // android.view.View
    public final void stopNestedScroll() {
        getScrollingChildHelper().m2163i(0);
    }

    @Override // android.view.ViewGroup
    public final void suppressLayout(boolean z5) {
        if (z5 != this.f1780F) {
            m1031i("Do not suppressLayout in layout or scroll");
            if (z5) {
                long uptimeMillis = SystemClock.uptimeMillis();
                onTouchEvent(MotionEvent.obtain(uptimeMillis, uptimeMillis, 3, 0.0f, 0.0f, 0));
                this.f1780F = true;
                this.f1782G = true;
                m1039m0();
                return;
            }
            this.f1780F = false;
            if (this.f1778E && this.f1832v != null && this.f1830u != null) {
                requestLayout();
            }
            this.f1778E = false;
        }
    }

    /* renamed from: t */
    public final boolean m1046t(int i6, int i7, int[] iArr, int[] iArr2, int i8) {
        return getScrollingChildHelper().m2157c(i6, i7, iArr, null, 1);
    }

    /* renamed from: u */
    public final void m1047u(int i6, int i7, int i8, int i9, int[] iArr, int i10, int[] iArr2) {
        getScrollingChildHelper().m2159e(i6, i7, i8, i9, null, 1, iArr2);
    }

    /* JADX WARN: Type inference failed for: r0v4, types: [java.util.ArrayList, java.util.List<androidx.recyclerview.widget.RecyclerView$r>] */
    /* JADX WARN: Type inference failed for: r1v1, types: [java.util.ArrayList, java.util.List<androidx.recyclerview.widget.RecyclerView$r>] */
    /* renamed from: v */
    public final void m1048v(int i6, int i7) {
        this.f1790O++;
        int scrollX = getScrollX();
        int scrollY = getScrollY();
        onScrollChanged(scrollX, scrollY, scrollX - i6, scrollY - i7);
        AbstractC0293r abstractC0293r = this.f1823q0;
        if (abstractC0293r != null) {
            abstractC0293r.mo1174b(this, i6, i7);
        }
        ?? r0 = this.f1825r0;
        if (r0 != 0) {
            int size = r0.size();
            while (true) {
                size--;
                if (size < 0) {
                    break;
                } else {
                    ((AbstractC0293r) this.f1825r0.get(size)).mo1174b(this, i6, i7);
                }
            }
        }
        this.f1790O--;
    }

    /* renamed from: w */
    public final void m1049w() {
        int measuredWidth;
        int measuredHeight;
        if (this.f1795T != null) {
            return;
        }
        EdgeEffect m1100a = this.f1791P.m1100a(this);
        this.f1795T = m1100a;
        if (this.f1820p) {
            measuredWidth = (getMeasuredWidth() - getPaddingLeft()) - getPaddingRight();
            measuredHeight = (getMeasuredHeight() - getPaddingTop()) - getPaddingBottom();
        } else {
            measuredWidth = getMeasuredWidth();
            measuredHeight = getMeasuredHeight();
        }
        m1100a.setSize(measuredWidth, measuredHeight);
    }

    /* renamed from: x */
    public final void m1050x() {
        int measuredHeight;
        int measuredWidth;
        if (this.f1792Q != null) {
            return;
        }
        EdgeEffect m1100a = this.f1791P.m1100a(this);
        this.f1792Q = m1100a;
        if (this.f1820p) {
            measuredHeight = (getMeasuredHeight() - getPaddingTop()) - getPaddingBottom();
            measuredWidth = (getMeasuredWidth() - getPaddingLeft()) - getPaddingRight();
        } else {
            measuredHeight = getMeasuredHeight();
            measuredWidth = getMeasuredWidth();
        }
        m1100a.setSize(measuredHeight, measuredWidth);
    }

    /* renamed from: y */
    public final void m1051y() {
        int measuredHeight;
        int measuredWidth;
        if (this.f1794S != null) {
            return;
        }
        EdgeEffect m1100a = this.f1791P.m1100a(this);
        this.f1794S = m1100a;
        if (this.f1820p) {
            measuredHeight = (getMeasuredHeight() - getPaddingTop()) - getPaddingBottom();
            measuredWidth = (getMeasuredWidth() - getPaddingLeft()) - getPaddingRight();
        } else {
            measuredHeight = getMeasuredHeight();
            measuredWidth = getMeasuredWidth();
        }
        m1100a.setSize(measuredHeight, measuredWidth);
    }

    /* renamed from: z */
    public final void m1052z() {
        int measuredWidth;
        int measuredHeight;
        if (this.f1793R != null) {
            return;
        }
        EdgeEffect m1100a = this.f1791P.m1100a(this);
        this.f1793R = m1100a;
        if (this.f1820p) {
            measuredWidth = (getMeasuredWidth() - getPaddingLeft()) - getPaddingRight();
            measuredHeight = (getMeasuredHeight() - getPaddingTop()) - getPaddingBottom();
        } else {
            measuredWidth = getMeasuredWidth();
            measuredHeight = getMeasuredHeight();
        }
        m1100a.setSize(measuredWidth, measuredHeight);
    }

    @Override // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateLayoutParams(ViewGroup.LayoutParams layoutParams) {
        AbstractC0288m abstractC0288m = this.f1832v;
        if (abstractC0288m != null) {
            return abstractC0288m.mo927v(layoutParams);
        }
        throw new IllegalStateException(C0174y.m489g(this, C0052a.m104h("RecyclerView has no LayoutManager")));
    }
}
