package androidx.recyclerview.widget;

import android.animation.TimeInterpolator;
import android.animation.ValueAnimator;
import android.view.View;
import androidx.activity.result.C0052a;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.List;

/* renamed from: androidx.recyclerview.widget.k */
/* loaded from: classes.dex */
public final class C0321k extends AbstractC0336z {

    /* renamed from: s */
    public static TimeInterpolator f2067s;

    /* renamed from: h */
    public ArrayList<RecyclerView.AbstractC0277b0> f2068h = new ArrayList<>();

    /* renamed from: i */
    public ArrayList<RecyclerView.AbstractC0277b0> f2069i = new ArrayList<>();

    /* renamed from: j */
    public ArrayList<b> f2070j = new ArrayList<>();

    /* renamed from: k */
    public ArrayList<a> f2071k = new ArrayList<>();

    /* renamed from: l */
    public ArrayList<ArrayList<RecyclerView.AbstractC0277b0>> f2072l = new ArrayList<>();

    /* renamed from: m */
    public ArrayList<ArrayList<b>> f2073m = new ArrayList<>();

    /* renamed from: n */
    public ArrayList<ArrayList<a>> f2074n = new ArrayList<>();

    /* renamed from: o */
    public ArrayList<RecyclerView.AbstractC0277b0> f2075o = new ArrayList<>();

    /* renamed from: p */
    public ArrayList<RecyclerView.AbstractC0277b0> f2076p = new ArrayList<>();

    /* renamed from: q */
    public ArrayList<RecyclerView.AbstractC0277b0> f2077q = new ArrayList<>();

    /* renamed from: r */
    public ArrayList<RecyclerView.AbstractC0277b0> f2078r = new ArrayList<>();

    /* renamed from: androidx.recyclerview.widget.k$a */
    public static class a {

        /* renamed from: a */
        public RecyclerView.AbstractC0277b0 f2079a;

        /* renamed from: b */
        public RecyclerView.AbstractC0277b0 f2080b;

        /* renamed from: c */
        public int f2081c;

        /* renamed from: d */
        public int f2082d;

        /* renamed from: e */
        public int f2083e;

        /* renamed from: f */
        public int f2084f;

        public a(RecyclerView.AbstractC0277b0 abstractC0277b0, RecyclerView.AbstractC0277b0 abstractC0277b02, int i6, int i7, int i8, int i9) {
            this.f2079a = abstractC0277b0;
            this.f2080b = abstractC0277b02;
            this.f2081c = i6;
            this.f2082d = i7;
            this.f2083e = i8;
            this.f2084f = i9;
        }

        public final String toString() {
            StringBuilder m104h = C0052a.m104h("ChangeInfo{oldHolder=");
            m104h.append(this.f2079a);
            m104h.append(", newHolder=");
            m104h.append(this.f2080b);
            m104h.append(", fromX=");
            m104h.append(this.f2081c);
            m104h.append(", fromY=");
            m104h.append(this.f2082d);
            m104h.append(", toX=");
            m104h.append(this.f2083e);
            m104h.append(", toY=");
            m104h.append(this.f2084f);
            m104h.append('}');
            return m104h.toString();
        }
    }

    /* renamed from: androidx.recyclerview.widget.k$b */
    public static class b {

        /* renamed from: a */
        public RecyclerView.AbstractC0277b0 f2085a;

        /* renamed from: b */
        public int f2086b;

        /* renamed from: c */
        public int f2087c;

        /* renamed from: d */
        public int f2088d;

        /* renamed from: e */
        public int f2089e;

        public b(RecyclerView.AbstractC0277b0 abstractC0277b0, int i6, int i7, int i8, int i9) {
            this.f2085a = abstractC0277b0;
            this.f2086b = i6;
            this.f2087c = i7;
            this.f2088d = i8;
            this.f2089e = i9;
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0285j
    /* renamed from: e */
    public final void mo1105e(RecyclerView.AbstractC0277b0 abstractC0277b0) {
        View view = abstractC0277b0.f1852a;
        view.animate().cancel();
        int size = this.f2070j.size();
        while (true) {
            size--;
            if (size < 0) {
                break;
            }
            if (this.f2070j.get(size).f2085a == abstractC0277b0) {
                view.setTranslationY(0.0f);
                view.setTranslationX(0.0f);
                m1103c(abstractC0277b0);
                this.f2070j.remove(size);
            }
        }
        m1303k(this.f2071k, abstractC0277b0);
        if (this.f2068h.remove(abstractC0277b0)) {
            view.setAlpha(1.0f);
            m1103c(abstractC0277b0);
        }
        if (this.f2069i.remove(abstractC0277b0)) {
            view.setAlpha(1.0f);
            m1103c(abstractC0277b0);
        }
        for (int size2 = this.f2074n.size() - 1; size2 >= 0; size2--) {
            ArrayList<a> arrayList = this.f2074n.get(size2);
            m1303k(arrayList, abstractC0277b0);
            if (arrayList.isEmpty()) {
                this.f2074n.remove(size2);
            }
        }
        for (int size3 = this.f2073m.size() - 1; size3 >= 0; size3--) {
            ArrayList<b> arrayList2 = this.f2073m.get(size3);
            int size4 = arrayList2.size() - 1;
            while (true) {
                if (size4 < 0) {
                    break;
                }
                if (arrayList2.get(size4).f2085a == abstractC0277b0) {
                    view.setTranslationY(0.0f);
                    view.setTranslationX(0.0f);
                    m1103c(abstractC0277b0);
                    arrayList2.remove(size4);
                    if (arrayList2.isEmpty()) {
                        this.f2073m.remove(size3);
                    }
                } else {
                    size4--;
                }
            }
        }
        for (int size5 = this.f2072l.size() - 1; size5 >= 0; size5--) {
            ArrayList<RecyclerView.AbstractC0277b0> arrayList3 = this.f2072l.get(size5);
            if (arrayList3.remove(abstractC0277b0)) {
                view.setAlpha(1.0f);
                m1103c(abstractC0277b0);
                if (arrayList3.isEmpty()) {
                    this.f2072l.remove(size5);
                }
            }
        }
        this.f2077q.remove(abstractC0277b0);
        this.f2075o.remove(abstractC0277b0);
        this.f2078r.remove(abstractC0277b0);
        this.f2076p.remove(abstractC0277b0);
        m1302j();
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0285j
    /* renamed from: f */
    public final void mo1106f() {
        int size = this.f2070j.size();
        while (true) {
            size--;
            if (size < 0) {
                break;
            }
            b bVar = this.f2070j.get(size);
            View view = bVar.f2085a.f1852a;
            view.setTranslationY(0.0f);
            view.setTranslationX(0.0f);
            m1103c(bVar.f2085a);
            this.f2070j.remove(size);
        }
        int size2 = this.f2068h.size();
        while (true) {
            size2--;
            if (size2 < 0) {
                break;
            }
            m1103c(this.f2068h.get(size2));
            this.f2068h.remove(size2);
        }
        int size3 = this.f2069i.size();
        while (true) {
            size3--;
            if (size3 < 0) {
                break;
            }
            RecyclerView.AbstractC0277b0 abstractC0277b0 = this.f2069i.get(size3);
            abstractC0277b0.f1852a.setAlpha(1.0f);
            m1103c(abstractC0277b0);
            this.f2069i.remove(size3);
        }
        int size4 = this.f2071k.size();
        while (true) {
            size4--;
            if (size4 < 0) {
                break;
            }
            a aVar = this.f2071k.get(size4);
            RecyclerView.AbstractC0277b0 abstractC0277b02 = aVar.f2079a;
            if (abstractC0277b02 != null) {
                m1304l(aVar, abstractC0277b02);
            }
            RecyclerView.AbstractC0277b0 abstractC0277b03 = aVar.f2080b;
            if (abstractC0277b03 != null) {
                m1304l(aVar, abstractC0277b03);
            }
        }
        this.f2071k.clear();
        if (!mo1107g()) {
            return;
        }
        int size5 = this.f2073m.size();
        while (true) {
            size5--;
            if (size5 < 0) {
                break;
            }
            ArrayList<b> arrayList = this.f2073m.get(size5);
            int size6 = arrayList.size();
            while (true) {
                size6--;
                if (size6 >= 0) {
                    b bVar2 = arrayList.get(size6);
                    View view2 = bVar2.f2085a.f1852a;
                    view2.setTranslationY(0.0f);
                    view2.setTranslationX(0.0f);
                    m1103c(bVar2.f2085a);
                    arrayList.remove(size6);
                    if (arrayList.isEmpty()) {
                        this.f2073m.remove(arrayList);
                    }
                }
            }
        }
        int size7 = this.f2072l.size();
        while (true) {
            size7--;
            if (size7 < 0) {
                break;
            }
            ArrayList<RecyclerView.AbstractC0277b0> arrayList2 = this.f2072l.get(size7);
            int size8 = arrayList2.size();
            while (true) {
                size8--;
                if (size8 >= 0) {
                    RecyclerView.AbstractC0277b0 abstractC0277b04 = arrayList2.get(size8);
                    abstractC0277b04.f1852a.setAlpha(1.0f);
                    m1103c(abstractC0277b04);
                    arrayList2.remove(size8);
                    if (arrayList2.isEmpty()) {
                        this.f2072l.remove(arrayList2);
                    }
                }
            }
        }
        int size9 = this.f2074n.size();
        while (true) {
            size9--;
            if (size9 < 0) {
                m1301i(this.f2077q);
                m1301i(this.f2076p);
                m1301i(this.f2075o);
                m1301i(this.f2078r);
                m1104d();
                return;
            }
            ArrayList<a> arrayList3 = this.f2074n.get(size9);
            int size10 = arrayList3.size();
            while (true) {
                size10--;
                if (size10 >= 0) {
                    a aVar2 = arrayList3.get(size10);
                    RecyclerView.AbstractC0277b0 abstractC0277b05 = aVar2.f2079a;
                    if (abstractC0277b05 != null) {
                        m1304l(aVar2, abstractC0277b05);
                    }
                    RecyclerView.AbstractC0277b0 abstractC0277b06 = aVar2.f2080b;
                    if (abstractC0277b06 != null) {
                        m1304l(aVar2, abstractC0277b06);
                    }
                    if (arrayList3.isEmpty()) {
                        this.f2074n.remove(arrayList3);
                    }
                }
            }
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0285j
    /* renamed from: g */
    public final boolean mo1107g() {
        return (this.f2069i.isEmpty() && this.f2071k.isEmpty() && this.f2070j.isEmpty() && this.f2068h.isEmpty() && this.f2076p.isEmpty() && this.f2077q.isEmpty() && this.f2075o.isEmpty() && this.f2078r.isEmpty() && this.f2073m.isEmpty() && this.f2072l.isEmpty() && this.f2074n.isEmpty()) ? false : true;
    }

    @Override // androidx.recyclerview.widget.AbstractC0336z
    /* renamed from: h */
    public final boolean mo1300h(RecyclerView.AbstractC0277b0 abstractC0277b0, int i6, int i7, int i8, int i9) {
        View view = abstractC0277b0.f1852a;
        int translationX = i6 + ((int) view.getTranslationX());
        int translationY = i7 + ((int) abstractC0277b0.f1852a.getTranslationY());
        m1305m(abstractC0277b0);
        int i10 = i8 - translationX;
        int i11 = i9 - translationY;
        if (i10 == 0 && i11 == 0) {
            m1103c(abstractC0277b0);
            return false;
        }
        if (i10 != 0) {
            view.setTranslationX(-i10);
        }
        if (i11 != 0) {
            view.setTranslationY(-i11);
        }
        this.f2070j.add(new b(abstractC0277b0, translationX, translationY, i8, i9));
        return true;
    }

    /* renamed from: i */
    public final void m1301i(List<RecyclerView.AbstractC0277b0> list) {
        for (int size = list.size() - 1; size >= 0; size--) {
            list.get(size).f1852a.animate().cancel();
        }
    }

    /* renamed from: j */
    public final void m1302j() {
        if (mo1107g()) {
            return;
        }
        m1104d();
    }

    /* renamed from: k */
    public final void m1303k(List<a> list, RecyclerView.AbstractC0277b0 abstractC0277b0) {
        int size = list.size();
        while (true) {
            size--;
            if (size < 0) {
                return;
            }
            a aVar = list.get(size);
            if (m1304l(aVar, abstractC0277b0) && aVar.f2079a == null && aVar.f2080b == null) {
                list.remove(aVar);
            }
        }
    }

    /* renamed from: l */
    public final boolean m1304l(a aVar, RecyclerView.AbstractC0277b0 abstractC0277b0) {
        if (aVar.f2080b == abstractC0277b0) {
            aVar.f2080b = null;
        } else {
            if (aVar.f2079a != abstractC0277b0) {
                return false;
            }
            aVar.f2079a = null;
        }
        abstractC0277b0.f1852a.setAlpha(1.0f);
        abstractC0277b0.f1852a.setTranslationX(0.0f);
        abstractC0277b0.f1852a.setTranslationY(0.0f);
        m1103c(abstractC0277b0);
        return true;
    }

    /* renamed from: m */
    public final void m1305m(RecyclerView.AbstractC0277b0 abstractC0277b0) {
        if (f2067s == null) {
            f2067s = new ValueAnimator().getInterpolator();
        }
        abstractC0277b0.f1852a.animate().setInterpolator(f2067s);
        mo1105e(abstractC0277b0);
    }
}
