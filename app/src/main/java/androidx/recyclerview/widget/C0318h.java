package androidx.recyclerview.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.view.View;
import android.view.ViewPropertyAnimator;
import androidx.recyclerview.widget.RecyclerView;
import java.util.Objects;

/* renamed from: androidx.recyclerview.widget.h */
/* loaded from: classes.dex */
public final class C0318h extends AnimatorListenerAdapter {

    /* renamed from: a */
    public final /* synthetic */ RecyclerView.AbstractC0277b0 f2053a;

    /* renamed from: b */
    public final /* synthetic */ int f2054b;

    /* renamed from: c */
    public final /* synthetic */ View f2055c;

    /* renamed from: d */
    public final /* synthetic */ int f2056d;

    /* renamed from: e */
    public final /* synthetic */ ViewPropertyAnimator f2057e;

    /* renamed from: f */
    public final /* synthetic */ C0321k f2058f;

    public C0318h(C0321k c0321k, RecyclerView.AbstractC0277b0 abstractC0277b0, int i6, View view, int i7, ViewPropertyAnimator viewPropertyAnimator) {
        this.f2058f = c0321k;
        this.f2053a = abstractC0277b0;
        this.f2054b = i6;
        this.f2055c = view;
        this.f2056d = i7;
        this.f2057e = viewPropertyAnimator;
    }

    @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
    public final void onAnimationCancel(Animator animator) {
        if (this.f2054b != 0) {
            this.f2055c.setTranslationX(0.0f);
        }
        if (this.f2056d != 0) {
            this.f2055c.setTranslationY(0.0f);
        }
    }

    @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
    public final void onAnimationEnd(Animator animator) {
        this.f2057e.setListener(null);
        this.f2058f.m1103c(this.f2053a);
        this.f2058f.f2076p.remove(this.f2053a);
        this.f2058f.m1302j();
    }

    @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
    public final void onAnimationStart(Animator animator) {
        Objects.requireNonNull(this.f2058f);
    }
}
