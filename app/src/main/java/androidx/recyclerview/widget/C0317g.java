package androidx.recyclerview.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.view.View;
import android.view.ViewPropertyAnimator;
import androidx.recyclerview.widget.RecyclerView;
import java.util.Objects;

/* renamed from: androidx.recyclerview.widget.g */
/* loaded from: classes.dex */
public final class C0317g extends AnimatorListenerAdapter {

    /* renamed from: a */
    public final /* synthetic */ RecyclerView.AbstractC0277b0 f2049a;

    /* renamed from: b */
    public final /* synthetic */ View f2050b;

    /* renamed from: c */
    public final /* synthetic */ ViewPropertyAnimator f2051c;

    /* renamed from: d */
    public final /* synthetic */ C0321k f2052d;

    public C0317g(C0321k c0321k, RecyclerView.AbstractC0277b0 abstractC0277b0, View view, ViewPropertyAnimator viewPropertyAnimator) {
        this.f2052d = c0321k;
        this.f2049a = abstractC0277b0;
        this.f2050b = view;
        this.f2051c = viewPropertyAnimator;
    }

    @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
    public final void onAnimationCancel(Animator animator) {
        this.f2050b.setAlpha(1.0f);
    }

    @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
    public final void onAnimationEnd(Animator animator) {
        this.f2051c.setListener(null);
        this.f2052d.m1103c(this.f2049a);
        this.f2052d.f2075o.remove(this.f2049a);
        this.f2052d.m1302j();
    }

    @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
    public final void onAnimationStart(Animator animator) {
        Objects.requireNonNull(this.f2052d);
    }
}
