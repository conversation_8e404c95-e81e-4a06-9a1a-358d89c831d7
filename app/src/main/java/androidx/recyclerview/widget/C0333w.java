package androidx.recyclerview.widget;

import android.view.View;
import androidx.recyclerview.widget.C0308a;
import androidx.recyclerview.widget.RecyclerView;

/* renamed from: androidx.recyclerview.widget.w */
/* loaded from: classes.dex */
public final class C0333w implements C0308a.a {

    /* renamed from: a */
    public final /* synthetic */ RecyclerView f2166a;

    public C0333w(RecyclerView recyclerView) {
        this.f2166a = recyclerView;
    }

    /* renamed from: a */
    public final void m1346a(C0308a.b bVar) {
        int i6 = bVar.f2013a;
        if (i6 == 1) {
            this.f2166a.f1832v.mo904c0(bVar.f2014b, bVar.f2016d);
            return;
        }
        if (i6 == 2) {
            this.f2166a.f1832v.mo908f0(bVar.f2014b, bVar.f2016d);
        } else if (i6 == 4) {
            this.f2166a.f1832v.mo911g0(bVar.f2014b, bVar.f2016d);
        } else {
            if (i6 != 8) {
                return;
            }
            this.f2166a.f1832v.mo906e0(bVar.f2014b, bVar.f2016d);
        }
    }

    /* renamed from: b */
    public final RecyclerView.AbstractC0277b0 m1347b(int i6) {
        RecyclerView recyclerView = this.f2166a;
        int m1274h = recyclerView.f1816n.m1274h();
        int i7 = 0;
        RecyclerView.AbstractC0277b0 abstractC0277b0 = null;
        while (true) {
            if (i7 >= m1274h) {
                break;
            }
            RecyclerView.AbstractC0277b0 m990K = RecyclerView.m990K(recyclerView.f1816n.m1273g(i7));
            if (m990K != null && !m990K.m1068m() && m990K.f1854c == i6) {
                if (!recyclerView.f1816n.m1277k(m990K.f1852a)) {
                    abstractC0277b0 = m990K;
                    break;
                }
                abstractC0277b0 = m990K;
            }
            i7++;
        }
        if (abstractC0277b0 == null || this.f2166a.f1816n.m1277k(abstractC0277b0.f1852a)) {
            return null;
        }
        return abstractC0277b0;
    }

    /* renamed from: c */
    public final void m1348c(int i6, int i7, Object obj) {
        int i8;
        int i9;
        RecyclerView recyclerView = this.f2166a;
        int m1274h = recyclerView.f1816n.m1274h();
        int i10 = i7 + i6;
        for (int i11 = 0; i11 < m1274h; i11++) {
            View m1273g = recyclerView.f1816n.m1273g(i11);
            RecyclerView.AbstractC0277b0 m990K = RecyclerView.m990K(m1273g);
            if (m990K != null && !m990K.m1076u() && (i9 = m990K.f1854c) >= i6 && i9 < i10) {
                m990K.m1057b(2);
                m990K.m1056a(obj);
                ((RecyclerView.C0289n) m1273g.getLayoutParams()).f1907c = true;
            }
        }
        RecyclerView.C0295t c0295t = recyclerView.f1810k;
        int size = c0295t.f1917c.size();
        while (true) {
            size--;
            if (size < 0) {
                this.f2166a.f1829t0 = true;
                return;
            }
            RecyclerView.AbstractC0277b0 abstractC0277b0 = c0295t.f1917c.get(size);
            if (abstractC0277b0 != null && (i8 = abstractC0277b0.f1854c) >= i6 && i8 < i10) {
                abstractC0277b0.m1057b(2);
                c0295t.m1181f(size);
            }
        }
    }

    /* renamed from: d */
    public final void m1349d(int i6, int i7) {
        RecyclerView recyclerView = this.f2166a;
        int m1274h = recyclerView.f1816n.m1274h();
        for (int i8 = 0; i8 < m1274h; i8++) {
            RecyclerView.AbstractC0277b0 m990K = RecyclerView.m990K(recyclerView.f1816n.m1273g(i8));
            if (m990K != null && !m990K.m1076u() && m990K.f1854c >= i6) {
                m990K.m1072q(i7, false);
                recyclerView.f1821p0.f1945f = true;
            }
        }
        RecyclerView.C0295t c0295t = recyclerView.f1810k;
        int size = c0295t.f1917c.size();
        for (int i9 = 0; i9 < size; i9++) {
            RecyclerView.AbstractC0277b0 abstractC0277b0 = c0295t.f1917c.get(i9);
            if (abstractC0277b0 != null && abstractC0277b0.f1854c >= i6) {
                abstractC0277b0.m1072q(i7, true);
            }
        }
        recyclerView.requestLayout();
        this.f2166a.f1827s0 = true;
    }

    /* renamed from: e */
    public final void m1350e(int i6, int i7) {
        int i8;
        int i9;
        int i10;
        int i11;
        int i12;
        int i13;
        int i14;
        RecyclerView recyclerView = this.f2166a;
        int m1274h = recyclerView.f1816n.m1274h();
        int i15 = -1;
        if (i6 < i7) {
            i9 = i6;
            i8 = i7;
            i10 = -1;
        } else {
            i8 = i6;
            i9 = i7;
            i10 = 1;
        }
        for (int i16 = 0; i16 < m1274h; i16++) {
            RecyclerView.AbstractC0277b0 m990K = RecyclerView.m990K(recyclerView.f1816n.m1273g(i16));
            if (m990K != null && (i14 = m990K.f1854c) >= i9 && i14 <= i8) {
                if (i14 == i6) {
                    m990K.m1072q(i7 - i6, false);
                } else {
                    m990K.m1072q(i10, false);
                }
                recyclerView.f1821p0.f1945f = true;
            }
        }
        RecyclerView.C0295t c0295t = recyclerView.f1810k;
        if (i6 < i7) {
            i12 = i6;
            i11 = i7;
        } else {
            i11 = i6;
            i12 = i7;
            i15 = 1;
        }
        int size = c0295t.f1917c.size();
        for (int i17 = 0; i17 < size; i17++) {
            RecyclerView.AbstractC0277b0 abstractC0277b0 = c0295t.f1917c.get(i17);
            if (abstractC0277b0 != null && (i13 = abstractC0277b0.f1854c) >= i12 && i13 <= i11) {
                if (i13 == i6) {
                    abstractC0277b0.m1072q(i7 - i6, false);
                } else {
                    abstractC0277b0.m1072q(i15, false);
                }
            }
        }
        recyclerView.requestLayout();
        this.f2166a.f1827s0 = true;
    }
}
