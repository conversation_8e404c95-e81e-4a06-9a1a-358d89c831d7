package androidx.recyclerview.widget;

import android.annotation.SuppressLint;
import android.os.Trace;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.concurrent.TimeUnit;
import p001a0.C0003b;

/* renamed from: androidx.recyclerview.widget.m */
/* loaded from: classes.dex */
public final class RunnableC0323m implements Runnable {

    /* renamed from: n */
    public static final ThreadLocal<RunnableC0323m> f2126n = new ThreadLocal<>();

    /* renamed from: o */
    public static Comparator<c> f2127o = new a();

    /* renamed from: k */
    public long f2129k;

    /* renamed from: l */
    public long f2130l;

    /* renamed from: j */
    public ArrayList<RecyclerView> f2128j = new ArrayList<>();

    /* renamed from: m */
    public ArrayList<c> f2131m = new ArrayList<>();

    /* renamed from: androidx.recyclerview.widget.m$a */
    public static class a implements Comparator<c> {
        /* JADX WARN: Code restructure failed: missing block: B:11:?, code lost:
        
            return 1;
         */
        /* JADX WARN: Code restructure failed: missing block: B:14:?, code lost:
        
            return -1;
         */
        /* JADX WARN: Code restructure failed: missing block: B:17:0x0023, code lost:
        
            if (r0 != false) goto L44;
         */
        /* JADX WARN: Code restructure failed: missing block: B:9:0x0017, code lost:
        
            if (r0 == null) goto L43;
         */
        @Override // java.util.Comparator
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct code enable 'Show inconsistent code' option in preferences
        */
        public final int compare(androidx.recyclerview.widget.RunnableC0323m.c r7, androidx.recyclerview.widget.RunnableC0323m.c r8) {
            /*
                r6 = this;
                androidx.recyclerview.widget.m$c r7 = (androidx.recyclerview.widget.RunnableC0323m.c) r7
                androidx.recyclerview.widget.m$c r8 = (androidx.recyclerview.widget.RunnableC0323m.c) r8
                androidx.recyclerview.widget.RecyclerView r0 = r7.f2139d
                r1 = 0
                r2 = 1
                if (r0 != 0) goto Lc
                r3 = r2
                goto Ld
            Lc:
                r3 = r1
            Ld:
                androidx.recyclerview.widget.RecyclerView r4 = r8.f2139d
                if (r4 != 0) goto L13
                r4 = r2
                goto L14
            L13:
                r4 = r1
            L14:
                r5 = -1
                if (r3 == r4) goto L1d
                if (r0 != 0) goto L1b
            L19:
                r1 = r2
                goto L37
            L1b:
                r1 = r5
                goto L37
            L1d:
                boolean r0 = r7.f2136a
                boolean r3 = r8.f2136a
                if (r0 == r3) goto L26
                if (r0 == 0) goto L19
                goto L1b
            L26:
                int r0 = r8.f2137b
                int r2 = r7.f2137b
                int r0 = r0 - r2
                if (r0 == 0) goto L2f
                r1 = r0
                goto L37
            L2f:
                int r7 = r7.f2138c
                int r8 = r8.f2138c
                int r7 = r7 - r8
                if (r7 == 0) goto L37
                r1 = r7
            L37:
                return r1
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RunnableC0323m.a.compare(java.lang.Object, java.lang.Object):int");
        }
    }

    @SuppressLint({"VisibleForTests"})
    /* renamed from: androidx.recyclerview.widget.m$b */
    public static class b implements RecyclerView.AbstractC0288m.c {

        /* renamed from: a */
        public int f2132a;

        /* renamed from: b */
        public int f2133b;

        /* renamed from: c */
        public int[] f2134c;

        /* renamed from: d */
        public int f2135d;

        /* renamed from: a */
        public final void m1316a(int i6, int i7) {
            if (i6 < 0) {
                throw new IllegalArgumentException("Layout positions must be non-negative");
            }
            if (i7 < 0) {
                throw new IllegalArgumentException("Pixel distance must be non-negative");
            }
            int i8 = this.f2135d * 2;
            int[] iArr = this.f2134c;
            if (iArr == null) {
                int[] iArr2 = new int[4];
                this.f2134c = iArr2;
                Arrays.fill(iArr2, -1);
            } else if (i8 >= iArr.length) {
                int[] iArr3 = new int[i8 * 2];
                this.f2134c = iArr3;
                System.arraycopy(iArr, 0, iArr3, 0, iArr.length);
            }
            int[] iArr4 = this.f2134c;
            iArr4[i8] = i6;
            iArr4[i8 + 1] = i7;
            this.f2135d++;
        }

        /* renamed from: b */
        public final void m1317b(RecyclerView recyclerView, boolean z5) {
            this.f2135d = 0;
            int[] iArr = this.f2134c;
            if (iArr != null) {
                Arrays.fill(iArr, -1);
            }
            RecyclerView.AbstractC0288m abstractC0288m = recyclerView.f1832v;
            if (recyclerView.f1830u == null || abstractC0288m == null || !abstractC0288m.f1892k) {
                return;
            }
            if (z5) {
                if (!recyclerView.f1814m.m1256g()) {
                    abstractC0288m.mo969j(recyclerView.f1830u.mo1081c(), this);
                }
            } else if (!recyclerView.m1006M()) {
                abstractC0288m.mo967i(this.f2132a, this.f2133b, recyclerView.f1821p0, this);
            }
            int i6 = this.f2135d;
            if (i6 > abstractC0288m.f1893l) {
                abstractC0288m.f1893l = i6;
                abstractC0288m.f1894m = z5;
                recyclerView.f1810k.m1187l();
            }
        }

        /* renamed from: c */
        public final boolean m1318c(int i6) {
            if (this.f2134c != null) {
                int i7 = this.f2135d * 2;
                for (int i8 = 0; i8 < i7; i8 += 2) {
                    if (this.f2134c[i8] == i6) {
                        return true;
                    }
                }
            }
            return false;
        }
    }

    /* renamed from: androidx.recyclerview.widget.m$c */
    public static class c {

        /* renamed from: a */
        public boolean f2136a;

        /* renamed from: b */
        public int f2137b;

        /* renamed from: c */
        public int f2138c;

        /* renamed from: d */
        public RecyclerView f2139d;

        /* renamed from: e */
        public int f2140e;
    }

    /* renamed from: a */
    public final void m1313a(RecyclerView recyclerView, int i6, int i7) {
        if (recyclerView.isAttachedToWindow() && this.f2129k == 0) {
            this.f2129k = recyclerView.getNanoTime();
            recyclerView.post(this);
        }
        b bVar = recyclerView.f1819o0;
        bVar.f2132a = i6;
        bVar.f2133b = i7;
    }

    /* renamed from: b */
    public final void m1314b(long j6) {
        c cVar;
        RecyclerView recyclerView;
        RecyclerView recyclerView2;
        c cVar2;
        int size = this.f2128j.size();
        int i6 = 0;
        for (int i7 = 0; i7 < size; i7++) {
            RecyclerView recyclerView3 = this.f2128j.get(i7);
            if (recyclerView3.getWindowVisibility() == 0) {
                recyclerView3.f1819o0.m1317b(recyclerView3, false);
                i6 += recyclerView3.f1819o0.f2135d;
            }
        }
        this.f2131m.ensureCapacity(i6);
        int i8 = 0;
        for (int i9 = 0; i9 < size; i9++) {
            RecyclerView recyclerView4 = this.f2128j.get(i9);
            if (recyclerView4.getWindowVisibility() == 0) {
                b bVar = recyclerView4.f1819o0;
                int abs = Math.abs(bVar.f2133b) + Math.abs(bVar.f2132a);
                for (int i10 = 0; i10 < bVar.f2135d * 2; i10 += 2) {
                    if (i8 >= this.f2131m.size()) {
                        cVar2 = new c();
                        this.f2131m.add(cVar2);
                    } else {
                        cVar2 = this.f2131m.get(i8);
                    }
                    int[] iArr = bVar.f2134c;
                    int i11 = iArr[i10 + 1];
                    cVar2.f2136a = i11 <= abs;
                    cVar2.f2137b = abs;
                    cVar2.f2138c = i11;
                    cVar2.f2139d = recyclerView4;
                    cVar2.f2140e = iArr[i10];
                    i8++;
                }
            }
        }
        Collections.sort(this.f2131m, f2127o);
        for (int i12 = 0; i12 < this.f2131m.size() && (recyclerView = (cVar = this.f2131m.get(i12)).f2139d) != null; i12++) {
            RecyclerView.AbstractC0277b0 m1315c = m1315c(recyclerView, cVar.f2140e, cVar.f2136a ? Long.MAX_VALUE : j6);
            if (m1315c != null && m1315c.f1853b != null && m1315c.m1065j() && !m1315c.m1066k() && (recyclerView2 = m1315c.f1853b.get()) != null) {
                if (recyclerView2.f1787L && recyclerView2.f1816n.m1274h() != 0) {
                    recyclerView2.m1019Z();
                }
                b bVar2 = recyclerView2.f1819o0;
                bVar2.m1317b(recyclerView2, true);
                if (bVar2.f2135d != 0) {
                    try {
                        int i13 = C0003b.f5a;
                        Trace.beginSection("RV Nested Prefetch");
                        RecyclerView.C0300y c0300y = recyclerView2.f1821p0;
                        RecyclerView.AbstractC0280e abstractC0280e = recyclerView2.f1830u;
                        c0300y.f1943d = 1;
                        c0300y.f1944e = abstractC0280e.mo1081c();
                        c0300y.f1946g = false;
                        c0300y.f1947h = false;
                        c0300y.f1948i = false;
                        for (int i14 = 0; i14 < bVar2.f2135d * 2; i14 += 2) {
                            m1315c(recyclerView2, bVar2.f2134c[i14], j6);
                        }
                        Trace.endSection();
                    } catch (Throwable th) {
                        int i15 = C0003b.f5a;
                        Trace.endSection();
                        throw th;
                    }
                } else {
                    continue;
                }
            }
            cVar.f2136a = false;
            cVar.f2137b = 0;
            cVar.f2138c = 0;
            cVar.f2139d = null;
            cVar.f2140e = 0;
        }
    }

    /* renamed from: c */
    public final RecyclerView.AbstractC0277b0 m1315c(RecyclerView recyclerView, int i6, long j6) {
        boolean z5;
        int m1274h = recyclerView.f1816n.m1274h();
        int i7 = 0;
        while (true) {
            if (i7 >= m1274h) {
                z5 = false;
                break;
            }
            RecyclerView.AbstractC0277b0 m990K = RecyclerView.m990K(recyclerView.f1816n.m1273g(i7));
            if (m990K.f1854c == i6 && !m990K.m1066k()) {
                z5 = true;
                break;
            }
            i7++;
        }
        if (z5) {
            return null;
        }
        RecyclerView.C0295t c0295t = recyclerView.f1810k;
        try {
            recyclerView.m1012S();
            RecyclerView.AbstractC0277b0 m1185j = c0295t.m1185j(i6, j6);
            if (m1185j != null) {
                if (!m1185j.m1065j() || m1185j.m1066k()) {
                    c0295t.m1176a(m1185j, false);
                } else {
                    c0295t.m1182g(m1185j.f1852a);
                }
            }
            return m1185j;
        } finally {
            recyclerView.m1013T(false);
        }
    }

    @Override // java.lang.Runnable
    public final void run() {
        try {
            int i6 = C0003b.f5a;
            Trace.beginSection("RV Prefetch");
            if (this.f2128j.isEmpty()) {
                this.f2129k = 0L;
                Trace.endSection();
                return;
            }
            int size = this.f2128j.size();
            long j6 = 0;
            for (int i7 = 0; i7 < size; i7++) {
                RecyclerView recyclerView = this.f2128j.get(i7);
                if (recyclerView.getWindowVisibility() == 0) {
                    j6 = Math.max(recyclerView.getDrawingTime(), j6);
                }
            }
            if (j6 == 0) {
                this.f2129k = 0L;
                Trace.endSection();
            } else {
                m1314b(TimeUnit.MILLISECONDS.toNanos(j6) + this.f2130l);
                this.f2129k = 0L;
                Trace.endSection();
            }
        } catch (Throwable th) {
            this.f2129k = 0L;
            int i8 = C0003b.f5a;
            Trace.endSection();
            throw th;
        }
    }
}
