package androidx.recyclerview.widget;

import android.view.View;
import android.view.animation.DecelerateInterpolator;
import android.widget.Scroller;
import androidx.recyclerview.widget.RecyclerView;

/* renamed from: androidx.recyclerview.widget.a0 */
/* loaded from: classes.dex */
public abstract class AbstractC0309a0 extends RecyclerView.AbstractC0291p {

    /* renamed from: a */
    public RecyclerView f2017a;

    /* renamed from: b */
    public final a f2018b = new a();

    /* renamed from: androidx.recyclerview.widget.a0$a */
    public class a extends RecyclerView.AbstractC0293r {

        /* renamed from: a */
        public boolean f2019a = false;

        public a() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0293r
        /* renamed from: a */
        public final void mo1173a(RecyclerView recyclerView, int i6) {
            if (i6 == 0 && this.f2019a) {
                this.f2019a = false;
                AbstractC0309a0.this.m1266d();
            }
        }

        @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0293r
        /* renamed from: b */
        public final void mo1174b(RecyclerView recyclerView, int i6, int i7) {
            if (i6 == 0 && i7 == 0) {
                return;
            }
            this.f2019a = true;
        }
    }

    /* JADX WARN: Type inference failed for: r0v5, types: [java.util.ArrayList, java.util.List<androidx.recyclerview.widget.RecyclerView$r>] */
    /* renamed from: a */
    public final void m1263a(RecyclerView recyclerView) {
        RecyclerView recyclerView2 = this.f2017a;
        if (recyclerView2 == recyclerView) {
            return;
        }
        if (recyclerView2 != null) {
            a aVar = this.f2018b;
            ?? r0 = recyclerView2.f1825r0;
            if (r0 != 0) {
                r0.remove(aVar);
            }
            this.f2017a.setOnFlingListener(null);
        }
        this.f2017a = recyclerView;
        if (recyclerView != null) {
            if (recyclerView.getOnFlingListener() != null) {
                throw new IllegalStateException("An instance of OnFlingListener already set.");
            }
            this.f2017a.m1029h(this.f2018b);
            this.f2017a.setOnFlingListener(this);
            new Scroller(this.f2017a.getContext(), new DecelerateInterpolator());
            m1266d();
        }
    }

    /* renamed from: b */
    public abstract int[] mo1264b(RecyclerView.AbstractC0288m abstractC0288m, View view);

    /* renamed from: c */
    public abstract View mo1265c(RecyclerView.AbstractC0288m abstractC0288m);

    /* renamed from: d */
    public final void m1266d() {
        RecyclerView.AbstractC0288m layoutManager;
        View mo1265c;
        RecyclerView recyclerView = this.f2017a;
        if (recyclerView == null || (layoutManager = recyclerView.getLayoutManager()) == null || (mo1265c = mo1265c(layoutManager)) == null) {
            return;
        }
        int[] mo1264b = mo1264b(layoutManager, mo1265c);
        if (mo1264b[0] == 0 && mo1264b[1] == 0) {
            return;
        }
        this.f2017a.m1028g0(mo1264b[0], mo1264b[1], false);
    }
}
