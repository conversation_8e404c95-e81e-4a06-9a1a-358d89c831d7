package androidx.recyclerview.widget;

import android.view.View;
import android.view.ViewGroup;
import androidx.appcompat.widget.C0174y;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.WeakHashMap;
import p029e0.C0766p;
import p029e0.C0769s;

/* renamed from: androidx.recyclerview.widget.b */
/* loaded from: classes.dex */
public final class C0310b {

    /* renamed from: a */
    public final b f2021a;

    /* renamed from: b */
    public final a f2022b = new a();

    /* renamed from: c */
    public final List<View> f2023c = new ArrayList();

    /* renamed from: androidx.recyclerview.widget.b$a */
    public static class a {

        /* renamed from: a */
        public long f2024a = 0;

        /* renamed from: b */
        public a f2025b;

        /* renamed from: a */
        public final void m1279a(int i6) {
            if (i6 < 64) {
                this.f2024a &= ~(1 << i6);
                return;
            }
            a aVar = this.f2025b;
            if (aVar != null) {
                aVar.m1279a(i6 - 64);
            }
        }

        /* renamed from: b */
        public final int m1280b(int i6) {
            long j6;
            a aVar = this.f2025b;
            if (aVar == null) {
                if (i6 >= 64) {
                    j6 = this.f2024a;
                    return Long.bitCount(j6);
                }
            } else if (i6 >= 64) {
                return Long.bitCount(this.f2024a) + aVar.m1280b(i6 - 64);
            }
            j6 = this.f2024a & ((1 << i6) - 1);
            return Long.bitCount(j6);
        }

        /* renamed from: c */
        public final void m1281c() {
            if (this.f2025b == null) {
                this.f2025b = new a();
            }
        }

        /* renamed from: d */
        public final boolean m1282d(int i6) {
            if (i6 < 64) {
                return (this.f2024a & (1 << i6)) != 0;
            }
            m1281c();
            return this.f2025b.m1282d(i6 - 64);
        }

        /* renamed from: e */
        public final void m1283e(int i6, boolean z5) {
            if (i6 >= 64) {
                m1281c();
                this.f2025b.m1283e(i6 - 64, z5);
                return;
            }
            long j6 = this.f2024a;
            boolean z6 = (Long.MIN_VALUE & j6) != 0;
            long j7 = (1 << i6) - 1;
            this.f2024a = ((j6 & (~j7)) << 1) | (j6 & j7);
            if (z5) {
                m1286h(i6);
            } else {
                m1279a(i6);
            }
            if (z6 || this.f2025b != null) {
                m1281c();
                this.f2025b.m1283e(0, z6);
            }
        }

        /* renamed from: f */
        public final boolean m1284f(int i6) {
            if (i6 >= 64) {
                m1281c();
                return this.f2025b.m1284f(i6 - 64);
            }
            long j6 = 1 << i6;
            long j7 = this.f2024a;
            boolean z5 = (j7 & j6) != 0;
            long j8 = j7 & (~j6);
            this.f2024a = j8;
            long j9 = j6 - 1;
            this.f2024a = (j8 & j9) | Long.rotateRight((~j9) & j8, 1);
            a aVar = this.f2025b;
            if (aVar != null) {
                if (aVar.m1282d(0)) {
                    m1286h(63);
                }
                this.f2025b.m1284f(0);
            }
            return z5;
        }

        /* renamed from: g */
        public final void m1285g() {
            this.f2024a = 0L;
            a aVar = this.f2025b;
            if (aVar != null) {
                aVar.m1285g();
            }
        }

        /* renamed from: h */
        public final void m1286h(int i6) {
            if (i6 < 64) {
                this.f2024a |= 1 << i6;
            } else {
                m1281c();
                this.f2025b.m1286h(i6 - 64);
            }
        }

        public final String toString() {
            if (this.f2025b == null) {
                return Long.toBinaryString(this.f2024a);
            }
            return this.f2025b.toString() + "xx" + Long.toBinaryString(this.f2024a);
        }
    }

    /* renamed from: androidx.recyclerview.widget.b$b */
    public interface b {
    }

    public C0310b(b bVar) {
        this.f2021a = bVar;
    }

    /* JADX WARN: Type inference failed for: r0v3, types: [java.util.ArrayList, java.util.List<androidx.recyclerview.widget.RecyclerView$o>] */
    /* JADX WARN: Type inference failed for: r4v4, types: [java.util.ArrayList, java.util.List<androidx.recyclerview.widget.RecyclerView$o>] */
    /* renamed from: a */
    public final void m1267a(View view, int i6, boolean z5) {
        int m1343b = i6 < 0 ? ((C0332v) this.f2021a).m1343b() : m1272f(i6);
        this.f2022b.m1283e(m1343b, z5);
        if (z5) {
            m1275i(view);
        }
        C0332v c0332v = (C0332v) this.f2021a;
        c0332v.f2165a.addView(view, m1343b);
        RecyclerView recyclerView = c0332v.f2165a;
        Objects.requireNonNull(recyclerView);
        RecyclerView.AbstractC0277b0 m990K = RecyclerView.m990K(view);
        RecyclerView.AbstractC0280e abstractC0280e = recyclerView.f1830u;
        if (abstractC0280e != null && m990K != null) {
            abstractC0280e.mo1089k(m990K);
        }
        ?? r42 = recyclerView.f1786K;
        if (r42 == 0) {
            return;
        }
        int size = r42.size();
        while (true) {
            size--;
            if (size < 0) {
                return;
            } else {
                ((RecyclerView.InterfaceC0290o) recyclerView.f1786K.get(size)).mo1168a(view);
            }
        }
    }

    /* renamed from: b */
    public final void m1268b(View view, int i6, ViewGroup.LayoutParams layoutParams, boolean z5) {
        int m1343b = i6 < 0 ? ((C0332v) this.f2021a).m1343b() : m1272f(i6);
        this.f2022b.m1283e(m1343b, z5);
        if (z5) {
            m1275i(view);
        }
        C0332v c0332v = (C0332v) this.f2021a;
        Objects.requireNonNull(c0332v);
        RecyclerView.AbstractC0277b0 m990K = RecyclerView.m990K(view);
        if (m990K != null) {
            if (!m990K.m1070o() && !m990K.m1076u()) {
                StringBuilder sb = new StringBuilder();
                sb.append("Called attach on a child which is not detached: ");
                sb.append(m990K);
                throw new IllegalArgumentException(C0174y.m489g(c0332v.f2165a, sb));
            }
            m990K.f1861j &= -257;
        }
        c0332v.f2165a.attachViewToParent(view, m1343b, layoutParams);
    }

    /* renamed from: c */
    public final void m1269c(int i6) {
        RecyclerView.AbstractC0277b0 m990K;
        int m1272f = m1272f(i6);
        this.f2022b.m1284f(m1272f);
        C0332v c0332v = (C0332v) this.f2021a;
        View m1342a = c0332v.m1342a(m1272f);
        if (m1342a != null && (m990K = RecyclerView.m990K(m1342a)) != null) {
            if (m990K.m1070o() && !m990K.m1076u()) {
                StringBuilder sb = new StringBuilder();
                sb.append("called detach on an already detached child ");
                sb.append(m990K);
                throw new IllegalArgumentException(C0174y.m489g(c0332v.f2165a, sb));
            }
            m990K.m1057b(256);
        }
        c0332v.f2165a.detachViewFromParent(m1272f);
    }

    /* renamed from: d */
    public final View m1270d(int i6) {
        return ((C0332v) this.f2021a).m1342a(m1272f(i6));
    }

    /* JADX WARN: Type inference failed for: r1v0, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    /* renamed from: e */
    public final int m1271e() {
        return ((C0332v) this.f2021a).m1343b() - this.f2023c.size();
    }

    /* renamed from: f */
    public final int m1272f(int i6) {
        if (i6 < 0) {
            return -1;
        }
        int m1343b = ((C0332v) this.f2021a).m1343b();
        int i7 = i6;
        while (i7 < m1343b) {
            int m1280b = i6 - (i7 - this.f2022b.m1280b(i7));
            if (m1280b == 0) {
                while (this.f2022b.m1282d(i7)) {
                    i7++;
                }
                return i7;
            }
            i7 += m1280b;
        }
        return -1;
    }

    /* renamed from: g */
    public final View m1273g(int i6) {
        return ((C0332v) this.f2021a).m1342a(i6);
    }

    /* renamed from: h */
    public final int m1274h() {
        return ((C0332v) this.f2021a).m1343b();
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    /* renamed from: i */
    public final void m1275i(View view) {
        this.f2023c.add(view);
        C0332v c0332v = (C0332v) this.f2021a;
        Objects.requireNonNull(c0332v);
        RecyclerView.AbstractC0277b0 m990K = RecyclerView.m990K(view);
        if (m990K != null) {
            RecyclerView recyclerView = c0332v.f2165a;
            int i6 = m990K.f1868q;
            if (i6 == -1) {
                View view2 = m990K.f1852a;
                WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
                i6 = view2.getImportantForAccessibility();
            }
            m990K.f1867p = i6;
            recyclerView.m1026f0(m990K, 4);
        }
    }

    /* renamed from: j */
    public final int m1276j(View view) {
        int m1344c = ((C0332v) this.f2021a).m1344c(view);
        if (m1344c == -1 || this.f2022b.m1282d(m1344c)) {
            return -1;
        }
        return m1344c - this.f2022b.m1280b(m1344c);
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    /* renamed from: k */
    public final boolean m1277k(View view) {
        return this.f2023c.contains(view);
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    /* renamed from: l */
    public final boolean m1278l(View view) {
        if (!this.f2023c.remove(view)) {
            return false;
        }
        C0332v c0332v = (C0332v) this.f2021a;
        Objects.requireNonNull(c0332v);
        RecyclerView.AbstractC0277b0 m990K = RecyclerView.m990K(view);
        if (m990K == null) {
            return true;
        }
        c0332v.f2165a.m1026f0(m990K, m990K.f1867p);
        m990K.f1867p = 0;
        return true;
    }

    /* JADX WARN: Type inference failed for: r1v3, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    public final String toString() {
        return this.f2022b.toString() + ", hidden list:" + this.f2023c.size();
    }
}
