package androidx.recyclerview.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.view.View;
import android.view.ViewPropertyAnimator;
import androidx.recyclerview.widget.C0321k;
import androidx.recyclerview.widget.RecyclerView;
import java.util.Objects;

/* renamed from: androidx.recyclerview.widget.i */
/* loaded from: classes.dex */
public final class C0319i extends AnimatorListenerAdapter {

    /* renamed from: a */
    public final /* synthetic */ C0321k.a f2059a;

    /* renamed from: b */
    public final /* synthetic */ ViewPropertyAnimator f2060b;

    /* renamed from: c */
    public final /* synthetic */ View f2061c;

    /* renamed from: d */
    public final /* synthetic */ C0321k f2062d;

    public C0319i(C0321k c0321k, C0321k.a aVar, ViewPropertyAnimator viewPropertyAnimator, View view) {
        this.f2062d = c0321k;
        this.f2059a = aVar;
        this.f2060b = viewPropertyAnimator;
        this.f2061c = view;
    }

    @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
    public final void onAnimationEnd(Animator animator) {
        this.f2060b.setListener(null);
        this.f2061c.setAlpha(1.0f);
        this.f2061c.setTranslationX(0.0f);
        this.f2061c.setTranslationY(0.0f);
        this.f2062d.m1103c(this.f2059a.f2079a);
        this.f2062d.f2078r.remove(this.f2059a.f2079a);
        this.f2062d.m1302j();
    }

    @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
    public final void onAnimationStart(Animator animator) {
        C0321k c0321k = this.f2062d;
        RecyclerView.AbstractC0277b0 abstractC0277b0 = this.f2059a.f2079a;
        Objects.requireNonNull(c0321k);
    }
}
