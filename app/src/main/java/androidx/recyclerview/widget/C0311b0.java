package androidx.recyclerview.widget;

import android.view.View;

/* renamed from: androidx.recyclerview.widget.b0 */
/* loaded from: classes.dex */
public final class C0311b0 {

    /* renamed from: a */
    public final b f2026a;

    /* renamed from: b */
    public a f2027b = new a();

    /* renamed from: androidx.recyclerview.widget.b0$a */
    public static class a {

        /* renamed from: a */
        public int f2028a = 0;

        /* renamed from: b */
        public int f2029b;

        /* renamed from: c */
        public int f2030c;

        /* renamed from: d */
        public int f2031d;

        /* renamed from: e */
        public int f2032e;

        /* renamed from: a */
        public final void m1289a(int i6) {
            this.f2028a = i6 | this.f2028a;
        }

        /* renamed from: b */
        public final boolean m1290b() {
            int i6 = this.f2028a;
            if ((i6 & 7) != 0 && (i6 & (m1291c(this.f2031d, this.f2029b) << 0)) == 0) {
                return false;
            }
            int i7 = this.f2028a;
            if ((i7 & 112) != 0 && (i7 & (m1291c(this.f2031d, this.f2030c) << 4)) == 0) {
                return false;
            }
            int i8 = this.f2028a;
            if ((i8 & 1792) != 0 && (i8 & (m1291c(this.f2032e, this.f2029b) << 8)) == 0) {
                return false;
            }
            int i9 = this.f2028a;
            return (i9 & 28672) == 0 || (i9 & (m1291c(this.f2032e, this.f2030c) << 12)) != 0;
        }

        /* renamed from: c */
        public final int m1291c(int i6, int i7) {
            if (i6 > i7) {
                return 1;
            }
            return i6 == i7 ? 2 : 4;
        }
    }

    /* renamed from: androidx.recyclerview.widget.b0$b */
    public interface b {
        /* renamed from: a */
        View mo1160a(int i6);

        /* renamed from: b */
        int mo1161b();

        /* renamed from: c */
        int mo1162c();

        /* renamed from: d */
        int mo1163d(View view);

        /* renamed from: e */
        int mo1164e(View view);
    }

    public C0311b0(b bVar) {
        this.f2026a = bVar;
    }

    /* renamed from: a */
    public final View m1287a(int i6, int i7, int i8, int i9) {
        int mo1162c = this.f2026a.mo1162c();
        int mo1161b = this.f2026a.mo1161b();
        int i10 = i7 > i6 ? 1 : -1;
        View view = null;
        while (i6 != i7) {
            View mo1160a = this.f2026a.mo1160a(i6);
            int mo1164e = this.f2026a.mo1164e(mo1160a);
            int mo1163d = this.f2026a.mo1163d(mo1160a);
            a aVar = this.f2027b;
            aVar.f2029b = mo1162c;
            aVar.f2030c = mo1161b;
            aVar.f2031d = mo1164e;
            aVar.f2032e = mo1163d;
            if (i8 != 0) {
                aVar.f2028a = 0;
                aVar.m1289a(i8);
                if (this.f2027b.m1290b()) {
                    return mo1160a;
                }
            }
            if (i9 != 0) {
                a aVar2 = this.f2027b;
                aVar2.f2028a = 0;
                aVar2.m1289a(i9);
                if (this.f2027b.m1290b()) {
                    view = mo1160a;
                }
            }
            i6 += i10;
        }
        return view;
    }

    /* renamed from: b */
    public final boolean m1288b(View view) {
        a aVar = this.f2027b;
        int mo1162c = this.f2026a.mo1162c();
        int mo1161b = this.f2026a.mo1161b();
        int mo1164e = this.f2026a.mo1164e(view);
        int mo1163d = this.f2026a.mo1163d(view);
        aVar.f2029b = mo1162c;
        aVar.f2030c = mo1161b;
        aVar.f2031d = mo1164e;
        aVar.f2032e = mo1163d;
        a aVar2 = this.f2027b;
        aVar2.f2028a = 0;
        aVar2.m1289a(24579);
        return this.f2027b.m1290b();
    }
}
