package androidx.fragment.app;

import android.annotation.SuppressLint;
import android.content.ComponentCallbacks;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.Log;
import android.util.SparseArray;
import android.view.ContextMenu;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.View;
import android.view.ViewGroup;
import androidx.activity.result.AbstractC0055d;
import androidx.activity.result.C0052a;
import androidx.lifecycle.AbstractC0251d;
import androidx.lifecycle.C0255h;
import androidx.lifecycle.C0259l;
import androidx.lifecycle.C0265r;
import androidx.lifecycle.InterfaceC0254g;
import androidx.lifecycle.InterfaceC0266s;
import androidx.savedstate.C0338a;
import androidx.savedstate.C0339b;
import androidx.savedstate.InterfaceC0340c;
import java.util.ArrayList;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import p008b0.C0385m;
import p111q0.AbstractC1253a;
import p111q0.C1254b;
import p153w3.C1798e;

/* renamed from: androidx.fragment.app.m */
/* loaded from: classes.dex */
public class ComponentCallbacksC0223m implements ComponentCallbacks, View.OnCreateContextMenuListener, InterfaceC0254g, InterfaceC0266s, InterfaceC0340c {

    /* renamed from: Z */
    public static final Object f1493Z = new Object();

    /* renamed from: A */
    public AbstractC0240x f1494A;

    /* renamed from: B */
    public AbstractC0237u<?> f1495B;

    /* renamed from: D */
    public ComponentCallbacksC0223m f1497D;

    /* renamed from: E */
    public int f1498E;

    /* renamed from: F */
    public int f1499F;

    /* renamed from: G */
    public String f1500G;

    /* renamed from: H */
    public boolean f1501H;

    /* renamed from: I */
    public boolean f1502I;

    /* renamed from: J */
    public boolean f1503J;

    /* renamed from: L */
    public boolean f1505L;

    /* renamed from: M */
    public ViewGroup f1506M;

    /* renamed from: N */
    public View f1507N;

    /* renamed from: O */
    public boolean f1508O;

    /* renamed from: Q */
    public b f1510Q;

    /* renamed from: R */
    public boolean f1511R;

    /* renamed from: S */
    public boolean f1512S;

    /* renamed from: U */
    public C0255h f1514U;

    /* renamed from: V */
    public C0220k0 f1515V;

    /* renamed from: X */
    public C0339b f1517X;

    /* renamed from: Y */
    public final ArrayList<d> f1518Y;

    /* renamed from: k */
    public Bundle f1520k;

    /* renamed from: l */
    public SparseArray<Parcelable> f1521l;

    /* renamed from: m */
    public Bundle f1522m;

    /* renamed from: o */
    public Bundle f1524o;

    /* renamed from: p */
    public ComponentCallbacksC0223m f1525p;

    /* renamed from: r */
    public int f1527r;

    /* renamed from: t */
    public boolean f1529t;

    /* renamed from: u */
    public boolean f1530u;

    /* renamed from: v */
    public boolean f1531v;

    /* renamed from: w */
    public boolean f1532w;

    /* renamed from: x */
    public boolean f1533x;

    /* renamed from: y */
    public boolean f1534y;

    /* renamed from: z */
    public int f1535z;

    /* renamed from: j */
    public int f1519j = -1;

    /* renamed from: n */
    public String f1523n = UUID.randomUUID().toString();

    /* renamed from: q */
    public String f1526q = null;

    /* renamed from: s */
    public Boolean f1528s = null;

    /* renamed from: C */
    public C0241y f1496C = new C0241y();

    /* renamed from: K */
    public boolean f1504K = true;

    /* renamed from: P */
    public boolean f1509P = true;

    /* renamed from: T */
    public AbstractC0251d.c f1513T = AbstractC0251d.c.RESUMED;

    /* renamed from: W */
    public C0259l<InterfaceC0254g> f1516W = new C0259l<>();

    /* renamed from: androidx.fragment.app.m$a */
    public class a extends AbstractC0055d {
        public a() {
        }

        @Override // androidx.activity.result.AbstractC0055d
        /* renamed from: k */
        public final View mo118k(int i6) {
            View view = ComponentCallbacksC0223m.this.f1507N;
            if (view != null) {
                return view.findViewById(i6);
            }
            StringBuilder m104h = C0052a.m104h("Fragment ");
            m104h.append(ComponentCallbacksC0223m.this);
            m104h.append(" does not have a view");
            throw new IllegalStateException(m104h.toString());
        }

        @Override // androidx.activity.result.AbstractC0055d
        /* renamed from: n */
        public final boolean mo121n() {
            return ComponentCallbacksC0223m.this.f1507N != null;
        }
    }

    /* renamed from: androidx.fragment.app.m$b */
    public static class b {

        /* renamed from: a */
        public View f1537a;

        /* renamed from: b */
        public boolean f1538b;

        /* renamed from: c */
        public int f1539c;

        /* renamed from: d */
        public int f1540d;

        /* renamed from: e */
        public int f1541e;

        /* renamed from: f */
        public int f1542f;

        /* renamed from: g */
        public int f1543g;

        /* renamed from: h */
        public ArrayList<String> f1544h;

        /* renamed from: i */
        public ArrayList<String> f1545i;

        /* renamed from: j */
        public Object f1546j;

        /* renamed from: k */
        public Object f1547k;

        /* renamed from: l */
        public Object f1548l;

        /* renamed from: m */
        public float f1549m;

        /* renamed from: n */
        public View f1550n;

        public b() {
            Object obj = ComponentCallbacksC0223m.f1493Z;
            this.f1546j = obj;
            this.f1547k = obj;
            this.f1548l = obj;
            this.f1549m = 1.0f;
            this.f1550n = null;
        }
    }

    /* renamed from: androidx.fragment.app.m$c */
    public static class c extends RuntimeException {
        public c(String str, Exception exc) {
            super(str, exc);
        }
    }

    /* renamed from: androidx.fragment.app.m$d */
    public static abstract class d {
        /* renamed from: a */
        public abstract void m748a();
    }

    @SuppressLint({"BanParcelableUsage, ParcelClassLoader"})
    /* renamed from: androidx.fragment.app.m$e */
    public static class e implements Parcelable {
        public static final Parcelable.Creator<e> CREATOR = new a();

        /* renamed from: j */
        public final Bundle f1551j;

        /* renamed from: androidx.fragment.app.m$e$a */
        public class a implements Parcelable.ClassLoaderCreator<e> {
            @Override // android.os.Parcelable.Creator
            public final Object createFromParcel(Parcel parcel) {
                return new e(parcel, null);
            }

            @Override // android.os.Parcelable.Creator
            public final Object[] newArray(int i6) {
                return new e[i6];
            }

            @Override // android.os.Parcelable.ClassLoaderCreator
            public final e createFromParcel(Parcel parcel, ClassLoader classLoader) {
                return new e(parcel, classLoader);
            }
        }

        public e(Bundle bundle) {
            this.f1551j = bundle;
        }

        public e(Parcel parcel, ClassLoader classLoader) {
            Bundle readBundle = parcel.readBundle();
            this.f1551j = readBundle;
            if (classLoader == null || readBundle == null) {
                return;
            }
            readBundle.setClassLoader(classLoader);
        }

        @Override // android.os.Parcelable
        public final int describeContents() {
            return 0;
        }

        @Override // android.os.Parcelable
        public final void writeToParcel(Parcel parcel, int i6) {
            parcel.writeBundle(this.f1551j);
        }
    }

    public ComponentCallbacksC0223m() {
        new AtomicInteger();
        this.f1518Y = new ArrayList<>();
        this.f1514U = new C0255h(this);
        this.f1517X = new C0339b(this);
    }

    /* renamed from: A */
    public void mo695A(Bundle bundle) {
        this.f1505L = true;
        m723W(bundle);
        C0241y c0241y = this.f1496C;
        if (c0241y.f1625o >= 1) {
            return;
        }
        c0241y.m832j();
    }

    /* renamed from: B */
    public View mo710B(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        return null;
    }

    /* renamed from: C */
    public void mo711C() {
        this.f1505L = true;
    }

    /* renamed from: D */
    public void mo696D() {
        this.f1505L = true;
    }

    /* renamed from: E */
    public void mo697E() {
        this.f1505L = true;
    }

    /* renamed from: F */
    public LayoutInflater mo698F(Bundle bundle) {
        AbstractC0237u<?> abstractC0237u = this.f1495B;
        if (abstractC0237u == null) {
            throw new IllegalStateException("onGetLayoutInflater() cannot be executed until the Fragment is attached to the FragmentManager.");
        }
        LayoutInflater mo769t = abstractC0237u.mo769t();
        mo769t.setFactory2(this.f1496C.f1616f);
        return mo769t;
    }

    /* renamed from: G */
    public void mo712G(boolean z5) {
    }

    /* renamed from: H */
    public final void m713H() {
        this.f1505L = true;
        AbstractC0237u<?> abstractC0237u = this.f1495B;
        if ((abstractC0237u == null ? null : abstractC0237u.f1591j) != null) {
            this.f1505L = true;
        }
    }

    /* renamed from: I */
    public void mo714I() {
        this.f1505L = true;
    }

    /* renamed from: J */
    public void mo699J(Bundle bundle) {
    }

    /* renamed from: K */
    public void mo700K() {
        this.f1505L = true;
    }

    /* renamed from: L */
    public void mo701L() {
        this.f1505L = true;
    }

    /* renamed from: M */
    public void mo715M(View view) {
    }

    /* renamed from: N */
    public void mo702N(Bundle bundle) {
        this.f1505L = true;
    }

    /* renamed from: O */
    public void mo703O(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        this.f1496C.m807Q();
        this.f1534y = true;
        this.f1515V = new C0220k0(mo90g());
        View mo710B = mo710B(layoutInflater, viewGroup, bundle);
        this.f1507N = mo710B;
        if (mo710B == null) {
            if (this.f1515V.f1468k != null) {
                throw new IllegalStateException("Called getViewLifecycleOwner() but onCreateView() returned null");
            }
            this.f1515V = null;
        } else {
            this.f1515V.m694e();
            C0385m.m1423o(this.f1507N, this.f1515V);
            C1798e.m4518J(this.f1507N, this.f1515V);
            C0385m.m1424p(this.f1507N, this.f1515V);
            this.f1516W.mo890h(this.f1515V);
        }
    }

    /* renamed from: P */
    public final void m716P() {
        this.f1496C.m842t(1);
        if (this.f1507N != null) {
            C0220k0 c0220k0 = this.f1515V;
            c0220k0.m694e();
            if (c0220k0.f1468k.f1700b.m875d(AbstractC0251d.c.CREATED)) {
                this.f1515V.m693d(AbstractC0251d.b.ON_DESTROY);
            }
        }
        this.f1519j = 1;
        this.f1505L = false;
        mo696D();
        if (!this.f1505L) {
            throw new C0234r0("Fragment " + this + " did not call through to super.onDestroyView()");
        }
        C1254b.b bVar = ((C1254b) AbstractC1253a.m3142b(this)).f5936b;
        int i6 = bVar.f5938b.f5042l;
        for (int i7 = 0; i7 < i6; i7++) {
            Objects.requireNonNull((C1254b.a) bVar.f5938b.f5041k[i7]);
        }
        this.f1534y = false;
    }

    /* renamed from: Q */
    public final void m717Q() {
        onLowMemory();
        this.f1496C.m835m();
    }

    /* renamed from: R */
    public final void m718R(boolean z5) {
        this.f1496C.m836n(z5);
    }

    /* renamed from: S */
    public final void m719S(boolean z5) {
        this.f1496C.m840r(z5);
    }

    /* renamed from: T */
    public final boolean m720T(Menu menu) {
        if (this.f1501H) {
            return false;
        }
        return false | this.f1496C.m841s(menu);
    }

    /* renamed from: U */
    public final Context m721U() {
        Context m732j = m732j();
        if (m732j != null) {
            return m732j;
        }
        throw new IllegalStateException("Fragment " + this + " not attached to a context.");
    }

    /* renamed from: V */
    public final View m722V() {
        View view = this.f1507N;
        if (view != null) {
            return view;
        }
        throw new IllegalStateException("Fragment " + this + " did not return a View from onCreateView() or this was called before onCreateView().");
    }

    /* renamed from: W */
    public final void m723W(Bundle bundle) {
        Parcelable parcelable;
        if (bundle == null || (parcelable = bundle.getParcelable("android:support:fragments")) == null) {
            return;
        }
        this.f1496C.m812V(parcelable);
        this.f1496C.m832j();
    }

    /* renamed from: X */
    public final void m724X(int i6, int i7, int i8, int i9) {
        if (this.f1510Q == null && i6 == 0 && i7 == 0 && i8 == 0 && i9 == 0) {
            return;
        }
        m728e().f1539c = i6;
        m728e().f1540d = i7;
        m728e().f1541e = i8;
        m728e().f1542f = i9;
    }

    /* renamed from: Y */
    public final void m725Y(Bundle bundle) {
        AbstractC0240x abstractC0240x = this.f1494A;
        if (abstractC0240x != null) {
            if (abstractC0240x == null ? false : abstractC0240x.m805O()) {
                throw new IllegalStateException("Fragment already added and state has been saved");
            }
        }
        this.f1524o = bundle;
    }

    /* renamed from: Z */
    public final void m726Z(View view) {
        m728e().f1550n = view;
    }

    @Override // androidx.lifecycle.InterfaceC0254g
    /* renamed from: a */
    public final AbstractC0251d mo86a() {
        return this.f1514U;
    }

    /* renamed from: a0 */
    public final void m727a0(boolean z5) {
        if (this.f1510Q == null) {
            return;
        }
        m728e().f1538b = z5;
    }

    @Override // androidx.savedstate.InterfaceC0340c
    /* renamed from: c */
    public final C0338a mo88c() {
        return this.f1517X.f2181b;
    }

    /* renamed from: d */
    public AbstractC0055d mo706d() {
        return new a();
    }

    /* renamed from: e */
    public final b m728e() {
        if (this.f1510Q == null) {
            this.f1510Q = new b();
        }
        return this.f1510Q;
    }

    public final boolean equals(Object obj) {
        return super.equals(obj);
    }

    /* renamed from: f */
    public final ActivityC0229p m729f() {
        AbstractC0237u<?> abstractC0237u = this.f1495B;
        if (abstractC0237u == null) {
            return null;
        }
        return (ActivityC0229p) abstractC0237u.f1591j;
    }

    @Override // androidx.lifecycle.InterfaceC0266s
    /* renamed from: g */
    public final C0265r mo90g() {
        if (this.f1494A == null) {
            throw new IllegalStateException("Can't access ViewModels from detached fragment");
        }
        if (m735m() == 1) {
            throw new IllegalStateException("Calling getViewModelStore() before a Fragment reaches onCreate() when using setMaxLifecycle(INITIALIZED) is not supported");
        }
        C0200a0 c0200a0 = this.f1494A.f1609H;
        C0265r c0265r = c0200a0.f1351d.get(this.f1523n);
        if (c0265r != null) {
            return c0265r;
        }
        C0265r c0265r2 = new C0265r();
        c0200a0.f1351d.put(this.f1523n, c0265r2);
        return c0265r2;
    }

    /* renamed from: h */
    public final View m730h() {
        b bVar = this.f1510Q;
        if (bVar == null) {
            return null;
        }
        return bVar.f1537a;
    }

    public final int hashCode() {
        return super.hashCode();
    }

    /* renamed from: i */
    public final AbstractC0240x m731i() {
        if (this.f1495B != null) {
            return this.f1496C;
        }
        throw new IllegalStateException("Fragment " + this + " has not been attached yet.");
    }

    /* renamed from: j */
    public final Context m732j() {
        AbstractC0237u<?> abstractC0237u = this.f1495B;
        if (abstractC0237u == null) {
            return null;
        }
        return abstractC0237u.f1592k;
    }

    /* renamed from: k */
    public final int m733k() {
        b bVar = this.f1510Q;
        if (bVar == null) {
            return 0;
        }
        return bVar.f1539c;
    }

    /* renamed from: l */
    public final int m734l() {
        b bVar = this.f1510Q;
        if (bVar == null) {
            return 0;
        }
        return bVar.f1540d;
    }

    /* renamed from: m */
    public final int m735m() {
        AbstractC0251d.c cVar = this.f1513T;
        return (cVar == AbstractC0251d.c.INITIALIZED || this.f1497D == null) ? cVar.ordinal() : Math.min(cVar.ordinal(), this.f1497D.m735m());
    }

    /* renamed from: n */
    public final AbstractC0240x m736n() {
        AbstractC0240x abstractC0240x = this.f1494A;
        if (abstractC0240x != null) {
            return abstractC0240x;
        }
        throw new IllegalStateException("Fragment " + this + " not associated with a fragment manager.");
    }

    /* renamed from: o */
    public final boolean m737o() {
        b bVar = this.f1510Q;
        if (bVar == null) {
            return false;
        }
        return bVar.f1538b;
    }

    @Override // android.content.ComponentCallbacks
    public final void onConfigurationChanged(Configuration configuration) {
        this.f1505L = true;
    }

    @Override // android.view.View.OnCreateContextMenuListener
    public final void onCreateContextMenu(ContextMenu contextMenu, View view, ContextMenu.ContextMenuInfo contextMenuInfo) {
        ActivityC0229p m729f = m729f();
        if (m729f != null) {
            m729f.onCreateContextMenu(contextMenu, view, contextMenuInfo);
            return;
        }
        throw new IllegalStateException("Fragment " + this + " not attached to an activity.");
    }

    @Override // android.content.ComponentCallbacks
    public final void onLowMemory() {
        this.f1505L = true;
    }

    /* renamed from: p */
    public final int m738p() {
        b bVar = this.f1510Q;
        if (bVar == null) {
            return 0;
        }
        return bVar.f1541e;
    }

    /* renamed from: q */
    public final int m739q() {
        b bVar = this.f1510Q;
        if (bVar == null) {
            return 0;
        }
        return bVar.f1542f;
    }

    /* renamed from: r */
    public final Object m740r() {
        Object obj;
        b bVar = this.f1510Q;
        if (bVar == null || (obj = bVar.f1547k) == f1493Z) {
            return null;
        }
        return obj;
    }

    /* renamed from: s */
    public final Resources m741s() {
        return m721U().getResources();
    }

    /* renamed from: t */
    public final Object m742t() {
        Object obj;
        b bVar = this.f1510Q;
        if (bVar == null || (obj = bVar.f1546j) == f1493Z) {
            return null;
        }
        return obj;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder(128);
        sb.append(getClass().getSimpleName());
        sb.append("{");
        sb.append(Integer.toHexString(System.identityHashCode(this)));
        sb.append("}");
        sb.append(" (");
        sb.append(this.f1523n);
        if (this.f1498E != 0) {
            sb.append(" id=0x");
            sb.append(Integer.toHexString(this.f1498E));
        }
        if (this.f1500G != null) {
            sb.append(" tag=");
            sb.append(this.f1500G);
        }
        sb.append(")");
        return sb.toString();
    }

    /* renamed from: u */
    public final Object m743u() {
        Object obj;
        b bVar = this.f1510Q;
        if (bVar == null || (obj = bVar.f1548l) == f1493Z) {
            return null;
        }
        return obj;
    }

    /* renamed from: v */
    public final String m744v(int i6) {
        return m741s().getString(i6);
    }

    /* renamed from: w */
    public final boolean m745w() {
        return this.f1495B != null && this.f1529t;
    }

    /* renamed from: x */
    public final boolean m746x() {
        return this.f1535z > 0;
    }

    @Deprecated
    /* renamed from: y */
    public final void m747y(int i6, int i7, Intent intent) {
        if (AbstractC0240x.m791K(2)) {
            Log.v("FragmentManager", "Fragment " + this + " received the following in onActivityResult(): requestCode: " + i6 + " resultCode: " + i7 + " data: " + intent);
        }
    }

    /* renamed from: z */
    public void mo708z(Context context) {
        this.f1505L = true;
        AbstractC0237u<?> abstractC0237u = this.f1495B;
        if ((abstractC0237u == null ? null : abstractC0237u.f1591j) != null) {
            this.f1505L = true;
        }
    }
}
