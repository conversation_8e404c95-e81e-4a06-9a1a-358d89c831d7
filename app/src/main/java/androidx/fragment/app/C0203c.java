package androidx.fragment.app;

import android.content.Context;
import android.transition.Transition;
import android.view.View;
import android.view.ViewGroup;
import androidx.appcompat.widget.C0174y;
import androidx.fragment.app.AbstractC0228o0;
import androidx.fragment.app.C0231q;
import androidx.fragment.app.ComponentCallbacksC0223m;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.WeakHashMap;
import p001a0.C0002a;
import p029e0.C0766p;
import p029e0.C0769s;
import p077l.AbstractC1052f;
import p077l.C1047a;

/* renamed from: androidx.fragment.app.c */
/* loaded from: classes.dex */
public final class C0203c extends AbstractC0228o0 {

    /* renamed from: androidx.fragment.app.c$a */
    public class a implements Runnable {

        /* renamed from: j */
        public final /* synthetic */ List f1369j;

        /* renamed from: k */
        public final /* synthetic */ AbstractC0228o0.b f1370k;

        public a(List list, AbstractC0228o0.b bVar) {
            this.f1369j = list;
            this.f1370k = bVar;
        }

        @Override // java.lang.Runnable
        public final void run() {
            if (this.f1369j.contains(this.f1370k)) {
                this.f1369j.remove(this.f1370k);
                C0203c c0203c = C0203c.this;
                AbstractC0228o0.b bVar = this.f1370k;
                Objects.requireNonNull(c0203c);
                C0174y.m484b(bVar.f1564a, bVar.f1566c.f1507N);
            }
        }
    }

    /* renamed from: androidx.fragment.app.c$b */
    public static class b extends c {

        /* renamed from: c */
        public boolean f1372c;

        /* renamed from: d */
        public boolean f1373d;

        /* renamed from: e */
        public C0231q.a f1374e;

        public b(AbstractC0228o0.b bVar, C0002a c0002a, boolean z5) {
            super(bVar, c0002a);
            this.f1373d = false;
            this.f1372c = z5;
        }

        /* renamed from: c */
        public final C0231q.a m633c(Context context) {
            if (this.f1373d) {
                return this.f1374e;
            }
            AbstractC0228o0.b bVar = this.f1375a;
            C0231q.a m771a = C0231q.m771a(context, bVar.f1566c, bVar.f1564a == 2, this.f1372c);
            this.f1374e = m771a;
            this.f1373d = true;
            return m771a;
        }
    }

    /* renamed from: androidx.fragment.app.c$c */
    public static class c {

        /* renamed from: a */
        public final AbstractC0228o0.b f1375a;

        /* renamed from: b */
        public final C0002a f1376b;

        public c(AbstractC0228o0.b bVar, C0002a c0002a) {
            this.f1375a = bVar;
            this.f1376b = c0002a;
        }

        /* renamed from: a */
        public final void m634a() {
            AbstractC0228o0.b bVar = this.f1375a;
            if (bVar.f1568e.remove(this.f1376b) && bVar.f1568e.isEmpty()) {
                bVar.mo758c();
            }
        }

        /* renamed from: b */
        public final boolean m635b() {
            int m486d = C0174y.m486d(this.f1375a.f1566c.f1507N);
            int i6 = this.f1375a.f1564a;
            return m486d == i6 || !(m486d == 2 || i6 == 2);
        }
    }

    /* renamed from: androidx.fragment.app.c$d */
    public static class d extends c {

        /* renamed from: c */
        public final Object f1377c;

        /* renamed from: d */
        public final boolean f1378d;

        /* renamed from: e */
        public final Object f1379e;

        public d(AbstractC0228o0.b bVar, C0002a c0002a, boolean z5, boolean z6) {
            super(bVar, c0002a);
            Object obj;
            Object obj2;
            if (bVar.f1564a == 2) {
                if (z5) {
                    obj2 = bVar.f1566c.m740r();
                } else {
                    Objects.requireNonNull(bVar.f1566c);
                    obj2 = null;
                }
                this.f1377c = obj2;
                if (z5) {
                    ComponentCallbacksC0223m.b bVar2 = bVar.f1566c.f1510Q;
                } else {
                    ComponentCallbacksC0223m.b bVar3 = bVar.f1566c.f1510Q;
                }
            } else {
                if (z5) {
                    obj = bVar.f1566c.m742t();
                } else {
                    Objects.requireNonNull(bVar.f1566c);
                    obj = null;
                }
                this.f1377c = obj;
            }
            this.f1378d = true;
            if (z6) {
                if (z5) {
                    this.f1379e = bVar.f1566c.m743u();
                    return;
                }
                Objects.requireNonNull(bVar.f1566c);
            }
            this.f1379e = null;
        }

        /* renamed from: c */
        public final AbstractC0218j0 m636c(Object obj) {
            if (obj == null) {
                return null;
            }
            C0214h0 c0214h0 = C0212g0.f1439b;
            if (obj instanceof Transition) {
                return c0214h0;
            }
            AbstractC0218j0 abstractC0218j0 = C0212g0.f1440c;
            if (abstractC0218j0 != null && abstractC0218j0.mo675e(obj)) {
                return abstractC0218j0;
            }
            throw new IllegalArgumentException("Transition " + obj + " for fragment " + this.f1375a.f1566c + " is not a valid framework Transition or AndroidX Transition");
        }
    }

    public C0203c(ViewGroup viewGroup) {
        super(viewGroup);
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:251:0x0408  */
    /* JADX WARN: Removed duplicated region for block: B:254:0x046d  */
    /* JADX WARN: Removed duplicated region for block: B:260:0x048a  */
    /* JADX WARN: Removed duplicated region for block: B:261:0x0491  */
    /* JADX WARN: Removed duplicated region for block: B:262:0x0478  */
    /* JADX WARN: Removed duplicated region for block: B:263:0x041c  */
    @Override // androidx.fragment.app.AbstractC0228o0
    /* renamed from: b */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void mo629b(java.util.List<androidx.fragment.app.AbstractC0228o0.b> r37, boolean r38) {
        /*
            Method dump skipped, instructions count: 1717
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.fragment.app.C0203c.mo629b(java.util.List, boolean):void");
    }

    /* renamed from: j */
    public final void m630j(ArrayList<View> arrayList, View view) {
        if (!(view instanceof ViewGroup)) {
            if (arrayList.contains(view)) {
                return;
            }
            arrayList.add(view);
            return;
        }
        if (!arrayList.contains(view)) {
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            if (view.getTransitionName() != null) {
                arrayList.add(view);
            }
        }
        ViewGroup viewGroup = (ViewGroup) view;
        int childCount = viewGroup.getChildCount();
        for (int i6 = 0; i6 < childCount; i6++) {
            View childAt = viewGroup.getChildAt(i6);
            if (childAt.getVisibility() == 0) {
                m630j(arrayList, childAt);
            }
        }
    }

    /* renamed from: k */
    public final void m631k(Map<String, View> map, View view) {
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        String transitionName = view.getTransitionName();
        if (transitionName != null) {
            map.put(transitionName, view);
        }
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            int childCount = viewGroup.getChildCount();
            for (int i6 = 0; i6 < childCount; i6++) {
                View childAt = viewGroup.getChildAt(i6);
                if (childAt.getVisibility() == 0) {
                    m631k(map, childAt);
                }
            }
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* renamed from: l */
    public final void m632l(C1047a<String, View> c1047a, Collection<String> collection) {
        Iterator it = ((AbstractC1052f.b) c1047a.entrySet()).iterator();
        while (true) {
            AbstractC1052f.d dVar = (AbstractC1052f.d) it;
            if (!dVar.hasNext()) {
                return;
            }
            dVar.next();
            View view = (View) dVar.getValue();
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            if (!collection.contains(view.getTransitionName())) {
                dVar.remove();
            }
        }
    }
}
