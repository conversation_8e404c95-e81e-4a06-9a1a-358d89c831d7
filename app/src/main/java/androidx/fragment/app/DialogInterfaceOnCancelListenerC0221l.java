package androidx.fragment.app;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.activity.result.AbstractC0055d;
import androidx.appcompat.widget.C0174y;
import androidx.fragment.app.AbstractC0240x.n;
import androidx.fragment.app.ComponentCallbacksC0223m;
import androidx.lifecycle.InterfaceC0254g;
import androidx.lifecycle.InterfaceC0260m;
import com.liaoyuan.aicast.R;
import p008b0.C0385m;

/* renamed from: androidx.fragment.app.l */
/* loaded from: classes.dex */
public class DialogInterfaceOnCancelListenerC0221l extends ComponentCallbacksC0223m implements DialogInterface.OnCancelListener, DialogInterface.OnDismissListener {

    /* renamed from: a0 */
    public Handler f1470a0;

    /* renamed from: j0 */
    public boolean f1479j0;

    /* renamed from: l0 */
    public Dialog f1481l0;

    /* renamed from: m0 */
    public boolean f1482m0;

    /* renamed from: n0 */
    public boolean f1483n0;

    /* renamed from: b0 */
    public a f1471b0 = new a();

    /* renamed from: c0 */
    public b f1472c0 = new b();

    /* renamed from: d0 */
    public c f1473d0 = new c();

    /* renamed from: e0 */
    public int f1474e0 = 0;

    /* renamed from: f0 */
    public int f1475f0 = 0;

    /* renamed from: g0 */
    public boolean f1476g0 = true;

    /* renamed from: h0 */
    public boolean f1477h0 = true;

    /* renamed from: i0 */
    public int f1478i0 = -1;

    /* renamed from: k0 */
    public InterfaceC0260m<InterfaceC0254g> f1480k0 = new d();

    /* renamed from: o0 */
    public boolean f1484o0 = false;

    /* renamed from: androidx.fragment.app.l$a */
    public class a implements Runnable {
        public a() {
        }

        @Override // java.lang.Runnable
        @SuppressLint({"SyntheticAccessor"})
        public final void run() {
            DialogInterfaceOnCancelListenerC0221l dialogInterfaceOnCancelListenerC0221l = DialogInterfaceOnCancelListenerC0221l.this;
            dialogInterfaceOnCancelListenerC0221l.f1473d0.onDismiss(dialogInterfaceOnCancelListenerC0221l.f1481l0);
        }
    }

    /* renamed from: androidx.fragment.app.l$b */
    public class b implements DialogInterface.OnCancelListener {
        public b() {
        }

        @Override // android.content.DialogInterface.OnCancelListener
        @SuppressLint({"SyntheticAccessor"})
        public final void onCancel(DialogInterface dialogInterface) {
            DialogInterfaceOnCancelListenerC0221l dialogInterfaceOnCancelListenerC0221l = DialogInterfaceOnCancelListenerC0221l.this;
            Dialog dialog = dialogInterfaceOnCancelListenerC0221l.f1481l0;
            if (dialog != null) {
                dialogInterfaceOnCancelListenerC0221l.onCancel(dialog);
            }
        }
    }

    /* renamed from: androidx.fragment.app.l$c */
    public class c implements DialogInterface.OnDismissListener {
        public c() {
        }

        @Override // android.content.DialogInterface.OnDismissListener
        @SuppressLint({"SyntheticAccessor"})
        public final void onDismiss(DialogInterface dialogInterface) {
            DialogInterfaceOnCancelListenerC0221l dialogInterfaceOnCancelListenerC0221l = DialogInterfaceOnCancelListenerC0221l.this;
            Dialog dialog = dialogInterfaceOnCancelListenerC0221l.f1481l0;
            if (dialog != null) {
                dialogInterfaceOnCancelListenerC0221l.onDismiss(dialog);
            }
        }
    }

    /* renamed from: androidx.fragment.app.l$d */
    public class d implements InterfaceC0260m<InterfaceC0254g> {
        public d() {
        }
    }

    /* renamed from: androidx.fragment.app.l$e */
    public class e extends AbstractC0055d {

        /* renamed from: a */
        public final /* synthetic */ AbstractC0055d f1489a;

        public e(AbstractC0055d abstractC0055d) {
            this.f1489a = abstractC0055d;
        }

        @Override // androidx.activity.result.AbstractC0055d
        /* renamed from: k */
        public final View mo118k(int i6) {
            if (this.f1489a.mo121n()) {
                return this.f1489a.mo118k(i6);
            }
            Dialog dialog = DialogInterfaceOnCancelListenerC0221l.this.f1481l0;
            if (dialog != null) {
                return dialog.findViewById(i6);
            }
            return null;
        }

        @Override // androidx.activity.result.AbstractC0055d
        /* renamed from: n */
        public final boolean mo121n() {
            return this.f1489a.mo121n() || DialogInterfaceOnCancelListenerC0221l.this.f1484o0;
        }
    }

    @Override // androidx.fragment.app.ComponentCallbacksC0223m
    /* renamed from: A */
    public void mo695A(Bundle bundle) {
        super.mo695A(bundle);
        this.f1470a0 = new Handler();
        this.f1477h0 = this.f1499F == 0;
        if (bundle != null) {
            this.f1474e0 = bundle.getInt("android:style", 0);
            this.f1475f0 = bundle.getInt("android:theme", 0);
            this.f1476g0 = bundle.getBoolean("android:cancelable", true);
            this.f1477h0 = bundle.getBoolean("android:showsDialog", this.f1477h0);
            this.f1478i0 = bundle.getInt("android:backStackId", -1);
        }
    }

    @Override // androidx.fragment.app.ComponentCallbacksC0223m
    /* renamed from: D */
    public final void mo696D() {
        this.f1505L = true;
        Dialog dialog = this.f1481l0;
        if (dialog != null) {
            this.f1482m0 = true;
            dialog.setOnDismissListener(null);
            this.f1481l0.dismiss();
            if (!this.f1483n0) {
                onDismiss(this.f1481l0);
            }
            this.f1481l0 = null;
            this.f1484o0 = false;
        }
    }

    @Override // androidx.fragment.app.ComponentCallbacksC0223m
    /* renamed from: E */
    public final void mo697E() {
        this.f1505L = true;
        if (!this.f1483n0) {
            this.f1483n0 = true;
        }
        this.f1516W.mo856g(this.f1480k0);
    }

    /* JADX WARN: Removed duplicated region for block: B:20:0x0046 A[Catch: all -> 0x006b, TryCatch #0 {all -> 0x006b, blocks: (B:10:0x001a, B:12:0x0026, B:18:0x003e, B:20:0x0046, B:21:0x004d, B:23:0x0030, B:25:0x0036, B:26:0x003b, B:27:0x0065), top: B:9:0x001a }] */
    @Override // androidx.fragment.app.ComponentCallbacksC0223m
    /* renamed from: F */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final android.view.LayoutInflater mo698F(android.os.Bundle r8) {
        /*
            r7 = this;
            android.view.LayoutInflater r8 = super.mo698F(r8)
            boolean r0 = r7.f1477h0
            java.lang.String r1 = "FragmentManager"
            r2 = 2
            if (r0 == 0) goto L9b
            boolean r3 = r7.f1479j0
            if (r3 == 0) goto L11
            goto L9b
        L11:
            if (r0 != 0) goto L14
            goto L6f
        L14:
            boolean r0 = r7.f1484o0
            if (r0 != 0) goto L6f
            r0 = 0
            r3 = 1
            r7.f1479j0 = r3     // Catch: java.lang.Throwable -> L6b
            android.app.Dialog r4 = r7.mo705c0()     // Catch: java.lang.Throwable -> L6b
            r7.f1481l0 = r4     // Catch: java.lang.Throwable -> L6b
            boolean r5 = r7.f1477h0     // Catch: java.lang.Throwable -> L6b
            if (r5 == 0) goto L65
            int r5 = r7.f1474e0     // Catch: java.lang.Throwable -> L6b
            if (r5 == r3) goto L3b
            if (r5 == r2) goto L3b
            r6 = 3
            if (r5 == r6) goto L30
            goto L3e
        L30:
            android.view.Window r5 = r4.getWindow()     // Catch: java.lang.Throwable -> L6b
            if (r5 == 0) goto L3b
            r6 = 24
            r5.addFlags(r6)     // Catch: java.lang.Throwable -> L6b
        L3b:
            r4.requestWindowFeature(r3)     // Catch: java.lang.Throwable -> L6b
        L3e:
            android.content.Context r4 = r7.m732j()     // Catch: java.lang.Throwable -> L6b
            boolean r5 = r4 instanceof android.app.Activity     // Catch: java.lang.Throwable -> L6b
            if (r5 == 0) goto L4d
            android.app.Dialog r5 = r7.f1481l0     // Catch: java.lang.Throwable -> L6b
            android.app.Activity r4 = (android.app.Activity) r4     // Catch: java.lang.Throwable -> L6b
            r5.setOwnerActivity(r4)     // Catch: java.lang.Throwable -> L6b
        L4d:
            android.app.Dialog r4 = r7.f1481l0     // Catch: java.lang.Throwable -> L6b
            boolean r5 = r7.f1476g0     // Catch: java.lang.Throwable -> L6b
            r4.setCancelable(r5)     // Catch: java.lang.Throwable -> L6b
            android.app.Dialog r4 = r7.f1481l0     // Catch: java.lang.Throwable -> L6b
            androidx.fragment.app.l$b r5 = r7.f1472c0     // Catch: java.lang.Throwable -> L6b
            r4.setOnCancelListener(r5)     // Catch: java.lang.Throwable -> L6b
            android.app.Dialog r4 = r7.f1481l0     // Catch: java.lang.Throwable -> L6b
            androidx.fragment.app.l$c r5 = r7.f1473d0     // Catch: java.lang.Throwable -> L6b
            r4.setOnDismissListener(r5)     // Catch: java.lang.Throwable -> L6b
            r7.f1484o0 = r3     // Catch: java.lang.Throwable -> L6b
            goto L68
        L65:
            r3 = 0
            r7.f1481l0 = r3     // Catch: java.lang.Throwable -> L6b
        L68:
            r7.f1479j0 = r0
            goto L6f
        L6b:
            r8 = move-exception
            r7.f1479j0 = r0
            throw r8
        L6f:
            boolean r0 = androidx.fragment.app.AbstractC0240x.m791K(r2)
            if (r0 == 0) goto L8e
            java.lang.StringBuilder r0 = new java.lang.StringBuilder
            r0.<init>()
            java.lang.String r2 = "get layout inflater for DialogFragment "
            r0.append(r2)
            r0.append(r7)
            java.lang.String r2 = " from dialog context"
            r0.append(r2)
            java.lang.String r0 = r0.toString()
            android.util.Log.d(r1, r0)
        L8e:
            android.app.Dialog r0 = r7.f1481l0
            if (r0 == 0) goto L9a
            android.content.Context r0 = r0.getContext()
            android.view.LayoutInflater r8 = r8.cloneInContext(r0)
        L9a:
            return r8
        L9b:
            boolean r0 = androidx.fragment.app.AbstractC0240x.m791K(r2)
            if (r0 == 0) goto Ld2
            java.lang.StringBuilder r0 = new java.lang.StringBuilder
            r0.<init>()
            java.lang.String r2 = "getting layout inflater for DialogFragment "
            r0.append(r2)
            r0.append(r7)
            java.lang.String r0 = r0.toString()
            boolean r2 = r7.f1477h0
            if (r2 != 0) goto Lbe
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>()
            java.lang.String r3 = "mShowsDialog = false: "
            goto Lc5
        Lbe:
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>()
            java.lang.String r3 = "mCreatingDialog = true: "
        Lc5:
            r2.append(r3)
            r2.append(r0)
            java.lang.String r0 = r2.toString()
            android.util.Log.d(r1, r0)
        Ld2:
            return r8
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.fragment.app.DialogInterfaceOnCancelListenerC0221l.mo698F(android.os.Bundle):android.view.LayoutInflater");
    }

    @Override // androidx.fragment.app.ComponentCallbacksC0223m
    /* renamed from: J */
    public void mo699J(Bundle bundle) {
        Dialog dialog = this.f1481l0;
        if (dialog != null) {
            Bundle onSaveInstanceState = dialog.onSaveInstanceState();
            onSaveInstanceState.putBoolean("android:dialogShowing", false);
            bundle.putBundle("android:savedDialogState", onSaveInstanceState);
        }
        int i6 = this.f1474e0;
        if (i6 != 0) {
            bundle.putInt("android:style", i6);
        }
        int i7 = this.f1475f0;
        if (i7 != 0) {
            bundle.putInt("android:theme", i7);
        }
        boolean z5 = this.f1476g0;
        if (!z5) {
            bundle.putBoolean("android:cancelable", z5);
        }
        boolean z6 = this.f1477h0;
        if (!z6) {
            bundle.putBoolean("android:showsDialog", z6);
        }
        int i8 = this.f1478i0;
        if (i8 != -1) {
            bundle.putInt("android:backStackId", i8);
        }
    }

    @Override // androidx.fragment.app.ComponentCallbacksC0223m
    /* renamed from: K */
    public void mo700K() {
        this.f1505L = true;
        Dialog dialog = this.f1481l0;
        if (dialog != null) {
            this.f1482m0 = false;
            dialog.show();
            View decorView = this.f1481l0.getWindow().getDecorView();
            C0385m.m1423o(decorView, this);
            decorView.setTag(R.id.view_tree_view_model_store_owner, this);
            decorView.setTag(R.id.view_tree_saved_state_registry_owner, this);
        }
    }

    @Override // androidx.fragment.app.ComponentCallbacksC0223m
    /* renamed from: L */
    public void mo701L() {
        this.f1505L = true;
        Dialog dialog = this.f1481l0;
        if (dialog != null) {
            dialog.hide();
        }
    }

    @Override // androidx.fragment.app.ComponentCallbacksC0223m
    /* renamed from: N */
    public final void mo702N(Bundle bundle) {
        Bundle bundle2;
        this.f1505L = true;
        if (this.f1481l0 == null || bundle == null || (bundle2 = bundle.getBundle("android:savedDialogState")) == null) {
            return;
        }
        this.f1481l0.onRestoreInstanceState(bundle2);
    }

    @Override // androidx.fragment.app.ComponentCallbacksC0223m
    /* renamed from: O */
    public final void mo703O(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        Bundle bundle2;
        super.mo703O(layoutInflater, viewGroup, bundle);
        if (this.f1507N != null || this.f1481l0 == null || bundle == null || (bundle2 = bundle.getBundle("android:savedDialogState")) == null) {
            return;
        }
        this.f1481l0.onRestoreInstanceState(bundle2);
    }

    /* renamed from: b0 */
    public final void m704b0(boolean z5, boolean z6) {
        if (this.f1483n0) {
            return;
        }
        this.f1483n0 = true;
        Dialog dialog = this.f1481l0;
        if (dialog != null) {
            dialog.setOnDismissListener(null);
            this.f1481l0.dismiss();
            if (!z6) {
                if (Looper.myLooper() == this.f1470a0.getLooper()) {
                    onDismiss(this.f1481l0);
                } else {
                    this.f1470a0.post(this.f1471b0);
                }
            }
        }
        this.f1482m0 = true;
        if (this.f1478i0 >= 0) {
            AbstractC0240x m736n = m736n();
            int i6 = this.f1478i0;
            if (i6 < 0) {
                throw new IllegalArgumentException(C0174y.m490h("Bad id: ", i6));
            }
            m736n.m846x(m736n.new n(i6), false);
            this.f1478i0 = -1;
            return;
        }
        C0199a c0199a = new C0199a(m736n());
        c0199a.m623j(this);
        if (z5) {
            c0199a.m617d(true);
        } else {
            c0199a.m617d(false);
        }
    }

    /* renamed from: c0 */
    public Dialog mo705c0() {
        if (AbstractC0240x.m791K(3)) {
            Log.d("FragmentManager", "onCreateDialog called for DialogFragment " + this);
        }
        return new Dialog(m721U(), this.f1475f0);
    }

    @Override // androidx.fragment.app.ComponentCallbacksC0223m
    /* renamed from: d */
    public final AbstractC0055d mo706d() {
        return new e(new ComponentCallbacksC0223m.a());
    }

    /* renamed from: d0 */
    public final Dialog m707d0() {
        Dialog dialog = this.f1481l0;
        if (dialog != null) {
            return dialog;
        }
        throw new IllegalStateException("DialogFragment " + this + " does not have a Dialog.");
    }

    @Override // android.content.DialogInterface.OnCancelListener
    public void onCancel(DialogInterface dialogInterface) {
    }

    @Override // android.content.DialogInterface.OnDismissListener
    public void onDismiss(DialogInterface dialogInterface) {
        if (this.f1482m0) {
            return;
        }
        if (AbstractC0240x.m791K(3)) {
            Log.d("FragmentManager", "onDismiss called for DialogFragment " + this);
        }
        m704b0(true, true);
    }

    @Override // androidx.fragment.app.ComponentCallbacksC0223m
    /* renamed from: z */
    public final void mo708z(Context context) {
        super.mo708z(context);
        this.f1516W.m853d(this.f1480k0);
        this.f1483n0 = false;
    }
}
