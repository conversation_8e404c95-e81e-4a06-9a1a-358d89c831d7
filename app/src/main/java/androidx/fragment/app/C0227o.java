package androidx.fragment.app;

import android.os.Bundle;
import android.os.Parcelable;
import androidx.lifecycle.InterfaceC0266s;
import p000a.InterfaceC0001b;

/* renamed from: androidx.fragment.app.o */
/* loaded from: classes.dex */
public final class C0227o implements InterfaceC0001b {

    /* renamed from: a */
    public final /* synthetic */ ActivityC0229p f1557a;

    public C0227o(ActivityC0229p activityC0229p) {
        this.f1557a = activityC0229p;
    }

    @Override // p000a.InterfaceC0001b
    /* renamed from: a */
    public final void mo0a() {
        AbstractC0237u<?> abstractC0237u = this.f1557a.f1571q.f1589a;
        abstractC0237u.f1594m.m819b(abstractC0237u, abstractC0237u, null);
        Bundle m1361a = this.f1557a.f185m.f2181b.m1361a("android:support:fragments");
        if (m1361a != null) {
            Parcelable parcelable = m1361a.getParcelable("android:support:fragments");
            AbstractC0237u<?> abstractC0237u2 = this.f1557a.f1571q.f1589a;
            if (!(abstractC0237u2 instanceof InterfaceC0266s)) {
                throw new IllegalStateException("Your FragmentHostCallback must implement ViewModelStoreOwner to call restoreSaveState(). Call restoreAllState()  if you're still using retainNestedNonConfig().");
            }
            abstractC0237u2.f1594m.m812V(parcelable);
        }
    }
}
