package androidx.fragment.app;

import android.view.View;
import java.util.ArrayList;
import java.util.Objects;
import p077l.C1047a;
import p129t0.C1362d;

/* renamed from: androidx.fragment.app.g0 */
/* loaded from: classes.dex */
public final class C0212g0 {

    /* renamed from: a */
    public static final int[] f1438a = {0, 3, 0, 1, 5, 4, 7, 6, 9, 8, 10};

    /* renamed from: b */
    public static final C0214h0 f1439b = new C0214h0();

    /* renamed from: c */
    public static final AbstractC0218j0 f1440c;

    static {
        AbstractC0218j0 abstractC0218j0;
        try {
            abstractC0218j0 = (AbstractC0218j0) C1362d.class.getDeclaredConstructor(new Class[0]).newInstance(new Object[0]);
        } catch (Exception unused) {
            abstractC0218j0 = null;
        }
        f1440c = abstractC0218j0;
    }

    /* renamed from: a */
    public static void m668a(ComponentCallbacksC0223m componentCallbacksC0223m, ComponentCallbacksC0223m componentCallbacksC0223m2, boolean z5) {
        if (z5) {
            Objects.requireNonNull(componentCallbacksC0223m2);
        } else {
            Objects.requireNonNull(componentCallbacksC0223m);
        }
    }

    /* renamed from: b */
    public static void m669b(C1047a<String, String> c1047a, C1047a<String, View> c1047a2) {
        int i6 = c1047a.f5038l;
        while (true) {
            i6--;
            if (i6 < 0) {
                return;
            }
            if (!c1047a2.containsKey(c1047a.m2694l(i6))) {
                c1047a.mo2692j(i6);
            }
        }
    }

    /* renamed from: c */
    public static void m670c(ArrayList<View> arrayList, int i6) {
        if (arrayList == null) {
            return;
        }
        for (int size = arrayList.size() - 1; size >= 0; size--) {
            arrayList.get(size).setVisibility(i6);
        }
    }
}
