package androidx.fragment.app;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import androidx.fragment.app.AbstractC0240x;
import java.util.ArrayList;

@SuppressLint({"BanParcelableUsage"})
/* renamed from: androidx.fragment.app.z */
/* loaded from: classes.dex */
public final class C0242z implements Parcelable {
    public static final Parcelable.Creator<C0242z> CREATOR = new a();

    /* renamed from: j */
    public ArrayList<C0204c0> f1650j;

    /* renamed from: k */
    public ArrayList<String> f1651k;

    /* renamed from: l */
    public C0201b[] f1652l;

    /* renamed from: m */
    public int f1653m;

    /* renamed from: n */
    public String f1654n;

    /* renamed from: o */
    public ArrayList<String> f1655o;

    /* renamed from: p */
    public ArrayList<Bundle> f1656p;

    /* renamed from: q */
    public ArrayList<AbstractC0240x.l> f1657q;

    /* renamed from: androidx.fragment.app.z$a */
    public class a implements Parcelable.Creator<C0242z> {
        @Override // android.os.Parcelable.Creator
        public final C0242z createFromParcel(Parcel parcel) {
            return new C0242z(parcel);
        }

        @Override // android.os.Parcelable.Creator
        public final C0242z[] newArray(int i6) {
            return new C0242z[i6];
        }
    }

    public C0242z() {
        this.f1654n = null;
        this.f1655o = new ArrayList<>();
        this.f1656p = new ArrayList<>();
    }

    public C0242z(Parcel parcel) {
        this.f1654n = null;
        this.f1655o = new ArrayList<>();
        this.f1656p = new ArrayList<>();
        this.f1650j = parcel.createTypedArrayList(C0204c0.CREATOR);
        this.f1651k = parcel.createStringArrayList();
        this.f1652l = (C0201b[]) parcel.createTypedArray(C0201b.CREATOR);
        this.f1653m = parcel.readInt();
        this.f1654n = parcel.readString();
        this.f1655o = parcel.createStringArrayList();
        this.f1656p = parcel.createTypedArrayList(Bundle.CREATOR);
        this.f1657q = parcel.createTypedArrayList(AbstractC0240x.l.CREATOR);
    }

    @Override // android.os.Parcelable
    public final int describeContents() {
        return 0;
    }

    @Override // android.os.Parcelable
    public final void writeToParcel(Parcel parcel, int i6) {
        parcel.writeTypedList(this.f1650j);
        parcel.writeStringList(this.f1651k);
        parcel.writeTypedArray(this.f1652l, i6);
        parcel.writeInt(this.f1653m);
        parcel.writeString(this.f1654n);
        parcel.writeStringList(this.f1655o);
        parcel.writeTypedList(this.f1656p);
        parcel.writeTypedList(this.f1657q);
    }
}
