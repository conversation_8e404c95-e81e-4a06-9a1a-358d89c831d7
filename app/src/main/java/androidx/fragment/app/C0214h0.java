package androidx.fragment.app;

import android.graphics.Rect;
import android.transition.Transition;
import android.transition.TransitionManager;
import android.transition.TransitionSet;
import android.view.View;
import android.view.ViewGroup;
import java.util.ArrayList;
import java.util.List;

/* renamed from: androidx.fragment.app.h0 */
/* loaded from: classes.dex */
public final class C0214h0 extends AbstractC0218j0 {

    /* renamed from: androidx.fragment.app.h0$a */
    public class a extends Transition.EpicenterCallback {

        /* renamed from: a */
        public final /* synthetic */ Rect f1445a;

        public a(Rect rect) {
            this.f1445a = rect;
        }

        @Override // android.transition.Transition.EpicenterCallback
        public final Rect onGetEpicenter(Transition transition) {
            return this.f1445a;
        }
    }

    /* renamed from: androidx.fragment.app.h0$b */
    public class b implements Transition.TransitionListener {

        /* renamed from: a */
        public final /* synthetic */ View f1446a;

        /* renamed from: b */
        public final /* synthetic */ ArrayList f1447b;

        public b(View view, ArrayList arrayList) {
            this.f1446a = view;
            this.f1447b = arrayList;
        }

        @Override // android.transition.Transition.TransitionListener
        public final void onTransitionCancel(Transition transition) {
        }

        @Override // android.transition.Transition.TransitionListener
        public final void onTransitionEnd(Transition transition) {
            transition.removeListener(this);
            this.f1446a.setVisibility(8);
            int size = this.f1447b.size();
            for (int i6 = 0; i6 < size; i6++) {
                ((View) this.f1447b.get(i6)).setVisibility(0);
            }
        }

        @Override // android.transition.Transition.TransitionListener
        public final void onTransitionPause(Transition transition) {
        }

        @Override // android.transition.Transition.TransitionListener
        public final void onTransitionResume(Transition transition) {
        }

        @Override // android.transition.Transition.TransitionListener
        public final void onTransitionStart(Transition transition) {
            transition.removeListener(this);
            transition.addListener(this);
        }
    }

    /* renamed from: androidx.fragment.app.h0$c */
    public class c implements Transition.TransitionListener {

        /* renamed from: a */
        public final /* synthetic */ Object f1448a;

        /* renamed from: b */
        public final /* synthetic */ ArrayList f1449b;

        /* renamed from: c */
        public final /* synthetic */ Object f1450c;

        /* renamed from: d */
        public final /* synthetic */ ArrayList f1451d;

        /* renamed from: e */
        public final /* synthetic */ Object f1452e;

        /* renamed from: f */
        public final /* synthetic */ ArrayList f1453f;

        public c(Object obj, ArrayList arrayList, Object obj2, ArrayList arrayList2, Object obj3, ArrayList arrayList3) {
            this.f1448a = obj;
            this.f1449b = arrayList;
            this.f1450c = obj2;
            this.f1451d = arrayList2;
            this.f1452e = obj3;
            this.f1453f = arrayList3;
        }

        @Override // android.transition.Transition.TransitionListener
        public final void onTransitionCancel(Transition transition) {
        }

        @Override // android.transition.Transition.TransitionListener
        public final void onTransitionEnd(Transition transition) {
            transition.removeListener(this);
        }

        @Override // android.transition.Transition.TransitionListener
        public final void onTransitionPause(Transition transition) {
        }

        @Override // android.transition.Transition.TransitionListener
        public final void onTransitionResume(Transition transition) {
        }

        @Override // android.transition.Transition.TransitionListener
        public final void onTransitionStart(Transition transition) {
            Object obj = this.f1448a;
            if (obj != null) {
                C0214h0.this.m687v(obj, this.f1449b, null);
            }
            Object obj2 = this.f1450c;
            if (obj2 != null) {
                C0214h0.this.m687v(obj2, this.f1451d, null);
            }
            Object obj3 = this.f1452e;
            if (obj3 != null) {
                C0214h0.this.m687v(obj3, this.f1453f, null);
            }
        }
    }

    /* renamed from: androidx.fragment.app.h0$d */
    public class d extends Transition.EpicenterCallback {

        /* renamed from: a */
        public final /* synthetic */ Rect f1455a;

        public d(Rect rect) {
            this.f1455a = rect;
        }

        @Override // android.transition.Transition.EpicenterCallback
        public final Rect onGetEpicenter(Transition transition) {
            Rect rect = this.f1455a;
            if (rect == null || rect.isEmpty()) {
                return null;
            }
            return this.f1455a;
        }
    }

    /* renamed from: u */
    public static boolean m671u(Transition transition) {
        return (AbstractC0218j0.m689h(transition.getTargetIds()) && AbstractC0218j0.m689h(transition.getTargetNames()) && AbstractC0218j0.m689h(transition.getTargetTypes())) ? false : true;
    }

    @Override // androidx.fragment.app.AbstractC0218j0
    /* renamed from: a */
    public final void mo672a(Object obj, View view) {
        if (obj != null) {
            ((Transition) obj).addTarget(view);
        }
    }

    @Override // androidx.fragment.app.AbstractC0218j0
    /* renamed from: b */
    public final void mo673b(Object obj, ArrayList<View> arrayList) {
        Transition transition = (Transition) obj;
        if (transition == null) {
            return;
        }
        int i6 = 0;
        if (transition instanceof TransitionSet) {
            TransitionSet transitionSet = (TransitionSet) transition;
            int transitionCount = transitionSet.getTransitionCount();
            while (i6 < transitionCount) {
                mo673b(transitionSet.getTransitionAt(i6), arrayList);
                i6++;
            }
            return;
        }
        if (m671u(transition) || !AbstractC0218j0.m689h(transition.getTargets())) {
            return;
        }
        int size = arrayList.size();
        while (i6 < size) {
            transition.addTarget(arrayList.get(i6));
            i6++;
        }
    }

    @Override // androidx.fragment.app.AbstractC0218j0
    /* renamed from: c */
    public final void mo674c(ViewGroup viewGroup, Object obj) {
        TransitionManager.beginDelayedTransition(viewGroup, (Transition) obj);
    }

    @Override // androidx.fragment.app.AbstractC0218j0
    /* renamed from: e */
    public final boolean mo675e(Object obj) {
        return obj instanceof Transition;
    }

    @Override // androidx.fragment.app.AbstractC0218j0
    /* renamed from: f */
    public final Object mo676f(Object obj) {
        if (obj != null) {
            return ((Transition) obj).clone();
        }
        return null;
    }

    @Override // androidx.fragment.app.AbstractC0218j0
    /* renamed from: i */
    public final Object mo677i(Object obj, Object obj2, Object obj3) {
        Transition transition = (Transition) obj;
        Transition transition2 = (Transition) obj2;
        Transition transition3 = (Transition) obj3;
        if (transition != null && transition2 != null) {
            transition = new TransitionSet().addTransition(transition).addTransition(transition2).setOrdering(1);
        } else if (transition == null) {
            transition = transition2 != null ? transition2 : null;
        }
        if (transition3 == null) {
            return transition;
        }
        TransitionSet transitionSet = new TransitionSet();
        if (transition != null) {
            transitionSet.addTransition(transition);
        }
        transitionSet.addTransition(transition3);
        return transitionSet;
    }

    @Override // androidx.fragment.app.AbstractC0218j0
    /* renamed from: j */
    public final Object mo678j(Object obj, Object obj2, Object obj3) {
        TransitionSet transitionSet = new TransitionSet();
        if (obj != null) {
            transitionSet.addTransition((Transition) obj);
        }
        if (obj2 != null) {
            transitionSet.addTransition((Transition) obj2);
        }
        if (obj3 != null) {
            transitionSet.addTransition((Transition) obj3);
        }
        return transitionSet;
    }

    @Override // androidx.fragment.app.AbstractC0218j0
    /* renamed from: l */
    public final void mo679l(Object obj, View view, ArrayList<View> arrayList) {
        ((Transition) obj).addListener(new b(view, arrayList));
    }

    @Override // androidx.fragment.app.AbstractC0218j0
    /* renamed from: m */
    public final void mo680m(Object obj, Object obj2, ArrayList<View> arrayList, Object obj3, ArrayList<View> arrayList2, Object obj4, ArrayList<View> arrayList3) {
        ((Transition) obj).addListener(new c(obj2, arrayList, obj3, arrayList2, obj4, arrayList3));
    }

    @Override // androidx.fragment.app.AbstractC0218j0
    /* renamed from: n */
    public final void mo681n(Object obj, Rect rect) {
        if (obj != null) {
            ((Transition) obj).setEpicenterCallback(new d(rect));
        }
    }

    @Override // androidx.fragment.app.AbstractC0218j0
    /* renamed from: o */
    public final void mo682o(Object obj, View view) {
        if (view != null) {
            Rect rect = new Rect();
            m690g(view, rect);
            ((Transition) obj).setEpicenterCallback(new a(rect));
        }
    }

    @Override // androidx.fragment.app.AbstractC0218j0
    /* renamed from: p */
    public final void mo683p(Object obj, Runnable runnable) {
        ((Transition) obj).addListener(new C0216i0(runnable));
    }

    @Override // androidx.fragment.app.AbstractC0218j0
    /* renamed from: r */
    public final void mo684r(Object obj, View view, ArrayList<View> arrayList) {
        TransitionSet transitionSet = (TransitionSet) obj;
        List<View> targets = transitionSet.getTargets();
        targets.clear();
        int size = arrayList.size();
        for (int i6 = 0; i6 < size; i6++) {
            AbstractC0218j0.m688d(targets, arrayList.get(i6));
        }
        targets.add(view);
        arrayList.add(view);
        mo673b(transitionSet, arrayList);
    }

    @Override // androidx.fragment.app.AbstractC0218j0
    /* renamed from: s */
    public final void mo685s(Object obj, ArrayList<View> arrayList, ArrayList<View> arrayList2) {
        TransitionSet transitionSet = (TransitionSet) obj;
        if (transitionSet != null) {
            transitionSet.getTargets().clear();
            transitionSet.getTargets().addAll(arrayList2);
            m687v(transitionSet, arrayList, arrayList2);
        }
    }

    @Override // androidx.fragment.app.AbstractC0218j0
    /* renamed from: t */
    public final Object mo686t(Object obj) {
        if (obj == null) {
            return null;
        }
        TransitionSet transitionSet = new TransitionSet();
        transitionSet.addTransition((Transition) obj);
        return transitionSet;
    }

    /* renamed from: v */
    public final void m687v(Object obj, ArrayList<View> arrayList, ArrayList<View> arrayList2) {
        List<View> targets;
        Transition transition = (Transition) obj;
        int i6 = 0;
        if (transition instanceof TransitionSet) {
            TransitionSet transitionSet = (TransitionSet) transition;
            int transitionCount = transitionSet.getTransitionCount();
            while (i6 < transitionCount) {
                m687v(transitionSet.getTransitionAt(i6), arrayList, arrayList2);
                i6++;
            }
            return;
        }
        if (m671u(transition) || (targets = transition.getTargets()) == null || targets.size() != arrayList.size() || !targets.containsAll(arrayList)) {
            return;
        }
        int size = arrayList2 == null ? 0 : arrayList2.size();
        while (i6 < size) {
            transition.addTarget(arrayList2.get(i6));
            i6++;
        }
        int size2 = arrayList.size();
        while (true) {
            size2--;
            if (size2 < 0) {
                return;
            } else {
                transition.removeTarget(arrayList.get(size2));
            }
        }
    }
}
