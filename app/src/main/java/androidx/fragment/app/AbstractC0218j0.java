package androidx.fragment.app;

import android.annotation.SuppressLint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.view.View;
import android.view.ViewGroup;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.WeakHashMap;
import p029e0.C0766p;
import p029e0.C0769s;
import p029e0.ViewTreeObserverOnPreDrawListenerC0765o;

@SuppressLint({"UnknownNullness"})
/* renamed from: androidx.fragment.app.j0 */
/* loaded from: classes.dex */
public abstract class AbstractC0218j0 {

    /* renamed from: androidx.fragment.app.j0$a */
    public class a implements Runnable {

        /* renamed from: j */
        public final /* synthetic */ int f1461j;

        /* renamed from: k */
        public final /* synthetic */ ArrayList f1462k;

        /* renamed from: l */
        public final /* synthetic */ ArrayList f1463l;

        /* renamed from: m */
        public final /* synthetic */ ArrayList f1464m;

        /* renamed from: n */
        public final /* synthetic */ ArrayList f1465n;

        public a(int i6, ArrayList arrayList, ArrayList arrayList2, ArrayList arrayList3, ArrayList arrayList4) {
            this.f1461j = i6;
            this.f1462k = arrayList;
            this.f1463l = arrayList2;
            this.f1464m = arrayList3;
            this.f1465n = arrayList4;
        }

        @Override // java.lang.Runnable
        public final void run() {
            for (int i6 = 0; i6 < this.f1461j; i6++) {
                View view = (View) this.f1462k.get(i6);
                String str = (String) this.f1463l.get(i6);
                WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
                view.setTransitionName(str);
                ((View) this.f1464m.get(i6)).setTransitionName((String) this.f1465n.get(i6));
            }
        }
    }

    /* renamed from: d */
    public static void m688d(List<View> list, View view) {
        boolean z5;
        boolean z6;
        int size = list.size();
        int i6 = 0;
        while (true) {
            if (i6 >= size) {
                z5 = false;
                break;
            } else {
                if (list.get(i6) == view) {
                    z5 = true;
                    break;
                }
                i6++;
            }
        }
        if (z5) {
            return;
        }
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        if (view.getTransitionName() != null) {
            list.add(view);
        }
        for (int i7 = size; i7 < list.size(); i7++) {
            View view2 = list.get(i7);
            if (view2 instanceof ViewGroup) {
                ViewGroup viewGroup = (ViewGroup) view2;
                int childCount = viewGroup.getChildCount();
                for (int i8 = 0; i8 < childCount; i8++) {
                    View childAt = viewGroup.getChildAt(i8);
                    int i9 = 0;
                    while (true) {
                        if (i9 >= size) {
                            z6 = false;
                            break;
                        } else {
                            if (list.get(i9) == childAt) {
                                z6 = true;
                                break;
                            }
                            i9++;
                        }
                    }
                    if (!z6 && childAt.getTransitionName() != null) {
                        list.add(childAt);
                    }
                }
            }
        }
    }

    /* renamed from: h */
    public static boolean m689h(List list) {
        return list == null || list.isEmpty();
    }

    /* renamed from: a */
    public abstract void mo672a(Object obj, View view);

    /* renamed from: b */
    public abstract void mo673b(Object obj, ArrayList<View> arrayList);

    /* renamed from: c */
    public abstract void mo674c(ViewGroup viewGroup, Object obj);

    /* renamed from: e */
    public abstract boolean mo675e(Object obj);

    /* renamed from: f */
    public abstract Object mo676f(Object obj);

    /* renamed from: g */
    public final void m690g(View view, Rect rect) {
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        if (view.isAttachedToWindow()) {
            RectF rectF = new RectF();
            rectF.set(0.0f, 0.0f, view.getWidth(), view.getHeight());
            view.getMatrix().mapRect(rectF);
            rectF.offset(view.getLeft(), view.getTop());
            Object parent = view.getParent();
            while (parent instanceof View) {
                View view2 = (View) parent;
                rectF.offset(-view2.getScrollX(), -view2.getScrollY());
                view2.getMatrix().mapRect(rectF);
                rectF.offset(view2.getLeft(), view2.getTop());
                parent = view2.getParent();
            }
            view.getRootView().getLocationOnScreen(new int[2]);
            rectF.offset(r1[0], r1[1]);
            rect.set(Math.round(rectF.left), Math.round(rectF.top), Math.round(rectF.right), Math.round(rectF.bottom));
        }
    }

    /* renamed from: i */
    public abstract Object mo677i(Object obj, Object obj2, Object obj3);

    /* renamed from: j */
    public abstract Object mo678j(Object obj, Object obj2, Object obj3);

    /* renamed from: k */
    public final ArrayList<String> m691k(ArrayList<View> arrayList) {
        ArrayList<String> arrayList2 = new ArrayList<>();
        int size = arrayList.size();
        for (int i6 = 0; i6 < size; i6++) {
            View view = arrayList.get(i6);
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            arrayList2.add(view.getTransitionName());
            view.setTransitionName(null);
        }
        return arrayList2;
    }

    /* renamed from: l */
    public abstract void mo679l(Object obj, View view, ArrayList<View> arrayList);

    /* renamed from: m */
    public abstract void mo680m(Object obj, Object obj2, ArrayList<View> arrayList, Object obj3, ArrayList<View> arrayList2, Object obj4, ArrayList<View> arrayList3);

    /* renamed from: n */
    public abstract void mo681n(Object obj, Rect rect);

    /* renamed from: o */
    public abstract void mo682o(Object obj, View view);

    /* renamed from: p */
    public void mo683p(Object obj, Runnable runnable) {
        runnable.run();
    }

    /* renamed from: q */
    public final void m692q(View view, ArrayList<View> arrayList, ArrayList<View> arrayList2, ArrayList<String> arrayList3, Map<String, String> map) {
        int size = arrayList2.size();
        ArrayList arrayList4 = new ArrayList();
        for (int i6 = 0; i6 < size; i6++) {
            View view2 = arrayList.get(i6);
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            String transitionName = view2.getTransitionName();
            arrayList4.add(transitionName);
            if (transitionName != null) {
                view2.setTransitionName(null);
                String str = map.get(transitionName);
                int i7 = 0;
                while (true) {
                    if (i7 >= size) {
                        break;
                    }
                    if (str.equals(arrayList3.get(i7))) {
                        arrayList2.get(i7).setTransitionName(transitionName);
                        break;
                    }
                    i7++;
                }
            }
        }
        ViewTreeObserverOnPreDrawListenerC0765o.m2166a(view, new a(size, arrayList2, arrayList3, arrayList, arrayList4));
    }

    /* renamed from: r */
    public abstract void mo684r(Object obj, View view, ArrayList<View> arrayList);

    /* renamed from: s */
    public abstract void mo685s(Object obj, ArrayList<View> arrayList, ArrayList<View> arrayList2);

    /* renamed from: t */
    public abstract Object mo686t(Object obj);
}
