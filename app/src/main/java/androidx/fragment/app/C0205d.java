package androidx.fragment.app;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.view.View;
import android.view.ViewGroup;
import androidx.appcompat.widget.C0174y;
import androidx.fragment.app.AbstractC0228o0;
import androidx.fragment.app.C0203c;

/* renamed from: androidx.fragment.app.d */
/* loaded from: classes.dex */
public final class C0205d extends AnimatorListenerAdapter {

    /* renamed from: a */
    public final /* synthetic */ ViewGroup f1393a;

    /* renamed from: b */
    public final /* synthetic */ View f1394b;

    /* renamed from: c */
    public final /* synthetic */ boolean f1395c;

    /* renamed from: d */
    public final /* synthetic */ AbstractC0228o0.b f1396d;

    /* renamed from: e */
    public final /* synthetic */ C0203c.b f1397e;

    public C0205d(ViewGroup viewGroup, View view, boolean z5, AbstractC0228o0.b bVar, C0203c.b bVar2) {
        this.f1393a = viewGroup;
        this.f1394b = view;
        this.f1395c = z5;
        this.f1396d = bVar;
        this.f1397e = bVar2;
    }

    @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
    public final void onAnimationEnd(Animator animator) {
        this.f1393a.endViewTransition(this.f1394b);
        if (this.f1395c) {
            C0174y.m484b(this.f1396d.f1564a, this.f1394b);
        }
        this.f1397e.m634a();
    }
}
