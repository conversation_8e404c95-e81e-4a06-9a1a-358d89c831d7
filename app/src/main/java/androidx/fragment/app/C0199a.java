package androidx.fragment.app;

import android.util.Log;
import androidx.activity.result.C0052a;
import androidx.fragment.app.AbstractC0210f0;
import androidx.fragment.app.AbstractC0240x;
import androidx.fragment.app.ComponentCallbacksC0223m;
import androidx.lifecycle.AbstractC0251d;
import java.io.PrintWriter;
import java.lang.reflect.Modifier;
import java.util.ArrayList;

/* renamed from: androidx.fragment.app.a */
/* loaded from: classes.dex */
public final class C0199a extends AbstractC0210f0 implements AbstractC0240x.m {

    /* renamed from: p */
    public final AbstractC0240x f1345p;

    /* renamed from: q */
    public boolean f1346q;

    /* renamed from: r */
    public int f1347r;

    public C0199a(AbstractC0240x abstractC0240x) {
        abstractC0240x.m799H();
        AbstractC0237u<?> abstractC0237u = abstractC0240x.f1626p;
        if (abstractC0237u != null) {
            abstractC0237u.f1592k.getClassLoader();
        }
        this.f1347r = -1;
        this.f1345p = abstractC0240x;
    }

    @Override // androidx.fragment.app.AbstractC0240x.m
    /* renamed from: a */
    public final boolean mo615a(ArrayList<C0199a> arrayList, ArrayList<Boolean> arrayList2) {
        if (AbstractC0240x.m791K(2)) {
            Log.v("FragmentManager", "Run: " + this);
        }
        arrayList.add(this);
        arrayList2.add(Boolean.FALSE);
        if (!this.f1418g) {
            return true;
        }
        AbstractC0240x abstractC0240x = this.f1345p;
        if (abstractC0240x.f1614d == null) {
            abstractC0240x.f1614d = new ArrayList<>();
        }
        abstractC0240x.f1614d.add(this);
        return true;
    }

    /* renamed from: c */
    public final void m616c(int i6) {
        if (this.f1418g) {
            if (AbstractC0240x.m791K(2)) {
                Log.v("FragmentManager", "Bump nesting in " + this + " by " + i6);
            }
            int size = this.f1412a.size();
            for (int i7 = 0; i7 < size; i7++) {
                AbstractC0210f0.a aVar = this.f1412a.get(i7);
                ComponentCallbacksC0223m componentCallbacksC0223m = aVar.f1428b;
                if (componentCallbacksC0223m != null) {
                    componentCallbacksC0223m.f1535z += i6;
                    if (AbstractC0240x.m791K(2)) {
                        StringBuilder m104h = C0052a.m104h("Bump nesting of ");
                        m104h.append(aVar.f1428b);
                        m104h.append(" to ");
                        m104h.append(aVar.f1428b.f1535z);
                        Log.v("FragmentManager", m104h.toString());
                    }
                }
            }
        }
    }

    /* renamed from: d */
    public final int m617d(boolean z5) {
        if (this.f1346q) {
            throw new IllegalStateException("commit already called");
        }
        if (AbstractC0240x.m791K(2)) {
            Log.v("FragmentManager", "Commit: " + this);
            PrintWriter printWriter = new PrintWriter(new C0222l0());
            m620g("  ", printWriter, true);
            printWriter.close();
        }
        this.f1346q = true;
        this.f1347r = this.f1418g ? this.f1345p.f1619i.getAndIncrement() : -1;
        this.f1345p.m846x(this, z5);
        return this.f1347r;
    }

    /* renamed from: e */
    public final void m618e() {
        if (this.f1418g) {
            throw new IllegalStateException("This transaction is already being added to the back stack");
        }
        this.f1345p.m792A(this, false);
    }

    /* renamed from: f */
    public final void m619f(int i6, ComponentCallbacksC0223m componentCallbacksC0223m, String str, int i7) {
        Class<?> cls = componentCallbacksC0223m.getClass();
        int modifiers = cls.getModifiers();
        if (cls.isAnonymousClass() || !Modifier.isPublic(modifiers) || (cls.isMemberClass() && !Modifier.isStatic(modifiers))) {
            StringBuilder m104h = C0052a.m104h("Fragment ");
            m104h.append(cls.getCanonicalName());
            m104h.append(" must be a public static class to be  properly recreated from instance state.");
            throw new IllegalStateException(m104h.toString());
        }
        if (str != null) {
            String str2 = componentCallbacksC0223m.f1500G;
            if (str2 != null && !str.equals(str2)) {
                throw new IllegalStateException("Can't change tag of fragment " + componentCallbacksC0223m + ": was " + componentCallbacksC0223m.f1500G + " now " + str);
            }
            componentCallbacksC0223m.f1500G = str;
        }
        if (i6 != 0) {
            if (i6 == -1) {
                throw new IllegalArgumentException("Can't add fragment " + componentCallbacksC0223m + " with tag " + str + " to container view with no id");
            }
            int i8 = componentCallbacksC0223m.f1498E;
            if (i8 != 0 && i8 != i6) {
                throw new IllegalStateException("Can't change container ID of fragment " + componentCallbacksC0223m + ": was " + componentCallbacksC0223m.f1498E + " now " + i6);
            }
            componentCallbacksC0223m.f1498E = i6;
            componentCallbacksC0223m.f1499F = i6;
        }
        m667b(new AbstractC0210f0.a(i7, componentCallbacksC0223m));
        componentCallbacksC0223m.f1494A = this.f1345p;
    }

    /* renamed from: g */
    public final void m620g(String str, PrintWriter printWriter, boolean z5) {
        String str2;
        if (z5) {
            printWriter.print(str);
            printWriter.print("mName=");
            printWriter.print(this.f1419h);
            printWriter.print(" mIndex=");
            printWriter.print(this.f1347r);
            printWriter.print(" mCommitted=");
            printWriter.println(this.f1346q);
            if (this.f1417f != 0) {
                printWriter.print(str);
                printWriter.print("mTransition=#");
                printWriter.print(Integer.toHexString(this.f1417f));
            }
            if (this.f1413b != 0 || this.f1414c != 0) {
                printWriter.print(str);
                printWriter.print("mEnterAnim=#");
                printWriter.print(Integer.toHexString(this.f1413b));
                printWriter.print(" mExitAnim=#");
                printWriter.println(Integer.toHexString(this.f1414c));
            }
            if (this.f1415d != 0 || this.f1416e != 0) {
                printWriter.print(str);
                printWriter.print("mPopEnterAnim=#");
                printWriter.print(Integer.toHexString(this.f1415d));
                printWriter.print(" mPopExitAnim=#");
                printWriter.println(Integer.toHexString(this.f1416e));
            }
            if (this.f1420i != 0 || this.f1421j != null) {
                printWriter.print(str);
                printWriter.print("mBreadCrumbTitleRes=#");
                printWriter.print(Integer.toHexString(this.f1420i));
                printWriter.print(" mBreadCrumbTitleText=");
                printWriter.println(this.f1421j);
            }
            if (this.f1422k != 0 || this.f1423l != null) {
                printWriter.print(str);
                printWriter.print("mBreadCrumbShortTitleRes=#");
                printWriter.print(Integer.toHexString(this.f1422k));
                printWriter.print(" mBreadCrumbShortTitleText=");
                printWriter.println(this.f1423l);
            }
        }
        if (this.f1412a.isEmpty()) {
            return;
        }
        printWriter.print(str);
        printWriter.println("Operations:");
        int size = this.f1412a.size();
        for (int i6 = 0; i6 < size; i6++) {
            AbstractC0210f0.a aVar = this.f1412a.get(i6);
            switch (aVar.f1427a) {
                case 0:
                    str2 = "NULL";
                    break;
                case 1:
                    str2 = "ADD";
                    break;
                case 2:
                    str2 = "REPLACE";
                    break;
                case 3:
                    str2 = "REMOVE";
                    break;
                case 4:
                    str2 = "HIDE";
                    break;
                case 5:
                    str2 = "SHOW";
                    break;
                case 6:
                    str2 = "DETACH";
                    break;
                case 7:
                    str2 = "ATTACH";
                    break;
                case 8:
                    str2 = "SET_PRIMARY_NAV";
                    break;
                case 9:
                    str2 = "UNSET_PRIMARY_NAV";
                    break;
                case 10:
                    str2 = "OP_SET_MAX_LIFECYCLE";
                    break;
                default:
                    StringBuilder m104h = C0052a.m104h("cmd=");
                    m104h.append(aVar.f1427a);
                    str2 = m104h.toString();
                    break;
            }
            printWriter.print(str);
            printWriter.print("  Op #");
            printWriter.print(i6);
            printWriter.print(": ");
            printWriter.print(str2);
            printWriter.print(" ");
            printWriter.println(aVar.f1428b);
            if (z5) {
                if (aVar.f1429c != 0 || aVar.f1430d != 0) {
                    printWriter.print(str);
                    printWriter.print("enterAnim=#");
                    printWriter.print(Integer.toHexString(aVar.f1429c));
                    printWriter.print(" exitAnim=#");
                    printWriter.println(Integer.toHexString(aVar.f1430d));
                }
                if (aVar.f1431e != 0 || aVar.f1432f != 0) {
                    printWriter.print(str);
                    printWriter.print("popEnterAnim=#");
                    printWriter.print(Integer.toHexString(aVar.f1431e));
                    printWriter.print(" popExitAnim=#");
                    printWriter.println(Integer.toHexString(aVar.f1432f));
                }
            }
        }
    }

    /* renamed from: h */
    public final void m621h() {
        int size = this.f1412a.size();
        for (int i6 = 0; i6 < size; i6++) {
            AbstractC0210f0.a aVar = this.f1412a.get(i6);
            ComponentCallbacksC0223m componentCallbacksC0223m = aVar.f1428b;
            if (componentCallbacksC0223m != null) {
                componentCallbacksC0223m.m727a0(false);
                int i7 = this.f1417f;
                if (componentCallbacksC0223m.f1510Q != null || i7 != 0) {
                    componentCallbacksC0223m.m728e();
                    componentCallbacksC0223m.f1510Q.f1543g = i7;
                }
                ArrayList<String> arrayList = this.f1424m;
                ArrayList<String> arrayList2 = this.f1425n;
                componentCallbacksC0223m.m728e();
                ComponentCallbacksC0223m.b bVar = componentCallbacksC0223m.f1510Q;
                bVar.f1544h = arrayList;
                bVar.f1545i = arrayList2;
            }
            switch (aVar.f1427a) {
                case 1:
                    componentCallbacksC0223m.m724X(aVar.f1429c, aVar.f1430d, aVar.f1431e, aVar.f1432f);
                    this.f1345p.m815Y(componentCallbacksC0223m, false);
                    this.f1345p.m817a(componentCallbacksC0223m);
                    break;
                case 2:
                default:
                    StringBuilder m104h = C0052a.m104h("Unknown cmd: ");
                    m104h.append(aVar.f1427a);
                    throw new IllegalArgumentException(m104h.toString());
                case 3:
                    componentCallbacksC0223m.m724X(aVar.f1429c, aVar.f1430d, aVar.f1431e, aVar.f1432f);
                    this.f1345p.m810T(componentCallbacksC0223m);
                    break;
                case 4:
                    componentCallbacksC0223m.m724X(aVar.f1429c, aVar.f1430d, aVar.f1431e, aVar.f1432f);
                    this.f1345p.m801J(componentCallbacksC0223m);
                    break;
                case 5:
                    componentCallbacksC0223m.m724X(aVar.f1429c, aVar.f1430d, aVar.f1431e, aVar.f1432f);
                    this.f1345p.m815Y(componentCallbacksC0223m, false);
                    this.f1345p.m822c0(componentCallbacksC0223m);
                    break;
                case 6:
                    componentCallbacksC0223m.m724X(aVar.f1429c, aVar.f1430d, aVar.f1431e, aVar.f1432f);
                    this.f1345p.m829g(componentCallbacksC0223m);
                    break;
                case 7:
                    componentCallbacksC0223m.m724X(aVar.f1429c, aVar.f1430d, aVar.f1431e, aVar.f1432f);
                    this.f1345p.m815Y(componentCallbacksC0223m, false);
                    this.f1345p.m821c(componentCallbacksC0223m);
                    break;
                case 8:
                    this.f1345p.m818a0(componentCallbacksC0223m);
                    break;
                case 9:
                    this.f1345p.m818a0(null);
                    break;
                case 10:
                    this.f1345p.m816Z(componentCallbacksC0223m, aVar.f1434h);
                    break;
            }
            if (!this.f1426o) {
                int i8 = aVar.f1427a;
            }
        }
    }

    /* renamed from: i */
    public final void m622i() {
        AbstractC0240x abstractC0240x;
        for (int size = this.f1412a.size() - 1; size >= 0; size--) {
            AbstractC0210f0.a aVar = this.f1412a.get(size);
            ComponentCallbacksC0223m componentCallbacksC0223m = aVar.f1428b;
            if (componentCallbacksC0223m != null) {
                componentCallbacksC0223m.m727a0(true);
                int i6 = this.f1417f;
                int i7 = i6 != 4097 ? i6 != 4099 ? i6 != 8194 ? 0 : 4097 : 4099 : 8194;
                if (componentCallbacksC0223m.f1510Q != null || i7 != 0) {
                    componentCallbacksC0223m.m728e();
                    componentCallbacksC0223m.f1510Q.f1543g = i7;
                }
                ArrayList<String> arrayList = this.f1425n;
                ArrayList<String> arrayList2 = this.f1424m;
                componentCallbacksC0223m.m728e();
                ComponentCallbacksC0223m.b bVar = componentCallbacksC0223m.f1510Q;
                bVar.f1544h = arrayList;
                bVar.f1545i = arrayList2;
            }
            switch (aVar.f1427a) {
                case 1:
                    componentCallbacksC0223m.m724X(aVar.f1429c, aVar.f1430d, aVar.f1431e, aVar.f1432f);
                    this.f1345p.m815Y(componentCallbacksC0223m, true);
                    this.f1345p.m810T(componentCallbacksC0223m);
                    continue;
                case 2:
                default:
                    StringBuilder m104h = C0052a.m104h("Unknown cmd: ");
                    m104h.append(aVar.f1427a);
                    throw new IllegalArgumentException(m104h.toString());
                case 3:
                    componentCallbacksC0223m.m724X(aVar.f1429c, aVar.f1430d, aVar.f1431e, aVar.f1432f);
                    this.f1345p.m817a(componentCallbacksC0223m);
                    continue;
                case 4:
                    componentCallbacksC0223m.m724X(aVar.f1429c, aVar.f1430d, aVar.f1431e, aVar.f1432f);
                    this.f1345p.m822c0(componentCallbacksC0223m);
                    continue;
                case 5:
                    componentCallbacksC0223m.m724X(aVar.f1429c, aVar.f1430d, aVar.f1431e, aVar.f1432f);
                    this.f1345p.m815Y(componentCallbacksC0223m, true);
                    this.f1345p.m801J(componentCallbacksC0223m);
                    continue;
                case 6:
                    componentCallbacksC0223m.m724X(aVar.f1429c, aVar.f1430d, aVar.f1431e, aVar.f1432f);
                    this.f1345p.m821c(componentCallbacksC0223m);
                    continue;
                case 7:
                    componentCallbacksC0223m.m724X(aVar.f1429c, aVar.f1430d, aVar.f1431e, aVar.f1432f);
                    this.f1345p.m815Y(componentCallbacksC0223m, true);
                    this.f1345p.m829g(componentCallbacksC0223m);
                    continue;
                case 8:
                    abstractC0240x = this.f1345p;
                    componentCallbacksC0223m = null;
                    break;
                case 9:
                    abstractC0240x = this.f1345p;
                    break;
                case 10:
                    this.f1345p.m816Z(componentCallbacksC0223m, aVar.f1433g);
                    continue;
            }
            abstractC0240x.m818a0(componentCallbacksC0223m);
        }
    }

    /* renamed from: j */
    public final AbstractC0210f0 m623j(ComponentCallbacksC0223m componentCallbacksC0223m) {
        AbstractC0240x abstractC0240x = componentCallbacksC0223m.f1494A;
        if (abstractC0240x == null || abstractC0240x == this.f1345p) {
            m667b(new AbstractC0210f0.a(3, componentCallbacksC0223m));
            return this;
        }
        StringBuilder m104h = C0052a.m104h("Cannot remove Fragment attached to a different FragmentManager. Fragment ");
        m104h.append(componentCallbacksC0223m.toString());
        m104h.append(" is already attached to a FragmentManager.");
        throw new IllegalStateException(m104h.toString());
    }

    /* renamed from: k */
    public final AbstractC0210f0 m624k(ComponentCallbacksC0223m componentCallbacksC0223m, AbstractC0251d.c cVar) {
        if (componentCallbacksC0223m.f1494A != this.f1345p) {
            StringBuilder m104h = C0052a.m104h("Cannot setMaxLifecycle for Fragment not attached to FragmentManager ");
            m104h.append(this.f1345p);
            throw new IllegalArgumentException(m104h.toString());
        }
        if (cVar == AbstractC0251d.c.INITIALIZED && componentCallbacksC0223m.f1519j > -1) {
            throw new IllegalArgumentException("Cannot set maximum Lifecycle to " + cVar + " after the Fragment has been created");
        }
        if (cVar != AbstractC0251d.c.DESTROYED) {
            m667b(new AbstractC0210f0.a(componentCallbacksC0223m, cVar));
            return this;
        }
        throw new IllegalArgumentException("Cannot set maximum Lifecycle to " + cVar + ". Use remove() to remove the fragment from the FragmentManager and trigger its destruction.");
    }

    public final String toString() {
        StringBuilder sb = new StringBuilder(128);
        sb.append("BackStackEntry{");
        sb.append(Integer.toHexString(System.identityHashCode(this)));
        if (this.f1347r >= 0) {
            sb.append(" #");
            sb.append(this.f1347r);
        }
        if (this.f1419h != null) {
            sb.append(" ");
            sb.append(this.f1419h);
        }
        sb.append("}");
        return sb.toString();
    }
}
