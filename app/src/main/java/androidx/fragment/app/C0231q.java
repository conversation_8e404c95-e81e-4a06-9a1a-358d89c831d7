package androidx.fragment.app;

import android.animation.Animator;
import android.animation.AnimatorInflater;
import android.content.Context;
import android.content.res.Resources;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationSet;
import android.view.animation.AnimationUtils;
import android.view.animation.Transformation;
import androidx.fragment.app.ComponentCallbacksC0223m;
import com.liaoyuan.aicast.R;
import p029e0.ViewTreeObserverOnPreDrawListenerC0765o;

/* renamed from: androidx.fragment.app.q */
/* loaded from: classes.dex */
public final class C0231q {

    /* renamed from: androidx.fragment.app.q$a */
    public static class a {

        /* renamed from: a */
        public final Animation f1578a;

        /* renamed from: b */
        public final Animator f1579b;

        public a(Animator animator) {
            this.f1578a = null;
            this.f1579b = animator;
        }

        public a(Animation animation) {
            this.f1578a = animation;
            this.f1579b = null;
        }
    }

    /* renamed from: androidx.fragment.app.q$b */
    public static class b extends AnimationSet implements Runnable {

        /* renamed from: j */
        public final ViewGroup f1580j;

        /* renamed from: k */
        public final View f1581k;

        /* renamed from: l */
        public boolean f1582l;

        /* renamed from: m */
        public boolean f1583m;

        /* renamed from: n */
        public boolean f1584n;

        public b(Animation animation, ViewGroup viewGroup, View view) {
            super(false);
            this.f1584n = true;
            this.f1580j = viewGroup;
            this.f1581k = view;
            addAnimation(animation);
            viewGroup.post(this);
        }

        @Override // android.view.animation.AnimationSet, android.view.animation.Animation
        public final boolean getTransformation(long j6, Transformation transformation) {
            this.f1584n = true;
            if (this.f1582l) {
                return !this.f1583m;
            }
            if (!super.getTransformation(j6, transformation)) {
                this.f1582l = true;
                ViewTreeObserverOnPreDrawListenerC0765o.m2166a(this.f1580j, this);
            }
            return true;
        }

        @Override // android.view.animation.Animation
        public final boolean getTransformation(long j6, Transformation transformation, float f6) {
            this.f1584n = true;
            if (this.f1582l) {
                return !this.f1583m;
            }
            if (!super.getTransformation(j6, transformation, f6)) {
                this.f1582l = true;
                ViewTreeObserverOnPreDrawListenerC0765o.m2166a(this.f1580j, this);
            }
            return true;
        }

        @Override // java.lang.Runnable
        public final void run() {
            if (this.f1582l || !this.f1584n) {
                this.f1580j.endViewTransition(this.f1581k);
                this.f1583m = true;
            } else {
                this.f1584n = false;
                this.f1580j.post(this);
            }
        }
    }

    /* renamed from: a */
    public static a m771a(Context context, ComponentCallbacksC0223m componentCallbacksC0223m, boolean z5, boolean z6) {
        ComponentCallbacksC0223m.b bVar = componentCallbacksC0223m.f1510Q;
        boolean z7 = false;
        int i6 = bVar == null ? 0 : bVar.f1543g;
        int m738p = z6 ? z5 ? componentCallbacksC0223m.m738p() : componentCallbacksC0223m.m739q() : z5 ? componentCallbacksC0223m.m733k() : componentCallbacksC0223m.m734l();
        componentCallbacksC0223m.m724X(0, 0, 0, 0);
        ViewGroup viewGroup = componentCallbacksC0223m.f1506M;
        if (viewGroup != null && viewGroup.getTag(R.id.visible_removing_fragment_view_tag) != null) {
            componentCallbacksC0223m.f1506M.setTag(R.id.visible_removing_fragment_view_tag, null);
        }
        ViewGroup viewGroup2 = componentCallbacksC0223m.f1506M;
        if (viewGroup2 != null && viewGroup2.getLayoutTransition() != null) {
            return null;
        }
        if (m738p == 0 && i6 != 0) {
            m738p = i6 != 4097 ? i6 != 4099 ? i6 != 8194 ? -1 : z5 ? R.animator.fragment_close_enter : R.animator.fragment_close_exit : z5 ? R.animator.fragment_fade_enter : R.animator.fragment_fade_exit : z5 ? R.animator.fragment_open_enter : R.animator.fragment_open_exit;
        }
        if (m738p != 0) {
            boolean equals = "anim".equals(context.getResources().getResourceTypeName(m738p));
            if (equals) {
                try {
                    Animation loadAnimation = AnimationUtils.loadAnimation(context, m738p);
                    if (loadAnimation != null) {
                        return new a(loadAnimation);
                    }
                    z7 = true;
                } catch (Resources.NotFoundException e6) {
                    throw e6;
                } catch (RuntimeException unused) {
                }
            }
            if (!z7) {
                try {
                    Animator loadAnimator = AnimatorInflater.loadAnimator(context, m738p);
                    if (loadAnimator != null) {
                        return new a(loadAnimator);
                    }
                } catch (RuntimeException e7) {
                    if (equals) {
                        throw e7;
                    }
                    Animation loadAnimation2 = AnimationUtils.loadAnimation(context, m738p);
                    if (loadAnimation2 != null) {
                        return new a(loadAnimation2);
                    }
                }
            }
        }
        return null;
    }
}
