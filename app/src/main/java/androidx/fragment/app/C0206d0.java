package androidx.fragment.app;

import android.content.res.Resources;
import android.os.Bundle;
import android.os.Parcelable;
import android.util.Log;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.activity.result.C0052a;
import androidx.appcompat.widget.C0174y;
import androidx.fragment.app.AbstractC0228o0;
import androidx.fragment.app.ComponentCallbacksC0223m;
import androidx.lifecycle.AbstractC0251d;
import androidx.lifecycle.C0255h;
import androidx.lifecycle.InterfaceC0252e;
import androidx.lifecycle.InterfaceC0254g;
import androidx.savedstate.C0339b;
import com.liaoyuan.aicast.R;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Objects;
import java.util.UUID;
import java.util.WeakHashMap;
import p029e0.C0766p;
import p029e0.C0769s;
import p090n.C1094g;

/* renamed from: androidx.fragment.app.d0 */
/* loaded from: classes.dex */
public final class C0206d0 {

    /* renamed from: a */
    public final C0239w f1398a;

    /* renamed from: b */
    public final C0208e0 f1399b;

    /* renamed from: c */
    public final ComponentCallbacksC0223m f1400c;

    /* renamed from: d */
    public boolean f1401d = false;

    /* renamed from: e */
    public int f1402e = -1;

    /* renamed from: androidx.fragment.app.d0$a */
    public class a implements View.OnAttachStateChangeListener {

        /* renamed from: j */
        public final /* synthetic */ View f1403j;

        public a(View view) {
            this.f1403j = view;
        }

        @Override // android.view.View.OnAttachStateChangeListener
        public final void onViewAttachedToWindow(View view) {
            this.f1403j.removeOnAttachStateChangeListener(this);
            View view2 = this.f1403j;
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            view2.requestApplyInsets();
        }

        @Override // android.view.View.OnAttachStateChangeListener
        public final void onViewDetachedFromWindow(View view) {
        }
    }

    public C0206d0(C0239w c0239w, C0208e0 c0208e0, ComponentCallbacksC0223m componentCallbacksC0223m) {
        this.f1398a = c0239w;
        this.f1399b = c0208e0;
        this.f1400c = componentCallbacksC0223m;
    }

    public C0206d0(C0239w c0239w, C0208e0 c0208e0, ComponentCallbacksC0223m componentCallbacksC0223m, C0204c0 c0204c0) {
        this.f1398a = c0239w;
        this.f1399b = c0208e0;
        this.f1400c = componentCallbacksC0223m;
        componentCallbacksC0223m.f1521l = null;
        componentCallbacksC0223m.f1522m = null;
        componentCallbacksC0223m.f1535z = 0;
        componentCallbacksC0223m.f1532w = false;
        componentCallbacksC0223m.f1529t = false;
        ComponentCallbacksC0223m componentCallbacksC0223m2 = componentCallbacksC0223m.f1525p;
        componentCallbacksC0223m.f1526q = componentCallbacksC0223m2 != null ? componentCallbacksC0223m2.f1523n : null;
        componentCallbacksC0223m.f1525p = null;
        Bundle bundle = c0204c0.f1392v;
        componentCallbacksC0223m.f1520k = bundle == null ? new Bundle() : bundle;
    }

    public C0206d0(C0239w c0239w, C0208e0 c0208e0, ClassLoader classLoader, C0236t c0236t, C0204c0 c0204c0) {
        this.f1398a = c0239w;
        this.f1399b = c0208e0;
        ComponentCallbacksC0223m mo776a = c0236t.mo776a(classLoader, c0204c0.f1380j);
        this.f1400c = mo776a;
        Bundle bundle = c0204c0.f1389s;
        if (bundle != null) {
            bundle.setClassLoader(classLoader);
        }
        mo776a.m725Y(c0204c0.f1389s);
        mo776a.f1523n = c0204c0.f1381k;
        mo776a.f1531v = c0204c0.f1382l;
        mo776a.f1533x = true;
        mo776a.f1498E = c0204c0.f1383m;
        mo776a.f1499F = c0204c0.f1384n;
        mo776a.f1500G = c0204c0.f1385o;
        mo776a.f1503J = c0204c0.f1386p;
        mo776a.f1530u = c0204c0.f1387q;
        mo776a.f1502I = c0204c0.f1388r;
        mo776a.f1501H = c0204c0.f1390t;
        mo776a.f1513T = AbstractC0251d.c.values()[c0204c0.f1391u];
        Bundle bundle2 = c0204c0.f1392v;
        mo776a.f1520k = bundle2 == null ? new Bundle() : bundle2;
        if (AbstractC0240x.m791K(2)) {
            Log.v("FragmentManager", "Instantiated fragment " + mo776a);
        }
    }

    /* renamed from: a */
    public final void m637a() {
        if (AbstractC0240x.m791K(3)) {
            StringBuilder m104h = C0052a.m104h("moveto ACTIVITY_CREATED: ");
            m104h.append(this.f1400c);
            Log.d("FragmentManager", m104h.toString());
        }
        ComponentCallbacksC0223m componentCallbacksC0223m = this.f1400c;
        Bundle bundle = componentCallbacksC0223m.f1520k;
        componentCallbacksC0223m.f1496C.m807Q();
        componentCallbacksC0223m.f1519j = 3;
        componentCallbacksC0223m.f1505L = true;
        if (AbstractC0240x.m791K(3)) {
            Log.d("FragmentManager", "moveto RESTORE_VIEW_STATE: " + componentCallbacksC0223m);
        }
        View view = componentCallbacksC0223m.f1507N;
        if (view != null) {
            Bundle bundle2 = componentCallbacksC0223m.f1520k;
            SparseArray<Parcelable> sparseArray = componentCallbacksC0223m.f1521l;
            if (sparseArray != null) {
                view.restoreHierarchyState(sparseArray);
                componentCallbacksC0223m.f1521l = null;
            }
            if (componentCallbacksC0223m.f1507N != null) {
                componentCallbacksC0223m.f1515V.f1469l.m1364a(componentCallbacksC0223m.f1522m);
                componentCallbacksC0223m.f1522m = null;
            }
            componentCallbacksC0223m.f1505L = false;
            componentCallbacksC0223m.mo702N(bundle2);
            if (!componentCallbacksC0223m.f1505L) {
                throw new C0234r0("Fragment " + componentCallbacksC0223m + " did not call through to super.onViewStateRestored()");
            }
            if (componentCallbacksC0223m.f1507N != null) {
                componentCallbacksC0223m.f1515V.m693d(AbstractC0251d.b.ON_CREATE);
            }
        }
        componentCallbacksC0223m.f1520k = null;
        C0241y c0241y = componentCallbacksC0223m.f1496C;
        c0241y.f1602A = false;
        c0241y.f1603B = false;
        c0241y.f1609H.f1354g = false;
        c0241y.m842t(4);
        C0239w c0239w = this.f1398a;
        ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1400c;
        c0239w.m777a(componentCallbacksC0223m2, componentCallbacksC0223m2.f1520k, false);
    }

    /* renamed from: b */
    public final void m638b() {
        View view;
        View view2;
        C0208e0 c0208e0 = this.f1399b;
        ComponentCallbacksC0223m componentCallbacksC0223m = this.f1400c;
        Objects.requireNonNull(c0208e0);
        ViewGroup viewGroup = componentCallbacksC0223m.f1506M;
        int i6 = -1;
        if (viewGroup != null) {
            int indexOf = ((ArrayList) c0208e0.f1405j).indexOf(componentCallbacksC0223m);
            int i7 = indexOf - 1;
            while (true) {
                if (i7 < 0) {
                    while (true) {
                        indexOf++;
                        if (indexOf >= ((ArrayList) c0208e0.f1405j).size()) {
                            break;
                        }
                        ComponentCallbacksC0223m componentCallbacksC0223m2 = (ComponentCallbacksC0223m) ((ArrayList) c0208e0.f1405j).get(indexOf);
                        if (componentCallbacksC0223m2.f1506M == viewGroup && (view = componentCallbacksC0223m2.f1507N) != null) {
                            i6 = viewGroup.indexOfChild(view);
                            break;
                        }
                    }
                } else {
                    ComponentCallbacksC0223m componentCallbacksC0223m3 = (ComponentCallbacksC0223m) ((ArrayList) c0208e0.f1405j).get(i7);
                    if (componentCallbacksC0223m3.f1506M == viewGroup && (view2 = componentCallbacksC0223m3.f1507N) != null) {
                        i6 = viewGroup.indexOfChild(view2) + 1;
                        break;
                    }
                    i7--;
                }
            }
        }
        ComponentCallbacksC0223m componentCallbacksC0223m4 = this.f1400c;
        componentCallbacksC0223m4.f1506M.addView(componentCallbacksC0223m4.f1507N, i6);
    }

    /* renamed from: c */
    public final void m639c() {
        if (AbstractC0240x.m791K(3)) {
            StringBuilder m104h = C0052a.m104h("moveto ATTACHED: ");
            m104h.append(this.f1400c);
            Log.d("FragmentManager", m104h.toString());
        }
        ComponentCallbacksC0223m componentCallbacksC0223m = this.f1400c;
        ComponentCallbacksC0223m componentCallbacksC0223m2 = componentCallbacksC0223m.f1525p;
        C0206d0 c0206d0 = null;
        if (componentCallbacksC0223m2 != null) {
            C0206d0 m663i = this.f1399b.m663i(componentCallbacksC0223m2.f1523n);
            if (m663i == null) {
                StringBuilder m104h2 = C0052a.m104h("Fragment ");
                m104h2.append(this.f1400c);
                m104h2.append(" declared target fragment ");
                m104h2.append(this.f1400c.f1525p);
                m104h2.append(" that does not belong to this FragmentManager!");
                throw new IllegalStateException(m104h2.toString());
            }
            ComponentCallbacksC0223m componentCallbacksC0223m3 = this.f1400c;
            componentCallbacksC0223m3.f1526q = componentCallbacksC0223m3.f1525p.f1523n;
            componentCallbacksC0223m3.f1525p = null;
            c0206d0 = m663i;
        } else {
            String str = componentCallbacksC0223m.f1526q;
            if (str != null && (c0206d0 = this.f1399b.m663i(str)) == null) {
                StringBuilder m104h3 = C0052a.m104h("Fragment ");
                m104h3.append(this.f1400c);
                m104h3.append(" declared target fragment ");
                throw new IllegalStateException(C1094g.m2839c(m104h3, this.f1400c.f1526q, " that does not belong to this FragmentManager!"));
            }
        }
        if (c0206d0 != null) {
            c0206d0.m647k();
        }
        ComponentCallbacksC0223m componentCallbacksC0223m4 = this.f1400c;
        AbstractC0240x abstractC0240x = componentCallbacksC0223m4.f1494A;
        componentCallbacksC0223m4.f1495B = abstractC0240x.f1626p;
        componentCallbacksC0223m4.f1497D = abstractC0240x.f1628r;
        this.f1398a.m783g(componentCallbacksC0223m4, false);
        ComponentCallbacksC0223m componentCallbacksC0223m5 = this.f1400c;
        Iterator<ComponentCallbacksC0223m.d> it = componentCallbacksC0223m5.f1518Y.iterator();
        while (it.hasNext()) {
            it.next().m748a();
        }
        componentCallbacksC0223m5.f1518Y.clear();
        componentCallbacksC0223m5.f1496C.m819b(componentCallbacksC0223m5.f1495B, componentCallbacksC0223m5.mo706d(), componentCallbacksC0223m5);
        componentCallbacksC0223m5.f1519j = 0;
        componentCallbacksC0223m5.f1505L = false;
        componentCallbacksC0223m5.mo708z(componentCallbacksC0223m5.f1495B.f1592k);
        if (!componentCallbacksC0223m5.f1505L) {
            throw new C0234r0("Fragment " + componentCallbacksC0223m5 + " did not call through to super.onAttach()");
        }
        Iterator<InterfaceC0202b0> it2 = componentCallbacksC0223m5.f1494A.f1624n.iterator();
        while (it2.hasNext()) {
            it2.next().mo628d();
        }
        C0241y c0241y = componentCallbacksC0223m5.f1496C;
        c0241y.f1602A = false;
        c0241y.f1603B = false;
        c0241y.f1609H.f1354g = false;
        c0241y.m842t(0);
        this.f1398a.m778b(this.f1400c, false);
    }

    /* renamed from: d */
    public final int m640d() {
        int i6;
        ComponentCallbacksC0223m componentCallbacksC0223m = this.f1400c;
        if (componentCallbacksC0223m.f1494A == null) {
            return componentCallbacksC0223m.f1519j;
        }
        int i7 = this.f1402e;
        int ordinal = componentCallbacksC0223m.f1513T.ordinal();
        int i8 = 0;
        if (ordinal == 1) {
            i7 = Math.min(i7, 0);
        } else if (ordinal == 2) {
            i7 = Math.min(i7, 1);
        } else if (ordinal == 3) {
            i7 = Math.min(i7, 5);
        } else if (ordinal != 4) {
            i7 = Math.min(i7, -1);
        }
        ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1400c;
        if (componentCallbacksC0223m2.f1531v) {
            if (componentCallbacksC0223m2.f1532w) {
                i7 = Math.max(this.f1402e, 2);
                View view = this.f1400c.f1507N;
                if (view != null && view.getParent() == null) {
                    i7 = Math.min(i7, 2);
                }
            } else {
                i7 = this.f1402e < 4 ? Math.min(i7, componentCallbacksC0223m2.f1519j) : Math.min(i7, 1);
            }
        }
        if (!this.f1400c.f1529t) {
            i7 = Math.min(i7, 1);
        }
        ComponentCallbacksC0223m componentCallbacksC0223m3 = this.f1400c;
        ViewGroup viewGroup = componentCallbacksC0223m3.f1506M;
        AbstractC0228o0.b bVar = null;
        if (viewGroup != null) {
            AbstractC0228o0 m751g = AbstractC0228o0.m751g(viewGroup, componentCallbacksC0223m3.m736n().m800I());
            Objects.requireNonNull(m751g);
            AbstractC0228o0.b m754d = m751g.m754d(this.f1400c);
            if (m754d != null) {
                i6 = m754d.f1565b;
            } else {
                ComponentCallbacksC0223m componentCallbacksC0223m4 = this.f1400c;
                Iterator<AbstractC0228o0.b> it = m751g.f1560c.iterator();
                while (true) {
                    if (!it.hasNext()) {
                        break;
                    }
                    AbstractC0228o0.b next = it.next();
                    if (next.f1566c.equals(componentCallbacksC0223m4) && !next.f1569f) {
                        bVar = next;
                        break;
                    }
                }
                if (bVar != null) {
                    i6 = bVar.f1565b;
                }
            }
            i8 = i6;
        }
        if (i8 == 2) {
            i7 = Math.min(i7, 6);
        } else if (i8 == 3) {
            i7 = Math.max(i7, 3);
        } else {
            ComponentCallbacksC0223m componentCallbacksC0223m5 = this.f1400c;
            if (componentCallbacksC0223m5.f1530u) {
                i7 = componentCallbacksC0223m5.m746x() ? Math.min(i7, 1) : Math.min(i7, -1);
            }
        }
        ComponentCallbacksC0223m componentCallbacksC0223m6 = this.f1400c;
        if (componentCallbacksC0223m6.f1508O && componentCallbacksC0223m6.f1519j < 5) {
            i7 = Math.min(i7, 4);
        }
        if (AbstractC0240x.m791K(2)) {
            Log.v("FragmentManager", "computeExpectedState() of " + i7 + " for " + this.f1400c);
        }
        return i7;
    }

    /* renamed from: e */
    public final void m641e() {
        if (AbstractC0240x.m791K(3)) {
            StringBuilder m104h = C0052a.m104h("moveto CREATED: ");
            m104h.append(this.f1400c);
            Log.d("FragmentManager", m104h.toString());
        }
        ComponentCallbacksC0223m componentCallbacksC0223m = this.f1400c;
        if (componentCallbacksC0223m.f1512S) {
            componentCallbacksC0223m.m723W(componentCallbacksC0223m.f1520k);
            this.f1400c.f1519j = 1;
            return;
        }
        this.f1398a.m784h(componentCallbacksC0223m, componentCallbacksC0223m.f1520k, false);
        ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1400c;
        Bundle bundle = componentCallbacksC0223m2.f1520k;
        componentCallbacksC0223m2.f1496C.m807Q();
        componentCallbacksC0223m2.f1519j = 1;
        componentCallbacksC0223m2.f1505L = false;
        componentCallbacksC0223m2.f1514U.mo871a(new InterfaceC0252e() { // from class: androidx.fragment.app.Fragment$5
            public Fragment$5() {
            }

            @Override // androidx.lifecycle.InterfaceC0252e
            /* renamed from: e */
            public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
                View view;
                if (bVar != AbstractC0251d.b.ON_STOP || (view = ComponentCallbacksC0223m.this.f1507N) == null) {
                    return;
                }
                view.cancelPendingInputEvents();
            }
        });
        componentCallbacksC0223m2.f1517X.m1364a(bundle);
        componentCallbacksC0223m2.mo695A(bundle);
        componentCallbacksC0223m2.f1512S = true;
        if (componentCallbacksC0223m2.f1505L) {
            componentCallbacksC0223m2.f1514U.m879e(AbstractC0251d.b.ON_CREATE);
            C0239w c0239w = this.f1398a;
            ComponentCallbacksC0223m componentCallbacksC0223m3 = this.f1400c;
            c0239w.m779c(componentCallbacksC0223m3, componentCallbacksC0223m3.f1520k, false);
            return;
        }
        throw new C0234r0("Fragment " + componentCallbacksC0223m2 + " did not call through to super.onCreate()");
    }

    /* renamed from: f */
    public final void m642f() {
        String str;
        if (this.f1400c.f1531v) {
            return;
        }
        if (AbstractC0240x.m791K(3)) {
            StringBuilder m104h = C0052a.m104h("moveto CREATE_VIEW: ");
            m104h.append(this.f1400c);
            Log.d("FragmentManager", m104h.toString());
        }
        ComponentCallbacksC0223m componentCallbacksC0223m = this.f1400c;
        LayoutInflater mo698F = componentCallbacksC0223m.mo698F(componentCallbacksC0223m.f1520k);
        ViewGroup viewGroup = null;
        ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1400c;
        ViewGroup viewGroup2 = componentCallbacksC0223m2.f1506M;
        if (viewGroup2 != null) {
            viewGroup = viewGroup2;
        } else {
            int i6 = componentCallbacksC0223m2.f1499F;
            if (i6 != 0) {
                if (i6 == -1) {
                    StringBuilder m104h2 = C0052a.m104h("Cannot create fragment ");
                    m104h2.append(this.f1400c);
                    m104h2.append(" for a container view with no id");
                    throw new IllegalArgumentException(m104h2.toString());
                }
                viewGroup = (ViewGroup) componentCallbacksC0223m2.f1494A.f1627q.mo118k(i6);
                if (viewGroup == null) {
                    ComponentCallbacksC0223m componentCallbacksC0223m3 = this.f1400c;
                    if (!componentCallbacksC0223m3.f1533x) {
                        try {
                            str = componentCallbacksC0223m3.m741s().getResourceName(this.f1400c.f1499F);
                        } catch (Resources.NotFoundException unused) {
                            str = "unknown";
                        }
                        StringBuilder m104h3 = C0052a.m104h("No view found for id 0x");
                        m104h3.append(Integer.toHexString(this.f1400c.f1499F));
                        m104h3.append(" (");
                        m104h3.append(str);
                        m104h3.append(") for fragment ");
                        m104h3.append(this.f1400c);
                        throw new IllegalArgumentException(m104h3.toString());
                    }
                }
            }
        }
        ComponentCallbacksC0223m componentCallbacksC0223m4 = this.f1400c;
        componentCallbacksC0223m4.f1506M = viewGroup;
        componentCallbacksC0223m4.mo703O(mo698F, viewGroup, componentCallbacksC0223m4.f1520k);
        View view = this.f1400c.f1507N;
        if (view != null) {
            view.setSaveFromParentEnabled(false);
            ComponentCallbacksC0223m componentCallbacksC0223m5 = this.f1400c;
            componentCallbacksC0223m5.f1507N.setTag(R.id.fragment_container_view_tag, componentCallbacksC0223m5);
            if (viewGroup != null) {
                m638b();
            }
            ComponentCallbacksC0223m componentCallbacksC0223m6 = this.f1400c;
            if (componentCallbacksC0223m6.f1501H) {
                componentCallbacksC0223m6.f1507N.setVisibility(8);
            }
            View view2 = this.f1400c.f1507N;
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            if (view2.isAttachedToWindow()) {
                this.f1400c.f1507N.requestApplyInsets();
            } else {
                View view3 = this.f1400c.f1507N;
                view3.addOnAttachStateChangeListener(new a(view3));
            }
            ComponentCallbacksC0223m componentCallbacksC0223m7 = this.f1400c;
            componentCallbacksC0223m7.mo715M(componentCallbacksC0223m7.f1507N);
            componentCallbacksC0223m7.f1496C.m842t(2);
            C0239w c0239w = this.f1398a;
            ComponentCallbacksC0223m componentCallbacksC0223m8 = this.f1400c;
            c0239w.m789m(componentCallbacksC0223m8, componentCallbacksC0223m8.f1507N, componentCallbacksC0223m8.f1520k, false);
            int visibility = this.f1400c.f1507N.getVisibility();
            this.f1400c.m728e().f1549m = this.f1400c.f1507N.getAlpha();
            ComponentCallbacksC0223m componentCallbacksC0223m9 = this.f1400c;
            if (componentCallbacksC0223m9.f1506M != null && visibility == 0) {
                View findFocus = componentCallbacksC0223m9.f1507N.findFocus();
                if (findFocus != null) {
                    this.f1400c.m726Z(findFocus);
                    if (AbstractC0240x.m791K(2)) {
                        Log.v("FragmentManager", "requestFocus: Saved focused view " + findFocus + " for Fragment " + this.f1400c);
                    }
                }
                this.f1400c.f1507N.setAlpha(0.0f);
            }
        }
        this.f1400c.f1519j = 2;
    }

    /* JADX WARN: Removed duplicated region for block: B:18:0x0051  */
    /* JADX WARN: Removed duplicated region for block: B:60:0x0146  */
    /* renamed from: g */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m643g() {
        /*
            Method dump skipped, instructions count: 353
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.fragment.app.C0206d0.m643g():void");
    }

    /* renamed from: h */
    public final void m644h() {
        View view;
        if (AbstractC0240x.m791K(3)) {
            StringBuilder m104h = C0052a.m104h("movefrom CREATE_VIEW: ");
            m104h.append(this.f1400c);
            Log.d("FragmentManager", m104h.toString());
        }
        ComponentCallbacksC0223m componentCallbacksC0223m = this.f1400c;
        ViewGroup viewGroup = componentCallbacksC0223m.f1506M;
        if (viewGroup != null && (view = componentCallbacksC0223m.f1507N) != null) {
            viewGroup.removeView(view);
        }
        this.f1400c.m716P();
        this.f1398a.m790n(this.f1400c, false);
        ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1400c;
        componentCallbacksC0223m2.f1506M = null;
        componentCallbacksC0223m2.f1507N = null;
        componentCallbacksC0223m2.f1515V = null;
        componentCallbacksC0223m2.f1516W.mo890h(null);
        this.f1400c.f1532w = false;
    }

    /* renamed from: i */
    public final void m645i() {
        if (AbstractC0240x.m791K(3)) {
            StringBuilder m104h = C0052a.m104h("movefrom ATTACHED: ");
            m104h.append(this.f1400c);
            Log.d("FragmentManager", m104h.toString());
        }
        ComponentCallbacksC0223m componentCallbacksC0223m = this.f1400c;
        componentCallbacksC0223m.f1519j = -1;
        componentCallbacksC0223m.f1505L = false;
        componentCallbacksC0223m.mo697E();
        if (!componentCallbacksC0223m.f1505L) {
            throw new C0234r0("Fragment " + componentCallbacksC0223m + " did not call through to super.onDetach()");
        }
        C0241y c0241y = componentCallbacksC0223m.f1496C;
        if (!c0241y.f1604C) {
            c0241y.m834l();
            componentCallbacksC0223m.f1496C = new C0241y();
        }
        this.f1398a.m781e(this.f1400c, false);
        ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1400c;
        componentCallbacksC0223m2.f1519j = -1;
        componentCallbacksC0223m2.f1495B = null;
        componentCallbacksC0223m2.f1497D = null;
        componentCallbacksC0223m2.f1494A = null;
        boolean z5 = true;
        if (!(componentCallbacksC0223m2.f1530u && !componentCallbacksC0223m2.m746x())) {
            C0200a0 c0200a0 = (C0200a0) this.f1399b.f1407l;
            if (c0200a0.f1349b.containsKey(this.f1400c.f1523n) && c0200a0.f1352e) {
                z5 = c0200a0.f1353f;
            }
            if (!z5) {
                return;
            }
        }
        if (AbstractC0240x.m791K(3)) {
            StringBuilder m104h2 = C0052a.m104h("initState called for fragment: ");
            m104h2.append(this.f1400c);
            Log.d("FragmentManager", m104h2.toString());
        }
        ComponentCallbacksC0223m componentCallbacksC0223m3 = this.f1400c;
        Objects.requireNonNull(componentCallbacksC0223m3);
        componentCallbacksC0223m3.f1514U = new C0255h(componentCallbacksC0223m3);
        componentCallbacksC0223m3.f1517X = new C0339b(componentCallbacksC0223m3);
        componentCallbacksC0223m3.f1523n = UUID.randomUUID().toString();
        componentCallbacksC0223m3.f1529t = false;
        componentCallbacksC0223m3.f1530u = false;
        componentCallbacksC0223m3.f1531v = false;
        componentCallbacksC0223m3.f1532w = false;
        componentCallbacksC0223m3.f1533x = false;
        componentCallbacksC0223m3.f1535z = 0;
        componentCallbacksC0223m3.f1494A = null;
        componentCallbacksC0223m3.f1496C = new C0241y();
        componentCallbacksC0223m3.f1495B = null;
        componentCallbacksC0223m3.f1498E = 0;
        componentCallbacksC0223m3.f1499F = 0;
        componentCallbacksC0223m3.f1500G = null;
        componentCallbacksC0223m3.f1501H = false;
        componentCallbacksC0223m3.f1502I = false;
    }

    /* renamed from: j */
    public final void m646j() {
        ComponentCallbacksC0223m componentCallbacksC0223m = this.f1400c;
        if (componentCallbacksC0223m.f1531v && componentCallbacksC0223m.f1532w && !componentCallbacksC0223m.f1534y) {
            if (AbstractC0240x.m791K(3)) {
                StringBuilder m104h = C0052a.m104h("moveto CREATE_VIEW: ");
                m104h.append(this.f1400c);
                Log.d("FragmentManager", m104h.toString());
            }
            ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1400c;
            componentCallbacksC0223m2.mo703O(componentCallbacksC0223m2.mo698F(componentCallbacksC0223m2.f1520k), null, this.f1400c.f1520k);
            View view = this.f1400c.f1507N;
            if (view != null) {
                view.setSaveFromParentEnabled(false);
                ComponentCallbacksC0223m componentCallbacksC0223m3 = this.f1400c;
                componentCallbacksC0223m3.f1507N.setTag(R.id.fragment_container_view_tag, componentCallbacksC0223m3);
                ComponentCallbacksC0223m componentCallbacksC0223m4 = this.f1400c;
                if (componentCallbacksC0223m4.f1501H) {
                    componentCallbacksC0223m4.f1507N.setVisibility(8);
                }
                ComponentCallbacksC0223m componentCallbacksC0223m5 = this.f1400c;
                componentCallbacksC0223m5.mo715M(componentCallbacksC0223m5.f1507N);
                componentCallbacksC0223m5.f1496C.m842t(2);
                C0239w c0239w = this.f1398a;
                ComponentCallbacksC0223m componentCallbacksC0223m6 = this.f1400c;
                c0239w.m789m(componentCallbacksC0223m6, componentCallbacksC0223m6.f1507N, componentCallbacksC0223m6.f1520k, false);
                this.f1400c.f1519j = 2;
            }
        }
    }

    /* renamed from: k */
    public final void m647k() {
        ViewGroup viewGroup;
        ViewGroup viewGroup2;
        ViewGroup viewGroup3;
        if (this.f1401d) {
            if (AbstractC0240x.m791K(2)) {
                StringBuilder m104h = C0052a.m104h("Ignoring re-entrant call to moveToExpectedState() for ");
                m104h.append(this.f1400c);
                Log.v("FragmentManager", m104h.toString());
                return;
            }
            return;
        }
        try {
            this.f1401d = true;
            while (true) {
                int m640d = m640d();
                ComponentCallbacksC0223m componentCallbacksC0223m = this.f1400c;
                int i6 = componentCallbacksC0223m.f1519j;
                if (m640d == i6) {
                    if (componentCallbacksC0223m.f1511R) {
                        if (componentCallbacksC0223m.f1507N != null && (viewGroup = componentCallbacksC0223m.f1506M) != null) {
                            AbstractC0228o0 m751g = AbstractC0228o0.m751g(viewGroup, componentCallbacksC0223m.m736n().m800I());
                            if (this.f1400c.f1501H) {
                                Objects.requireNonNull(m751g);
                                if (AbstractC0240x.m791K(2)) {
                                    Log.v("FragmentManager", "SpecialEffectsController: Enqueuing hide operation for fragment " + this.f1400c);
                                }
                                m751g.m752a(3, 1, this);
                            } else {
                                Objects.requireNonNull(m751g);
                                if (AbstractC0240x.m791K(2)) {
                                    Log.v("FragmentManager", "SpecialEffectsController: Enqueuing show operation for fragment " + this.f1400c);
                                }
                                m751g.m752a(2, 1, this);
                            }
                        }
                        ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1400c;
                        AbstractC0240x abstractC0240x = componentCallbacksC0223m2.f1494A;
                        if (abstractC0240x != null && componentCallbacksC0223m2.f1529t && abstractC0240x.m802L(componentCallbacksC0223m2)) {
                            abstractC0240x.f1636z = true;
                        }
                        ComponentCallbacksC0223m componentCallbacksC0223m3 = this.f1400c;
                        componentCallbacksC0223m3.f1511R = false;
                        componentCallbacksC0223m3.mo712G(componentCallbacksC0223m3.f1501H);
                    }
                    return;
                }
                if (m640d <= i6) {
                    switch (i6 - 1) {
                        case -1:
                            m645i();
                            break;
                        case 0:
                            m643g();
                            break;
                        case 1:
                            m644h();
                            this.f1400c.f1519j = 1;
                            break;
                        case 2:
                            componentCallbacksC0223m.f1532w = false;
                            componentCallbacksC0223m.f1519j = 2;
                            break;
                        case 3:
                            if (AbstractC0240x.m791K(3)) {
                                Log.d("FragmentManager", "movefrom ACTIVITY_CREATED: " + this.f1400c);
                            }
                            ComponentCallbacksC0223m componentCallbacksC0223m4 = this.f1400c;
                            if (componentCallbacksC0223m4.f1507N != null && componentCallbacksC0223m4.f1521l == null) {
                                m652p();
                            }
                            ComponentCallbacksC0223m componentCallbacksC0223m5 = this.f1400c;
                            if (componentCallbacksC0223m5.f1507N != null && (viewGroup3 = componentCallbacksC0223m5.f1506M) != null) {
                                AbstractC0228o0 m751g2 = AbstractC0228o0.m751g(viewGroup3, componentCallbacksC0223m5.m736n().m800I());
                                Objects.requireNonNull(m751g2);
                                if (AbstractC0240x.m791K(2)) {
                                    Log.v("FragmentManager", "SpecialEffectsController: Enqueuing remove operation for fragment " + this.f1400c);
                                }
                                m751g2.m752a(1, 3, this);
                            }
                            this.f1400c.f1519j = 3;
                            break;
                        case 4:
                            m654r();
                            break;
                        case 5:
                            componentCallbacksC0223m.f1519j = 5;
                            break;
                        case 6:
                            m648l();
                            break;
                    }
                } else {
                    switch (i6 + 1) {
                        case 0:
                            m639c();
                            break;
                        case 1:
                            m641e();
                            break;
                        case 2:
                            m646j();
                            m642f();
                            break;
                        case 3:
                            m637a();
                            break;
                        case 4:
                            if (componentCallbacksC0223m.f1507N != null && (viewGroup2 = componentCallbacksC0223m.f1506M) != null) {
                                AbstractC0228o0 m751g3 = AbstractC0228o0.m751g(viewGroup2, componentCallbacksC0223m.m736n().m800I());
                                int m485c = C0174y.m485c(this.f1400c.f1507N.getVisibility());
                                Objects.requireNonNull(m751g3);
                                if (AbstractC0240x.m791K(2)) {
                                    Log.v("FragmentManager", "SpecialEffectsController: Enqueuing add operation for fragment " + this.f1400c);
                                }
                                m751g3.m752a(m485c, 2, this);
                            }
                            this.f1400c.f1519j = 4;
                            break;
                        case 5:
                            m653q();
                            break;
                        case 6:
                            componentCallbacksC0223m.f1519j = 6;
                            break;
                        case 7:
                            m650n();
                            break;
                    }
                }
            }
        } finally {
            this.f1401d = false;
        }
    }

    /* renamed from: l */
    public final void m648l() {
        if (AbstractC0240x.m791K(3)) {
            StringBuilder m104h = C0052a.m104h("movefrom RESUMED: ");
            m104h.append(this.f1400c);
            Log.d("FragmentManager", m104h.toString());
        }
        ComponentCallbacksC0223m componentCallbacksC0223m = this.f1400c;
        componentCallbacksC0223m.f1496C.m842t(5);
        if (componentCallbacksC0223m.f1507N != null) {
            componentCallbacksC0223m.f1515V.m693d(AbstractC0251d.b.ON_PAUSE);
        }
        componentCallbacksC0223m.f1514U.m879e(AbstractC0251d.b.ON_PAUSE);
        componentCallbacksC0223m.f1519j = 6;
        componentCallbacksC0223m.f1505L = true;
        this.f1398a.m782f(this.f1400c, false);
    }

    /* renamed from: m */
    public final void m649m(ClassLoader classLoader) {
        Bundle bundle = this.f1400c.f1520k;
        if (bundle == null) {
            return;
        }
        bundle.setClassLoader(classLoader);
        ComponentCallbacksC0223m componentCallbacksC0223m = this.f1400c;
        componentCallbacksC0223m.f1521l = componentCallbacksC0223m.f1520k.getSparseParcelableArray("android:view_state");
        ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1400c;
        componentCallbacksC0223m2.f1522m = componentCallbacksC0223m2.f1520k.getBundle("android:view_registry_state");
        ComponentCallbacksC0223m componentCallbacksC0223m3 = this.f1400c;
        componentCallbacksC0223m3.f1526q = componentCallbacksC0223m3.f1520k.getString("android:target_state");
        ComponentCallbacksC0223m componentCallbacksC0223m4 = this.f1400c;
        if (componentCallbacksC0223m4.f1526q != null) {
            componentCallbacksC0223m4.f1527r = componentCallbacksC0223m4.f1520k.getInt("android:target_req_state", 0);
        }
        ComponentCallbacksC0223m componentCallbacksC0223m5 = this.f1400c;
        Objects.requireNonNull(componentCallbacksC0223m5);
        componentCallbacksC0223m5.f1509P = componentCallbacksC0223m5.f1520k.getBoolean("android:user_visible_hint", true);
        ComponentCallbacksC0223m componentCallbacksC0223m6 = this.f1400c;
        if (componentCallbacksC0223m6.f1509P) {
            return;
        }
        componentCallbacksC0223m6.f1508O = true;
    }

    /* JADX WARN: Removed duplicated region for block: B:20:0x0045  */
    /* renamed from: n */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m650n() {
        /*
            Method dump skipped, instructions count: 243
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.fragment.app.C0206d0.m650n():void");
    }

    /* renamed from: o */
    public final Bundle m651o() {
        Bundle bundle = new Bundle();
        ComponentCallbacksC0223m componentCallbacksC0223m = this.f1400c;
        componentCallbacksC0223m.mo699J(bundle);
        componentCallbacksC0223m.f1517X.m1365b(bundle);
        Parcelable m813W = componentCallbacksC0223m.f1496C.m813W();
        if (m813W != null) {
            bundle.putParcelable("android:support:fragments", m813W);
        }
        this.f1398a.m786j(this.f1400c, bundle, false);
        if (bundle.isEmpty()) {
            bundle = null;
        }
        if (this.f1400c.f1507N != null) {
            m652p();
        }
        if (this.f1400c.f1521l != null) {
            if (bundle == null) {
                bundle = new Bundle();
            }
            bundle.putSparseParcelableArray("android:view_state", this.f1400c.f1521l);
        }
        if (this.f1400c.f1522m != null) {
            if (bundle == null) {
                bundle = new Bundle();
            }
            bundle.putBundle("android:view_registry_state", this.f1400c.f1522m);
        }
        if (!this.f1400c.f1509P) {
            if (bundle == null) {
                bundle = new Bundle();
            }
            bundle.putBoolean("android:user_visible_hint", this.f1400c.f1509P);
        }
        return bundle;
    }

    /* renamed from: p */
    public final void m652p() {
        if (this.f1400c.f1507N == null) {
            return;
        }
        SparseArray<Parcelable> sparseArray = new SparseArray<>();
        this.f1400c.f1507N.saveHierarchyState(sparseArray);
        if (sparseArray.size() > 0) {
            this.f1400c.f1521l = sparseArray;
        }
        Bundle bundle = new Bundle();
        this.f1400c.f1515V.f1469l.m1365b(bundle);
        if (bundle.isEmpty()) {
            return;
        }
        this.f1400c.f1522m = bundle;
    }

    /* renamed from: q */
    public final void m653q() {
        if (AbstractC0240x.m791K(3)) {
            StringBuilder m104h = C0052a.m104h("moveto STARTED: ");
            m104h.append(this.f1400c);
            Log.d("FragmentManager", m104h.toString());
        }
        ComponentCallbacksC0223m componentCallbacksC0223m = this.f1400c;
        componentCallbacksC0223m.f1496C.m807Q();
        componentCallbacksC0223m.f1496C.m848z(true);
        componentCallbacksC0223m.f1519j = 5;
        componentCallbacksC0223m.f1505L = false;
        componentCallbacksC0223m.mo700K();
        if (!componentCallbacksC0223m.f1505L) {
            throw new C0234r0("Fragment " + componentCallbacksC0223m + " did not call through to super.onStart()");
        }
        C0255h c0255h = componentCallbacksC0223m.f1514U;
        AbstractC0251d.b bVar = AbstractC0251d.b.ON_START;
        c0255h.m879e(bVar);
        if (componentCallbacksC0223m.f1507N != null) {
            componentCallbacksC0223m.f1515V.m693d(bVar);
        }
        C0241y c0241y = componentCallbacksC0223m.f1496C;
        c0241y.f1602A = false;
        c0241y.f1603B = false;
        c0241y.f1609H.f1354g = false;
        c0241y.m842t(5);
        this.f1398a.m787k(this.f1400c, false);
    }

    /* renamed from: r */
    public final void m654r() {
        if (AbstractC0240x.m791K(3)) {
            StringBuilder m104h = C0052a.m104h("movefrom STARTED: ");
            m104h.append(this.f1400c);
            Log.d("FragmentManager", m104h.toString());
        }
        ComponentCallbacksC0223m componentCallbacksC0223m = this.f1400c;
        C0241y c0241y = componentCallbacksC0223m.f1496C;
        c0241y.f1603B = true;
        c0241y.f1609H.f1354g = true;
        c0241y.m842t(4);
        if (componentCallbacksC0223m.f1507N != null) {
            componentCallbacksC0223m.f1515V.m693d(AbstractC0251d.b.ON_STOP);
        }
        componentCallbacksC0223m.f1514U.m879e(AbstractC0251d.b.ON_STOP);
        componentCallbacksC0223m.f1519j = 4;
        componentCallbacksC0223m.f1505L = false;
        componentCallbacksC0223m.mo701L();
        if (componentCallbacksC0223m.f1505L) {
            this.f1398a.m788l(this.f1400c, false);
            return;
        }
        throw new C0234r0("Fragment " + componentCallbacksC0223m + " did not call through to super.onStop()");
    }
}
