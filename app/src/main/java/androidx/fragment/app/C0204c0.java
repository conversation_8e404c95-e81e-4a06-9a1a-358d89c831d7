package androidx.fragment.app;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

@SuppressLint({"BanParcelableUsage"})
/* renamed from: androidx.fragment.app.c0 */
/* loaded from: classes.dex */
public final class C0204c0 implements Parcelable {
    public static final Parcelable.Creator<C0204c0> CREATOR = new a();

    /* renamed from: j */
    public final String f1380j;

    /* renamed from: k */
    public final String f1381k;

    /* renamed from: l */
    public final boolean f1382l;

    /* renamed from: m */
    public final int f1383m;

    /* renamed from: n */
    public final int f1384n;

    /* renamed from: o */
    public final String f1385o;

    /* renamed from: p */
    public final boolean f1386p;

    /* renamed from: q */
    public final boolean f1387q;

    /* renamed from: r */
    public final boolean f1388r;

    /* renamed from: s */
    public final Bundle f1389s;

    /* renamed from: t */
    public final boolean f1390t;

    /* renamed from: u */
    public final int f1391u;

    /* renamed from: v */
    public Bundle f1392v;

    /* renamed from: androidx.fragment.app.c0$a */
    public class a implements Parcelable.Creator<C0204c0> {
        @Override // android.os.Parcelable.Creator
        public final C0204c0 createFromParcel(Parcel parcel) {
            return new C0204c0(parcel);
        }

        @Override // android.os.Parcelable.Creator
        public final C0204c0[] newArray(int i6) {
            return new C0204c0[i6];
        }
    }

    public C0204c0(Parcel parcel) {
        this.f1380j = parcel.readString();
        this.f1381k = parcel.readString();
        this.f1382l = parcel.readInt() != 0;
        this.f1383m = parcel.readInt();
        this.f1384n = parcel.readInt();
        this.f1385o = parcel.readString();
        this.f1386p = parcel.readInt() != 0;
        this.f1387q = parcel.readInt() != 0;
        this.f1388r = parcel.readInt() != 0;
        this.f1389s = parcel.readBundle();
        this.f1390t = parcel.readInt() != 0;
        this.f1392v = parcel.readBundle();
        this.f1391u = parcel.readInt();
    }

    public C0204c0(ComponentCallbacksC0223m componentCallbacksC0223m) {
        this.f1380j = componentCallbacksC0223m.getClass().getName();
        this.f1381k = componentCallbacksC0223m.f1523n;
        this.f1382l = componentCallbacksC0223m.f1531v;
        this.f1383m = componentCallbacksC0223m.f1498E;
        this.f1384n = componentCallbacksC0223m.f1499F;
        this.f1385o = componentCallbacksC0223m.f1500G;
        this.f1386p = componentCallbacksC0223m.f1503J;
        this.f1387q = componentCallbacksC0223m.f1530u;
        this.f1388r = componentCallbacksC0223m.f1502I;
        this.f1389s = componentCallbacksC0223m.f1524o;
        this.f1390t = componentCallbacksC0223m.f1501H;
        this.f1391u = componentCallbacksC0223m.f1513T.ordinal();
    }

    @Override // android.os.Parcelable
    public final int describeContents() {
        return 0;
    }

    public final String toString() {
        StringBuilder sb = new StringBuilder(128);
        sb.append("FragmentState{");
        sb.append(this.f1380j);
        sb.append(" (");
        sb.append(this.f1381k);
        sb.append(")}:");
        if (this.f1382l) {
            sb.append(" fromLayout");
        }
        if (this.f1384n != 0) {
            sb.append(" id=0x");
            sb.append(Integer.toHexString(this.f1384n));
        }
        String str = this.f1385o;
        if (str != null && !str.isEmpty()) {
            sb.append(" tag=");
            sb.append(this.f1385o);
        }
        if (this.f1386p) {
            sb.append(" retainInstance");
        }
        if (this.f1387q) {
            sb.append(" removing");
        }
        if (this.f1388r) {
            sb.append(" detached");
        }
        if (this.f1390t) {
            sb.append(" hidden");
        }
        return sb.toString();
    }

    @Override // android.os.Parcelable
    public final void writeToParcel(Parcel parcel, int i6) {
        parcel.writeString(this.f1380j);
        parcel.writeString(this.f1381k);
        parcel.writeInt(this.f1382l ? 1 : 0);
        parcel.writeInt(this.f1383m);
        parcel.writeInt(this.f1384n);
        parcel.writeString(this.f1385o);
        parcel.writeInt(this.f1386p ? 1 : 0);
        parcel.writeInt(this.f1387q ? 1 : 0);
        parcel.writeInt(this.f1388r ? 1 : 0);
        parcel.writeBundle(this.f1389s);
        parcel.writeInt(this.f1390t ? 1 : 0);
        parcel.writeBundle(this.f1392v);
        parcel.writeInt(this.f1391u);
    }
}
