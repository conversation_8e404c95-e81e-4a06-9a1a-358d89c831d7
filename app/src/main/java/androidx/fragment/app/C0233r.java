package androidx.fragment.app;

import android.animation.LayoutTransition;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowInsets;
import android.widget.FrameLayout;
import androidx.activity.result.C0052a;
import androidx.appcompat.widget.C0174y;
import com.liaoyuan.aicast.R;
import java.util.ArrayList;
import java.util.Iterator;
import p008b0.C0385m;
import p029e0.C0766p;
import p029e0.C0772v;

/* renamed from: androidx.fragment.app.r */
/* loaded from: classes.dex */
public final class C0233r extends FrameLayout {

    /* renamed from: j */
    public ArrayList<View> f1585j;

    /* renamed from: k */
    public ArrayList<View> f1586k;

    /* renamed from: l */
    public View.OnApplyWindowInsetsListener f1587l;

    /* renamed from: m */
    public boolean f1588m;

    public C0233r(Context context, AttributeSet attributeSet, AbstractC0240x abstractC0240x) {
        super(context, attributeSet);
        View view;
        this.f1588m = true;
        String classAttribute = attributeSet.getClassAttribute();
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C0385m.f2322M);
        classAttribute = classAttribute == null ? obtainStyledAttributes.getString(0) : classAttribute;
        String string = obtainStyledAttributes.getString(1);
        obtainStyledAttributes.recycle();
        int id = getId();
        ComponentCallbacksC0223m m796E = abstractC0240x.m796E(id);
        if (classAttribute != null && m796E == null) {
            if (id <= 0) {
                throw new IllegalStateException(C0174y.m491i("FragmentContainerView must have an android:id to add Fragment ", classAttribute, string != null ? C0052a.m103g(" with tag ", string) : ""));
            }
            ComponentCallbacksC0223m mo776a = abstractC0240x.m799H().mo776a(context.getClassLoader(), classAttribute);
            mo776a.m713H();
            C0199a c0199a = new C0199a(abstractC0240x);
            c0199a.f1426o = true;
            mo776a.f1506M = this;
            c0199a.m619f(getId(), mo776a, string, 1);
            if (c0199a.f1418g) {
                throw new IllegalStateException("This transaction is already being added to the back stack");
            }
            c0199a.f1345p.m792A(c0199a, true);
        }
        Iterator it = ((ArrayList) abstractC0240x.f1613c.m660f()).iterator();
        while (it.hasNext()) {
            C0206d0 c0206d0 = (C0206d0) it.next();
            ComponentCallbacksC0223m componentCallbacksC0223m = c0206d0.f1400c;
            if (componentCallbacksC0223m.f1499F == getId() && (view = componentCallbacksC0223m.f1507N) != null && view.getParent() == null) {
                componentCallbacksC0223m.f1506M = this;
                c0206d0.m638b();
            }
        }
    }

    /* renamed from: a */
    public final void m772a(View view) {
        ArrayList<View> arrayList = this.f1586k;
        if (arrayList == null || !arrayList.contains(view)) {
            return;
        }
        if (this.f1585j == null) {
            this.f1585j = new ArrayList<>();
        }
        this.f1585j.add(view);
    }

    @Override // android.view.ViewGroup
    public final void addView(View view, int i6, ViewGroup.LayoutParams layoutParams) {
        Object tag = view.getTag(R.id.fragment_container_view_tag);
        if ((tag instanceof ComponentCallbacksC0223m ? (ComponentCallbacksC0223m) tag : null) != null) {
            super.addView(view, i6, layoutParams);
            return;
        }
        throw new IllegalStateException("Views added to a FragmentContainerView must be associated with a Fragment. View " + view + " is not associated with a Fragment.");
    }

    @Override // android.view.ViewGroup
    public final boolean addViewInLayout(View view, int i6, ViewGroup.LayoutParams layoutParams, boolean z5) {
        Object tag = view.getTag(R.id.fragment_container_view_tag);
        if ((tag instanceof ComponentCallbacksC0223m ? (ComponentCallbacksC0223m) tag : null) != null) {
            return super.addViewInLayout(view, i6, layoutParams, z5);
        }
        throw new IllegalStateException("Views added to a FragmentContainerView must be associated with a Fragment. View " + view + " is not associated with a Fragment.");
    }

    @Override // android.view.ViewGroup, android.view.View
    public final WindowInsets dispatchApplyWindowInsets(WindowInsets windowInsets) {
        C0772v m2215j = C0772v.m2215j(windowInsets, null);
        View.OnApplyWindowInsetsListener onApplyWindowInsetsListener = this.f1587l;
        C0772v m2215j2 = onApplyWindowInsetsListener != null ? C0772v.m2215j(onApplyWindowInsetsListener.onApplyWindowInsets(this, windowInsets), null) : C0766p.m2181n(this, m2215j);
        if (!m2215j2.m2221g()) {
            int childCount = getChildCount();
            for (int i6 = 0; i6 < childCount; i6++) {
                C0766p.m2170c(getChildAt(i6), m2215j2);
            }
        }
        return windowInsets;
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void dispatchDraw(Canvas canvas) {
        if (this.f1588m && this.f1585j != null) {
            for (int i6 = 0; i6 < this.f1585j.size(); i6++) {
                super.drawChild(canvas, this.f1585j.get(i6), getDrawingTime());
            }
        }
        super.dispatchDraw(canvas);
    }

    @Override // android.view.ViewGroup
    public final boolean drawChild(Canvas canvas, View view, long j6) {
        ArrayList<View> arrayList;
        if (!this.f1588m || (arrayList = this.f1585j) == null || arrayList.size() <= 0 || !this.f1585j.contains(view)) {
            return super.drawChild(canvas, view, j6);
        }
        return false;
    }

    @Override // android.view.ViewGroup
    public final void endViewTransition(View view) {
        ArrayList<View> arrayList = this.f1586k;
        if (arrayList != null) {
            arrayList.remove(view);
            ArrayList<View> arrayList2 = this.f1585j;
            if (arrayList2 != null && arrayList2.remove(view)) {
                this.f1588m = true;
            }
        }
        super.endViewTransition(view);
    }

    @Override // android.view.View
    public final WindowInsets onApplyWindowInsets(WindowInsets windowInsets) {
        return windowInsets;
    }

    @Override // android.view.ViewGroup
    public final void removeAllViewsInLayout() {
        for (int childCount = getChildCount() - 1; childCount >= 0; childCount--) {
            m772a(getChildAt(childCount));
        }
        super.removeAllViewsInLayout();
    }

    @Override // android.view.ViewGroup
    public final void removeDetachedView(View view, boolean z5) {
        if (z5) {
            m772a(view);
        }
        super.removeDetachedView(view, z5);
    }

    @Override // android.view.ViewGroup, android.view.ViewManager
    public final void removeView(View view) {
        m772a(view);
        super.removeView(view);
    }

    @Override // android.view.ViewGroup
    public final void removeViewAt(int i6) {
        m772a(getChildAt(i6));
        super.removeViewAt(i6);
    }

    @Override // android.view.ViewGroup
    public final void removeViewInLayout(View view) {
        m772a(view);
        super.removeViewInLayout(view);
    }

    @Override // android.view.ViewGroup
    public final void removeViews(int i6, int i7) {
        for (int i8 = i6; i8 < i6 + i7; i8++) {
            m772a(getChildAt(i8));
        }
        super.removeViews(i6, i7);
    }

    @Override // android.view.ViewGroup
    public final void removeViewsInLayout(int i6, int i7) {
        for (int i8 = i6; i8 < i6 + i7; i8++) {
            m772a(getChildAt(i8));
        }
        super.removeViewsInLayout(i6, i7);
    }

    public void setDrawDisappearingViewsLast(boolean z5) {
        this.f1588m = z5;
    }

    @Override // android.view.ViewGroup
    public void setLayoutTransition(LayoutTransition layoutTransition) {
        throw new UnsupportedOperationException("FragmentContainerView does not support Layout Transitions or animateLayoutChanges=\"true\".");
    }

    @Override // android.view.View
    public void setOnApplyWindowInsetsListener(View.OnApplyWindowInsetsListener onApplyWindowInsetsListener) {
        this.f1587l = onApplyWindowInsetsListener;
    }

    @Override // android.view.ViewGroup
    public final void startViewTransition(View view) {
        if (view.getParent() == this) {
            if (this.f1586k == null) {
                this.f1586k = new ArrayList<>();
            }
            this.f1586k.add(view);
        }
        super.startViewTransition(view);
    }
}
