package androidx.fragment.app;

import androidx.lifecycle.AbstractC0251d;
import androidx.lifecycle.InterfaceC0252e;
import androidx.lifecycle.InterfaceC0254g;

/* loaded from: classes.dex */
class FragmentManager$6 implements InterfaceC0252e {
    @Override // androidx.lifecycle.InterfaceC0252e
    /* renamed from: e */
    public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
        if (bVar == AbstractC0251d.b.ON_START) {
            throw null;
        }
        if (bVar == AbstractC0251d.b.ON_DESTROY) {
            throw null;
        }
    }
}
