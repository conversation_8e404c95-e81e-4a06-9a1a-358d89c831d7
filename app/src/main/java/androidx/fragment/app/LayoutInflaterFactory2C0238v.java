package androidx.fragment.app;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

/* renamed from: androidx.fragment.app.v */
/* loaded from: classes.dex */
public final class LayoutInflaterFactory2C0238v implements LayoutInflater.Factory2 {

    /* renamed from: j */
    public final AbstractC0240x f1595j;

    /* renamed from: androidx.fragment.app.v$a */
    public class a implements View.OnAttachStateChangeListener {

        /* renamed from: j */
        public final /* synthetic */ C0206d0 f1596j;

        public a(C0206d0 c0206d0) {
            this.f1596j = c0206d0;
        }

        @Override // android.view.View.OnAttachStateChangeListener
        public final void onViewAttachedToWindow(View view) {
            C0206d0 c0206d0 = this.f1596j;
            ComponentCallbacksC0223m componentCallbacksC0223m = c0206d0.f1400c;
            c0206d0.m647k();
            AbstractC0228o0.m750f((ViewGroup) componentCallbacksC0223m.f1507N.getParent(), LayoutInflaterFactory2C0238v.this.f1595j).m755e();
        }

        @Override // android.view.View.OnAttachStateChangeListener
        public final void onViewDetachedFromWindow(View view) {
        }
    }

    public LayoutInflaterFactory2C0238v(AbstractC0240x abstractC0240x) {
        this.f1595j = abstractC0240x;
    }

    /* JADX WARN: Removed duplicated region for block: B:44:0x0135  */
    /* JADX WARN: Removed duplicated region for block: B:51:0x0154  */
    @Override // android.view.LayoutInflater.Factory2
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final android.view.View onCreateView(android.view.View r10, java.lang.String r11, android.content.Context r12, android.util.AttributeSet r13) {
        /*
            Method dump skipped, instructions count: 415
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.fragment.app.LayoutInflaterFactory2C0238v.onCreateView(android.view.View, java.lang.String, android.content.Context, android.util.AttributeSet):android.view.View");
    }

    @Override // android.view.LayoutInflater.Factory
    public final View onCreateView(String str, Context context, AttributeSet attributeSet) {
        return onCreateView(null, str, context, attributeSet);
    }
}
