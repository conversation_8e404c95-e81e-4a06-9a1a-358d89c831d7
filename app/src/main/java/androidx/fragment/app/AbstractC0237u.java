package androidx.fragment.app;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.view.LayoutInflater;
import androidx.activity.result.AbstractC0055d;
import java.io.PrintWriter;
import p008b0.C0385m;

/* renamed from: androidx.fragment.app.u */
/* loaded from: classes.dex */
public abstract class AbstractC0237u<E> extends AbstractC0055d {

    /* renamed from: j */
    public final Activity f1591j;

    /* renamed from: k */
    public final Context f1592k;

    /* renamed from: l */
    public final Handler f1593l;

    /* renamed from: m */
    public final C0241y f1594m;

    public AbstractC0237u(ActivityC0229p activityC0229p) {
        Handler handler = new Handler();
        this.f1594m = new C0241y();
        this.f1591j = activityC0229p;
        C0385m.m1415f(activityC0229p, "context == null");
        this.f1592k = activityC0229p;
        this.f1593l = handler;
    }

    /* renamed from: r */
    public abstract void mo767r(PrintWriter printWriter, String[] strArr);

    /* renamed from: s */
    public abstract E mo768s();

    /* renamed from: t */
    public abstract LayoutInflater mo769t();

    /* renamed from: u */
    public abstract void mo770u();
}
