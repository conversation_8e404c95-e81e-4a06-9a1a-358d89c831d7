package androidx.fragment.app;

import android.annotation.SuppressLint;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;
import androidx.fragment.app.AbstractC0210f0;
import java.util.ArrayList;

@SuppressLint({"BanParcelableUsage"})
/* renamed from: androidx.fragment.app.b */
/* loaded from: classes.dex */
public final class C0201b implements Parcelable {
    public static final Parcelable.Creator<C0201b> CREATOR = new a();

    /* renamed from: j */
    public final int[] f1355j;

    /* renamed from: k */
    public final ArrayList<String> f1356k;

    /* renamed from: l */
    public final int[] f1357l;

    /* renamed from: m */
    public final int[] f1358m;

    /* renamed from: n */
    public final int f1359n;

    /* renamed from: o */
    public final String f1360o;

    /* renamed from: p */
    public final int f1361p;

    /* renamed from: q */
    public final int f1362q;

    /* renamed from: r */
    public final CharSequence f1363r;

    /* renamed from: s */
    public final int f1364s;

    /* renamed from: t */
    public final CharSequence f1365t;

    /* renamed from: u */
    public final ArrayList<String> f1366u;

    /* renamed from: v */
    public final ArrayList<String> f1367v;

    /* renamed from: w */
    public final boolean f1368w;

    /* renamed from: androidx.fragment.app.b$a */
    public class a implements Parcelable.Creator<C0201b> {
        @Override // android.os.Parcelable.Creator
        public final C0201b createFromParcel(Parcel parcel) {
            return new C0201b(parcel);
        }

        @Override // android.os.Parcelable.Creator
        public final C0201b[] newArray(int i6) {
            return new C0201b[i6];
        }
    }

    public C0201b(Parcel parcel) {
        this.f1355j = parcel.createIntArray();
        this.f1356k = parcel.createStringArrayList();
        this.f1357l = parcel.createIntArray();
        this.f1358m = parcel.createIntArray();
        this.f1359n = parcel.readInt();
        this.f1360o = parcel.readString();
        this.f1361p = parcel.readInt();
        this.f1362q = parcel.readInt();
        this.f1363r = (CharSequence) TextUtils.CHAR_SEQUENCE_CREATOR.createFromParcel(parcel);
        this.f1364s = parcel.readInt();
        this.f1365t = (CharSequence) TextUtils.CHAR_SEQUENCE_CREATOR.createFromParcel(parcel);
        this.f1366u = parcel.createStringArrayList();
        this.f1367v = parcel.createStringArrayList();
        this.f1368w = parcel.readInt() != 0;
    }

    public C0201b(C0199a c0199a) {
        int size = c0199a.f1412a.size();
        this.f1355j = new int[size * 5];
        if (!c0199a.f1418g) {
            throw new IllegalStateException("Not on back stack");
        }
        this.f1356k = new ArrayList<>(size);
        this.f1357l = new int[size];
        this.f1358m = new int[size];
        int i6 = 0;
        int i7 = 0;
        while (i6 < size) {
            AbstractC0210f0.a aVar = c0199a.f1412a.get(i6);
            int i8 = i7 + 1;
            this.f1355j[i7] = aVar.f1427a;
            ArrayList<String> arrayList = this.f1356k;
            ComponentCallbacksC0223m componentCallbacksC0223m = aVar.f1428b;
            arrayList.add(componentCallbacksC0223m != null ? componentCallbacksC0223m.f1523n : null);
            int[] iArr = this.f1355j;
            int i9 = i8 + 1;
            iArr[i8] = aVar.f1429c;
            int i10 = i9 + 1;
            iArr[i9] = aVar.f1430d;
            int i11 = i10 + 1;
            iArr[i10] = aVar.f1431e;
            iArr[i11] = aVar.f1432f;
            this.f1357l[i6] = aVar.f1433g.ordinal();
            this.f1358m[i6] = aVar.f1434h.ordinal();
            i6++;
            i7 = i11 + 1;
        }
        this.f1359n = c0199a.f1417f;
        this.f1360o = c0199a.f1419h;
        this.f1361p = c0199a.f1347r;
        this.f1362q = c0199a.f1420i;
        this.f1363r = c0199a.f1421j;
        this.f1364s = c0199a.f1422k;
        this.f1365t = c0199a.f1423l;
        this.f1366u = c0199a.f1424m;
        this.f1367v = c0199a.f1425n;
        this.f1368w = c0199a.f1426o;
    }

    @Override // android.os.Parcelable
    public final int describeContents() {
        return 0;
    }

    @Override // android.os.Parcelable
    public final void writeToParcel(Parcel parcel, int i6) {
        parcel.writeIntArray(this.f1355j);
        parcel.writeStringList(this.f1356k);
        parcel.writeIntArray(this.f1357l);
        parcel.writeIntArray(this.f1358m);
        parcel.writeInt(this.f1359n);
        parcel.writeString(this.f1360o);
        parcel.writeInt(this.f1361p);
        parcel.writeInt(this.f1362q);
        TextUtils.writeToParcel(this.f1363r, parcel, 0);
        parcel.writeInt(this.f1364s);
        TextUtils.writeToParcel(this.f1365t, parcel, 0);
        parcel.writeStringList(this.f1366u);
        parcel.writeStringList(this.f1367v);
        parcel.writeInt(this.f1368w ? 1 : 0);
    }
}
