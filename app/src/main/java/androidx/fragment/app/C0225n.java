package androidx.fragment.app;

import android.os.Bundle;
import android.os.Parcelable;
import androidx.lifecycle.AbstractC0251d;
import androidx.savedstate.C0338a;

/* renamed from: androidx.fragment.app.n */
/* loaded from: classes.dex */
public final class C0225n implements C0338a.b {

    /* renamed from: a */
    public final /* synthetic */ ActivityC0229p f1554a;

    public C0225n(ActivityC0229p activityC0229p) {
        this.f1554a = activityC0229p;
    }

    @Override // androidx.savedstate.C0338a.b
    /* renamed from: a */
    public final Bundle mo749a() {
        Bundle bundle = new Bundle();
        while (ActivityC0229p.m763o(this.f1554a.m765n())) {
        }
        this.f1554a.f1572r.m879e(AbstractC0251d.b.ON_STOP);
        Parcelable m813W = this.f1554a.f1571q.f1589a.f1594m.m813W();
        if (m813W != null) {
            bundle.putParcelable("android:support:fragments", m813W);
        }
        return bundle;
    }
}
