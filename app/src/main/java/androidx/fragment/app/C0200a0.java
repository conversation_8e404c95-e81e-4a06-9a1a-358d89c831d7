package androidx.fragment.app;

import android.util.Log;
import androidx.lifecycle.AbstractC0263p;
import androidx.lifecycle.C0264q;
import androidx.lifecycle.C0265r;
import java.util.HashMap;
import java.util.Iterator;

/* renamed from: androidx.fragment.app.a0 */
/* loaded from: classes.dex */
public final class C0200a0 extends AbstractC0263p {

    /* renamed from: h */
    public static final a f1348h = new a();

    /* renamed from: e */
    public final boolean f1352e;

    /* renamed from: b */
    public final HashMap<String, ComponentCallbacksC0223m> f1349b = new HashMap<>();

    /* renamed from: c */
    public final HashMap<String, C0200a0> f1350c = new HashMap<>();

    /* renamed from: d */
    public final HashMap<String, C0265r> f1351d = new HashMap<>();

    /* renamed from: f */
    public boolean f1353f = false;

    /* renamed from: g */
    public boolean f1354g = false;

    /* renamed from: androidx.fragment.app.a0$a */
    public class a implements C0264q.a {
        @Override // androidx.lifecycle.C0264q.a
        /* renamed from: a */
        public final AbstractC0263p mo627a() {
            return new C0200a0(true);
        }
    }

    public C0200a0(boolean z5) {
        this.f1352e = z5;
    }

    @Override // androidx.lifecycle.AbstractC0263p
    /* renamed from: a */
    public final void mo625a() {
        if (AbstractC0240x.m791K(3)) {
            Log.d("FragmentManager", "onCleared called for " + this);
        }
        this.f1353f = true;
    }

    /* renamed from: b */
    public final void m626b(ComponentCallbacksC0223m componentCallbacksC0223m) {
        if (this.f1354g) {
            if (AbstractC0240x.m791K(2)) {
                Log.v("FragmentManager", "Ignoring removeRetainedFragment as the state is already saved");
                return;
            }
            return;
        }
        if ((this.f1349b.remove(componentCallbacksC0223m.f1523n) != null) && AbstractC0240x.m791K(2)) {
            Log.v("FragmentManager", "Updating retained Fragments: Removed " + componentCallbacksC0223m);
        }
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || C0200a0.class != obj.getClass()) {
            return false;
        }
        C0200a0 c0200a0 = (C0200a0) obj;
        return this.f1349b.equals(c0200a0.f1349b) && this.f1350c.equals(c0200a0.f1350c) && this.f1351d.equals(c0200a0.f1351d);
    }

    public final int hashCode() {
        return this.f1351d.hashCode() + ((this.f1350c.hashCode() + (this.f1349b.hashCode() * 31)) * 31);
    }

    public final String toString() {
        StringBuilder sb = new StringBuilder("FragmentManagerViewModel{");
        sb.append(Integer.toHexString(System.identityHashCode(this)));
        sb.append("} Fragments (");
        Iterator<ComponentCallbacksC0223m> it = this.f1349b.values().iterator();
        while (it.hasNext()) {
            sb.append(it.next());
            if (it.hasNext()) {
                sb.append(", ");
            }
        }
        sb.append(") Child Non Config (");
        Iterator<String> it2 = this.f1350c.keySet().iterator();
        while (it2.hasNext()) {
            sb.append(it2.next());
            if (it2.hasNext()) {
                sb.append(", ");
            }
        }
        sb.append(") ViewModelStores (");
        Iterator<String> it3 = this.f1351d.keySet().iterator();
        while (it3.hasNext()) {
            sb.append(it3.next());
            if (it3.hasNext()) {
                sb.append(", ");
            }
        }
        sb.append(')');
        return sb.toString();
    }
}
