package androidx.fragment.app;

import android.util.Log;
import java.io.Writer;

/* renamed from: androidx.fragment.app.l0 */
/* loaded from: classes.dex */
public final class C0222l0 extends Writer {

    /* renamed from: k */
    public StringBuilder f1492k = new StringBuilder(128);

    /* renamed from: j */
    public final String f1491j = "FragmentManager";

    @Override // java.io.Writer, java.io.Closeable, java.lang.AutoCloseable
    public final void close() {
        m709o();
    }

    @Override // java.io.Writer, java.io.Flushable
    public final void flush() {
        m709o();
    }

    /* renamed from: o */
    public final void m709o() {
        if (this.f1492k.length() > 0) {
            Log.d(this.f1491j, this.f1492k.toString());
            StringBuilder sb = this.f1492k;
            sb.delete(0, sb.length());
        }
    }

    @Override // java.io.Writer
    public final void write(char[] cArr, int i6, int i7) {
        for (int i8 = 0; i8 < i7; i8++) {
            char c = cArr[i6 + i8];
            if (c == '\n') {
                m709o();
            } else {
                this.f1492k.append(c);
            }
        }
    }
}
