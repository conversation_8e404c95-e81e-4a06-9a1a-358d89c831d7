package androidx.fragment.app;

import androidx.lifecycle.AbstractC0251d;
import java.util.ArrayList;

/* renamed from: androidx.fragment.app.f0 */
/* loaded from: classes.dex */
public abstract class AbstractC0210f0 {

    /* renamed from: b */
    public int f1413b;

    /* renamed from: c */
    public int f1414c;

    /* renamed from: d */
    public int f1415d;

    /* renamed from: e */
    public int f1416e;

    /* renamed from: f */
    public int f1417f;

    /* renamed from: g */
    public boolean f1418g;

    /* renamed from: h */
    public String f1419h;

    /* renamed from: i */
    public int f1420i;

    /* renamed from: j */
    public CharSequence f1421j;

    /* renamed from: k */
    public int f1422k;

    /* renamed from: l */
    public CharSequence f1423l;

    /* renamed from: m */
    public ArrayList<String> f1424m;

    /* renamed from: n */
    public ArrayList<String> f1425n;

    /* renamed from: a */
    public ArrayList<a> f1412a = new ArrayList<>();

    /* renamed from: o */
    public boolean f1426o = false;

    /* renamed from: androidx.fragment.app.f0$a */
    public static final class a {

        /* renamed from: a */
        public int f1427a;

        /* renamed from: b */
        public ComponentCallbacksC0223m f1428b;

        /* renamed from: c */
        public int f1429c;

        /* renamed from: d */
        public int f1430d;

        /* renamed from: e */
        public int f1431e;

        /* renamed from: f */
        public int f1432f;

        /* renamed from: g */
        public AbstractC0251d.c f1433g;

        /* renamed from: h */
        public AbstractC0251d.c f1434h;

        public a() {
        }

        public a(int i6, ComponentCallbacksC0223m componentCallbacksC0223m) {
            this.f1427a = i6;
            this.f1428b = componentCallbacksC0223m;
            AbstractC0251d.c cVar = AbstractC0251d.c.RESUMED;
            this.f1433g = cVar;
            this.f1434h = cVar;
        }

        public a(ComponentCallbacksC0223m componentCallbacksC0223m, AbstractC0251d.c cVar) {
            this.f1427a = 10;
            this.f1428b = componentCallbacksC0223m;
            this.f1433g = componentCallbacksC0223m.f1513T;
            this.f1434h = cVar;
        }
    }

    /* renamed from: b */
    public final void m667b(a aVar) {
        this.f1412a.add(aVar);
        aVar.f1429c = this.f1413b;
        aVar.f1430d = this.f1414c;
        aVar.f1431e = this.f1415d;
        aVar.f1432f = this.f1416e;
    }
}
