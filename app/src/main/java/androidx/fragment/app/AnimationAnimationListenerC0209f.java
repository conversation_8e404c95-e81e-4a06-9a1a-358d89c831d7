package androidx.fragment.app;

import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import androidx.fragment.app.C0203c;

/* renamed from: androidx.fragment.app.f */
/* loaded from: classes.dex */
public final class AnimationAnimationListenerC0209f implements Animation.AnimationListener {

    /* renamed from: a */
    public final /* synthetic */ ViewGroup f1408a;

    /* renamed from: b */
    public final /* synthetic */ View f1409b;

    /* renamed from: c */
    public final /* synthetic */ C0203c.b f1410c;

    /* renamed from: androidx.fragment.app.f$a */
    public class a implements Runnable {
        public a() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            AnimationAnimationListenerC0209f animationAnimationListenerC0209f = AnimationAnimationListenerC0209f.this;
            animationAnimationListenerC0209f.f1408a.endViewTransition(animationAnimationListenerC0209f.f1409b);
            AnimationAnimationListenerC0209f.this.f1410c.m634a();
        }
    }

    public AnimationAnimationListenerC0209f(ViewGroup viewGroup, View view, C0203c.b bVar) {
        this.f1408a = viewGroup;
        this.f1409b = view;
        this.f1410c = bVar;
    }

    @Override // android.view.animation.Animation.AnimationListener
    public final void onAnimationEnd(Animation animation) {
        this.f1408a.post(new a());
    }

    @Override // android.view.animation.Animation.AnimationListener
    public final void onAnimationRepeat(Animation animation) {
    }

    @Override // android.view.animation.Animation.AnimationListener
    public final void onAnimationStart(Animation animation) {
    }
}
