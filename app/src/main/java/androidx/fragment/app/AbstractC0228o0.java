package androidx.fragment.app;

import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import androidx.activity.result.C0052a;
import androidx.appcompat.widget.C0174y;
import androidx.fragment.app.AbstractC0240x;
import androidx.fragment.app.ComponentCallbacksC0223m;
import com.liaoyuan.aicast.R;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.WeakHashMap;
import p001a0.C0002a;
import p029e0.C0766p;
import p029e0.C0769s;

/* renamed from: androidx.fragment.app.o0 */
/* loaded from: classes.dex */
public abstract class AbstractC0228o0 {

    /* renamed from: a */
    public final ViewGroup f1558a;

    /* renamed from: b */
    public final ArrayList<b> f1559b = new ArrayList<>();

    /* renamed from: c */
    public final ArrayList<b> f1560c = new ArrayList<>();

    /* renamed from: d */
    public boolean f1561d = false;

    /* renamed from: e */
    public boolean f1562e = false;

    /* renamed from: androidx.fragment.app.o0$a */
    public static class a extends b {

        /* renamed from: h */
        public final C0206d0 f1563h;

        public a(int i6, int i7, C0206d0 c0206d0, C0002a c0002a) {
            super(i6, i7, c0206d0.f1400c, c0002a);
            this.f1563h = c0206d0;
        }

        @Override // androidx.fragment.app.AbstractC0228o0.b
        /* renamed from: c */
        public final void mo758c() {
            super.mo758c();
            this.f1563h.m647k();
        }

        @Override // androidx.fragment.app.AbstractC0228o0.b
        /* renamed from: e */
        public final void mo759e() {
            if (this.f1565b == 2) {
                ComponentCallbacksC0223m componentCallbacksC0223m = this.f1563h.f1400c;
                View findFocus = componentCallbacksC0223m.f1507N.findFocus();
                if (findFocus != null) {
                    componentCallbacksC0223m.m726Z(findFocus);
                    if (AbstractC0240x.m791K(2)) {
                        Log.v("FragmentManager", "requestFocus: Saved focused view " + findFocus + " for Fragment " + componentCallbacksC0223m);
                    }
                }
                View m722V = this.f1566c.m722V();
                if (m722V.getParent() == null) {
                    this.f1563h.m638b();
                    m722V.setAlpha(0.0f);
                }
                if (m722V.getAlpha() == 0.0f && m722V.getVisibility() == 0) {
                    m722V.setVisibility(4);
                }
                ComponentCallbacksC0223m.b bVar = componentCallbacksC0223m.f1510Q;
                m722V.setAlpha(bVar == null ? 1.0f : bVar.f1549m);
            }
        }
    }

    /* renamed from: androidx.fragment.app.o0$b */
    public static class b {

        /* renamed from: a */
        public int f1564a;

        /* renamed from: b */
        public int f1565b;

        /* renamed from: c */
        public final ComponentCallbacksC0223m f1566c;

        /* renamed from: d */
        public final List<Runnable> f1567d = new ArrayList();

        /* renamed from: e */
        public final HashSet<C0002a> f1568e = new HashSet<>();

        /* renamed from: f */
        public boolean f1569f = false;

        /* renamed from: g */
        public boolean f1570g = false;

        public b(int i6, int i7, ComponentCallbacksC0223m componentCallbacksC0223m, C0002a c0002a) {
            this.f1564a = i6;
            this.f1565b = i7;
            this.f1566c = componentCallbacksC0223m;
            c0002a.m2b(new C0230p0(this));
        }

        /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<java.lang.Runnable>] */
        /* renamed from: a */
        public final void m760a(Runnable runnable) {
            this.f1567d.add(runnable);
        }

        /* renamed from: b */
        public final void m761b() {
            if (this.f1569f) {
                return;
            }
            this.f1569f = true;
            if (this.f1568e.isEmpty()) {
                mo758c();
                return;
            }
            Iterator it = new ArrayList(this.f1568e).iterator();
            while (it.hasNext()) {
                ((C0002a) it.next()).m1a();
            }
        }

        /* JADX WARN: Type inference failed for: r0v4, types: [java.util.ArrayList, java.util.List<java.lang.Runnable>] */
        /* renamed from: c */
        public void mo758c() {
            if (this.f1570g) {
                return;
            }
            if (AbstractC0240x.m791K(2)) {
                Log.v("FragmentManager", "SpecialEffectsController: " + this + " has called complete.");
            }
            this.f1570g = true;
            Iterator it = this.f1567d.iterator();
            while (it.hasNext()) {
                ((Runnable) it.next()).run();
            }
        }

        /* renamed from: d */
        public final void m762d(int i6, int i7) {
            if (i7 == 0) {
                throw null;
            }
            int i8 = i7 - 1;
            if (i8 == 0) {
                if (this.f1564a != 1) {
                    if (AbstractC0240x.m791K(2)) {
                        StringBuilder m104h = C0052a.m104h("SpecialEffectsController: For fragment ");
                        m104h.append(this.f1566c);
                        m104h.append(" mFinalState = ");
                        m104h.append(C0174y.m493k(this.f1564a));
                        m104h.append(" -> ");
                        m104h.append(C0174y.m493k(i6));
                        m104h.append(". ");
                        Log.v("FragmentManager", m104h.toString());
                    }
                    this.f1564a = i6;
                    return;
                }
                return;
            }
            if (i8 == 1) {
                if (this.f1564a == 1) {
                    if (AbstractC0240x.m791K(2)) {
                        StringBuilder m104h2 = C0052a.m104h("SpecialEffectsController: For fragment ");
                        m104h2.append(this.f1566c);
                        m104h2.append(" mFinalState = REMOVED -> VISIBLE. mLifecycleImpact = ");
                        m104h2.append(C0052a.m108l(this.f1565b));
                        m104h2.append(" to ADDING.");
                        Log.v("FragmentManager", m104h2.toString());
                    }
                    this.f1564a = 2;
                    this.f1565b = 2;
                    return;
                }
                return;
            }
            if (i8 != 2) {
                return;
            }
            if (AbstractC0240x.m791K(2)) {
                StringBuilder m104h3 = C0052a.m104h("SpecialEffectsController: For fragment ");
                m104h3.append(this.f1566c);
                m104h3.append(" mFinalState = ");
                m104h3.append(C0174y.m493k(this.f1564a));
                m104h3.append(" -> REMOVED. mLifecycleImpact  = ");
                m104h3.append(C0052a.m108l(this.f1565b));
                m104h3.append(" to REMOVING.");
                Log.v("FragmentManager", m104h3.toString());
            }
            this.f1564a = 1;
            this.f1565b = 3;
        }

        /* renamed from: e */
        public void mo759e() {
        }

        public final String toString() {
            StringBuilder m492j = C0174y.m492j("Operation ", "{");
            m492j.append(Integer.toHexString(System.identityHashCode(this)));
            m492j.append("} ");
            m492j.append("{");
            m492j.append("mFinalState = ");
            m492j.append(C0174y.m493k(this.f1564a));
            m492j.append("} ");
            m492j.append("{");
            m492j.append("mLifecycleImpact = ");
            m492j.append(C0052a.m108l(this.f1565b));
            m492j.append("} ");
            m492j.append("{");
            m492j.append("mFragment = ");
            m492j.append(this.f1566c);
            m492j.append("}");
            return m492j.toString();
        }
    }

    public AbstractC0228o0(ViewGroup viewGroup) {
        this.f1558a = viewGroup;
    }

    /* renamed from: f */
    public static AbstractC0228o0 m750f(ViewGroup viewGroup, AbstractC0240x abstractC0240x) {
        return m751g(viewGroup, abstractC0240x.m800I());
    }

    /* renamed from: g */
    public static AbstractC0228o0 m751g(ViewGroup viewGroup, InterfaceC0232q0 interfaceC0232q0) {
        Object tag = viewGroup.getTag(R.id.special_effects_controller_view_tag);
        if (tag instanceof AbstractC0228o0) {
            return (AbstractC0228o0) tag;
        }
        Objects.requireNonNull((AbstractC0240x.f) interfaceC0232q0);
        C0203c c0203c = new C0203c(viewGroup);
        viewGroup.setTag(R.id.special_effects_controller_view_tag, c0203c);
        return c0203c;
    }

    /* renamed from: a */
    public final void m752a(int i6, int i7, C0206d0 c0206d0) {
        synchronized (this.f1559b) {
            C0002a c0002a = new C0002a();
            b m754d = m754d(c0206d0.f1400c);
            if (m754d != null) {
                m754d.m762d(i6, i7);
                return;
            }
            a aVar = new a(i6, i7, c0206d0, c0002a);
            this.f1559b.add(aVar);
            aVar.m760a(new RunnableC0224m0(this, aVar));
            aVar.m760a(new RunnableC0226n0(this, aVar));
        }
    }

    /* renamed from: b */
    public abstract void mo629b(List<b> list, boolean z5);

    /* renamed from: c */
    public final void m753c() {
        if (this.f1562e) {
            return;
        }
        ViewGroup viewGroup = this.f1558a;
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        if (!viewGroup.isAttachedToWindow()) {
            m755e();
            this.f1561d = false;
            return;
        }
        synchronized (this.f1559b) {
            if (!this.f1559b.isEmpty()) {
                ArrayList arrayList = new ArrayList(this.f1560c);
                this.f1560c.clear();
                Iterator it = arrayList.iterator();
                while (it.hasNext()) {
                    b bVar = (b) it.next();
                    if (AbstractC0240x.m791K(2)) {
                        Log.v("FragmentManager", "SpecialEffectsController: Cancelling operation " + bVar);
                    }
                    bVar.m761b();
                    if (!bVar.f1570g) {
                        this.f1560c.add(bVar);
                    }
                }
                m757i();
                ArrayList arrayList2 = new ArrayList(this.f1559b);
                this.f1559b.clear();
                this.f1560c.addAll(arrayList2);
                Iterator it2 = arrayList2.iterator();
                while (it2.hasNext()) {
                    ((b) it2.next()).mo759e();
                }
                mo629b(arrayList2, this.f1561d);
                this.f1561d = false;
            }
        }
    }

    /* renamed from: d */
    public final b m754d(ComponentCallbacksC0223m componentCallbacksC0223m) {
        Iterator<b> it = this.f1559b.iterator();
        while (it.hasNext()) {
            b next = it.next();
            if (next.f1566c.equals(componentCallbacksC0223m) && !next.f1569f) {
                return next;
            }
        }
        return null;
    }

    /* renamed from: e */
    public final void m755e() {
        String str;
        String str2;
        ViewGroup viewGroup = this.f1558a;
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        boolean isAttachedToWindow = viewGroup.isAttachedToWindow();
        synchronized (this.f1559b) {
            m757i();
            Iterator<b> it = this.f1559b.iterator();
            while (it.hasNext()) {
                it.next().mo759e();
            }
            Iterator it2 = new ArrayList(this.f1560c).iterator();
            while (it2.hasNext()) {
                b bVar = (b) it2.next();
                if (AbstractC0240x.m791K(2)) {
                    StringBuilder sb = new StringBuilder();
                    sb.append("SpecialEffectsController: ");
                    if (isAttachedToWindow) {
                        str2 = "";
                    } else {
                        str2 = "Container " + this.f1558a + " is not attached to window. ";
                    }
                    sb.append(str2);
                    sb.append("Cancelling running operation ");
                    sb.append(bVar);
                    Log.v("FragmentManager", sb.toString());
                }
                bVar.m761b();
            }
            Iterator it3 = new ArrayList(this.f1559b).iterator();
            while (it3.hasNext()) {
                b bVar2 = (b) it3.next();
                if (AbstractC0240x.m791K(2)) {
                    StringBuilder sb2 = new StringBuilder();
                    sb2.append("SpecialEffectsController: ");
                    if (isAttachedToWindow) {
                        str = "";
                    } else {
                        str = "Container " + this.f1558a + " is not attached to window. ";
                    }
                    sb2.append(str);
                    sb2.append("Cancelling pending operation ");
                    sb2.append(bVar2);
                    Log.v("FragmentManager", sb2.toString());
                }
                bVar2.m761b();
            }
        }
    }

    /* renamed from: h */
    public final void m756h() {
        synchronized (this.f1559b) {
            m757i();
            this.f1562e = false;
            int size = this.f1559b.size();
            while (true) {
                size--;
                if (size < 0) {
                    break;
                }
                b bVar = this.f1559b.get(size);
                int m486d = C0174y.m486d(bVar.f1566c.f1507N);
                if (bVar.f1564a == 2 && m486d != 2) {
                    Objects.requireNonNull(bVar.f1566c);
                    this.f1562e = false;
                    break;
                }
            }
        }
    }

    /* renamed from: i */
    public final void m757i() {
        Iterator<b> it = this.f1559b.iterator();
        while (it.hasNext()) {
            b next = it.next();
            if (next.f1565b == 2) {
                next.m762d(C0174y.m485c(next.f1566c.m722V().getVisibility()), 1);
            }
        }
    }
}
