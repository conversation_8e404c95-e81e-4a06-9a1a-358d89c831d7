package androidx.fragment.app;

import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.util.Log;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import p009b1.C0395h;
import p023d1.InterfaceC0725w;
import p030e1.InterfaceC0775c;
import p072k1.C0985d;
import p098o1.C1139c;
import p105p1.InterfaceC1232b;

/* renamed from: androidx.fragment.app.e0 */
/* loaded from: classes.dex */
public final class C0208e0 implements InterfaceC1232b {

    /* renamed from: j */
    public final Object f1405j;

    /* renamed from: k */
    public final Object f1406k;

    /* renamed from: l */
    public Object f1407l;

    public /* synthetic */ C0208e0() {
        this.f1405j = new ArrayList();
        this.f1406k = new HashMap();
    }

    public /* synthetic */ C0208e0(Object obj, Object obj2, Object obj3) {
        this.f1405j = obj;
        this.f1406k = obj2;
        this.f1407l = obj3;
    }

    /* renamed from: a */
    public final void m655a(ComponentCallbacksC0223m componentCallbacksC0223m) {
        if (((ArrayList) this.f1405j).contains(componentCallbacksC0223m)) {
            throw new IllegalStateException("Fragment already added: " + componentCallbacksC0223m);
        }
        synchronized (((ArrayList) this.f1405j)) {
            ((ArrayList) this.f1405j).add(componentCallbacksC0223m);
        }
        componentCallbacksC0223m.f1529t = true;
    }

    /* renamed from: b */
    public final void m656b() {
        ((HashMap) this.f1406k).values().removeAll(Collections.singleton(null));
    }

    /* renamed from: c */
    public final boolean m657c(String str) {
        return ((HashMap) this.f1406k).get(str) != null;
    }

    /* renamed from: d */
    public final ComponentCallbacksC0223m m658d(String str) {
        C0206d0 c0206d0 = (C0206d0) ((HashMap) this.f1406k).get(str);
        if (c0206d0 != null) {
            return c0206d0.f1400c;
        }
        return null;
    }

    /* renamed from: e */
    public final ComponentCallbacksC0223m m659e(String str) {
        for (C0206d0 c0206d0 : ((HashMap) this.f1406k).values()) {
            if (c0206d0 != null) {
                ComponentCallbacksC0223m componentCallbacksC0223m = c0206d0.f1400c;
                if (!str.equals(componentCallbacksC0223m.f1523n)) {
                    componentCallbacksC0223m = componentCallbacksC0223m.f1496C.f1613c.m659e(str);
                }
                if (componentCallbacksC0223m != null) {
                    return componentCallbacksC0223m;
                }
            }
        }
        return null;
    }

    /* renamed from: f */
    public final List m660f() {
        ArrayList arrayList = new ArrayList();
        for (C0206d0 c0206d0 : ((HashMap) this.f1406k).values()) {
            if (c0206d0 != null) {
                arrayList.add(c0206d0);
            }
        }
        return arrayList;
    }

    /* renamed from: g */
    public final List m661g() {
        ArrayList arrayList = new ArrayList();
        for (C0206d0 c0206d0 : ((HashMap) this.f1406k).values()) {
            arrayList.add(c0206d0 != null ? c0206d0.f1400c : null);
        }
        return arrayList;
    }

    @Override // p105p1.InterfaceC1232b
    /* renamed from: h */
    public final InterfaceC0725w mo662h(InterfaceC0725w interfaceC0725w, C0395h c0395h) {
        Drawable drawable = (Drawable) interfaceC0725w.get();
        if (drawable instanceof BitmapDrawable) {
            return ((InterfaceC1232b) this.f1406k).mo662h(C0985d.m2560f(((BitmapDrawable) drawable).getBitmap(), (InterfaceC0775c) this.f1405j), c0395h);
        }
        if (drawable instanceof C1139c) {
            return ((InterfaceC1232b) this.f1407l).mo662h(interfaceC0725w, c0395h);
        }
        return null;
    }

    /* renamed from: i */
    public final C0206d0 m663i(String str) {
        return (C0206d0) ((HashMap) this.f1406k).get(str);
    }

    /* renamed from: j */
    public final List m664j() {
        ArrayList arrayList;
        if (((ArrayList) this.f1405j).isEmpty()) {
            return Collections.emptyList();
        }
        synchronized (((ArrayList) this.f1405j)) {
            arrayList = new ArrayList((ArrayList) this.f1405j);
        }
        return arrayList;
    }

    /* renamed from: k */
    public final void m665k(C0206d0 c0206d0) {
        ComponentCallbacksC0223m componentCallbacksC0223m = c0206d0.f1400c;
        if (m657c(componentCallbacksC0223m.f1523n)) {
            return;
        }
        ((HashMap) this.f1406k).put(componentCallbacksC0223m.f1523n, c0206d0);
        if (AbstractC0240x.m791K(2)) {
            Log.v("FragmentManager", "Added fragment to active set " + componentCallbacksC0223m);
        }
    }

    /* renamed from: l */
    public final void m666l(C0206d0 c0206d0) {
        ComponentCallbacksC0223m componentCallbacksC0223m = c0206d0.f1400c;
        if (componentCallbacksC0223m.f1503J) {
            ((C0200a0) this.f1407l).m626b(componentCallbacksC0223m);
        }
        if (((C0206d0) ((HashMap) this.f1406k).put(componentCallbacksC0223m.f1523n, null)) != null && AbstractC0240x.m791K(2)) {
            Log.v("FragmentManager", "Removed fragment from active set " + componentCallbacksC0223m);
        }
    }
}
