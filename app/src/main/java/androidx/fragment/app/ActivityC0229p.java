package androidx.fragment.app;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.Window;
import androidx.activity.ComponentActivity;
import androidx.activity.InterfaceC0051c;
import androidx.activity.OnBackPressedDispatcher;
import androidx.activity.result.AbstractC0056e;
import androidx.activity.result.InterfaceC0057f;
import androidx.lifecycle.AbstractC0251d;
import androidx.lifecycle.C0255h;
import androidx.lifecycle.C0265r;
import androidx.lifecycle.InterfaceC0266s;
import java.io.FileDescriptor;
import java.io.PrintWriter;
import java.util.Objects;
import p000a.C0000a;
import p111q0.AbstractC1253a;
import p135u.C1407a;

/* renamed from: androidx.fragment.app.p */
/* loaded from: classes.dex */
public class ActivityC0229p extends ComponentActivity implements C1407a.a {

    /* renamed from: s */
    public boolean f1573s;

    /* renamed from: t */
    public boolean f1574t;

    /* renamed from: q */
    public final C0235s f1571q = new C0235s(new a());

    /* renamed from: r */
    public final C0255h f1572r = new C0255h(this);

    /* renamed from: u */
    public boolean f1575u = true;

    /* renamed from: androidx.fragment.app.p$a */
    public class a extends AbstractC0237u<ActivityC0229p> implements InterfaceC0266s, InterfaceC0051c, InterfaceC0057f, InterfaceC0202b0 {
        public a() {
            super(ActivityC0229p.this);
        }

        @Override // androidx.lifecycle.InterfaceC0254g
        /* renamed from: a */
        public final AbstractC0251d mo86a() {
            return ActivityC0229p.this.f1572r;
        }

        @Override // androidx.activity.InterfaceC0051c
        /* renamed from: b */
        public final OnBackPressedDispatcher mo87b() {
            return ActivityC0229p.this.f187o;
        }

        @Override // androidx.fragment.app.InterfaceC0202b0
        /* renamed from: d */
        public final void mo628d() {
            Objects.requireNonNull(ActivityC0229p.this);
        }

        @Override // androidx.activity.result.InterfaceC0057f
        /* renamed from: e */
        public final AbstractC0056e mo89e() {
            return ActivityC0229p.this.f188p;
        }

        @Override // androidx.lifecycle.InterfaceC0266s
        /* renamed from: g */
        public final C0265r mo90g() {
            return ActivityC0229p.this.mo90g();
        }

        @Override // androidx.activity.result.AbstractC0055d
        /* renamed from: k */
        public final View mo118k(int i6) {
            return ActivityC0229p.this.findViewById(i6);
        }

        @Override // androidx.activity.result.AbstractC0055d
        /* renamed from: n */
        public final boolean mo121n() {
            Window window = ActivityC0229p.this.getWindow();
            return (window == null || window.peekDecorView() == null) ? false : true;
        }

        @Override // androidx.fragment.app.AbstractC0237u
        /* renamed from: r */
        public final void mo767r(PrintWriter printWriter, String[] strArr) {
            ActivityC0229p.this.dump("  ", null, printWriter, strArr);
        }

        @Override // androidx.fragment.app.AbstractC0237u
        /* renamed from: s */
        public final ActivityC0229p mo768s() {
            return ActivityC0229p.this;
        }

        @Override // androidx.fragment.app.AbstractC0237u
        /* renamed from: t */
        public final LayoutInflater mo769t() {
            return ActivityC0229p.this.getLayoutInflater().cloneInContext(ActivityC0229p.this);
        }

        @Override // androidx.fragment.app.AbstractC0237u
        /* renamed from: u */
        public final void mo770u() {
            ActivityC0229p.this.mo766p();
        }
    }

    /* JADX WARN: Type inference failed for: r1v3, types: [java.util.Set<a.b>, java.util.concurrent.CopyOnWriteArraySet] */
    public ActivityC0229p() {
        this.f185m.f2181b.m1362b("android:support:fragments", new C0225n(this));
        C0227o c0227o = new C0227o(this);
        C0000a c0000a = this.f183k;
        if (c0000a.f1b != null) {
            c0227o.mo0a();
        }
        c0000a.f0a.add(c0227o);
    }

    /* renamed from: o */
    public static boolean m763o(AbstractC0240x abstractC0240x) {
        AbstractC0251d.c cVar = AbstractC0251d.c.STARTED;
        boolean z5 = false;
        for (ComponentCallbacksC0223m componentCallbacksC0223m : abstractC0240x.f1613c.m664j()) {
            if (componentCallbacksC0223m != null) {
                AbstractC0237u<?> abstractC0237u = componentCallbacksC0223m.f1495B;
                if ((abstractC0237u == null ? null : abstractC0237u.mo768s()) != null) {
                    z5 |= m763o(componentCallbacksC0223m.m731i());
                }
                C0220k0 c0220k0 = componentCallbacksC0223m.f1515V;
                if (c0220k0 != null) {
                    c0220k0.m694e();
                    if (c0220k0.f1468k.f1700b.m875d(cVar)) {
                        componentCallbacksC0223m.f1515V.f1468k.m883j();
                        z5 = true;
                    }
                }
                if (componentCallbacksC0223m.f1514U.f1700b.m875d(cVar)) {
                    componentCallbacksC0223m.f1514U.m883j();
                    z5 = true;
                }
            }
        }
        return z5;
    }

    @Override // android.app.Activity
    public final void dump(String str, FileDescriptor fileDescriptor, PrintWriter printWriter, String[] strArr) {
        super.dump(str, fileDescriptor, printWriter, strArr);
        printWriter.print(str);
        printWriter.print("Local FragmentActivity ");
        printWriter.print(Integer.toHexString(System.identityHashCode(this)));
        printWriter.println(" State:");
        String str2 = str + "  ";
        printWriter.print(str2);
        printWriter.print("mCreated=");
        printWriter.print(this.f1573s);
        printWriter.print(" mResumed=");
        printWriter.print(this.f1574t);
        printWriter.print(" mStopped=");
        printWriter.print(this.f1575u);
        if (getApplication() != null) {
            AbstractC1253a.m3142b(this).mo3143a(str2, printWriter);
        }
        this.f1571q.f1589a.f1594m.m844v(str, fileDescriptor, printWriter, strArr);
    }

    @Override // p135u.C1407a.a
    @Deprecated
    /* renamed from: j */
    public final void mo764j() {
    }

    /* renamed from: n */
    public final AbstractC0240x m765n() {
        return this.f1571q.f1589a.f1594m;
    }

    @Override // androidx.activity.ComponentActivity, android.app.Activity
    public void onActivityResult(int i6, int i7, Intent intent) {
        this.f1571q.m773a();
        super.onActivityResult(i6, i7, intent);
    }

    @Override // android.app.Activity, android.content.ComponentCallbacks
    public void onConfigurationChanged(Configuration configuration) {
        super.onConfigurationChanged(configuration);
        this.f1571q.m773a();
        this.f1571q.f1589a.f1594m.m830h(configuration);
    }

    @Override // androidx.activity.ComponentActivity, p135u.ActivityC1410d, android.app.Activity
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        this.f1572r.m879e(AbstractC0251d.b.ON_CREATE);
        this.f1571q.f1589a.f1594m.m832j();
    }

    @Override // android.app.Activity, android.view.Window.Callback
    public final boolean onCreatePanelMenu(int i6, Menu menu) {
        if (i6 != 0) {
            return super.onCreatePanelMenu(i6, menu);
        }
        boolean onCreatePanelMenu = super.onCreatePanelMenu(i6, menu);
        C0235s c0235s = this.f1571q;
        return onCreatePanelMenu | c0235s.f1589a.f1594m.m833k(menu, getMenuInflater());
    }

    @Override // android.app.Activity, android.view.LayoutInflater.Factory2
    public final View onCreateView(View view, String str, Context context, AttributeSet attributeSet) {
        View onCreateView = this.f1571q.f1589a.f1594m.f1616f.onCreateView(view, str, context, attributeSet);
        return onCreateView == null ? super.onCreateView(view, str, context, attributeSet) : onCreateView;
    }

    @Override // android.app.Activity
    public void onDestroy() {
        super.onDestroy();
        this.f1571q.f1589a.f1594m.m834l();
        this.f1572r.m879e(AbstractC0251d.b.ON_DESTROY);
    }

    @Override // android.app.Activity, android.content.ComponentCallbacks
    public final void onLowMemory() {
        super.onLowMemory();
        this.f1571q.f1589a.f1594m.m835m();
    }

    @Override // android.app.Activity, android.view.Window.Callback
    public boolean onMenuItemSelected(int i6, MenuItem menuItem) {
        if (super.onMenuItemSelected(i6, menuItem)) {
            return true;
        }
        if (i6 == 0) {
            return this.f1571q.f1589a.f1594m.m837o(menuItem);
        }
        if (i6 != 6) {
            return false;
        }
        return this.f1571q.f1589a.f1594m.m831i(menuItem);
    }

    @Override // android.app.Activity
    public final void onMultiWindowModeChanged(boolean z5) {
        this.f1571q.f1589a.f1594m.m836n(z5);
    }

    @Override // android.app.Activity
    public final void onNewIntent(@SuppressLint({"UnknownNullness"}) Intent intent) {
        super.onNewIntent(intent);
        this.f1571q.m773a();
    }

    @Override // android.app.Activity, android.view.Window.Callback
    public void onPanelClosed(int i6, Menu menu) {
        if (i6 == 0) {
            this.f1571q.f1589a.f1594m.m838p(menu);
        }
        super.onPanelClosed(i6, menu);
    }

    @Override // android.app.Activity
    public void onPause() {
        super.onPause();
        this.f1574t = false;
        this.f1571q.f1589a.f1594m.m842t(5);
        this.f1572r.m879e(AbstractC0251d.b.ON_PAUSE);
    }

    @Override // android.app.Activity
    public final void onPictureInPictureModeChanged(boolean z5) {
        this.f1571q.f1589a.f1594m.m840r(z5);
    }

    @Override // android.app.Activity
    public void onPostResume() {
        super.onPostResume();
        this.f1572r.m879e(AbstractC0251d.b.ON_RESUME);
        C0241y c0241y = this.f1571q.f1589a.f1594m;
        c0241y.f1602A = false;
        c0241y.f1603B = false;
        c0241y.f1609H.f1354g = false;
        c0241y.m842t(7);
    }

    @Override // android.app.Activity, android.view.Window.Callback
    public final boolean onPreparePanel(int i6, View view, Menu menu) {
        return i6 == 0 ? super.onPreparePanel(0, view, menu) | this.f1571q.f1589a.f1594m.m841s(menu) : super.onPreparePanel(i6, view, menu);
    }

    @Override // androidx.activity.ComponentActivity, android.app.Activity
    public void onRequestPermissionsResult(int i6, String[] strArr, int[] iArr) {
        this.f1571q.m773a();
        super.onRequestPermissionsResult(i6, strArr, iArr);
    }

    @Override // android.app.Activity
    public void onResume() {
        super.onResume();
        this.f1574t = true;
        this.f1571q.m773a();
        this.f1571q.f1589a.f1594m.m848z(true);
    }

    @Override // android.app.Activity
    public void onStart() {
        super.onStart();
        this.f1575u = false;
        if (!this.f1573s) {
            this.f1573s = true;
            C0241y c0241y = this.f1571q.f1589a.f1594m;
            c0241y.f1602A = false;
            c0241y.f1603B = false;
            c0241y.f1609H.f1354g = false;
            c0241y.m842t(4);
        }
        this.f1571q.m773a();
        this.f1571q.f1589a.f1594m.m848z(true);
        this.f1572r.m879e(AbstractC0251d.b.ON_START);
        C0241y c0241y2 = this.f1571q.f1589a.f1594m;
        c0241y2.f1602A = false;
        c0241y2.f1603B = false;
        c0241y2.f1609H.f1354g = false;
        c0241y2.m842t(5);
    }

    @Override // android.app.Activity
    public final void onStateNotSaved() {
        this.f1571q.m773a();
    }

    @Override // android.app.Activity
    public void onStop() {
        super.onStop();
        this.f1575u = true;
        while (m763o(m765n())) {
        }
        C0241y c0241y = this.f1571q.f1589a.f1594m;
        c0241y.f1603B = true;
        c0241y.f1609H.f1354g = true;
        c0241y.m842t(4);
        this.f1572r.m879e(AbstractC0251d.b.ON_STOP);
    }

    @Deprecated
    /* renamed from: p */
    public void mo766p() {
        invalidateOptionsMenu();
    }

    @Override // android.app.Activity, android.view.LayoutInflater.Factory
    public final View onCreateView(String str, Context context, AttributeSet attributeSet) {
        View onCreateView = this.f1571q.f1589a.f1594m.f1616f.onCreateView(null, str, context, attributeSet);
        return onCreateView == null ? super.onCreateView(str, context, attributeSet) : onCreateView;
    }
}
