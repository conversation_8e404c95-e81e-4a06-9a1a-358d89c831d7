package androidx.fragment.app;

import androidx.appcompat.widget.C0174y;
import androidx.fragment.app.ComponentCallbacksC0223m;
import p077l.C1053g;

/* renamed from: androidx.fragment.app.t */
/* loaded from: classes.dex */
public class C0236t {

    /* renamed from: a */
    public static final C1053g<ClassLoader, C1053g<String, Class<?>>> f1590a = new C1053g<>();

    /* renamed from: b */
    public static Class<?> m774b(ClassLoader classLoader, String str) {
        C1053g<ClassLoader, C1053g<String, Class<?>>> c1053g = f1590a;
        C1053g<String, Class<?>> orDefault = c1053g.getOrDefault(classLoader, null);
        if (orDefault == null) {
            orDefault = new C1053g<>();
            c1053g.put(classLoader, orDefault);
        }
        Class<?> orDefault2 = orDefault.getOrDefault(str, null);
        if (orDefault2 != null) {
            return orDefault2;
        }
        Class<?> cls = Class.forName(str, false, classLoader);
        orDefault.put(str, cls);
        return cls;
    }

    /* renamed from: c */
    public static Class<? extends ComponentCallbacksC0223m> m775c(ClassLoader classLoader, String str) {
        try {
            return m774b(classLoader, str);
        } catch (ClassCastException e6) {
            throw new ComponentCallbacksC0223m.c(C0174y.m491i("Unable to instantiate fragment ", str, ": make sure class is a valid subclass of Fragment"), e6);
        } catch (ClassNotFoundException e7) {
            throw new ComponentCallbacksC0223m.c(C0174y.m491i("Unable to instantiate fragment ", str, ": make sure class name exists"), e7);
        }
    }

    /* renamed from: a */
    public ComponentCallbacksC0223m mo776a(ClassLoader classLoader, String str) {
        throw null;
    }
}
