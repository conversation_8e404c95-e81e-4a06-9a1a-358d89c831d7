package androidx.fragment.app;

import androidx.lifecycle.AbstractC0251d;
import androidx.lifecycle.C0255h;
import androidx.lifecycle.C0265r;
import androidx.lifecycle.InterfaceC0266s;
import androidx.savedstate.C0338a;
import androidx.savedstate.C0339b;
import androidx.savedstate.InterfaceC0340c;

/* renamed from: androidx.fragment.app.k0 */
/* loaded from: classes.dex */
public final class C0220k0 implements InterfaceC0340c, InterfaceC0266s {

    /* renamed from: j */
    public final C0265r f1467j;

    /* renamed from: k */
    public C0255h f1468k = null;

    /* renamed from: l */
    public C0339b f1469l = null;

    public C0220k0(C0265r c0265r) {
        this.f1467j = c0265r;
    }

    @Override // androidx.lifecycle.InterfaceC0254g
    /* renamed from: a */
    public final AbstractC0251d mo86a() {
        m694e();
        return this.f1468k;
    }

    @Override // androidx.savedstate.InterfaceC0340c
    /* renamed from: c */
    public final C0338a mo88c() {
        m694e();
        return this.f1469l.f2181b;
    }

    /* renamed from: d */
    public final void m693d(AbstractC0251d.b bVar) {
        this.f1468k.m879e(bVar);
    }

    /* renamed from: e */
    public final void m694e() {
        if (this.f1468k == null) {
            this.f1468k = new C0255h(this);
            this.f1469l = new C0339b(this);
        }
    }

    @Override // androidx.lifecycle.InterfaceC0266s
    /* renamed from: g */
    public final C0265r mo90g() {
        m694e();
        return this.f1467j;
    }
}
