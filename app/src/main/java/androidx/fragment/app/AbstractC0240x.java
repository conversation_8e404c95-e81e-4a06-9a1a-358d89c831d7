package androidx.fragment.app;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.Looper;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.Log;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import androidx.activity.AbstractC0050b;
import androidx.activity.InterfaceC0049a;
import androidx.activity.OnBackPressedDispatcher;
import androidx.activity.result.AbstractC0055d;
import androidx.activity.result.C0052a;
import androidx.activity.result.C0053b;
import androidx.activity.result.InterfaceC0054c;
import androidx.appcompat.widget.C0174y;
import androidx.fragment.app.AbstractC0210f0;
import androidx.fragment.app.ComponentCallbacksC0223m;
import androidx.lifecycle.AbstractC0251d;
import com.liaoyuan.aicast.R;
import java.io.FileDescriptor;
import java.io.PrintWriter;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;
import p001a0.C0002a;
import p007b.AbstractC0370a;
import p111q0.AbstractC1253a;

/* renamed from: androidx.fragment.app.x */
/* loaded from: classes.dex */
public abstract class AbstractC0240x {

    /* renamed from: A */
    public boolean f1602A;

    /* renamed from: B */
    public boolean f1603B;

    /* renamed from: C */
    public boolean f1604C;

    /* renamed from: D */
    public boolean f1605D;

    /* renamed from: E */
    public ArrayList<C0199a> f1606E;

    /* renamed from: F */
    public ArrayList<Boolean> f1607F;

    /* renamed from: G */
    public ArrayList<ComponentCallbacksC0223m> f1608G;

    /* renamed from: H */
    public C0200a0 f1609H;

    /* renamed from: I */
    public g f1610I;

    /* renamed from: b */
    public boolean f1612b;

    /* renamed from: d */
    public ArrayList<C0199a> f1614d;

    /* renamed from: e */
    public ArrayList<ComponentCallbacksC0223m> f1615e;

    /* renamed from: g */
    public OnBackPressedDispatcher f1617g;

    /* renamed from: k */
    public Map<ComponentCallbacksC0223m, HashSet<C0002a>> f1621k;

    /* renamed from: l */
    public final d f1622l;

    /* renamed from: m */
    public final C0239w f1623m;

    /* renamed from: n */
    public final CopyOnWriteArrayList<InterfaceC0202b0> f1624n;

    /* renamed from: o */
    public int f1625o;

    /* renamed from: p */
    public AbstractC0237u<?> f1626p;

    /* renamed from: q */
    public AbstractC0055d f1627q;

    /* renamed from: r */
    public ComponentCallbacksC0223m f1628r;

    /* renamed from: s */
    public ComponentCallbacksC0223m f1629s;

    /* renamed from: t */
    public e f1630t;

    /* renamed from: u */
    public f f1631u;

    /* renamed from: v */
    public AbstractC0055d f1632v;

    /* renamed from: w */
    public AbstractC0055d f1633w;

    /* renamed from: x */
    public AbstractC0055d f1634x;

    /* renamed from: y */
    public ArrayDeque<l> f1635y;

    /* renamed from: z */
    public boolean f1636z;

    /* renamed from: a */
    public final ArrayList<m> f1611a = new ArrayList<>();

    /* renamed from: c */
    public final C0208e0 f1613c = new C0208e0();

    /* renamed from: f */
    public final LayoutInflaterFactory2C0238v f1616f = new LayoutInflaterFactory2C0238v(this);

    /* renamed from: h */
    public final c f1618h = new c();

    /* renamed from: i */
    public final AtomicInteger f1619i = new AtomicInteger();

    /* renamed from: j */
    public final Map<String, Bundle> f1620j = Collections.synchronizedMap(new HashMap());

    /* renamed from: androidx.fragment.app.x$a */
    public class a implements InterfaceC0054c<C0053b> {
        public a() {
        }

        @Override // androidx.activity.result.InterfaceC0054c
        /* renamed from: a */
        public final void mo113a(C0053b c0053b) {
            StringBuilder m492j;
            C0053b c0053b2 = c0053b;
            l pollFirst = AbstractC0240x.this.f1635y.pollFirst();
            if (pollFirst == null) {
                m492j = new StringBuilder();
                m492j.append("No IntentSenders were started for ");
                m492j.append(this);
            } else {
                String str = pollFirst.f1645j;
                int i6 = pollFirst.f1646k;
                ComponentCallbacksC0223m m659e = AbstractC0240x.this.f1613c.m659e(str);
                if (m659e != null) {
                    m659e.m747y(i6, c0053b2.f209j, c0053b2.f210k);
                    return;
                }
                m492j = C0174y.m492j("Intent Sender result delivered for unknown Fragment ", str);
            }
            Log.w("FragmentManager", m492j.toString());
        }
    }

    /* renamed from: androidx.fragment.app.x$b */
    public class b implements InterfaceC0054c<Map<String, Boolean>> {
        public b() {
        }

        @Override // androidx.activity.result.InterfaceC0054c
        @SuppressLint({"SyntheticAccessor"})
        /* renamed from: a */
        public final void mo113a(Map<String, Boolean> map) {
            String m103g;
            Map<String, Boolean> map2 = map;
            ArrayList arrayList = new ArrayList(map2.values());
            int[] iArr = new int[arrayList.size()];
            for (int i6 = 0; i6 < arrayList.size(); i6++) {
                iArr[i6] = ((Boolean) arrayList.get(i6)).booleanValue() ? 0 : -1;
            }
            l pollFirst = AbstractC0240x.this.f1635y.pollFirst();
            if (pollFirst == null) {
                m103g = "No permissions were requested for " + this;
            } else {
                String str = pollFirst.f1645j;
                if (AbstractC0240x.this.f1613c.m659e(str) != null) {
                    return;
                } else {
                    m103g = C0052a.m103g("Permission request result delivered for unknown Fragment ", str);
                }
            }
            Log.w("FragmentManager", m103g);
        }
    }

    /* renamed from: androidx.fragment.app.x$c */
    public class c extends AbstractC0050b {
        public c() {
        }

        @Override // androidx.activity.AbstractC0050b
        /* renamed from: a */
        public final void mo96a() {
            AbstractC0240x abstractC0240x = AbstractC0240x.this;
            abstractC0240x.m848z(true);
            if (abstractC0240x.f1618h.f207a) {
                abstractC0240x.m808R();
            } else {
                abstractC0240x.f1617g.m95b();
            }
        }
    }

    /* renamed from: androidx.fragment.app.x$d */
    public class d {
        public d() {
        }
    }

    /* renamed from: androidx.fragment.app.x$e */
    public class e extends C0236t {
        public e() {
        }

        @Override // androidx.fragment.app.C0236t
        /* renamed from: a */
        public final ComponentCallbacksC0223m mo776a(ClassLoader classLoader, String str) {
            Context context = AbstractC0240x.this.f1626p.f1592k;
            Object obj = ComponentCallbacksC0223m.f1493Z;
            try {
                return C0236t.m775c(context.getClassLoader(), str).getConstructor(new Class[0]).newInstance(new Object[0]);
            } catch (IllegalAccessException e6) {
                throw new ComponentCallbacksC0223m.c(C0174y.m491i("Unable to instantiate fragment ", str, ": make sure class name exists, is public, and has an empty constructor that is public"), e6);
            } catch (InstantiationException e7) {
                throw new ComponentCallbacksC0223m.c(C0174y.m491i("Unable to instantiate fragment ", str, ": make sure class name exists, is public, and has an empty constructor that is public"), e7);
            } catch (NoSuchMethodException e8) {
                throw new ComponentCallbacksC0223m.c(C0174y.m491i("Unable to instantiate fragment ", str, ": could not find Fragment constructor"), e8);
            } catch (InvocationTargetException e9) {
                throw new ComponentCallbacksC0223m.c(C0174y.m491i("Unable to instantiate fragment ", str, ": calling Fragment constructor caused an exception"), e9);
            }
        }
    }

    /* renamed from: androidx.fragment.app.x$f */
    public class f implements InterfaceC0232q0 {
    }

    /* renamed from: androidx.fragment.app.x$g */
    public class g implements Runnable {
        public g() {
        }

        @Override // java.lang.Runnable
        public final void run() {
            AbstractC0240x.this.m848z(true);
        }
    }

    /* renamed from: androidx.fragment.app.x$h */
    public class h implements InterfaceC0202b0 {

        /* renamed from: j */
        public final /* synthetic */ ComponentCallbacksC0223m f1643j;

        public h(ComponentCallbacksC0223m componentCallbacksC0223m) {
            this.f1643j = componentCallbacksC0223m;
        }

        @Override // androidx.fragment.app.InterfaceC0202b0
        /* renamed from: d */
        public final void mo628d() {
            Objects.requireNonNull(this.f1643j);
        }
    }

    /* renamed from: androidx.fragment.app.x$i */
    public class i implements InterfaceC0054c<C0053b> {
        public i() {
        }

        @Override // androidx.activity.result.InterfaceC0054c
        /* renamed from: a */
        public final void mo113a(C0053b c0053b) {
            StringBuilder m492j;
            C0053b c0053b2 = c0053b;
            l pollFirst = AbstractC0240x.this.f1635y.pollFirst();
            if (pollFirst == null) {
                m492j = new StringBuilder();
                m492j.append("No Activities were started for result for ");
                m492j.append(this);
            } else {
                String str = pollFirst.f1645j;
                int i6 = pollFirst.f1646k;
                ComponentCallbacksC0223m m659e = AbstractC0240x.this.f1613c.m659e(str);
                if (m659e != null) {
                    m659e.m747y(i6, c0053b2.f209j, c0053b2.f210k);
                    return;
                }
                m492j = C0174y.m492j("Activity result delivered for unknown Fragment ", str);
            }
            Log.w("FragmentManager", m492j.toString());
        }
    }

    /* renamed from: androidx.fragment.app.x$j */
    public static class j extends AbstractC0370a<Object, C0053b> {
        @Override // p007b.AbstractC0370a
        /* renamed from: a */
        public final C0053b mo849a(int i6, Intent intent) {
            return new C0053b(i6, intent);
        }
    }

    /* renamed from: androidx.fragment.app.x$k */
    public static abstract class k {
    }

    @SuppressLint({"BanParcelableUsage"})
    /* renamed from: androidx.fragment.app.x$l */
    public static class l implements Parcelable {
        public static final Parcelable.Creator<l> CREATOR = new a();

        /* renamed from: j */
        public String f1645j;

        /* renamed from: k */
        public int f1646k;

        /* renamed from: androidx.fragment.app.x$l$a */
        public class a implements Parcelable.Creator<l> {
            @Override // android.os.Parcelable.Creator
            public final l createFromParcel(Parcel parcel) {
                return new l(parcel);
            }

            @Override // android.os.Parcelable.Creator
            public final l[] newArray(int i6) {
                return new l[i6];
            }
        }

        public l(Parcel parcel) {
            this.f1645j = parcel.readString();
            this.f1646k = parcel.readInt();
        }

        @Override // android.os.Parcelable
        public final int describeContents() {
            return 0;
        }

        @Override // android.os.Parcelable
        public final void writeToParcel(Parcel parcel, int i6) {
            parcel.writeString(this.f1645j);
            parcel.writeInt(this.f1646k);
        }
    }

    /* renamed from: androidx.fragment.app.x$m */
    public interface m {
        /* renamed from: a */
        boolean mo615a(ArrayList<C0199a> arrayList, ArrayList<Boolean> arrayList2);
    }

    /* renamed from: androidx.fragment.app.x$n */
    public class n implements m {

        /* renamed from: a */
        public final int f1647a;

        /* renamed from: b */
        public final int f1648b = 1;

        public n(int i6) {
            this.f1647a = i6;
        }

        @Override // androidx.fragment.app.AbstractC0240x.m
        /* renamed from: a */
        public final boolean mo615a(ArrayList<C0199a> arrayList, ArrayList<Boolean> arrayList2) {
            ComponentCallbacksC0223m componentCallbacksC0223m = AbstractC0240x.this.f1629s;
            if (componentCallbacksC0223m == null || this.f1647a >= 0 || !componentCallbacksC0223m.m731i().m808R()) {
                return AbstractC0240x.this.m809S(arrayList, arrayList2, this.f1647a, this.f1648b);
            }
            return false;
        }
    }

    public AbstractC0240x() {
        Collections.synchronizedMap(new HashMap());
        this.f1621k = Collections.synchronizedMap(new HashMap());
        this.f1622l = new d();
        this.f1623m = new C0239w(this);
        this.f1624n = new CopyOnWriteArrayList<>();
        this.f1625o = -1;
        this.f1630t = new e();
        this.f1631u = new f();
        this.f1635y = new ArrayDeque<>();
        this.f1610I = new g();
    }

    /* renamed from: K */
    public static boolean m791K(int i6) {
        return Log.isLoggable("FragmentManager", i6);
    }

    /* renamed from: A */
    public final void m792A(m mVar, boolean z5) {
        if (z5 && (this.f1626p == null || this.f1604C)) {
            return;
        }
        m847y(z5);
        if (mVar.mo615a(this.f1606E, this.f1607F)) {
            this.f1612b = true;
            try {
                m811U(this.f1606E, this.f1607F);
            } finally {
                m823d();
            }
        }
        m828f0();
        m843u();
        this.f1613c.m656b();
    }

    /* renamed from: B */
    public final void m793B(ArrayList<C0199a> arrayList, ArrayList<Boolean> arrayList2, int i6, int i7) {
        ViewGroup viewGroup;
        int i8;
        int i9;
        ArrayList<Boolean> arrayList3 = arrayList2;
        boolean z5 = arrayList.get(i6).f1426o;
        ArrayList<ComponentCallbacksC0223m> arrayList4 = this.f1608G;
        if (arrayList4 == null) {
            this.f1608G = new ArrayList<>();
        } else {
            arrayList4.clear();
        }
        this.f1608G.addAll(this.f1613c.m664j());
        ComponentCallbacksC0223m componentCallbacksC0223m = this.f1629s;
        int i10 = i6;
        boolean z6 = false;
        while (true) {
            int i11 = 1;
            if (i10 >= i7) {
                this.f1608G.clear();
                if (!z5 && this.f1625o >= 1) {
                    for (int i12 = i6; i12 < i7; i12++) {
                        Iterator<AbstractC0210f0.a> it = arrayList.get(i12).f1412a.iterator();
                        while (it.hasNext()) {
                            ComponentCallbacksC0223m componentCallbacksC0223m2 = it.next().f1428b;
                            if (componentCallbacksC0223m2 != null && componentCallbacksC0223m2.f1494A != null) {
                                this.f1613c.m665k(m827f(componentCallbacksC0223m2));
                            }
                        }
                    }
                }
                for (int i13 = i6; i13 < i7; i13++) {
                    C0199a c0199a = arrayList.get(i13);
                    if (arrayList2.get(i13).booleanValue()) {
                        c0199a.m616c(-1);
                        c0199a.m622i();
                    } else {
                        c0199a.m616c(1);
                        c0199a.m621h();
                    }
                }
                boolean booleanValue = arrayList2.get(i7 - 1).booleanValue();
                for (int i14 = i6; i14 < i7; i14++) {
                    C0199a c0199a2 = arrayList.get(i14);
                    if (booleanValue) {
                        for (int size = c0199a2.f1412a.size() - 1; size >= 0; size--) {
                            ComponentCallbacksC0223m componentCallbacksC0223m3 = c0199a2.f1412a.get(size).f1428b;
                            if (componentCallbacksC0223m3 != null) {
                                m827f(componentCallbacksC0223m3).m647k();
                            }
                        }
                    } else {
                        Iterator<AbstractC0210f0.a> it2 = c0199a2.f1412a.iterator();
                        while (it2.hasNext()) {
                            ComponentCallbacksC0223m componentCallbacksC0223m4 = it2.next().f1428b;
                            if (componentCallbacksC0223m4 != null) {
                                m827f(componentCallbacksC0223m4).m647k();
                            }
                        }
                    }
                }
                m806P(this.f1625o, true);
                HashSet hashSet = new HashSet();
                for (int i15 = i6; i15 < i7; i15++) {
                    Iterator<AbstractC0210f0.a> it3 = arrayList.get(i15).f1412a.iterator();
                    while (it3.hasNext()) {
                        ComponentCallbacksC0223m componentCallbacksC0223m5 = it3.next().f1428b;
                        if (componentCallbacksC0223m5 != null && (viewGroup = componentCallbacksC0223m5.f1506M) != null) {
                            hashSet.add(AbstractC0228o0.m751g(viewGroup, m800I()));
                        }
                    }
                }
                Iterator it4 = hashSet.iterator();
                while (it4.hasNext()) {
                    AbstractC0228o0 abstractC0228o0 = (AbstractC0228o0) it4.next();
                    abstractC0228o0.f1561d = booleanValue;
                    abstractC0228o0.m756h();
                    abstractC0228o0.m753c();
                }
                for (int i16 = i6; i16 < i7; i16++) {
                    C0199a c0199a3 = arrayList.get(i16);
                    if (arrayList2.get(i16).booleanValue() && c0199a3.f1347r >= 0) {
                        c0199a3.f1347r = -1;
                    }
                    Objects.requireNonNull(c0199a3);
                }
                return;
            }
            C0199a c0199a4 = arrayList.get(i10);
            int i17 = 3;
            if (arrayList3.get(i10).booleanValue()) {
                int i18 = 1;
                ArrayList<ComponentCallbacksC0223m> arrayList5 = this.f1608G;
                int size2 = c0199a4.f1412a.size() - 1;
                while (size2 >= 0) {
                    AbstractC0210f0.a aVar = c0199a4.f1412a.get(size2);
                    int i19 = aVar.f1427a;
                    if (i19 != i18) {
                        if (i19 != 3) {
                            switch (i19) {
                                case 8:
                                    componentCallbacksC0223m = null;
                                    break;
                                case 9:
                                    componentCallbacksC0223m = aVar.f1428b;
                                    break;
                                case 10:
                                    aVar.f1434h = aVar.f1433g;
                                    break;
                            }
                            size2--;
                            i18 = 1;
                        }
                        arrayList5.add(aVar.f1428b);
                        size2--;
                        i18 = 1;
                    }
                    arrayList5.remove(aVar.f1428b);
                    size2--;
                    i18 = 1;
                }
            } else {
                ArrayList<ComponentCallbacksC0223m> arrayList6 = this.f1608G;
                int i20 = 0;
                while (i20 < c0199a4.f1412a.size()) {
                    AbstractC0210f0.a aVar2 = c0199a4.f1412a.get(i20);
                    int i21 = aVar2.f1427a;
                    if (i21 == i11) {
                        i8 = i11;
                    } else if (i21 != 2) {
                        if (i21 == i17 || i21 == 6) {
                            arrayList6.remove(aVar2.f1428b);
                            ComponentCallbacksC0223m componentCallbacksC0223m6 = aVar2.f1428b;
                            if (componentCallbacksC0223m6 == componentCallbacksC0223m) {
                                c0199a4.f1412a.add(i20, new AbstractC0210f0.a(9, componentCallbacksC0223m6));
                                i20++;
                                i8 = 1;
                                componentCallbacksC0223m = null;
                                i20 += i8;
                                i11 = i8;
                                i17 = 3;
                            }
                        } else if (i21 == 7) {
                            i8 = 1;
                        } else if (i21 == 8) {
                            c0199a4.f1412a.add(i20, new AbstractC0210f0.a(9, componentCallbacksC0223m));
                            i20++;
                            componentCallbacksC0223m = aVar2.f1428b;
                        }
                        i8 = 1;
                        i20 += i8;
                        i11 = i8;
                        i17 = 3;
                    } else {
                        ComponentCallbacksC0223m componentCallbacksC0223m7 = aVar2.f1428b;
                        int i22 = componentCallbacksC0223m7.f1499F;
                        int size3 = arrayList6.size() - 1;
                        boolean z7 = false;
                        while (size3 >= 0) {
                            ComponentCallbacksC0223m componentCallbacksC0223m8 = arrayList6.get(size3);
                            if (componentCallbacksC0223m8.f1499F != i22) {
                                i9 = i22;
                            } else if (componentCallbacksC0223m8 == componentCallbacksC0223m7) {
                                i9 = i22;
                                z7 = true;
                            } else {
                                if (componentCallbacksC0223m8 == componentCallbacksC0223m) {
                                    i9 = i22;
                                    c0199a4.f1412a.add(i20, new AbstractC0210f0.a(9, componentCallbacksC0223m8));
                                    i20++;
                                    componentCallbacksC0223m = null;
                                } else {
                                    i9 = i22;
                                }
                                AbstractC0210f0.a aVar3 = new AbstractC0210f0.a(3, componentCallbacksC0223m8);
                                aVar3.f1429c = aVar2.f1429c;
                                aVar3.f1431e = aVar2.f1431e;
                                aVar3.f1430d = aVar2.f1430d;
                                aVar3.f1432f = aVar2.f1432f;
                                c0199a4.f1412a.add(i20, aVar3);
                                arrayList6.remove(componentCallbacksC0223m8);
                                i20++;
                            }
                            size3--;
                            i22 = i9;
                        }
                        if (z7) {
                            c0199a4.f1412a.remove(i20);
                            i20--;
                            i8 = 1;
                            i20 += i8;
                            i11 = i8;
                            i17 = 3;
                        } else {
                            i8 = 1;
                            aVar2.f1427a = 1;
                            arrayList6.add(componentCallbacksC0223m7);
                            i20 += i8;
                            i11 = i8;
                            i17 = 3;
                        }
                    }
                    arrayList6.add(aVar2.f1428b);
                    i20 += i8;
                    i11 = i8;
                    i17 = 3;
                }
            }
            z6 = z6 || c0199a4.f1418g;
            i10++;
            arrayList3 = arrayList2;
        }
    }

    /* renamed from: C */
    public final void m794C(ArrayList<C0199a> arrayList, ArrayList<Boolean> arrayList2) {
    }

    /* renamed from: D */
    public final ComponentCallbacksC0223m m795D(String str) {
        return this.f1613c.m658d(str);
    }

    /* renamed from: E */
    public final ComponentCallbacksC0223m m796E(int i6) {
        C0208e0 c0208e0 = this.f1613c;
        int size = ((ArrayList) c0208e0.f1405j).size();
        while (true) {
            size--;
            if (size < 0) {
                for (C0206d0 c0206d0 : ((HashMap) c0208e0.f1406k).values()) {
                    if (c0206d0 != null) {
                        ComponentCallbacksC0223m componentCallbacksC0223m = c0206d0.f1400c;
                        if (componentCallbacksC0223m.f1498E == i6) {
                            return componentCallbacksC0223m;
                        }
                    }
                }
                return null;
            }
            ComponentCallbacksC0223m componentCallbacksC0223m2 = (ComponentCallbacksC0223m) ((ArrayList) c0208e0.f1405j).get(size);
            if (componentCallbacksC0223m2 != null && componentCallbacksC0223m2.f1498E == i6) {
                return componentCallbacksC0223m2;
            }
        }
    }

    /* renamed from: F */
    public final ComponentCallbacksC0223m m797F(String str) {
        C0208e0 c0208e0 = this.f1613c;
        Objects.requireNonNull(c0208e0);
        int size = ((ArrayList) c0208e0.f1405j).size();
        while (true) {
            size--;
            if (size < 0) {
                for (C0206d0 c0206d0 : ((HashMap) c0208e0.f1406k).values()) {
                    if (c0206d0 != null) {
                        ComponentCallbacksC0223m componentCallbacksC0223m = c0206d0.f1400c;
                        if (str.equals(componentCallbacksC0223m.f1500G)) {
                            return componentCallbacksC0223m;
                        }
                    }
                }
                return null;
            }
            ComponentCallbacksC0223m componentCallbacksC0223m2 = (ComponentCallbacksC0223m) ((ArrayList) c0208e0.f1405j).get(size);
            if (componentCallbacksC0223m2 != null && str.equals(componentCallbacksC0223m2.f1500G)) {
                return componentCallbacksC0223m2;
            }
        }
    }

    /* renamed from: G */
    public final ViewGroup m798G(ComponentCallbacksC0223m componentCallbacksC0223m) {
        ViewGroup viewGroup = componentCallbacksC0223m.f1506M;
        if (viewGroup != null) {
            return viewGroup;
        }
        if (componentCallbacksC0223m.f1499F > 0 && this.f1627q.mo121n()) {
            View mo118k = this.f1627q.mo118k(componentCallbacksC0223m.f1499F);
            if (mo118k instanceof ViewGroup) {
                return (ViewGroup) mo118k;
            }
        }
        return null;
    }

    /* renamed from: H */
    public final C0236t m799H() {
        ComponentCallbacksC0223m componentCallbacksC0223m = this.f1628r;
        return componentCallbacksC0223m != null ? componentCallbacksC0223m.f1494A.m799H() : this.f1630t;
    }

    /* renamed from: I */
    public final InterfaceC0232q0 m800I() {
        ComponentCallbacksC0223m componentCallbacksC0223m = this.f1628r;
        return componentCallbacksC0223m != null ? componentCallbacksC0223m.f1494A.m800I() : this.f1631u;
    }

    /* renamed from: J */
    public final void m801J(ComponentCallbacksC0223m componentCallbacksC0223m) {
        if (m791K(2)) {
            Log.v("FragmentManager", "hide: " + componentCallbacksC0223m);
        }
        if (componentCallbacksC0223m.f1501H) {
            return;
        }
        componentCallbacksC0223m.f1501H = true;
        componentCallbacksC0223m.f1511R = true ^ componentCallbacksC0223m.f1511R;
        m820b0(componentCallbacksC0223m);
    }

    /* renamed from: L */
    public final boolean m802L(ComponentCallbacksC0223m componentCallbacksC0223m) {
        C0241y c0241y = componentCallbacksC0223m.f1496C;
        Iterator it = ((ArrayList) c0241y.f1613c.m661g()).iterator();
        boolean z5 = false;
        while (it.hasNext()) {
            ComponentCallbacksC0223m componentCallbacksC0223m2 = (ComponentCallbacksC0223m) it.next();
            if (componentCallbacksC0223m2 != null) {
                z5 = c0241y.m802L(componentCallbacksC0223m2);
            }
            if (z5) {
                return true;
            }
        }
        return false;
    }

    /* renamed from: M */
    public final boolean m803M(ComponentCallbacksC0223m componentCallbacksC0223m) {
        AbstractC0240x abstractC0240x;
        if (componentCallbacksC0223m == null) {
            return true;
        }
        return componentCallbacksC0223m.f1504K && ((abstractC0240x = componentCallbacksC0223m.f1494A) == null || abstractC0240x.m803M(componentCallbacksC0223m.f1497D));
    }

    /* renamed from: N */
    public final boolean m804N(ComponentCallbacksC0223m componentCallbacksC0223m) {
        if (componentCallbacksC0223m == null) {
            return true;
        }
        AbstractC0240x abstractC0240x = componentCallbacksC0223m.f1494A;
        return componentCallbacksC0223m.equals(abstractC0240x.f1629s) && m804N(abstractC0240x.f1628r);
    }

    /* renamed from: O */
    public final boolean m805O() {
        return this.f1602A || this.f1603B;
    }

    /* renamed from: P */
    public final void m806P(int i6, boolean z5) {
        AbstractC0237u<?> abstractC0237u;
        if (this.f1626p == null && i6 != -1) {
            throw new IllegalStateException("No activity");
        }
        if (z5 || i6 != this.f1625o) {
            this.f1625o = i6;
            C0208e0 c0208e0 = this.f1613c;
            Iterator it = ((ArrayList) c0208e0.f1405j).iterator();
            while (it.hasNext()) {
                C0206d0 c0206d0 = (C0206d0) ((HashMap) c0208e0.f1406k).get(((ComponentCallbacksC0223m) it.next()).f1523n);
                if (c0206d0 != null) {
                    c0206d0.m647k();
                }
            }
            Iterator it2 = ((HashMap) c0208e0.f1406k).values().iterator();
            while (true) {
                boolean z6 = false;
                if (!it2.hasNext()) {
                    break;
                }
                C0206d0 c0206d02 = (C0206d0) it2.next();
                if (c0206d02 != null) {
                    c0206d02.m647k();
                    ComponentCallbacksC0223m componentCallbacksC0223m = c0206d02.f1400c;
                    if (componentCallbacksC0223m.f1530u && !componentCallbacksC0223m.m746x()) {
                        z6 = true;
                    }
                    if (z6) {
                        c0208e0.m666l(c0206d02);
                    }
                }
            }
            m824d0();
            if (this.f1636z && (abstractC0237u = this.f1626p) != null && this.f1625o == 7) {
                abstractC0237u.mo770u();
                this.f1636z = false;
            }
        }
    }

    /* renamed from: Q */
    public final void m807Q() {
        if (this.f1626p == null) {
            return;
        }
        this.f1602A = false;
        this.f1603B = false;
        this.f1609H.f1354g = false;
        for (ComponentCallbacksC0223m componentCallbacksC0223m : this.f1613c.m664j()) {
            if (componentCallbacksC0223m != null) {
                componentCallbacksC0223m.f1496C.m807Q();
            }
        }
    }

    /* renamed from: R */
    public final boolean m808R() {
        m848z(false);
        m847y(true);
        ComponentCallbacksC0223m componentCallbacksC0223m = this.f1629s;
        if (componentCallbacksC0223m != null && componentCallbacksC0223m.m731i().m808R()) {
            return true;
        }
        boolean m809S = m809S(this.f1606E, this.f1607F, -1, 0);
        if (m809S) {
            this.f1612b = true;
            try {
                m811U(this.f1606E, this.f1607F);
            } finally {
                m823d();
            }
        }
        m828f0();
        m843u();
        this.f1613c.m656b();
        return m809S;
    }

    /* renamed from: S */
    public final boolean m809S(ArrayList arrayList, ArrayList arrayList2, int i6, int i7) {
        C0199a c0199a;
        ArrayList<C0199a> arrayList3 = this.f1614d;
        if (arrayList3 == null) {
            return false;
        }
        if (i6 >= 0 || (i7 & 1) != 0) {
            int i8 = -1;
            if (i6 >= 0) {
                int size = arrayList3.size() - 1;
                while (size >= 0) {
                    C0199a c0199a2 = this.f1614d.get(size);
                    if (i6 >= 0 && i6 == c0199a2.f1347r) {
                        break;
                    }
                    size--;
                }
                if (size < 0) {
                    return false;
                }
                if ((i7 & 1) != 0) {
                    do {
                        size--;
                        if (size < 0) {
                            break;
                        }
                        c0199a = this.f1614d.get(size);
                        if (i6 < 0) {
                            break;
                        }
                    } while (i6 == c0199a.f1347r);
                }
                i8 = size;
            }
            if (i8 == this.f1614d.size() - 1) {
                return false;
            }
            for (int size2 = this.f1614d.size() - 1; size2 > i8; size2--) {
                arrayList.add(this.f1614d.remove(size2));
                arrayList2.add(Boolean.TRUE);
            }
        } else {
            int size3 = arrayList3.size() - 1;
            if (size3 < 0) {
                return false;
            }
            arrayList.add(this.f1614d.remove(size3));
            arrayList2.add(Boolean.TRUE);
        }
        return true;
    }

    /* renamed from: T */
    public final void m810T(ComponentCallbacksC0223m componentCallbacksC0223m) {
        if (m791K(2)) {
            Log.v("FragmentManager", "remove: " + componentCallbacksC0223m + " nesting=" + componentCallbacksC0223m.f1535z);
        }
        boolean z5 = !componentCallbacksC0223m.m746x();
        if (!componentCallbacksC0223m.f1502I || z5) {
            C0208e0 c0208e0 = this.f1613c;
            synchronized (((ArrayList) c0208e0.f1405j)) {
                ((ArrayList) c0208e0.f1405j).remove(componentCallbacksC0223m);
            }
            componentCallbacksC0223m.f1529t = false;
            if (m802L(componentCallbacksC0223m)) {
                this.f1636z = true;
            }
            componentCallbacksC0223m.f1530u = true;
            m820b0(componentCallbacksC0223m);
        }
    }

    /* renamed from: U */
    public final void m811U(ArrayList<C0199a> arrayList, ArrayList<Boolean> arrayList2) {
        if (arrayList.isEmpty()) {
            return;
        }
        if (arrayList.size() != arrayList2.size()) {
            throw new IllegalStateException("Internal error with the back stack records");
        }
        m794C(arrayList, arrayList2);
        int size = arrayList.size();
        int i6 = 0;
        int i7 = 0;
        while (i6 < size) {
            if (!arrayList.get(i6).f1426o) {
                if (i7 != i6) {
                    m793B(arrayList, arrayList2, i7, i6);
                }
                i7 = i6 + 1;
                if (arrayList2.get(i6).booleanValue()) {
                    while (i7 < size && arrayList2.get(i7).booleanValue() && !arrayList.get(i7).f1426o) {
                        i7++;
                    }
                }
                m793B(arrayList, arrayList2, i6, i7);
                i6 = i7 - 1;
            }
            i6++;
        }
        if (i7 != size) {
            m793B(arrayList, arrayList2, i7, size);
        }
    }

    /* renamed from: V */
    public final void m812V(Parcelable parcelable) {
        C0206d0 c0206d0;
        if (parcelable == null) {
            return;
        }
        C0242z c0242z = (C0242z) parcelable;
        if (c0242z.f1650j == null) {
            return;
        }
        ((HashMap) this.f1613c.f1406k).clear();
        Iterator<C0204c0> it = c0242z.f1650j.iterator();
        while (it.hasNext()) {
            C0204c0 next = it.next();
            if (next != null) {
                ComponentCallbacksC0223m componentCallbacksC0223m = this.f1609H.f1349b.get(next.f1381k);
                if (componentCallbacksC0223m != null) {
                    if (m791K(2)) {
                        Log.v("FragmentManager", "restoreSaveState: re-attaching retained " + componentCallbacksC0223m);
                    }
                    c0206d0 = new C0206d0(this.f1623m, this.f1613c, componentCallbacksC0223m, next);
                } else {
                    c0206d0 = new C0206d0(this.f1623m, this.f1613c, this.f1626p.f1592k.getClassLoader(), m799H(), next);
                }
                ComponentCallbacksC0223m componentCallbacksC0223m2 = c0206d0.f1400c;
                componentCallbacksC0223m2.f1494A = this;
                if (m791K(2)) {
                    StringBuilder m104h = C0052a.m104h("restoreSaveState: active (");
                    m104h.append(componentCallbacksC0223m2.f1523n);
                    m104h.append("): ");
                    m104h.append(componentCallbacksC0223m2);
                    Log.v("FragmentManager", m104h.toString());
                }
                c0206d0.m649m(this.f1626p.f1592k.getClassLoader());
                this.f1613c.m665k(c0206d0);
                c0206d0.f1402e = this.f1625o;
            }
        }
        C0200a0 c0200a0 = this.f1609H;
        Objects.requireNonNull(c0200a0);
        Iterator it2 = new ArrayList(c0200a0.f1349b.values()).iterator();
        while (it2.hasNext()) {
            ComponentCallbacksC0223m componentCallbacksC0223m3 = (ComponentCallbacksC0223m) it2.next();
            if (!this.f1613c.m657c(componentCallbacksC0223m3.f1523n)) {
                if (m791K(2)) {
                    Log.v("FragmentManager", "Discarding retained Fragment " + componentCallbacksC0223m3 + " that was not found in the set of active Fragments " + c0242z.f1650j);
                }
                this.f1609H.m626b(componentCallbacksC0223m3);
                componentCallbacksC0223m3.f1494A = this;
                C0206d0 c0206d02 = new C0206d0(this.f1623m, this.f1613c, componentCallbacksC0223m3);
                c0206d02.f1402e = 1;
                c0206d02.m647k();
                componentCallbacksC0223m3.f1530u = true;
                c0206d02.m647k();
            }
        }
        C0208e0 c0208e0 = this.f1613c;
        ArrayList<String> arrayList = c0242z.f1651k;
        ((ArrayList) c0208e0.f1405j).clear();
        if (arrayList != null) {
            for (String str : arrayList) {
                ComponentCallbacksC0223m m658d = c0208e0.m658d(str);
                if (m658d == null) {
                    throw new IllegalStateException(C0174y.m491i("No instantiated fragment for (", str, ")"));
                }
                if (m791K(2)) {
                    Log.v("FragmentManager", "restoreSaveState: added (" + str + "): " + m658d);
                }
                c0208e0.m655a(m658d);
            }
        }
        if (c0242z.f1652l != null) {
            this.f1614d = new ArrayList<>(c0242z.f1652l.length);
            int i6 = 0;
            while (true) {
                C0201b[] c0201bArr = c0242z.f1652l;
                if (i6 >= c0201bArr.length) {
                    break;
                }
                C0201b c0201b = c0201bArr[i6];
                Objects.requireNonNull(c0201b);
                C0199a c0199a = new C0199a(this);
                int i7 = 0;
                int i8 = 0;
                while (true) {
                    int[] iArr = c0201b.f1355j;
                    if (i7 >= iArr.length) {
                        break;
                    }
                    AbstractC0210f0.a aVar = new AbstractC0210f0.a();
                    int i9 = i7 + 1;
                    aVar.f1427a = iArr[i7];
                    if (m791K(2)) {
                        Log.v("FragmentManager", "Instantiate " + c0199a + " op #" + i8 + " base fragment #" + c0201b.f1355j[i9]);
                    }
                    String str2 = c0201b.f1356k.get(i8);
                    aVar.f1428b = str2 != null ? m795D(str2) : null;
                    aVar.f1433g = AbstractC0251d.c.values()[c0201b.f1357l[i8]];
                    aVar.f1434h = AbstractC0251d.c.values()[c0201b.f1358m[i8]];
                    int[] iArr2 = c0201b.f1355j;
                    int i10 = i9 + 1;
                    int i11 = iArr2[i9];
                    aVar.f1429c = i11;
                    int i12 = i10 + 1;
                    int i13 = iArr2[i10];
                    aVar.f1430d = i13;
                    int i14 = i12 + 1;
                    int i15 = iArr2[i12];
                    aVar.f1431e = i15;
                    int i16 = iArr2[i14];
                    aVar.f1432f = i16;
                    c0199a.f1413b = i11;
                    c0199a.f1414c = i13;
                    c0199a.f1415d = i15;
                    c0199a.f1416e = i16;
                    c0199a.m667b(aVar);
                    i8++;
                    i7 = i14 + 1;
                }
                c0199a.f1417f = c0201b.f1359n;
                c0199a.f1419h = c0201b.f1360o;
                c0199a.f1347r = c0201b.f1361p;
                c0199a.f1418g = true;
                c0199a.f1420i = c0201b.f1362q;
                c0199a.f1421j = c0201b.f1363r;
                c0199a.f1422k = c0201b.f1364s;
                c0199a.f1423l = c0201b.f1365t;
                c0199a.f1424m = c0201b.f1366u;
                c0199a.f1425n = c0201b.f1367v;
                c0199a.f1426o = c0201b.f1368w;
                c0199a.m616c(1);
                if (m791K(2)) {
                    Log.v("FragmentManager", "restoreAllState: back stack #" + i6 + " (index " + c0199a.f1347r + "): " + c0199a);
                    PrintWriter printWriter = new PrintWriter(new C0222l0());
                    c0199a.m620g("  ", printWriter, false);
                    printWriter.close();
                }
                this.f1614d.add(c0199a);
                i6++;
            }
        } else {
            this.f1614d = null;
        }
        this.f1619i.set(c0242z.f1653m);
        String str3 = c0242z.f1654n;
        if (str3 != null) {
            ComponentCallbacksC0223m m795D = m795D(str3);
            this.f1629s = m795D;
            m839q(m795D);
        }
        ArrayList<String> arrayList2 = c0242z.f1655o;
        if (arrayList2 != null) {
            for (int i17 = 0; i17 < arrayList2.size(); i17++) {
                Bundle bundle = c0242z.f1656p.get(i17);
                bundle.setClassLoader(this.f1626p.f1592k.getClassLoader());
                this.f1620j.put(arrayList2.get(i17), bundle);
            }
        }
        this.f1635y = new ArrayDeque<>(c0242z.f1657q);
    }

    /* renamed from: W */
    public final Parcelable m813W() {
        int i6;
        ArrayList<String> arrayList;
        int size;
        Iterator it = ((HashSet) m825e()).iterator();
        while (true) {
            if (!it.hasNext()) {
                break;
            }
            AbstractC0228o0 abstractC0228o0 = (AbstractC0228o0) it.next();
            if (abstractC0228o0.f1562e) {
                abstractC0228o0.f1562e = false;
                abstractC0228o0.m753c();
            }
        }
        m845w();
        m848z(true);
        this.f1602A = true;
        this.f1609H.f1354g = true;
        C0208e0 c0208e0 = this.f1613c;
        Objects.requireNonNull(c0208e0);
        ArrayList<C0204c0> arrayList2 = new ArrayList<>(((HashMap) c0208e0.f1406k).size());
        for (C0206d0 c0206d0 : ((HashMap) c0208e0.f1406k).values()) {
            if (c0206d0 != null) {
                ComponentCallbacksC0223m componentCallbacksC0223m = c0206d0.f1400c;
                C0204c0 c0204c0 = new C0204c0(componentCallbacksC0223m);
                ComponentCallbacksC0223m componentCallbacksC0223m2 = c0206d0.f1400c;
                if (componentCallbacksC0223m2.f1519j <= -1 || c0204c0.f1392v != null) {
                    c0204c0.f1392v = componentCallbacksC0223m2.f1520k;
                } else {
                    Bundle m651o = c0206d0.m651o();
                    c0204c0.f1392v = m651o;
                    if (c0206d0.f1400c.f1526q != null) {
                        if (m651o == null) {
                            c0204c0.f1392v = new Bundle();
                        }
                        c0204c0.f1392v.putString("android:target_state", c0206d0.f1400c.f1526q);
                        int i7 = c0206d0.f1400c.f1527r;
                        if (i7 != 0) {
                            c0204c0.f1392v.putInt("android:target_req_state", i7);
                        }
                    }
                }
                arrayList2.add(c0204c0);
                if (m791K(2)) {
                    Log.v("FragmentManager", "Saved state of " + componentCallbacksC0223m + ": " + c0204c0.f1392v);
                }
            }
        }
        C0201b[] c0201bArr = null;
        if (arrayList2.isEmpty()) {
            if (m791K(2)) {
                Log.v("FragmentManager", "saveAllState: no fragments!");
            }
            return null;
        }
        C0208e0 c0208e02 = this.f1613c;
        synchronized (((ArrayList) c0208e02.f1405j)) {
            if (((ArrayList) c0208e02.f1405j).isEmpty()) {
                arrayList = null;
            } else {
                arrayList = new ArrayList<>(((ArrayList) c0208e02.f1405j).size());
                Iterator it2 = ((ArrayList) c0208e02.f1405j).iterator();
                while (it2.hasNext()) {
                    ComponentCallbacksC0223m componentCallbacksC0223m3 = (ComponentCallbacksC0223m) it2.next();
                    arrayList.add(componentCallbacksC0223m3.f1523n);
                    if (m791K(2)) {
                        Log.v("FragmentManager", "saveAllState: adding fragment (" + componentCallbacksC0223m3.f1523n + "): " + componentCallbacksC0223m3);
                    }
                }
            }
        }
        ArrayList<C0199a> arrayList3 = this.f1614d;
        if (arrayList3 != null && (size = arrayList3.size()) > 0) {
            c0201bArr = new C0201b[size];
            for (i6 = 0; i6 < size; i6++) {
                c0201bArr[i6] = new C0201b(this.f1614d.get(i6));
                if (m791K(2)) {
                    Log.v("FragmentManager", "saveAllState: adding back stack #" + i6 + ": " + this.f1614d.get(i6));
                }
            }
        }
        C0242z c0242z = new C0242z();
        c0242z.f1650j = arrayList2;
        c0242z.f1651k = arrayList;
        c0242z.f1652l = c0201bArr;
        c0242z.f1653m = this.f1619i.get();
        ComponentCallbacksC0223m componentCallbacksC0223m4 = this.f1629s;
        if (componentCallbacksC0223m4 != null) {
            c0242z.f1654n = componentCallbacksC0223m4.f1523n;
        }
        c0242z.f1655o.addAll(this.f1620j.keySet());
        c0242z.f1656p.addAll(this.f1620j.values());
        c0242z.f1657q = new ArrayList<>(this.f1635y);
        return c0242z;
    }

    /* renamed from: X */
    public final void m814X() {
        synchronized (this.f1611a) {
            if (this.f1611a.size() == 1) {
                this.f1626p.f1593l.removeCallbacks(this.f1610I);
                this.f1626p.f1593l.post(this.f1610I);
                m828f0();
            }
        }
    }

    /* renamed from: Y */
    public final void m815Y(ComponentCallbacksC0223m componentCallbacksC0223m, boolean z5) {
        ViewGroup m798G = m798G(componentCallbacksC0223m);
        if (m798G == null || !(m798G instanceof C0233r)) {
            return;
        }
        ((C0233r) m798G).setDrawDisappearingViewsLast(!z5);
    }

    /* renamed from: Z */
    public final void m816Z(ComponentCallbacksC0223m componentCallbacksC0223m, AbstractC0251d.c cVar) {
        if (componentCallbacksC0223m.equals(m795D(componentCallbacksC0223m.f1523n)) && (componentCallbacksC0223m.f1495B == null || componentCallbacksC0223m.f1494A == this)) {
            componentCallbacksC0223m.f1513T = cVar;
            return;
        }
        throw new IllegalArgumentException("Fragment " + componentCallbacksC0223m + " is not an active fragment of FragmentManager " + this);
    }

    /* renamed from: a */
    public final C0206d0 m817a(ComponentCallbacksC0223m componentCallbacksC0223m) {
        if (m791K(2)) {
            Log.v("FragmentManager", "add: " + componentCallbacksC0223m);
        }
        C0206d0 m827f = m827f(componentCallbacksC0223m);
        componentCallbacksC0223m.f1494A = this;
        this.f1613c.m665k(m827f);
        if (!componentCallbacksC0223m.f1502I) {
            this.f1613c.m655a(componentCallbacksC0223m);
            componentCallbacksC0223m.f1530u = false;
            if (componentCallbacksC0223m.f1507N == null) {
                componentCallbacksC0223m.f1511R = false;
            }
            if (m802L(componentCallbacksC0223m)) {
                this.f1636z = true;
            }
        }
        return m827f;
    }

    /* renamed from: a0 */
    public final void m818a0(ComponentCallbacksC0223m componentCallbacksC0223m) {
        if (componentCallbacksC0223m == null || (componentCallbacksC0223m.equals(m795D(componentCallbacksC0223m.f1523n)) && (componentCallbacksC0223m.f1495B == null || componentCallbacksC0223m.f1494A == this))) {
            ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1629s;
            this.f1629s = componentCallbacksC0223m;
            m839q(componentCallbacksC0223m2);
            m839q(this.f1629s);
            return;
        }
        throw new IllegalArgumentException("Fragment " + componentCallbacksC0223m + " is not an active fragment of FragmentManager " + this);
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:12:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:17:0x003c  */
    /* JADX WARN: Removed duplicated region for block: B:23:0x0093  */
    /* JADX WARN: Removed duplicated region for block: B:30:? A[RETURN, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:31:0x005d  */
    /* JADX WARN: Removed duplicated region for block: B:9:0x0022  */
    @android.annotation.SuppressLint({"SyntheticAccessor"})
    /* renamed from: b */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m819b(androidx.fragment.app.AbstractC0237u<?> r3, androidx.activity.result.AbstractC0055d r4, androidx.fragment.app.ComponentCallbacksC0223m r5) {
        /*
            Method dump skipped, instructions count: 258
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.fragment.app.AbstractC0240x.m819b(androidx.fragment.app.u, androidx.activity.result.d, androidx.fragment.app.m):void");
    }

    /* renamed from: b0 */
    public final void m820b0(ComponentCallbacksC0223m componentCallbacksC0223m) {
        ViewGroup m798G = m798G(componentCallbacksC0223m);
        if (m798G != null) {
            if (componentCallbacksC0223m.m739q() + componentCallbacksC0223m.m738p() + componentCallbacksC0223m.m734l() + componentCallbacksC0223m.m733k() > 0) {
                if (m798G.getTag(R.id.visible_removing_fragment_view_tag) == null) {
                    m798G.setTag(R.id.visible_removing_fragment_view_tag, componentCallbacksC0223m);
                }
                ((ComponentCallbacksC0223m) m798G.getTag(R.id.visible_removing_fragment_view_tag)).m727a0(componentCallbacksC0223m.m737o());
            }
        }
    }

    /* renamed from: c */
    public final void m821c(ComponentCallbacksC0223m componentCallbacksC0223m) {
        if (m791K(2)) {
            Log.v("FragmentManager", "attach: " + componentCallbacksC0223m);
        }
        if (componentCallbacksC0223m.f1502I) {
            componentCallbacksC0223m.f1502I = false;
            if (componentCallbacksC0223m.f1529t) {
                return;
            }
            this.f1613c.m655a(componentCallbacksC0223m);
            if (m791K(2)) {
                Log.v("FragmentManager", "add from attach: " + componentCallbacksC0223m);
            }
            if (m802L(componentCallbacksC0223m)) {
                this.f1636z = true;
            }
        }
    }

    /* renamed from: c0 */
    public final void m822c0(ComponentCallbacksC0223m componentCallbacksC0223m) {
        if (m791K(2)) {
            Log.v("FragmentManager", "show: " + componentCallbacksC0223m);
        }
        if (componentCallbacksC0223m.f1501H) {
            componentCallbacksC0223m.f1501H = false;
            componentCallbacksC0223m.f1511R = !componentCallbacksC0223m.f1511R;
        }
    }

    /* renamed from: d */
    public final void m823d() {
        this.f1612b = false;
        this.f1607F.clear();
        this.f1606E.clear();
    }

    /* renamed from: d0 */
    public final void m824d0() {
        Iterator it = ((ArrayList) this.f1613c.m660f()).iterator();
        while (it.hasNext()) {
            C0206d0 c0206d0 = (C0206d0) it.next();
            ComponentCallbacksC0223m componentCallbacksC0223m = c0206d0.f1400c;
            if (componentCallbacksC0223m.f1508O) {
                if (this.f1612b) {
                    this.f1605D = true;
                } else {
                    componentCallbacksC0223m.f1508O = false;
                    c0206d0.m647k();
                }
            }
        }
    }

    /* renamed from: e */
    public final Set<AbstractC0228o0> m825e() {
        HashSet hashSet = new HashSet();
        Iterator it = ((ArrayList) this.f1613c.m660f()).iterator();
        while (it.hasNext()) {
            ViewGroup viewGroup = ((C0206d0) it.next()).f1400c.f1506M;
            if (viewGroup != null) {
                hashSet.add(AbstractC0228o0.m751g(viewGroup, m800I()));
            }
        }
        return hashSet;
    }

    /* renamed from: e0 */
    public final void m826e0(RuntimeException runtimeException) {
        Log.e("FragmentManager", runtimeException.getMessage());
        Log.e("FragmentManager", "Activity state:");
        PrintWriter printWriter = new PrintWriter(new C0222l0());
        AbstractC0237u<?> abstractC0237u = this.f1626p;
        try {
            if (abstractC0237u != null) {
                abstractC0237u.mo767r(printWriter, new String[0]);
            } else {
                m844v("  ", null, printWriter, new String[0]);
            }
            throw runtimeException;
        } catch (Exception e6) {
            Log.e("FragmentManager", "Failed dumping state", e6);
            throw runtimeException;
        }
    }

    /* renamed from: f */
    public final C0206d0 m827f(ComponentCallbacksC0223m componentCallbacksC0223m) {
        C0206d0 m663i = this.f1613c.m663i(componentCallbacksC0223m.f1523n);
        if (m663i != null) {
            return m663i;
        }
        C0206d0 c0206d0 = new C0206d0(this.f1623m, this.f1613c, componentCallbacksC0223m);
        c0206d0.m649m(this.f1626p.f1592k.getClassLoader());
        c0206d0.f1402e = this.f1625o;
        return c0206d0;
    }

    /* renamed from: f0 */
    public final void m828f0() {
        synchronized (this.f1611a) {
            if (!this.f1611a.isEmpty()) {
                this.f1618h.f207a = true;
                return;
            }
            c cVar = this.f1618h;
            ArrayList<C0199a> arrayList = this.f1614d;
            cVar.f207a = (arrayList != null ? arrayList.size() : 0) > 0 && m804N(this.f1628r);
        }
    }

    /* renamed from: g */
    public final void m829g(ComponentCallbacksC0223m componentCallbacksC0223m) {
        if (m791K(2)) {
            Log.v("FragmentManager", "detach: " + componentCallbacksC0223m);
        }
        if (componentCallbacksC0223m.f1502I) {
            return;
        }
        componentCallbacksC0223m.f1502I = true;
        if (componentCallbacksC0223m.f1529t) {
            if (m791K(2)) {
                Log.v("FragmentManager", "remove from detach: " + componentCallbacksC0223m);
            }
            C0208e0 c0208e0 = this.f1613c;
            synchronized (((ArrayList) c0208e0.f1405j)) {
                ((ArrayList) c0208e0.f1405j).remove(componentCallbacksC0223m);
            }
            componentCallbacksC0223m.f1529t = false;
            if (m802L(componentCallbacksC0223m)) {
                this.f1636z = true;
            }
            m820b0(componentCallbacksC0223m);
        }
    }

    /* renamed from: h */
    public final void m830h(Configuration configuration) {
        for (ComponentCallbacksC0223m componentCallbacksC0223m : this.f1613c.m664j()) {
            if (componentCallbacksC0223m != null) {
                componentCallbacksC0223m.onConfigurationChanged(configuration);
                componentCallbacksC0223m.f1496C.m830h(configuration);
            }
        }
    }

    /* renamed from: i */
    public final boolean m831i(MenuItem menuItem) {
        if (this.f1625o < 1) {
            return false;
        }
        for (ComponentCallbacksC0223m componentCallbacksC0223m : this.f1613c.m664j()) {
            if (componentCallbacksC0223m != null) {
                if (!componentCallbacksC0223m.f1501H ? componentCallbacksC0223m.f1496C.m831i(menuItem) : false) {
                    return true;
                }
            }
        }
        return false;
    }

    /* renamed from: j */
    public final void m832j() {
        this.f1602A = false;
        this.f1603B = false;
        this.f1609H.f1354g = false;
        m842t(1);
    }

    /* renamed from: k */
    public final boolean m833k(Menu menu, MenuInflater menuInflater) {
        if (this.f1625o < 1) {
            return false;
        }
        ArrayList<ComponentCallbacksC0223m> arrayList = null;
        boolean z5 = false;
        for (ComponentCallbacksC0223m componentCallbacksC0223m : this.f1613c.m664j()) {
            if (componentCallbacksC0223m != null && m803M(componentCallbacksC0223m)) {
                if (!componentCallbacksC0223m.f1501H ? componentCallbacksC0223m.f1496C.m833k(menu, menuInflater) | false : false) {
                    if (arrayList == null) {
                        arrayList = new ArrayList<>();
                    }
                    arrayList.add(componentCallbacksC0223m);
                    z5 = true;
                }
            }
        }
        if (this.f1615e != null) {
            for (int i6 = 0; i6 < this.f1615e.size(); i6++) {
                ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1615e.get(i6);
                if (arrayList == null || !arrayList.contains(componentCallbacksC0223m2)) {
                    Objects.requireNonNull(componentCallbacksC0223m2);
                }
            }
        }
        this.f1615e = arrayList;
        return z5;
    }

    /* JADX WARN: Type inference failed for: r0v3, types: [androidx.activity.result.d, androidx.activity.result.e$a] */
    /* JADX WARN: Type inference failed for: r0v4, types: [androidx.activity.result.d, androidx.activity.result.e$a] */
    /* JADX WARN: Type inference failed for: r0v5, types: [androidx.activity.result.d, androidx.activity.result.e$a] */
    /* renamed from: l */
    public final void m834l() {
        this.f1604C = true;
        m848z(true);
        m845w();
        m842t(-1);
        this.f1626p = null;
        this.f1627q = null;
        this.f1628r = null;
        if (this.f1617g != null) {
            Iterator<InterfaceC0049a> it = this.f1618h.f208b.iterator();
            while (it.hasNext()) {
                it.next().cancel();
            }
            this.f1617g = null;
        }
        ?? r0 = this.f1632v;
        if (r0 != 0) {
            r0.m129r();
            this.f1633w.m129r();
            this.f1634x.m129r();
        }
    }

    /* renamed from: m */
    public final void m835m() {
        for (ComponentCallbacksC0223m componentCallbacksC0223m : this.f1613c.m664j()) {
            if (componentCallbacksC0223m != null) {
                componentCallbacksC0223m.m717Q();
            }
        }
    }

    /* renamed from: n */
    public final void m836n(boolean z5) {
        for (ComponentCallbacksC0223m componentCallbacksC0223m : this.f1613c.m664j()) {
            if (componentCallbacksC0223m != null) {
                componentCallbacksC0223m.m718R(z5);
            }
        }
    }

    /* renamed from: o */
    public final boolean m837o(MenuItem menuItem) {
        if (this.f1625o < 1) {
            return false;
        }
        for (ComponentCallbacksC0223m componentCallbacksC0223m : this.f1613c.m664j()) {
            if (componentCallbacksC0223m != null) {
                if (!componentCallbacksC0223m.f1501H ? componentCallbacksC0223m.f1496C.m837o(menuItem) : false) {
                    return true;
                }
            }
        }
        return false;
    }

    /* renamed from: p */
    public final void m838p(Menu menu) {
        if (this.f1625o < 1) {
            return;
        }
        for (ComponentCallbacksC0223m componentCallbacksC0223m : this.f1613c.m664j()) {
            if (componentCallbacksC0223m != null && !componentCallbacksC0223m.f1501H) {
                componentCallbacksC0223m.f1496C.m838p(menu);
            }
        }
    }

    /* renamed from: q */
    public final void m839q(ComponentCallbacksC0223m componentCallbacksC0223m) {
        if (componentCallbacksC0223m == null || !componentCallbacksC0223m.equals(m795D(componentCallbacksC0223m.f1523n))) {
            return;
        }
        boolean m804N = componentCallbacksC0223m.f1494A.m804N(componentCallbacksC0223m);
        Boolean bool = componentCallbacksC0223m.f1528s;
        if (bool == null || bool.booleanValue() != m804N) {
            componentCallbacksC0223m.f1528s = Boolean.valueOf(m804N);
            C0241y c0241y = componentCallbacksC0223m.f1496C;
            c0241y.m828f0();
            c0241y.m839q(c0241y.f1629s);
        }
    }

    /* renamed from: r */
    public final void m840r(boolean z5) {
        for (ComponentCallbacksC0223m componentCallbacksC0223m : this.f1613c.m664j()) {
            if (componentCallbacksC0223m != null) {
                componentCallbacksC0223m.m719S(z5);
            }
        }
    }

    /* renamed from: s */
    public final boolean m841s(Menu menu) {
        boolean z5 = false;
        if (this.f1625o < 1) {
            return false;
        }
        for (ComponentCallbacksC0223m componentCallbacksC0223m : this.f1613c.m664j()) {
            if (componentCallbacksC0223m != null && m803M(componentCallbacksC0223m) && componentCallbacksC0223m.m720T(menu)) {
                z5 = true;
            }
        }
        return z5;
    }

    /* renamed from: t */
    public final void m842t(int i6) {
        try {
            this.f1612b = true;
            for (C0206d0 c0206d0 : ((HashMap) this.f1613c.f1406k).values()) {
                if (c0206d0 != null) {
                    c0206d0.f1402e = i6;
                }
            }
            m806P(i6, false);
            Iterator it = ((HashSet) m825e()).iterator();
            while (it.hasNext()) {
                ((AbstractC0228o0) it.next()).m755e();
            }
            this.f1612b = false;
            m848z(true);
        } catch (Throwable th) {
            this.f1612b = false;
            throw th;
        }
    }

    public final String toString() {
        Object obj;
        StringBuilder sb = new StringBuilder(128);
        sb.append("FragmentManager{");
        sb.append(Integer.toHexString(System.identityHashCode(this)));
        sb.append(" in ");
        ComponentCallbacksC0223m componentCallbacksC0223m = this.f1628r;
        if (componentCallbacksC0223m != null) {
            sb.append(componentCallbacksC0223m.getClass().getSimpleName());
            sb.append("{");
            obj = this.f1628r;
        } else {
            AbstractC0237u<?> abstractC0237u = this.f1626p;
            if (abstractC0237u == null) {
                sb.append("null");
                sb.append("}}");
                return sb.toString();
            }
            sb.append(abstractC0237u.getClass().getSimpleName());
            sb.append("{");
            obj = this.f1626p;
        }
        sb.append(Integer.toHexString(System.identityHashCode(obj)));
        sb.append("}");
        sb.append("}}");
        return sb.toString();
    }

    /* renamed from: u */
    public final void m843u() {
        if (this.f1605D) {
            this.f1605D = false;
            m824d0();
        }
    }

    /* renamed from: v */
    public final void m844v(String str, FileDescriptor fileDescriptor, PrintWriter printWriter, String[] strArr) {
        int size;
        int size2;
        String str2;
        String m103g = C0052a.m103g(str, "    ");
        C0208e0 c0208e0 = this.f1613c;
        Objects.requireNonNull(c0208e0);
        String str3 = str + "    ";
        if (!((HashMap) c0208e0.f1406k).isEmpty()) {
            printWriter.print(str);
            printWriter.println("Active Fragments:");
            for (C0206d0 c0206d0 : ((HashMap) c0208e0.f1406k).values()) {
                printWriter.print(str);
                if (c0206d0 != null) {
                    ComponentCallbacksC0223m componentCallbacksC0223m = c0206d0.f1400c;
                    printWriter.println(componentCallbacksC0223m);
                    Objects.requireNonNull(componentCallbacksC0223m);
                    printWriter.print(str3);
                    printWriter.print("mFragmentId=#");
                    printWriter.print(Integer.toHexString(componentCallbacksC0223m.f1498E));
                    printWriter.print(" mContainerId=#");
                    printWriter.print(Integer.toHexString(componentCallbacksC0223m.f1499F));
                    printWriter.print(" mTag=");
                    printWriter.println(componentCallbacksC0223m.f1500G);
                    printWriter.print(str3);
                    printWriter.print("mState=");
                    printWriter.print(componentCallbacksC0223m.f1519j);
                    printWriter.print(" mWho=");
                    printWriter.print(componentCallbacksC0223m.f1523n);
                    printWriter.print(" mBackStackNesting=");
                    printWriter.println(componentCallbacksC0223m.f1535z);
                    printWriter.print(str3);
                    printWriter.print("mAdded=");
                    printWriter.print(componentCallbacksC0223m.f1529t);
                    printWriter.print(" mRemoving=");
                    printWriter.print(componentCallbacksC0223m.f1530u);
                    printWriter.print(" mFromLayout=");
                    printWriter.print(componentCallbacksC0223m.f1531v);
                    printWriter.print(" mInLayout=");
                    printWriter.println(componentCallbacksC0223m.f1532w);
                    printWriter.print(str3);
                    printWriter.print("mHidden=");
                    printWriter.print(componentCallbacksC0223m.f1501H);
                    printWriter.print(" mDetached=");
                    printWriter.print(componentCallbacksC0223m.f1502I);
                    printWriter.print(" mMenuVisible=");
                    printWriter.print(componentCallbacksC0223m.f1504K);
                    printWriter.print(" mHasMenu=");
                    printWriter.println(false);
                    printWriter.print(str3);
                    printWriter.print("mRetainInstance=");
                    printWriter.print(componentCallbacksC0223m.f1503J);
                    printWriter.print(" mUserVisibleHint=");
                    printWriter.println(componentCallbacksC0223m.f1509P);
                    if (componentCallbacksC0223m.f1494A != null) {
                        printWriter.print(str3);
                        printWriter.print("mFragmentManager=");
                        printWriter.println(componentCallbacksC0223m.f1494A);
                    }
                    if (componentCallbacksC0223m.f1495B != null) {
                        printWriter.print(str3);
                        printWriter.print("mHost=");
                        printWriter.println(componentCallbacksC0223m.f1495B);
                    }
                    if (componentCallbacksC0223m.f1497D != null) {
                        printWriter.print(str3);
                        printWriter.print("mParentFragment=");
                        printWriter.println(componentCallbacksC0223m.f1497D);
                    }
                    if (componentCallbacksC0223m.f1524o != null) {
                        printWriter.print(str3);
                        printWriter.print("mArguments=");
                        printWriter.println(componentCallbacksC0223m.f1524o);
                    }
                    if (componentCallbacksC0223m.f1520k != null) {
                        printWriter.print(str3);
                        printWriter.print("mSavedFragmentState=");
                        printWriter.println(componentCallbacksC0223m.f1520k);
                    }
                    if (componentCallbacksC0223m.f1521l != null) {
                        printWriter.print(str3);
                        printWriter.print("mSavedViewState=");
                        printWriter.println(componentCallbacksC0223m.f1521l);
                    }
                    if (componentCallbacksC0223m.f1522m != null) {
                        printWriter.print(str3);
                        printWriter.print("mSavedViewRegistryState=");
                        printWriter.println(componentCallbacksC0223m.f1522m);
                    }
                    Object obj = componentCallbacksC0223m.f1525p;
                    if (obj == null) {
                        AbstractC0240x abstractC0240x = componentCallbacksC0223m.f1494A;
                        obj = (abstractC0240x == null || (str2 = componentCallbacksC0223m.f1526q) == null) ? null : abstractC0240x.m795D(str2);
                    }
                    if (obj != null) {
                        printWriter.print(str3);
                        printWriter.print("mTarget=");
                        printWriter.print(obj);
                        printWriter.print(" mTargetRequestCode=");
                        printWriter.println(componentCallbacksC0223m.f1527r);
                    }
                    printWriter.print(str3);
                    printWriter.print("mPopDirection=");
                    printWriter.println(componentCallbacksC0223m.m737o());
                    if (componentCallbacksC0223m.m733k() != 0) {
                        printWriter.print(str3);
                        printWriter.print("getEnterAnim=");
                        printWriter.println(componentCallbacksC0223m.m733k());
                    }
                    if (componentCallbacksC0223m.m734l() != 0) {
                        printWriter.print(str3);
                        printWriter.print("getExitAnim=");
                        printWriter.println(componentCallbacksC0223m.m734l());
                    }
                    if (componentCallbacksC0223m.m738p() != 0) {
                        printWriter.print(str3);
                        printWriter.print("getPopEnterAnim=");
                        printWriter.println(componentCallbacksC0223m.m738p());
                    }
                    if (componentCallbacksC0223m.m739q() != 0) {
                        printWriter.print(str3);
                        printWriter.print("getPopExitAnim=");
                        printWriter.println(componentCallbacksC0223m.m739q());
                    }
                    if (componentCallbacksC0223m.f1506M != null) {
                        printWriter.print(str3);
                        printWriter.print("mContainer=");
                        printWriter.println(componentCallbacksC0223m.f1506M);
                    }
                    if (componentCallbacksC0223m.f1507N != null) {
                        printWriter.print(str3);
                        printWriter.print("mView=");
                        printWriter.println(componentCallbacksC0223m.f1507N);
                    }
                    if (componentCallbacksC0223m.m730h() != null) {
                        printWriter.print(str3);
                        printWriter.print("mAnimatingAway=");
                        printWriter.println(componentCallbacksC0223m.m730h());
                    }
                    if (componentCallbacksC0223m.m732j() != null) {
                        AbstractC1253a.m3142b(componentCallbacksC0223m).mo3143a(str3, printWriter);
                    }
                    printWriter.print(str3);
                    printWriter.println("Child " + componentCallbacksC0223m.f1496C + ":");
                    componentCallbacksC0223m.f1496C.m844v(C0052a.m103g(str3, "  "), fileDescriptor, printWriter, strArr);
                } else {
                    printWriter.println("null");
                }
            }
        }
        int size3 = ((ArrayList) c0208e0.f1405j).size();
        if (size3 > 0) {
            printWriter.print(str);
            printWriter.println("Added Fragments:");
            for (int i6 = 0; i6 < size3; i6++) {
                ComponentCallbacksC0223m componentCallbacksC0223m2 = (ComponentCallbacksC0223m) ((ArrayList) c0208e0.f1405j).get(i6);
                printWriter.print(str);
                printWriter.print("  #");
                printWriter.print(i6);
                printWriter.print(": ");
                printWriter.println(componentCallbacksC0223m2.toString());
            }
        }
        ArrayList<ComponentCallbacksC0223m> arrayList = this.f1615e;
        if (arrayList != null && (size2 = arrayList.size()) > 0) {
            printWriter.print(str);
            printWriter.println("Fragments Created Menus:");
            for (int i7 = 0; i7 < size2; i7++) {
                ComponentCallbacksC0223m componentCallbacksC0223m3 = this.f1615e.get(i7);
                printWriter.print(str);
                printWriter.print("  #");
                printWriter.print(i7);
                printWriter.print(": ");
                printWriter.println(componentCallbacksC0223m3.toString());
            }
        }
        ArrayList<C0199a> arrayList2 = this.f1614d;
        if (arrayList2 != null && (size = arrayList2.size()) > 0) {
            printWriter.print(str);
            printWriter.println("Back Stack:");
            for (int i8 = 0; i8 < size; i8++) {
                C0199a c0199a = this.f1614d.get(i8);
                printWriter.print(str);
                printWriter.print("  #");
                printWriter.print(i8);
                printWriter.print(": ");
                printWriter.println(c0199a.toString());
                c0199a.m620g(m103g, printWriter, true);
            }
        }
        printWriter.print(str);
        printWriter.println("Back Stack Index: " + this.f1619i.get());
        synchronized (this.f1611a) {
            int size4 = this.f1611a.size();
            if (size4 > 0) {
                printWriter.print(str);
                printWriter.println("Pending Actions:");
                for (int i9 = 0; i9 < size4; i9++) {
                    Object obj2 = (m) this.f1611a.get(i9);
                    printWriter.print(str);
                    printWriter.print("  #");
                    printWriter.print(i9);
                    printWriter.print(": ");
                    printWriter.println(obj2);
                }
            }
        }
        printWriter.print(str);
        printWriter.println("FragmentManager misc state:");
        printWriter.print(str);
        printWriter.print("  mHost=");
        printWriter.println(this.f1626p);
        printWriter.print(str);
        printWriter.print("  mContainer=");
        printWriter.println(this.f1627q);
        if (this.f1628r != null) {
            printWriter.print(str);
            printWriter.print("  mParent=");
            printWriter.println(this.f1628r);
        }
        printWriter.print(str);
        printWriter.print("  mCurState=");
        printWriter.print(this.f1625o);
        printWriter.print(" mStateSaved=");
        printWriter.print(this.f1602A);
        printWriter.print(" mStopped=");
        printWriter.print(this.f1603B);
        printWriter.print(" mDestroyed=");
        printWriter.println(this.f1604C);
        if (this.f1636z) {
            printWriter.print(str);
            printWriter.print("  mNeedMenuInvalidate=");
            printWriter.println(this.f1636z);
        }
    }

    /* renamed from: w */
    public final void m845w() {
        Iterator it = ((HashSet) m825e()).iterator();
        while (it.hasNext()) {
            ((AbstractC0228o0) it.next()).m755e();
        }
    }

    /* renamed from: x */
    public final void m846x(m mVar, boolean z5) {
        if (!z5) {
            if (this.f1626p == null) {
                if (!this.f1604C) {
                    throw new IllegalStateException("FragmentManager has not been attached to a host.");
                }
                throw new IllegalStateException("FragmentManager has been destroyed");
            }
            if (m805O()) {
                throw new IllegalStateException("Can not perform this action after onSaveInstanceState");
            }
        }
        synchronized (this.f1611a) {
            if (this.f1626p == null) {
                if (!z5) {
                    throw new IllegalStateException("Activity has been destroyed");
                }
            } else {
                this.f1611a.add(mVar);
                m814X();
            }
        }
    }

    /* renamed from: y */
    public final void m847y(boolean z5) {
        if (this.f1612b) {
            throw new IllegalStateException("FragmentManager is already executing transactions");
        }
        if (this.f1626p == null) {
            if (!this.f1604C) {
                throw new IllegalStateException("FragmentManager has not been attached to a host.");
            }
            throw new IllegalStateException("FragmentManager has been destroyed");
        }
        if (Looper.myLooper() != this.f1626p.f1593l.getLooper()) {
            throw new IllegalStateException("Must be called from main thread of fragment host");
        }
        if (!z5 && m805O()) {
            throw new IllegalStateException("Can not perform this action after onSaveInstanceState");
        }
        if (this.f1606E == null) {
            this.f1606E = new ArrayList<>();
            this.f1607F = new ArrayList<>();
        }
        this.f1612b = true;
        try {
            m794C(null, null);
        } finally {
            this.f1612b = false;
        }
    }

    /* renamed from: z */
    public final boolean m848z(boolean z5) {
        boolean z6;
        m847y(z5);
        boolean z7 = false;
        while (true) {
            ArrayList<C0199a> arrayList = this.f1606E;
            ArrayList<Boolean> arrayList2 = this.f1607F;
            synchronized (this.f1611a) {
                if (this.f1611a.isEmpty()) {
                    z6 = false;
                } else {
                    int size = this.f1611a.size();
                    z6 = false;
                    for (int i6 = 0; i6 < size; i6++) {
                        z6 |= this.f1611a.get(i6).mo615a(arrayList, arrayList2);
                    }
                    this.f1611a.clear();
                    this.f1626p.f1593l.removeCallbacks(this.f1610I);
                }
            }
            if (!z6) {
                m828f0();
                m843u();
                this.f1613c.m656b();
                return z7;
            }
            this.f1612b = true;
            try {
                m811U(this.f1606E, this.f1607F);
                m823d();
                z7 = true;
            } catch (Throwable th) {
                m823d();
                throw th;
            }
        }
    }
}
