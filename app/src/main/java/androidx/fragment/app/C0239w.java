package androidx.fragment.app;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import androidx.fragment.app.AbstractC0240x;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import java.util.Iterator;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;

/* renamed from: androidx.fragment.app.w */
/* loaded from: classes.dex */
public final class C0239w {

    /* renamed from: a */
    public final CopyOnWriteArrayList<a> f1598a = new CopyOnWriteArrayList<>();

    /* renamed from: b */
    public final AbstractC0240x f1599b;

    /* renamed from: androidx.fragment.app.w$a */
    public static final class a {

        /* renamed from: a */
        public final AbstractC0240x.k f1600a;

        /* renamed from: b */
        public final boolean f1601b = false;

        public a(AbstractC0240x.k kVar) {
            this.f1600a = kVar;
        }
    }

    public C0239w(AbstractC0240x abstractC0240x) {
        this.f1599b = abstractC0240x;
    }

    /* renamed from: a */
    public final void m777a(ComponentCallbacksC0223m componentCallbacksC0223m, Bundle bundle, boolean z5) {
        ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1599b.f1628r;
        if (componentCallbacksC0223m2 != null) {
            componentCallbacksC0223m2.m736n().f1623m.m777a(componentCallbacksC0223m, bundle, true);
        }
        Iterator<a> it = this.f1598a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (!z5 || next.f1601b) {
                Objects.requireNonNull(next.f1600a);
            }
        }
    }

    /* renamed from: b */
    public final void m778b(ComponentCallbacksC0223m componentCallbacksC0223m, boolean z5) {
        AbstractC0240x abstractC0240x = this.f1599b;
        Context context = abstractC0240x.f1626p.f1592k;
        ComponentCallbacksC0223m componentCallbacksC0223m2 = abstractC0240x.f1628r;
        if (componentCallbacksC0223m2 != null) {
            componentCallbacksC0223m2.m736n().f1623m.m778b(componentCallbacksC0223m, true);
        }
        Iterator<a> it = this.f1598a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (!z5 || next.f1601b) {
                Objects.requireNonNull(next.f1600a);
            }
        }
    }

    /* renamed from: c */
    public final void m779c(ComponentCallbacksC0223m componentCallbacksC0223m, Bundle bundle, boolean z5) {
        ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1599b.f1628r;
        if (componentCallbacksC0223m2 != null) {
            componentCallbacksC0223m2.m736n().f1623m.m779c(componentCallbacksC0223m, bundle, true);
        }
        Iterator<a> it = this.f1598a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (!z5 || next.f1601b) {
                Objects.requireNonNull(next.f1600a);
            }
        }
    }

    /* renamed from: d */
    public final void m780d(ComponentCallbacksC0223m componentCallbacksC0223m, boolean z5) {
        ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1599b.f1628r;
        if (componentCallbacksC0223m2 != null) {
            componentCallbacksC0223m2.m736n().f1623m.m780d(componentCallbacksC0223m, true);
        }
        Iterator<a> it = this.f1598a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (!z5 || next.f1601b) {
                Objects.requireNonNull(next.f1600a);
            }
        }
    }

    /* renamed from: e */
    public final void m781e(ComponentCallbacksC0223m componentCallbacksC0223m, boolean z5) {
        ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1599b.f1628r;
        if (componentCallbacksC0223m2 != null) {
            componentCallbacksC0223m2.m736n().f1623m.m781e(componentCallbacksC0223m, true);
        }
        Iterator<a> it = this.f1598a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (!z5 || next.f1601b) {
                Objects.requireNonNull(next.f1600a);
            }
        }
    }

    /* renamed from: f */
    public final void m782f(ComponentCallbacksC0223m componentCallbacksC0223m, boolean z5) {
        ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1599b.f1628r;
        if (componentCallbacksC0223m2 != null) {
            componentCallbacksC0223m2.m736n().f1623m.m782f(componentCallbacksC0223m, true);
        }
        Iterator<a> it = this.f1598a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (!z5 || next.f1601b) {
                Objects.requireNonNull(next.f1600a);
            }
        }
    }

    /* renamed from: g */
    public final void m783g(ComponentCallbacksC0223m componentCallbacksC0223m, boolean z5) {
        AbstractC0240x abstractC0240x = this.f1599b;
        Context context = abstractC0240x.f1626p.f1592k;
        ComponentCallbacksC0223m componentCallbacksC0223m2 = abstractC0240x.f1628r;
        if (componentCallbacksC0223m2 != null) {
            componentCallbacksC0223m2.m736n().f1623m.m783g(componentCallbacksC0223m, true);
        }
        Iterator<a> it = this.f1598a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (!z5 || next.f1601b) {
                Objects.requireNonNull(next.f1600a);
            }
        }
    }

    /* renamed from: h */
    public final void m784h(ComponentCallbacksC0223m componentCallbacksC0223m, Bundle bundle, boolean z5) {
        ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1599b.f1628r;
        if (componentCallbacksC0223m2 != null) {
            componentCallbacksC0223m2.m736n().f1623m.m784h(componentCallbacksC0223m, bundle, true);
        }
        Iterator<a> it = this.f1598a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (!z5 || next.f1601b) {
                Objects.requireNonNull(next.f1600a);
            }
        }
    }

    /* renamed from: i */
    public final void m785i(ComponentCallbacksC0223m componentCallbacksC0223m, boolean z5) {
        ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1599b.f1628r;
        if (componentCallbacksC0223m2 != null) {
            componentCallbacksC0223m2.m736n().f1623m.m785i(componentCallbacksC0223m, true);
        }
        Iterator<a> it = this.f1598a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (!z5 || next.f1601b) {
                Objects.requireNonNull(next.f1600a);
            }
        }
    }

    /* renamed from: j */
    public final void m786j(ComponentCallbacksC0223m componentCallbacksC0223m, Bundle bundle, boolean z5) {
        ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1599b.f1628r;
        if (componentCallbacksC0223m2 != null) {
            componentCallbacksC0223m2.m736n().f1623m.m786j(componentCallbacksC0223m, bundle, true);
        }
        Iterator<a> it = this.f1598a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (!z5 || next.f1601b) {
                Objects.requireNonNull(next.f1600a);
            }
        }
    }

    /* renamed from: k */
    public final void m787k(ComponentCallbacksC0223m componentCallbacksC0223m, boolean z5) {
        ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1599b.f1628r;
        if (componentCallbacksC0223m2 != null) {
            componentCallbacksC0223m2.m736n().f1623m.m787k(componentCallbacksC0223m, true);
        }
        Iterator<a> it = this.f1598a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (!z5 || next.f1601b) {
                Objects.requireNonNull(next.f1600a);
            }
        }
    }

    /* renamed from: l */
    public final void m788l(ComponentCallbacksC0223m componentCallbacksC0223m, boolean z5) {
        ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1599b.f1628r;
        if (componentCallbacksC0223m2 != null) {
            componentCallbacksC0223m2.m736n().f1623m.m788l(componentCallbacksC0223m, true);
        }
        Iterator<a> it = this.f1598a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (!z5 || next.f1601b) {
                Objects.requireNonNull(next.f1600a);
            }
        }
    }

    /* renamed from: m */
    public final void m789m(ComponentCallbacksC0223m componentCallbacksC0223m, View view, Bundle bundle, boolean z5) {
        ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1599b.f1628r;
        if (componentCallbacksC0223m2 != null) {
            componentCallbacksC0223m2.m736n().f1623m.m789m(componentCallbacksC0223m, view, bundle, true);
        }
        Iterator<a> it = this.f1598a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (!z5 || next.f1601b) {
                AbstractC0240x.k kVar = next.f1600a;
                AbstractC0240x abstractC0240x = this.f1599b;
                FragmentStateAdapter.C0344a c0344a = (FragmentStateAdapter.C0344a) kVar;
                if (componentCallbacksC0223m == c0344a.f2196a) {
                    C0239w c0239w = abstractC0240x.f1623m;
                    synchronized (c0239w.f1598a) {
                        int i6 = 0;
                        int size = c0239w.f1598a.size();
                        while (true) {
                            if (i6 >= size) {
                                break;
                            }
                            if (c0239w.f1598a.get(i6).f1600a == c0344a) {
                                c0239w.f1598a.remove(i6);
                                break;
                            }
                            i6++;
                        }
                    }
                    FragmentStateAdapter.this.m1368o(view, c0344a.f2197b);
                } else {
                    continue;
                }
            }
        }
    }

    /* renamed from: n */
    public final void m790n(ComponentCallbacksC0223m componentCallbacksC0223m, boolean z5) {
        ComponentCallbacksC0223m componentCallbacksC0223m2 = this.f1599b.f1628r;
        if (componentCallbacksC0223m2 != null) {
            componentCallbacksC0223m2.m736n().f1623m.m790n(componentCallbacksC0223m, true);
        }
        Iterator<a> it = this.f1598a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (!z5 || next.f1601b) {
                Objects.requireNonNull(next.f1600a);
            }
        }
    }
}
