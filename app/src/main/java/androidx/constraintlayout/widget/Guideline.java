package androidx.constraintlayout.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.view.View;
import androidx.constraintlayout.widget.ConstraintLayout;

/* loaded from: classes.dex */
public class Guideline extends View {
    public Guideline(Context context) {
        super(context);
        super.setVisibility(8);
    }

    public Guideline(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        super.setVisibility(8);
    }

    @Override // android.view.View
    public final void draw(Canvas canvas) {
    }

    @Override // android.view.View
    public final void onMeasure(int i6, int i7) {
        setMeasuredDimension(0, 0);
    }

    public void setGuidelineBegin(int i6) {
        ConstraintLayout.C0178a c0178a = (ConstraintLayout.C0178a) getLayoutParams();
        c0178a.f1082a = i6;
        setLayoutParams(c0178a);
    }

    public void setGuidelineEnd(int i6) {
        ConstraintLayout.C0178a c0178a = (ConstraintLayout.C0178a) getLayoutParams();
        c0178a.f1084b = i6;
        setLayoutParams(c0178a);
    }

    public void setGuidelinePercent(float f6) {
        ConstraintLayout.C0178a c0178a = (ConstraintLayout.C0178a) getLayoutParams();
        c0178a.f1086c = f6;
        setLayoutParams(c0178a);
    }

    @Override // android.view.View
    public void setVisibility(int i6) {
    }
}
