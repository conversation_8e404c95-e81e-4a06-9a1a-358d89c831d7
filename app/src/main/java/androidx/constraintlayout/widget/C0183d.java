package androidx.constraintlayout.widget;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.view.View;
import androidx.constraintlayout.widget.ConstraintLayout;

/* renamed from: androidx.constraintlayout.widget.d */
/* loaded from: classes.dex */
public final class C0183d extends View {

    /* renamed from: j */
    public int f1245j;

    /* renamed from: k */
    public View f1246k;

    /* renamed from: l */
    public int f1247l;

    public View getContent() {
        return this.f1246k;
    }

    public int getEmptyVisibility() {
        return this.f1247l;
    }

    @Override // android.view.View
    public final void onDraw(Canvas canvas) {
        if (isInEditMode()) {
            canvas.drawRGB(223, 223, 223);
            Paint paint = new Paint();
            paint.setARGB(255, 210, 210, 210);
            paint.setTextAlign(Paint.Align.CENTER);
            paint.setTypeface(Typeface.create(Typeface.DEFAULT, 0));
            Rect rect = new Rect();
            canvas.getClipBounds(rect);
            paint.setTextSize(rect.height());
            int height = rect.height();
            int width = rect.width();
            paint.setTextAlign(Paint.Align.LEFT);
            paint.getTextBounds("?", 0, 1, rect);
            canvas.drawText("?", ((width / 2.0f) - (rect.width() / 2.0f)) - rect.left, ((rect.height() / 2.0f) + (height / 2.0f)) - rect.bottom, paint);
        }
    }

    public void setContentId(int i6) {
        View findViewById;
        if (this.f1245j == i6) {
            return;
        }
        View view = this.f1246k;
        if (view != null) {
            view.setVisibility(0);
            ((ConstraintLayout.C0178a) this.f1246k.getLayoutParams()).f1083a0 = false;
            this.f1246k = null;
        }
        this.f1245j = i6;
        if (i6 == -1 || (findViewById = ((View) getParent()).findViewById(i6)) == null) {
            return;
        }
        findViewById.setVisibility(8);
    }

    public void setEmptyVisibility(int i6) {
        this.f1247l = i6;
    }
}
