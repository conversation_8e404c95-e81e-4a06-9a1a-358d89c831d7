package androidx.constraintlayout.widget;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseArray;
import android.util.SparseIntArray;
import android.view.View;
import android.view.ViewGroup;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Objects;
import p096o.C1126d;
import p096o.C1127e;
import p096o.C1128f;
import p103p.C1215b;
import p110q.AbstractC1250c;
import p110q.C1249b;
import p153w3.C1798e;

/* loaded from: classes.dex */
public class ConstraintLayout extends ViewGroup {

    /* renamed from: j */
    public SparseArray<View> f1039j;

    /* renamed from: k */
    public ArrayList<AbstractC0180a> f1040k;

    /* renamed from: l */
    public C1127e f1041l;

    /* renamed from: m */
    public int f1042m;

    /* renamed from: n */
    public int f1043n;

    /* renamed from: o */
    public int f1044o;

    /* renamed from: p */
    public int f1045p;

    /* renamed from: q */
    public boolean f1046q;

    /* renamed from: r */
    public int f1047r;

    /* renamed from: s */
    public C0181b f1048s;

    /* renamed from: t */
    public C1249b f1049t;

    /* renamed from: u */
    public int f1050u;

    /* renamed from: v */
    public HashMap<String, Integer> f1051v;

    /* renamed from: w */
    public SparseArray<C1126d> f1052w;

    /* renamed from: x */
    public C0179b f1053x;

    /* renamed from: y */
    public int f1054y;

    /* renamed from: z */
    public int f1055z;

    /* renamed from: androidx.constraintlayout.widget.ConstraintLayout$a */
    public static class C0178a extends ViewGroup.MarginLayoutParams {

        /* renamed from: A */
        public float f1056A;

        /* renamed from: B */
        public String f1057B;

        /* renamed from: C */
        public int f1058C;

        /* renamed from: D */
        public float f1059D;

        /* renamed from: E */
        public float f1060E;

        /* renamed from: F */
        public int f1061F;

        /* renamed from: G */
        public int f1062G;

        /* renamed from: H */
        public int f1063H;

        /* renamed from: I */
        public int f1064I;

        /* renamed from: J */
        public int f1065J;

        /* renamed from: K */
        public int f1066K;

        /* renamed from: L */
        public int f1067L;

        /* renamed from: M */
        public int f1068M;

        /* renamed from: N */
        public float f1069N;

        /* renamed from: O */
        public float f1070O;

        /* renamed from: P */
        public int f1071P;

        /* renamed from: Q */
        public int f1072Q;

        /* renamed from: R */
        public int f1073R;

        /* renamed from: S */
        public boolean f1074S;

        /* renamed from: T */
        public boolean f1075T;

        /* renamed from: U */
        public String f1076U;

        /* renamed from: V */
        public boolean f1077V;

        /* renamed from: W */
        public boolean f1078W;

        /* renamed from: X */
        public boolean f1079X;

        /* renamed from: Y */
        public boolean f1080Y;

        /* renamed from: Z */
        public boolean f1081Z;

        /* renamed from: a */
        public int f1082a;

        /* renamed from: a0 */
        public boolean f1083a0;

        /* renamed from: b */
        public int f1084b;

        /* renamed from: b0 */
        public int f1085b0;

        /* renamed from: c */
        public float f1086c;

        /* renamed from: c0 */
        public int f1087c0;

        /* renamed from: d */
        public int f1088d;

        /* renamed from: d0 */
        public int f1089d0;

        /* renamed from: e */
        public int f1090e;

        /* renamed from: e0 */
        public int f1091e0;

        /* renamed from: f */
        public int f1092f;

        /* renamed from: f0 */
        public int f1093f0;

        /* renamed from: g */
        public int f1094g;

        /* renamed from: g0 */
        public int f1095g0;

        /* renamed from: h */
        public int f1096h;

        /* renamed from: h0 */
        public float f1097h0;

        /* renamed from: i */
        public int f1098i;

        /* renamed from: i0 */
        public int f1099i0;

        /* renamed from: j */
        public int f1100j;

        /* renamed from: j0 */
        public int f1101j0;

        /* renamed from: k */
        public int f1102k;

        /* renamed from: k0 */
        public float f1103k0;

        /* renamed from: l */
        public int f1104l;

        /* renamed from: l0 */
        public C1126d f1105l0;

        /* renamed from: m */
        public int f1106m;

        /* renamed from: n */
        public int f1107n;

        /* renamed from: o */
        public float f1108o;

        /* renamed from: p */
        public int f1109p;

        /* renamed from: q */
        public int f1110q;

        /* renamed from: r */
        public int f1111r;

        /* renamed from: s */
        public int f1112s;

        /* renamed from: t */
        public int f1113t;

        /* renamed from: u */
        public int f1114u;

        /* renamed from: v */
        public int f1115v;

        /* renamed from: w */
        public int f1116w;

        /* renamed from: x */
        public int f1117x;

        /* renamed from: y */
        public int f1118y;

        /* renamed from: z */
        public float f1119z;

        /* renamed from: androidx.constraintlayout.widget.ConstraintLayout$a$a */
        public static class a {

            /* renamed from: a */
            public static final SparseIntArray f1120a;

            static {
                SparseIntArray sparseIntArray = new SparseIntArray();
                f1120a = sparseIntArray;
                sparseIntArray.append(64, 8);
                sparseIntArray.append(65, 9);
                sparseIntArray.append(67, 10);
                sparseIntArray.append(68, 11);
                sparseIntArray.append(74, 12);
                sparseIntArray.append(73, 13);
                sparseIntArray.append(46, 14);
                sparseIntArray.append(45, 15);
                sparseIntArray.append(43, 16);
                sparseIntArray.append(47, 2);
                sparseIntArray.append(49, 3);
                sparseIntArray.append(48, 4);
                sparseIntArray.append(82, 49);
                sparseIntArray.append(83, 50);
                sparseIntArray.append(53, 5);
                sparseIntArray.append(54, 6);
                sparseIntArray.append(55, 7);
                sparseIntArray.append(0, 1);
                sparseIntArray.append(69, 17);
                sparseIntArray.append(70, 18);
                sparseIntArray.append(52, 19);
                sparseIntArray.append(51, 20);
                sparseIntArray.append(86, 21);
                sparseIntArray.append(89, 22);
                sparseIntArray.append(87, 23);
                sparseIntArray.append(84, 24);
                sparseIntArray.append(88, 25);
                sparseIntArray.append(85, 26);
                sparseIntArray.append(60, 29);
                sparseIntArray.append(75, 30);
                sparseIntArray.append(50, 44);
                sparseIntArray.append(62, 45);
                sparseIntArray.append(77, 46);
                sparseIntArray.append(61, 47);
                sparseIntArray.append(76, 48);
                sparseIntArray.append(41, 27);
                sparseIntArray.append(40, 28);
                sparseIntArray.append(78, 31);
                sparseIntArray.append(56, 32);
                sparseIntArray.append(80, 33);
                sparseIntArray.append(79, 34);
                sparseIntArray.append(81, 35);
                sparseIntArray.append(58, 36);
                sparseIntArray.append(57, 37);
                sparseIntArray.append(59, 38);
                sparseIntArray.append(63, 39);
                sparseIntArray.append(72, 40);
                sparseIntArray.append(66, 41);
                sparseIntArray.append(44, 42);
                sparseIntArray.append(42, 43);
                sparseIntArray.append(71, 51);
            }
        }

        public C0178a() {
            super(-2, -2);
            this.f1082a = -1;
            this.f1084b = -1;
            this.f1086c = -1.0f;
            this.f1088d = -1;
            this.f1090e = -1;
            this.f1092f = -1;
            this.f1094g = -1;
            this.f1096h = -1;
            this.f1098i = -1;
            this.f1100j = -1;
            this.f1102k = -1;
            this.f1104l = -1;
            this.f1106m = -1;
            this.f1107n = 0;
            this.f1108o = 0.0f;
            this.f1109p = -1;
            this.f1110q = -1;
            this.f1111r = -1;
            this.f1112s = -1;
            this.f1113t = -1;
            this.f1114u = -1;
            this.f1115v = -1;
            this.f1116w = -1;
            this.f1117x = -1;
            this.f1118y = -1;
            this.f1119z = 0.5f;
            this.f1056A = 0.5f;
            this.f1057B = null;
            this.f1058C = 1;
            this.f1059D = -1.0f;
            this.f1060E = -1.0f;
            this.f1061F = 0;
            this.f1062G = 0;
            this.f1063H = 0;
            this.f1064I = 0;
            this.f1065J = 0;
            this.f1066K = 0;
            this.f1067L = 0;
            this.f1068M = 0;
            this.f1069N = 1.0f;
            this.f1070O = 1.0f;
            this.f1071P = -1;
            this.f1072Q = -1;
            this.f1073R = -1;
            this.f1074S = false;
            this.f1075T = false;
            this.f1076U = null;
            this.f1077V = true;
            this.f1078W = true;
            this.f1079X = false;
            this.f1080Y = false;
            this.f1081Z = false;
            this.f1083a0 = false;
            this.f1085b0 = -1;
            this.f1087c0 = -1;
            this.f1089d0 = -1;
            this.f1091e0 = -1;
            this.f1093f0 = -1;
            this.f1095g0 = -1;
            this.f1097h0 = 0.5f;
            this.f1105l0 = new C1126d();
        }

        public C0178a(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
            String str;
            int i6;
            this.f1082a = -1;
            this.f1084b = -1;
            this.f1086c = -1.0f;
            this.f1088d = -1;
            this.f1090e = -1;
            this.f1092f = -1;
            this.f1094g = -1;
            this.f1096h = -1;
            this.f1098i = -1;
            this.f1100j = -1;
            this.f1102k = -1;
            this.f1104l = -1;
            this.f1106m = -1;
            this.f1107n = 0;
            this.f1108o = 0.0f;
            this.f1109p = -1;
            this.f1110q = -1;
            this.f1111r = -1;
            this.f1112s = -1;
            this.f1113t = -1;
            this.f1114u = -1;
            this.f1115v = -1;
            this.f1116w = -1;
            this.f1117x = -1;
            this.f1118y = -1;
            this.f1119z = 0.5f;
            this.f1056A = 0.5f;
            this.f1057B = null;
            this.f1058C = 1;
            this.f1059D = -1.0f;
            this.f1060E = -1.0f;
            this.f1061F = 0;
            this.f1062G = 0;
            this.f1063H = 0;
            this.f1064I = 0;
            this.f1065J = 0;
            this.f1066K = 0;
            this.f1067L = 0;
            this.f1068M = 0;
            this.f1069N = 1.0f;
            this.f1070O = 1.0f;
            this.f1071P = -1;
            this.f1072Q = -1;
            this.f1073R = -1;
            this.f1074S = false;
            this.f1075T = false;
            this.f1076U = null;
            this.f1077V = true;
            this.f1078W = true;
            this.f1079X = false;
            this.f1080Y = false;
            this.f1081Z = false;
            this.f1083a0 = false;
            this.f1085b0 = -1;
            this.f1087c0 = -1;
            this.f1089d0 = -1;
            this.f1091e0 = -1;
            this.f1093f0 = -1;
            this.f1095g0 = -1;
            this.f1097h0 = 0.5f;
            this.f1105l0 = new C1126d();
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C1798e.f7339m);
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i7 = 0; i7 < indexCount; i7++) {
                int index = obtainStyledAttributes.getIndex(i7);
                int i8 = a.f1120a.get(index);
                switch (i8) {
                    case 1:
                        this.f1073R = obtainStyledAttributes.getInt(index, this.f1073R);
                        continue;
                    case 2:
                        int resourceId = obtainStyledAttributes.getResourceId(index, this.f1106m);
                        this.f1106m = resourceId;
                        if (resourceId == -1) {
                            this.f1106m = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            continue;
                        }
                    case 3:
                        this.f1107n = obtainStyledAttributes.getDimensionPixelSize(index, this.f1107n);
                        continue;
                    case 4:
                        float f6 = obtainStyledAttributes.getFloat(index, this.f1108o) % 360.0f;
                        this.f1108o = f6;
                        if (f6 < 0.0f) {
                            this.f1108o = (360.0f - f6) % 360.0f;
                            break;
                        } else {
                            continue;
                        }
                    case 5:
                        this.f1082a = obtainStyledAttributes.getDimensionPixelOffset(index, this.f1082a);
                        continue;
                    case 6:
                        this.f1084b = obtainStyledAttributes.getDimensionPixelOffset(index, this.f1084b);
                        continue;
                    case 7:
                        this.f1086c = obtainStyledAttributes.getFloat(index, this.f1086c);
                        continue;
                    case 8:
                        int resourceId2 = obtainStyledAttributes.getResourceId(index, this.f1088d);
                        this.f1088d = resourceId2;
                        if (resourceId2 == -1) {
                            this.f1088d = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            continue;
                        }
                    case 9:
                        int resourceId3 = obtainStyledAttributes.getResourceId(index, this.f1090e);
                        this.f1090e = resourceId3;
                        if (resourceId3 == -1) {
                            this.f1090e = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            continue;
                        }
                    case 10:
                        int resourceId4 = obtainStyledAttributes.getResourceId(index, this.f1092f);
                        this.f1092f = resourceId4;
                        if (resourceId4 == -1) {
                            this.f1092f = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            continue;
                        }
                    case 11:
                        int resourceId5 = obtainStyledAttributes.getResourceId(index, this.f1094g);
                        this.f1094g = resourceId5;
                        if (resourceId5 == -1) {
                            this.f1094g = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            continue;
                        }
                    case 12:
                        int resourceId6 = obtainStyledAttributes.getResourceId(index, this.f1096h);
                        this.f1096h = resourceId6;
                        if (resourceId6 == -1) {
                            this.f1096h = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            continue;
                        }
                    case 13:
                        int resourceId7 = obtainStyledAttributes.getResourceId(index, this.f1098i);
                        this.f1098i = resourceId7;
                        if (resourceId7 == -1) {
                            this.f1098i = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            continue;
                        }
                    case 14:
                        int resourceId8 = obtainStyledAttributes.getResourceId(index, this.f1100j);
                        this.f1100j = resourceId8;
                        if (resourceId8 == -1) {
                            this.f1100j = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            continue;
                        }
                    case 15:
                        int resourceId9 = obtainStyledAttributes.getResourceId(index, this.f1102k);
                        this.f1102k = resourceId9;
                        if (resourceId9 == -1) {
                            this.f1102k = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            continue;
                        }
                    case 16:
                        int resourceId10 = obtainStyledAttributes.getResourceId(index, this.f1104l);
                        this.f1104l = resourceId10;
                        if (resourceId10 == -1) {
                            this.f1104l = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            continue;
                        }
                    case 17:
                        int resourceId11 = obtainStyledAttributes.getResourceId(index, this.f1109p);
                        this.f1109p = resourceId11;
                        if (resourceId11 == -1) {
                            this.f1109p = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            continue;
                        }
                    case 18:
                        int resourceId12 = obtainStyledAttributes.getResourceId(index, this.f1110q);
                        this.f1110q = resourceId12;
                        if (resourceId12 == -1) {
                            this.f1110q = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            continue;
                        }
                    case 19:
                        int resourceId13 = obtainStyledAttributes.getResourceId(index, this.f1111r);
                        this.f1111r = resourceId13;
                        if (resourceId13 == -1) {
                            this.f1111r = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            continue;
                        }
                    case 20:
                        int resourceId14 = obtainStyledAttributes.getResourceId(index, this.f1112s);
                        this.f1112s = resourceId14;
                        if (resourceId14 == -1) {
                            this.f1112s = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            continue;
                        }
                    case 21:
                        this.f1113t = obtainStyledAttributes.getDimensionPixelSize(index, this.f1113t);
                        continue;
                    case 22:
                        this.f1114u = obtainStyledAttributes.getDimensionPixelSize(index, this.f1114u);
                        continue;
                    case 23:
                        this.f1115v = obtainStyledAttributes.getDimensionPixelSize(index, this.f1115v);
                        continue;
                    case 24:
                        this.f1116w = obtainStyledAttributes.getDimensionPixelSize(index, this.f1116w);
                        continue;
                    case 25:
                        this.f1117x = obtainStyledAttributes.getDimensionPixelSize(index, this.f1117x);
                        continue;
                    case 26:
                        this.f1118y = obtainStyledAttributes.getDimensionPixelSize(index, this.f1118y);
                        continue;
                    case 27:
                        this.f1074S = obtainStyledAttributes.getBoolean(index, this.f1074S);
                        continue;
                    case 28:
                        this.f1075T = obtainStyledAttributes.getBoolean(index, this.f1075T);
                        continue;
                    case 29:
                        this.f1119z = obtainStyledAttributes.getFloat(index, this.f1119z);
                        continue;
                    case 30:
                        this.f1056A = obtainStyledAttributes.getFloat(index, this.f1056A);
                        continue;
                    case 31:
                        int i9 = obtainStyledAttributes.getInt(index, 0);
                        this.f1063H = i9;
                        if (i9 == 1) {
                            str = "layout_constraintWidth_default=\"wrap\" is deprecated.\nUse layout_width=\"WRAP_CONTENT\" and layout_constrainedWidth=\"true\" instead.";
                            break;
                        } else {
                            break;
                        }
                    case 32:
                        int i10 = obtainStyledAttributes.getInt(index, 0);
                        this.f1064I = i10;
                        if (i10 == 1) {
                            str = "layout_constraintHeight_default=\"wrap\" is deprecated.\nUse layout_height=\"WRAP_CONTENT\" and layout_constrainedHeight=\"true\" instead.";
                            break;
                        } else {
                            break;
                        }
                    case 33:
                        try {
                            this.f1065J = obtainStyledAttributes.getDimensionPixelSize(index, this.f1065J);
                            continue;
                        } catch (Exception unused) {
                            if (obtainStyledAttributes.getInt(index, this.f1065J) == -2) {
                                this.f1065J = -2;
                                break;
                            } else {
                                break;
                            }
                        }
                    case 34:
                        try {
                            this.f1067L = obtainStyledAttributes.getDimensionPixelSize(index, this.f1067L);
                            continue;
                        } catch (Exception unused2) {
                            if (obtainStyledAttributes.getInt(index, this.f1067L) == -2) {
                                this.f1067L = -2;
                                break;
                            } else {
                                break;
                            }
                        }
                    case 35:
                        this.f1069N = Math.max(0.0f, obtainStyledAttributes.getFloat(index, this.f1069N));
                        this.f1063H = 2;
                        continue;
                    case 36:
                        try {
                            this.f1066K = obtainStyledAttributes.getDimensionPixelSize(index, this.f1066K);
                            continue;
                        } catch (Exception unused3) {
                            if (obtainStyledAttributes.getInt(index, this.f1066K) == -2) {
                                this.f1066K = -2;
                                break;
                            } else {
                                break;
                            }
                        }
                    case 37:
                        try {
                            this.f1068M = obtainStyledAttributes.getDimensionPixelSize(index, this.f1068M);
                            continue;
                        } catch (Exception unused4) {
                            if (obtainStyledAttributes.getInt(index, this.f1068M) == -2) {
                                this.f1068M = -2;
                                break;
                            } else {
                                break;
                            }
                        }
                    case 38:
                        this.f1070O = Math.max(0.0f, obtainStyledAttributes.getFloat(index, this.f1070O));
                        this.f1064I = 2;
                        continue;
                    default:
                        switch (i8) {
                            case 44:
                                String string = obtainStyledAttributes.getString(index);
                                this.f1057B = string;
                                this.f1058C = -1;
                                if (string == null) {
                                    break;
                                } else {
                                    int length = string.length();
                                    int indexOf = this.f1057B.indexOf(44);
                                    if (indexOf <= 0 || indexOf >= length - 1) {
                                        i6 = 0;
                                    } else {
                                        String substring = this.f1057B.substring(0, indexOf);
                                        if (substring.equalsIgnoreCase("W")) {
                                            this.f1058C = 0;
                                        } else if (substring.equalsIgnoreCase("H")) {
                                            this.f1058C = 1;
                                        }
                                        i6 = indexOf + 1;
                                    }
                                    int indexOf2 = this.f1057B.indexOf(58);
                                    if (indexOf2 >= 0 && indexOf2 < length - 1) {
                                        String substring2 = this.f1057B.substring(i6, indexOf2);
                                        String substring3 = this.f1057B.substring(indexOf2 + 1);
                                        if (substring2.length() > 0 && substring3.length() > 0) {
                                            try {
                                                float parseFloat = Float.parseFloat(substring2);
                                                float parseFloat2 = Float.parseFloat(substring3);
                                                if (parseFloat > 0.0f && parseFloat2 > 0.0f) {
                                                    if (this.f1058C == 1) {
                                                        Math.abs(parseFloat2 / parseFloat);
                                                        break;
                                                    } else {
                                                        Math.abs(parseFloat / parseFloat2);
                                                        break;
                                                    }
                                                }
                                            } catch (NumberFormatException unused5) {
                                                break;
                                            }
                                        }
                                    } else {
                                        String substring4 = this.f1057B.substring(i6);
                                        if (substring4.length() <= 0) {
                                            break;
                                        } else {
                                            Float.parseFloat(substring4);
                                            continue;
                                        }
                                    }
                                }
                                break;
                            case 45:
                                this.f1059D = obtainStyledAttributes.getFloat(index, this.f1059D);
                                break;
                            case 46:
                                this.f1060E = obtainStyledAttributes.getFloat(index, this.f1060E);
                                break;
                            case 47:
                                this.f1061F = obtainStyledAttributes.getInt(index, 0);
                                break;
                            case 48:
                                this.f1062G = obtainStyledAttributes.getInt(index, 0);
                                break;
                            case 49:
                                this.f1071P = obtainStyledAttributes.getDimensionPixelOffset(index, this.f1071P);
                                break;
                            case 50:
                                this.f1072Q = obtainStyledAttributes.getDimensionPixelOffset(index, this.f1072Q);
                                break;
                            case 51:
                                this.f1076U = obtainStyledAttributes.getString(index);
                                break;
                        }
                }
                Log.e("ConstraintLayout", str);
            }
            obtainStyledAttributes.recycle();
            m526a();
        }

        public C0178a(ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
            this.f1082a = -1;
            this.f1084b = -1;
            this.f1086c = -1.0f;
            this.f1088d = -1;
            this.f1090e = -1;
            this.f1092f = -1;
            this.f1094g = -1;
            this.f1096h = -1;
            this.f1098i = -1;
            this.f1100j = -1;
            this.f1102k = -1;
            this.f1104l = -1;
            this.f1106m = -1;
            this.f1107n = 0;
            this.f1108o = 0.0f;
            this.f1109p = -1;
            this.f1110q = -1;
            this.f1111r = -1;
            this.f1112s = -1;
            this.f1113t = -1;
            this.f1114u = -1;
            this.f1115v = -1;
            this.f1116w = -1;
            this.f1117x = -1;
            this.f1118y = -1;
            this.f1119z = 0.5f;
            this.f1056A = 0.5f;
            this.f1057B = null;
            this.f1058C = 1;
            this.f1059D = -1.0f;
            this.f1060E = -1.0f;
            this.f1061F = 0;
            this.f1062G = 0;
            this.f1063H = 0;
            this.f1064I = 0;
            this.f1065J = 0;
            this.f1066K = 0;
            this.f1067L = 0;
            this.f1068M = 0;
            this.f1069N = 1.0f;
            this.f1070O = 1.0f;
            this.f1071P = -1;
            this.f1072Q = -1;
            this.f1073R = -1;
            this.f1074S = false;
            this.f1075T = false;
            this.f1076U = null;
            this.f1077V = true;
            this.f1078W = true;
            this.f1079X = false;
            this.f1080Y = false;
            this.f1081Z = false;
            this.f1083a0 = false;
            this.f1085b0 = -1;
            this.f1087c0 = -1;
            this.f1089d0 = -1;
            this.f1091e0 = -1;
            this.f1093f0 = -1;
            this.f1095g0 = -1;
            this.f1097h0 = 0.5f;
            this.f1105l0 = new C1126d();
        }

        /* renamed from: a */
        public final void m526a() {
            this.f1080Y = false;
            this.f1077V = true;
            this.f1078W = true;
            int i6 = ((ViewGroup.MarginLayoutParams) this).width;
            if (i6 == -2 && this.f1074S) {
                this.f1077V = false;
                if (this.f1063H == 0) {
                    this.f1063H = 1;
                }
            }
            int i7 = ((ViewGroup.MarginLayoutParams) this).height;
            if (i7 == -2 && this.f1075T) {
                this.f1078W = false;
                if (this.f1064I == 0) {
                    this.f1064I = 1;
                }
            }
            if (i6 == 0 || i6 == -1) {
                this.f1077V = false;
                if (i6 == 0 && this.f1063H == 1) {
                    ((ViewGroup.MarginLayoutParams) this).width = -2;
                    this.f1074S = true;
                }
            }
            if (i7 == 0 || i7 == -1) {
                this.f1078W = false;
                if (i7 == 0 && this.f1064I == 1) {
                    ((ViewGroup.MarginLayoutParams) this).height = -2;
                    this.f1075T = true;
                }
            }
            if (this.f1086c == -1.0f && this.f1082a == -1 && this.f1084b == -1) {
                return;
            }
            this.f1080Y = true;
            this.f1077V = true;
            this.f1078W = true;
            if (!(this.f1105l0 instanceof C1128f)) {
                this.f1105l0 = new C1128f();
            }
            ((C1128f) this.f1105l0).m2951O(this.f1073R);
        }

        /* JADX WARN: Code restructure failed: missing block: B:49:0x00ca, code lost:
        
            if (r1 > 0) goto L175;
         */
        /* JADX WARN: Code restructure failed: missing block: B:50:0x00cc, code lost:
        
            ((android.view.ViewGroup.MarginLayoutParams) r9).rightMargin = r1;
         */
        /* JADX WARN: Code restructure failed: missing block: B:73:0x00d9, code lost:
        
            if (r1 > 0) goto L175;
         */
        /* JADX WARN: Removed duplicated region for block: B:12:0x0048  */
        /* JADX WARN: Removed duplicated region for block: B:15:0x004f  */
        /* JADX WARN: Removed duplicated region for block: B:18:0x0056  */
        /* JADX WARN: Removed duplicated region for block: B:21:0x005c  */
        /* JADX WARN: Removed duplicated region for block: B:24:0x0062  */
        /* JADX WARN: Removed duplicated region for block: B:31:0x0074  */
        /* JADX WARN: Removed duplicated region for block: B:32:0x007c  */
        /* JADX WARN: Removed duplicated region for block: B:53:0x00e0  */
        /* JADX WARN: Removed duplicated region for block: B:61:0x00eb  */
        @Override // android.view.ViewGroup.MarginLayoutParams, android.view.ViewGroup.LayoutParams
        @android.annotation.TargetApi(17)
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct code enable 'Show inconsistent code' option in preferences
        */
        public final void resolveLayoutDirection(int r10) {
            /*
                Method dump skipped, instructions count: 249
                To view this dump change 'Code comments level' option to 'DEBUG'
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.widget.ConstraintLayout.C0178a.resolveLayoutDirection(int):void");
        }
    }

    /* renamed from: androidx.constraintlayout.widget.ConstraintLayout$b */
    public class C0179b implements C1215b.b {

        /* renamed from: a */
        public ConstraintLayout f1121a;

        /* renamed from: b */
        public int f1122b;

        /* renamed from: c */
        public int f1123c;

        /* renamed from: d */
        public int f1124d;

        /* renamed from: e */
        public int f1125e;

        /* renamed from: f */
        public int f1126f;

        /* renamed from: g */
        public int f1127g;

        public C0179b(ConstraintLayout constraintLayout) {
            this.f1121a = constraintLayout;
        }

        /* renamed from: a */
        public final boolean m527a(int i6, int i7, int i8) {
            if (i6 == i7) {
                return true;
            }
            int mode = View.MeasureSpec.getMode(i6);
            View.MeasureSpec.getSize(i6);
            int mode2 = View.MeasureSpec.getMode(i7);
            int size = View.MeasureSpec.getSize(i7);
            if (mode2 == 1073741824) {
                return (mode == Integer.MIN_VALUE || mode == 0) && i8 == size;
            }
            return false;
        }

        /* JADX WARN: Removed duplicated region for block: B:153:0x01bd  */
        /* JADX WARN: Removed duplicated region for block: B:154:0x01b8  */
        /* JADX WARN: Removed duplicated region for block: B:55:0x01a3  */
        /* JADX WARN: Removed duplicated region for block: B:59:0x01b6  */
        /* JADX WARN: Removed duplicated region for block: B:61:0x01bb  */
        /* JADX WARN: Removed duplicated region for block: B:79:0x01e8 A[RETURN] */
        /* JADX WARN: Removed duplicated region for block: B:80:0x01e9  */
        @android.annotation.SuppressLint({"WrongCall"})
        /* renamed from: b */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct code enable 'Show inconsistent code' option in preferences
        */
        public final void m528b(p096o.C1126d r18, p103p.C1215b.a r19) {
            /*
                Method dump skipped, instructions count: 730
                To view this dump change 'Code comments level' option to 'DEBUG'
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.widget.ConstraintLayout.C0179b.m528b(o.d, p.b$a):void");
        }
    }

    public ConstraintLayout(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.f1039j = new SparseArray<>();
        this.f1040k = new ArrayList<>(4);
        this.f1041l = new C1127e();
        this.f1042m = 0;
        this.f1043n = 0;
        this.f1044o = Integer.MAX_VALUE;
        this.f1045p = Integer.MAX_VALUE;
        this.f1046q = true;
        this.f1047r = 257;
        this.f1048s = null;
        this.f1049t = null;
        this.f1050u = -1;
        this.f1051v = new HashMap<>();
        this.f1052w = new SparseArray<>();
        this.f1053x = new C0179b(this);
        this.f1054y = 0;
        this.f1055z = 0;
        m521f(attributeSet, 0);
    }

    public ConstraintLayout(Context context, AttributeSet attributeSet, int i6) {
        super(context, attributeSet, i6);
        this.f1039j = new SparseArray<>();
        this.f1040k = new ArrayList<>(4);
        this.f1041l = new C1127e();
        this.f1042m = 0;
        this.f1043n = 0;
        this.f1044o = Integer.MAX_VALUE;
        this.f1045p = Integer.MAX_VALUE;
        this.f1046q = true;
        this.f1047r = 257;
        this.f1048s = null;
        this.f1049t = null;
        this.f1050u = -1;
        this.f1051v = new HashMap<>();
        this.f1052w = new SparseArray<>();
        this.f1053x = new C0179b(this);
        this.f1054y = 0;
        this.f1055z = 0;
        m521f(attributeSet, i6);
    }

    private int getPaddingWidth() {
        int max = Math.max(0, getPaddingRight()) + Math.max(0, getPaddingLeft());
        int max2 = Math.max(0, getPaddingEnd()) + Math.max(0, getPaddingStart());
        return max2 > 0 ? max2 : max;
    }

    @Override // android.view.ViewGroup
    public void addView(View view, int i6, ViewGroup.LayoutParams layoutParams) {
        super.addView(view, i6, layoutParams);
    }

    @Override // android.view.ViewGroup
    /* renamed from: b */
    public final C0178a generateDefaultLayoutParams() {
        return new C0178a();
    }

    /* renamed from: c */
    public final Object m518c(Object obj) {
        if (!(obj instanceof String)) {
            return null;
        }
        String str = (String) obj;
        HashMap<String, Integer> hashMap = this.f1051v;
        if (hashMap == null || !hashMap.containsKey(str)) {
            return null;
        }
        return this.f1051v.get(str);
    }

    @Override // android.view.ViewGroup
    public final boolean checkLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return layoutParams instanceof C0178a;
    }

    /* renamed from: d */
    public final View m519d(int i6) {
        return this.f1039j.get(i6);
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void dispatchDraw(Canvas canvas) {
        Object tag;
        int size;
        ArrayList<AbstractC0180a> arrayList = this.f1040k;
        if (arrayList != null && (size = arrayList.size()) > 0) {
            for (int i6 = 0; i6 < size; i6++) {
                Objects.requireNonNull(this.f1040k.get(i6));
            }
        }
        super.dispatchDraw(canvas);
        if (isInEditMode()) {
            int childCount = getChildCount();
            float width = getWidth();
            float height = getHeight();
            for (int i7 = 0; i7 < childCount; i7++) {
                View childAt = getChildAt(i7);
                if (childAt.getVisibility() != 8 && (tag = childAt.getTag()) != null && (tag instanceof String)) {
                    String[] split = ((String) tag).split(",");
                    if (split.length == 4) {
                        int parseInt = Integer.parseInt(split[0]);
                        int parseInt2 = Integer.parseInt(split[1]);
                        int parseInt3 = Integer.parseInt(split[2]);
                        int i8 = (int) ((parseInt / 1080.0f) * width);
                        int i9 = (int) ((parseInt2 / 1920.0f) * height);
                        Paint paint = new Paint();
                        paint.setColor(-65536);
                        float f6 = i8;
                        float f7 = i9;
                        float f8 = i8 + ((int) ((parseInt3 / 1080.0f) * width));
                        canvas.drawLine(f6, f7, f8, f7, paint);
                        float parseInt4 = i9 + ((int) ((Integer.parseInt(split[3]) / 1920.0f) * height));
                        canvas.drawLine(f8, f7, f8, parseInt4, paint);
                        canvas.drawLine(f8, parseInt4, f6, parseInt4, paint);
                        canvas.drawLine(f6, parseInt4, f6, f7, paint);
                        paint.setColor(-16711936);
                        canvas.drawLine(f6, f7, f8, parseInt4, paint);
                        canvas.drawLine(f6, parseInt4, f8, f7, paint);
                    }
                }
            }
        }
    }

    /* renamed from: e */
    public final C1126d m520e(View view) {
        if (view == this) {
            return this.f1041l;
        }
        if (view == null) {
            return null;
        }
        return ((C0178a) view.getLayoutParams()).f1105l0;
    }

    /* renamed from: f */
    public final void m521f(AttributeSet attributeSet, int i6) {
        C1127e c1127e = this.f1041l;
        c1127e.f5410b0 = this;
        C0179b c0179b = this.f1053x;
        c1127e.f5451o0 = c0179b;
        c1127e.f5450n0.f5798f = c0179b;
        this.f1039j.put(getId(), this);
        this.f1048s = null;
        if (attributeSet != null) {
            TypedArray obtainStyledAttributes = getContext().obtainStyledAttributes(attributeSet, C1798e.f7339m, i6, 0);
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i7 = 0; i7 < indexCount; i7++) {
                int index = obtainStyledAttributes.getIndex(i7);
                if (index == 9) {
                    this.f1042m = obtainStyledAttributes.getDimensionPixelOffset(index, this.f1042m);
                } else if (index == 10) {
                    this.f1043n = obtainStyledAttributes.getDimensionPixelOffset(index, this.f1043n);
                } else if (index == 7) {
                    this.f1044o = obtainStyledAttributes.getDimensionPixelOffset(index, this.f1044o);
                } else if (index == 8) {
                    this.f1045p = obtainStyledAttributes.getDimensionPixelOffset(index, this.f1045p);
                } else if (index == 90) {
                    this.f1047r = obtainStyledAttributes.getInt(index, this.f1047r);
                } else if (index == 39) {
                    int resourceId = obtainStyledAttributes.getResourceId(index, 0);
                    if (resourceId != 0) {
                        try {
                            this.f1049t = new C1249b(getContext(), resourceId);
                        } catch (Resources.NotFoundException unused) {
                            this.f1049t = null;
                        }
                    }
                } else if (index == 18) {
                    int resourceId2 = obtainStyledAttributes.getResourceId(index, 0);
                    try {
                        C0181b c0181b = new C0181b();
                        this.f1048s = c0181b;
                        c0181b.m540e(getContext(), resourceId2);
                    } catch (Resources.NotFoundException unused2) {
                        this.f1048s = null;
                    }
                    this.f1050u = resourceId2;
                }
            }
            obtainStyledAttributes.recycle();
        }
        this.f1041l.m2949X(this.f1047r);
    }

    @Override // android.view.View
    public final void forceLayout() {
        this.f1046q = true;
        super.forceLayout();
    }

    /* renamed from: g */
    public final boolean m522g() {
        return ((getContext().getApplicationInfo().flags & 4194304) != 0) && 1 == getLayoutDirection();
    }

    @Override // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        return new C0178a(getContext(), attributeSet);
    }

    @Override // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return new C0178a(layoutParams);
    }

    public int getMaxHeight() {
        return this.f1045p;
    }

    public int getMaxWidth() {
        return this.f1044o;
    }

    public int getMinHeight() {
        return this.f1043n;
    }

    public int getMinWidth() {
        return this.f1042m;
    }

    public int getOptimizationLevel() {
        return this.f1041l.f5460x0;
    }

    /* renamed from: h */
    public final void m523h(int i6, int i7, int i8, int i9, boolean z5, boolean z6) {
        C0179b c0179b = this.f1053x;
        int i10 = c0179b.f1125e;
        int resolveSizeAndState = View.resolveSizeAndState(i8 + c0179b.f1124d, i6, 0);
        int resolveSizeAndState2 = View.resolveSizeAndState(i9 + i10, i7, 0) & 16777215;
        int min = Math.min(this.f1044o, resolveSizeAndState & 16777215);
        int min2 = Math.min(this.f1045p, resolveSizeAndState2);
        if (z5) {
            min |= 16777216;
        }
        if (z6) {
            min2 |= 16777216;
        }
        setMeasuredDimension(min, min2);
    }

    /* renamed from: i */
    public final void m524i(Object obj, Object obj2) {
        if ((obj instanceof String) && (obj2 instanceof Integer)) {
            if (this.f1051v == null) {
                this.f1051v = new HashMap<>();
            }
            String str = (String) obj;
            int indexOf = str.indexOf("/");
            if (indexOf != -1) {
                str = str.substring(indexOf + 1);
            }
            this.f1051v.put(str, Integer.valueOf(((Integer) obj2).intValue()));
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:273:0x0334  */
    /* JADX WARN: Removed duplicated region for block: B:279:0x0366  */
    /* JADX WARN: Removed duplicated region for block: B:285:0x0399  */
    /* JADX WARN: Removed duplicated region for block: B:293:0x03f1  */
    /* JADX WARN: Removed duplicated region for block: B:296:0x03fb  */
    /* JADX WARN: Removed duplicated region for block: B:298:0x03e8  */
    /* JADX WARN: Removed duplicated region for block: B:299:0x0378  */
    /* JADX WARN: Removed duplicated region for block: B:304:0x0346  */
    /* renamed from: j */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean m525j() {
        /*
            Method dump skipped, instructions count: 1434
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.widget.ConstraintLayout.m525j():boolean");
    }

    @Override // android.view.ViewGroup, android.view.View
    public void onLayout(boolean z5, int i6, int i7, int i8, int i9) {
        View content;
        int childCount = getChildCount();
        boolean isInEditMode = isInEditMode();
        for (int i10 = 0; i10 < childCount; i10++) {
            View childAt = getChildAt(i10);
            C0178a c0178a = (C0178a) childAt.getLayoutParams();
            C1126d c1126d = c0178a.f1105l0;
            if ((childAt.getVisibility() != 8 || c0178a.f1080Y || c0178a.f1081Z || isInEditMode) && !c0178a.f1083a0) {
                int m2930p = c1126d.m2930p();
                int m2931q = c1126d.m2931q();
                int m2929o = c1126d.m2929o() + m2930p;
                int m2925k = c1126d.m2925k() + m2931q;
                childAt.layout(m2930p, m2931q, m2929o, m2925k);
                if ((childAt instanceof C0183d) && (content = ((C0183d) childAt).getContent()) != null) {
                    content.setVisibility(0);
                    content.layout(m2930p, m2931q, m2929o, m2925k);
                }
            }
        }
        int size = this.f1040k.size();
        if (size > 0) {
            for (int i11 = 0; i11 < size; i11++) {
                Objects.requireNonNull(this.f1040k.get(i11));
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:158:0x0507  */
    /* JADX WARN: Removed duplicated region for block: B:164:0x0515  */
    /* JADX WARN: Removed duplicated region for block: B:213:0x05ad  */
    /* JADX WARN: Removed duplicated region for block: B:216:0x05b1 A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:251:0x0636  */
    /* JADX WARN: Removed duplicated region for block: B:253:0x063b  */
    /* JADX WARN: Removed duplicated region for block: B:361:0x081e  */
    /* JADX WARN: Removed duplicated region for block: B:426:0x04f9  */
    /* JADX WARN: Removed duplicated region for block: B:455:0x014d  */
    /* JADX WARN: Removed duplicated region for block: B:51:0x0135  */
    /* JADX WARN: Removed duplicated region for block: B:58:0x0161  */
    /* JADX WARN: Removed duplicated region for block: B:63:0x01cf  */
    /* JADX WARN: Removed duplicated region for block: B:67:0x01db  */
    /* JADX WARN: Removed duplicated region for block: B:86:0x0240 A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:90:0x024b  */
    @Override // android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void onMeasure(int r28, int r29) {
        /*
            Method dump skipped, instructions count: 2117
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.widget.ConstraintLayout.onMeasure(int, int):void");
    }

    @Override // android.view.ViewGroup
    public final void onViewAdded(View view) {
        super.onViewAdded(view);
        C1126d m520e = m520e(view);
        if ((view instanceof Guideline) && !(m520e instanceof C1128f)) {
            C0178a c0178a = (C0178a) view.getLayoutParams();
            C1128f c1128f = new C1128f();
            c0178a.f1105l0 = c1128f;
            c0178a.f1080Y = true;
            c1128f.m2951O(c0178a.f1073R);
        }
        if (view instanceof AbstractC0180a) {
            AbstractC0180a abstractC0180a = (AbstractC0180a) view;
            abstractC0180a.m534h();
            ((C0178a) view.getLayoutParams()).f1081Z = true;
            if (!this.f1040k.contains(abstractC0180a)) {
                this.f1040k.add(abstractC0180a);
            }
        }
        this.f1039j.put(view.getId(), view);
        this.f1046q = true;
    }

    @Override // android.view.ViewGroup
    public void onViewRemoved(View view) {
        super.onViewRemoved(view);
        this.f1039j.remove(view.getId());
        C1126d m520e = m520e(view);
        this.f1041l.f5471l0.remove(m520e);
        m520e.mo2938z();
        this.f1040k.remove(view);
        this.f1046q = true;
    }

    @Override // android.view.ViewGroup, android.view.ViewManager
    public final void removeView(View view) {
        super.removeView(view);
    }

    @Override // android.view.View, android.view.ViewParent
    public final void requestLayout() {
        this.f1046q = true;
        super.requestLayout();
    }

    public void setConstraintSet(C0181b c0181b) {
        this.f1048s = c0181b;
    }

    @Override // android.view.View
    public void setId(int i6) {
        this.f1039j.remove(getId());
        super.setId(i6);
        this.f1039j.put(getId(), this);
    }

    public void setMaxHeight(int i6) {
        if (i6 == this.f1045p) {
            return;
        }
        this.f1045p = i6;
        requestLayout();
    }

    public void setMaxWidth(int i6) {
        if (i6 == this.f1044o) {
            return;
        }
        this.f1044o = i6;
        requestLayout();
    }

    public void setMinHeight(int i6) {
        if (i6 == this.f1043n) {
            return;
        }
        this.f1043n = i6;
        requestLayout();
    }

    public void setMinWidth(int i6) {
        if (i6 == this.f1042m) {
            return;
        }
        this.f1042m = i6;
        requestLayout();
    }

    public void setOnConstraintsChanged(AbstractC1250c abstractC1250c) {
    }

    public void setOptimizationLevel(int i6) {
        this.f1047r = i6;
        this.f1041l.m2949X(i6);
    }

    @Override // android.view.ViewGroup
    public final boolean shouldDelayChildPressedState() {
        return false;
    }
}
