package androidx.constraintlayout.widget;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import androidx.activity.result.C0052a;
import androidx.constraintlayout.widget.ConstraintLayout;
import java.util.Arrays;
import java.util.HashMap;
import p096o.C1123a;
import p096o.C1126d;
import p110q.C1251d;
import p153w3.C1798e;

/* renamed from: androidx.constraintlayout.widget.a */
/* loaded from: classes.dex */
public abstract class AbstractC0180a extends View {

    /* renamed from: j */
    public int[] f1129j;

    /* renamed from: k */
    public int f1130k;

    /* renamed from: l */
    public Context f1131l;

    /* renamed from: m */
    public C1123a f1132m;

    /* renamed from: n */
    public String f1133n;

    /* renamed from: o */
    public String f1134o;

    /* renamed from: p */
    public HashMap<Integer, String> f1135p;

    public AbstractC0180a(Context context) {
        super(context);
        this.f1129j = new int[32];
        this.f1135p = new HashMap<>();
        this.f1131l = context;
        mo515f(null);
    }

    public AbstractC0180a(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.f1129j = new int[32];
        this.f1135p = new HashMap<>();
        this.f1131l = context;
        mo515f(attributeSet);
    }

    /* renamed from: a */
    public final void m529a(String str) {
        if (str == null || str.length() == 0 || this.f1131l == null) {
            return;
        }
        String trim = str.trim();
        if (getParent() instanceof ConstraintLayout) {
        }
        ConstraintLayout constraintLayout = getParent() instanceof ConstraintLayout ? (ConstraintLayout) getParent() : null;
        int i6 = 0;
        if (isInEditMode() && constraintLayout != null) {
            Object m518c = constraintLayout.m518c(trim);
            if (m518c instanceof Integer) {
                i6 = ((Integer) m518c).intValue();
            }
        }
        if (i6 == 0 && constraintLayout != null) {
            i6 = m533e(constraintLayout, trim);
        }
        if (i6 == 0) {
            try {
                i6 = C1251d.class.getField(trim).getInt(null);
            } catch (Exception unused) {
            }
        }
        if (i6 == 0) {
            i6 = this.f1131l.getResources().getIdentifier(trim, "id", this.f1131l.getPackageName());
        }
        if (i6 != 0) {
            this.f1135p.put(Integer.valueOf(i6), trim);
            m530b(i6);
            return;
        }
        Log.w("ConstraintHelper", "Could not find id of \"" + trim + "\"");
    }

    /* renamed from: b */
    public final void m530b(int i6) {
        if (i6 == getId()) {
            return;
        }
        int i7 = this.f1130k + 1;
        int[] iArr = this.f1129j;
        if (i7 > iArr.length) {
            this.f1129j = Arrays.copyOf(iArr, iArr.length * 2);
        }
        int[] iArr2 = this.f1129j;
        int i8 = this.f1130k;
        iArr2[i8] = i6;
        this.f1130k = i8 + 1;
    }

    /* renamed from: c */
    public final void m531c(String str) {
        if (str == null || str.length() == 0 || this.f1131l == null) {
            return;
        }
        String trim = str.trim();
        ConstraintLayout constraintLayout = getParent() instanceof ConstraintLayout ? (ConstraintLayout) getParent() : null;
        if (constraintLayout == null) {
            Log.w("ConstraintHelper", "Parent not a ConstraintLayout");
            return;
        }
        int childCount = constraintLayout.getChildCount();
        for (int i6 = 0; i6 < childCount; i6++) {
            View childAt = constraintLayout.getChildAt(i6);
            ViewGroup.LayoutParams layoutParams = childAt.getLayoutParams();
            if ((layoutParams instanceof ConstraintLayout.C0178a) && trim.equals(((ConstraintLayout.C0178a) layoutParams).f1076U)) {
                if (childAt.getId() == -1) {
                    StringBuilder m104h = C0052a.m104h("to use ConstraintTag view ");
                    m104h.append(childAt.getClass().getSimpleName());
                    m104h.append(" must have an ID");
                    Log.w("ConstraintHelper", m104h.toString());
                } else {
                    m530b(childAt.getId());
                }
            }
        }
    }

    /* renamed from: d */
    public final void m532d() {
        ViewParent parent = getParent();
        if (parent == null || !(parent instanceof ConstraintLayout)) {
            return;
        }
        ConstraintLayout constraintLayout = (ConstraintLayout) parent;
        int visibility = getVisibility();
        float elevation = getElevation();
        for (int i6 = 0; i6 < this.f1130k; i6++) {
            View m519d = constraintLayout.m519d(this.f1129j[i6]);
            if (m519d != null) {
                m519d.setVisibility(visibility);
                if (elevation > 0.0f) {
                    m519d.setTranslationZ(m519d.getTranslationZ() + elevation);
                }
            }
        }
    }

    /* renamed from: e */
    public final int m533e(ConstraintLayout constraintLayout, String str) {
        Resources resources;
        if (str == null || constraintLayout == null || (resources = this.f1131l.getResources()) == null) {
            return 0;
        }
        int childCount = constraintLayout.getChildCount();
        for (int i6 = 0; i6 < childCount; i6++) {
            View childAt = constraintLayout.getChildAt(i6);
            if (childAt.getId() != -1) {
                String str2 = null;
                try {
                    str2 = resources.getResourceEntryName(childAt.getId());
                } catch (Resources.NotFoundException unused) {
                }
                if (str.equals(str2)) {
                    return childAt.getId();
                }
            }
        }
        return 0;
    }

    /* renamed from: f */
    public void mo515f(AttributeSet attributeSet) {
        if (attributeSet != null) {
            TypedArray obtainStyledAttributes = getContext().obtainStyledAttributes(attributeSet, C1798e.f7339m);
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i6 = 0; i6 < indexCount; i6++) {
                int index = obtainStyledAttributes.getIndex(i6);
                if (index == 19) {
                    String string = obtainStyledAttributes.getString(index);
                    this.f1133n = string;
                    setIds(string);
                } else if (index == 20) {
                    String string2 = obtainStyledAttributes.getString(index);
                    this.f1134o = string2;
                    setReferenceTags(string2);
                }
            }
            obtainStyledAttributes.recycle();
        }
    }

    /* renamed from: g */
    public void mo516g(C1126d c1126d, boolean z5) {
    }

    public int[] getReferencedIds() {
        return Arrays.copyOf(this.f1129j, this.f1130k);
    }

    /* renamed from: h */
    public final void m534h() {
        if (this.f1132m == null) {
            return;
        }
        ViewGroup.LayoutParams layoutParams = getLayoutParams();
        if (layoutParams instanceof ConstraintLayout.C0178a) {
            ((ConstraintLayout.C0178a) layoutParams).f1105l0 = this.f1132m;
        }
    }

    @Override // android.view.View
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        String str = this.f1133n;
        if (str != null) {
            setIds(str);
        }
        String str2 = this.f1134o;
        if (str2 != null) {
            setReferenceTags(str2);
        }
    }

    @Override // android.view.View
    public final void onDraw(Canvas canvas) {
    }

    @Override // android.view.View
    public final void onMeasure(int i6, int i7) {
        setMeasuredDimension(0, 0);
    }

    public void setIds(String str) {
        this.f1133n = str;
        if (str == null) {
            return;
        }
        int i6 = 0;
        this.f1130k = 0;
        while (true) {
            int indexOf = str.indexOf(44, i6);
            if (indexOf == -1) {
                m529a(str.substring(i6));
                return;
            } else {
                m529a(str.substring(i6, indexOf));
                i6 = indexOf + 1;
            }
        }
    }

    public void setReferenceTags(String str) {
        this.f1134o = str;
        if (str == null) {
            return;
        }
        int i6 = 0;
        this.f1130k = 0;
        while (true) {
            int indexOf = str.indexOf(44, i6);
            if (indexOf == -1) {
                m531c(str.substring(i6));
                return;
            } else {
                m531c(str.substring(i6, indexOf));
                i6 = indexOf + 1;
            }
        }
    }

    public void setReferencedIds(int[] iArr) {
        this.f1133n = null;
        this.f1130k = 0;
        for (int i6 : iArr) {
            m530b(i6);
        }
    }

    @Override // android.view.View
    public final void setTag(int i6, Object obj) {
        super.setTag(i6, obj);
        if (obj == null && this.f1133n == null) {
            m530b(i6);
        }
    }
}
