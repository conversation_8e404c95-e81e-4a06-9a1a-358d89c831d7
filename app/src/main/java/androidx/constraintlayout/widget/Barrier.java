package androidx.constraintlayout.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import p096o.C1123a;
import p153w3.C1798e;

/* loaded from: classes.dex */
public class Barrier extends AbstractC0180a {

    /* renamed from: q */
    public int f1036q;

    /* renamed from: r */
    public int f1037r;

    /* renamed from: s */
    public C1123a f1038s;

    public Barrier(Context context) {
        super(context);
        super.setVisibility(8);
    }

    public Barrier(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        super.setVisibility(8);
    }

    @Override // androidx.constraintlayout.widget.AbstractC0180a
    /* renamed from: f */
    public final void mo515f(AttributeSet attributeSet) {
        super.mo515f(attributeSet);
        this.f1038s = new C1123a();
        if (attributeSet != null) {
            TypedArray obtainStyledAttributes = getContext().obtainStyledAttributes(attributeSet, C1798e.f7339m);
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i6 = 0; i6 < indexCount; i6++) {
                int index = obtainStyledAttributes.getIndex(i6);
                if (index == 15) {
                    setType(obtainStyledAttributes.getInt(index, 0));
                } else if (index == 14) {
                    this.f1038s.f5343o0 = obtainStyledAttributes.getBoolean(index, true);
                } else if (index == 16) {
                    this.f1038s.f5344p0 = obtainStyledAttributes.getDimensionPixelSize(index, 0);
                }
            }
            obtainStyledAttributes.recycle();
        }
        this.f1132m = this.f1038s;
        m534h();
    }

    /* JADX WARN: Code restructure failed: missing block: B:15:0x0014, code lost:
    
        if (r0 == 6) goto L12;
     */
    /* JADX WARN: Code restructure failed: missing block: B:4:0x000b, code lost:
    
        if (r0 == 6) goto L9;
     */
    @Override // androidx.constraintlayout.widget.AbstractC0180a
    /* renamed from: g */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void mo516g(p096o.C1126d r4, boolean r5) {
        /*
            r3 = this;
            int r0 = r3.f1036q
            r3.f1037r = r0
            r1 = 6
            r2 = 5
            if (r5 == 0) goto Le
            if (r0 != r2) goto Lb
            goto L16
        Lb:
            if (r0 != r1) goto L18
            goto L10
        Le:
            if (r0 != r2) goto L14
        L10:
            r5 = 0
        L11:
            r3.f1037r = r5
            goto L18
        L14:
            if (r0 != r1) goto L18
        L16:
            r5 = 1
            goto L11
        L18:
            boolean r5 = r4 instanceof p096o.C1123a
            if (r5 == 0) goto L22
            o.a r4 = (p096o.C1123a) r4
            int r5 = r3.f1037r
            r4.f5342n0 = r5
        L22:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.widget.Barrier.mo516g(o.d, boolean):void");
    }

    public int getMargin() {
        return this.f1038s.f5344p0;
    }

    public int getType() {
        return this.f1036q;
    }

    public void setAllowsGoneWidget(boolean z5) {
        this.f1038s.f5343o0 = z5;
    }

    public void setDpMargin(int i6) {
        this.f1038s.f5344p0 = (int) ((i6 * getResources().getDisplayMetrics().density) + 0.5f);
    }

    public void setMargin(int i6) {
        this.f1038s.f5344p0 = i6;
    }

    public void setType(int i6) {
        this.f1036q = i6;
    }
}
