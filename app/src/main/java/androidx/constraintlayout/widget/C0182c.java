package androidx.constraintlayout.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import androidx.constraintlayout.widget.C0181b;
import androidx.constraintlayout.widget.ConstraintLayout;
import java.util.Objects;
import p153w3.C1798e;

/* renamed from: androidx.constraintlayout.widget.c */
/* loaded from: classes.dex */
public final class C0182c extends ViewGroup {

    /* renamed from: j */
    public C0181b f1231j;

    /* renamed from: androidx.constraintlayout.widget.c$a */
    public static class a extends ConstraintLayout.C0178a {

        /* renamed from: m0 */
        public float f1232m0;

        /* renamed from: n0 */
        public boolean f1233n0;

        /* renamed from: o0 */
        public float f1234o0;

        /* renamed from: p0 */
        public float f1235p0;

        /* renamed from: q0 */
        public float f1236q0;

        /* renamed from: r0 */
        public float f1237r0;

        /* renamed from: s0 */
        public float f1238s0;

        /* renamed from: t0 */
        public float f1239t0;

        /* renamed from: u0 */
        public float f1240u0;

        /* renamed from: v0 */
        public float f1241v0;

        /* renamed from: w0 */
        public float f1242w0;

        /* renamed from: x0 */
        public float f1243x0;

        /* renamed from: y0 */
        public float f1244y0;

        public a() {
            this.f1232m0 = 1.0f;
            this.f1233n0 = false;
            this.f1234o0 = 0.0f;
            this.f1235p0 = 0.0f;
            this.f1236q0 = 0.0f;
            this.f1237r0 = 0.0f;
            this.f1238s0 = 1.0f;
            this.f1239t0 = 1.0f;
            this.f1240u0 = 0.0f;
            this.f1241v0 = 0.0f;
            this.f1242w0 = 0.0f;
            this.f1243x0 = 0.0f;
            this.f1244y0 = 0.0f;
        }

        public a(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
            this.f1232m0 = 1.0f;
            this.f1233n0 = false;
            this.f1234o0 = 0.0f;
            this.f1235p0 = 0.0f;
            this.f1236q0 = 0.0f;
            this.f1237r0 = 0.0f;
            this.f1238s0 = 1.0f;
            this.f1239t0 = 1.0f;
            this.f1240u0 = 0.0f;
            this.f1241v0 = 0.0f;
            this.f1242w0 = 0.0f;
            this.f1243x0 = 0.0f;
            this.f1244y0 = 0.0f;
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C1798e.f7340n);
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i6 = 0; i6 < indexCount; i6++) {
                int index = obtainStyledAttributes.getIndex(i6);
                if (index == 15) {
                    this.f1232m0 = obtainStyledAttributes.getFloat(index, this.f1232m0);
                } else if (index == 28) {
                    this.f1234o0 = obtainStyledAttributes.getFloat(index, this.f1234o0);
                    this.f1233n0 = true;
                } else if (index == 23) {
                    this.f1236q0 = obtainStyledAttributes.getFloat(index, this.f1236q0);
                } else if (index == 24) {
                    this.f1237r0 = obtainStyledAttributes.getFloat(index, this.f1237r0);
                } else if (index == 22) {
                    this.f1235p0 = obtainStyledAttributes.getFloat(index, this.f1235p0);
                } else if (index == 20) {
                    this.f1238s0 = obtainStyledAttributes.getFloat(index, this.f1238s0);
                } else if (index == 21) {
                    this.f1239t0 = obtainStyledAttributes.getFloat(index, this.f1239t0);
                } else if (index == 16) {
                    this.f1240u0 = obtainStyledAttributes.getFloat(index, this.f1240u0);
                } else if (index == 17) {
                    this.f1241v0 = obtainStyledAttributes.getFloat(index, this.f1241v0);
                } else if (index == 18) {
                    this.f1242w0 = obtainStyledAttributes.getFloat(index, this.f1242w0);
                } else if (index == 19) {
                    this.f1243x0 = obtainStyledAttributes.getFloat(index, this.f1243x0);
                } else if (index == 27) {
                    this.f1244y0 = obtainStyledAttributes.getFloat(index, this.f1244y0);
                }
            }
            obtainStyledAttributes.recycle();
        }
    }

    @Override // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateDefaultLayoutParams() {
        return new a();
    }

    @Override // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        return new a(getContext(), attributeSet);
    }

    @Override // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return new ConstraintLayout.C0178a(layoutParams);
    }

    public C0181b getConstraintSet() {
        if (this.f1231j == null) {
            this.f1231j = new C0181b();
        }
        C0181b c0181b = this.f1231j;
        Objects.requireNonNull(c0181b);
        int childCount = getChildCount();
        c0181b.f1140c.clear();
        for (int i6 = 0; i6 < childCount; i6++) {
            View childAt = getChildAt(i6);
            a aVar = (a) childAt.getLayoutParams();
            int id = childAt.getId();
            if (c0181b.f1139b && id == -1) {
                throw new RuntimeException("All children of ConstraintLayout must have ids to use ConstraintSet");
            }
            if (!c0181b.f1140c.containsKey(Integer.valueOf(id))) {
                c0181b.f1140c.put(Integer.valueOf(id), new C0181b.a());
            }
            C0181b.a aVar2 = c0181b.f1140c.get(Integer.valueOf(id));
            if (childAt instanceof AbstractC0180a) {
                AbstractC0180a abstractC0180a = (AbstractC0180a) childAt;
                aVar2.m543c(id, aVar);
                if (abstractC0180a instanceof Barrier) {
                    C0181b.b bVar = aVar2.f1144d;
                    bVar.f1179c0 = 1;
                    Barrier barrier = (Barrier) abstractC0180a;
                    bVar.f1175a0 = barrier.getType();
                    aVar2.f1144d.f1181d0 = barrier.getReferencedIds();
                    aVar2.f1144d.f1177b0 = barrier.getMargin();
                }
            }
            aVar2.m543c(id, aVar);
        }
        return this.f1231j;
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onLayout(boolean z5, int i6, int i7, int i8, int i9) {
    }
}
