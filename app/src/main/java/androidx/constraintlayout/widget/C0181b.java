package androidx.constraintlayout.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.content.res.XmlResourceParser;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseIntArray;
import android.util.Xml;
import android.view.View;
import android.view.ViewGroup;
import androidx.constraintlayout.widget.C0182c;
import androidx.constraintlayout.widget.ConstraintLayout;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Objects;
import org.xmlpull.v1.XmlPullParserException;
import p008b0.C0385m;
import p090n.C1094g;
import p110q.C1248a;
import p110q.C1251d;
import p153w3.C1798e;

/* renamed from: androidx.constraintlayout.widget.b */
/* loaded from: classes.dex */
public final class C0181b {

    /* renamed from: d */
    public static final int[] f1136d = {0, 4, 8};

    /* renamed from: e */
    public static SparseIntArray f1137e;

    /* renamed from: a */
    public HashMap<String, C1248a> f1138a = new HashMap<>();

    /* renamed from: b */
    public boolean f1139b = true;

    /* renamed from: c */
    public HashMap<Integer, a> f1140c = new HashMap<>();

    /* renamed from: androidx.constraintlayout.widget.b$a */
    public static class a {

        /* renamed from: a */
        public int f1141a;

        /* renamed from: b */
        public final d f1142b = new d();

        /* renamed from: c */
        public final c f1143c = new c();

        /* renamed from: d */
        public final b f1144d = new b();

        /* renamed from: e */
        public final e f1145e = new e();

        /* renamed from: f */
        public HashMap<String, C1248a> f1146f = new HashMap<>();

        /* renamed from: a */
        public final void m541a(ConstraintLayout.C0178a c0178a) {
            b bVar = this.f1144d;
            c0178a.f1088d = bVar.f1186g;
            c0178a.f1090e = bVar.f1188h;
            c0178a.f1092f = bVar.f1190i;
            c0178a.f1094g = bVar.f1192j;
            c0178a.f1096h = bVar.f1193k;
            c0178a.f1098i = bVar.f1194l;
            c0178a.f1100j = bVar.f1195m;
            c0178a.f1102k = bVar.f1196n;
            c0178a.f1104l = bVar.f1197o;
            c0178a.f1109p = bVar.f1198p;
            c0178a.f1110q = bVar.f1199q;
            c0178a.f1111r = bVar.f1200r;
            c0178a.f1112s = bVar.f1201s;
            ((ViewGroup.MarginLayoutParams) c0178a).leftMargin = bVar.f1150C;
            ((ViewGroup.MarginLayoutParams) c0178a).rightMargin = bVar.f1151D;
            ((ViewGroup.MarginLayoutParams) c0178a).topMargin = bVar.f1152E;
            ((ViewGroup.MarginLayoutParams) c0178a).bottomMargin = bVar.f1153F;
            c0178a.f1117x = bVar.f1161N;
            c0178a.f1118y = bVar.f1160M;
            c0178a.f1114u = bVar.f1157J;
            c0178a.f1116w = bVar.f1159L;
            c0178a.f1119z = bVar.f1202t;
            c0178a.f1056A = bVar.f1203u;
            c0178a.f1106m = bVar.f1205w;
            c0178a.f1107n = bVar.f1206x;
            c0178a.f1108o = bVar.f1207y;
            c0178a.f1057B = bVar.f1204v;
            c0178a.f1071P = bVar.f1208z;
            c0178a.f1072Q = bVar.f1148A;
            c0178a.f1060E = bVar.f1162O;
            c0178a.f1059D = bVar.f1163P;
            c0178a.f1062G = bVar.f1165R;
            c0178a.f1061F = bVar.f1164Q;
            c0178a.f1074S = bVar.f1187g0;
            c0178a.f1075T = bVar.f1189h0;
            c0178a.f1063H = bVar.f1166S;
            c0178a.f1064I = bVar.f1167T;
            c0178a.f1067L = bVar.f1168U;
            c0178a.f1068M = bVar.f1169V;
            c0178a.f1065J = bVar.f1170W;
            c0178a.f1066K = bVar.f1171X;
            c0178a.f1069N = bVar.f1172Y;
            c0178a.f1070O = bVar.f1173Z;
            c0178a.f1073R = bVar.f1149B;
            c0178a.f1086c = bVar.f1184f;
            c0178a.f1082a = bVar.f1180d;
            c0178a.f1084b = bVar.f1182e;
            ((ViewGroup.MarginLayoutParams) c0178a).width = bVar.f1176b;
            ((ViewGroup.MarginLayoutParams) c0178a).height = bVar.f1178c;
            String str = bVar.f1185f0;
            if (str != null) {
                c0178a.f1076U = str;
            }
            c0178a.setMarginStart(bVar.f1155H);
            c0178a.setMarginEnd(this.f1144d.f1154G);
            c0178a.m526a();
        }

        /* renamed from: b */
        public final void m542b(int i6, ConstraintLayout.C0178a c0178a) {
            this.f1141a = i6;
            b bVar = this.f1144d;
            bVar.f1186g = c0178a.f1088d;
            bVar.f1188h = c0178a.f1090e;
            bVar.f1190i = c0178a.f1092f;
            bVar.f1192j = c0178a.f1094g;
            bVar.f1193k = c0178a.f1096h;
            bVar.f1194l = c0178a.f1098i;
            bVar.f1195m = c0178a.f1100j;
            bVar.f1196n = c0178a.f1102k;
            bVar.f1197o = c0178a.f1104l;
            bVar.f1198p = c0178a.f1109p;
            bVar.f1199q = c0178a.f1110q;
            bVar.f1200r = c0178a.f1111r;
            bVar.f1201s = c0178a.f1112s;
            bVar.f1202t = c0178a.f1119z;
            bVar.f1203u = c0178a.f1056A;
            bVar.f1204v = c0178a.f1057B;
            bVar.f1205w = c0178a.f1106m;
            bVar.f1206x = c0178a.f1107n;
            bVar.f1207y = c0178a.f1108o;
            bVar.f1208z = c0178a.f1071P;
            bVar.f1148A = c0178a.f1072Q;
            bVar.f1149B = c0178a.f1073R;
            bVar.f1184f = c0178a.f1086c;
            bVar.f1180d = c0178a.f1082a;
            bVar.f1182e = c0178a.f1084b;
            bVar.f1176b = ((ViewGroup.MarginLayoutParams) c0178a).width;
            bVar.f1178c = ((ViewGroup.MarginLayoutParams) c0178a).height;
            bVar.f1150C = ((ViewGroup.MarginLayoutParams) c0178a).leftMargin;
            bVar.f1151D = ((ViewGroup.MarginLayoutParams) c0178a).rightMargin;
            bVar.f1152E = ((ViewGroup.MarginLayoutParams) c0178a).topMargin;
            bVar.f1153F = ((ViewGroup.MarginLayoutParams) c0178a).bottomMargin;
            bVar.f1162O = c0178a.f1060E;
            bVar.f1163P = c0178a.f1059D;
            bVar.f1165R = c0178a.f1062G;
            bVar.f1164Q = c0178a.f1061F;
            bVar.f1187g0 = c0178a.f1074S;
            bVar.f1189h0 = c0178a.f1075T;
            bVar.f1166S = c0178a.f1063H;
            bVar.f1167T = c0178a.f1064I;
            bVar.f1168U = c0178a.f1067L;
            bVar.f1169V = c0178a.f1068M;
            bVar.f1170W = c0178a.f1065J;
            bVar.f1171X = c0178a.f1066K;
            bVar.f1172Y = c0178a.f1069N;
            bVar.f1173Z = c0178a.f1070O;
            bVar.f1185f0 = c0178a.f1076U;
            bVar.f1157J = c0178a.f1114u;
            bVar.f1159L = c0178a.f1116w;
            bVar.f1156I = c0178a.f1113t;
            bVar.f1158K = c0178a.f1115v;
            bVar.f1161N = c0178a.f1117x;
            bVar.f1160M = c0178a.f1118y;
            bVar.f1154G = c0178a.getMarginEnd();
            this.f1144d.f1155H = c0178a.getMarginStart();
        }

        /* renamed from: c */
        public final void m543c(int i6, C0182c.a aVar) {
            m542b(i6, aVar);
            this.f1142b.f1216c = aVar.f1232m0;
            e eVar = this.f1145e;
            eVar.f1219a = aVar.f1235p0;
            eVar.f1220b = aVar.f1236q0;
            eVar.f1221c = aVar.f1237r0;
            eVar.f1222d = aVar.f1238s0;
            eVar.f1223e = aVar.f1239t0;
            eVar.f1224f = aVar.f1240u0;
            eVar.f1225g = aVar.f1241v0;
            eVar.f1226h = aVar.f1242w0;
            eVar.f1227i = aVar.f1243x0;
            eVar.f1228j = aVar.f1244y0;
            eVar.f1230l = aVar.f1234o0;
            eVar.f1229k = aVar.f1233n0;
        }

        public final Object clone() {
            a aVar = new a();
            b bVar = aVar.f1144d;
            b bVar2 = this.f1144d;
            Objects.requireNonNull(bVar);
            bVar.f1174a = bVar2.f1174a;
            bVar.f1176b = bVar2.f1176b;
            bVar.f1178c = bVar2.f1178c;
            bVar.f1180d = bVar2.f1180d;
            bVar.f1182e = bVar2.f1182e;
            bVar.f1184f = bVar2.f1184f;
            bVar.f1186g = bVar2.f1186g;
            bVar.f1188h = bVar2.f1188h;
            bVar.f1190i = bVar2.f1190i;
            bVar.f1192j = bVar2.f1192j;
            bVar.f1193k = bVar2.f1193k;
            bVar.f1194l = bVar2.f1194l;
            bVar.f1195m = bVar2.f1195m;
            bVar.f1196n = bVar2.f1196n;
            bVar.f1197o = bVar2.f1197o;
            bVar.f1198p = bVar2.f1198p;
            bVar.f1199q = bVar2.f1199q;
            bVar.f1200r = bVar2.f1200r;
            bVar.f1201s = bVar2.f1201s;
            bVar.f1202t = bVar2.f1202t;
            bVar.f1203u = bVar2.f1203u;
            bVar.f1204v = bVar2.f1204v;
            bVar.f1205w = bVar2.f1205w;
            bVar.f1206x = bVar2.f1206x;
            bVar.f1207y = bVar2.f1207y;
            bVar.f1208z = bVar2.f1208z;
            bVar.f1148A = bVar2.f1148A;
            bVar.f1149B = bVar2.f1149B;
            bVar.f1150C = bVar2.f1150C;
            bVar.f1151D = bVar2.f1151D;
            bVar.f1152E = bVar2.f1152E;
            bVar.f1153F = bVar2.f1153F;
            bVar.f1154G = bVar2.f1154G;
            bVar.f1155H = bVar2.f1155H;
            bVar.f1156I = bVar2.f1156I;
            bVar.f1157J = bVar2.f1157J;
            bVar.f1158K = bVar2.f1158K;
            bVar.f1159L = bVar2.f1159L;
            bVar.f1160M = bVar2.f1160M;
            bVar.f1161N = bVar2.f1161N;
            bVar.f1162O = bVar2.f1162O;
            bVar.f1163P = bVar2.f1163P;
            bVar.f1164Q = bVar2.f1164Q;
            bVar.f1165R = bVar2.f1165R;
            bVar.f1166S = bVar2.f1166S;
            bVar.f1167T = bVar2.f1167T;
            bVar.f1168U = bVar2.f1168U;
            bVar.f1169V = bVar2.f1169V;
            bVar.f1170W = bVar2.f1170W;
            bVar.f1171X = bVar2.f1171X;
            bVar.f1172Y = bVar2.f1172Y;
            bVar.f1173Z = bVar2.f1173Z;
            bVar.f1175a0 = bVar2.f1175a0;
            bVar.f1177b0 = bVar2.f1177b0;
            bVar.f1179c0 = bVar2.f1179c0;
            bVar.f1185f0 = bVar2.f1185f0;
            int[] iArr = bVar2.f1181d0;
            if (iArr != null) {
                bVar.f1181d0 = Arrays.copyOf(iArr, iArr.length);
            } else {
                bVar.f1181d0 = null;
            }
            bVar.f1183e0 = bVar2.f1183e0;
            bVar.f1187g0 = bVar2.f1187g0;
            bVar.f1189h0 = bVar2.f1189h0;
            bVar.f1191i0 = bVar2.f1191i0;
            c cVar = aVar.f1143c;
            c cVar2 = this.f1143c;
            Objects.requireNonNull(cVar);
            Objects.requireNonNull(cVar2);
            cVar.f1210a = cVar2.f1210a;
            cVar.f1211b = cVar2.f1211b;
            cVar.f1213d = cVar2.f1213d;
            cVar.f1212c = cVar2.f1212c;
            d dVar = aVar.f1142b;
            d dVar2 = this.f1142b;
            Objects.requireNonNull(dVar);
            Objects.requireNonNull(dVar2);
            dVar.f1214a = dVar2.f1214a;
            dVar.f1216c = dVar2.f1216c;
            dVar.f1217d = dVar2.f1217d;
            dVar.f1215b = dVar2.f1215b;
            e eVar = aVar.f1145e;
            e eVar2 = this.f1145e;
            Objects.requireNonNull(eVar);
            Objects.requireNonNull(eVar2);
            eVar.f1219a = eVar2.f1219a;
            eVar.f1220b = eVar2.f1220b;
            eVar.f1221c = eVar2.f1221c;
            eVar.f1222d = eVar2.f1222d;
            eVar.f1223e = eVar2.f1223e;
            eVar.f1224f = eVar2.f1224f;
            eVar.f1225g = eVar2.f1225g;
            eVar.f1226h = eVar2.f1226h;
            eVar.f1227i = eVar2.f1227i;
            eVar.f1228j = eVar2.f1228j;
            eVar.f1229k = eVar2.f1229k;
            eVar.f1230l = eVar2.f1230l;
            aVar.f1141a = this.f1141a;
            return aVar;
        }
    }

    /* renamed from: androidx.constraintlayout.widget.b$b */
    public static class b {

        /* renamed from: j0 */
        public static SparseIntArray f1147j0;

        /* renamed from: b */
        public int f1176b;

        /* renamed from: c */
        public int f1178c;

        /* renamed from: d0 */
        public int[] f1181d0;

        /* renamed from: e0 */
        public String f1183e0;

        /* renamed from: f0 */
        public String f1185f0;

        /* renamed from: a */
        public boolean f1174a = false;

        /* renamed from: d */
        public int f1180d = -1;

        /* renamed from: e */
        public int f1182e = -1;

        /* renamed from: f */
        public float f1184f = -1.0f;

        /* renamed from: g */
        public int f1186g = -1;

        /* renamed from: h */
        public int f1188h = -1;

        /* renamed from: i */
        public int f1190i = -1;

        /* renamed from: j */
        public int f1192j = -1;

        /* renamed from: k */
        public int f1193k = -1;

        /* renamed from: l */
        public int f1194l = -1;

        /* renamed from: m */
        public int f1195m = -1;

        /* renamed from: n */
        public int f1196n = -1;

        /* renamed from: o */
        public int f1197o = -1;

        /* renamed from: p */
        public int f1198p = -1;

        /* renamed from: q */
        public int f1199q = -1;

        /* renamed from: r */
        public int f1200r = -1;

        /* renamed from: s */
        public int f1201s = -1;

        /* renamed from: t */
        public float f1202t = 0.5f;

        /* renamed from: u */
        public float f1203u = 0.5f;

        /* renamed from: v */
        public String f1204v = null;

        /* renamed from: w */
        public int f1205w = -1;

        /* renamed from: x */
        public int f1206x = 0;

        /* renamed from: y */
        public float f1207y = 0.0f;

        /* renamed from: z */
        public int f1208z = -1;

        /* renamed from: A */
        public int f1148A = -1;

        /* renamed from: B */
        public int f1149B = -1;

        /* renamed from: C */
        public int f1150C = -1;

        /* renamed from: D */
        public int f1151D = -1;

        /* renamed from: E */
        public int f1152E = -1;

        /* renamed from: F */
        public int f1153F = -1;

        /* renamed from: G */
        public int f1154G = -1;

        /* renamed from: H */
        public int f1155H = -1;

        /* renamed from: I */
        public int f1156I = -1;

        /* renamed from: J */
        public int f1157J = -1;

        /* renamed from: K */
        public int f1158K = -1;

        /* renamed from: L */
        public int f1159L = -1;

        /* renamed from: M */
        public int f1160M = -1;

        /* renamed from: N */
        public int f1161N = -1;

        /* renamed from: O */
        public float f1162O = -1.0f;

        /* renamed from: P */
        public float f1163P = -1.0f;

        /* renamed from: Q */
        public int f1164Q = 0;

        /* renamed from: R */
        public int f1165R = 0;

        /* renamed from: S */
        public int f1166S = 0;

        /* renamed from: T */
        public int f1167T = 0;

        /* renamed from: U */
        public int f1168U = -1;

        /* renamed from: V */
        public int f1169V = -1;

        /* renamed from: W */
        public int f1170W = -1;

        /* renamed from: X */
        public int f1171X = -1;

        /* renamed from: Y */
        public float f1172Y = 1.0f;

        /* renamed from: Z */
        public float f1173Z = 1.0f;

        /* renamed from: a0 */
        public int f1175a0 = -1;

        /* renamed from: b0 */
        public int f1177b0 = 0;

        /* renamed from: c0 */
        public int f1179c0 = -1;

        /* renamed from: g0 */
        public boolean f1187g0 = false;

        /* renamed from: h0 */
        public boolean f1189h0 = false;

        /* renamed from: i0 */
        public boolean f1191i0 = true;

        static {
            SparseIntArray sparseIntArray = new SparseIntArray();
            f1147j0 = sparseIntArray;
            sparseIntArray.append(39, 24);
            f1147j0.append(40, 25);
            f1147j0.append(42, 28);
            f1147j0.append(43, 29);
            f1147j0.append(48, 35);
            f1147j0.append(47, 34);
            f1147j0.append(21, 4);
            f1147j0.append(20, 3);
            f1147j0.append(18, 1);
            f1147j0.append(56, 6);
            f1147j0.append(57, 7);
            f1147j0.append(28, 17);
            f1147j0.append(29, 18);
            f1147j0.append(30, 19);
            f1147j0.append(0, 26);
            f1147j0.append(44, 31);
            f1147j0.append(45, 32);
            f1147j0.append(27, 10);
            f1147j0.append(26, 9);
            f1147j0.append(60, 13);
            f1147j0.append(63, 16);
            f1147j0.append(61, 14);
            f1147j0.append(58, 11);
            f1147j0.append(62, 15);
            f1147j0.append(59, 12);
            f1147j0.append(51, 38);
            f1147j0.append(37, 37);
            f1147j0.append(36, 39);
            f1147j0.append(50, 40);
            f1147j0.append(35, 20);
            f1147j0.append(49, 36);
            f1147j0.append(25, 5);
            f1147j0.append(38, 76);
            f1147j0.append(46, 76);
            f1147j0.append(41, 76);
            f1147j0.append(19, 76);
            f1147j0.append(17, 76);
            f1147j0.append(3, 23);
            f1147j0.append(5, 27);
            f1147j0.append(7, 30);
            f1147j0.append(8, 8);
            f1147j0.append(4, 33);
            f1147j0.append(6, 2);
            f1147j0.append(1, 22);
            f1147j0.append(2, 21);
            f1147j0.append(22, 61);
            f1147j0.append(24, 62);
            f1147j0.append(23, 63);
            f1147j0.append(55, 69);
            f1147j0.append(34, 70);
            f1147j0.append(12, 71);
            f1147j0.append(10, 72);
            f1147j0.append(11, 73);
            f1147j0.append(13, 74);
            f1147j0.append(9, 75);
        }

        /* renamed from: a */
        public final void m544a(Context context, AttributeSet attributeSet) {
            StringBuilder sb;
            String str;
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C1798e.f7342p);
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i6 = 0; i6 < indexCount; i6++) {
                int index = obtainStyledAttributes.getIndex(i6);
                int i7 = f1147j0.get(index);
                if (i7 == 80) {
                    this.f1187g0 = obtainStyledAttributes.getBoolean(index, this.f1187g0);
                } else if (i7 != 81) {
                    switch (i7) {
                        case 1:
                            this.f1197o = C0181b.m535f(obtainStyledAttributes, index, this.f1197o);
                            break;
                        case 2:
                            this.f1153F = obtainStyledAttributes.getDimensionPixelSize(index, this.f1153F);
                            break;
                        case 3:
                            this.f1196n = C0181b.m535f(obtainStyledAttributes, index, this.f1196n);
                            break;
                        case 4:
                            this.f1195m = C0181b.m535f(obtainStyledAttributes, index, this.f1195m);
                            break;
                        case 5:
                            this.f1204v = obtainStyledAttributes.getString(index);
                            break;
                        case 6:
                            this.f1208z = obtainStyledAttributes.getDimensionPixelOffset(index, this.f1208z);
                            break;
                        case 7:
                            this.f1148A = obtainStyledAttributes.getDimensionPixelOffset(index, this.f1148A);
                            break;
                        case 8:
                            this.f1154G = obtainStyledAttributes.getDimensionPixelSize(index, this.f1154G);
                            break;
                        case 9:
                            this.f1201s = C0181b.m535f(obtainStyledAttributes, index, this.f1201s);
                            break;
                        case 10:
                            this.f1200r = C0181b.m535f(obtainStyledAttributes, index, this.f1200r);
                            break;
                        case 11:
                            this.f1159L = obtainStyledAttributes.getDimensionPixelSize(index, this.f1159L);
                            break;
                        case 12:
                            this.f1160M = obtainStyledAttributes.getDimensionPixelSize(index, this.f1160M);
                            break;
                        case 13:
                            this.f1156I = obtainStyledAttributes.getDimensionPixelSize(index, this.f1156I);
                            break;
                        case 14:
                            this.f1158K = obtainStyledAttributes.getDimensionPixelSize(index, this.f1158K);
                            break;
                        case 15:
                            this.f1161N = obtainStyledAttributes.getDimensionPixelSize(index, this.f1161N);
                            break;
                        case 16:
                            this.f1157J = obtainStyledAttributes.getDimensionPixelSize(index, this.f1157J);
                            break;
                        case 17:
                            this.f1180d = obtainStyledAttributes.getDimensionPixelOffset(index, this.f1180d);
                            break;
                        case 18:
                            this.f1182e = obtainStyledAttributes.getDimensionPixelOffset(index, this.f1182e);
                            break;
                        case 19:
                            this.f1184f = obtainStyledAttributes.getFloat(index, this.f1184f);
                            break;
                        case 20:
                            this.f1202t = obtainStyledAttributes.getFloat(index, this.f1202t);
                            break;
                        case 21:
                            this.f1178c = obtainStyledAttributes.getLayoutDimension(index, this.f1178c);
                            break;
                        case 22:
                            this.f1176b = obtainStyledAttributes.getLayoutDimension(index, this.f1176b);
                            break;
                        case 23:
                            this.f1150C = obtainStyledAttributes.getDimensionPixelSize(index, this.f1150C);
                            break;
                        case 24:
                            this.f1186g = C0181b.m535f(obtainStyledAttributes, index, this.f1186g);
                            break;
                        case 25:
                            this.f1188h = C0181b.m535f(obtainStyledAttributes, index, this.f1188h);
                            break;
                        case 26:
                            this.f1149B = obtainStyledAttributes.getInt(index, this.f1149B);
                            break;
                        case 27:
                            this.f1151D = obtainStyledAttributes.getDimensionPixelSize(index, this.f1151D);
                            break;
                        case 28:
                            this.f1190i = C0181b.m535f(obtainStyledAttributes, index, this.f1190i);
                            break;
                        case 29:
                            this.f1192j = C0181b.m535f(obtainStyledAttributes, index, this.f1192j);
                            break;
                        case 30:
                            this.f1155H = obtainStyledAttributes.getDimensionPixelSize(index, this.f1155H);
                            break;
                        case 31:
                            this.f1198p = C0181b.m535f(obtainStyledAttributes, index, this.f1198p);
                            break;
                        case 32:
                            this.f1199q = C0181b.m535f(obtainStyledAttributes, index, this.f1199q);
                            break;
                        case 33:
                            this.f1152E = obtainStyledAttributes.getDimensionPixelSize(index, this.f1152E);
                            break;
                        case 34:
                            this.f1194l = C0181b.m535f(obtainStyledAttributes, index, this.f1194l);
                            break;
                        case 35:
                            this.f1193k = C0181b.m535f(obtainStyledAttributes, index, this.f1193k);
                            break;
                        case 36:
                            this.f1203u = obtainStyledAttributes.getFloat(index, this.f1203u);
                            break;
                        case 37:
                            this.f1163P = obtainStyledAttributes.getFloat(index, this.f1163P);
                            break;
                        case 38:
                            this.f1162O = obtainStyledAttributes.getFloat(index, this.f1162O);
                            break;
                        case 39:
                            this.f1164Q = obtainStyledAttributes.getInt(index, this.f1164Q);
                            break;
                        case 40:
                            this.f1165R = obtainStyledAttributes.getInt(index, this.f1165R);
                            break;
                        default:
                            switch (i7) {
                                case 54:
                                    this.f1166S = obtainStyledAttributes.getInt(index, this.f1166S);
                                    break;
                                case 55:
                                    this.f1167T = obtainStyledAttributes.getInt(index, this.f1167T);
                                    break;
                                case 56:
                                    this.f1168U = obtainStyledAttributes.getDimensionPixelSize(index, this.f1168U);
                                    break;
                                case 57:
                                    this.f1169V = obtainStyledAttributes.getDimensionPixelSize(index, this.f1169V);
                                    break;
                                case 58:
                                    this.f1170W = obtainStyledAttributes.getDimensionPixelSize(index, this.f1170W);
                                    break;
                                case 59:
                                    this.f1171X = obtainStyledAttributes.getDimensionPixelSize(index, this.f1171X);
                                    break;
                                default:
                                    switch (i7) {
                                        case 61:
                                            this.f1205w = C0181b.m535f(obtainStyledAttributes, index, this.f1205w);
                                            break;
                                        case 62:
                                            this.f1206x = obtainStyledAttributes.getDimensionPixelSize(index, this.f1206x);
                                            break;
                                        case 63:
                                            this.f1207y = obtainStyledAttributes.getFloat(index, this.f1207y);
                                            break;
                                        default:
                                            switch (i7) {
                                                case 69:
                                                    this.f1172Y = obtainStyledAttributes.getFloat(index, 1.0f);
                                                    continue;
                                                case 70:
                                                    this.f1173Z = obtainStyledAttributes.getFloat(index, 1.0f);
                                                    continue;
                                                case 71:
                                                    Log.e("ConstraintSet", "CURRENTLY UNSUPPORTED");
                                                    continue;
                                                case 72:
                                                    this.f1175a0 = obtainStyledAttributes.getInt(index, this.f1175a0);
                                                    continue;
                                                case 73:
                                                    this.f1177b0 = obtainStyledAttributes.getDimensionPixelSize(index, this.f1177b0);
                                                    continue;
                                                case 74:
                                                    this.f1183e0 = obtainStyledAttributes.getString(index);
                                                    continue;
                                                case 75:
                                                    this.f1191i0 = obtainStyledAttributes.getBoolean(index, this.f1191i0);
                                                    continue;
                                                case 76:
                                                    sb = new StringBuilder();
                                                    str = "unused attribute 0x";
                                                    break;
                                                case 77:
                                                    this.f1185f0 = obtainStyledAttributes.getString(index);
                                                    continue;
                                                default:
                                                    sb = new StringBuilder();
                                                    str = "Unknown attribute 0x";
                                                    break;
                                            }
                                            sb.append(str);
                                            sb.append(Integer.toHexString(index));
                                            sb.append("   ");
                                            sb.append(f1147j0.get(index));
                                            Log.w("ConstraintSet", sb.toString());
                                            break;
                                    }
                            }
                    }
                } else {
                    this.f1189h0 = obtainStyledAttributes.getBoolean(index, this.f1189h0);
                }
            }
            obtainStyledAttributes.recycle();
        }
    }

    /* renamed from: androidx.constraintlayout.widget.b$c */
    public static class c {

        /* renamed from: e */
        public static SparseIntArray f1209e;

        /* renamed from: a */
        public int f1210a = -1;

        /* renamed from: b */
        public int f1211b = -1;

        /* renamed from: c */
        public float f1212c = Float.NaN;

        /* renamed from: d */
        public float f1213d = Float.NaN;

        static {
            SparseIntArray sparseIntArray = new SparseIntArray();
            f1209e = sparseIntArray;
            sparseIntArray.append(2, 1);
            f1209e.append(4, 2);
            f1209e.append(5, 3);
            f1209e.append(1, 4);
            f1209e.append(0, 5);
            f1209e.append(3, 6);
        }

        /* renamed from: a */
        public final void m545a(Context context, AttributeSet attributeSet) {
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C1798e.f7343q);
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i6 = 0; i6 < indexCount; i6++) {
                int index = obtainStyledAttributes.getIndex(i6);
                switch (f1209e.get(index)) {
                    case 1:
                        this.f1213d = obtainStyledAttributes.getFloat(index, this.f1213d);
                        break;
                    case 2:
                        this.f1211b = obtainStyledAttributes.getInt(index, this.f1211b);
                        break;
                    case 3:
                        if (obtainStyledAttributes.peekValue(index).type == 3) {
                            obtainStyledAttributes.getString(index);
                            break;
                        } else {
                            String str = C0385m.f2320K[obtainStyledAttributes.getInteger(index, 0)];
                            break;
                        }
                    case 4:
                        obtainStyledAttributes.getInt(index, 0);
                        break;
                    case 5:
                        this.f1210a = C0181b.m535f(obtainStyledAttributes, index, this.f1210a);
                        break;
                    case 6:
                        this.f1212c = obtainStyledAttributes.getFloat(index, this.f1212c);
                        break;
                }
            }
            obtainStyledAttributes.recycle();
        }
    }

    /* renamed from: androidx.constraintlayout.widget.b$d */
    public static class d {

        /* renamed from: a */
        public int f1214a = 0;

        /* renamed from: b */
        public int f1215b = 0;

        /* renamed from: c */
        public float f1216c = 1.0f;

        /* renamed from: d */
        public float f1217d = Float.NaN;

        /* renamed from: a */
        public final void m546a(Context context, AttributeSet attributeSet) {
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C1798e.f7344r);
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i6 = 0; i6 < indexCount; i6++) {
                int index = obtainStyledAttributes.getIndex(i6);
                if (index == 1) {
                    this.f1216c = obtainStyledAttributes.getFloat(index, this.f1216c);
                } else if (index == 0) {
                    int i7 = obtainStyledAttributes.getInt(index, this.f1214a);
                    this.f1214a = i7;
                    int[] iArr = C0181b.f1136d;
                    this.f1214a = C0181b.f1136d[i7];
                } else if (index == 4) {
                    this.f1215b = obtainStyledAttributes.getInt(index, this.f1215b);
                } else if (index == 3) {
                    this.f1217d = obtainStyledAttributes.getFloat(index, this.f1217d);
                }
            }
            obtainStyledAttributes.recycle();
        }
    }

    /* renamed from: androidx.constraintlayout.widget.b$e */
    public static class e {

        /* renamed from: m */
        public static SparseIntArray f1218m;

        /* renamed from: a */
        public float f1219a = 0.0f;

        /* renamed from: b */
        public float f1220b = 0.0f;

        /* renamed from: c */
        public float f1221c = 0.0f;

        /* renamed from: d */
        public float f1222d = 1.0f;

        /* renamed from: e */
        public float f1223e = 1.0f;

        /* renamed from: f */
        public float f1224f = Float.NaN;

        /* renamed from: g */
        public float f1225g = Float.NaN;

        /* renamed from: h */
        public float f1226h = 0.0f;

        /* renamed from: i */
        public float f1227i = 0.0f;

        /* renamed from: j */
        public float f1228j = 0.0f;

        /* renamed from: k */
        public boolean f1229k = false;

        /* renamed from: l */
        public float f1230l = 0.0f;

        static {
            SparseIntArray sparseIntArray = new SparseIntArray();
            f1218m = sparseIntArray;
            sparseIntArray.append(6, 1);
            f1218m.append(7, 2);
            f1218m.append(8, 3);
            f1218m.append(4, 4);
            f1218m.append(5, 5);
            f1218m.append(0, 6);
            f1218m.append(1, 7);
            f1218m.append(2, 8);
            f1218m.append(3, 9);
            f1218m.append(9, 10);
            f1218m.append(10, 11);
        }

        /* renamed from: a */
        public final void m547a(Context context, AttributeSet attributeSet) {
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C1798e.f7346t);
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i6 = 0; i6 < indexCount; i6++) {
                int index = obtainStyledAttributes.getIndex(i6);
                switch (f1218m.get(index)) {
                    case 1:
                        this.f1219a = obtainStyledAttributes.getFloat(index, this.f1219a);
                        break;
                    case 2:
                        this.f1220b = obtainStyledAttributes.getFloat(index, this.f1220b);
                        break;
                    case 3:
                        this.f1221c = obtainStyledAttributes.getFloat(index, this.f1221c);
                        break;
                    case 4:
                        this.f1222d = obtainStyledAttributes.getFloat(index, this.f1222d);
                        break;
                    case 5:
                        this.f1223e = obtainStyledAttributes.getFloat(index, this.f1223e);
                        break;
                    case 6:
                        this.f1224f = obtainStyledAttributes.getDimension(index, this.f1224f);
                        break;
                    case 7:
                        this.f1225g = obtainStyledAttributes.getDimension(index, this.f1225g);
                        break;
                    case 8:
                        this.f1226h = obtainStyledAttributes.getDimension(index, this.f1226h);
                        break;
                    case 9:
                        this.f1227i = obtainStyledAttributes.getDimension(index, this.f1227i);
                        break;
                    case 10:
                        this.f1228j = obtainStyledAttributes.getDimension(index, this.f1228j);
                        break;
                    case 11:
                        this.f1229k = true;
                        this.f1230l = obtainStyledAttributes.getDimension(index, this.f1230l);
                        break;
                }
            }
            obtainStyledAttributes.recycle();
        }
    }

    static {
        SparseIntArray sparseIntArray = new SparseIntArray();
        f1137e = sparseIntArray;
        sparseIntArray.append(77, 25);
        f1137e.append(78, 26);
        f1137e.append(80, 29);
        f1137e.append(81, 30);
        f1137e.append(87, 36);
        f1137e.append(86, 35);
        f1137e.append(59, 4);
        f1137e.append(58, 3);
        f1137e.append(56, 1);
        f1137e.append(95, 6);
        f1137e.append(96, 7);
        f1137e.append(66, 17);
        f1137e.append(67, 18);
        f1137e.append(68, 19);
        f1137e.append(0, 27);
        f1137e.append(82, 32);
        f1137e.append(83, 33);
        f1137e.append(65, 10);
        f1137e.append(64, 9);
        f1137e.append(99, 13);
        f1137e.append(102, 16);
        f1137e.append(100, 14);
        f1137e.append(97, 11);
        f1137e.append(101, 15);
        f1137e.append(98, 12);
        f1137e.append(90, 40);
        f1137e.append(75, 39);
        f1137e.append(74, 41);
        f1137e.append(89, 42);
        f1137e.append(73, 20);
        f1137e.append(88, 37);
        f1137e.append(63, 5);
        f1137e.append(76, 82);
        f1137e.append(85, 82);
        f1137e.append(79, 82);
        f1137e.append(57, 82);
        f1137e.append(55, 82);
        f1137e.append(5, 24);
        f1137e.append(7, 28);
        f1137e.append(23, 31);
        f1137e.append(24, 8);
        f1137e.append(6, 34);
        f1137e.append(8, 2);
        f1137e.append(3, 23);
        f1137e.append(4, 21);
        f1137e.append(2, 22);
        f1137e.append(13, 43);
        f1137e.append(26, 44);
        f1137e.append(21, 45);
        f1137e.append(22, 46);
        f1137e.append(20, 60);
        f1137e.append(18, 47);
        f1137e.append(19, 48);
        f1137e.append(14, 49);
        f1137e.append(15, 50);
        f1137e.append(16, 51);
        f1137e.append(17, 52);
        f1137e.append(25, 53);
        f1137e.append(91, 54);
        f1137e.append(69, 55);
        f1137e.append(92, 56);
        f1137e.append(70, 57);
        f1137e.append(93, 58);
        f1137e.append(71, 59);
        f1137e.append(60, 61);
        f1137e.append(62, 62);
        f1137e.append(61, 63);
        f1137e.append(27, 64);
        f1137e.append(107, 65);
        f1137e.append(34, 66);
        f1137e.append(108, 67);
        f1137e.append(104, 79);
        f1137e.append(1, 38);
        f1137e.append(103, 68);
        f1137e.append(94, 69);
        f1137e.append(72, 70);
        f1137e.append(31, 71);
        f1137e.append(29, 72);
        f1137e.append(30, 73);
        f1137e.append(32, 74);
        f1137e.append(28, 75);
        f1137e.append(105, 76);
        f1137e.append(84, 77);
        f1137e.append(109, 78);
        f1137e.append(54, 80);
        f1137e.append(53, 81);
    }

    /* renamed from: f */
    public static int m535f(TypedArray typedArray, int i6, int i7) {
        int resourceId = typedArray.getResourceId(i6, i7);
        return resourceId == -1 ? typedArray.getInt(i6, -1) : resourceId;
    }

    /* renamed from: a */
    public final void m536a(ConstraintLayout constraintLayout) {
        ViewGroup viewGroup;
        int i6;
        HashMap<String, C1248a> hashMap;
        StringBuilder sb;
        String str;
        ConstraintLayout constraintLayout2 = constraintLayout;
        int childCount = constraintLayout.getChildCount();
        HashSet hashSet = new HashSet(this.f1140c.keySet());
        int i7 = 0;
        while (i7 < childCount) {
            View childAt = constraintLayout2.getChildAt(i7);
            int id = childAt.getId();
            if (!this.f1140c.containsKey(Integer.valueOf(id))) {
                StringBuilder sb2 = new StringBuilder();
                sb2.append("id unknown ");
                try {
                    str = childAt.getContext().getResources().getResourceEntryName(childAt.getId());
                } catch (Exception unused) {
                    str = "UNKNOWN";
                }
                sb2.append(str);
                Log.w("ConstraintSet", sb2.toString());
            } else {
                if (this.f1139b && id == -1) {
                    throw new RuntimeException("All children of ConstraintLayout must have ids to use ConstraintSet");
                }
                if (id != -1) {
                    if (this.f1140c.containsKey(Integer.valueOf(id))) {
                        hashSet.remove(Integer.valueOf(id));
                        a aVar = this.f1140c.get(Integer.valueOf(id));
                        if (childAt instanceof Barrier) {
                            aVar.f1144d.f1179c0 = 1;
                        }
                        int i8 = aVar.f1144d.f1179c0;
                        if (i8 != -1 && i8 == 1) {
                            Barrier barrier = (Barrier) childAt;
                            barrier.setId(id);
                            barrier.setType(aVar.f1144d.f1175a0);
                            barrier.setMargin(aVar.f1144d.f1177b0);
                            barrier.setAllowsGoneWidget(aVar.f1144d.f1191i0);
                            b bVar = aVar.f1144d;
                            int[] iArr = bVar.f1181d0;
                            if (iArr != null) {
                                barrier.setReferencedIds(iArr);
                            } else {
                                String str2 = bVar.f1183e0;
                                if (str2 != null) {
                                    bVar.f1181d0 = m538c(barrier, str2);
                                    barrier.setReferencedIds(aVar.f1144d.f1181d0);
                                }
                            }
                        }
                        ConstraintLayout.C0178a c0178a = (ConstraintLayout.C0178a) childAt.getLayoutParams();
                        c0178a.m526a();
                        aVar.m541a(c0178a);
                        HashMap<String, C1248a> hashMap2 = aVar.f1146f;
                        Class<?> cls = childAt.getClass();
                        for (String str3 : hashMap2.keySet()) {
                            C1248a c1248a = hashMap2.get(str3);
                            StringBuilder sb3 = new StringBuilder();
                            int i9 = childCount;
                            sb3.append("set");
                            sb3.append(str3);
                            String sb4 = sb3.toString();
                            try {
                            } catch (IllegalAccessException e6) {
                                e = e6;
                                hashMap = hashMap2;
                            } catch (NoSuchMethodException e7) {
                                e = e7;
                                hashMap = hashMap2;
                            } catch (InvocationTargetException e8) {
                                e = e8;
                                hashMap = hashMap2;
                            }
                            switch (C1094g.m2840d(c1248a.f5916a)) {
                                case 0:
                                    hashMap = hashMap2;
                                    Class<?>[] clsArr = new Class[1];
                                    try {
                                        clsArr[0] = Integer.TYPE;
                                        cls.getMethod(sb4, clsArr).invoke(childAt, Integer.valueOf(c1248a.f5917b));
                                    } catch (IllegalAccessException e9) {
                                        e = e9;
                                        sb = new StringBuilder();
                                        sb.append(" Custom Attribute \"");
                                        sb.append(str3);
                                        sb.append("\" not found on ");
                                        sb.append(cls.getName());
                                        Log.e("TransitionLayout", sb.toString());
                                        e.printStackTrace();
                                        childCount = i9;
                                        hashMap2 = hashMap;
                                    } catch (NoSuchMethodException e10) {
                                        e = e10;
                                        Log.e("TransitionLayout", e.getMessage());
                                        Log.e("TransitionLayout", " Custom Attribute \"" + str3 + "\" not found on " + cls.getName());
                                        StringBuilder sb5 = new StringBuilder();
                                        sb5.append(cls.getName());
                                        sb5.append(" must have a method ");
                                        sb5.append(sb4);
                                        Log.e("TransitionLayout", sb5.toString());
                                        childCount = i9;
                                        hashMap2 = hashMap;
                                    } catch (InvocationTargetException e11) {
                                        e = e11;
                                        sb = new StringBuilder();
                                        sb.append(" Custom Attribute \"");
                                        sb.append(str3);
                                        sb.append("\" not found on ");
                                        sb.append(cls.getName());
                                        Log.e("TransitionLayout", sb.toString());
                                        e.printStackTrace();
                                        childCount = i9;
                                        hashMap2 = hashMap;
                                    }
                                    childCount = i9;
                                    hashMap2 = hashMap;
                                case 1:
                                    hashMap = hashMap2;
                                    cls.getMethod(sb4, Float.TYPE).invoke(childAt, Float.valueOf(c1248a.f5918c));
                                    childCount = i9;
                                    hashMap2 = hashMap;
                                    break;
                                case 2:
                                    hashMap = hashMap2;
                                    cls.getMethod(sb4, Integer.TYPE).invoke(childAt, Integer.valueOf(c1248a.f5921f));
                                    childCount = i9;
                                    hashMap2 = hashMap;
                                    break;
                                case 3:
                                    hashMap = hashMap2;
                                    Method method = cls.getMethod(sb4, Drawable.class);
                                    ColorDrawable colorDrawable = new ColorDrawable();
                                    colorDrawable.setColor(c1248a.f5921f);
                                    method.invoke(childAt, colorDrawable);
                                    childCount = i9;
                                    hashMap2 = hashMap;
                                    break;
                                case 4:
                                    hashMap = hashMap2;
                                    cls.getMethod(sb4, CharSequence.class).invoke(childAt, c1248a.f5919d);
                                    childCount = i9;
                                    hashMap2 = hashMap;
                                    break;
                                case 5:
                                    hashMap = hashMap2;
                                    cls.getMethod(sb4, Boolean.TYPE).invoke(childAt, Boolean.valueOf(c1248a.f5920e));
                                    childCount = i9;
                                    hashMap2 = hashMap;
                                    break;
                                case 6:
                                    hashMap = hashMap2;
                                    try {
                                        cls.getMethod(sb4, Float.TYPE).invoke(childAt, Float.valueOf(c1248a.f5918c));
                                    } catch (IllegalAccessException e12) {
                                        e = e12;
                                        sb = new StringBuilder();
                                        sb.append(" Custom Attribute \"");
                                        sb.append(str3);
                                        sb.append("\" not found on ");
                                        sb.append(cls.getName());
                                        Log.e("TransitionLayout", sb.toString());
                                        e.printStackTrace();
                                        childCount = i9;
                                        hashMap2 = hashMap;
                                    } catch (NoSuchMethodException e13) {
                                        e = e13;
                                        Log.e("TransitionLayout", e.getMessage());
                                        Log.e("TransitionLayout", " Custom Attribute \"" + str3 + "\" not found on " + cls.getName());
                                        StringBuilder sb52 = new StringBuilder();
                                        sb52.append(cls.getName());
                                        sb52.append(" must have a method ");
                                        sb52.append(sb4);
                                        Log.e("TransitionLayout", sb52.toString());
                                        childCount = i9;
                                        hashMap2 = hashMap;
                                    } catch (InvocationTargetException e14) {
                                        e = e14;
                                        sb = new StringBuilder();
                                        sb.append(" Custom Attribute \"");
                                        sb.append(str3);
                                        sb.append("\" not found on ");
                                        sb.append(cls.getName());
                                        Log.e("TransitionLayout", sb.toString());
                                        e.printStackTrace();
                                        childCount = i9;
                                        hashMap2 = hashMap;
                                    }
                                    childCount = i9;
                                    hashMap2 = hashMap;
                                default:
                                    childCount = i9;
                                    break;
                            }
                        }
                        i6 = childCount;
                        childAt.setLayoutParams(c0178a);
                        d dVar = aVar.f1142b;
                        if (dVar.f1215b == 0) {
                            childAt.setVisibility(dVar.f1214a);
                        }
                        childAt.setAlpha(aVar.f1142b.f1216c);
                        childAt.setRotation(aVar.f1145e.f1219a);
                        childAt.setRotationX(aVar.f1145e.f1220b);
                        childAt.setRotationY(aVar.f1145e.f1221c);
                        childAt.setScaleX(aVar.f1145e.f1222d);
                        childAt.setScaleY(aVar.f1145e.f1223e);
                        if (!Float.isNaN(aVar.f1145e.f1224f)) {
                            childAt.setPivotX(aVar.f1145e.f1224f);
                        }
                        if (!Float.isNaN(aVar.f1145e.f1225g)) {
                            childAt.setPivotY(aVar.f1145e.f1225g);
                        }
                        childAt.setTranslationX(aVar.f1145e.f1226h);
                        childAt.setTranslationY(aVar.f1145e.f1227i);
                        childAt.setTranslationZ(aVar.f1145e.f1228j);
                        e eVar = aVar.f1145e;
                        if (eVar.f1229k) {
                            childAt.setElevation(eVar.f1230l);
                        }
                    } else {
                        i6 = childCount;
                        Log.v("ConstraintSet", "WARNING NO CONSTRAINTS for view " + id);
                    }
                    i7++;
                    constraintLayout2 = constraintLayout;
                    childCount = i6;
                }
            }
            i6 = childCount;
            i7++;
            constraintLayout2 = constraintLayout;
            childCount = i6;
        }
        Iterator it = hashSet.iterator();
        while (it.hasNext()) {
            Integer num = (Integer) it.next();
            a aVar2 = this.f1140c.get(num);
            int i10 = aVar2.f1144d.f1179c0;
            if (i10 == -1) {
                viewGroup = constraintLayout;
            } else if (i10 != 1) {
                viewGroup = constraintLayout;
            } else {
                Barrier barrier2 = new Barrier(constraintLayout.getContext());
                barrier2.setId(num.intValue());
                b bVar2 = aVar2.f1144d;
                int[] iArr2 = bVar2.f1181d0;
                if (iArr2 != null) {
                    barrier2.setReferencedIds(iArr2);
                } else {
                    String str4 = bVar2.f1183e0;
                    if (str4 != null) {
                        bVar2.f1181d0 = m538c(barrier2, str4);
                        barrier2.setReferencedIds(aVar2.f1144d.f1181d0);
                    }
                }
                barrier2.setType(aVar2.f1144d.f1175a0);
                barrier2.setMargin(aVar2.f1144d.f1177b0);
                ConstraintLayout.C0178a c0178a2 = new ConstraintLayout.C0178a();
                barrier2.m534h();
                aVar2.m541a(c0178a2);
                viewGroup = constraintLayout;
                viewGroup.addView(barrier2, c0178a2);
            }
            if (aVar2.f1144d.f1174a) {
                Guideline guideline = new Guideline(constraintLayout.getContext());
                guideline.setId(num.intValue());
                ConstraintLayout.C0178a c0178a3 = new ConstraintLayout.C0178a();
                aVar2.m541a(c0178a3);
                viewGroup.addView(guideline, c0178a3);
            }
        }
    }

    /* renamed from: b */
    public final void m537b(ConstraintLayout constraintLayout) {
        C1248a c1248a;
        C0181b c0181b = this;
        int childCount = constraintLayout.getChildCount();
        c0181b.f1140c.clear();
        int i6 = 0;
        while (i6 < childCount) {
            View childAt = constraintLayout.getChildAt(i6);
            ConstraintLayout.C0178a c0178a = (ConstraintLayout.C0178a) childAt.getLayoutParams();
            int id = childAt.getId();
            if (c0181b.f1139b && id == -1) {
                throw new RuntimeException("All children of ConstraintLayout must have ids to use ConstraintSet");
            }
            if (!c0181b.f1140c.containsKey(Integer.valueOf(id))) {
                c0181b.f1140c.put(Integer.valueOf(id), new a());
            }
            a aVar = c0181b.f1140c.get(Integer.valueOf(id));
            HashMap<String, C1248a> hashMap = c0181b.f1138a;
            HashMap<String, C1248a> hashMap2 = new HashMap<>();
            Class<?> cls = childAt.getClass();
            for (String str : hashMap.keySet()) {
                C1248a c1248a2 = hashMap.get(str);
                try {
                    if (str.equals("BackgroundColor")) {
                        c1248a = new C1248a(c1248a2, Integer.valueOf(((ColorDrawable) childAt.getBackground()).getColor()));
                    } else {
                        try {
                            c1248a = new C1248a(c1248a2, cls.getMethod("getMap" + str, new Class[0]).invoke(childAt, new Object[0]));
                        } catch (IllegalAccessException e6) {
                            e = e6;
                            e.printStackTrace();
                        } catch (NoSuchMethodException e7) {
                            e = e7;
                            e.printStackTrace();
                        } catch (InvocationTargetException e8) {
                            e = e8;
                            e.printStackTrace();
                        }
                    }
                    hashMap2.put(str, c1248a);
                } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e9) {
                    e = e9;
                }
            }
            aVar.f1146f = hashMap2;
            aVar.m542b(id, c0178a);
            aVar.f1142b.f1214a = childAt.getVisibility();
            aVar.f1142b.f1216c = childAt.getAlpha();
            aVar.f1145e.f1219a = childAt.getRotation();
            aVar.f1145e.f1220b = childAt.getRotationX();
            aVar.f1145e.f1221c = childAt.getRotationY();
            aVar.f1145e.f1222d = childAt.getScaleX();
            aVar.f1145e.f1223e = childAt.getScaleY();
            float pivotX = childAt.getPivotX();
            float pivotY = childAt.getPivotY();
            if (pivotX != 0.0d || pivotY != 0.0d) {
                e eVar = aVar.f1145e;
                eVar.f1224f = pivotX;
                eVar.f1225g = pivotY;
            }
            aVar.f1145e.f1226h = childAt.getTranslationX();
            aVar.f1145e.f1227i = childAt.getTranslationY();
            aVar.f1145e.f1228j = childAt.getTranslationZ();
            e eVar2 = aVar.f1145e;
            if (eVar2.f1229k) {
                eVar2.f1230l = childAt.getElevation();
            }
            if (childAt instanceof Barrier) {
                Barrier barrier = (Barrier) childAt;
                b bVar = aVar.f1144d;
                bVar.f1191i0 = barrier.f1038s.f5343o0;
                bVar.f1181d0 = barrier.getReferencedIds();
                aVar.f1144d.f1175a0 = barrier.getType();
                aVar.f1144d.f1177b0 = barrier.getMargin();
            }
            i6++;
            c0181b = this;
        }
    }

    /* renamed from: c */
    public final int[] m538c(View view, String str) {
        int i6;
        Object m518c;
        String[] split = str.split(",");
        Context context = view.getContext();
        int[] iArr = new int[split.length];
        int i7 = 0;
        int i8 = 0;
        while (i7 < split.length) {
            String trim = split[i7].trim();
            try {
                i6 = C1251d.class.getField(trim).getInt(null);
            } catch (Exception unused) {
                i6 = 0;
            }
            if (i6 == 0) {
                i6 = context.getResources().getIdentifier(trim, "id", context.getPackageName());
            }
            if (i6 == 0 && view.isInEditMode() && (view.getParent() instanceof ConstraintLayout) && (m518c = ((ConstraintLayout) view.getParent()).m518c(trim)) != null && (m518c instanceof Integer)) {
                i6 = ((Integer) m518c).intValue();
            }
            iArr[i8] = i6;
            i7++;
            i8++;
        }
        return i8 != split.length ? Arrays.copyOf(iArr, i8) : iArr;
    }

    /* renamed from: d */
    public final a m539d(Context context, AttributeSet attributeSet) {
        c cVar;
        StringBuilder sb;
        String str;
        a aVar = new a();
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C1798e.f7338l);
        int indexCount = obtainStyledAttributes.getIndexCount();
        for (int i6 = 0; i6 < indexCount; i6++) {
            int index = obtainStyledAttributes.getIndex(i6);
            if (index != 1 && 23 != index && 24 != index) {
                Objects.requireNonNull(aVar.f1143c);
                Objects.requireNonNull(aVar.f1144d);
                Objects.requireNonNull(aVar.f1142b);
                Objects.requireNonNull(aVar.f1145e);
            }
            switch (f1137e.get(index)) {
                case 1:
                    b bVar = aVar.f1144d;
                    bVar.f1197o = m535f(obtainStyledAttributes, index, bVar.f1197o);
                    break;
                case 2:
                    b bVar2 = aVar.f1144d;
                    bVar2.f1153F = obtainStyledAttributes.getDimensionPixelSize(index, bVar2.f1153F);
                    break;
                case 3:
                    b bVar3 = aVar.f1144d;
                    bVar3.f1196n = m535f(obtainStyledAttributes, index, bVar3.f1196n);
                    break;
                case 4:
                    b bVar4 = aVar.f1144d;
                    bVar4.f1195m = m535f(obtainStyledAttributes, index, bVar4.f1195m);
                    break;
                case 5:
                    aVar.f1144d.f1204v = obtainStyledAttributes.getString(index);
                    break;
                case 6:
                    b bVar5 = aVar.f1144d;
                    bVar5.f1208z = obtainStyledAttributes.getDimensionPixelOffset(index, bVar5.f1208z);
                    break;
                case 7:
                    b bVar6 = aVar.f1144d;
                    bVar6.f1148A = obtainStyledAttributes.getDimensionPixelOffset(index, bVar6.f1148A);
                    break;
                case 8:
                    b bVar7 = aVar.f1144d;
                    bVar7.f1154G = obtainStyledAttributes.getDimensionPixelSize(index, bVar7.f1154G);
                    break;
                case 9:
                    b bVar8 = aVar.f1144d;
                    bVar8.f1201s = m535f(obtainStyledAttributes, index, bVar8.f1201s);
                    break;
                case 10:
                    b bVar9 = aVar.f1144d;
                    bVar9.f1200r = m535f(obtainStyledAttributes, index, bVar9.f1200r);
                    break;
                case 11:
                    b bVar10 = aVar.f1144d;
                    bVar10.f1159L = obtainStyledAttributes.getDimensionPixelSize(index, bVar10.f1159L);
                    break;
                case 12:
                    b bVar11 = aVar.f1144d;
                    bVar11.f1160M = obtainStyledAttributes.getDimensionPixelSize(index, bVar11.f1160M);
                    break;
                case 13:
                    b bVar12 = aVar.f1144d;
                    bVar12.f1156I = obtainStyledAttributes.getDimensionPixelSize(index, bVar12.f1156I);
                    break;
                case 14:
                    b bVar13 = aVar.f1144d;
                    bVar13.f1158K = obtainStyledAttributes.getDimensionPixelSize(index, bVar13.f1158K);
                    break;
                case 15:
                    b bVar14 = aVar.f1144d;
                    bVar14.f1161N = obtainStyledAttributes.getDimensionPixelSize(index, bVar14.f1161N);
                    break;
                case 16:
                    b bVar15 = aVar.f1144d;
                    bVar15.f1157J = obtainStyledAttributes.getDimensionPixelSize(index, bVar15.f1157J);
                    break;
                case 17:
                    b bVar16 = aVar.f1144d;
                    bVar16.f1180d = obtainStyledAttributes.getDimensionPixelOffset(index, bVar16.f1180d);
                    break;
                case 18:
                    b bVar17 = aVar.f1144d;
                    bVar17.f1182e = obtainStyledAttributes.getDimensionPixelOffset(index, bVar17.f1182e);
                    break;
                case 19:
                    b bVar18 = aVar.f1144d;
                    bVar18.f1184f = obtainStyledAttributes.getFloat(index, bVar18.f1184f);
                    break;
                case 20:
                    b bVar19 = aVar.f1144d;
                    bVar19.f1202t = obtainStyledAttributes.getFloat(index, bVar19.f1202t);
                    break;
                case 21:
                    b bVar20 = aVar.f1144d;
                    bVar20.f1178c = obtainStyledAttributes.getLayoutDimension(index, bVar20.f1178c);
                    break;
                case 22:
                    d dVar = aVar.f1142b;
                    dVar.f1214a = obtainStyledAttributes.getInt(index, dVar.f1214a);
                    d dVar2 = aVar.f1142b;
                    dVar2.f1214a = f1136d[dVar2.f1214a];
                    break;
                case 23:
                    b bVar21 = aVar.f1144d;
                    bVar21.f1176b = obtainStyledAttributes.getLayoutDimension(index, bVar21.f1176b);
                    break;
                case 24:
                    b bVar22 = aVar.f1144d;
                    bVar22.f1150C = obtainStyledAttributes.getDimensionPixelSize(index, bVar22.f1150C);
                    break;
                case 25:
                    b bVar23 = aVar.f1144d;
                    bVar23.f1186g = m535f(obtainStyledAttributes, index, bVar23.f1186g);
                    break;
                case 26:
                    b bVar24 = aVar.f1144d;
                    bVar24.f1188h = m535f(obtainStyledAttributes, index, bVar24.f1188h);
                    break;
                case 27:
                    b bVar25 = aVar.f1144d;
                    bVar25.f1149B = obtainStyledAttributes.getInt(index, bVar25.f1149B);
                    break;
                case 28:
                    b bVar26 = aVar.f1144d;
                    bVar26.f1151D = obtainStyledAttributes.getDimensionPixelSize(index, bVar26.f1151D);
                    break;
                case 29:
                    b bVar27 = aVar.f1144d;
                    bVar27.f1190i = m535f(obtainStyledAttributes, index, bVar27.f1190i);
                    break;
                case 30:
                    b bVar28 = aVar.f1144d;
                    bVar28.f1192j = m535f(obtainStyledAttributes, index, bVar28.f1192j);
                    break;
                case 31:
                    b bVar29 = aVar.f1144d;
                    bVar29.f1155H = obtainStyledAttributes.getDimensionPixelSize(index, bVar29.f1155H);
                    break;
                case 32:
                    b bVar30 = aVar.f1144d;
                    bVar30.f1198p = m535f(obtainStyledAttributes, index, bVar30.f1198p);
                    break;
                case 33:
                    b bVar31 = aVar.f1144d;
                    bVar31.f1199q = m535f(obtainStyledAttributes, index, bVar31.f1199q);
                    break;
                case 34:
                    b bVar32 = aVar.f1144d;
                    bVar32.f1152E = obtainStyledAttributes.getDimensionPixelSize(index, bVar32.f1152E);
                    break;
                case 35:
                    b bVar33 = aVar.f1144d;
                    bVar33.f1194l = m535f(obtainStyledAttributes, index, bVar33.f1194l);
                    break;
                case 36:
                    b bVar34 = aVar.f1144d;
                    bVar34.f1193k = m535f(obtainStyledAttributes, index, bVar34.f1193k);
                    break;
                case 37:
                    b bVar35 = aVar.f1144d;
                    bVar35.f1203u = obtainStyledAttributes.getFloat(index, bVar35.f1203u);
                    break;
                case 38:
                    aVar.f1141a = obtainStyledAttributes.getResourceId(index, aVar.f1141a);
                    break;
                case 39:
                    b bVar36 = aVar.f1144d;
                    bVar36.f1163P = obtainStyledAttributes.getFloat(index, bVar36.f1163P);
                    break;
                case 40:
                    b bVar37 = aVar.f1144d;
                    bVar37.f1162O = obtainStyledAttributes.getFloat(index, bVar37.f1162O);
                    break;
                case 41:
                    b bVar38 = aVar.f1144d;
                    bVar38.f1164Q = obtainStyledAttributes.getInt(index, bVar38.f1164Q);
                    break;
                case 42:
                    b bVar39 = aVar.f1144d;
                    bVar39.f1165R = obtainStyledAttributes.getInt(index, bVar39.f1165R);
                    break;
                case 43:
                    d dVar3 = aVar.f1142b;
                    dVar3.f1216c = obtainStyledAttributes.getFloat(index, dVar3.f1216c);
                    break;
                case 44:
                    e eVar = aVar.f1145e;
                    eVar.f1229k = true;
                    eVar.f1230l = obtainStyledAttributes.getDimension(index, eVar.f1230l);
                    break;
                case 45:
                    e eVar2 = aVar.f1145e;
                    eVar2.f1220b = obtainStyledAttributes.getFloat(index, eVar2.f1220b);
                    break;
                case 46:
                    e eVar3 = aVar.f1145e;
                    eVar3.f1221c = obtainStyledAttributes.getFloat(index, eVar3.f1221c);
                    break;
                case 47:
                    e eVar4 = aVar.f1145e;
                    eVar4.f1222d = obtainStyledAttributes.getFloat(index, eVar4.f1222d);
                    break;
                case 48:
                    e eVar5 = aVar.f1145e;
                    eVar5.f1223e = obtainStyledAttributes.getFloat(index, eVar5.f1223e);
                    break;
                case 49:
                    e eVar6 = aVar.f1145e;
                    eVar6.f1224f = obtainStyledAttributes.getDimension(index, eVar6.f1224f);
                    break;
                case 50:
                    e eVar7 = aVar.f1145e;
                    eVar7.f1225g = obtainStyledAttributes.getDimension(index, eVar7.f1225g);
                    break;
                case 51:
                    e eVar8 = aVar.f1145e;
                    eVar8.f1226h = obtainStyledAttributes.getDimension(index, eVar8.f1226h);
                    break;
                case 52:
                    e eVar9 = aVar.f1145e;
                    eVar9.f1227i = obtainStyledAttributes.getDimension(index, eVar9.f1227i);
                    break;
                case 53:
                    e eVar10 = aVar.f1145e;
                    eVar10.f1228j = obtainStyledAttributes.getDimension(index, eVar10.f1228j);
                    break;
                case 54:
                    b bVar40 = aVar.f1144d;
                    bVar40.f1166S = obtainStyledAttributes.getInt(index, bVar40.f1166S);
                    break;
                case 55:
                    b bVar41 = aVar.f1144d;
                    bVar41.f1167T = obtainStyledAttributes.getInt(index, bVar41.f1167T);
                    break;
                case 56:
                    b bVar42 = aVar.f1144d;
                    bVar42.f1168U = obtainStyledAttributes.getDimensionPixelSize(index, bVar42.f1168U);
                    break;
                case 57:
                    b bVar43 = aVar.f1144d;
                    bVar43.f1169V = obtainStyledAttributes.getDimensionPixelSize(index, bVar43.f1169V);
                    break;
                case 58:
                    b bVar44 = aVar.f1144d;
                    bVar44.f1170W = obtainStyledAttributes.getDimensionPixelSize(index, bVar44.f1170W);
                    break;
                case 59:
                    b bVar45 = aVar.f1144d;
                    bVar45.f1171X = obtainStyledAttributes.getDimensionPixelSize(index, bVar45.f1171X);
                    break;
                case 60:
                    e eVar11 = aVar.f1145e;
                    eVar11.f1219a = obtainStyledAttributes.getFloat(index, eVar11.f1219a);
                    break;
                case 61:
                    b bVar46 = aVar.f1144d;
                    bVar46.f1205w = m535f(obtainStyledAttributes, index, bVar46.f1205w);
                    break;
                case 62:
                    b bVar47 = aVar.f1144d;
                    bVar47.f1206x = obtainStyledAttributes.getDimensionPixelSize(index, bVar47.f1206x);
                    break;
                case 63:
                    b bVar48 = aVar.f1144d;
                    bVar48.f1207y = obtainStyledAttributes.getFloat(index, bVar48.f1207y);
                    break;
                case 64:
                    c cVar2 = aVar.f1143c;
                    cVar2.f1210a = m535f(obtainStyledAttributes, index, cVar2.f1210a);
                    break;
                case 65:
                    if (obtainStyledAttributes.peekValue(index).type == 3) {
                        cVar = aVar.f1143c;
                        obtainStyledAttributes.getString(index);
                    } else {
                        cVar = aVar.f1143c;
                        String str2 = C0385m.f2320K[obtainStyledAttributes.getInteger(index, 0)];
                    }
                    Objects.requireNonNull(cVar);
                    break;
                case 66:
                    cVar = aVar.f1143c;
                    obtainStyledAttributes.getInt(index, 0);
                    Objects.requireNonNull(cVar);
                    break;
                case 67:
                    c cVar3 = aVar.f1143c;
                    cVar3.f1213d = obtainStyledAttributes.getFloat(index, cVar3.f1213d);
                    break;
                case 68:
                    d dVar4 = aVar.f1142b;
                    dVar4.f1217d = obtainStyledAttributes.getFloat(index, dVar4.f1217d);
                    break;
                case 69:
                    aVar.f1144d.f1172Y = obtainStyledAttributes.getFloat(index, 1.0f);
                    break;
                case 70:
                    aVar.f1144d.f1173Z = obtainStyledAttributes.getFloat(index, 1.0f);
                    break;
                case 71:
                    Log.e("ConstraintSet", "CURRENTLY UNSUPPORTED");
                    break;
                case 72:
                    b bVar49 = aVar.f1144d;
                    bVar49.f1175a0 = obtainStyledAttributes.getInt(index, bVar49.f1175a0);
                    break;
                case 73:
                    b bVar50 = aVar.f1144d;
                    bVar50.f1177b0 = obtainStyledAttributes.getDimensionPixelSize(index, bVar50.f1177b0);
                    break;
                case 74:
                    aVar.f1144d.f1183e0 = obtainStyledAttributes.getString(index);
                    break;
                case 75:
                    b bVar51 = aVar.f1144d;
                    bVar51.f1191i0 = obtainStyledAttributes.getBoolean(index, bVar51.f1191i0);
                    break;
                case 76:
                    c cVar4 = aVar.f1143c;
                    cVar4.f1211b = obtainStyledAttributes.getInt(index, cVar4.f1211b);
                    break;
                case 77:
                    aVar.f1144d.f1185f0 = obtainStyledAttributes.getString(index);
                    break;
                case 78:
                    d dVar5 = aVar.f1142b;
                    dVar5.f1215b = obtainStyledAttributes.getInt(index, dVar5.f1215b);
                    break;
                case 79:
                    c cVar5 = aVar.f1143c;
                    cVar5.f1212c = obtainStyledAttributes.getFloat(index, cVar5.f1212c);
                    break;
                case 80:
                    b bVar52 = aVar.f1144d;
                    bVar52.f1187g0 = obtainStyledAttributes.getBoolean(index, bVar52.f1187g0);
                    break;
                case 81:
                    b bVar53 = aVar.f1144d;
                    bVar53.f1189h0 = obtainStyledAttributes.getBoolean(index, bVar53.f1189h0);
                    break;
                case 82:
                    sb = new StringBuilder();
                    str = "unused attribute 0x";
                    sb.append(str);
                    sb.append(Integer.toHexString(index));
                    sb.append("   ");
                    sb.append(f1137e.get(index));
                    Log.w("ConstraintSet", sb.toString());
                    break;
                default:
                    sb = new StringBuilder();
                    str = "Unknown attribute 0x";
                    sb.append(str);
                    sb.append(Integer.toHexString(index));
                    sb.append("   ");
                    sb.append(f1137e.get(index));
                    Log.w("ConstraintSet", sb.toString());
                    break;
            }
        }
        obtainStyledAttributes.recycle();
        return aVar;
    }

    /* renamed from: e */
    public final void m540e(Context context, int i6) {
        XmlResourceParser xml = context.getResources().getXml(i6);
        try {
            for (int eventType = xml.getEventType(); eventType != 1; eventType = xml.next()) {
                if (eventType == 0) {
                    xml.getName();
                } else if (eventType == 2) {
                    String name = xml.getName();
                    a m539d = m539d(context, Xml.asAttributeSet(xml));
                    if (name.equalsIgnoreCase("Guideline")) {
                        m539d.f1144d.f1174a = true;
                    }
                    this.f1140c.put(Integer.valueOf(m539d.f1141a), m539d);
                }
            }
        } catch (IOException e6) {
            e6.printStackTrace();
        } catch (XmlPullParserException e7) {
            e7.printStackTrace();
        }
    }
}
