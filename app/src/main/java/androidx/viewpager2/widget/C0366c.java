package androidx.viewpager2.widget;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

/* renamed from: androidx.viewpager2.widget.c */
/* loaded from: classes.dex */
public final class C0366c extends RecyclerView.AbstractC0293r {

    /* renamed from: a */
    public ViewPager2.AbstractC0357e f2250a;

    /* renamed from: b */
    public final ViewPager2 f2251b;

    /* renamed from: c */
    public final RecyclerView f2252c;

    /* renamed from: d */
    public final LinearLayoutManager f2253d;

    /* renamed from: e */
    public int f2254e;

    /* renamed from: f */
    public int f2255f;

    /* renamed from: g */
    public a f2256g;

    /* renamed from: h */
    public int f2257h;

    /* renamed from: i */
    public int f2258i;

    /* renamed from: j */
    public boolean f2259j;

    /* renamed from: k */
    public boolean f2260k;

    /* renamed from: l */
    public boolean f2261l;

    /* renamed from: m */
    public boolean f2262m;

    /* renamed from: androidx.viewpager2.widget.c$a */
    public static final class a {

        /* renamed from: a */
        public int f2263a;

        /* renamed from: b */
        public float f2264b;

        /* renamed from: c */
        public int f2265c;

        /* renamed from: a */
        public final void m1397a() {
            this.f2263a = -1;
            this.f2264b = 0.0f;
            this.f2265c = 0;
        }
    }

    public C0366c(ViewPager2 viewPager2) {
        this.f2251b = viewPager2;
        ViewPager2.C0361i c0361i = viewPager2.f2224s;
        this.f2252c = c0361i;
        this.f2253d = (LinearLayoutManager) c0361i.getLayoutManager();
        this.f2256g = new a();
        m1395e();
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0293r
    /* renamed from: a */
    public final void mo1173a(RecyclerView recyclerView, int i6) {
        ViewPager2.AbstractC0357e abstractC0357e;
        int i7 = this.f2254e;
        boolean z5 = true;
        if (!(i7 == 1 && this.f2255f == 1) && i6 == 1) {
            this.f2262m = false;
            this.f2254e = 1;
            int i8 = this.f2258i;
            if (i8 != -1) {
                this.f2257h = i8;
                this.f2258i = -1;
            } else if (this.f2257h == -1) {
                this.f2257h = this.f2253d.m950U0();
            }
            m1394d(1);
            return;
        }
        if ((i7 == 1 || i7 == 4) && i6 == 2) {
            if (this.f2260k) {
                m1394d(2);
                this.f2259j = true;
                return;
            }
            return;
        }
        if ((i7 == 1 || i7 == 4) && i6 == 0) {
            m1396f();
            if (this.f2260k) {
                a aVar = this.f2256g;
                if (aVar.f2265c == 0) {
                    int i9 = this.f2257h;
                    int i10 = aVar.f2263a;
                    if (i9 != i10) {
                        m1393c(i10);
                    }
                } else {
                    z5 = false;
                }
            } else {
                int i11 = this.f2256g.f2263a;
                if (i11 != -1 && (abstractC0357e = this.f2250a) != null) {
                    abstractC0357e.mo1385b(i11, 0.0f, 0);
                }
            }
            if (z5) {
                m1394d(0);
                m1395e();
            }
        }
        if (this.f2254e == 2 && i6 == 0 && this.f2261l) {
            m1396f();
            a aVar2 = this.f2256g;
            if (aVar2.f2265c == 0) {
                int i12 = this.f2258i;
                int i13 = aVar2.f2263a;
                if (i12 != i13) {
                    if (i13 == -1) {
                        i13 = 0;
                    }
                    m1393c(i13);
                }
                m1394d(0);
                m1395e();
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:17:0x0037, code lost:
    
        if (r4.f2257h != r6) goto L75;
     */
    /* JADX WARN: Code restructure failed: missing block: B:9:0x001d, code lost:
    
        if ((r6 < 0) == r4.f2251b.m1380a()) goto L61;
     */
    /* JADX WARN: Removed duplicated region for block: B:13:0x0025  */
    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0293r
    /* renamed from: b */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void mo1174b(androidx.recyclerview.widget.RecyclerView r5, int r6, int r7) {
        /*
            r4 = this;
            r5 = 1
            r4.f2260k = r5
            r4.m1396f()
            boolean r0 = r4.f2259j
            r1 = -1
            r2 = 0
            if (r0 == 0) goto L3a
            r4.f2259j = r2
            if (r7 > 0) goto L22
            if (r7 != 0) goto L20
            if (r6 >= 0) goto L16
            r6 = r5
            goto L17
        L16:
            r6 = r2
        L17:
            androidx.viewpager2.widget.ViewPager2 r7 = r4.f2251b
            boolean r7 = r7.m1380a()
            if (r6 != r7) goto L20
            goto L22
        L20:
            r6 = r2
            goto L23
        L22:
            r6 = r5
        L23:
            if (r6 == 0) goto L2f
            androidx.viewpager2.widget.c$a r6 = r4.f2256g
            int r7 = r6.f2265c
            if (r7 == 0) goto L2f
            int r6 = r6.f2263a
            int r6 = r6 + r5
            goto L33
        L2f:
            androidx.viewpager2.widget.c$a r6 = r4.f2256g
            int r6 = r6.f2263a
        L33:
            r4.f2258i = r6
            int r7 = r4.f2257h
            if (r7 == r6) goto L48
            goto L45
        L3a:
            int r6 = r4.f2254e
            if (r6 != 0) goto L48
            androidx.viewpager2.widget.c$a r6 = r4.f2256g
            int r6 = r6.f2263a
            if (r6 != r1) goto L45
            r6 = r2
        L45:
            r4.m1393c(r6)
        L48:
            androidx.viewpager2.widget.c$a r6 = r4.f2256g
            int r7 = r6.f2263a
            if (r7 != r1) goto L4f
            r7 = r2
        L4f:
            float r0 = r6.f2264b
            int r6 = r6.f2265c
            androidx.viewpager2.widget.ViewPager2$e r3 = r4.f2250a
            if (r3 == 0) goto L5a
            r3.mo1385b(r7, r0, r6)
        L5a:
            androidx.viewpager2.widget.c$a r6 = r4.f2256g
            int r7 = r6.f2263a
            int r0 = r4.f2258i
            if (r7 == r0) goto L64
            if (r0 != r1) goto L72
        L64:
            int r6 = r6.f2265c
            if (r6 != 0) goto L72
            int r6 = r4.f2255f
            if (r6 == r5) goto L72
            r4.m1394d(r2)
            r4.m1395e()
        L72:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.viewpager2.widget.C0366c.mo1174b(androidx.recyclerview.widget.RecyclerView, int, int):void");
    }

    /* renamed from: c */
    public final void m1393c(int i6) {
        ViewPager2.AbstractC0357e abstractC0357e = this.f2250a;
        if (abstractC0357e != null) {
            abstractC0357e.mo1379c(i6);
        }
    }

    /* renamed from: d */
    public final void m1394d(int i6) {
        if ((this.f2254e == 3 && this.f2255f == 0) || this.f2255f == i6) {
            return;
        }
        this.f2255f = i6;
        ViewPager2.AbstractC0357e abstractC0357e = this.f2250a;
        if (abstractC0357e != null) {
            abstractC0357e.mo1378a(i6);
        }
    }

    /* renamed from: e */
    public final void m1395e() {
        this.f2254e = 0;
        this.f2255f = 0;
        this.f2256g.m1397a();
        this.f2257h = -1;
        this.f2258i = -1;
        this.f2259j = false;
        this.f2260k = false;
        this.f2262m = false;
        this.f2261l = false;
    }

    /* JADX WARN: Code restructure failed: missing block: B:57:0x0156, code lost:
    
        r2 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:82:0x0151, code lost:
    
        if (r4[r2 - 1][1] >= r3) goto L157;
     */
    /* JADX WARN: Removed duplicated region for block: B:63:0x0183  */
    /* JADX WARN: Removed duplicated region for block: B:65:0x018b  */
    /* renamed from: f */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m1396f() {
        /*
            Method dump skipped, instructions count: 429
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.viewpager2.widget.C0366c.m1396f():void");
    }
}
