package androidx.viewpager2.widget;

import androidx.viewpager2.widget.ViewPager2;
import java.util.ArrayList;
import java.util.ConcurrentModificationException;
import java.util.Iterator;
import java.util.List;

/* renamed from: androidx.viewpager2.widget.a */
/* loaded from: classes.dex */
public final class C0364a extends ViewPager2.AbstractC0357e {

    /* renamed from: a */
    public final List<ViewPager2.AbstractC0357e> f2247a = new ArrayList(3);

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<androidx.viewpager2.widget.ViewPager2$e>] */
    @Override // androidx.viewpager2.widget.ViewPager2.AbstractC0357e
    /* renamed from: a */
    public final void mo1378a(int i6) {
        try {
            Iterator it = this.f2247a.iterator();
            while (it.hasNext()) {
                ((ViewPager2.AbstractC0357e) it.next()).mo1378a(i6);
            }
        } catch (ConcurrentModificationException e6) {
            m1392e(e6);
            throw null;
        }
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<androidx.viewpager2.widget.ViewPager2$e>] */
    @Override // androidx.viewpager2.widget.ViewPager2.AbstractC0357e
    /* renamed from: b */
    public final void mo1385b(int i6, float f6, int i7) {
        try {
            Iterator it = this.f2247a.iterator();
            while (it.hasNext()) {
                ((ViewPager2.AbstractC0357e) it.next()).mo1385b(i6, f6, i7);
            }
        } catch (ConcurrentModificationException e6) {
            m1392e(e6);
            throw null;
        }
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<androidx.viewpager2.widget.ViewPager2$e>] */
    @Override // androidx.viewpager2.widget.ViewPager2.AbstractC0357e
    /* renamed from: c */
    public final void mo1379c(int i6) {
        try {
            Iterator it = this.f2247a.iterator();
            while (it.hasNext()) {
                ((ViewPager2.AbstractC0357e) it.next()).mo1379c(i6);
            }
        } catch (ConcurrentModificationException e6) {
            m1392e(e6);
            throw null;
        }
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<androidx.viewpager2.widget.ViewPager2$e>] */
    /* renamed from: d */
    public final void m1391d(ViewPager2.AbstractC0357e abstractC0357e) {
        this.f2247a.add(abstractC0357e);
    }

    /* renamed from: e */
    public final void m1392e(ConcurrentModificationException concurrentModificationException) {
        throw new IllegalStateException("Adding and removing callbacks during dispatch to callbacks is not supported", concurrentModificationException);
    }
}
