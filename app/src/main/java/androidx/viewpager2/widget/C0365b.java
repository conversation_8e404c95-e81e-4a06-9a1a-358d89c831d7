package androidx.viewpager2.widget;

import android.view.View;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.viewpager2.widget.ViewPager2;
import java.util.Locale;

/* renamed from: androidx.viewpager2.widget.b */
/* loaded from: classes.dex */
public final class C0365b extends ViewPager2.AbstractC0357e {

    /* renamed from: a */
    public final LinearLayoutManager f2248a;

    /* renamed from: b */
    public ViewPager2.InterfaceC0359g f2249b;

    public C0365b(LinearLayoutManager linearLayoutManager) {
        this.f2248a = linearLayoutManager;
    }

    @Override // androidx.viewpager2.widget.ViewPager2.AbstractC0357e
    /* renamed from: a */
    public final void mo1378a(int i6) {
    }

    @Override // androidx.viewpager2.widget.ViewPager2.AbstractC0357e
    /* renamed from: b */
    public final void mo1385b(int i6, float f6, int i7) {
        if (this.f2249b == null) {
            return;
        }
        for (int i8 = 0; i8 < this.f2248a.m1157x(); i8++) {
            View m1156w = this.f2248a.m1156w(i8);
            if (m1156w == null) {
                throw new IllegalStateException(String.format(Locale.US, "LayoutManager returned a null child at pos %d/%d while transforming pages", Integer.valueOf(i8), Integer.valueOf(this.f2248a.m1157x())));
            }
            this.f2248a.m1135N(m1156w);
            this.f2249b.m1390a();
        }
    }

    @Override // androidx.viewpager2.widget.ViewPager2.AbstractC0357e
    /* renamed from: c */
    public final void mo1379c(int i6) {
    }
}
