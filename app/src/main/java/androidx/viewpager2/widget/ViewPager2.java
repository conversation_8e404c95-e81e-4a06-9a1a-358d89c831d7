package androidx.viewpager2.widget;

import android.R;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Rect;
import android.os.Build;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.AttributeSet;
import android.util.SparseArray;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import androidx.fragment.app.C0208e0;
import androidx.recyclerview.widget.C0331u;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.adapter.InterfaceC0352f;
import androidx.viewpager2.widget.C0366c;
import java.util.ArrayList;
import java.util.Objects;
import java.util.WeakHashMap;
import p008b0.C0385m;
import p029e0.C0766p;
import p029e0.C0769s;
import p036f0.C0831b;
import p036f0.InterfaceC0833d;
import p163y0.C2062c;

/* loaded from: classes.dex */
public final class ViewPager2 extends ViewGroup {

    /* renamed from: A */
    public boolean f2212A;

    /* renamed from: B */
    public int f2213B;

    /* renamed from: C */
    public C0358f f2214C;

    /* renamed from: j */
    public final Rect f2215j;

    /* renamed from: k */
    public final Rect f2216k;

    /* renamed from: l */
    public C0364a f2217l;

    /* renamed from: m */
    public int f2218m;

    /* renamed from: n */
    public boolean f2219n;

    /* renamed from: o */
    public C0353a f2220o;

    /* renamed from: p */
    public C0356d f2221p;

    /* renamed from: q */
    public int f2222q;

    /* renamed from: r */
    public Parcelable f2223r;

    /* renamed from: s */
    public C0361i f2224s;

    /* renamed from: t */
    public C0360h f2225t;

    /* renamed from: u */
    public C0366c f2226u;

    /* renamed from: v */
    public C0364a f2227v;

    /* renamed from: w */
    public C0208e0 f2228w;

    /* renamed from: x */
    public C0365b f2229x;

    /* renamed from: y */
    public RecyclerView.AbstractC0285j f2230y;

    /* renamed from: z */
    public boolean f2231z;

    /* renamed from: androidx.viewpager2.widget.ViewPager2$a */
    public class C0353a extends AbstractC0355c {
        public C0353a() {
        }

        @Override // androidx.viewpager2.widget.ViewPager2.AbstractC0355c, androidx.recyclerview.widget.RecyclerView.AbstractC0282g
        /* renamed from: a */
        public final void mo1096a() {
            ViewPager2 viewPager2 = ViewPager2.this;
            viewPager2.f2219n = true;
            viewPager2.f2226u.f2261l = true;
        }
    }

    /* renamed from: androidx.viewpager2.widget.ViewPager2$b */
    public abstract class AbstractC0354b {
    }

    /* renamed from: androidx.viewpager2.widget.ViewPager2$c */
    public static abstract class AbstractC0355c extends RecyclerView.AbstractC0282g {
        @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0282g
        /* renamed from: a */
        public abstract void mo1096a();

        @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0282g
        /* renamed from: b */
        public final void mo1097b() {
            mo1096a();
        }

        @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0282g
        /* renamed from: c */
        public final void mo1098c() {
            mo1096a();
        }
    }

    /* renamed from: androidx.viewpager2.widget.ViewPager2$d */
    public class C0356d extends LinearLayoutManager {
        public C0356d(Context context) {
            super(context);
        }

        @Override // androidx.recyclerview.widget.LinearLayoutManager
        /* renamed from: K0 */
        public final void mo940K0(RecyclerView.C0300y c0300y, int[] iArr) {
            int offscreenPageLimit = ViewPager2.this.getOffscreenPageLimit();
            if (offscreenPageLimit == -1) {
                super.mo940K0(c0300y, iArr);
                return;
            }
            int pageSize = ViewPager2.this.getPageSize() * offscreenPageLimit;
            iArr[0] = pageSize;
            iArr[1] = pageSize;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
        /* renamed from: Z */
        public final void mo1140Z(RecyclerView.C0295t c0295t, RecyclerView.C0300y c0300y, C0831b c0831b) {
            super.mo1140Z(c0295t, c0300y, c0831b);
            Objects.requireNonNull(ViewPager2.this.f2214C);
        }

        @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
        /* renamed from: n0 */
        public final boolean mo1146n0(RecyclerView.C0295t c0295t, RecyclerView.C0300y c0300y, int i6, Bundle bundle) {
            Objects.requireNonNull(ViewPager2.this.f2214C);
            return super.mo1146n0(c0295t, c0300y, i6, bundle);
        }

        @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0288m
        /* renamed from: t0 */
        public final boolean mo1154t0(RecyclerView recyclerView, View view, Rect rect, boolean z5, boolean z6) {
            return false;
        }
    }

    /* renamed from: androidx.viewpager2.widget.ViewPager2$e */
    public static abstract class AbstractC0357e {
        /* renamed from: a */
        public void mo1378a(int i6) {
        }

        /* renamed from: b */
        public void mo1385b(int i6, float f6, int i7) {
        }

        /* renamed from: c */
        public void mo1379c(int i6) {
        }
    }

    /* renamed from: androidx.viewpager2.widget.ViewPager2$f */
    public class C0358f extends AbstractC0354b {

        /* renamed from: a */
        public final a f2234a = new a();

        /* renamed from: b */
        public final b f2235b = new b();

        /* renamed from: c */
        public C0369f f2236c;

        /* renamed from: androidx.viewpager2.widget.ViewPager2$f$a */
        public class a implements InterfaceC0833d {
            public a() {
            }

            @Override // p036f0.InterfaceC0833d
            /* renamed from: a */
            public final boolean mo1389a(View view) {
                C0358f.this.m1387b(((ViewPager2) view).getCurrentItem() + 1);
                return true;
            }
        }

        /* renamed from: androidx.viewpager2.widget.ViewPager2$f$b */
        public class b implements InterfaceC0833d {
            public b() {
            }

            @Override // p036f0.InterfaceC0833d
            /* renamed from: a */
            public final boolean mo1389a(View view) {
                C0358f.this.m1387b(((ViewPager2) view).getCurrentItem() - 1);
                return true;
            }
        }

        public C0358f() {
        }

        /* renamed from: a */
        public final void m1386a(RecyclerView recyclerView) {
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            recyclerView.setImportantForAccessibility(2);
            this.f2236c = new C0369f(this);
            if (ViewPager2.this.getImportantForAccessibility() == 0) {
                ViewPager2.this.setImportantForAccessibility(1);
            }
        }

        /* renamed from: b */
        public final void m1387b(int i6) {
            ViewPager2 viewPager2 = ViewPager2.this;
            if (viewPager2.f2212A) {
                viewPager2.m1383d(i6, true);
            }
        }

        /* renamed from: c */
        public final void m1388c() {
            int mo1081c;
            ViewPager2 viewPager2 = ViewPager2.this;
            C0766p.m2183p(viewPager2);
            int i6 = R.id.accessibilityActionPageRight;
            C0766p.m2184q(R.id.accessibilityActionPageRight, viewPager2);
            C0766p.m2180m(viewPager2, 0);
            C0766p.m2184q(R.id.accessibilityActionPageUp, viewPager2);
            C0766p.m2180m(viewPager2, 0);
            C0766p.m2184q(R.id.accessibilityActionPageDown, viewPager2);
            C0766p.m2180m(viewPager2, 0);
            if (ViewPager2.this.getAdapter() == null || (mo1081c = ViewPager2.this.getAdapter().mo1081c()) == 0) {
                return;
            }
            ViewPager2 viewPager22 = ViewPager2.this;
            if (viewPager22.f2212A) {
                if (viewPager22.getOrientation() != 0) {
                    if (ViewPager2.this.f2218m < mo1081c - 1) {
                        C0766p.m2185r(viewPager2, new C0831b.a(R.id.accessibilityActionPageDown), this.f2234a);
                    }
                    if (ViewPager2.this.f2218m > 0) {
                        C0766p.m2185r(viewPager2, new C0831b.a(R.id.accessibilityActionPageUp), this.f2235b);
                        return;
                    }
                    return;
                }
                boolean m1380a = ViewPager2.this.m1380a();
                int i7 = m1380a ? 16908360 : 16908361;
                if (!m1380a) {
                    i6 = 16908360;
                }
                if (ViewPager2.this.f2218m < mo1081c - 1) {
                    C0766p.m2185r(viewPager2, new C0831b.a(i7), this.f2234a);
                }
                if (ViewPager2.this.f2218m > 0) {
                    C0766p.m2185r(viewPager2, new C0831b.a(i6), this.f2235b);
                }
            }
        }
    }

    /* renamed from: androidx.viewpager2.widget.ViewPager2$g */
    public interface InterfaceC0359g {
        /* renamed from: a */
        void m1390a();
    }

    /* renamed from: androidx.viewpager2.widget.ViewPager2$h */
    public class C0360h extends C0331u {
        public C0360h() {
        }

        @Override // androidx.recyclerview.widget.C0331u, androidx.recyclerview.widget.AbstractC0309a0
        /* renamed from: c */
        public final View mo1265c(RecyclerView.AbstractC0288m abstractC0288m) {
            if (((C0366c) ViewPager2.this.f2228w.f1406k).f2262m) {
                return null;
            }
            return super.mo1265c(abstractC0288m);
        }
    }

    /* renamed from: androidx.viewpager2.widget.ViewPager2$i */
    public class C0361i extends RecyclerView {
        public C0361i(Context context) {
            super(context, null);
        }

        @Override // androidx.recyclerview.widget.RecyclerView, android.view.ViewGroup, android.view.View
        public final CharSequence getAccessibilityClassName() {
            Objects.requireNonNull(ViewPager2.this.f2214C);
            return super.getAccessibilityClassName();
        }

        @Override // android.view.View
        public final void onInitializeAccessibilityEvent(AccessibilityEvent accessibilityEvent) {
            super.onInitializeAccessibilityEvent(accessibilityEvent);
            accessibilityEvent.setFromIndex(ViewPager2.this.f2218m);
            accessibilityEvent.setToIndex(ViewPager2.this.f2218m);
            accessibilityEvent.setSource(ViewPager2.this);
            accessibilityEvent.setClassName("androidx.viewpager.widget.ViewPager");
        }

        @Override // androidx.recyclerview.widget.RecyclerView, android.view.ViewGroup
        public final boolean onInterceptTouchEvent(MotionEvent motionEvent) {
            return ViewPager2.this.f2212A && super.onInterceptTouchEvent(motionEvent);
        }

        @Override // androidx.recyclerview.widget.RecyclerView, android.view.View
        @SuppressLint({"ClickableViewAccessibility"})
        public final boolean onTouchEvent(MotionEvent motionEvent) {
            return ViewPager2.this.f2212A && super.onTouchEvent(motionEvent);
        }
    }

    /* renamed from: androidx.viewpager2.widget.ViewPager2$j */
    public static class C0362j extends View.BaseSavedState {
        public static final Parcelable.Creator<C0362j> CREATOR = new a();

        /* renamed from: j */
        public int f2242j;

        /* renamed from: k */
        public int f2243k;

        /* renamed from: l */
        public Parcelable f2244l;

        /* renamed from: androidx.viewpager2.widget.ViewPager2$j$a */
        public static class a implements Parcelable.ClassLoaderCreator<C0362j> {
            @Override // android.os.Parcelable.Creator
            public final Object createFromParcel(Parcel parcel) {
                return new C0362j(parcel, null);
            }

            @Override // android.os.Parcelable.Creator
            public final Object[] newArray(int i6) {
                return new C0362j[i6];
            }

            @Override // android.os.Parcelable.ClassLoaderCreator
            public final C0362j createFromParcel(Parcel parcel, ClassLoader classLoader) {
                return new C0362j(parcel, classLoader);
            }
        }

        public C0362j(Parcel parcel, ClassLoader classLoader) {
            super(parcel, classLoader);
            this.f2242j = parcel.readInt();
            this.f2243k = parcel.readInt();
            this.f2244l = parcel.readParcelable(classLoader);
        }

        public C0362j(Parcelable parcelable) {
            super(parcelable);
        }

        @Override // android.view.View.BaseSavedState, android.view.AbsSavedState, android.os.Parcelable
        public final void writeToParcel(Parcel parcel, int i6) {
            super.writeToParcel(parcel, i6);
            parcel.writeInt(this.f2242j);
            parcel.writeInt(this.f2243k);
            parcel.writeParcelable(this.f2244l, i6);
        }
    }

    /* renamed from: androidx.viewpager2.widget.ViewPager2$k */
    public static class RunnableC0363k implements Runnable {

        /* renamed from: j */
        public final int f2245j;

        /* renamed from: k */
        public final RecyclerView f2246k;

        public RunnableC0363k(int i6, RecyclerView recyclerView) {
            this.f2245j = i6;
            this.f2246k = recyclerView;
        }

        @Override // java.lang.Runnable
        public final void run() {
            this.f2246k.m1030h0(this.f2245j);
        }
    }

    /* JADX WARN: Type inference failed for: r12v5, types: [java.util.ArrayList, java.util.List<androidx.recyclerview.widget.RecyclerView$o>] */
    public ViewPager2(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.f2215j = new Rect();
        this.f2216k = new Rect();
        this.f2217l = new C0364a();
        this.f2219n = false;
        this.f2220o = new C0353a();
        this.f2222q = -1;
        this.f2230y = null;
        this.f2231z = false;
        this.f2212A = true;
        this.f2213B = -1;
        this.f2214C = new C0358f();
        C0361i c0361i = new C0361i(context);
        this.f2224s = c0361i;
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        c0361i.setId(View.generateViewId());
        this.f2224s.setDescendantFocusability(131072);
        C0356d c0356d = new C0356d(context);
        this.f2221p = c0356d;
        this.f2224s.setLayoutManager(c0356d);
        this.f2224s.setScrollingTouchSlop(1);
        int[] iArr = C0385m.f2327R;
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, iArr);
        if (Build.VERSION.SDK_INT >= 29) {
            saveAttributeDataForStyleable(context, iArr, attributeSet, obtainStyledAttributes, 0, 0);
        }
        try {
            setOrientation(obtainStyledAttributes.getInt(0, 0));
            obtainStyledAttributes.recycle();
            this.f2224s.setLayoutParams(new ViewGroup.LayoutParams(-1, -1));
            C0361i c0361i2 = this.f2224s;
            C2062c c2062c = new C2062c();
            if (c0361i2.f1786K == null) {
                c0361i2.f1786K = new ArrayList();
            }
            c0361i2.f1786K.add(c2062c);
            C0366c c0366c = new C0366c(this);
            this.f2226u = c0366c;
            this.f2228w = new C0208e0(this, c0366c, this.f2224s);
            C0360h c0360h = new C0360h();
            this.f2225t = c0360h;
            c0360h.m1263a(this.f2224s);
            this.f2224s.m1029h(this.f2226u);
            C0364a c0364a = new C0364a();
            this.f2227v = c0364a;
            this.f2226u.f2250a = c0364a;
            C0367d c0367d = new C0367d(this);
            C0368e c0368e = new C0368e(this);
            c0364a.m1391d(c0367d);
            this.f2227v.m1391d(c0368e);
            this.f2214C.m1386a(this.f2224s);
            this.f2227v.m1391d(this.f2217l);
            C0365b c0365b = new C0365b(this.f2221p);
            this.f2229x = c0365b;
            this.f2227v.m1391d(c0365b);
            C0361i c0361i3 = this.f2224s;
            attachViewToParent(c0361i3, 0, c0361i3.getLayoutParams());
        } catch (Throwable th) {
            obtainStyledAttributes.recycle();
            throw th;
        }
    }

    /* renamed from: a */
    public final boolean m1380a() {
        return this.f2221p.m1126G() == 1;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* renamed from: b */
    public final void m1381b() {
        RecyclerView.AbstractC0280e adapter;
        if (this.f2222q == -1 || (adapter = getAdapter()) == 0) {
            return;
        }
        Parcelable parcelable = this.f2223r;
        if (parcelable != null) {
            if (adapter instanceof InterfaceC0352f) {
                ((InterfaceC0352f) adapter).mo1367b(parcelable);
            }
            this.f2223r = null;
        }
        int max = Math.max(0, Math.min(this.f2222q, adapter.mo1081c() - 1));
        this.f2218m = max;
        this.f2222q = -1;
        this.f2224s.m1024e0(max);
        this.f2214C.m1388c();
    }

    /* renamed from: c */
    public final void m1382c(int i6, boolean z5) {
        if (((C0366c) this.f2228w.f1406k).f2262m) {
            throw new IllegalStateException("Cannot change current item when ViewPager2 is fake dragging");
        }
        m1383d(i6, z5);
    }

    @Override // android.view.View
    public final boolean canScrollHorizontally(int i6) {
        return this.f2224s.canScrollHorizontally(i6);
    }

    @Override // android.view.View
    public final boolean canScrollVertically(int i6) {
        return this.f2224s.canScrollVertically(i6);
    }

    /* renamed from: d */
    public final void m1383d(int i6, boolean z5) {
        RecyclerView.AbstractC0280e adapter = getAdapter();
        if (adapter == null) {
            if (this.f2222q != -1) {
                this.f2222q = Math.max(i6, 0);
                return;
            }
            return;
        }
        if (adapter.mo1081c() <= 0) {
            return;
        }
        int min = Math.min(Math.max(i6, 0), adapter.mo1081c() - 1);
        int i7 = this.f2218m;
        if (min == i7) {
            if (this.f2226u.f2255f == 0) {
                return;
            }
        }
        if (min == i7 && z5) {
            return;
        }
        double d6 = i7;
        this.f2218m = min;
        this.f2214C.m1388c();
        C0366c c0366c = this.f2226u;
        if (!(c0366c.f2255f == 0)) {
            c0366c.m1396f();
            C0366c.a aVar = c0366c.f2256g;
            d6 = aVar.f2263a + aVar.f2264b;
        }
        C0366c c0366c2 = this.f2226u;
        c0366c2.f2254e = z5 ? 2 : 3;
        c0366c2.f2262m = false;
        boolean z6 = c0366c2.f2258i != min;
        c0366c2.f2258i = min;
        c0366c2.m1394d(2);
        if (z6) {
            c0366c2.m1393c(min);
        }
        if (!z5) {
            this.f2224s.m1024e0(min);
            return;
        }
        double d7 = min;
        if (Math.abs(d7 - d6) <= 3.0d) {
            this.f2224s.m1030h0(min);
            return;
        }
        this.f2224s.m1024e0(d7 > d6 ? min - 3 : min + 3);
        C0361i c0361i = this.f2224s;
        c0361i.post(new RunnableC0363k(min, c0361i));
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void dispatchRestoreInstanceState(SparseArray<Parcelable> sparseArray) {
        Parcelable parcelable = sparseArray.get(getId());
        if (parcelable instanceof C0362j) {
            int i6 = ((C0362j) parcelable).f2242j;
            sparseArray.put(this.f2224s.getId(), sparseArray.get(i6));
            sparseArray.remove(i6);
        }
        super.dispatchRestoreInstanceState(sparseArray);
        m1381b();
    }

    /* renamed from: e */
    public final void m1384e() {
        C0360h c0360h = this.f2225t;
        if (c0360h == null) {
            throw new IllegalStateException("Design assumption violated.");
        }
        View mo1265c = c0360h.mo1265c(this.f2221p);
        if (mo1265c == null) {
            return;
        }
        int m1135N = this.f2221p.m1135N(mo1265c);
        if (m1135N != this.f2218m && getScrollState() == 0) {
            this.f2227v.mo1379c(m1135N);
        }
        this.f2219n = false;
    }

    @Override // android.view.ViewGroup, android.view.View
    public CharSequence getAccessibilityClassName() {
        Objects.requireNonNull(this.f2214C);
        Objects.requireNonNull(this.f2214C);
        return "androidx.viewpager.widget.ViewPager";
    }

    public RecyclerView.AbstractC0280e getAdapter() {
        return this.f2224s.getAdapter();
    }

    public int getCurrentItem() {
        return this.f2218m;
    }

    public int getItemDecorationCount() {
        return this.f2224s.getItemDecorationCount();
    }

    public int getOffscreenPageLimit() {
        return this.f2213B;
    }

    public int getOrientation() {
        return this.f2221p.f1734r;
    }

    public int getPageSize() {
        int height;
        int paddingBottom;
        C0361i c0361i = this.f2224s;
        if (getOrientation() == 0) {
            height = c0361i.getWidth() - c0361i.getPaddingLeft();
            paddingBottom = c0361i.getPaddingRight();
        } else {
            height = c0361i.getHeight() - c0361i.getPaddingTop();
            paddingBottom = c0361i.getPaddingBottom();
        }
        return height - paddingBottom;
    }

    public int getScrollState() {
        return this.f2226u.f2255f;
    }

    @Override // android.view.View
    public final void onInitializeAccessibilityNodeInfo(AccessibilityNodeInfo accessibilityNodeInfo) {
        int i6;
        int i7;
        int mo1081c;
        super.onInitializeAccessibilityNodeInfo(accessibilityNodeInfo);
        C0358f c0358f = this.f2214C;
        if (ViewPager2.this.getAdapter() == null) {
            i6 = 0;
            i7 = 0;
        } else if (ViewPager2.this.getOrientation() == 1) {
            i6 = ViewPager2.this.getAdapter().mo1081c();
            i7 = 0;
        } else {
            i7 = ViewPager2.this.getAdapter().mo1081c();
            i6 = 0;
        }
        accessibilityNodeInfo.setCollectionInfo((AccessibilityNodeInfo.CollectionInfo) C0831b.b.m2339a(i6, i7, 0).f4270a);
        RecyclerView.AbstractC0280e adapter = ViewPager2.this.getAdapter();
        if (adapter == null || (mo1081c = adapter.mo1081c()) == 0) {
            return;
        }
        ViewPager2 viewPager2 = ViewPager2.this;
        if (viewPager2.f2212A) {
            if (viewPager2.f2218m > 0) {
                accessibilityNodeInfo.addAction(8192);
            }
            if (ViewPager2.this.f2218m < mo1081c - 1) {
                accessibilityNodeInfo.addAction(4096);
            }
            accessibilityNodeInfo.setScrollable(true);
        }
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onLayout(boolean z5, int i6, int i7, int i8, int i9) {
        int measuredWidth = this.f2224s.getMeasuredWidth();
        int measuredHeight = this.f2224s.getMeasuredHeight();
        this.f2215j.left = getPaddingLeft();
        this.f2215j.right = (i8 - i6) - getPaddingRight();
        this.f2215j.top = getPaddingTop();
        this.f2215j.bottom = (i9 - i7) - getPaddingBottom();
        Gravity.apply(8388659, measuredWidth, measuredHeight, this.f2215j, this.f2216k);
        C0361i c0361i = this.f2224s;
        Rect rect = this.f2216k;
        c0361i.layout(rect.left, rect.top, rect.right, rect.bottom);
        if (this.f2219n) {
            m1384e();
        }
    }

    @Override // android.view.View
    public final void onMeasure(int i6, int i7) {
        measureChild(this.f2224s, i6, i7);
        int measuredWidth = this.f2224s.getMeasuredWidth();
        int measuredHeight = this.f2224s.getMeasuredHeight();
        int measuredState = this.f2224s.getMeasuredState();
        int paddingRight = getPaddingRight() + getPaddingLeft() + measuredWidth;
        int paddingBottom = getPaddingBottom() + getPaddingTop() + measuredHeight;
        setMeasuredDimension(View.resolveSizeAndState(Math.max(paddingRight, getSuggestedMinimumWidth()), i6, measuredState), View.resolveSizeAndState(Math.max(paddingBottom, getSuggestedMinimumHeight()), i7, measuredState << 16));
    }

    @Override // android.view.View
    public final void onRestoreInstanceState(Parcelable parcelable) {
        if (!(parcelable instanceof C0362j)) {
            super.onRestoreInstanceState(parcelable);
            return;
        }
        C0362j c0362j = (C0362j) parcelable;
        super.onRestoreInstanceState(c0362j.getSuperState());
        this.f2222q = c0362j.f2243k;
        this.f2223r = c0362j.f2244l;
    }

    @Override // android.view.View
    public final Parcelable onSaveInstanceState() {
        C0362j c0362j = new C0362j(super.onSaveInstanceState());
        c0362j.f2242j = this.f2224s.getId();
        int i6 = this.f2222q;
        if (i6 == -1) {
            i6 = this.f2218m;
        }
        c0362j.f2243k = i6;
        Parcelable parcelable = this.f2223r;
        if (parcelable == null) {
            Object adapter = this.f2224s.getAdapter();
            if (adapter instanceof InterfaceC0352f) {
                parcelable = ((InterfaceC0352f) adapter).mo1366a();
            }
            return c0362j;
        }
        c0362j.f2244l = parcelable;
        return c0362j;
    }

    @Override // android.view.ViewGroup
    public final void onViewAdded(View view) {
        throw new IllegalStateException("ViewPager2 does not support direct child views");
    }

    @Override // android.view.View
    public final boolean performAccessibilityAction(int i6, Bundle bundle) {
        Objects.requireNonNull(this.f2214C);
        if (!(i6 == 8192 || i6 == 4096)) {
            return super.performAccessibilityAction(i6, bundle);
        }
        C0358f c0358f = this.f2214C;
        Objects.requireNonNull(c0358f);
        if (!(i6 == 8192 || i6 == 4096)) {
            throw new IllegalStateException();
        }
        c0358f.m1387b(i6 == 8192 ? ViewPager2.this.getCurrentItem() - 1 : ViewPager2.this.getCurrentItem() + 1);
        return true;
    }

    public void setAdapter(RecyclerView.AbstractC0280e abstractC0280e) {
        RecyclerView.AbstractC0280e adapter = this.f2224s.getAdapter();
        C0358f c0358f = this.f2214C;
        Objects.requireNonNull(c0358f);
        if (adapter != null) {
            adapter.m1092n(c0358f.f2236c);
        }
        if (adapter != null) {
            adapter.m1092n(this.f2220o);
        }
        this.f2224s.setAdapter(abstractC0280e);
        this.f2218m = 0;
        m1381b();
        C0358f c0358f2 = this.f2214C;
        c0358f2.m1388c();
        if (abstractC0280e != null) {
            abstractC0280e.m1091m(c0358f2.f2236c);
        }
        if (abstractC0280e != null) {
            abstractC0280e.m1091m(this.f2220o);
        }
    }

    public void setCurrentItem(int i6) {
        m1382c(i6, true);
    }

    @Override // android.view.View
    public void setLayoutDirection(int i6) {
        super.setLayoutDirection(i6);
        this.f2214C.m1388c();
    }

    public void setOffscreenPageLimit(int i6) {
        if (i6 < 1 && i6 != -1) {
            throw new IllegalArgumentException("Offscreen page limit must be OFFSCREEN_PAGE_LIMIT_DEFAULT or a number > 0");
        }
        this.f2213B = i6;
        this.f2224s.requestLayout();
    }

    public void setOrientation(int i6) {
        this.f2221p.m973k1(i6);
        this.f2214C.m1388c();
    }

    public void setPageTransformer(InterfaceC0359g interfaceC0359g) {
        boolean z5 = this.f2231z;
        if (interfaceC0359g != null) {
            if (!z5) {
                this.f2230y = this.f2224s.getItemAnimator();
                this.f2231z = true;
            }
            this.f2224s.setItemAnimator(null);
        } else if (z5) {
            this.f2224s.setItemAnimator(this.f2230y);
            this.f2230y = null;
            this.f2231z = false;
        }
        C0365b c0365b = this.f2229x;
        if (interfaceC0359g == c0365b.f2249b) {
            return;
        }
        c0365b.f2249b = interfaceC0359g;
        if (interfaceC0359g == null) {
            return;
        }
        C0366c c0366c = this.f2226u;
        c0366c.m1396f();
        C0366c.a aVar = c0366c.f2256g;
        double d6 = aVar.f2263a + aVar.f2264b;
        int i6 = (int) d6;
        float f6 = (float) (d6 - i6);
        this.f2229x.mo1385b(i6, f6, Math.round(getPageSize() * f6));
    }

    public void setUserInputEnabled(boolean z5) {
        this.f2212A = z5;
        this.f2214C.m1388c();
    }
}
