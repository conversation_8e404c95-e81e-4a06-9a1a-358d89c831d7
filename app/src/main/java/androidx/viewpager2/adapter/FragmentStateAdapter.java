package androidx.viewpager2.adapter;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Parcelable;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.FrameLayout;
import androidx.activity.result.C0052a;
import androidx.fragment.app.AbstractC0240x;
import androidx.fragment.app.ActivityC0229p;
import androidx.fragment.app.C0199a;
import androidx.fragment.app.C0206d0;
import androidx.fragment.app.C0239w;
import androidx.fragment.app.ComponentCallbacksC0223m;
import androidx.lifecycle.AbstractC0251d;
import androidx.lifecycle.C0255h;
import androidx.lifecycle.InterfaceC0252e;
import androidx.lifecycle.InterfaceC0254g;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;
import com.liaoyuan.aicast.MainActivity;
import java.util.Iterator;
import java.util.Objects;
import java.util.WeakHashMap;
import p029e0.C0766p;
import p029e0.C0769s;
import p077l.C1049c;
import p077l.C1050d;

/* loaded from: classes.dex */
public abstract class FragmentStateAdapter extends RecyclerView.AbstractC0280e<C0351e> implements InterfaceC0352f {

    /* renamed from: c */
    public final AbstractC0251d f2183c;

    /* renamed from: d */
    public final AbstractC0240x f2184d;

    /* renamed from: e */
    public final C1050d<ComponentCallbacksC0223m> f2185e;

    /* renamed from: f */
    public final C1050d<ComponentCallbacksC0223m.e> f2186f;

    /* renamed from: g */
    public final C1050d<Integer> f2187g;

    /* renamed from: h */
    public C0346c f2188h;

    /* renamed from: i */
    public boolean f2189i;

    /* renamed from: j */
    public boolean f2190j;

    /* renamed from: androidx.viewpager2.adapter.FragmentStateAdapter$a */
    public class C0344a extends AbstractC0240x.k {

        /* renamed from: a */
        public final /* synthetic */ ComponentCallbacksC0223m f2196a;

        /* renamed from: b */
        public final /* synthetic */ FrameLayout f2197b;

        public C0344a(ComponentCallbacksC0223m componentCallbacksC0223m, FrameLayout frameLayout) {
            this.f2196a = componentCallbacksC0223m;
            this.f2197b = frameLayout;
        }
    }

    /* renamed from: androidx.viewpager2.adapter.FragmentStateAdapter$b */
    public static abstract class AbstractC0345b extends RecyclerView.AbstractC0282g {
        @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0282g
        /* renamed from: a */
        public abstract void mo1096a();

        @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0282g
        /* renamed from: b */
        public final void mo1097b() {
            mo1096a();
        }

        @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0282g
        /* renamed from: c */
        public final void mo1098c() {
            mo1096a();
        }
    }

    /* renamed from: androidx.viewpager2.adapter.FragmentStateAdapter$c */
    public class C0346c {

        /* renamed from: a */
        public C0349c f2199a;

        /* renamed from: b */
        public C0350d f2200b;

        /* renamed from: c */
        public InterfaceC0252e f2201c;

        /* renamed from: d */
        public ViewPager2 f2202d;

        /* renamed from: e */
        public long f2203e = -1;

        public C0346c() {
        }

        /* renamed from: a */
        public final ViewPager2 m1376a(RecyclerView recyclerView) {
            ViewParent parent = recyclerView.getParent();
            if (parent instanceof ViewPager2) {
                return (ViewPager2) parent;
            }
            throw new IllegalStateException("Expected ViewPager2 instance. Got: " + parent);
        }

        /* renamed from: b */
        public final void m1377b(boolean z5) {
            int currentItem;
            if (FragmentStateAdapter.this.m1375v() || this.f2202d.getScrollState() != 0 || FragmentStateAdapter.this.f2185e.m2670f() || FragmentStateAdapter.this.mo1081c() == 0 || (currentItem = this.f2202d.getCurrentItem()) >= FragmentStateAdapter.this.mo1081c()) {
                return;
            }
            Objects.requireNonNull(FragmentStateAdapter.this);
            long j6 = currentItem;
            if (j6 != this.f2203e || z5) {
                ComponentCallbacksC0223m componentCallbacksC0223m = null;
                ComponentCallbacksC0223m m2669e = FragmentStateAdapter.this.f2185e.m2669e(j6, null);
                if (m2669e == null || !m2669e.m745w()) {
                    return;
                }
                this.f2203e = j6;
                C0199a c0199a = new C0199a(FragmentStateAdapter.this.f2184d);
                for (int i6 = 0; i6 < FragmentStateAdapter.this.f2185e.m2674j(); i6++) {
                    long m2671g = FragmentStateAdapter.this.f2185e.m2671g(i6);
                    ComponentCallbacksC0223m m2675k = FragmentStateAdapter.this.f2185e.m2675k(i6);
                    if (m2675k.m745w()) {
                        if (m2671g != this.f2203e) {
                            c0199a.m624k(m2675k, AbstractC0251d.c.STARTED);
                        } else {
                            componentCallbacksC0223m = m2675k;
                        }
                        boolean z6 = m2671g == this.f2203e;
                        if (m2675k.f1504K != z6) {
                            m2675k.f1504K = z6;
                        }
                    }
                }
                if (componentCallbacksC0223m != null) {
                    c0199a.m624k(componentCallbacksC0223m, AbstractC0251d.c.RESUMED);
                }
                if (c0199a.f1412a.isEmpty()) {
                    return;
                }
                c0199a.m618e();
            }
        }
    }

    public FragmentStateAdapter(ActivityC0229p activityC0229p) {
        AbstractC0240x m765n = activityC0229p.m765n();
        C0255h c0255h = activityC0229p.f184l;
        this.f2185e = new C1050d<>();
        this.f2186f = new C1050d<>();
        this.f2187g = new C1050d<>();
        this.f2189i = false;
        this.f2190j = false;
        this.f2184d = m765n;
        this.f2183c = c0255h;
        if (this.f1871a.m1093a()) {
            throw new IllegalStateException("Cannot change whether this adapter has stable IDs while the adapter has registered observers.");
        }
        this.f1872b = true;
    }

    @Override // androidx.viewpager2.adapter.InterfaceC0352f
    /* renamed from: a */
    public final Parcelable mo1366a() {
        Bundle bundle = new Bundle(this.f2186f.m2674j() + this.f2185e.m2674j());
        for (int i6 = 0; i6 < this.f2185e.m2674j(); i6++) {
            long m2671g = this.f2185e.m2671g(i6);
            ComponentCallbacksC0223m m2669e = this.f2185e.m2669e(m2671g, null);
            if (m2669e != null && m2669e.m745w()) {
                String str = "f#" + m2671g;
                AbstractC0240x abstractC0240x = this.f2184d;
                Objects.requireNonNull(abstractC0240x);
                if (m2669e.f1494A != abstractC0240x) {
                    abstractC0240x.m826e0(new IllegalStateException("Fragment " + m2669e + " is not currently in the FragmentManager"));
                    throw null;
                }
                bundle.putString(str, m2669e.f1523n);
            }
        }
        for (int i7 = 0; i7 < this.f2186f.m2674j(); i7++) {
            long m2671g2 = this.f2186f.m2671g(i7);
            if (m1369p(m2671g2)) {
                bundle.putParcelable("s#" + m2671g2, this.f2186f.m2669e(m2671g2, null));
            }
        }
        return bundle;
    }

    @Override // androidx.viewpager2.adapter.InterfaceC0352f
    /* renamed from: b */
    public final void mo1367b(Parcelable parcelable) {
        if (!this.f2186f.m2670f() || !this.f2185e.m2670f()) {
            throw new IllegalStateException("Expected the adapter to be 'fresh' while restoring state.");
        }
        Bundle bundle = (Bundle) parcelable;
        if (bundle.getClassLoader() == null) {
            bundle.setClassLoader(getClass().getClassLoader());
        }
        Iterator<String> it = bundle.keySet().iterator();
        while (true) {
            if (!it.hasNext()) {
                if (this.f2185e.m2670f()) {
                    return;
                }
                this.f2190j = true;
                this.f2189i = true;
                m1370q();
                final Handler handler = new Handler(Looper.getMainLooper());
                final RunnableC0348b runnableC0348b = new RunnableC0348b(this);
                this.f2183c.mo871a(new InterfaceC0252e() { // from class: androidx.viewpager2.adapter.FragmentStateAdapter.5
                    @Override // androidx.lifecycle.InterfaceC0252e
                    /* renamed from: e */
                    public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
                        if (bVar == AbstractC0251d.b.ON_DESTROY) {
                            handler.removeCallbacks(runnableC0348b);
                            interfaceC0254g.mo86a().mo872b(this);
                        }
                    }
                });
                handler.postDelayed(runnableC0348b, 10000L);
                return;
            }
            String next = it.next();
            if (next.startsWith("f#") && next.length() > 2) {
                long parseLong = Long.parseLong(next.substring(2));
                AbstractC0240x abstractC0240x = this.f2184d;
                Objects.requireNonNull(abstractC0240x);
                String string = bundle.getString(next);
                ComponentCallbacksC0223m componentCallbacksC0223m = null;
                if (string != null) {
                    ComponentCallbacksC0223m m795D = abstractC0240x.m795D(string);
                    if (m795D == null) {
                        abstractC0240x.m826e0(new IllegalStateException("Fragment no longer exists for key " + next + ": unique id " + string));
                        throw null;
                    }
                    componentCallbacksC0223m = m795D;
                }
                this.f2185e.m2672h(parseLong, componentCallbacksC0223m);
            } else {
                if (!(next.startsWith("s#") && next.length() > 2)) {
                    throw new IllegalArgumentException(C0052a.m103g("Unexpected key in savedState: ", next));
                }
                long parseLong2 = Long.parseLong(next.substring(2));
                ComponentCallbacksC0223m.e eVar = (ComponentCallbacksC0223m.e) bundle.getParcelable(next);
                if (m1369p(parseLong2)) {
                    this.f2186f.m2672h(parseLong2, eVar);
                }
            }
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0280e
    /* renamed from: d */
    public final long mo1082d(int i6) {
        return i6;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0280e
    /* renamed from: f */
    public final void mo1084f(RecyclerView recyclerView) {
        if (!(this.f2188h == null)) {
            throw new IllegalArgumentException();
        }
        final C0346c c0346c = new C0346c();
        this.f2188h = c0346c;
        ViewPager2 m1376a = c0346c.m1376a(recyclerView);
        c0346c.f2202d = m1376a;
        C0349c c0349c = new C0349c(c0346c);
        c0346c.f2199a = c0349c;
        m1376a.f2217l.m1391d(c0349c);
        C0350d c0350d = new C0350d(c0346c);
        c0346c.f2200b = c0350d;
        m1091m(c0350d);
        InterfaceC0252e interfaceC0252e = new InterfaceC0252e() { // from class: androidx.viewpager2.adapter.FragmentStateAdapter$FragmentMaxLifecycleEnforcer$3
            @Override // androidx.lifecycle.InterfaceC0252e
            /* renamed from: e */
            public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
                FragmentStateAdapter.C0346c.this.m1377b(false);
            }
        };
        c0346c.f2201c = interfaceC0252e;
        this.f2183c.mo871a(interfaceC0252e);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0280e
    /* renamed from: g */
    public final void mo1085g(C0351e c0351e, int i6) {
        Bundle bundle;
        C0351e c0351e2 = c0351e;
        long j6 = c0351e2.f1856e;
        int id = ((FrameLayout) c0351e2.f1852a).getId();
        Long m1371r = m1371r(id);
        if (m1371r != null && m1371r.longValue() != j6) {
            m1373t(m1371r.longValue());
            this.f2187g.m2673i(m1371r.longValue());
        }
        this.f2187g.m2672h(j6, Integer.valueOf(id));
        long j7 = i6;
        if (!this.f2185e.m2667c(j7)) {
            ComponentCallbacksC0223m componentCallbacksC0223m = ((MainActivity.C0662a) this).f3455k.get(i6);
            Bundle bundle2 = null;
            ComponentCallbacksC0223m.e m2669e = this.f2186f.m2669e(j7, null);
            if (componentCallbacksC0223m.f1494A != null) {
                throw new IllegalStateException("Fragment already added");
            }
            if (m2669e != null && (bundle = m2669e.f1551j) != null) {
                bundle2 = bundle;
            }
            componentCallbacksC0223m.f1520k = bundle2;
            this.f2185e.m2672h(j7, componentCallbacksC0223m);
        }
        FrameLayout frameLayout = (FrameLayout) c0351e2.f1852a;
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        if (frameLayout.isAttachedToWindow()) {
            if (frameLayout.getParent() != null) {
                throw new IllegalStateException("Design assumption violated.");
            }
            frameLayout.addOnLayoutChangeListener(new ViewOnLayoutChangeListenerC0347a(this, frameLayout, c0351e2));
        }
        m1370q();
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0280e
    /* renamed from: h */
    public final RecyclerView.AbstractC0277b0 mo1086h(ViewGroup viewGroup) {
        int i6 = C0351e.f2211t;
        FrameLayout frameLayout = new FrameLayout(viewGroup.getContext());
        frameLayout.setLayoutParams(new ViewGroup.LayoutParams(-1, -1));
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        frameLayout.setId(View.generateViewId());
        frameLayout.setSaveEnabled(false);
        return new C0351e(frameLayout);
    }

    /* JADX WARN: Type inference failed for: r3v3, types: [java.util.ArrayList, java.util.List<androidx.viewpager2.widget.ViewPager2$e>] */
    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0280e
    /* renamed from: i */
    public final void mo1087i(RecyclerView recyclerView) {
        C0346c c0346c = this.f2188h;
        ViewPager2 m1376a = c0346c.m1376a(recyclerView);
        m1376a.f2217l.f2247a.remove(c0346c.f2199a);
        FragmentStateAdapter.this.m1092n(c0346c.f2200b);
        FragmentStateAdapter.this.f2183c.mo872b(c0346c.f2201c);
        c0346c.f2202d = null;
        this.f2188h = null;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0280e
    /* renamed from: j */
    public final /* bridge */ /* synthetic */ boolean mo1088j(C0351e c0351e) {
        return true;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0280e
    /* renamed from: k */
    public final void mo1089k(C0351e c0351e) {
        m1372s(c0351e);
        m1370q();
    }

    @Override // androidx.recyclerview.widget.RecyclerView.AbstractC0280e
    /* renamed from: l */
    public final void mo1090l(C0351e c0351e) {
        Long m1371r = m1371r(((FrameLayout) c0351e.f1852a).getId());
        if (m1371r != null) {
            m1373t(m1371r.longValue());
            this.f2187g.m2673i(m1371r.longValue());
        }
    }

    /* renamed from: o */
    public final void m1368o(View view, FrameLayout frameLayout) {
        if (frameLayout.getChildCount() > 1) {
            throw new IllegalStateException("Design assumption violated.");
        }
        if (view.getParent() == frameLayout) {
            return;
        }
        if (frameLayout.getChildCount() > 0) {
            frameLayout.removeAllViews();
        }
        if (view.getParent() != null) {
            ((ViewGroup) view.getParent()).removeView(view);
        }
        frameLayout.addView(view);
    }

    /* renamed from: p */
    public final boolean m1369p(long j6) {
        return j6 >= 0 && j6 < ((long) mo1081c());
    }

    /* renamed from: q */
    public final void m1370q() {
        ComponentCallbacksC0223m m2669e;
        View view;
        if (!this.f2190j || m1375v()) {
            return;
        }
        C1049c c1049c = new C1049c();
        for (int i6 = 0; i6 < this.f2185e.m2674j(); i6++) {
            long m2671g = this.f2185e.m2671g(i6);
            if (!m1369p(m2671g)) {
                c1049c.add(Long.valueOf(m2671g));
                this.f2187g.m2673i(m2671g);
            }
        }
        if (!this.f2189i) {
            this.f2190j = false;
            for (int i7 = 0; i7 < this.f2185e.m2674j(); i7++) {
                long m2671g2 = this.f2185e.m2671g(i7);
                boolean z5 = true;
                if (!this.f2187g.m2667c(m2671g2) && ((m2669e = this.f2185e.m2669e(m2671g2, null)) == null || (view = m2669e.f1507N) == null || view.getParent() == null)) {
                    z5 = false;
                }
                if (!z5) {
                    c1049c.add(Long.valueOf(m2671g2));
                }
            }
        }
        Iterator it = c1049c.iterator();
        while (it.hasNext()) {
            m1373t(((Long) it.next()).longValue());
        }
    }

    /* renamed from: r */
    public final Long m1371r(int i6) {
        Long l6 = null;
        for (int i7 = 0; i7 < this.f2187g.m2674j(); i7++) {
            if (this.f2187g.m2675k(i7).intValue() == i6) {
                if (l6 != null) {
                    throw new IllegalStateException("Design assumption violated: a ViewHolder can only be bound to one item at a time.");
                }
                l6 = Long.valueOf(this.f2187g.m2671g(i7));
            }
        }
        return l6;
    }

    /* renamed from: s */
    public final void m1372s(final C0351e c0351e) {
        ComponentCallbacksC0223m m2669e = this.f2185e.m2669e(c0351e.f1856e, null);
        if (m2669e == null) {
            throw new IllegalStateException("Design assumption violated.");
        }
        FrameLayout frameLayout = (FrameLayout) c0351e.f1852a;
        View view = m2669e.f1507N;
        if (!m2669e.m745w() && view != null) {
            throw new IllegalStateException("Design assumption violated.");
        }
        if (m2669e.m745w() && view == null) {
            m1374u(m2669e, frameLayout);
            return;
        }
        if (m2669e.m745w() && view.getParent() != null) {
            if (view.getParent() != frameLayout) {
                m1368o(view, frameLayout);
                return;
            }
            return;
        }
        if (m2669e.m745w()) {
            m1368o(view, frameLayout);
            return;
        }
        if (m1375v()) {
            if (this.f2184d.f1604C) {
                return;
            }
            this.f2183c.mo871a(new InterfaceC0252e() { // from class: androidx.viewpager2.adapter.FragmentStateAdapter.2
                @Override // androidx.lifecycle.InterfaceC0252e
                /* renamed from: e */
                public final void mo93e(InterfaceC0254g interfaceC0254g, AbstractC0251d.b bVar) {
                    if (FragmentStateAdapter.this.m1375v()) {
                        return;
                    }
                    interfaceC0254g.mo86a().mo872b(this);
                    FrameLayout frameLayout2 = (FrameLayout) c0351e.f1852a;
                    WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
                    if (frameLayout2.isAttachedToWindow()) {
                        FragmentStateAdapter.this.m1372s(c0351e);
                    }
                }
            });
            return;
        }
        m1374u(m2669e, frameLayout);
        C0199a c0199a = new C0199a(this.f2184d);
        StringBuilder m104h = C0052a.m104h("f");
        m104h.append(c0351e.f1856e);
        c0199a.m619f(0, m2669e, m104h.toString(), 1);
        c0199a.m624k(m2669e, AbstractC0251d.c.STARTED);
        c0199a.m618e();
        this.f2188h.m1377b(false);
    }

    /* renamed from: t */
    public final void m1373t(long j6) {
        Bundle m651o;
        ViewParent parent;
        ComponentCallbacksC0223m.e eVar = null;
        ComponentCallbacksC0223m m2669e = this.f2185e.m2669e(j6, null);
        if (m2669e == null) {
            return;
        }
        View view = m2669e.f1507N;
        if (view != null && (parent = view.getParent()) != null) {
            ((FrameLayout) parent).removeAllViews();
        }
        if (!m1369p(j6)) {
            this.f2186f.m2673i(j6);
        }
        if (!m2669e.m745w()) {
            this.f2185e.m2673i(j6);
            return;
        }
        if (m1375v()) {
            this.f2190j = true;
            return;
        }
        if (m2669e.m745w() && m1369p(j6)) {
            C1050d<ComponentCallbacksC0223m.e> c1050d = this.f2186f;
            AbstractC0240x abstractC0240x = this.f2184d;
            C0206d0 m663i = abstractC0240x.f1613c.m663i(m2669e.f1523n);
            if (m663i == null || !m663i.f1400c.equals(m2669e)) {
                abstractC0240x.m826e0(new IllegalStateException("Fragment " + m2669e + " is not currently in the FragmentManager"));
                throw null;
            }
            if (m663i.f1400c.f1519j > -1 && (m651o = m663i.m651o()) != null) {
                eVar = new ComponentCallbacksC0223m.e(m651o);
            }
            c1050d.m2672h(j6, eVar);
        }
        C0199a c0199a = new C0199a(this.f2184d);
        c0199a.m623j(m2669e);
        c0199a.m618e();
        this.f2185e.m2673i(j6);
    }

    /* renamed from: u */
    public final void m1374u(ComponentCallbacksC0223m componentCallbacksC0223m, FrameLayout frameLayout) {
        this.f2184d.f1623m.f1598a.add(new C0239w.a(new C0344a(componentCallbacksC0223m, frameLayout)));
    }

    /* renamed from: v */
    public final boolean m1375v() {
        return this.f2184d.m805O();
    }
}
