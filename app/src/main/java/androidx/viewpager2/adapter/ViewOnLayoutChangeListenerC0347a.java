package androidx.viewpager2.adapter;

import android.view.View;
import android.widget.FrameLayout;

/* renamed from: androidx.viewpager2.adapter.a */
/* loaded from: classes.dex */
public final class ViewOnLayoutChangeListenerC0347a implements View.OnLayoutChangeListener {

    /* renamed from: a */
    public final /* synthetic */ FrameLayout f2205a;

    /* renamed from: b */
    public final /* synthetic */ C0351e f2206b;

    /* renamed from: c */
    public final /* synthetic */ FragmentStateAdapter f2207c;

    public ViewOnLayoutChangeListenerC0347a(FragmentStateAdapter fragmentStateAdapter, FrameLayout frameLayout, C0351e c0351e) {
        this.f2207c = fragmentStateAdapter;
        this.f2205a = frameLayout;
        this.f2206b = c0351e;
    }

    @Override // android.view.View.OnLayoutChangeListener
    public final void onLayoutChange(View view, int i6, int i7, int i8, int i9, int i10, int i11, int i12, int i13) {
        if (this.f2205a.getParent() != null) {
            this.f2205a.removeOnLayoutChangeListener(this);
            this.f2207c.m1372s(this.f2206b);
        }
    }
}
