package androidx.coordinatorlayout.widget;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Parcel;
import android.os.Parcelable;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseArray;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import androidx.activity.result.C0052a;
import com.liaoyuan.aicast.R;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.WeakHashMap;
import p008b0.C0385m;
import p022d0.C0700e;
import p022d0.InterfaceC0698c;
import p029e0.C0761k;
import p029e0.C0766p;
import p029e0.C0769s;
import p029e0.C0772v;
import p029e0.InterfaceC0759i;
import p029e0.InterfaceC0760j;
import p029e0.InterfaceC0762l;
import p064j0.AbstractC0956a;
import p077l.C1053g;
import p090n.C1090c;
import p122s.C1332a;
import p142v.C1450a;

/* loaded from: classes.dex */
public class CoordinatorLayout extends ViewGroup implements InterfaceC0759i, InterfaceC0760j {

    /* renamed from: C */
    public static final String f1248C;

    /* renamed from: D */
    public static final Class<?>[] f1249D;

    /* renamed from: E */
    public static final ThreadLocal<Map<String, Constructor<AbstractC0186c>>> f1250E;

    /* renamed from: F */
    public static final Comparator<View> f1251F;

    /* renamed from: G */
    public static final InterfaceC0698c<Rect> f1252G;

    /* renamed from: A */
    public C0184a f1253A;

    /* renamed from: B */
    public final C0761k f1254B;

    /* renamed from: j */
    public final List<View> f1255j;

    /* renamed from: k */
    public final C1090c f1256k;

    /* renamed from: l */
    public final List<View> f1257l;

    /* renamed from: m */
    public final List<View> f1258m;

    /* renamed from: n */
    public final int[] f1259n;

    /* renamed from: o */
    public final int[] f1260o;

    /* renamed from: p */
    public boolean f1261p;

    /* renamed from: q */
    public boolean f1262q;

    /* renamed from: r */
    public int[] f1263r;

    /* renamed from: s */
    public View f1264s;

    /* renamed from: t */
    public View f1265t;

    /* renamed from: u */
    public ViewTreeObserverOnPreDrawListenerC0190g f1266u;

    /* renamed from: v */
    public boolean f1267v;

    /* renamed from: w */
    public C0772v f1268w;

    /* renamed from: x */
    public boolean f1269x;

    /* renamed from: y */
    public Drawable f1270y;

    /* renamed from: z */
    public ViewGroup.OnHierarchyChangeListener f1271z;

    /* renamed from: androidx.coordinatorlayout.widget.CoordinatorLayout$a */
    public class C0184a implements InterfaceC0762l {
        public C0184a() {
        }

        @Override // p029e0.InterfaceC0762l
        /* renamed from: a */
        public final C0772v mo567a(View view, C0772v c0772v) {
            CoordinatorLayout coordinatorLayout = CoordinatorLayout.this;
            if (!Objects.equals(coordinatorLayout.f1268w, c0772v)) {
                coordinatorLayout.f1268w = c0772v;
                boolean z5 = c0772v.m2220e() > 0;
                coordinatorLayout.f1269x = z5;
                coordinatorLayout.setWillNotDraw(!z5 && coordinatorLayout.getBackground() == null);
                if (!c0772v.m2221g()) {
                    int childCount = coordinatorLayout.getChildCount();
                    for (int i6 = 0; i6 < childCount; i6++) {
                        View childAt = coordinatorLayout.getChildAt(i6);
                        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
                        if (childAt.getFitsSystemWindows() && ((C0189f) childAt.getLayoutParams()).f1274a != null && c0772v.m2221g()) {
                            break;
                        }
                    }
                }
                coordinatorLayout.requestLayout();
            }
            return c0772v;
        }
    }

    /* renamed from: androidx.coordinatorlayout.widget.CoordinatorLayout$b */
    public interface InterfaceC0185b {
        AbstractC0186c getBehavior();
    }

    /* renamed from: androidx.coordinatorlayout.widget.CoordinatorLayout$c */
    public static abstract class AbstractC0186c<V extends View> {
        public AbstractC0186c() {
        }

        public AbstractC0186c(Context context, AttributeSet attributeSet) {
        }

        /* renamed from: a */
        public boolean mo568a(View view) {
            return false;
        }

        /* renamed from: b */
        public boolean mo569b(View view, View view2) {
            return false;
        }

        /* renamed from: c */
        public void mo570c(C0189f c0189f) {
        }

        /* renamed from: d */
        public boolean mo571d(CoordinatorLayout coordinatorLayout, V v6, View view) {
            return false;
        }

        /* renamed from: e */
        public void mo572e(CoordinatorLayout coordinatorLayout, View view) {
        }

        /* renamed from: f */
        public void mo573f() {
        }

        /* renamed from: g */
        public boolean mo40g(CoordinatorLayout coordinatorLayout, V v6, MotionEvent motionEvent) {
            return false;
        }

        /* renamed from: h */
        public boolean mo52h(CoordinatorLayout coordinatorLayout, V v6, int i6) {
            return false;
        }

        /* renamed from: i */
        public boolean mo47i(CoordinatorLayout coordinatorLayout, View view, int i6, int i7, int i8) {
            return false;
        }

        /* renamed from: j */
        public boolean mo574j(View view) {
            return false;
        }

        /* renamed from: k */
        public void mo575k(CoordinatorLayout coordinatorLayout, V v6, View view, int i6, int i7, int[] iArr, int i8) {
        }

        /* renamed from: l */
        public void mo576l(CoordinatorLayout coordinatorLayout, View view, int i6, int i7, int i8, int[] iArr) {
            iArr[0] = iArr[0] + i7;
            iArr[1] = iArr[1] + i8;
        }

        /* renamed from: m */
        public boolean mo577m(CoordinatorLayout coordinatorLayout, V v6, Rect rect, boolean z5) {
            return false;
        }

        /* renamed from: n */
        public void mo578n(View view, Parcelable parcelable) {
        }

        /* renamed from: o */
        public Parcelable mo579o(View view) {
            return View.BaseSavedState.EMPTY_STATE;
        }

        /* renamed from: p */
        public boolean mo580p(CoordinatorLayout coordinatorLayout, View view, View view2, int i6, int i7) {
            return false;
        }

        /* renamed from: q */
        public void mo581q(CoordinatorLayout coordinatorLayout, V v6, View view, int i6) {
        }

        /* renamed from: r */
        public boolean mo41r(CoordinatorLayout coordinatorLayout, V v6, MotionEvent motionEvent) {
            return false;
        }
    }

    @Retention(RetentionPolicy.RUNTIME)
    @Deprecated
    /* renamed from: androidx.coordinatorlayout.widget.CoordinatorLayout$d */
    public @interface InterfaceC0187d {
        Class<? extends AbstractC0186c> value();
    }

    /* renamed from: androidx.coordinatorlayout.widget.CoordinatorLayout$e */
    public class ViewGroupOnHierarchyChangeListenerC0188e implements ViewGroup.OnHierarchyChangeListener {
        public ViewGroupOnHierarchyChangeListenerC0188e() {
        }

        @Override // android.view.ViewGroup.OnHierarchyChangeListener
        public final void onChildViewAdded(View view, View view2) {
            ViewGroup.OnHierarchyChangeListener onHierarchyChangeListener = CoordinatorLayout.this.f1271z;
            if (onHierarchyChangeListener != null) {
                onHierarchyChangeListener.onChildViewAdded(view, view2);
            }
        }

        @Override // android.view.ViewGroup.OnHierarchyChangeListener
        public final void onChildViewRemoved(View view, View view2) {
            CoordinatorLayout.this.m558q(2);
            ViewGroup.OnHierarchyChangeListener onHierarchyChangeListener = CoordinatorLayout.this.f1271z;
            if (onHierarchyChangeListener != null) {
                onHierarchyChangeListener.onChildViewRemoved(view, view2);
            }
        }
    }

    /* renamed from: androidx.coordinatorlayout.widget.CoordinatorLayout$f */
    public static class C0189f extends ViewGroup.MarginLayoutParams {

        /* renamed from: a */
        public AbstractC0186c f1274a;

        /* renamed from: b */
        public boolean f1275b;

        /* renamed from: c */
        public int f1276c;

        /* renamed from: d */
        public int f1277d;

        /* renamed from: e */
        public int f1278e;

        /* renamed from: f */
        public int f1279f;

        /* renamed from: g */
        public int f1280g;

        /* renamed from: h */
        public int f1281h;

        /* renamed from: i */
        public int f1282i;

        /* renamed from: j */
        public int f1283j;

        /* renamed from: k */
        public View f1284k;

        /* renamed from: l */
        public View f1285l;

        /* renamed from: m */
        public boolean f1286m;

        /* renamed from: n */
        public boolean f1287n;

        /* renamed from: o */
        public boolean f1288o;

        /* renamed from: p */
        public boolean f1289p;

        /* renamed from: q */
        public final Rect f1290q;

        public C0189f() {
            super(-2, -2);
            this.f1275b = false;
            this.f1276c = 0;
            this.f1277d = 0;
            this.f1278e = -1;
            this.f1279f = -1;
            this.f1280g = 0;
            this.f1281h = 0;
            this.f1290q = new Rect();
        }

        /* JADX WARN: Multi-variable type inference failed */
        public C0189f(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
            AbstractC0186c newInstance;
            this.f1275b = false;
            this.f1276c = 0;
            this.f1277d = 0;
            this.f1278e = -1;
            this.f1279f = -1;
            this.f1280g = 0;
            this.f1281h = 0;
            this.f1290q = new Rect();
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C0385m.f2326Q);
            this.f1276c = obtainStyledAttributes.getInteger(0, 0);
            this.f1279f = obtainStyledAttributes.getResourceId(1, -1);
            this.f1277d = obtainStyledAttributes.getInteger(2, 0);
            this.f1278e = obtainStyledAttributes.getInteger(6, -1);
            this.f1280g = obtainStyledAttributes.getInt(5, 0);
            this.f1281h = obtainStyledAttributes.getInt(4, 0);
            boolean hasValue = obtainStyledAttributes.hasValue(3);
            this.f1275b = hasValue;
            if (hasValue) {
                String string = obtainStyledAttributes.getString(3);
                String str = CoordinatorLayout.f1248C;
                if (TextUtils.isEmpty(string)) {
                    newInstance = null;
                } else {
                    if (string.startsWith(".")) {
                        string = context.getPackageName() + string;
                    } else if (string.indexOf(46) < 0) {
                        String str2 = CoordinatorLayout.f1248C;
                        if (!TextUtils.isEmpty(str2)) {
                            string = str2 + '.' + string;
                        }
                    }
                    try {
                        ThreadLocal<Map<String, Constructor<AbstractC0186c>>> threadLocal = CoordinatorLayout.f1250E;
                        Map<String, Constructor<AbstractC0186c>> map = threadLocal.get();
                        if (map == null) {
                            map = new HashMap<>();
                            threadLocal.set(map);
                        }
                        Constructor<AbstractC0186c> constructor = map.get(string);
                        if (constructor == null) {
                            constructor = Class.forName(string, false, context.getClassLoader()).getConstructor(CoordinatorLayout.f1249D);
                            constructor.setAccessible(true);
                            map.put(string, constructor);
                        }
                        newInstance = constructor.newInstance(context, attributeSet);
                    } catch (Exception e6) {
                        throw new RuntimeException(C0052a.m103g("Could not inflate Behavior subclass ", string), e6);
                    }
                }
                this.f1274a = newInstance;
            }
            obtainStyledAttributes.recycle();
            AbstractC0186c abstractC0186c = this.f1274a;
            if (abstractC0186c != null) {
                abstractC0186c.mo570c(this);
            }
        }

        public C0189f(ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
            this.f1275b = false;
            this.f1276c = 0;
            this.f1277d = 0;
            this.f1278e = -1;
            this.f1279f = -1;
            this.f1280g = 0;
            this.f1281h = 0;
            this.f1290q = new Rect();
        }

        public C0189f(ViewGroup.MarginLayoutParams marginLayoutParams) {
            super(marginLayoutParams);
            this.f1275b = false;
            this.f1276c = 0;
            this.f1277d = 0;
            this.f1278e = -1;
            this.f1279f = -1;
            this.f1280g = 0;
            this.f1281h = 0;
            this.f1290q = new Rect();
        }

        public C0189f(C0189f c0189f) {
            super((ViewGroup.MarginLayoutParams) c0189f);
            this.f1275b = false;
            this.f1276c = 0;
            this.f1277d = 0;
            this.f1278e = -1;
            this.f1279f = -1;
            this.f1280g = 0;
            this.f1281h = 0;
            this.f1290q = new Rect();
        }

        /* renamed from: a */
        public final boolean m582a(int i6) {
            if (i6 == 0) {
                return this.f1287n;
            }
            if (i6 != 1) {
                return false;
            }
            return this.f1288o;
        }

        /* renamed from: b */
        public final void m583b(int i6, boolean z5) {
            if (i6 == 0) {
                this.f1287n = z5;
            } else {
                if (i6 != 1) {
                    return;
                }
                this.f1288o = z5;
            }
        }
    }

    /* renamed from: androidx.coordinatorlayout.widget.CoordinatorLayout$g */
    public class ViewTreeObserverOnPreDrawListenerC0190g implements ViewTreeObserver.OnPreDrawListener {
        public ViewTreeObserverOnPreDrawListenerC0190g() {
        }

        @Override // android.view.ViewTreeObserver.OnPreDrawListener
        public final boolean onPreDraw() {
            CoordinatorLayout.this.m558q(0);
            return true;
        }
    }

    /* renamed from: androidx.coordinatorlayout.widget.CoordinatorLayout$h */
    public static class C0191h extends AbstractC0956a {
        public static final Parcelable.Creator<C0191h> CREATOR = new a();

        /* renamed from: l */
        public SparseArray<Parcelable> f1292l;

        /* renamed from: androidx.coordinatorlayout.widget.CoordinatorLayout$h$a */
        public static class a implements Parcelable.ClassLoaderCreator<C0191h> {
            @Override // android.os.Parcelable.Creator
            public final Object createFromParcel(Parcel parcel) {
                return new C0191h(parcel, null);
            }

            @Override // android.os.Parcelable.Creator
            public final Object[] newArray(int i6) {
                return new C0191h[i6];
            }

            @Override // android.os.Parcelable.ClassLoaderCreator
            public final C0191h createFromParcel(Parcel parcel, ClassLoader classLoader) {
                return new C0191h(parcel, classLoader);
            }
        }

        public C0191h(Parcel parcel, ClassLoader classLoader) {
            super(parcel, classLoader);
            int readInt = parcel.readInt();
            int[] iArr = new int[readInt];
            parcel.readIntArray(iArr);
            Parcelable[] readParcelableArray = parcel.readParcelableArray(classLoader);
            this.f1292l = new SparseArray<>(readInt);
            for (int i6 = 0; i6 < readInt; i6++) {
                this.f1292l.append(iArr[i6], readParcelableArray[i6]);
            }
        }

        public C0191h(Parcelable parcelable) {
            super(parcelable);
        }

        @Override // p064j0.AbstractC0956a, android.os.Parcelable
        public final void writeToParcel(Parcel parcel, int i6) {
            parcel.writeParcelable(this.f4731j, i6);
            SparseArray<Parcelable> sparseArray = this.f1292l;
            int size = sparseArray != null ? sparseArray.size() : 0;
            parcel.writeInt(size);
            int[] iArr = new int[size];
            Parcelable[] parcelableArr = new Parcelable[size];
            for (int i7 = 0; i7 < size; i7++) {
                iArr[i7] = this.f1292l.keyAt(i7);
                parcelableArr[i7] = this.f1292l.valueAt(i7);
            }
            parcel.writeIntArray(iArr);
            parcel.writeParcelableArray(parcelableArr, i6);
        }
    }

    /* renamed from: androidx.coordinatorlayout.widget.CoordinatorLayout$i */
    public static class C0192i implements Comparator<View> {
        @Override // java.util.Comparator
        public final int compare(View view, View view2) {
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            float z5 = view.getZ();
            float z6 = view2.getZ();
            if (z5 > z6) {
                return -1;
            }
            return z5 < z6 ? 1 : 0;
        }
    }

    static {
        Package r0 = CoordinatorLayout.class.getPackage();
        f1248C = r0 != null ? r0.getName() : null;
        f1251F = new C0192i();
        f1249D = new Class[]{Context.class, AttributeSet.class};
        f1250E = new ThreadLocal<>();
        f1252G = new C0700e(12);
    }

    public CoordinatorLayout(Context context, AttributeSet attributeSet) {
        super(context, attributeSet, R.attr.coordinatorLayoutStyle);
        this.f1255j = new ArrayList();
        this.f1256k = new C1090c(1);
        this.f1257l = new ArrayList();
        this.f1258m = new ArrayList();
        this.f1259n = new int[2];
        this.f1260o = new int[2];
        this.f1254B = new C0761k();
        int[] iArr = C0385m.f2325P;
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, iArr, R.attr.coordinatorLayoutStyle, 0);
        if (Build.VERSION.SDK_INT >= 29) {
            saveAttributeDataForStyleable(context, iArr, attributeSet, obtainStyledAttributes, R.attr.coordinatorLayoutStyle, 0);
        }
        int resourceId = obtainStyledAttributes.getResourceId(0, 0);
        if (resourceId != 0) {
            Resources resources = context.getResources();
            this.f1263r = resources.getIntArray(resourceId);
            float f6 = resources.getDisplayMetrics().density;
            int length = this.f1263r.length;
            for (int i6 = 0; i6 < length; i6++) {
                this.f1263r[i6] = (int) (r3[i6] * f6);
            }
        }
        this.f1270y = obtainStyledAttributes.getDrawable(1);
        obtainStyledAttributes.recycle();
        m566y();
        super.setOnHierarchyChangeListener(new ViewGroupOnHierarchyChangeListenerC0188e());
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        if (getImportantForAccessibility() == 0) {
            setImportantForAccessibility(1);
        }
    }

    /* renamed from: a */
    public static Rect m548a() {
        Rect mo2056b = f1252G.mo2056b();
        return mo2056b == null ? new Rect() : mo2056b;
    }

    /* renamed from: b */
    public final void m549b(C0189f c0189f, Rect rect, int i6, int i7) {
        int width = getWidth();
        int height = getHeight();
        int max = Math.max(getPaddingLeft() + ((ViewGroup.MarginLayoutParams) c0189f).leftMargin, Math.min(rect.left, ((width - getPaddingRight()) - i6) - ((ViewGroup.MarginLayoutParams) c0189f).rightMargin));
        int max2 = Math.max(getPaddingTop() + ((ViewGroup.MarginLayoutParams) c0189f).topMargin, Math.min(rect.top, ((height - getPaddingBottom()) - i7) - ((ViewGroup.MarginLayoutParams) c0189f).bottomMargin));
        rect.set(max, max2, i6 + max, i7 + max2);
    }

    /* renamed from: c */
    public final void m550c(View view, boolean z5, Rect rect) {
        if (view.isLayoutRequested() || view.getVisibility() == 8) {
            rect.setEmpty();
        } else if (z5) {
            m553f(view, rect);
        } else {
            rect.set(view.getLeft(), view.getTop(), view.getRight(), view.getBottom());
        }
    }

    @Override // android.view.ViewGroup
    public final boolean checkLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return (layoutParams instanceof C0189f) && super.checkLayoutParams(layoutParams);
    }

    /* JADX WARN: Type inference failed for: r6v1, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    /* JADX WARN: Type inference failed for: r6v3, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    /* renamed from: d */
    public final List<View> m551d(View view) {
        C1090c c1090c = this.f1256k;
        int i6 = ((C1053g) c1090c.f5213b).f5038l;
        ArrayList arrayList = null;
        for (int i7 = 0; i7 < i6; i7++) {
            ArrayList arrayList2 = (ArrayList) ((C1053g) c1090c.f5213b).m2694l(i7);
            if (arrayList2 != null && arrayList2.contains(view)) {
                if (arrayList == null) {
                    arrayList = new ArrayList();
                }
                arrayList.add(((C1053g) c1090c.f5213b).m2690h(i7));
            }
        }
        this.f1258m.clear();
        if (arrayList != null) {
            this.f1258m.addAll(arrayList);
        }
        return this.f1258m;
    }

    @Override // android.view.ViewGroup
    public final boolean drawChild(Canvas canvas, View view, long j6) {
        AbstractC0186c abstractC0186c = ((C0189f) view.getLayoutParams()).f1274a;
        if (abstractC0186c != null) {
            Objects.requireNonNull(abstractC0186c);
        }
        return super.drawChild(canvas, view, j6);
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        int[] drawableState = getDrawableState();
        Drawable drawable = this.f1270y;
        boolean z5 = false;
        if (drawable != null && drawable.isStateful()) {
            z5 = false | drawable.setState(drawableState);
        }
        if (z5) {
            invalidate();
        }
    }

    /* JADX WARN: Type inference failed for: r0v3, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    /* JADX WARN: Type inference failed for: r0v4, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    /* renamed from: e */
    public final List<View> m552e(View view) {
        List list = (List) ((C1053g) this.f1256k.f5213b).getOrDefault(view, null);
        this.f1258m.clear();
        if (list != null) {
            this.f1258m.addAll(list);
        }
        return this.f1258m;
    }

    /* renamed from: f */
    public final void m553f(View view, Rect rect) {
        ThreadLocal<Matrix> threadLocal = C1332a.f6207a;
        rect.set(0, 0, view.getWidth(), view.getHeight());
        ThreadLocal<Matrix> threadLocal2 = C1332a.f6207a;
        Matrix matrix = threadLocal2.get();
        if (matrix == null) {
            matrix = new Matrix();
            threadLocal2.set(matrix);
        } else {
            matrix.reset();
        }
        C1332a.m3267a(this, view, matrix);
        ThreadLocal<RectF> threadLocal3 = C1332a.f6208b;
        RectF rectF = threadLocal3.get();
        if (rectF == null) {
            rectF = new RectF();
            threadLocal3.set(rectF);
        }
        rectF.set(rect);
        matrix.mapRect(rectF);
        rect.set((int) (rectF.left + 0.5f), (int) (rectF.top + 0.5f), (int) (rectF.right + 0.5f), (int) (rectF.bottom + 0.5f));
    }

    /* renamed from: g */
    public final void m554g(int i6, Rect rect, Rect rect2, C0189f c0189f, int i7, int i8) {
        int i9 = c0189f.f1276c;
        if (i9 == 0) {
            i9 = 17;
        }
        int absoluteGravity = Gravity.getAbsoluteGravity(i9, i6);
        int i10 = c0189f.f1277d;
        if ((i10 & 7) == 0) {
            i10 |= 8388611;
        }
        if ((i10 & 112) == 0) {
            i10 |= 48;
        }
        int absoluteGravity2 = Gravity.getAbsoluteGravity(i10, i6);
        int i11 = absoluteGravity & 7;
        int i12 = absoluteGravity & 112;
        int i13 = absoluteGravity2 & 7;
        int i14 = absoluteGravity2 & 112;
        int width = i13 != 1 ? i13 != 5 ? rect.left : rect.right : rect.left + (rect.width() / 2);
        int height = i14 != 16 ? i14 != 80 ? rect.top : rect.bottom : rect.top + (rect.height() / 2);
        if (i11 == 1) {
            width -= i7 / 2;
        } else if (i11 != 5) {
            width -= i7;
        }
        if (i12 == 16) {
            height -= i8 / 2;
        } else if (i12 != 80) {
            height -= i8;
        }
        rect2.set(width, height, i7 + width, i8 + height);
    }

    @Override // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateDefaultLayoutParams() {
        return new C0189f();
    }

    @Override // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        return new C0189f(getContext(), attributeSet);
    }

    public final List<View> getDependencySortedChildren() {
        m562u();
        return Collections.unmodifiableList(this.f1255j);
    }

    public final C0772v getLastWindowInsets() {
        return this.f1268w;
    }

    @Override // android.view.ViewGroup
    public int getNestedScrollAxes() {
        C0761k c0761k = this.f1254B;
        return c0761k.f4037b | c0761k.f4036a;
    }

    public Drawable getStatusBarBackground() {
        return this.f1270y;
    }

    @Override // android.view.View
    public int getSuggestedMinimumHeight() {
        return Math.max(super.getSuggestedMinimumHeight(), getPaddingBottom() + getPaddingTop());
    }

    @Override // android.view.View
    public int getSuggestedMinimumWidth() {
        return Math.max(super.getSuggestedMinimumWidth(), getPaddingRight() + getPaddingLeft());
    }

    @Override // p029e0.InterfaceC0759i
    /* renamed from: h */
    public final void mo237h(View view, View view2, int i6, int i7) {
        this.f1254B.m2164a(i6, i7);
        this.f1265t = view2;
        int childCount = getChildCount();
        for (int i8 = 0; i8 < childCount; i8++) {
            Objects.requireNonNull((C0189f) getChildAt(i8).getLayoutParams());
        }
    }

    @Override // p029e0.InterfaceC0759i
    /* renamed from: i */
    public final void mo238i(View view, int i6) {
        C0761k c0761k = this.f1254B;
        if (i6 == 1) {
            c0761k.f4037b = 0;
        } else {
            c0761k.f4036a = 0;
        }
        int childCount = getChildCount();
        for (int i7 = 0; i7 < childCount; i7++) {
            View childAt = getChildAt(i7);
            C0189f c0189f = (C0189f) childAt.getLayoutParams();
            if (c0189f.m582a(i6)) {
                AbstractC0186c abstractC0186c = c0189f.f1274a;
                if (abstractC0186c != null) {
                    abstractC0186c.mo581q(this, childAt, view, i6);
                }
                c0189f.m583b(i6, false);
                c0189f.f1289p = false;
            }
        }
        this.f1265t = null;
    }

    @Override // p029e0.InterfaceC0759i
    /* renamed from: j */
    public final void mo239j(View view, int i6, int i7, int[] iArr, int i8) {
        AbstractC0186c abstractC0186c;
        int childCount = getChildCount();
        boolean z5 = false;
        int i9 = 0;
        int i10 = 0;
        for (int i11 = 0; i11 < childCount; i11++) {
            View childAt = getChildAt(i11);
            if (childAt.getVisibility() != 8) {
                C0189f c0189f = (C0189f) childAt.getLayoutParams();
                if (c0189f.m582a(i8) && (abstractC0186c = c0189f.f1274a) != null) {
                    int[] iArr2 = this.f1259n;
                    iArr2[0] = 0;
                    iArr2[1] = 0;
                    abstractC0186c.mo575k(this, childAt, view, i6, i7, iArr2, i8);
                    int[] iArr3 = this.f1259n;
                    i9 = i6 > 0 ? Math.max(i9, iArr3[0]) : Math.min(i9, iArr3[0]);
                    int[] iArr4 = this.f1259n;
                    i10 = i7 > 0 ? Math.max(i10, iArr4[1]) : Math.min(i10, iArr4[1]);
                    z5 = true;
                }
            }
        }
        iArr[0] = i9;
        iArr[1] = i10;
        if (z5) {
            m558q(1);
        }
    }

    /* renamed from: k */
    public final int m555k(int i6) {
        StringBuilder sb;
        int[] iArr = this.f1263r;
        if (iArr == null) {
            sb = new StringBuilder();
            sb.append("No keylines defined for ");
            sb.append(this);
            sb.append(" - attempted index lookup ");
            sb.append(i6);
        } else {
            if (i6 >= 0 && i6 < iArr.length) {
                return iArr[i6];
            }
            sb = new StringBuilder();
            sb.append("Keyline index ");
            sb.append(i6);
            sb.append(" out of range for ");
            sb.append(this);
        }
        Log.e("CoordinatorLayout", sb.toString());
        return 0;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* renamed from: l */
    public final C0189f m556l(View view) {
        C0189f c0189f = (C0189f) view.getLayoutParams();
        if (!c0189f.f1275b) {
            if (view instanceof InterfaceC0185b) {
                AbstractC0186c behavior = ((InterfaceC0185b) view).getBehavior();
                if (behavior == null) {
                    Log.e("CoordinatorLayout", "Attached behavior class is null");
                }
                AbstractC0186c abstractC0186c = c0189f.f1274a;
                if (abstractC0186c != behavior) {
                    if (abstractC0186c != null) {
                        abstractC0186c.mo573f();
                    }
                    c0189f.f1274a = behavior;
                    c0189f.f1275b = true;
                    if (behavior != null) {
                        behavior.mo570c(c0189f);
                    }
                }
            } else {
                InterfaceC0187d interfaceC0187d = null;
                for (Class<?> cls = view.getClass(); cls != null; cls = cls.getSuperclass()) {
                    interfaceC0187d = (InterfaceC0187d) cls.getAnnotation(InterfaceC0187d.class);
                    if (interfaceC0187d != null) {
                        break;
                    }
                }
                if (interfaceC0187d != null) {
                    try {
                        AbstractC0186c newInstance = interfaceC0187d.value().getDeclaredConstructor(new Class[0]).newInstance(new Object[0]);
                        AbstractC0186c abstractC0186c2 = c0189f.f1274a;
                        if (abstractC0186c2 != newInstance) {
                            if (abstractC0186c2 != null) {
                                abstractC0186c2.mo573f();
                            }
                            c0189f.f1274a = newInstance;
                            c0189f.f1275b = true;
                            if (newInstance != null) {
                                newInstance.mo570c(c0189f);
                            }
                        }
                    } catch (Exception e6) {
                        StringBuilder m104h = C0052a.m104h("Default behavior class ");
                        m104h.append(interfaceC0187d.value().getName());
                        m104h.append(" could not be instantiated. Did you forget a default constructor?");
                        Log.e("CoordinatorLayout", m104h.toString(), e6);
                    }
                }
            }
            c0189f.f1275b = true;
        }
        return c0189f;
    }

    @Override // p029e0.InterfaceC0760j
    /* renamed from: m */
    public final void mo242m(View view, int i6, int i7, int i8, int i9, int i10, int[] iArr) {
        AbstractC0186c abstractC0186c;
        int childCount = getChildCount();
        boolean z5 = false;
        int i11 = 0;
        int i12 = 0;
        for (int i13 = 0; i13 < childCount; i13++) {
            View childAt = getChildAt(i13);
            if (childAt.getVisibility() != 8) {
                C0189f c0189f = (C0189f) childAt.getLayoutParams();
                if (c0189f.m582a(i10) && (abstractC0186c = c0189f.f1274a) != null) {
                    int[] iArr2 = this.f1259n;
                    iArr2[0] = 0;
                    iArr2[1] = 0;
                    abstractC0186c.mo576l(this, childAt, i7, i8, i9, iArr2);
                    int[] iArr3 = this.f1259n;
                    i11 = i8 > 0 ? Math.max(i11, iArr3[0]) : Math.min(i11, iArr3[0]);
                    int[] iArr4 = this.f1259n;
                    i12 = i9 > 0 ? Math.max(i12, iArr4[1]) : Math.min(i12, iArr4[1]);
                    z5 = true;
                }
            }
        }
        iArr[0] = iArr[0] + i11;
        iArr[1] = iArr[1] + i12;
        if (z5) {
            m558q(1);
        }
    }

    @Override // p029e0.InterfaceC0759i
    /* renamed from: n */
    public final void mo243n(View view, int i6, int i7, int i8, int i9, int i10) {
        mo242m(view, i6, i7, i8, i9, 0, this.f1260o);
    }

    @Override // p029e0.InterfaceC0759i
    /* renamed from: o */
    public final boolean mo244o(View view, View view2, int i6, int i7) {
        int childCount = getChildCount();
        boolean z5 = false;
        for (int i8 = 0; i8 < childCount; i8++) {
            View childAt = getChildAt(i8);
            if (childAt.getVisibility() != 8) {
                C0189f c0189f = (C0189f) childAt.getLayoutParams();
                AbstractC0186c abstractC0186c = c0189f.f1274a;
                if (abstractC0186c != null) {
                    boolean mo580p = abstractC0186c.mo580p(this, childAt, view, i6, i7);
                    z5 |= mo580p;
                    c0189f.m583b(i7, mo580p);
                } else {
                    c0189f.m583b(i7, false);
                }
            }
        }
        return z5;
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onAttachedToWindow() {
        super.onAttachedToWindow();
        m563v(false);
        if (this.f1267v) {
            if (this.f1266u == null) {
                this.f1266u = new ViewTreeObserverOnPreDrawListenerC0190g();
            }
            getViewTreeObserver().addOnPreDrawListener(this.f1266u);
        }
        if (this.f1268w == null) {
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            if (getFitsSystemWindows()) {
                requestApplyInsets();
            }
        }
        this.f1262q = true;
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        m563v(false);
        if (this.f1267v && this.f1266u != null) {
            getViewTreeObserver().removeOnPreDrawListener(this.f1266u);
        }
        View view = this.f1265t;
        if (view != null) {
            onStopNestedScroll(view);
        }
        this.f1262q = false;
    }

    @Override // android.view.View
    public final void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (!this.f1269x || this.f1270y == null) {
            return;
        }
        C0772v c0772v = this.f1268w;
        int m2220e = c0772v != null ? c0772v.m2220e() : 0;
        if (m2220e > 0) {
            this.f1270y.setBounds(0, 0, getWidth(), m2220e);
            this.f1270y.draw(canvas);
        }
    }

    @Override // android.view.ViewGroup
    public final boolean onInterceptTouchEvent(MotionEvent motionEvent) {
        int actionMasked = motionEvent.getActionMasked();
        if (actionMasked == 0) {
            m563v(true);
        }
        boolean m561t = m561t(motionEvent, 0);
        if (actionMasked == 1 || actionMasked == 3) {
            m563v(true);
        }
        return m561t;
    }

    /* JADX WARN: Type inference failed for: r3v1, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    /* JADX WARN: Type inference failed for: r5v1, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    @Override // android.view.ViewGroup, android.view.View
    public final void onLayout(boolean z5, int i6, int i7, int i8, int i9) {
        AbstractC0186c abstractC0186c;
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        int layoutDirection = getLayoutDirection();
        int size = this.f1255j.size();
        for (int i10 = 0; i10 < size; i10++) {
            View view = (View) this.f1255j.get(i10);
            if (view.getVisibility() != 8 && ((abstractC0186c = ((C0189f) view.getLayoutParams()).f1274a) == null || !abstractC0186c.mo52h(this, view, layoutDirection))) {
                m559r(view, layoutDirection);
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:64:0x0190, code lost:
    
        if (r0.mo47i(r30, r19, r24, r20, r25) == false) goto L82;
     */
    /* JADX WARN: Removed duplicated region for block: B:63:0x0171  */
    /* JADX WARN: Removed duplicated region for block: B:68:0x0193  */
    /* JADX WARN: Type inference failed for: r0v8, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    /* JADX WARN: Type inference failed for: r2v1, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    @Override // android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void onMeasure(int r31, int r32) {
        /*
            Method dump skipped, instructions count: 516
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.coordinatorlayout.widget.CoordinatorLayout.onMeasure(int, int):void");
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final boolean onNestedFling(View view, float f6, float f7, boolean z5) {
        int childCount = getChildCount();
        for (int i6 = 0; i6 < childCount; i6++) {
            View childAt = getChildAt(i6);
            if (childAt.getVisibility() != 8) {
                ((C0189f) childAt.getLayoutParams()).m582a(0);
            }
        }
        return false;
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final boolean onNestedPreFling(View view, float f6, float f7) {
        AbstractC0186c abstractC0186c;
        int childCount = getChildCount();
        boolean z5 = false;
        for (int i6 = 0; i6 < childCount; i6++) {
            View childAt = getChildAt(i6);
            if (childAt.getVisibility() != 8) {
                C0189f c0189f = (C0189f) childAt.getLayoutParams();
                if (c0189f.m582a(0) && (abstractC0186c = c0189f.f1274a) != null) {
                    z5 |= abstractC0186c.mo574j(view);
                }
            }
        }
        return z5;
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final void onNestedPreScroll(View view, int i6, int i7, int[] iArr) {
        mo239j(view, i6, i7, iArr, 0);
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final void onNestedScroll(View view, int i6, int i7, int i8, int i9) {
        mo242m(view, i6, i7, i8, i9, 0, this.f1260o);
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final void onNestedScrollAccepted(View view, View view2, int i6) {
        mo237h(view, view2, i6, 0);
    }

    @Override // android.view.View
    public final void onRestoreInstanceState(Parcelable parcelable) {
        Parcelable parcelable2;
        if (!(parcelable instanceof C0191h)) {
            super.onRestoreInstanceState(parcelable);
            return;
        }
        C0191h c0191h = (C0191h) parcelable;
        super.onRestoreInstanceState(c0191h.f4731j);
        SparseArray<Parcelable> sparseArray = c0191h.f1292l;
        int childCount = getChildCount();
        for (int i6 = 0; i6 < childCount; i6++) {
            View childAt = getChildAt(i6);
            int id = childAt.getId();
            AbstractC0186c abstractC0186c = m556l(childAt).f1274a;
            if (id != -1 && abstractC0186c != null && (parcelable2 = sparseArray.get(id)) != null) {
                abstractC0186c.mo578n(childAt, parcelable2);
            }
        }
    }

    @Override // android.view.View
    public final Parcelable onSaveInstanceState() {
        Parcelable mo579o;
        C0191h c0191h = new C0191h(super.onSaveInstanceState());
        SparseArray<Parcelable> sparseArray = new SparseArray<>();
        int childCount = getChildCount();
        for (int i6 = 0; i6 < childCount; i6++) {
            View childAt = getChildAt(i6);
            int id = childAt.getId();
            AbstractC0186c abstractC0186c = ((C0189f) childAt.getLayoutParams()).f1274a;
            if (id != -1 && abstractC0186c != null && (mo579o = abstractC0186c.mo579o(childAt)) != null) {
                sparseArray.append(id, mo579o);
            }
        }
        c0191h.f1292l = sparseArray;
        return c0191h;
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final boolean onStartNestedScroll(View view, View view2, int i6) {
        return mo244o(view, view2, i6, 0);
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final void onStopNestedScroll(View view) {
        mo238i(view, 0);
    }

    /* JADX WARN: Code restructure failed: missing block: B:4:0x0012, code lost:
    
        if (r3 != false) goto L8;
     */
    /* JADX WARN: Removed duplicated region for block: B:10:0x004a  */
    /* JADX WARN: Removed duplicated region for block: B:17:0x0035  */
    /* JADX WARN: Removed duplicated region for block: B:8:0x002f  */
    @Override // android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean onTouchEvent(android.view.MotionEvent r18) {
        /*
            r17 = this;
            r0 = r17
            r1 = r18
            int r2 = r18.getActionMasked()
            android.view.View r3 = r0.f1264s
            r4 = 1
            r5 = 0
            if (r3 != 0) goto L15
            boolean r3 = r0.m561t(r1, r4)
            if (r3 == 0) goto L29
            goto L16
        L15:
            r3 = r5
        L16:
            android.view.View r6 = r0.f1264s
            android.view.ViewGroup$LayoutParams r6 = r6.getLayoutParams()
            androidx.coordinatorlayout.widget.CoordinatorLayout$f r6 = (androidx.coordinatorlayout.widget.CoordinatorLayout.C0189f) r6
            androidx.coordinatorlayout.widget.CoordinatorLayout$c r6 = r6.f1274a
            if (r6 == 0) goto L29
            android.view.View r7 = r0.f1264s
            boolean r6 = r6.mo41r(r0, r7, r1)
            goto L2a
        L29:
            r6 = r5
        L2a:
            android.view.View r7 = r0.f1264s
            r8 = 0
            if (r7 != 0) goto L35
            boolean r1 = super.onTouchEvent(r18)
            r6 = r6 | r1
            goto L48
        L35:
            if (r3 == 0) goto L48
            long r11 = android.os.SystemClock.uptimeMillis()
            r13 = 3
            r14 = 0
            r15 = 0
            r16 = 0
            r9 = r11
            android.view.MotionEvent r8 = android.view.MotionEvent.obtain(r9, r11, r13, r14, r15, r16)
            super.onTouchEvent(r8)
        L48:
            if (r8 == 0) goto L4d
            r8.recycle()
        L4d:
            if (r2 == r4) goto L52
            r1 = 3
            if (r2 != r1) goto L55
        L52:
            r0.m563v(r5)
        L55:
            return r6
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.coordinatorlayout.widget.CoordinatorLayout.onTouchEvent(android.view.MotionEvent):boolean");
    }

    /* renamed from: p */
    public final boolean m557p(View view, int i6, int i7) {
        Rect m548a = m548a();
        m553f(view, m548a);
        try {
            return m548a.contains(i6, i7);
        } finally {
            m548a.setEmpty();
            f1252G.mo2055a(m548a);
        }
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    /* JADX WARN: Type inference failed for: r0v31, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    /* JADX WARN: Type inference failed for: r3v1, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    /* renamed from: q */
    public final void m558q(int i6) {
        int i7;
        Rect rect;
        int i8;
        boolean z5;
        boolean z6;
        boolean z7;
        int width;
        int i9;
        int i10;
        int i11;
        int height;
        int i12;
        int i13;
        int i14;
        int i15;
        int i16;
        Rect rect2;
        int i17;
        C0189f c0189f;
        AbstractC0186c abstractC0186c;
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        int layoutDirection = getLayoutDirection();
        int size = this.f1255j.size();
        Rect m548a = m548a();
        Rect m548a2 = m548a();
        Rect m548a3 = m548a();
        int i18 = 0;
        int i19 = i6;
        while (i18 < size) {
            View view = (View) this.f1255j.get(i18);
            C0189f c0189f2 = (C0189f) view.getLayoutParams();
            if (i19 == 0 && view.getVisibility() == 8) {
                i8 = size;
                rect = m548a3;
                i7 = i18;
            } else {
                int i20 = 0;
                while (i20 < i18) {
                    if (c0189f2.f1285l == ((View) this.f1255j.get(i20))) {
                        C0189f c0189f3 = (C0189f) view.getLayoutParams();
                        if (c0189f3.f1284k != null) {
                            Rect m548a4 = m548a();
                            Rect m548a5 = m548a();
                            Rect m548a6 = m548a();
                            m553f(c0189f3.f1284k, m548a4);
                            m550c(view, false, m548a5);
                            int measuredWidth = view.getMeasuredWidth();
                            i16 = size;
                            int measuredHeight = view.getMeasuredHeight();
                            i17 = i18;
                            i15 = i20;
                            rect2 = m548a3;
                            c0189f = c0189f2;
                            m554g(layoutDirection, m548a4, m548a6, c0189f3, measuredWidth, measuredHeight);
                            boolean z8 = (m548a6.left == m548a5.left && m548a6.top == m548a5.top) ? false : true;
                            m549b(c0189f3, m548a6, measuredWidth, measuredHeight);
                            int i21 = m548a6.left - m548a5.left;
                            int i22 = m548a6.top - m548a5.top;
                            if (i21 != 0) {
                                WeakHashMap<View, C0769s> weakHashMap2 = C0766p.f4041a;
                                view.offsetLeftAndRight(i21);
                            }
                            if (i22 != 0) {
                                WeakHashMap<View, C0769s> weakHashMap3 = C0766p.f4041a;
                                view.offsetTopAndBottom(i22);
                            }
                            if (z8 && (abstractC0186c = c0189f3.f1274a) != null) {
                                abstractC0186c.mo571d(this, view, c0189f3.f1284k);
                            }
                            m548a4.setEmpty();
                            InterfaceC0698c<Rect> interfaceC0698c = f1252G;
                            interfaceC0698c.mo2055a(m548a4);
                            m548a5.setEmpty();
                            interfaceC0698c.mo2055a(m548a5);
                            m548a6.setEmpty();
                            interfaceC0698c.mo2055a(m548a6);
                            i20 = i15 + 1;
                            c0189f2 = c0189f;
                            size = i16;
                            i18 = i17;
                            m548a3 = rect2;
                        }
                    }
                    i15 = i20;
                    i16 = size;
                    rect2 = m548a3;
                    i17 = i18;
                    c0189f = c0189f2;
                    i20 = i15 + 1;
                    c0189f2 = c0189f;
                    size = i16;
                    i18 = i17;
                    m548a3 = rect2;
                }
                int i23 = size;
                Rect rect3 = m548a3;
                i7 = i18;
                C0189f c0189f4 = c0189f2;
                m550c(view, true, m548a2);
                if (c0189f4.f1280g != 0 && !m548a2.isEmpty()) {
                    int absoluteGravity = Gravity.getAbsoluteGravity(c0189f4.f1280g, layoutDirection);
                    int i24 = absoluteGravity & 112;
                    if (i24 == 48) {
                        m548a.top = Math.max(m548a.top, m548a2.bottom);
                    } else if (i24 == 80) {
                        m548a.bottom = Math.max(m548a.bottom, getHeight() - m548a2.top);
                    }
                    int i25 = absoluteGravity & 7;
                    if (i25 == 3) {
                        m548a.left = Math.max(m548a.left, m548a2.right);
                    } else if (i25 == 5) {
                        m548a.right = Math.max(m548a.right, getWidth() - m548a2.left);
                    }
                }
                if (c0189f4.f1281h != 0 && view.getVisibility() == 0) {
                    WeakHashMap<View, C0769s> weakHashMap4 = C0766p.f4041a;
                    if (view.isLaidOut() && view.getWidth() > 0 && view.getHeight() > 0) {
                        C0189f c0189f5 = (C0189f) view.getLayoutParams();
                        AbstractC0186c abstractC0186c2 = c0189f5.f1274a;
                        Rect m548a7 = m548a();
                        Rect m548a8 = m548a();
                        m548a8.set(view.getLeft(), view.getTop(), view.getRight(), view.getBottom());
                        if (abstractC0186c2 == null || !abstractC0186c2.mo568a(view)) {
                            m548a7.set(m548a8);
                        } else if (!m548a8.contains(m548a7)) {
                            StringBuilder m104h = C0052a.m104h("Rect should be within the child's bounds. Rect:");
                            m104h.append(m548a7.toShortString());
                            m104h.append(" | Bounds:");
                            m104h.append(m548a8.toShortString());
                            throw new IllegalArgumentException(m104h.toString());
                        }
                        m548a8.setEmpty();
                        InterfaceC0698c<Rect> interfaceC0698c2 = f1252G;
                        interfaceC0698c2.mo2055a(m548a8);
                        if (!m548a7.isEmpty()) {
                            int absoluteGravity2 = Gravity.getAbsoluteGravity(c0189f5.f1281h, layoutDirection);
                            if ((absoluteGravity2 & 48) != 48 || (i13 = (m548a7.top - ((ViewGroup.MarginLayoutParams) c0189f5).topMargin) - c0189f5.f1283j) >= (i14 = m548a.top)) {
                                z6 = false;
                            } else {
                                m565x(view, i14 - i13);
                                z6 = true;
                            }
                            if ((absoluteGravity2 & 80) == 80 && (height = ((getHeight() - m548a7.bottom) - ((ViewGroup.MarginLayoutParams) c0189f5).bottomMargin) + c0189f5.f1283j) < (i12 = m548a.bottom)) {
                                m565x(view, height - i12);
                                z6 = true;
                            }
                            if (!z6) {
                                m565x(view, 0);
                            }
                            if ((absoluteGravity2 & 3) != 3 || (i10 = (m548a7.left - ((ViewGroup.MarginLayoutParams) c0189f5).leftMargin) - c0189f5.f1282i) >= (i11 = m548a.left)) {
                                z7 = false;
                            } else {
                                m564w(view, i11 - i10);
                                z7 = true;
                            }
                            if ((absoluteGravity2 & 5) == 5 && (width = ((getWidth() - m548a7.right) - ((ViewGroup.MarginLayoutParams) c0189f5).rightMargin) + c0189f5.f1282i) < (i9 = m548a.right)) {
                                m564w(view, width - i9);
                                z7 = true;
                            }
                            if (!z7) {
                                m564w(view, 0);
                            }
                        }
                        m548a7.setEmpty();
                        interfaceC0698c2.mo2055a(m548a7);
                    }
                }
                if (i6 != 2) {
                    rect = rect3;
                    rect.set(((C0189f) view.getLayoutParams()).f1290q);
                    if (rect.equals(m548a2)) {
                        i8 = i23;
                        i19 = i6;
                    } else {
                        ((C0189f) view.getLayoutParams()).f1290q.set(m548a2);
                    }
                } else {
                    rect = rect3;
                }
                i8 = i23;
                for (int i26 = i7 + 1; i26 < i8; i26++) {
                    View view2 = (View) this.f1255j.get(i26);
                    C0189f c0189f6 = (C0189f) view2.getLayoutParams();
                    AbstractC0186c abstractC0186c3 = c0189f6.f1274a;
                    if (abstractC0186c3 != null && abstractC0186c3.mo569b(view2, view)) {
                        if (i6 == 0 && c0189f6.f1289p) {
                            c0189f6.f1289p = false;
                        } else {
                            if (i6 != 2) {
                                z5 = abstractC0186c3.mo571d(this, view2, view);
                            } else {
                                abstractC0186c3.mo572e(this, view);
                                z5 = true;
                            }
                            if (i6 == 1) {
                                c0189f6.f1289p = z5;
                            }
                        }
                    }
                }
                i19 = i6;
            }
            i18 = i7 + 1;
            size = i8;
            m548a3 = rect;
        }
        Rect rect4 = m548a3;
        m548a.setEmpty();
        InterfaceC0698c<Rect> interfaceC0698c3 = f1252G;
        interfaceC0698c3.mo2055a(m548a);
        m548a2.setEmpty();
        interfaceC0698c3.mo2055a(m548a2);
        rect4.setEmpty();
        interfaceC0698c3.mo2055a(rect4);
    }

    /* renamed from: r */
    public final void m559r(View view, int i6) {
        Rect m548a;
        Rect m548a2;
        InterfaceC0698c<Rect> interfaceC0698c;
        C0189f c0189f = (C0189f) view.getLayoutParams();
        View view2 = c0189f.f1284k;
        int i7 = 0;
        if (view2 == null && c0189f.f1279f != -1) {
            throw new IllegalStateException("An anchor may not be changed after CoordinatorLayout measurement begins before layout is complete.");
        }
        if (view2 != null) {
            m548a = m548a();
            m548a2 = m548a();
            try {
                m553f(view2, m548a);
                C0189f c0189f2 = (C0189f) view.getLayoutParams();
                int measuredWidth = view.getMeasuredWidth();
                int measuredHeight = view.getMeasuredHeight();
                m554g(i6, m548a, m548a2, c0189f2, measuredWidth, measuredHeight);
                m549b(c0189f2, m548a2, measuredWidth, measuredHeight);
                view.layout(m548a2.left, m548a2.top, m548a2.right, m548a2.bottom);
                return;
            } finally {
                m548a.setEmpty();
                interfaceC0698c = f1252G;
                interfaceC0698c.mo2055a(m548a);
                m548a2.setEmpty();
                interfaceC0698c.mo2055a(m548a2);
            }
        }
        int i8 = c0189f.f1278e;
        if (i8 < 0) {
            C0189f c0189f3 = (C0189f) view.getLayoutParams();
            m548a = m548a();
            m548a.set(getPaddingLeft() + ((ViewGroup.MarginLayoutParams) c0189f3).leftMargin, getPaddingTop() + ((ViewGroup.MarginLayoutParams) c0189f3).topMargin, (getWidth() - getPaddingRight()) - ((ViewGroup.MarginLayoutParams) c0189f3).rightMargin, (getHeight() - getPaddingBottom()) - ((ViewGroup.MarginLayoutParams) c0189f3).bottomMargin);
            if (this.f1268w != null) {
                WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
                if (getFitsSystemWindows() && !view.getFitsSystemWindows()) {
                    m548a.left = this.f1268w.m2218c() + m548a.left;
                    m548a.top = this.f1268w.m2220e() + m548a.top;
                    m548a.right -= this.f1268w.m2219d();
                    m548a.bottom -= this.f1268w.m2217b();
                }
            }
            m548a2 = m548a();
            int i9 = c0189f3.f1276c;
            if ((i9 & 7) == 0) {
                i9 |= 8388611;
            }
            if ((i9 & 112) == 0) {
                i9 |= 48;
            }
            Gravity.apply(i9, view.getMeasuredWidth(), view.getMeasuredHeight(), m548a, m548a2, i6);
            view.layout(m548a2.left, m548a2.top, m548a2.right, m548a2.bottom);
            return;
        }
        C0189f c0189f4 = (C0189f) view.getLayoutParams();
        int i10 = c0189f4.f1276c;
        if (i10 == 0) {
            i10 = 8388661;
        }
        int absoluteGravity = Gravity.getAbsoluteGravity(i10, i6);
        int i11 = absoluteGravity & 7;
        int i12 = absoluteGravity & 112;
        int width = getWidth();
        int height = getHeight();
        int measuredWidth2 = view.getMeasuredWidth();
        int measuredHeight2 = view.getMeasuredHeight();
        if (i6 == 1) {
            i8 = width - i8;
        }
        int m555k = m555k(i8) - measuredWidth2;
        if (i11 == 1) {
            m555k += measuredWidth2 / 2;
        } else if (i11 == 5) {
            m555k += measuredWidth2;
        }
        if (i12 == 16) {
            i7 = 0 + (measuredHeight2 / 2);
        } else if (i12 == 80) {
            i7 = measuredHeight2 + 0;
        }
        int max = Math.max(getPaddingLeft() + ((ViewGroup.MarginLayoutParams) c0189f4).leftMargin, Math.min(m555k, ((width - getPaddingRight()) - measuredWidth2) - ((ViewGroup.MarginLayoutParams) c0189f4).rightMargin));
        int max2 = Math.max(getPaddingTop() + ((ViewGroup.MarginLayoutParams) c0189f4).topMargin, Math.min(i7, ((height - getPaddingBottom()) - measuredHeight2) - ((ViewGroup.MarginLayoutParams) c0189f4).bottomMargin));
        view.layout(max, max2, measuredWidth2 + max, measuredHeight2 + max2);
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final boolean requestChildRectangleOnScreen(View view, Rect rect, boolean z5) {
        AbstractC0186c abstractC0186c = ((C0189f) view.getLayoutParams()).f1274a;
        if (abstractC0186c == null || !abstractC0186c.mo577m(this, view, rect, z5)) {
            return super.requestChildRectangleOnScreen(view, rect, z5);
        }
        return true;
    }

    @Override // android.view.ViewGroup, android.view.ViewParent
    public final void requestDisallowInterceptTouchEvent(boolean z5) {
        super.requestDisallowInterceptTouchEvent(z5);
        if (!z5 || this.f1261p) {
            return;
        }
        m563v(false);
        this.f1261p = true;
    }

    /* renamed from: s */
    public final void m560s(View view, int i6, int i7, int i8) {
        measureChildWithMargins(view, i6, i7, i8, 0);
    }

    @Override // android.view.View
    public void setFitsSystemWindows(boolean z5) {
        super.setFitsSystemWindows(z5);
        m566y();
    }

    @Override // android.view.ViewGroup
    public void setOnHierarchyChangeListener(ViewGroup.OnHierarchyChangeListener onHierarchyChangeListener) {
        this.f1271z = onHierarchyChangeListener;
    }

    public void setStatusBarBackground(Drawable drawable) {
        Drawable drawable2 = this.f1270y;
        if (drawable2 != drawable) {
            if (drawable2 != null) {
                drawable2.setCallback(null);
            }
            Drawable mutate = drawable != null ? drawable.mutate() : null;
            this.f1270y = mutate;
            if (mutate != null) {
                if (mutate.isStateful()) {
                    this.f1270y.setState(getDrawableState());
                }
                Drawable drawable3 = this.f1270y;
                WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
                drawable3.setLayoutDirection(getLayoutDirection());
                this.f1270y.setVisible(getVisibility() == 0, false);
                this.f1270y.setCallback(this);
            }
            WeakHashMap<View, C0769s> weakHashMap2 = C0766p.f4041a;
            postInvalidateOnAnimation();
        }
    }

    public void setStatusBarBackgroundColor(int i6) {
        setStatusBarBackground(new ColorDrawable(i6));
    }

    public void setStatusBarBackgroundResource(int i6) {
        Drawable drawable;
        if (i6 != 0) {
            Context context = getContext();
            Object obj = C1450a.f6698a;
            drawable = C1450a.c.m3527b(context, i6);
        } else {
            drawable = null;
        }
        setStatusBarBackground(drawable);
    }

    @Override // android.view.View
    public void setVisibility(int i6) {
        super.setVisibility(i6);
        boolean z5 = i6 == 0;
        Drawable drawable = this.f1270y;
        if (drawable == null || drawable.isVisible() == z5) {
            return;
        }
        this.f1270y.setVisible(z5, false);
    }

    /* JADX WARN: Type inference failed for: r4v0, types: [java.util.ArrayList, java.util.List, java.util.List<android.view.View>] */
    /* renamed from: t */
    public final boolean m561t(MotionEvent motionEvent, int i6) {
        boolean z5;
        int actionMasked = motionEvent.getActionMasked();
        ?? r42 = this.f1257l;
        r42.clear();
        boolean isChildrenDrawingOrderEnabled = isChildrenDrawingOrderEnabled();
        int childCount = getChildCount();
        for (int i7 = childCount - 1; i7 >= 0; i7--) {
            r42.add(getChildAt(isChildrenDrawingOrderEnabled ? getChildDrawingOrder(childCount, i7) : i7));
        }
        Comparator<View> comparator = f1251F;
        if (comparator != null) {
            Collections.sort(r42, comparator);
        }
        int size = r42.size();
        MotionEvent motionEvent2 = null;
        boolean z6 = false;
        boolean z7 = false;
        for (int i8 = 0; i8 < size; i8++) {
            View view = (View) r42.get(i8);
            C0189f c0189f = (C0189f) view.getLayoutParams();
            AbstractC0186c abstractC0186c = c0189f.f1274a;
            if (!(z6 || z7) || actionMasked == 0) {
                if (!z6 && abstractC0186c != null) {
                    if (i6 == 0) {
                        z6 = abstractC0186c.mo40g(this, view, motionEvent);
                    } else if (i6 == 1) {
                        z6 = abstractC0186c.mo41r(this, view, motionEvent);
                    }
                    if (z6) {
                        this.f1264s = view;
                    }
                }
                if (c0189f.f1274a == null) {
                    c0189f.f1286m = false;
                }
                boolean z8 = c0189f.f1286m;
                if (z8) {
                    z5 = true;
                } else {
                    z5 = z8 | false;
                    c0189f.f1286m = z5;
                }
                z7 = z5 && !z8;
                if (z5 && !z7) {
                    break;
                }
            } else if (abstractC0186c != null) {
                if (motionEvent2 == null) {
                    long uptimeMillis = SystemClock.uptimeMillis();
                    motionEvent2 = MotionEvent.obtain(uptimeMillis, uptimeMillis, 3, 0.0f, 0.0f, 0);
                }
                if (i6 == 0) {
                    abstractC0186c.mo40g(this, view, motionEvent2);
                } else if (i6 == 1) {
                    abstractC0186c.mo41r(this, view, motionEvent2);
                }
            }
        }
        r42.clear();
        return z6;
    }

    /* JADX WARN: Code restructure failed: missing block: B:83:0x007c, code lost:
    
        if (r5 != false) goto L56;
     */
    /* JADX WARN: Removed duplicated region for block: B:35:0x011f  */
    /* JADX WARN: Removed duplicated region for block: B:58:0x0184 A[SYNTHETIC] */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    /* JADX WARN: Type inference failed for: r0v5, types: [java.util.ArrayList, java.util.List<android.view.View>] */
    /* renamed from: u */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m562u() {
        /*
            Method dump skipped, instructions count: 494
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.coordinatorlayout.widget.CoordinatorLayout.m562u():void");
    }

    /* renamed from: v */
    public final void m563v(boolean z5) {
        int childCount = getChildCount();
        for (int i6 = 0; i6 < childCount; i6++) {
            View childAt = getChildAt(i6);
            AbstractC0186c abstractC0186c = ((C0189f) childAt.getLayoutParams()).f1274a;
            if (abstractC0186c != null) {
                long uptimeMillis = SystemClock.uptimeMillis();
                MotionEvent obtain = MotionEvent.obtain(uptimeMillis, uptimeMillis, 3, 0.0f, 0.0f, 0);
                if (z5) {
                    abstractC0186c.mo40g(this, childAt, obtain);
                } else {
                    abstractC0186c.mo41r(this, childAt, obtain);
                }
                obtain.recycle();
            }
        }
        for (int i7 = 0; i7 < childCount; i7++) {
            ((C0189f) getChildAt(i7).getLayoutParams()).f1286m = false;
        }
        this.f1264s = null;
        this.f1261p = false;
    }

    @Override // android.view.View
    public final boolean verifyDrawable(Drawable drawable) {
        return super.verifyDrawable(drawable) || drawable == this.f1270y;
    }

    /* renamed from: w */
    public final void m564w(View view, int i6) {
        C0189f c0189f = (C0189f) view.getLayoutParams();
        int i7 = c0189f.f1282i;
        if (i7 != i6) {
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            view.offsetLeftAndRight(i6 - i7);
            c0189f.f1282i = i6;
        }
    }

    /* renamed from: x */
    public final void m565x(View view, int i6) {
        C0189f c0189f = (C0189f) view.getLayoutParams();
        int i7 = c0189f.f1283j;
        if (i7 != i6) {
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            view.offsetTopAndBottom(i6 - i7);
            c0189f.f1283j = i6;
        }
    }

    /* renamed from: y */
    public final void m566y() {
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        if (!getFitsSystemWindows()) {
            C0766p.c.m2194c(this, null);
            return;
        }
        if (this.f1253A == null) {
            this.f1253A = new C0184a();
        }
        C0766p.c.m2194c(this, this.f1253A);
        setSystemUiVisibility(1280);
    }

    @Override // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return layoutParams instanceof C0189f ? new C0189f((C0189f) layoutParams) : layoutParams instanceof ViewGroup.MarginLayoutParams ? new C0189f((ViewGroup.MarginLayoutParams) layoutParams) : new C0189f(layoutParams);
    }
}
