package p041f5;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.location.LocationManager;
import android.net.NetworkInfo;
import android.net.wifi.p2p.WifiP2pConfig;
import android.net.wifi.p2p.WifiP2pDevice;
import android.net.wifi.p2p.WifiP2pDeviceList;
import android.net.wifi.p2p.WifiP2pGroup;
import android.net.wifi.p2p.WifiP2pInfo;
import android.net.wifi.p2p.WifiP2pManager;
import android.text.TextUtils;
import android.util.Log;
import androidx.activity.result.C0052a;
import java.net.InetAddress;
import java.net.InterfaceAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.List;
import p031e2.C0796l;
import p048g5.C0891c;
import p048g5.RunnableC0889a;
import p142v.C1450a;

/* renamed from: f5.b */
/* loaded from: classes.dex */
public final class C0865b {

    /* renamed from: a */
    public Context context;

    /* renamed from: b */
    public boolean f4381h;

    /* renamed from: c */
    public WifiP2pManager wifiP2pManager;

    /* renamed from: d */
    public WifiP2pManager.Channel channel;

    /* renamed from: e */
    public IntentFilter intentFilter;

    /* renamed from: h */
    public boolean isRequestingPeers;

    /* renamed from: i */
    public int connectionState;

    /* renamed from: j */
    public C0796l permissionCallback;

    /* renamed from: k */
    public SharedPreferences sharedPreferences;

    /* renamed from: m */
    public InetAddress groupOwnerAddress;

    /* renamed from: n */
    public String broadcastAddress;

    /* renamed from: o */
    public String deviceName;

    /* renamed from: p */
    public WifiP2pGroup wifiP2pGroup;

    /* renamed from: q */
    public WifiP2pInfo wifiP2pInfo;

    /* renamed from: r */
    public boolean isConnected;

    /* renamed from: f */
    public List<WifiP2pDevice> availableDevices = new ArrayList();

    /* renamed from: g */
    public List<WifiP2pDevice> connectedDevices = new ArrayList();

    /* renamed from: l */
    public List<WifiP2pListener> listeners = new ArrayList();

    /* renamed from: s */
    public P2pBroadcastReceiver p2pBroadcastReceiver = new P2pBroadcastReceiver();

    /* renamed from: t */
    public PeerListListener peerListListener = new PeerListListener();

    /* renamed from: f5.b$a */
    public class CancelConnectListener implements WifiP2pManager.ActionListener {
        @Override // android.net.wifi.p2p.WifiP2pManager.ActionListener
        public final void onFailure(int errorCode) {
            C0052a.m105i("connect onFailure reason:", errorCode, "BoxWifiP2pManager");
        }

        @Override // android.net.wifi.p2p.WifiP2pManager.ActionListener
        public final void onSuccess() {
            Log.d("BoxWifiP2pManager", "cancelConnect onSuccess");
        }
    }

    /* renamed from: f5.b$b */
    public class DisconnectListener implements WifiP2pManager.ActionListener {
        public DisconnectListener() {
        }

        @Override // android.net.wifi.p2p.WifiP2pManager.ActionListener
        public final void onFailure(int errorCode) {
            C0052a.m105i("disConnect onFailure reason:", errorCode, "BoxWifiP2pManager");
        }

        @Override // android.net.wifi.p2p.WifiP2pManager.ActionListener
        public final void onSuccess() {
            Log.d("BoxWifiP2pManager", "disConnect onSuccess");
            WifiP2pManager.this.resetConnection();
        }
    }

    /* renamed from: f5.b$c */
    public class P2pBroadcastReceiver extends BroadcastReceiver {
        public P2pBroadcastReceiver() {
        }

        /* JADX WARN: Type inference failed for: r0v19, types: [java.util.ArrayList, java.util.List<android.net.wifi.p2p.WifiP2pDevice>] */
        /* JADX WARN: Type inference failed for: r0v20, types: [java.util.ArrayList, java.util.List<android.net.wifi.p2p.WifiP2pDevice>] */
        /* JADX WARN: Type inference failed for: r9v27, types: [java.util.ArrayList, java.util.List<android.net.wifi.p2p.WifiP2pDevice>] */
        /* JADX WARN: Type inference failed for: r9v28, types: [java.util.ArrayList, java.util.List<android.net.wifi.p2p.WifiP2pDevice>] */
        /* JADX WARN: Type inference failed for: r9v29, types: [java.util.ArrayList, java.util.List<f5.d>] */
        @Override // android.content.BroadcastReceiver
        public final void onReceive(Context context, Intent intent) {
            String sb;
            String action = intent.getAction();
            Log.d("BoxWifiP2pManager", "---------------action :" + action);
            if ("android.net.wifi.p2p.STATE_CHANGED".equals(action)) {
                if (intent.getIntExtra("wifi_p2p_state", 1) == 2) {
                    boolean z5 = C0865b.this.f4391r;
                    Log.d("BoxWifiP2pManager", "打开 connected:" + z5);
                    if (C1450a.m3520a(C0865b.this.f4374a, "android.permission.ACCESS_FINE_LOCATION") != 0 || z5) {
                        return;
                    }
                    C0865b.this.m2389e();
                    return;
                }
                sb = "关闭";
            } else {
                if ("android.net.wifi.p2p.PEERS_CHANGED".equals(action)) {
                    StringBuilder m104h = C0052a.m104h(" WIFI_P2P_PEERS_CHANGED_ACTION isRequestPeersIng:");
                    m104h.append(C0865b.this.f4381h);
                    Log.d("BoxWifiP2pManager", m104h.toString());
                    Collection<WifiP2pDevice> deviceList = ((WifiP2pDeviceList) intent.getParcelableExtra("wifiP2pDeviceList")).getDeviceList();
                    C0865b c0865b = C0865b.this;
                    c0865b.f4379f.clear();
                    c0865b.f4380g.clear();
                    ArrayList arrayList = new ArrayList();
                    ArrayList arrayList2 = new ArrayList();
                    for (WifiP2pDevice wifiP2pDevice : deviceList) {
                        int i6 = wifiP2pDevice.status;
                        String str = wifiP2pDevice.deviceName;
                        if (i6 == 0) {
                            Log.d("BoxWifiP2pManager", "onPeersAvailable ------------------ deviceName:" + str + ",status:" + i6);
                            arrayList2.add(wifiP2pDevice);
                        } else {
                            arrayList.add(wifiP2pDevice);
                        }
                    }
                    c0865b.f4379f.addAll(arrayList);
                    c0865b.f4380g.addAll(arrayList2);
                    Iterator it = c0865b.f4385l.iterator();
                    while (it.hasNext()) {
                        ((InterfaceC0867d) it.next()).mo2400a(arrayList, arrayList2);
                    }
                    return;
                }
                if ("android.net.wifi.p2p.CONNECTION_STATE_CHANGE".equals(action)) {
                    C0865b.this.f4391r = ((NetworkInfo) intent.getParcelableExtra("networkInfo")).isConnected();
                    C0865b c0865b2 = C0865b.this;
                    if (!c0865b2.f4391r) {
                        c0865b2.m2397m();
                    }
                    StringBuilder m104h2 = C0052a.m104h("WIFI_P2P_CONNECTION_CHANGED_ACTION connected:");
                    m104h2.append(C0865b.this.f4391r);
                    Log.d("BoxWifiP2pManager", m104h2.toString());
                    C0865b.this.f4390q = (WifiP2pInfo) intent.getParcelableExtra("wifiP2pInfo");
                    C0865b.this.f4389p = (WifiP2pGroup) intent.getParcelableExtra("p2pGroupInfo");
                    C0865b.this.m2394j();
                    return;
                }
                if (!"android.net.wifi.p2p.THIS_DEVICE_CHANGED".equals(action)) {
                    return;
                }
                WifiP2pDevice wifiP2pDevice2 = (WifiP2pDevice) intent.getParcelableExtra("wifiP2pDevice");
                StringBuilder m104h3 = C0052a.m104h("WIFI_P2P_THIS_DEVICE_CHANGED_ACTION deviceName:");
                m104h3.append(wifiP2pDevice2.deviceName);
                sb = m104h3.toString();
            }
            Log.d("BoxWifiP2pManager", sb);
        }
    }

    /* renamed from: f5.b$d */
    public class GroupInfoListener implements WifiP2pManager.GroupInfoListener {
        public GroupInfoListener() {
        }

        @Override // android.net.wifi.p2p.WifiP2pManager.GroupInfoListener
        public final void onGroupInfoAvailable(WifiP2pGroup wifiP2pGroup) {
            Log.d("BoxWifiP2pManager", "************** requestGroupInfo wifiP2pGroup:" + wifiP2pGroup);
            C0865b c0865b = C0865b.this;
            c0865b.f4389p = wifiP2pGroup;
            c0865b.m2394j();
        }
    }

    /* renamed from: f5.b$e */
    public class ConnectionInfoListener implements WifiP2pManager.ConnectionInfoListener {
        public ConnectionInfoListener() {
        }

        @Override // android.net.wifi.p2p.WifiP2pManager.ConnectionInfoListener
        public final void onConnectionInfoAvailable(WifiP2pInfo wifiP2pInfo) {
            Log.d("BoxWifiP2pManager", "************** requestConnectionInfo wifiP2pInfo:" + wifiP2pInfo);
            C0865b c0865b = C0865b.this;
            c0865b.f4390q = wifiP2pInfo;
            c0865b.m2394j();
        }
    }

    /* renamed from: f5.b$f */
    public class DiscoverPeersListener implements WifiP2pManager.ActionListener {
        public DiscoverPeersListener() {
        }

        @Override // android.net.wifi.p2p.WifiP2pManager.ActionListener
        public final void onFailure(int errorCode) {
            C0052a.m105i("discoverPeers onFailure i:", errorCode, "BoxWifiP2pManager");
            m2385a(C0865b.this, false);
        }

        @Override // android.net.wifi.p2p.WifiP2pManager.ActionListener
        public final void onSuccess() {
            Log.d("BoxWifiP2pManager", "discoverPeers onSuccess");
            m2385a(C0865b.this, true);
        }
    }

    /* renamed from: f5.b$g */
    public class StopPeerDiscoveryListener implements WifiP2pManager.ActionListener {
        public StopPeerDiscoveryListener() {
        }

        @Override // android.net.wifi.p2p.WifiP2pManager.ActionListener
        public final void onFailure(int errorCode) {
            C0052a.m105i("stopPeerDiscovery onFailure i:", errorCode, "BoxWifiP2pManager");
            C0865b.m2385a(C0865b.this, false);
        }

        @Override // android.net.wifi.p2p.WifiP2pManager.ActionListener
        public final void onSuccess() {
            Log.d("BoxWifiP2pManager", "----------------stopPeerDiscovery onSuccess---------------");
            C0865b.m2385a(C0865b.this, false);
        }
    }

    /* renamed from: f5.b$h */
    public class PeerListListener implements WifiP2pManager.PeerListListener {
        @Override // android.net.wifi.p2p.WifiP2pManager.PeerListListener
        public final void onPeersAvailable(WifiP2pDeviceList wifiP2pDeviceList) {
        }
    }

    /* renamed from: f5.b$i */
    public class ConnectListener implements WifiP2pManager.ActionListener {
        public ConnectListener() {
        }

        @Override // android.net.wifi.p2p.WifiP2pManager.ActionListener
        public final void onFailure(int errorCode) {
            C0052a.m105i("connect onFailure reason:", errorCode, "BoxWifiP2pManager");
        }

        @Override // android.net.wifi.p2p.WifiP2pManager.ActionListener
        public final void onSuccess() {
            Log.d("BoxWifiP2pManager", "connect ------------onSuccess-----------等待接收连接改变广播");
        }
    }

    /* renamed from: f5.b$j */
    public class ResConnectListener implements WifiP2pManager.ActionListener {
        public ResConnectListener() {
        }

        @Override // android.net.wifi.p2p.WifiP2pManager.ActionListener
        public final void onFailure(int errorCode) {
            C0052a.m105i("resConnect discoverPeers onFailure i:", errorCode, "BoxWifiP2pManager");
        }

        @Override // android.net.wifi.p2p.WifiP2pManager.ActionListener
        public final void onSuccess() {
            Log.d("BoxWifiP2pManager", "resConnect discoverPeers onSuccess");
            C0865b c0865b = C0865b.this;
            c0865b.f4376c.requestConnectionInfo(c0865b.f4377d, new C0864a(c0865b));
        }
    }

    /* renamed from: f5.b$k */
    public static class k {

        /* renamed from: a */
        public static final C0865b f4401a = new C0865b();
    }

    /* JADX WARN: Type inference failed for: r2v1, types: [java.util.ArrayList, java.util.List<f5.d>] */
    /* renamed from: a */
    public static void m2385a(C0865b c0865b, boolean z5) {
        c0865b.f4381h = z5;
        StringBuilder m104h = C0052a.m104h("------notifyDiscover isRequestPeersIng:");
        m104h.append(c0865b.f4381h);
        Log.d("BoxWifiP2pManager", m104h.toString());
        Iterator it = c0865b.f4385l.iterator();
        while (it.hasNext()) {
            ((InterfaceC0867d) it.next()).mo2401b(z5);
        }
    }

    /* renamed from: b */
    public final void m2386b() {
        Log.d("BoxWifiP2pManager", "cancelConnect");
        if (C1450a.m3520a(this.f4374a, "android.permission.ACCESS_FINE_LOCATION") != 0) {
            m2393i(5);
        } else {
            this.f4376c.cancelConnect(this.f4377d, new CancelConnectListener());
        }
    }

    /* renamed from: c */
    public final void m2387c(WifiP2pDevice wifiP2pDevice) {
        if (C1450a.m3520a(this.f4374a, "android.permission.ACCESS_FINE_LOCATION") != 0) {
            m2393i(5);
            return;
        }
        this.f4384k.edit().putString("last_device_address", wifiP2pDevice.deviceAddress).commit();
        Log.d("BoxWifiP2pManager", "connect deviceAddress:" + wifiP2pDevice.deviceAddress);
        WifiP2pConfig wifiP2pConfig = new WifiP2pConfig();
        wifiP2pConfig.deviceAddress = wifiP2pDevice.deviceAddress;
        this.f4376c.connect(this.f4377d, wifiP2pConfig, new ConnectListener());
    }

    /* renamed from: d */
    public final void m2388d() {
        Log.d("BoxWifiP2pManager", "disConnect");
        if (C1450a.m3520a(this.f4374a, "android.permission.ACCESS_FINE_LOCATION") != 0) {
            m2393i(6);
            return;
        }
        this.f4391r = false;
        this.f4386m = null;
        m2392h(5, "");
        this.f4376c.removeGroup(this.f4377d, new DisconnectListener());
    }

    /* JADX WARN: Type inference failed for: r0v7, types: [java.util.ArrayList, java.util.List<f5.d>] */
    /* renamed from: e */
    public final void m2389e() {
        StringBuilder m104h = C0052a.m104h("discoverPeers :");
        m104h.append(m2391g());
        Log.d("BoxWifiP2pManager", m104h.toString());
        if (C1450a.m3520a(this.f4374a, "android.permission.ACCESS_FINE_LOCATION") != 0) {
            m2393i(2);
            return;
        }
        if (!m2391g()) {
            Iterator it = this.f4385l.iterator();
            while (it.hasNext()) {
                ((InterfaceC0867d) it.next()).mo2404e();
            }
        }
        this.f4376c.discoverPeers(this.f4377d, new DiscoverPeersListener());
    }

    /* renamed from: f */
    public final void m2390f() {
        Log.d("BoxWifiP2pManager", "主动断开,忘记上次记忆");
        this.f4384k.edit().putString("last_device_address", "").commit();
    }

    /* renamed from: g */
    public final boolean m2391g() {
        LocationManager locationManager = (LocationManager) this.f4374a.getSystemService("location");
        return locationManager.isProviderEnabled("gps") || locationManager.isProviderEnabled("network");
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<f5.d>] */
    /* renamed from: h */
    public final void m2392h(int i6, String str) {
        Iterator it = this.f4385l.iterator();
        while (it.hasNext()) {
            ((InterfaceC0867d) it.next()).mo2402c(i6, str);
        }
    }

    /* renamed from: i */
    public final void m2393i(int i6) {
        C0052a.m105i("notifyRequestPermissions requestCode:", i6, "BoxWifiP2pManager");
        C0796l c0796l = this.f4383j;
        if (c0796l != null) {
            C0891c c0891c = (C0891c) c0796l.f4161b;
            c0891c.f8462c.post(new RunnableC0889a(c0891c, i6, 0));
        }
    }

    /* JADX WARN: Type inference failed for: r1v4, types: [java.util.ArrayList, java.util.List<f5.d>] */
    /* renamed from: j */
    public final void m2394j() {
        String str;
        String str2;
        String str3;
        InetAddress broadcast;
        StringBuilder m104h = C0052a.m104h("=======******** notifyWifiP2pGroupAndWifiP2pInfo mWifiP2pGroup:");
        m104h.append(this.f4389p);
        m104h.append(",mWifiP2pInfo:");
        m104h.append(this.f4390q);
        Log.d("BoxWifiP2pManager", m104h.toString());
        WifiP2pGroup wifiP2pGroup = this.f4389p;
        if (wifiP2pGroup != null) {
            String str4 = wifiP2pGroup.getInterface();
            WifiP2pDevice owner = this.f4389p.getOwner();
            str2 = owner != null ? owner.deviceName : "";
            System.setProperty("java.net.preferIPv4Stack", "true");
            String[] strArr = new String[2];
            try {
                Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
                while (networkInterfaces.hasMoreElements()) {
                    NetworkInterface nextElement = networkInterfaces.nextElement();
                    if (!nextElement.isLoopback()) {
                        for (InterfaceAddress interfaceAddress : nextElement.getInterfaceAddresses()) {
                            String name = nextElement.getName();
                            if (TextUtils.equals(str4, name) && (broadcast = interfaceAddress.getBroadcast()) != null) {
                                String hostAddress = interfaceAddress.getAddress().getHostAddress();
                                String substring = broadcast.toString().substring(1);
                                strArr[0] = hostAddress;
                                strArr[1] = substring;
                                Log.d("BoxWifiP2pManager", "getBroadcast name:" + name + ", broadcastAddress:" + substring + ",hostAddress:" + hostAddress);
                            }
                        }
                    }
                }
            } catch (SocketException e6) {
                e6.printStackTrace();
            }
            str = strArr[0];
            str3 = strArr[1];
        } else {
            str = "";
            str2 = str;
            str3 = str2;
        }
        WifiP2pInfo wifiP2pInfo = this.f4390q;
        InetAddress inetAddress = wifiP2pInfo != null ? wifiP2pInfo.groupOwnerAddress : null;
        Log.d("BoxWifiP2pManager", "=======******** notifyWifiP2pGroupAndWifiP2pInfo broadcastAddress:" + str3 + ",deviceName:" + str2 + ",inetAddress:" + inetAddress);
        if (TextUtils.isEmpty(str3) || TextUtils.isEmpty(str2) || TextUtils.isEmpty(str2) || inetAddress == null) {
            this.f4386m = null;
            this.f4387n = "";
            this.f4388o = "";
            m2392h(5, "");
            return;
        }
        this.f4391r = true;
        m2399o();
        if (this.f4386m != inetAddress || !TextUtils.equals(this.f4387n, str3) || !TextUtils.equals(this.f4388o, str2)) {
            this.f4386m = inetAddress;
            this.f4387n = str3;
            this.f4388o = str2;
            Log.d("BoxWifiP2pManager", "notifyConnectedInfo groupOwnerAddress:" + inetAddress + ",broadcast:" + str3 + ",deviceName");
            Iterator it = this.f4385l.iterator();
            while (it.hasNext()) {
                ((InterfaceC0867d) it.next()).mo2403d(inetAddress, str, str3, str2);
            }
        }
        m2392h(4, this.f4388o);
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<f5.d>] */
    /* JADX WARN: Type inference failed for: r0v4, types: [java.util.ArrayList, java.util.List<f5.d>] */
    /* JADX WARN: Type inference failed for: r3v1, types: [java.util.ArrayList, java.util.List<f5.d>] */
    /* renamed from: k */
    public final void m2395k(InterfaceC0867d interfaceC0867d) {
        if (!this.f4385l.contains(interfaceC0867d)) {
            this.f4385l.add(interfaceC0867d);
        }
        if (this.f4385l.size() == 1) {
            this.f4374a.registerReceiver(this.f4392s, this.f4378e);
        }
    }

    /* renamed from: l */
    public final void m2396l() {
        Log.d("BoxWifiP2pManager", "requestConnectionInfo");
        if (C1450a.m3520a(this.f4374a, "android.permission.ACCESS_FINE_LOCATION") != 0) {
            m2393i(2);
            return;
        }
        if (this.f4389p == null) {
            this.f4376c.requestGroupInfo(this.f4377d, new GroupInfoListener());
        } else {
            Log.d("BoxWifiP2pManager", "=============== requestGroupInfo is connected");
            m2394j();
        }
        if (this.f4390q == null) {
            this.f4376c.requestConnectionInfo(this.f4377d, new ConnectionInfoListener());
        } else {
            Log.d("BoxWifiP2pManager", "=============== requestConnectionInfo is connected");
            m2394j();
        }
    }

    /* renamed from: m */
    public final void m2397m() {
        StringBuilder m104h = C0052a.m104h("requestPeers isRequestPeersIng:");
        m104h.append(this.f4381h);
        Log.d("BoxWifiP2pManager", m104h.toString());
        if (C1450a.m3520a(this.f4374a, "android.permission.ACCESS_FINE_LOCATION") != 0) {
            m2393i(3);
            return;
        }
        WifiP2pManager wifiP2pManager = this.f4376c;
        if (wifiP2pManager != null) {
            wifiP2pManager.requestPeers(this.f4377d, this.f4393t);
        }
    }

    /* renamed from: n */
    public final void m2398n() {
        if (C1450a.m3520a(this.f4374a, "android.permission.ACCESS_FINE_LOCATION") != 0) {
            m2393i(2);
            return;
        }
        boolean z5 = this.f4391r;
        Log.d("BoxWifiP2pManager", "resConnect connected:" + z5);
        if (z5) {
            Log.d("BoxWifiP2pManager", "设备已连接,请求连接信息");
            this.f4376c.requestConnectionInfo(this.f4377d, new C0864a(this));
        } else {
            Log.d("BoxWifiP2pManager", "设备未连接,请求列表");
            this.f4376c.discoverPeers(this.f4377d, new ResConnectListener());
        }
    }

    /* renamed from: o */
    public final void m2399o() {
        StringBuilder m104h = C0052a.m104h("stopPeerDiscovery isRequestPeersIng:");
        m104h.append(this.f4381h);
        Log.d("BoxWifiP2pManager", m104h.toString());
        if (C1450a.m3520a(this.f4374a, "android.permission.ACCESS_FINE_LOCATION") != 0) {
            m2393i(4);
            return;
        }
        WifiP2pManager wifiP2pManager = this.f4376c;
        if (wifiP2pManager != null) {
            wifiP2pManager.stopPeerDiscovery(this.f4377d, new StopPeerDiscoveryListener());
        }
    }
}
