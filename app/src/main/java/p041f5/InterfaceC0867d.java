package p041f5;

import android.net.wifi.p2p.WifiP2pDevice;
import java.net.InetAddress;
import java.util.List;

/* renamed from: f5.d */
/* loaded from: classes.dex */
public interface InterfaceC0867d {
    /* renamed from: a */
    default void mo2400a(List<WifiP2pDevice> list, List<WifiP2pDevice> list2) {
    }

    /* renamed from: b */
    default void mo2401b(boolean z5) {
    }

    /* renamed from: c */
    default void mo2402c(int i6, String str) {
    }

    /* renamed from: d */
    default void mo2403d(InetAddress inetAddress, String str, String str2, String str3) {
    }

    /* renamed from: e */
    default void mo2404e() {
    }
}
