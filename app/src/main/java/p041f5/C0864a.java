package p041f5;

import android.net.wifi.p2p.WifiP2pConfig;
import android.net.wifi.p2p.WifiP2pInfo;
import android.net.wifi.p2p.WifiP2pManager;
import android.text.TextUtils;
import android.util.Log;
import androidx.activity.result.C0052a;
import p142v.C1450a;

/* renamed from: f5.a */
/* loaded from: classes.dex */
public final /* synthetic */ class C0864a implements WifiP2pManager.ConnectionInfoListener {

    /* renamed from: a */
    public final /* synthetic */ C0865b f4373a;

    public /* synthetic */ C0864a(C0865b c0865b) {
        this.f4373a = c0865b;
    }

    @Override // android.net.wifi.p2p.WifiP2pManager.ConnectionInfoListener
    public final void onConnectionInfoAvailable(WifiP2pInfo wifiP2pInfo) {
        C0865b c0865b = this.f4373a;
        c0865b.m2394j();
        boolean z5 = c0865b.f4391r;
        StringBuilder m104h = C0052a.m104h("inRequestConnectionInfo");
        m104h.append(z5 ? "已经有设备连接了 " : "暂无设备连接");
        m104h.append(",Thread:");
        m104h.append(Thread.currentThread().getName());
        Log.d("BoxWifiP2pManager", m104h.toString());
        if (z5) {
            return;
        }
        String string = c0865b.f4384k.getString("last_device_address", "");
        Log.d("BoxWifiP2pManager", "inRequestConnectionInfo deviceAddress resConnect:" + string);
        if (TextUtils.isEmpty(string)) {
            return;
        }
        if (C1450a.m3520a(c0865b.f4374a, "android.permission.ACCESS_FINE_LOCATION") != 0) {
            c0865b.m2393i(5);
            return;
        }
        c0865b.m2392h(1, "");
        WifiP2pConfig wifiP2pConfig = new WifiP2pConfig();
        wifiP2pConfig.deviceAddress = string;
        c0865b.f4376c.connect(c0865b.f4377d, wifiP2pConfig, new C0866c(c0865b));
    }
}
