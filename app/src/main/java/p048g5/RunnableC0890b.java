package p048g5;

import android.net.wifi.p2p.WifiP2pDevice;
import com.p020ly.appupdate.download.VersionInfo;
import java.util.Iterator;
import java.util.List;
import p048g5.C0891c;
import p076k5.C1043a;
import p076k5.InterfaceC1046d;

/* renamed from: g5.b */
/* loaded from: classes.dex */
public final /* synthetic */ class RunnableC0890b implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f4497j;

    /* renamed from: k */
    public final /* synthetic */ Object f4498k;

    /* renamed from: l */
    public final /* synthetic */ Object f4499l;

    /* renamed from: m */
    public final /* synthetic */ Object f4500m;

    public /* synthetic */ RunnableC0890b(Object obj, Object obj2, Object obj3, int i6) {
        this.f4497j = i6;
        this.f4498k = obj;
        this.f4499l = obj2;
        this.f4500m = obj3;
    }

    /* JADX WARN: Type inference failed for: r0v6, types: [java.util.ArrayList, java.util.List<V>] */
    /* JADX WARN: Type inference failed for: r3v1, types: [java.util.ArrayList, java.util.List<V>] */
    @Override // java.lang.Runnable
    public final void run() {
        switch (this.f4497j) {
            case 0:
                C0891c.a aVar = (C0891c.a) this.f4498k;
                List<WifiP2pDevice> list = (List) this.f4499l;
                List<WifiP2pDevice> list2 = (List) this.f4500m;
                Iterator it = C0891c.this.f8461b.iterator();
                while (it.hasNext()) {
                    ((InterfaceC0892d) it.next()).mo1929c(list, list2);
                }
                break;
            default:
                C1043a.c cVar = (C1043a.c) this.f4498k;
                VersionInfo versionInfo = (VersionInfo) this.f4499l;
                VersionInfo versionInfo2 = (VersionInfo) this.f4500m;
                Iterator it2 = C1043a.this.f4992b.iterator();
                while (it2.hasNext()) {
                    InterfaceC1046d interfaceC1046d = (InterfaceC1046d) it2.next();
                    if (versionInfo != null) {
                        interfaceC1046d.mo1482a(versionInfo.getAppver());
                    }
                    if (versionInfo2 != null) {
                        boolean m2522b = C1043a.this.f4980e.m2522b();
                        String appver = versionInfo2.getAppver();
                        if (!m2522b) {
                            appver = versionInfo.getAppver();
                        }
                        interfaceC1046d.mo1483b(m2522b, appver, C1043a.this.f4982g);
                        C1043a.this.f4982g = false;
                    }
                }
                break;
        }
    }
}
