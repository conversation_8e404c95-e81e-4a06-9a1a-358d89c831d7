package p048g5;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.net.wifi.p2p.WifiP2pDevice;
import android.os.Build;
import android.util.Log;
import java.util.List;
import p041f5.C0865b;
import p041f5.InterfaceC0867d;
import p142v.C1450a;
import p160x4.RunnableC2052b;
import p173z4.AbstractC2114a;
import p173z4.RunnableC2119f;

/* renamed from: g5.c */
/* loaded from: classes.dex */
public final class C0891c extends AbstractC2114a<InterfaceC0892d> {

    /* renamed from: d */
    public Context f4501d;

    /* renamed from: e */
    public boolean f4502e;

    /* renamed from: f */
    public C0865b f4503f;

    /* renamed from: g */
    public WifiP2pDevice f4504g;

    /* renamed from: h */
    public a f4505h = new a();

    /* renamed from: g5.c$a */
    public class a implements InterfaceC0867d {
        public a() {
        }

        @Override // p041f5.InterfaceC0867d
        /* renamed from: a */
        public final void mo2400a(List<WifiP2pDevice> list, List<WifiP2pDevice> list2) {
            C0891c.this.f8462c.post(new RunnableC0890b(this, list, list2, 0));
        }

        @Override // p041f5.InterfaceC0867d
        /* renamed from: b */
        public final void mo2401b(boolean z5) {
            C0891c.this.f8462c.post(new RunnableC2119f(this, z5, 2));
        }

        @Override // p041f5.InterfaceC0867d
        /* renamed from: e */
        public final void mo2404e() {
            Log.d("BoxWifiP2pPresenter", "notifyOpenLocationSettings");
            C0891c.this.f8462c.post(new RunnableC2052b(this, 3));
        }
    }

    /* renamed from: g5.c$b */
    public static class b {

        /* renamed from: a */
        public static final C0891c f4507a = new C0891c();
    }

    @Override // p173z4.AbstractC2114a
    /* renamed from: b */
    public final /* bridge */ /* synthetic */ void mo2430b(InterfaceC0892d interfaceC0892d) {
    }

    /* renamed from: e */
    public final void m2431e() {
        Intent intent = new Intent();
        intent.setAction("android.settings.APPLICATION_DETAILS_SETTINGS");
        intent.setData(Uri.fromParts("package", this.f4501d.getPackageName(), null));
        intent.setFlags(268435456);
        this.f4501d.startActivity(intent);
    }

    /* renamed from: f */
    public final boolean m2432f() {
        boolean z5 = C1450a.m3520a(this.f4501d, "android.permission.ACCESS_FINE_LOCATION") == 0;
        boolean z6 = C1450a.m3520a(this.f4501d, "android.permission.ACCESS_COARSE_LOCATION") == 0;
        if (Build.VERSION.SDK_INT < 33) {
            Log.d("BoxWifiP2pPresenter", "hasFineLocationPermission hasFineLocationPermission:" + z5 + ",hasCoarseLocationPermission:" + z6);
            return z5 && z6;
        }
        boolean z7 = C1450a.m3520a(this.f4501d, "android.permission.NEARBY_WIFI_DEVICES") == 0;
        Log.d("BoxWifiP2pPresenter", "hasFineLocationPermission hasFineLocationPermission:" + z5 + ",hasCoarseLocationPermission:" + z6 + ",hasWifiDevicesPermission:" + z7);
        return z5 && z6 && z7;
    }
}
