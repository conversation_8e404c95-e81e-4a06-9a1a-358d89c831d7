package p158x2;

import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Pair;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Objects;
import p066j2.C0959a;
import p153w3.C1798e;

/* renamed from: x2.b5 */
/* loaded from: classes.dex */
public final /* synthetic */ class RunnableC1845b5 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f7585j;

    /* renamed from: k */
    public final Object f7586k;

    public /* synthetic */ RunnableC1845b5(Object obj, int i6) {
        this.f7585j = i6;
        this.f7586k = obj;
    }

    @Override // java.lang.Runnable
    public final void run() {
        Pair pair;
        C1897h3 c1897h3;
        String str;
        NetworkInfo activeNetworkInfo;
        URL url = null;
        switch (this.f7585j) {
            case 0:
                C1933l5 c1933l5 = (C1933l5) this.f7586k;
                c1933l5.mo4915h();
                if (!((C1948n4) c1933l5.f8145k).m4970q().f8234B.m4998a()) {
                    long m5050a = ((C1948n4) c1933l5.f8145k).m4970q().f8235C.m5050a();
                    ((C1948n4) c1933l5.f8145k).m4970q().f8235C.m5051b(1 + m5050a);
                    Objects.requireNonNull((C1948n4) c1933l5.f8145k);
                    if (m5050a < 5) {
                        C1948n4 c1948n4 = (C1948n4) c1933l5.f8145k;
                        c1948n4.mo4959b().mo4915h();
                        C1948n4.m4956o(c1948n4.m4976w());
                        String m4748m = c1948n4.m4960c().m4748m();
                        C2027x3 m4970q = c1948n4.m4970q();
                        m4970q.mo4915h();
                        Objects.requireNonNull(((C1948n4) m4970q.f8145k).f7930w);
                        long elapsedRealtime = SystemClock.elapsedRealtime();
                        String str2 = m4970q.f8245r;
                        if (str2 == null || elapsedRealtime >= m4970q.f8247t) {
                            m4970q.f8247t = ((C1948n4) m4970q.f8145k).f7923p.m4781n(m4748m, C2026x2.f8188b) + elapsedRealtime;
                            try {
                                C0959a.a m2501b = C0959a.m2501b(((C1948n4) m4970q.f8145k).f7917j);
                                m4970q.f8245r = "";
                                String str3 = m2501b.f4748a;
                                if (str3 != null) {
                                    m4970q.f8245r = str3;
                                }
                                m4970q.f8246s = m2501b.f4749b;
                            } catch (Exception e6) {
                                ((C1948n4) m4970q.f8145k).mo4962e().f7791w.m4842c("Unable to get advertising id", e6);
                                m4970q.f8245r = "";
                            }
                            pair = new Pair(m4970q.f8245r, Boolean.valueOf(m4970q.f8246s));
                        } else {
                            pair = new Pair(str2, Boolean.valueOf(m4970q.f8246s));
                        }
                        Boolean m4786s = c1948n4.f7923p.m4786s("google_analytics_adid_collection_enabled");
                        if (!(m4786s == null || m4786s.booleanValue()) || ((Boolean) pair.second).booleanValue() || TextUtils.isEmpty((CharSequence) pair.first)) {
                            c1897h3 = c1948n4.mo4962e().f7791w;
                            str = "ADID unavailable to retrieve Deferred Deep Link. Skipping";
                        } else {
                            C1965p5 m4976w = c1948n4.m4976w();
                            m4976w.m5153l();
                            ConnectivityManager connectivityManager = (ConnectivityManager) ((C1948n4) m4976w.f8145k).f7917j.getSystemService("connectivity");
                            if (connectivityManager != null) {
                                try {
                                    activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
                                } catch (SecurityException unused) {
                                }
                                if (activeNetworkInfo == null && activeNetworkInfo.isConnected()) {
                                    C1838a7 m4973t = c1948n4.m4973t();
                                    ((C1948n4) c1948n4.m4960c().f8145k).f7923p.m4780m();
                                    String str4 = (String) pair.first;
                                    long m5050a2 = c1948n4.m4970q().f8235C.m5050a() - 1;
                                    Objects.requireNonNull(m4973t);
                                    try {
                                        C1798e.m4554o(str4);
                                        C1798e.m4554o(m4748m);
                                        String format = String.format("https://www.googleadservices.com/pagead/conversion/app/deeplink?id_type=adid&sdk_version=%s&rdid=%s&bundleid=%s&retry=%s", String.format("v%s.%s", 42004L, Integer.valueOf(m4973t.m4714M())), str4, m4748m, Long.valueOf(m5050a2));
                                        if (m4748m.equals(((C1948n4) m4973t.f8145k).f7923p.m4777j("debug.deferred.deeplink"))) {
                                            format = format.concat("&ddl_test=1");
                                        }
                                        url = new URL(format);
                                    } catch (IllegalArgumentException | MalformedURLException e7) {
                                        ((C1948n4) m4973t.f8145k).mo4962e().f7784p.m4842c("Failed to create BOW URL for Deferred Deep Link. exception", e7.getMessage());
                                    }
                                    if (url != null) {
                                        C1965p5 m4976w2 = c1948n4.m4976w();
                                        C1891g6 c1891g6 = new C1891g6(c1948n4, 2);
                                        m4976w2.mo4915h();
                                        m4976w2.m5153l();
                                        ((C1948n4) m4976w2.f8145k).mo4959b().m4921t(new RunnableC1957o5(m4976w2, m4748m, url, c1891g6));
                                        break;
                                    }
                                } else {
                                    c1897h3 = c1948n4.mo4962e().f7787s;
                                    str = "Network is not available for Deferred Deep Link request. Skipping";
                                }
                            }
                            activeNetworkInfo = null;
                            if (activeNetworkInfo == null) {
                            }
                            c1897h3 = c1948n4.mo4962e().f7787s;
                            str = "Network is not available for Deferred Deep Link request. Skipping";
                        }
                        c1897h3.m4841b(str);
                        break;
                    } else {
                        ((C1948n4) c1933l5.f8145k).mo4962e().f7787s.m4841b("Permanently failed to retrieve Deferred Deep Link. Reached maximum retries.");
                        ((C1948n4) c1933l5.f8145k).m4970q().f8234B.m4999b(true);
                        break;
                    }
                } else {
                    ((C1948n4) c1933l5.f8145k).mo4962e().f7791w.m4841b("Deferred Deep Link already retrieved. Not fetching again.");
                    break;
                }
                break;
            case 1:
                ((C1997t5) this.f7586k).f8067t = null;
                break;
            default:
                C1855c6 c1855c6 = ((ServiceConnectionC1846b6) this.f7586k).f7589c;
                c1855c6.f7620n = null;
                c1855c6.m4758t();
                break;
        }
    }
}
