package p158x2;

import android.content.SharedPreferences;
import android.util.Pair;
import java.util.Objects;
import org.checkerframework.checker.nullness.qual.EnsuresNonNull;
import p153w3.C1798e;

/* renamed from: x2.x3 */
/* loaded from: classes.dex */
public final class C2027x3 extends AbstractC2028x4 {

    /* renamed from: H */
    public static final Pair<String, Long> f8232H = new Pair<>("", 0L);

    /* renamed from: A */
    public final C1979r3 f8233A;

    /* renamed from: B */
    public final C1979r3 f8234B;

    /* renamed from: C */
    public final C1995t3 f8235C;

    /* renamed from: D */
    public final C2019w3 f8236D;

    /* renamed from: E */
    public final C2019w3 f8237E;

    /* renamed from: F */
    public final C1995t3 f8238F;

    /* renamed from: G */
    public final C1987s3 f8239G;

    /* renamed from: m */
    public SharedPreferences f8240m;

    /* renamed from: n */
    public C2003u3 f8241n;

    /* renamed from: o */
    public final C1995t3 f8242o;

    /* renamed from: p */
    public final C1995t3 f8243p;

    /* renamed from: q */
    public final C2019w3 f8244q;

    /* renamed from: r */
    public String f8245r;

    /* renamed from: s */
    public boolean f8246s;

    /* renamed from: t */
    public long f8247t;

    /* renamed from: u */
    public final C1995t3 f8248u;

    /* renamed from: v */
    public final C1979r3 f8249v;

    /* renamed from: w */
    public final C2019w3 f8250w;

    /* renamed from: x */
    public final C1979r3 f8251x;

    /* renamed from: y */
    public final C1995t3 f8252y;

    /* renamed from: z */
    public boolean f8253z;

    public C2027x3(C1948n4 c1948n4) {
        super(c1948n4);
        this.f8248u = new C1995t3(this, "session_timeout", 1800000L);
        this.f8249v = new C1979r3(this, "start_new_session", true);
        this.f8252y = new C1995t3(this, "last_pause_time", 0L);
        this.f8250w = new C2019w3(this, "non_personalized_ads");
        this.f8251x = new C1979r3(this, "allow_remote_dynamite", false);
        this.f8242o = new C1995t3(this, "first_open_time", 0L);
        this.f8243p = new C1995t3(this, "app_install_time", 0L);
        this.f8244q = new C2019w3(this, "app_instance_id");
        this.f8233A = new C1979r3(this, "app_backgrounded", false);
        this.f8234B = new C1979r3(this, "deep_link_retrieval_complete", false);
        this.f8235C = new C1995t3(this, "deep_link_retrieval_attempts", 0L);
        this.f8236D = new C2019w3(this, "firebase_feature_rollouts");
        this.f8237E = new C2019w3(this, "deferred_attribution_cache");
        this.f8238F = new C1995t3(this, "deferred_attribution_cache_timestamp", 0L);
        this.f8239G = new C1987s3(this);
    }

    @Override // p158x2.AbstractC2028x4
    /* renamed from: i */
    public final boolean mo4731i() {
        return true;
    }

    @Override // p158x2.AbstractC2028x4
    @EnsuresNonNull.List({@EnsuresNonNull({"this.preferences"}), @EnsuresNonNull({"this.monitoringSample"})})
    /* renamed from: j */
    public final void mo4733j() {
        SharedPreferences sharedPreferences = ((C1948n4) this.f8145k).f7917j.getSharedPreferences("com.google.android.gms.measurement.prefs", 0);
        this.f8240m = sharedPreferences;
        boolean z5 = sharedPreferences.getBoolean("has_been_opened", false);
        this.f8253z = z5;
        if (!z5) {
            SharedPreferences.Editor edit = this.f8240m.edit();
            edit.putBoolean("has_been_opened", true);
            edit.apply();
        }
        Objects.requireNonNull((C1948n4) this.f8145k);
        this.f8241n = new C2003u3(this, Math.max(0L, C2026x2.f8190c.m5101a(null).longValue()));
    }

    /* renamed from: o */
    public final SharedPreferences m5145o() {
        mo4915h();
        m5153l();
        C1798e.m4560r(this.f8240m);
        return this.f8240m;
    }

    /* renamed from: p */
    public final void m5146p(Boolean bool) {
        mo4915h();
        SharedPreferences.Editor edit = m5145o().edit();
        if (bool != null) {
            edit.putBoolean("measurement_enabled", bool.booleanValue());
        } else {
            edit.remove("measurement_enabled");
        }
        edit.apply();
    }

    /* renamed from: q */
    public final Boolean m5147q() {
        mo4915h();
        if (m5145o().contains("measurement_enabled")) {
            return Boolean.valueOf(m5145o().getBoolean("measurement_enabled", true));
        }
        return null;
    }

    /* renamed from: r */
    public final boolean m5148r(int i6) {
        return i6 <= m5145o().getInt("consent_source", 100);
    }

    /* renamed from: s */
    public final C1875f m5149s() {
        mo4915h();
        return C1875f.m4801a(m5145o().getString("consent_settings", "G1"));
    }

    /* renamed from: t */
    public final void m5150t(boolean z5) {
        mo4915h();
        ((C1948n4) this.f8145k).mo4962e().f7792x.m4842c("App measurement setting deferred collection", Boolean.valueOf(z5));
        SharedPreferences.Editor edit = m5145o().edit();
        edit.putBoolean("deferred_analytics_collection", z5);
        edit.apply();
    }

    /* renamed from: u */
    public final boolean m5151u(long j6) {
        return j6 - this.f8248u.m5050a() > this.f8252y.m5050a();
    }
}
