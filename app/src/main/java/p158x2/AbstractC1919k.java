package p158x2;

import android.os.Handler;
import java.util.Objects;
import p152w2.HandlerC1579i0;
import p153w3.C1798e;

/* renamed from: x2.k */
/* loaded from: classes.dex */
public abstract class AbstractC1919k {

    /* renamed from: d */
    public static volatile HandlerC1579i0 f7807d;

    /* renamed from: a */
    public final InterfaceC2036y4 f7808a;

    /* renamed from: b */
    public final RunnableC1911j f7809b;

    /* renamed from: c */
    public volatile long f7810c;

    public AbstractC1919k(InterfaceC2036y4 interfaceC2036y4) {
        Objects.requireNonNull(interfaceC2036y4, "null reference");
        this.f7808a = interfaceC2036y4;
        this.f7809b = new RunnableC1911j(this, interfaceC2036y4, 0);
    }

    /* renamed from: a */
    public abstract void mo4898a();

    /* renamed from: b */
    public final void m4899b(long j6) {
        m4900c();
        if (j6 >= 0) {
            Objects.requireNonNull((C1798e) this.f7808a.mo4963f());
            this.f7810c = System.currentTimeMillis();
            if (m4901d().postDelayed(this.f7809b, j6)) {
                return;
            }
            this.f7808a.mo4962e().f7784p.m4842c("Failed to schedule delayed post. time", Long.valueOf(j6));
        }
    }

    /* renamed from: c */
    public final void m4900c() {
        this.f7810c = 0L;
        m4901d().removeCallbacks(this.f7809b);
    }

    /* renamed from: d */
    public final Handler m4901d() {
        HandlerC1579i0 handlerC1579i0;
        if (f7807d != null) {
            return f7807d;
        }
        synchronized (AbstractC1919k.class) {
            if (f7807d == null) {
                f7807d = new HandlerC1579i0(this.f7808a.mo4961d().getMainLooper());
            }
            handlerC1579i0 = f7807d;
        }
        return handlerC1579i0;
    }
}
