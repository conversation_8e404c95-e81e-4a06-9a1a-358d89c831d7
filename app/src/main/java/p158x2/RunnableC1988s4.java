package p158x2;

import p153w3.C1798e;

/* renamed from: x2.s4 */
/* loaded from: classes.dex */
public final class RunnableC1988s4 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f8037j;

    /* renamed from: k */
    public final /* synthetic */ C1847b7 f8038k;

    /* renamed from: l */
    public final /* synthetic */ BinderC2012v4 f8039l;

    public /* synthetic */ RunnableC1988s4(BinderC2012v4 binderC2012v4, C1847b7 c1847b7, int i6) {
        this.f8037j = i6;
        this.f8039l = binderC2012v4;
        this.f8038k = c1847b7;
    }

    @Override // java.lang.Runnable
    public final void run() {
        switch (this.f8037j) {
            case 0:
                this.f8039l.f8129a.m5086j();
                this.f8039l.f8129a.m5087k(this.f8038k);
                break;
            case 1:
                this.f8039l.f8129a.m5086j();
                C1998t6 c1998t6 = this.f8039l.f8129a;
                C1847b7 c1847b7 = this.f8038k;
                c1998t6.mo4959b().mo4915h();
                c1998t6.m5074L();
                C1798e.m4554o(c1847b7.f7595j);
                C1875f m4801a = C1875f.m4801a(c1847b7.f7594E);
                C1875f m5076N = c1998t6.m5076N(c1847b7.f7595j);
                c1998t6.mo4962e().f7792x.m4843d("Setting consent, package, consent", c1847b7.f7595j, m4801a);
                c1998t6.m5075M(c1847b7.f7595j, m4801a);
                if (m4801a.m4808e(m5076N)) {
                    c1998t6.m5087k(c1847b7);
                    break;
                }
                break;
            default:
                this.f8039l.f8129a.m5086j();
                this.f8039l.f8129a.m5090n(this.f8038k);
                break;
        }
    }
}
