package p158x2;

import android.content.Context;
import java.util.Objects;
import org.checkerframework.dataflow.qual.Pure;
import p110q.C1251d;
import p113q2.InterfaceC1273a;

/* renamed from: x2.w4 */
/* loaded from: classes.dex */
public class C2020w4 implements InterfaceC2036y4, InterfaceC2046z6 {

    /* renamed from: j */
    public final /* synthetic */ int f8144j;

    /* renamed from: k */
    public final Object f8145k;

    public /* synthetic */ C2020w4(Object obj, int i6) {
        this.f8144j = i6;
        this.f8145k = obj;
    }

    @Override // p158x2.InterfaceC2036y4
    @Pure
    /* renamed from: a */
    public final C1251d mo4958a() {
        throw null;
    }

    @Override // p158x2.InterfaceC2036y4
    @Pure
    /* renamed from: b */
    public final C1932l4 mo4959b() {
        throw null;
    }

    @Override // p158x2.InterfaceC2036y4
    @Pure
    /* renamed from: d */
    public final Context mo4961d() {
        throw null;
    }

    @Override // p158x2.InterfaceC2036y4
    @Pure
    /* renamed from: e */
    public final C1915j3 mo4962e() {
        throw null;
    }

    @Override // p158x2.InterfaceC2036y4
    @Pure
    /* renamed from: f */
    public final InterfaceC1273a mo4963f() {
        throw null;
    }

    /* renamed from: g */
    public void mo4914g() {
        ((C1948n4) this.f8145k).mo4959b().mo4914g();
    }

    /* renamed from: h */
    public void mo4915h() {
        ((C1948n4) this.f8145k).mo4959b().mo4915h();
    }

    public C2020w4(C1948n4 c1948n4) {
        this.f8144j = 0;
        Objects.requireNonNull(c1948n4, "null reference");
        this.f8145k = c1948n4;
    }
}
