package p158x2;

/* renamed from: x2.p6 */
/* loaded from: classes.dex */
public abstract class AbstractC1966p6 extends C1958o6 {

    /* renamed from: m */
    public boolean f7964m;

    public AbstractC1966p6(C1998t6 c1998t6) {
        super(c1998t6);
        this.f7954l.f8097y++;
    }

    /* renamed from: i */
    public final void m4994i() {
        if (!this.f7964m) {
            throw new IllegalStateException("Not initialized");
        }
    }

    /* renamed from: j */
    public abstract void mo4768j();

    /* renamed from: k */
    public final void m4995k() {
        if (this.f7964m) {
            throw new IllegalStateException("Can't initialize twice");
        }
        mo4768j();
        this.f7954l.f8098z++;
        this.f7964m = true;
    }
}
