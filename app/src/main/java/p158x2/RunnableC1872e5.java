package p158x2;

import java.util.concurrent.atomic.AtomicReference;

/* renamed from: x2.e5 */
/* loaded from: classes.dex */
public final class RunnableC1872e5 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ AtomicReference f7662j;

    /* renamed from: k */
    public final /* synthetic */ String f7663k;

    /* renamed from: l */
    public final /* synthetic */ String f7664l;

    /* renamed from: m */
    public final /* synthetic */ C1933l5 f7665m;

    public RunnableC1872e5(C1933l5 c1933l5, AtomicReference atomicReference, String str, String str2) {
        this.f7665m = c1933l5;
        this.f7662j = atomicReference;
        this.f7663k = str;
        this.f7664l = str2;
    }

    @Override // java.lang.Runnable
    public final void run() {
        C1855c6 m4979z = ((C1948n4) this.f7665m.f8145k).m4979z();
        AtomicReference atomicReference = this.f7662j;
        String str = this.f7663k;
        String str2 = this.f7664l;
        m4979z.mo4915h();
        m4979z.m5102i();
        m4979z.m4757s(new RunnableC2037y5(m4979z, atomicReference, str, str2, m4979z.m4759u(false)));
    }
}
