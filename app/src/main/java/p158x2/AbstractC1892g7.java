package p158x2;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;
import p090n.C1094g;
import p152w2.C1481a1;
import p152w2.C1747w0;

/* renamed from: x2.g7 */
/* loaded from: classes.dex */
public abstract class AbstractC1892g7 {

    /* renamed from: a */
    public final String f7723a;

    /* renamed from: b */
    public final int f7724b;

    /* renamed from: c */
    public Boolean f7725c;

    /* renamed from: d */
    public Boolean f7726d;

    /* renamed from: e */
    public Long f7727e;

    /* renamed from: f */
    public Long f7728f;

    public AbstractC1892g7(String str, int i6) {
        this.f7723a = str;
        this.f7724b = i6;
    }

    /* renamed from: d */
    public static Boolean m4835d(Boolean bool, boolean z5) {
        if (bool == null) {
            return null;
        }
        return Boolean.valueOf(bool.booleanValue() != z5);
    }

    /* renamed from: e */
    public static Boolean m4836e(String str, C1481a1 c1481a1, C1915j3 c1915j3) {
        List<String> m3611y;
        boolean startsWith;
        Objects.requireNonNull(c1481a1, "null reference");
        if (str == null || !c1481a1.m3605s() || c1481a1.m3606t() == 1) {
            return null;
        }
        if (c1481a1.m3606t() == 7) {
            if (c1481a1.m3612z() == 0) {
                return null;
            }
        } else if (!c1481a1.m3607u()) {
            return null;
        }
        int m3606t = c1481a1.m3606t();
        boolean m3610x = c1481a1.m3610x();
        String m3608v = (m3610x || m3606t == 2 || m3606t == 7) ? c1481a1.m3608v() : c1481a1.m3608v().toUpperCase(Locale.ENGLISH);
        if (c1481a1.m3612z() == 0) {
            m3611y = null;
        } else {
            m3611y = c1481a1.m3611y();
            if (!m3610x) {
                ArrayList arrayList = new ArrayList(m3611y.size());
                Iterator<String> it = m3611y.iterator();
                while (it.hasNext()) {
                    arrayList.add(it.next().toUpperCase(Locale.ENGLISH));
                }
                m3611y = Collections.unmodifiableList(arrayList);
            }
        }
        String str2 = m3606t == 2 ? m3608v : null;
        if (m3606t == 7) {
            if (m3611y == null || m3611y.size() == 0) {
                return null;
            }
        } else if (m3608v == null) {
            return null;
        }
        if (!m3610x && m3606t != 2) {
            str = str.toUpperCase(Locale.ENGLISH);
        }
        switch (C1094g.m2840d(m3606t)) {
            case 1:
                if (str2 == null) {
                    return null;
                }
                try {
                    return Boolean.valueOf(Pattern.compile(str2, true != m3610x ? 66 : 0).matcher(str).matches());
                } catch (PatternSyntaxException unused) {
                    if (c1915j3 == null) {
                        return null;
                    }
                    c1915j3.f7787s.m4842c("Invalid regular expression in REGEXP audience filter. expression", str2);
                    return null;
                }
            case 2:
                startsWith = str.startsWith(m3608v);
                break;
            case 3:
                startsWith = str.endsWith(m3608v);
                break;
            case 4:
                startsWith = str.contains(m3608v);
                break;
            case 5:
                startsWith = str.equals(m3608v);
                break;
            case 6:
                if (m3611y != null) {
                    startsWith = m3611y.contains(str);
                    break;
                } else {
                    return null;
                }
            default:
                return null;
        }
        return Boolean.valueOf(startsWith);
    }

    /* renamed from: f */
    public static Boolean m4837f(long j6, C1747w0 c1747w0) {
        try {
            return m4839h(new BigDecimal(j6), c1747w0, 0.0d);
        } catch (NumberFormatException unused) {
            return null;
        }
    }

    /* renamed from: g */
    public static Boolean m4838g(String str, C1747w0 c1747w0) {
        if (!C2014v6.m5108A(str)) {
            return null;
        }
        try {
            return m4839h(new BigDecimal(str), c1747w0, 0.0d);
        } catch (NumberFormatException unused) {
            return null;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:36:0x00a3, code lost:
    
        if (r8.compareTo(r5) <= 0) goto L66;
     */
    /* renamed from: h */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static java.lang.Boolean m4839h(java.math.BigDecimal r8, p152w2.C1747w0 r9, double r10) {
        /*
            Method dump skipped, instructions count: 270
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.AbstractC1892g7.m4839h(java.math.BigDecimal, w2.w0, double):java.lang.Boolean");
    }

    /* renamed from: a */
    public abstract int mo4810a();

    /* renamed from: b */
    public abstract boolean mo4811b();

    /* renamed from: c */
    public abstract boolean mo4812c();
}
