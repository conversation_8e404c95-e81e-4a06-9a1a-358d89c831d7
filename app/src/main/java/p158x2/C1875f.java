package p158x2;

/* renamed from: x2.f */
/* loaded from: classes.dex */
public final class C1875f {

    /* renamed from: c */
    public static final C1875f f7678c = new C1875f(null, null);

    /* renamed from: a */
    public final Boolean f7679a;

    /* renamed from: b */
    public final Boolean f7680b;

    public C1875f(Boolean bool, Boolean bool2) {
        this.f7679a = bool;
        this.f7680b = bool2;
    }

    /* JADX WARN: Removed duplicated region for block: B:11:0x0026  */
    /* renamed from: a */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static p158x2.C1875f m4801a(java.lang.String r7) {
        /*
            r0 = 0
            if (r7 == 0) goto L38
            int r1 = r7.length()
            r2 = 49
            r3 = 48
            r4 = 3
            if (r1 < r4) goto L1e
            r1 = 2
            char r1 = r7.charAt(r1)
            if (r1 == r3) goto L1b
            if (r1 == r2) goto L18
            goto L1e
        L18:
            java.lang.Boolean r1 = java.lang.Boolean.TRUE
            goto L1f
        L1b:
            java.lang.Boolean r1 = java.lang.Boolean.FALSE
            goto L1f
        L1e:
            r1 = r0
        L1f:
            int r5 = r7.length()
            r6 = 4
            if (r5 < r6) goto L35
            char r7 = r7.charAt(r4)
            if (r7 == r3) goto L32
            if (r7 == r2) goto L2f
            goto L35
        L2f:
            java.lang.Boolean r7 = java.lang.Boolean.TRUE
            goto L34
        L32:
            java.lang.Boolean r7 = java.lang.Boolean.FALSE
        L34:
            r0 = r7
        L35:
            r7 = r0
            r0 = r1
            goto L39
        L38:
            r7 = r0
        L39:
            x2.f r1 = new x2.f
            r1.<init>(r0, r7)
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1875f.m4801a(java.lang.String):x2.f");
    }

    /* renamed from: f */
    public static Boolean m4802f(Boolean bool, Boolean bool2) {
        if (bool == null) {
            return bool2;
        }
        if (bool2 == null) {
            return bool;
        }
        boolean z5 = false;
        if (bool.booleanValue() && bool2.booleanValue()) {
            z5 = true;
        }
        return Boolean.valueOf(z5);
    }

    /* renamed from: h */
    public static final int m4803h(Boolean bool) {
        if (bool == null) {
            return 0;
        }
        return bool.booleanValue() ? 1 : 2;
    }

    /* renamed from: i */
    public static Boolean m4804i(String str) {
        if (str == null) {
            return null;
        }
        if (str.equals("granted")) {
            return Boolean.TRUE;
        }
        if (str.equals("denied")) {
            return Boolean.FALSE;
        }
        return null;
    }

    /* renamed from: b */
    public final String m4805b() {
        StringBuilder sb = new StringBuilder("G1");
        Boolean bool = this.f7679a;
        char c = '1';
        sb.append(bool == null ? '-' : bool.booleanValue() ? '1' : '0');
        Boolean bool2 = this.f7680b;
        if (bool2 == null) {
            c = '-';
        } else if (!bool2.booleanValue()) {
            c = '0';
        }
        sb.append(c);
        return sb.toString();
    }

    /* renamed from: c */
    public final boolean m4806c() {
        Boolean bool = this.f7679a;
        return bool == null || bool.booleanValue();
    }

    /* renamed from: d */
    public final boolean m4807d() {
        Boolean bool = this.f7680b;
        return bool == null || bool.booleanValue();
    }

    /* renamed from: e */
    public final boolean m4808e(C1875f c1875f) {
        Boolean bool = this.f7679a;
        Boolean bool2 = Boolean.FALSE;
        if (bool != bool2 || c1875f.f7679a == bool2) {
            return this.f7680b == bool2 && c1875f.f7680b != bool2;
        }
        return true;
    }

    public final boolean equals(Object obj) {
        if (!(obj instanceof C1875f)) {
            return false;
        }
        C1875f c1875f = (C1875f) obj;
        return m4803h(this.f7679a) == m4803h(c1875f.f7679a) && m4803h(this.f7680b) == m4803h(c1875f.f7680b);
    }

    /* renamed from: g */
    public final C1875f m4809g(C1875f c1875f) {
        return new C1875f(m4802f(this.f7679a, c1875f.f7679a), m4802f(this.f7680b, c1875f.f7680b));
    }

    public final int hashCode() {
        return m4803h(this.f7680b) + ((m4803h(this.f7679a) + 527) * 31);
    }

    public final String toString() {
        StringBuilder sb = new StringBuilder("ConsentSettings: ");
        sb.append("adStorage=");
        Boolean bool = this.f7679a;
        if (bool == null) {
            sb.append("uninitialized");
        } else {
            sb.append(true != bool.booleanValue() ? "denied" : "granted");
        }
        sb.append(", analyticsStorage=");
        Boolean bool2 = this.f7680b;
        if (bool2 == null) {
            sb.append("uninitialized");
        } else {
            sb.append(true == bool2.booleanValue() ? "granted" : "denied");
        }
        return sb.toString();
    }
}
