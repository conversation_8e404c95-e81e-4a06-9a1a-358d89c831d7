package p158x2;

import android.os.Parcel;
import android.os.Parcelable;
import java.util.Objects;
import p090n.C1094g;
import p099o2.AbstractC1146a;

/* renamed from: x2.q */
/* loaded from: classes.dex */
public final class C1967q extends AbstractC1146a {
    public static final Parcelable.Creator<C1967q> CREATOR = new C1975r();

    /* renamed from: j */
    public final String f7965j;

    /* renamed from: k */
    public final C1951o f7966k;

    /* renamed from: l */
    public final String f7967l;

    /* renamed from: m */
    public final long f7968m;

    public C1967q(String str, C1951o c1951o, String str2, long j6) {
        this.f7965j = str;
        this.f7966k = c1951o;
        this.f7967l = str2;
        this.f7968m = j6;
    }

    public C1967q(C1967q c1967q, long j6) {
        Objects.requireNonNull(c1967q, "null reference");
        this.f7965j = c1967q.f7965j;
        this.f7966k = c1967q.f7966k;
        this.f7967l = c1967q.f7967l;
        this.f7968m = j6;
    }

    public final String toString() {
        String str = this.f7967l;
        String str2 = this.f7965j;
        String valueOf = String.valueOf(this.f7966k);
        int length = String.valueOf(str).length();
        StringBuilder sb = new StringBuilder(length + 21 + String.valueOf(str2).length() + valueOf.length());
        sb.append("origin=");
        sb.append(str);
        sb.append(",name=");
        sb.append(str2);
        return C1094g.m2839c(sb, ",params=", valueOf);
    }

    @Override // android.os.Parcelable
    public final void writeToParcel(Parcel parcel, int i6) {
        C1975r.m4997a(this, parcel, i6);
    }
}
