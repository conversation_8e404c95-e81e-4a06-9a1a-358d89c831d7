package p158x2;

import android.content.SharedPreferences;
import p153w3.C1798e;

/* renamed from: x2.t3 */
/* loaded from: classes.dex */
public final class C1995t3 {

    /* renamed from: a */
    public final String f8054a;

    /* renamed from: b */
    public final long f8055b;

    /* renamed from: c */
    public boolean f8056c;

    /* renamed from: d */
    public long f8057d;

    /* renamed from: e */
    public final /* synthetic */ C2027x3 f8058e;

    public C1995t3(C2027x3 c2027x3, String str, long j6) {
        this.f8058e = c2027x3;
        C1798e.m4554o(str);
        this.f8054a = str;
        this.f8055b = j6;
    }

    /* renamed from: a */
    public final long m5050a() {
        if (!this.f8056c) {
            this.f8056c = true;
            this.f8057d = this.f8058e.m5145o().getLong(this.f8054a, this.f8055b);
        }
        return this.f8057d;
    }

    /* renamed from: b */
    public final void m5051b(long j6) {
        SharedPreferences.Editor edit = this.f8058e.m5145o().edit();
        edit.putLong(this.f8054a, j6);
        edit.apply();
        this.f8057d = j6;
    }
}
