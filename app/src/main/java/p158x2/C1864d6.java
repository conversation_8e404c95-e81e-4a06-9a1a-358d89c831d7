package p158x2;

import android.os.SystemClock;
import android.util.Pair;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.util.Locale;
import java.util.Objects;
import p066j2.C0959a;

/* renamed from: x2.d6 */
/* loaded from: classes.dex */
public final class C1864d6 extends AbstractC1966p6 {

    /* renamed from: n */
    public String f7643n;

    /* renamed from: o */
    public boolean f7644o;

    /* renamed from: p */
    public long f7645p;

    /* renamed from: q */
    public final C1995t3 f7646q;

    /* renamed from: r */
    public final C1995t3 f7647r;

    /* renamed from: s */
    public final C1995t3 f7648s;

    /* renamed from: t */
    public final C1995t3 f7649t;

    /* renamed from: u */
    public final C1995t3 f7650u;

    public C1864d6(C1998t6 c1998t6) {
        super(c1998t6);
        C2027x3 m4970q = ((C1948n4) this.f8145k).m4970q();
        Objects.requireNonNull(m4970q);
        this.f7646q = new C1995t3(m4970q, "last_delete_stale", 0L);
        C2027x3 m4970q2 = ((C1948n4) this.f8145k).m4970q();
        Objects.requireNonNull(m4970q2);
        this.f7647r = new C1995t3(m4970q2, "backoff", 0L);
        C2027x3 m4970q3 = ((C1948n4) this.f8145k).m4970q();
        Objects.requireNonNull(m4970q3);
        this.f7648s = new C1995t3(m4970q3, "last_upload", 0L);
        C2027x3 m4970q4 = ((C1948n4) this.f8145k).m4970q();
        Objects.requireNonNull(m4970q4);
        this.f7649t = new C1995t3(m4970q4, "last_upload_attempt", 0L);
        C2027x3 m4970q5 = ((C1948n4) this.f8145k).m4970q();
        Objects.requireNonNull(m4970q5);
        this.f7650u = new C1995t3(m4970q5, "midnight_offset", 0L);
    }

    @Override // p158x2.AbstractC1966p6
    /* renamed from: j */
    public final void mo4768j() {
    }

    /* renamed from: l */
    public final Pair<String, Boolean> m4769l(String str, C1875f c1875f) {
        return c1875f.m4806c() ? m4770m(str) : new Pair<>("", Boolean.FALSE);
    }

    @Deprecated
    /* renamed from: m */
    public final Pair<String, Boolean> m4770m(String str) {
        mo4915h();
        Objects.requireNonNull(((C1948n4) this.f8145k).f7930w);
        long elapsedRealtime = SystemClock.elapsedRealtime();
        String str2 = this.f7643n;
        if (str2 != null && elapsedRealtime < this.f7645p) {
            return new Pair<>(str2, Boolean.valueOf(this.f7644o));
        }
        this.f7645p = ((C1948n4) this.f8145k).f7923p.m4781n(str, C2026x2.f8188b) + elapsedRealtime;
        try {
            C0959a.a m2501b = C0959a.m2501b(((C1948n4) this.f8145k).f7917j);
            this.f7643n = "";
            String str3 = m2501b.f4748a;
            if (str3 != null) {
                this.f7643n = str3;
            }
            this.f7644o = m2501b.f4749b;
        } catch (Exception e6) {
            ((C1948n4) this.f8145k).mo4962e().f7791w.m4842c("Unable to get advertising id", e6);
            this.f7643n = "";
        }
        return new Pair<>(this.f7643n, Boolean.valueOf(this.f7644o));
    }

    @Deprecated
    /* renamed from: n */
    public final String m4771n(String str) {
        mo4915h();
        String str2 = (String) m4770m(str).first;
        MessageDigest m4697B = C1838a7.m4697B();
        if (m4697B == null) {
            return null;
        }
        return String.format(Locale.US, "%032X", new BigInteger(1, m4697B.digest(str2.getBytes())));
    }
}
