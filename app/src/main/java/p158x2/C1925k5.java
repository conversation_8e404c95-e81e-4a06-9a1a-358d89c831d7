package p158x2;

import android.annotation.TargetApi;
import android.app.Activity;
import android.app.Application;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.SystemClock;
import java.util.Objects;
import org.litepal.util.Const;

@TargetApi(14)
/* renamed from: x2.k5 */
/* loaded from: classes.dex */
public final class C1925k5 implements Application.ActivityLifecycleCallbacks {

    /* renamed from: j */
    public final /* synthetic */ C1933l5 f7823j;

    public /* synthetic */ C1925k5(C1933l5 c1933l5) {
        this.f7823j = c1933l5;
    }

    @Override // android.app.Application.ActivityLifecycleCallbacks
    public final void onActivityCreated(Activity activity, Bundle bundle) {
        Uri data;
        try {
            try {
                ((C1948n4) this.f7823j.f8145k).mo4962e().f7792x.m4841b("onActivityCreated");
                Intent intent = activity.getIntent();
                if (intent != null && (data = intent.getData()) != null && data.isHierarchical()) {
                    ((C1948n4) this.f7823j.f8145k).m4973t();
                    String stringExtra = intent.getStringExtra("android.intent.extra.REFERRER_NAME");
                    boolean z5 = true;
                    String str = true != ("android-app://com.google.android.googlequicksearchbox/https/www.google.com".equals(stringExtra) || "https://www.google.com".equals(stringExtra) || "android-app://com.google.appcrawler".equals(stringExtra)) ? "auto" : "gs";
                    String queryParameter = data.getQueryParameter("referrer");
                    if (bundle != null) {
                        z5 = false;
                    }
                    ((C1948n4) this.f7823j.f8145k).mo4959b().m4918q(new RunnableC1917j5(this, z5, data, str, queryParameter));
                }
            } catch (RuntimeException e6) {
                ((C1948n4) this.f7823j.f8145k).mo4962e().f7784p.m4842c("Throwable caught in onActivityCreated", e6);
            }
        } finally {
            ((C1948n4) this.f7823j.f8145k).m4978y().m5059s(activity, bundle);
        }
    }

    /* JADX WARN: Type inference failed for: r0v4, types: [java.util.Map<android.app.Activity, x2.r5>, java.util.concurrent.ConcurrentHashMap] */
    @Override // android.app.Application.ActivityLifecycleCallbacks
    public final void onActivityDestroyed(Activity activity) {
        C1997t5 m4978y = ((C1948n4) this.f7823j.f8145k).m4978y();
        synchronized (m4978y.f8069v) {
            if (activity == m4978y.f8064q) {
                m4978y.f8064q = null;
            }
        }
        if (((C1948n4) m4978y.f8145k).f7923p.m4788u()) {
            m4978y.f8063p.remove(activity);
        }
    }

    @Override // android.app.Application.ActivityLifecycleCallbacks
    public final void onActivityPaused(Activity activity) {
        C1932l4 c1932l4;
        Runnable runnable;
        C1997t5 m4978y = ((C1948n4) this.f7823j.f8145k).m4978y();
        if (((C1948n4) m4978y.f8145k).f7923p.m4784q(null, C2026x2.f8209l0)) {
            synchronized (m4978y.f8069v) {
                m4978y.f8068u = false;
                m4978y.f8065r = true;
            }
        }
        Objects.requireNonNull(((C1948n4) m4978y.f8145k).f7930w);
        long elapsedRealtime = SystemClock.elapsedRealtime();
        if (!((C1948n4) m4978y.f8145k).f7923p.m4784q(null, C2026x2.f8207k0) || ((C1948n4) m4978y.f8145k).f7923p.m4788u()) {
            C1981r5 m5055n = m4978y.m5055n(activity);
            m4978y.f8061n = m4978y.f8060m;
            m4978y.f8060m = null;
            C1932l4 mo4959b = ((C1948n4) m4978y.f8145k).mo4959b();
            RunnableC1830a runnableC1830a = new RunnableC1830a(m4978y, m5055n, elapsedRealtime, 2);
            c1932l4 = mo4959b;
            runnable = runnableC1830a;
        } else {
            m4978y.f8060m = null;
            c1932l4 = ((C1948n4) m4978y.f8145k).mo4959b();
            runnable = new RunnableC2016w0(m4978y, elapsedRealtime, 1);
        }
        c1932l4.m4918q(runnable);
        C1934l6 m4971r = ((C1948n4) this.f7823j.f8145k).m4971r();
        Objects.requireNonNull(((C1948n4) m4971r.f8145k).f7930w);
        ((C1948n4) m4971r.f8145k).mo4959b().m4918q(new RunnableC2016w0(m4971r, SystemClock.elapsedRealtime(), 2));
    }

    @Override // android.app.Application.ActivityLifecycleCallbacks
    public final void onActivityResumed(Activity activity) {
        C1934l6 m4971r = ((C1948n4) this.f7823j.f8145k).m4971r();
        Objects.requireNonNull(((C1948n4) m4971r.f8145k).f7930w);
        ((C1948n4) m4971r.f8145k).mo4959b().m4918q(new RunnableC1900h6(m4971r, SystemClock.elapsedRealtime()));
        C1997t5 m4978y = ((C1948n4) this.f7823j.f8145k).m4978y();
        if (((C1948n4) m4978y.f8145k).f7923p.m4784q(null, C2026x2.f8209l0)) {
            synchronized (m4978y.f8069v) {
                m4978y.f8068u = true;
                if (activity != m4978y.f8064q) {
                    synchronized (m4978y.f8069v) {
                        m4978y.f8064q = activity;
                        m4978y.f8065r = false;
                    }
                    if (((C1948n4) m4978y.f8145k).f7923p.m4784q(null, C2026x2.f8207k0) && ((C1948n4) m4978y.f8145k).f7923p.m4788u()) {
                        m4978y.f8066s = null;
                        ((C1948n4) m4978y.f8145k).mo4959b().m4918q(new RunnableC1845b5(m4978y, 1));
                    }
                }
            }
        }
        if (((C1948n4) m4978y.f8145k).f7923p.m4784q(null, C2026x2.f8207k0) && !((C1948n4) m4978y.f8145k).f7923p.m4788u()) {
            m4978y.f8060m = m4978y.f8066s;
            ((C1948n4) m4978y.f8145k).mo4959b().m4918q(new RunnableC1854c5(m4978y, 1));
            return;
        }
        C1981r5 m5055n = m4978y.m5055n(activity);
        C1981r5 c1981r5 = m4978y.f8060m == null ? m4978y.f8061n : m4978y.f8060m;
        C1981r5 c1981r52 = m5055n.f8021b == null ? new C1981r5(m5055n.f8020a, m4978y.m5057p(activity.getClass()), m5055n.f8022c, m5055n.f8024e, m5055n.f8025f) : m5055n;
        m4978y.f8061n = m4978y.f8060m;
        m4978y.f8060m = c1981r52;
        Objects.requireNonNull(((C1948n4) m4978y.f8145k).f7930w);
        ((C1948n4) m4978y.f8145k).mo4959b().m4918q(new RunnableC1989s5(m4978y, c1981r52, c1981r5, SystemClock.elapsedRealtime()));
        C2025x1 m4964g = ((C1948n4) m4978y.f8145k).m4964g();
        Objects.requireNonNull(((C1948n4) m4964g.f8145k).f7930w);
        ((C1948n4) m4964g.f8145k).mo4959b().m4918q(new RunnableC2016w0(m4964g, SystemClock.elapsedRealtime(), 0));
    }

    /* JADX WARN: Type inference failed for: r0v4, types: [java.util.Map<android.app.Activity, x2.r5>, java.util.concurrent.ConcurrentHashMap] */
    @Override // android.app.Application.ActivityLifecycleCallbacks
    public final void onActivitySaveInstanceState(Activity activity, Bundle bundle) {
        C1981r5 c1981r5;
        C1997t5 m4978y = ((C1948n4) this.f7823j.f8145k).m4978y();
        if (!((C1948n4) m4978y.f8145k).f7923p.m4788u() || bundle == null || (c1981r5 = (C1981r5) m4978y.f8063p.get(activity)) == null) {
            return;
        }
        Bundle bundle2 = new Bundle();
        bundle2.putLong("id", c1981r5.f8022c);
        bundle2.putString(Const.TableSchema.COLUMN_NAME, c1981r5.f8020a);
        bundle2.putString("referrer_name", c1981r5.f8021b);
        bundle.putBundle("com.google.app_measurement.screen_service", bundle2);
    }

    @Override // android.app.Application.ActivityLifecycleCallbacks
    public final void onActivityStarted(Activity activity) {
    }

    @Override // android.app.Application.ActivityLifecycleCallbacks
    public final void onActivityStopped(Activity activity) {
    }
}
