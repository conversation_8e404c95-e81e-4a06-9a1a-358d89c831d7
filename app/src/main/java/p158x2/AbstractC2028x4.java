package p158x2;

/* renamed from: x2.x4 */
/* loaded from: classes.dex */
public abstract class AbstractC2028x4 extends C2020w4 {

    /* renamed from: l */
    public boolean f8254l;

    public AbstractC2028x4(C1948n4 c1948n4) {
        super(c1948n4);
        ((C1948n4) this.f8145k).f7914O++;
    }

    /* renamed from: i */
    public abstract boolean mo4731i();

    /* renamed from: j */
    public void mo4733j() {
    }

    /* renamed from: k */
    public final boolean m5152k() {
        return this.f8254l;
    }

    /* renamed from: l */
    public final void m5153l() {
        if (!m5152k()) {
            throw new IllegalStateException("Not initialized");
        }
    }

    /* renamed from: m */
    public final void m5154m() {
        if (this.f8254l) {
            throw new IllegalStateException("Can't initialize twice");
        }
        if (mo4731i()) {
            return;
        }
        ((C1948n4) this.f8145k).m4967k();
        this.f8254l = true;
    }

    /* renamed from: n */
    public final void m5155n() {
        if (this.f8254l) {
            throw new IllegalStateException("Can't initialize twice");
        }
        mo4733j();
        ((C1948n4) this.f8145k).m4967k();
        this.f8254l = true;
    }
}
