package p158x2;

import android.content.Context;
import android.content.ServiceConnection;
import android.net.Uri;
import android.os.Bundle;
import android.os.RemoteException;
import android.text.TextUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import p106p2.C1234a;
import p152w2.C1492b;
import p152w2.C1505c;
import p152w2.C1591j0;
import p152w2.C1627m0;
import p152w2.C1792z9;
import p152w2.InterfaceC1543f0;
import p153w3.C1798e;

/* renamed from: x2.y3 */
/* loaded from: classes.dex */
public final class RunnableC2035y3 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f8259j;

    /* renamed from: k */
    public final /* synthetic */ Object f8260k;

    /* renamed from: l */
    public final /* synthetic */ Object f8261l;

    /* renamed from: m */
    public final /* synthetic */ Object f8262m;

    public /* synthetic */ RunnableC2035y3(Object obj, Object obj2, Object obj3, int i6) {
        this.f8259j = i6;
        this.f8262m = obj;
        this.f8260k = obj2;
        this.f8261l = obj3;
    }

    @Override // java.lang.Runnable
    public final void run() {
        C1897h3 c1897h3;
        String str;
        C1897h3 c1897h32;
        String str2;
        String str3;
        C1951o c1951o;
        Bundle bundle = null;
        r5 = null;
        C1591j0 c1591j0 = null;
        bundle = null;
        switch (this.f8259j) {
            case 0:
                ServiceConnectionC2043z3 serviceConnectionC2043z3 = (ServiceConnectionC2043z3) this.f8262m;
                C1835a4 c1835a4 = serviceConnectionC2043z3.f8277b;
                String str4 = serviceConnectionC2043z3.f8276a;
                InterfaceC1543f0 interfaceC1543f0 = (InterfaceC1543f0) this.f8260k;
                ServiceConnection serviceConnection = (ServiceConnection) this.f8261l;
                c1835a4.f7540a.mo4959b().mo4915h();
                Bundle bundle2 = new Bundle();
                bundle2.putString("package_name", str4);
                try {
                    Bundle mo3708q = interfaceC1543f0.mo3708q(bundle2);
                    if (mo3708q == null) {
                        c1835a4.f7540a.mo4962e().f7784p.m4841b("Install Referrer Service returned a null response");
                    } else {
                        bundle = mo3708q;
                    }
                } catch (Exception e6) {
                    c1835a4.f7540a.mo4962e().f7784p.m4842c("Exception occurred while retrieving the Install Referrer", e6.getMessage());
                }
                c1835a4.f7540a.mo4959b().mo4915h();
                if (bundle != null) {
                    long j6 = bundle.getLong("install_begin_timestamp_seconds", 0L) * 1000;
                    if (j6 == 0) {
                        c1897h3 = c1835a4.f7540a.mo4962e().f7787s;
                        str = "Service response is missing Install Referrer install timestamp";
                    } else {
                        String string = bundle.getString("install_referrer");
                        if (string == null || string.isEmpty()) {
                            c1897h3 = c1835a4.f7540a.mo4962e().f7784p;
                            str = "No referrer defined in Install Referrer response";
                        } else {
                            c1835a4.f7540a.mo4962e().f7792x.m4842c("InstallReferrer API result", string);
                            Bundle m4721Y = c1835a4.f7540a.m4973t().m4721Y(Uri.parse(string.length() != 0 ? "?".concat(string) : new String("?")));
                            if (m4721Y == null) {
                                c1897h3 = c1835a4.f7540a.mo4962e().f7784p;
                                str = "No campaign params defined in Install Referrer result";
                            } else {
                                String string2 = m4721Y.getString("medium");
                                if (string2 != null && !"(not set)".equalsIgnoreCase(string2) && !"organic".equalsIgnoreCase(string2)) {
                                    long j7 = 1000 * bundle.getLong("referrer_click_timestamp_seconds", 0L);
                                    if (j7 == 0) {
                                        c1897h3 = c1835a4.f7540a.mo4962e().f7784p;
                                        str = "Install Referrer is missing click timestamp for ad campaign";
                                    } else {
                                        m4721Y.putLong("click_timestamp", j7);
                                    }
                                }
                                if (j6 == c1835a4.f7540a.m4970q().f8243p.m5050a()) {
                                    c1897h3 = c1835a4.f7540a.mo4962e().f7792x;
                                    str = "Install Referrer campaign has already been logged";
                                } else if (c1835a4.f7540a.m4965i()) {
                                    c1835a4.f7540a.m4970q().f8243p.m5051b(j6);
                                    c1835a4.f7540a.mo4962e().f7792x.m4842c("Logging Install Referrer campaign from sdk with ", "referrer API");
                                    m4721Y.putString("_cis", "referrer API");
                                    c1835a4.f7540a.m4972s().m4930s("auto", "_cmp", m4721Y);
                                }
                            }
                        }
                    }
                    c1897h3.m4841b(str);
                }
                C1234a m3104b = C1234a.m3104b();
                Context context = c1835a4.f7540a.f7917j;
                Objects.requireNonNull(m3104b);
                context.unbindService(serviceConnection);
                break;
            case 1:
                ((BinderC2012v4) this.f8262m).f8129a.m5086j();
                if (((C1839b) this.f8260k).f7562l.m5138c() == null) {
                    ((BinderC2012v4) this.f8262m).f8129a.m5092p((C1839b) this.f8260k, (C1847b7) this.f8261l);
                    break;
                } else {
                    ((BinderC2012v4) this.f8262m).f8129a.m5091o((C1839b) this.f8260k, (C1847b7) this.f8261l);
                    break;
                }
            case 2:
                BinderC2012v4 binderC2012v4 = (BinderC2012v4) this.f8262m;
                C1967q c1967q = (C1967q) this.f8260k;
                Objects.requireNonNull(binderC2012v4);
                if ("_cmp".equals(c1967q.f7965j) && (c1951o = c1967q.f7966k) != null && c1951o.f7942j.size() != 0) {
                    String m4988t = c1967q.f7966k.m4988t("_cis");
                    if ("referrer broadcast".equals(m4988t) || "referrer API".equals(m4988t)) {
                        binderC2012v4.f8129a.mo4962e().f7790v.m4842c("Event has been filtered ", c1967q.toString());
                        c1967q = new C1967q("_cmpx", c1967q.f7966k, c1967q.f7967l, c1967q.f7968m);
                    }
                }
                C1792z9.m4507b();
                C1866e m5068F = ((BinderC2012v4) this.f8262m).f8129a.m5068F();
                C2010v2<Boolean> c2010v2 = C2026x2.f8225t0;
                if (m5068F.m4784q(null, c2010v2)) {
                    BinderC2012v4 binderC2012v42 = (BinderC2012v4) this.f8262m;
                    C1847b7 c1847b7 = (C1847b7) this.f8261l;
                    C1889g4 c1889g4 = binderC2012v42.f8129a.f8082j;
                    C1998t6.m5060E(c1889g4);
                    if (c1889g4.m4817n(c1847b7.f7595j)) {
                        binderC2012v42.f8129a.mo4962e().f7792x.m4842c("EES config found for", c1847b7.f7595j);
                        C1889g4 c1889g42 = binderC2012v42.f8129a.f8082j;
                        C1998t6.m5060E(c1889g42);
                        String str5 = c1847b7.f7595j;
                        C1792z9.m4507b();
                        if (((C1948n4) c1889g42.f8145k).f7923p.m4784q(null, c2010v2) && !TextUtils.isEmpty(str5)) {
                            c1591j0 = c1889g42.f7712s.m2677b(str5);
                        }
                        if (c1591j0 != null) {
                            try {
                                Bundle m4989u = c1967q.f7966k.m4989u();
                                HashMap hashMap = new HashMap();
                                for (String str6 : m4989u.keySet()) {
                                    Object obj = m4989u.get(str6);
                                    if (obj != null) {
                                        hashMap.put(str6, obj);
                                    }
                                }
                                String m4543i0 = C1798e.m4543i0(c1967q.f7965j, C1798e.f7331J, C1798e.f7329H);
                                if (m4543i0 == null) {
                                    m4543i0 = c1967q.f7965j;
                                }
                                if (c1591j0.m3869a(new C1492b(m4543i0, c1967q.f7968m, hashMap))) {
                                    C1505c c1505c = c1591j0.f7019c;
                                    if (!((C1492b) c1505c.f6908c).equals((C1492b) c1505c.f6907b)) {
                                        binderC2012v42.f8129a.mo4962e().f7792x.m4842c("EES edited event", c1967q.f7965j);
                                        c1967q = C2014v6.m5114K((C1492b) c1591j0.f7019c.f6908c);
                                    }
                                    binderC2012v42.m5107F(c1967q, c1847b7);
                                    if (!((List) c1591j0.f7019c.f6909d).isEmpty()) {
                                        for (C1492b c1492b : (List) c1591j0.f7019c.f6909d) {
                                            binderC2012v42.f8129a.mo4962e().f7792x.m4842c("EES logging created event", c1492b.f6878a);
                                            binderC2012v42.m5107F(C2014v6.m5114K(c1492b), c1847b7);
                                        }
                                        break;
                                    }
                                }
                            } catch (C1627m0 unused) {
                                binderC2012v42.f8129a.mo4962e().f7784p.m4843d("EES error. appId, eventName", c1847b7.f7596k, c1967q.f7965j);
                            }
                            c1897h32 = binderC2012v42.f8129a.mo4962e().f7792x;
                            str2 = c1967q.f7965j;
                            str3 = "EES was not applied to event";
                        } else {
                            c1897h32 = binderC2012v42.f8129a.mo4962e().f7792x;
                            str2 = c1847b7.f7595j;
                            str3 = "EES not loaded for";
                        }
                        c1897h32.m4842c(str3, str2);
                    }
                    binderC2012v42.m5107F(c1967q, c1847b7);
                    break;
                } else {
                    ((BinderC2012v4) this.f8262m).m5107F(c1967q, (C1847b7) this.f8261l);
                    break;
                }
                break;
            default:
                C1855c6 c1855c6 = (C1855c6) this.f8262m;
                InterfaceC1834a3 interfaceC1834a3 = c1855c6.f7620n;
                if (interfaceC1834a3 == null) {
                    ((C1948n4) c1855c6.f8145k).mo4962e().f7784p.m4841b("Failed to send default event parameters to service");
                    break;
                } else {
                    try {
                        C1798e.m4560r((C1847b7) this.f8260k);
                        interfaceC1834a3.mo4682A((Bundle) this.f8261l, (C1847b7) this.f8260k);
                        break;
                    } catch (RemoteException e7) {
                        ((C1948n4) ((C1855c6) this.f8262m).f8145k).mo4962e().f7784p.m4842c("Failed to send default event parameters to service", e7);
                        return;
                    }
                }
        }
    }
}
