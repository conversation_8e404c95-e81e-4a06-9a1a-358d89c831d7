package p158x2;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteDatabaseLockedException;
import android.database.sqlite.SQLiteException;
import android.database.sqlite.SQLiteFullException;
import android.os.SystemClock;
import java.util.Objects;

/* renamed from: x2.d3 */
/* loaded from: classes.dex */
public final class C1861d3 extends AbstractC2011v3 {

    /* renamed from: m */
    public final C1852c3 f7629m;

    /* renamed from: n */
    public boolean f7630n;

    public C1861d3(C1948n4 c1948n4) {
        super(c1948n4);
        C1948n4 c1948n42 = (C1948n4) this.f8145k;
        Context context = c1948n42.f7917j;
        Objects.requireNonNull(c1948n42);
        this.f7629m = new C1852c3(this, context);
    }

    @Override // p158x2.AbstractC2011v3
    /* renamed from: k */
    public final boolean mo4746k() {
        return false;
    }

    /* renamed from: l */
    public final void m4763l() {
        int delete;
        mo4915h();
        try {
            SQLiteDatabase m4765n = m4765n();
            if (m4765n == null || (delete = m4765n.delete("messages", null, null)) <= 0) {
                return;
            }
            ((C1948n4) this.f8145k).mo4962e().f7792x.m4842c("Reset local analytics data. records", Integer.valueOf(delete));
        } catch (SQLiteException e6) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4842c("Error resetting local analytics data. error", e6);
        }
    }

    /* renamed from: m */
    public final boolean m4764m() {
        int i6;
        mo4915h();
        if (!this.f7630n && m4766o()) {
            int i7 = 5;
            for (0; i6 < 5; i6 + 1) {
                SQLiteDatabase sQLiteDatabase = null;
                try {
                    try {
                        SQLiteDatabase m4765n = m4765n();
                        if (m4765n == null) {
                            this.f7630n = true;
                            return false;
                        }
                        m4765n.beginTransaction();
                        m4765n.delete("messages", "type == ?", new String[]{Integer.toString(3)});
                        m4765n.setTransactionSuccessful();
                        m4765n.endTransaction();
                        m4765n.close();
                        return true;
                    } catch (SQLiteFullException e6) {
                        ((C1948n4) this.f8145k).mo4962e().f7784p.m4842c("Error deleting app launch break from local database", e6);
                        this.f7630n = true;
                        i6 = 0 == 0 ? i6 + 1 : 0;
                        sQLiteDatabase.close();
                    } catch (SQLiteException e7) {
                        if (0 != 0) {
                            try {
                                if (sQLiteDatabase.inTransaction()) {
                                    sQLiteDatabase.endTransaction();
                                }
                            } catch (Throwable th) {
                                if (0 != 0) {
                                    sQLiteDatabase.close();
                                }
                                throw th;
                            }
                        }
                        ((C1948n4) this.f8145k).mo4962e().f7784p.m4842c("Error deleting app launch break from local database", e7);
                        this.f7630n = true;
                        if (0 != 0) {
                            sQLiteDatabase.close();
                        }
                    }
                } catch (SQLiteDatabaseLockedException unused) {
                    SystemClock.sleep(i7);
                    i7 += 20;
                    if (0 != 0) {
                        sQLiteDatabase.close();
                    }
                }
            }
            ((C1948n4) this.f8145k).mo4962e().f7787s.m4841b("Error deleting app launch break from local database in reasonable time");
        }
        return false;
    }

    /* renamed from: n */
    public final SQLiteDatabase m4765n() {
        if (this.f7630n) {
            return null;
        }
        SQLiteDatabase writableDatabase = this.f7629m.getWritableDatabase();
        if (writableDatabase != null) {
            return writableDatabase;
        }
        this.f7630n = true;
        return null;
    }

    /* renamed from: o */
    public final boolean m4766o() {
        Object obj = this.f8145k;
        Context context = ((C1948n4) obj).f7917j;
        Objects.requireNonNull((C1948n4) obj);
        return context.getDatabasePath("google_app_measurement_local.db").exists();
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:36:0x0130  */
    /* JADX WARN: Removed duplicated region for block: B:38:0x0135  */
    /* JADX WARN: Type inference failed for: r2v0 */
    /* JADX WARN: Type inference failed for: r2v1, types: [boolean, int] */
    /* JADX WARN: Type inference failed for: r2v13 */
    /* renamed from: p */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean m4767p(int r17, byte[] r18) {
        /*
            Method dump skipped, instructions count: 330
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1861d3.m4767p(int, byte[]):boolean");
    }
}
