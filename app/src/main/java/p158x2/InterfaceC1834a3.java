package p158x2;

import android.os.Bundle;
import android.os.IInterface;
import java.util.List;

/* renamed from: x2.a3 */
/* loaded from: classes.dex */
public interface InterfaceC1834a3 extends IInterface {
    /* renamed from: A */
    void mo4682A(Bundle bundle, C1847b7 c1847b7);

    /* renamed from: d */
    List<C2022w6> mo4683d(String str, String str2, boolean z5, C1847b7 c1847b7);

    /* renamed from: f */
    void mo4684f(C1847b7 c1847b7);

    /* renamed from: g */
    void mo4685g(C1839b c1839b, C1847b7 c1847b7);

    /* renamed from: k */
    void mo4686k(C1847b7 c1847b7);

    /* renamed from: l */
    void mo4687l(long j6, String str, String str2, String str3);

    /* renamed from: m */
    void mo4688m(C1847b7 c1847b7);

    /* renamed from: o */
    void mo4689o(C1847b7 c1847b7);

    /* renamed from: p */
    List<C1839b> mo4690p(String str, String str2, C1847b7 c1847b7);

    /* renamed from: t */
    void mo4691t(C2022w6 c2022w6, C1847b7 c1847b7);

    /* renamed from: u */
    List<C1839b> mo4692u(String str, String str2, String str3);

    /* renamed from: v */
    String mo4693v(C1847b7 c1847b7);

    /* renamed from: y */
    void mo4694y(C1967q c1967q, C1847b7 c1847b7);

    /* renamed from: z */
    List<C2022w6> mo4695z(String str, String str2, String str3, boolean z5);
}
