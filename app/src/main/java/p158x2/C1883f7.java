package p158x2;

import p152w2.C1687r0;

/* renamed from: x2.f7 */
/* loaded from: classes.dex */
public final class C1883f7 extends AbstractC1892g7 {

    /* renamed from: g */
    public final C1687r0 f7691g;

    /* renamed from: h */
    public final /* synthetic */ C1910i7 f7692h;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C1883f7(C1910i7 c1910i7, String str, int i6, C1687r0 c1687r0) {
        super(str, i6);
        this.f7692h = c1910i7;
        this.f7691g = c1687r0;
    }

    @Override // p158x2.AbstractC1892g7
    /* renamed from: a */
    public final int mo4810a() {
        return this.f7691g.m4042t();
    }

    @Override // p158x2.AbstractC1892g7
    /* renamed from: b */
    public final boolean mo4811b() {
        return false;
    }

    @Override // p158x2.AbstractC1892g7
    /* renamed from: c */
    public final boolean mo4812c() {
        return this.f7691g.m4047y();
    }

    /* JADX WARN: Code restructure failed: missing block: B:53:0x019f, code lost:
    
        if (r2.booleanValue() == false) goto L359;
     */
    /* JADX WARN: Code restructure failed: missing block: B:54:0x0449, code lost:
    
        r9 = java.lang.Boolean.FALSE;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:57:0x0481  */
    /* JADX WARN: Removed duplicated region for block: B:60:0x0489 A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:61:0x048a  */
    /* JADX WARN: Type inference failed for: r12v12, types: [java.lang.Double] */
    /* JADX WARN: Type inference failed for: r12v15, types: [java.lang.Long] */
    /* JADX WARN: Type inference failed for: r9v10 */
    /* JADX WARN: Type inference failed for: r9v11 */
    /* JADX WARN: Type inference failed for: r9v12 */
    /* JADX WARN: Type inference failed for: r9v13 */
    /* JADX WARN: Type inference failed for: r9v14 */
    /* JADX WARN: Type inference failed for: r9v3, types: [java.lang.Boolean] */
    /* JADX WARN: Type inference failed for: r9v5, types: [java.lang.Boolean] */
    /* JADX WARN: Type inference failed for: r9v6, types: [java.lang.Boolean] */
    /* JADX WARN: Type inference failed for: r9v9 */
    /* renamed from: i */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean m4813i(java.lang.Long r17, java.lang.Long r18, p152w2.C1628m1 r19, long r20, p158x2.C1943n r22, boolean r23) {
        /*
            Method dump skipped, instructions count: 1276
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1883f7.m4813i(java.lang.Long, java.lang.Long, w2.m1, long, x2.n, boolean):boolean");
    }
}
