package p158x2;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;
import androidx.activity.result.C0052a;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.BitSet;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.zip.GZIPOutputStream;
import org.litepal.util.Const;
import p099o2.C1147b;
import p152w2.AbstractC1619l4;
import p152w2.AbstractC1630m3;
import p152w2.C1481a1;
import p152w2.C1482a2;
import p152w2.C1492b;
import p152w2.C1495b2;
import p152w2.C1508c2;
import p152w2.C1523d4;
import p152w2.C1580i1;
import p152w2.C1604k1;
import p152w2.C1616l1;
import p152w2.C1628m1;
import p152w2.C1643n4;
import p152w2.C1652o1;
import p152w2.C1664p1;
import p152w2.C1688r1;
import p152w2.C1700s1;
import p152w2.C1711t0;
import p152w2.C1712t1;
import p152w2.C1747w0;
import p152w2.C1771y0;
import p152w2.C1772y1;
import p153w3.C1798e;

/* renamed from: x2.v6 */
/* loaded from: classes.dex */
public final class C2014v6 extends AbstractC1966p6 {
    public C2014v6(C1998t6 c1998t6) {
        super(c1998t6);
    }

    /* renamed from: A */
    public static boolean m5108A(String str) {
        return str != null && str.matches("([+-])?([0-9]+\\.?[0-9]*|[0-9]*\\.?[0-9]+)") && str.length() <= 310;
    }

    /* renamed from: B */
    public static boolean m5109B(List<Long> list, int i6) {
        if (i6 < list.size() * 64) {
            return ((1 << (i6 % 64)) & list.get(i6 / 64).longValue()) != 0;
        }
        return false;
    }

    /* renamed from: C */
    public static List<Long> m5110C(BitSet bitSet) {
        int length = (bitSet.length() + 63) / 64;
        ArrayList arrayList = new ArrayList(length);
        for (int i6 = 0; i6 < length; i6++) {
            long j6 = 0;
            for (int i7 = 0; i7 < 64; i7++) {
                int i8 = (i6 * 64) + i7;
                if (i8 >= bitSet.length()) {
                    break;
                }
                if (bitSet.get(i8)) {
                    j6 |= 1 << i7;
                }
            }
            arrayList.add(Long.valueOf(j6));
        }
        return arrayList;
    }

    /* renamed from: H */
    public static <Builder extends AbstractC1630m3> Builder m5111H(Builder builder, byte[] bArr) {
        C1523d4 c1523d4 = C1523d4.f6932c;
        if (c1523d4 == null) {
            synchronized (C1523d4.class) {
                c1523d4 = C1523d4.f6932c;
                if (c1523d4 == null) {
                    c1523d4 = AbstractC1619l4.m3901b();
                    C1523d4.f6932c = c1523d4;
                }
            }
        }
        Objects.requireNonNull(builder);
        if (c1523d4 != null) {
            C1643n4 c1643n4 = (C1643n4) builder;
            c1643n4.m3949h(bArr, bArr.length, c1523d4);
            return c1643n4;
        }
        C1643n4 c1643n42 = (C1643n4) builder;
        c1643n42.m3949h(bArr, bArr.length, C1523d4.m3713a());
        return c1643n42;
    }

    /* renamed from: I */
    public static int m5112I(C1700s1 c1700s1, String str) {
        for (int i6 = 0; i6 < ((C1712t1) c1700s1.f7099k).m4246m1(); i6++) {
            if (str.equals(((C1712t1) c1700s1.f7099k).m4247n1(i6).m3697u())) {
                return i6;
            }
        }
        return -1;
    }

    /* renamed from: J */
    public static List<C1664p1> m5113J(Bundle[] bundleArr) {
        ArrayList arrayList = new ArrayList();
        for (Bundle bundle : bundleArr) {
            if (bundle != null) {
                C1652o1 m3984E = C1664p1.m3984E();
                for (String str : bundle.keySet()) {
                    C1652o1 m3984E2 = C1664p1.m3984E();
                    m3984E2.m3961l(str);
                    Object obj = bundle.get(str);
                    if (obj instanceof Long) {
                        m3984E2.m3963n(((Long) obj).longValue());
                    } else if (obj instanceof String) {
                        m3984E2.m3962m((String) obj);
                    } else if (obj instanceof Double) {
                        m3984E2.m3964o(((Double) obj).doubleValue());
                    }
                    if (m3984E.f7100l) {
                        m3984E.m3950i();
                        m3984E.f7100l = false;
                    }
                    C1664p1.m3993N((C1664p1) m3984E.f7099k, m3984E2.m3947f());
                }
                if (((C1664p1) m3984E.f7099k).m3999D() > 0) {
                    arrayList.add(m3984E.m3947f());
                }
            }
        }
        return arrayList;
    }

    /* JADX WARN: Type inference failed for: r1v0, types: [java.util.HashMap, java.util.Map<java.lang.String, java.lang.Object>] */
    /* renamed from: K */
    public static C1967q m5114K(C1492b c1492b) {
        String str;
        Bundle bundle = new Bundle();
        String str2 = "app";
        for (String str3 : c1492b.f6880c.keySet()) {
            Object m3652a = c1492b.m3652a(str3);
            if ("_o".equals(str3) && m3652a != null) {
                str2 = m3652a.toString();
            }
            if (m3652a == null) {
                str = null;
            } else if (m3652a instanceof Long) {
                bundle.putLong(str3, ((Long) m3652a).longValue());
            } else if (m3652a instanceof Double) {
                bundle.putDouble(str3, ((Double) m3652a).doubleValue());
            } else {
                str = m3652a.toString();
            }
            bundle.putString(str3, str);
        }
        String m4537d0 = C1798e.m4537d0(c1492b.f6878a);
        if (m4537d0 == null) {
            m4537d0 = c1492b.f6878a;
        }
        return new C1967q(m4537d0, new C1951o(bundle), str2, c1492b.f6879b);
    }

    /* renamed from: L */
    public static final void m5115L(C1616l1 c1616l1, String str, Object obj) {
        List<C1664p1> m3891l = c1616l1.m3891l();
        int i6 = 0;
        while (true) {
            if (i6 >= m3891l.size()) {
                i6 = -1;
                break;
            } else if (str.equals(m3891l.get(i6).m4001t())) {
                break;
            } else {
                i6++;
            }
        }
        C1652o1 m3984E = C1664p1.m3984E();
        m3984E.m3961l(str);
        if (obj instanceof Long) {
            m3984E.m3963n(((Long) obj).longValue());
        } else if (obj instanceof String) {
            m3984E.m3962m((String) obj);
        } else if (obj instanceof Double) {
            m3984E.m3964o(((Double) obj).doubleValue());
        } else if (obj instanceof Bundle[]) {
            List<C1664p1> m5113J = m5113J((Bundle[]) obj);
            if (m3984E.f7100l) {
                m3984E.m3950i();
                m3984E.f7100l = false;
            }
            C1664p1.m3994O((C1664p1) m3984E.f7099k, m5113J);
        }
        if (i6 < 0) {
            c1616l1.m3895p(m3984E);
            return;
        }
        if (c1616l1.f7100l) {
            c1616l1.m3950i();
            c1616l1.f7100l = false;
        }
        C1628m1.m3913E((C1628m1) c1616l1.f7099k, i6, m3984E.m3947f());
    }

    /* renamed from: M */
    public static final boolean m5116M(C1967q c1967q, C1847b7 c1847b7) {
        Objects.requireNonNull(c1847b7, "null reference");
        return (TextUtils.isEmpty(c1847b7.f7596k) && TextUtils.isEmpty(c1847b7.f7611z)) ? false : true;
    }

    /* renamed from: l */
    public static final C1664p1 m5117l(C1628m1 c1628m1, String str) {
        for (C1664p1 c1664p1 : c1628m1.m3924s()) {
            if (c1664p1.m4001t().equals(str)) {
                return c1664p1;
            }
        }
        return null;
    }

    /* renamed from: m */
    public static final Object m5118m(C1628m1 c1628m1, String str) {
        C1664p1 m5117l = m5117l(c1628m1, str);
        if (m5117l == null) {
            return null;
        }
        if (m5117l.m4002u()) {
            return m5117l.m4003v();
        }
        if (m5117l.m4004w()) {
            return Long.valueOf(m5117l.m4005x());
        }
        if (m5117l.m3996A()) {
            return Double.valueOf(m5117l.m3997B());
        }
        if (m5117l.m3999D() <= 0) {
            return null;
        }
        List<C1664p1> m3998C = m5117l.m3998C();
        ArrayList arrayList = new ArrayList();
        for (C1664p1 c1664p1 : m3998C) {
            if (c1664p1 != null) {
                Bundle bundle = new Bundle();
                for (C1664p1 c1664p12 : c1664p1.m3998C()) {
                    if (c1664p12.m4002u()) {
                        bundle.putString(c1664p12.m4001t(), c1664p12.m4003v());
                    } else if (c1664p12.m4004w()) {
                        bundle.putLong(c1664p12.m4001t(), c1664p12.m4005x());
                    } else if (c1664p12.m3996A()) {
                        bundle.putDouble(c1664p12.m4001t(), c1664p12.m3997B());
                    }
                }
                if (!bundle.isEmpty()) {
                    arrayList.add(bundle);
                }
            }
        }
        return (Bundle[]) arrayList.toArray(new Bundle[arrayList.size()]);
    }

    /* renamed from: p */
    public static final void m5119p(StringBuilder sb, int i6) {
        for (int i7 = 0; i7 < i6; i7++) {
            sb.append("  ");
        }
    }

    /* renamed from: q */
    public static final String m5120q(boolean z5, boolean z6, boolean z7) {
        StringBuilder sb = new StringBuilder();
        if (z5) {
            sb.append("Dynamic ");
        }
        if (z6) {
            sb.append("Sequence ");
        }
        if (z7) {
            sb.append("Session-Scoped ");
        }
        return sb.toString();
    }

    /* renamed from: r */
    public static final void m5121r(StringBuilder sb, String str, C1772y1 c1772y1) {
        if (c1772y1 == null) {
            return;
        }
        m5119p(sb, 3);
        sb.append(str);
        sb.append(" {\n");
        if (c1772y1.m4454v() != 0) {
            m5119p(sb, 4);
            sb.append("results: ");
            int i6 = 0;
            for (Long l6 : c1772y1.m4453u()) {
                int i7 = i6 + 1;
                if (i6 != 0) {
                    sb.append(", ");
                }
                sb.append(l6);
                i6 = i7;
            }
            sb.append('\n');
        }
        if (c1772y1.m4452t() != 0) {
            m5119p(sb, 4);
            sb.append("status: ");
            int i8 = 0;
            for (Long l7 : c1772y1.m4451s()) {
                int i9 = i8 + 1;
                if (i8 != 0) {
                    sb.append(", ");
                }
                sb.append(l7);
                i8 = i9;
            }
            sb.append('\n');
        }
        if (c1772y1.m4456x() != 0) {
            m5119p(sb, 4);
            sb.append("dynamic_filter_timestamps: {");
            int i10 = 0;
            for (C1604k1 c1604k1 : c1772y1.m4455w()) {
                int i11 = i10 + 1;
                if (i10 != 0) {
                    sb.append(", ");
                }
                sb.append(c1604k1.m3879s() ? Integer.valueOf(c1604k1.m3880t()) : null);
                sb.append(":");
                sb.append(c1604k1.m3881u() ? Long.valueOf(c1604k1.m3882v()) : null);
                i10 = i11;
            }
            sb.append("}\n");
        }
        if (c1772y1.m4449A() != 0) {
            m5119p(sb, 4);
            sb.append("sequence_filter_timestamps: {");
            int i12 = 0;
            for (C1482a2 c1482a2 : c1772y1.m4458z()) {
                int i13 = i12 + 1;
                if (i12 != 0) {
                    sb.append(", ");
                }
                sb.append(c1482a2.m3617s() ? Integer.valueOf(c1482a2.m3618t()) : null);
                sb.append(": [");
                Iterator<Long> it = c1482a2.m3619u().iterator();
                int i14 = 0;
                while (it.hasNext()) {
                    long longValue = it.next().longValue();
                    int i15 = i14 + 1;
                    if (i14 != 0) {
                        sb.append(", ");
                    }
                    sb.append(longValue);
                    i14 = i15;
                }
                sb.append("]");
                i12 = i13;
            }
            sb.append("}\n");
        }
        m5119p(sb, 3);
        sb.append("}\n");
    }

    /* renamed from: s */
    public static final void m5122s(StringBuilder sb, int i6, String str, Object obj) {
        if (obj == null) {
            return;
        }
        m5119p(sb, i6 + 1);
        sb.append(str);
        sb.append(": ");
        sb.append(obj);
        sb.append('\n');
    }

    /* renamed from: t */
    public static final void m5123t(StringBuilder sb, int i6, String str, C1747w0 c1747w0) {
        if (c1747w0 == null) {
            return;
        }
        m5119p(sb, i6);
        sb.append(str);
        sb.append(" {\n");
        if (c1747w0.m4345s()) {
            m5122s(sb, i6, "comparison_type", C0052a.m106j(c1747w0.m4346t()));
        }
        if (c1747w0.m4347u()) {
            m5122s(sb, i6, "match_as_float", Boolean.valueOf(c1747w0.m4348v()));
        }
        if (c1747w0.m4349w()) {
            m5122s(sb, i6, "comparison_value", c1747w0.m4350x());
        }
        if (c1747w0.m4351y()) {
            m5122s(sb, i6, "min_comparison_value", c1747w0.m4352z());
        }
        if (c1747w0.m4343A()) {
            m5122s(sb, i6, "max_comparison_value", c1747w0.m4344B());
        }
        m5119p(sb, i6);
        sb.append("}\n");
    }

    /* renamed from: D */
    public final List<Long> m5124D(List<Long> list, List<Integer> list2) {
        int i6;
        ArrayList arrayList = new ArrayList(list);
        for (Integer num : list2) {
            if (num.intValue() < 0) {
                ((C1948n4) this.f8145k).mo4962e().f7787s.m4842c("Ignoring negative bit index to be cleared", num);
            } else {
                int intValue = num.intValue() / 64;
                if (intValue >= arrayList.size()) {
                    ((C1948n4) this.f8145k).mo4962e().f7787s.m4843d("Ignoring bit index greater than bitSet size", num, Integer.valueOf(arrayList.size()));
                } else {
                    arrayList.set(intValue, Long.valueOf(((Long) arrayList.get(intValue)).longValue() & (~(1 << (num.intValue() % 64)))));
                }
            }
        }
        int size = arrayList.size();
        int size2 = arrayList.size() - 1;
        while (true) {
            int i7 = size2;
            i6 = size;
            size = i7;
            if (size < 0 || ((Long) arrayList.get(size)).longValue() != 0) {
                break;
            }
            size2 = size - 1;
        }
        return arrayList.subList(0, i6);
    }

    /* renamed from: E */
    public final boolean m5125E(long j6, long j7) {
        if (j6 == 0 || j7 <= 0) {
            return true;
        }
        Objects.requireNonNull(((C1948n4) this.f8145k).f7930w);
        return Math.abs(System.currentTimeMillis() - j6) > j7;
    }

    /* renamed from: F */
    public final long m5126F(byte[] bArr) {
        ((C1948n4) this.f8145k).m4973t().mo4915h();
        MessageDigest m4697B = C1838a7.m4697B();
        if (m4697B != null) {
            return C1838a7.m4698C(m4697B.digest(bArr));
        }
        ((C1948n4) this.f8145k).mo4962e().f7784p.m4841b("Failed to get MD5");
        return 0L;
    }

    /* renamed from: G */
    public final byte[] m5127G(byte[] bArr) {
        try {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            GZIPOutputStream gZIPOutputStream = new GZIPOutputStream(byteArrayOutputStream);
            gZIPOutputStream.write(bArr);
            gZIPOutputStream.close();
            byteArrayOutputStream.close();
            return byteArrayOutputStream.toByteArray();
        } catch (IOException e6) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4842c("Failed to gzip content", e6);
            throw e6;
        }
    }

    @Override // p158x2.AbstractC1966p6
    /* renamed from: j */
    public final void mo4768j() {
    }

    /* renamed from: n */
    public final void m5128n(StringBuilder sb, int i6, List<C1664p1> list) {
        if (list == null) {
            return;
        }
        int i7 = i6 + 1;
        for (C1664p1 c1664p1 : list) {
            if (c1664p1 != null) {
                m5119p(sb, i7);
                sb.append("param {\n");
                m5122s(sb, i7, Const.TableSchema.COLUMN_NAME, c1664p1.m4000s() ? ((C1948n4) this.f8145k).m4974u().m4795q(c1664p1.m4001t()) : null);
                m5122s(sb, i7, "string_value", c1664p1.m4002u() ? c1664p1.m4003v() : null);
                m5122s(sb, i7, "int_value", c1664p1.m4004w() ? Long.valueOf(c1664p1.m4005x()) : null);
                m5122s(sb, i7, "double_value", c1664p1.m3996A() ? Double.valueOf(c1664p1.m3997B()) : null);
                if (c1664p1.m3999D() > 0) {
                    m5128n(sb, i7, c1664p1.m3998C());
                }
                m5119p(sb, i7);
                sb.append("}\n");
            }
        }
    }

    /* renamed from: o */
    public final void m5129o(StringBuilder sb, int i6, C1711t0 c1711t0) {
        if (c1711t0 == null) {
            return;
        }
        m5119p(sb, i6);
        sb.append("filter {\n");
        if (c1711t0.m4154w()) {
            m5122s(sb, i6, "complement", Boolean.valueOf(c1711t0.m4155x()));
        }
        if (c1711t0.m4156y()) {
            m5122s(sb, i6, "param_name", ((C1948n4) this.f8145k).m4974u().m4795q(c1711t0.m4157z()));
        }
        if (c1711t0.m4150s()) {
            int i7 = i6 + 1;
            C1481a1 m4151t = c1711t0.m4151t();
            if (m4151t != null) {
                m5119p(sb, i7);
                sb.append("string_filter {\n");
                if (m4151t.m3605s()) {
                    m5122s(sb, i7, "match_type", C0052a.m107k(m4151t.m3606t()));
                }
                if (m4151t.m3607u()) {
                    m5122s(sb, i7, "expression", m4151t.m3608v());
                }
                if (m4151t.m3609w()) {
                    m5122s(sb, i7, "case_sensitive", Boolean.valueOf(m4151t.m3610x()));
                }
                if (m4151t.m3612z() > 0) {
                    m5119p(sb, i7 + 1);
                    sb.append("expression_list {\n");
                    for (String str : m4151t.m3611y()) {
                        m5119p(sb, i7 + 2);
                        sb.append(str);
                        sb.append("\n");
                    }
                    sb.append("}\n");
                }
                m5119p(sb, i7);
                sb.append("}\n");
            }
        }
        if (c1711t0.m4152u()) {
            m5123t(sb, i6 + 1, "number_filter", c1711t0.m4153v());
        }
        m5119p(sb, i6);
        sb.append("}\n");
    }

    /* renamed from: u */
    public final void m5130u(C1495b2 c1495b2, Object obj) {
        Objects.requireNonNull(obj, "null reference");
        if (c1495b2.f7100l) {
            c1495b2.m3950i();
            c1495b2.f7100l = false;
        }
        C1508c2.m3689G((C1508c2) c1495b2.f7099k);
        if (c1495b2.f7100l) {
            c1495b2.m3950i();
            c1495b2.f7100l = false;
        }
        C1508c2.m3691I((C1508c2) c1495b2.f7099k);
        if (c1495b2.f7100l) {
            c1495b2.m3950i();
            c1495b2.f7100l = false;
        }
        C1508c2.m3693K((C1508c2) c1495b2.f7099k);
        if (obj instanceof String) {
            String str = (String) obj;
            if (c1495b2.f7100l) {
                c1495b2.m3950i();
                c1495b2.f7100l = false;
            }
            C1508c2.m3688F((C1508c2) c1495b2.f7099k, str);
            return;
        }
        if (obj instanceof Long) {
            c1495b2.m3658n(((Long) obj).longValue());
            return;
        }
        if (!(obj instanceof Double)) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4842c("Ignoring invalid (type) user attribute value", obj);
            return;
        }
        double doubleValue = ((Double) obj).doubleValue();
        if (c1495b2.f7100l) {
            c1495b2.m3950i();
            c1495b2.f7100l = false;
        }
        C1508c2.m3692J((C1508c2) c1495b2.f7099k, doubleValue);
    }

    /* renamed from: v */
    public final void m5131v(C1652o1 c1652o1, Object obj) {
        if (c1652o1.f7100l) {
            c1652o1.m3950i();
            c1652o1.f7100l = false;
        }
        C1664p1.m3988I((C1664p1) c1652o1.f7099k);
        if (c1652o1.f7100l) {
            c1652o1.m3950i();
            c1652o1.f7100l = false;
        }
        C1664p1.m3990K((C1664p1) c1652o1.f7099k);
        if (c1652o1.f7100l) {
            c1652o1.m3950i();
            c1652o1.f7100l = false;
        }
        C1664p1.m3992M((C1664p1) c1652o1.f7099k);
        if (c1652o1.f7100l) {
            c1652o1.m3950i();
            c1652o1.f7100l = false;
        }
        C1664p1.m3995P((C1664p1) c1652o1.f7099k);
        if (obj instanceof String) {
            c1652o1.m3962m((String) obj);
            return;
        }
        if (obj instanceof Long) {
            c1652o1.m3963n(((Long) obj).longValue());
            return;
        }
        if (obj instanceof Double) {
            c1652o1.m3964o(((Double) obj).doubleValue());
            return;
        }
        if (!(obj instanceof Bundle[])) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4842c("Ignoring invalid (type) event param value", obj);
            return;
        }
        List<C1664p1> m5113J = m5113J((Bundle[]) obj);
        if (c1652o1.f7100l) {
            c1652o1.m3950i();
            c1652o1.f7100l = false;
        }
        C1664p1.m3994O((C1664p1) c1652o1.f7099k, m5113J);
    }

    /* renamed from: w */
    public final C1628m1 m5132w(C1935m c1935m) {
        C1616l1 m3911C = C1628m1.m3911C();
        long j6 = c1935m.f7863e;
        if (m3911C.f7100l) {
            m3911C.m3950i();
            m3911C.f7100l = false;
        }
        C1628m1.m3920L((C1628m1) m3911C.f7099k, j6);
        for (String str : c1935m.f7864f.f7942j.keySet()) {
            C1652o1 m3984E = C1664p1.m3984E();
            m3984E.m3961l(str);
            Object m4985c = c1935m.f7864f.m4985c(str);
            C1798e.m4560r(m4985c);
            m5131v(m3984E, m4985c);
            m3911C.m3895p(m3984E);
        }
        return m3911C.m3947f();
    }

    /* renamed from: x */
    public final String m5133x(C1688r1 c1688r1) {
        StringBuilder m104h = C0052a.m104h("\nbatch {\n");
        for (C1712t1 c1712t1 : c1688r1.m4052s()) {
            if (c1712t1 != null) {
                m5119p(m104h, 1);
                m104h.append("bundle {\n");
                if (c1712t1.m4238S()) {
                    m5122s(m104h, 1, "protocol_version", Integer.valueOf(c1712t1.m4239S0()));
                }
                m5122s(m104h, 1, "platform", c1712t1.m4271y1());
                if (c1712t1.m4257u()) {
                    m5122s(m104h, 1, "gmp_version", Long.valueOf(c1712t1.m4260v()));
                }
                if (c1712t1.m4263w()) {
                    m5122s(m104h, 1, "uploading_gmp_version", Long.valueOf(c1712t1.m4266x()));
                }
                if (c1712t1.m4267x0()) {
                    m5122s(m104h, 1, "dynamite_version", Long.valueOf(c1712t1.m4270y0()));
                }
                if (c1712t1.m4234O()) {
                    m5122s(m104h, 1, "config_version", Long.valueOf(c1712t1.m4235P()));
                }
                m5122s(m104h, 1, "gmp_app_id", c1712t1.m4227H());
                m5122s(m104h, 1, "admob_app_id", c1712t1.m4264w0());
                m5122s(m104h, 1, "app_id", c1712t1.m4252s());
                m5122s(m104h, 1, "app_version", c1712t1.m4254t());
                if (c1712t1.m4232M()) {
                    m5122s(m104h, 1, "app_version_major", Integer.valueOf(c1712t1.m4233N()));
                }
                m5122s(m104h, 1, "firebase_instance_id", c1712t1.m4231L());
                if (c1712t1.m4219C()) {
                    m5122s(m104h, 1, "dev_cert_hash", Long.valueOf(c1712t1.m4221D()));
                }
                m5122s(m104h, 1, "app_store", c1712t1.m4224E1());
                if (c1712t1.m4248o1()) {
                    m5122s(m104h, 1, "upload_timestamp_millis", Long.valueOf(c1712t1.m4249p1()));
                }
                if (c1712t1.m4250q1()) {
                    m5122s(m104h, 1, "start_timestamp_millis", Long.valueOf(c1712t1.m4251r1()));
                }
                if (c1712t1.m4253s1()) {
                    m5122s(m104h, 1, "end_timestamp_millis", Long.valueOf(c1712t1.m4256t1()));
                }
                if (c1712t1.m4259u1()) {
                    m5122s(m104h, 1, "previous_bundle_start_timestamp_millis", Long.valueOf(c1712t1.m4262v1()));
                }
                if (c1712t1.m4265w1()) {
                    m5122s(m104h, 1, "previous_bundle_end_timestamp_millis", Long.valueOf(c1712t1.m4268x1()));
                }
                m5122s(m104h, 1, "app_instance_id", c1712t1.m4216B());
                m5122s(m104h, 1, "resettable_device_id", c1712t1.m4269y());
                m5122s(m104h, 1, "ds_id", c1712t1.m4255t0());
                if (c1712t1.m4272z()) {
                    m5122s(m104h, 1, "limited_ad_tracking", Boolean.valueOf(c1712t1.m4213A()));
                }
                m5122s(m104h, 1, "os_version", c1712t1.m4274z1());
                m5122s(m104h, 1, "device_model", c1712t1.m4215A1());
                m5122s(m104h, 1, "user_default_language", c1712t1.m4218B1());
                if (c1712t1.m4220C1()) {
                    m5122s(m104h, 1, "time_zone_offset_minutes", Integer.valueOf(c1712t1.m4222D1()));
                }
                if (c1712t1.m4223E()) {
                    m5122s(m104h, 1, "bundle_sequential_index", Integer.valueOf(c1712t1.m4225F()));
                }
                if (c1712t1.m4228I()) {
                    m5122s(m104h, 1, "service_upload", Boolean.valueOf(c1712t1.m4229J()));
                }
                m5122s(m104h, 1, "health_monitor", c1712t1.m4226G());
                if (!((C1948n4) this.f8145k).f7923p.m4784q(null, C2026x2.f8211m0) && c1712t1.m4236Q() && c1712t1.m4237R() != 0) {
                    m5122s(m104h, 1, "android_id", Long.valueOf(c1712t1.m4237R()));
                }
                if (c1712t1.m4258u0()) {
                    m5122s(m104h, 1, "retry_counter", Integer.valueOf(c1712t1.m4261v0()));
                }
                if (c1712t1.m4214A0()) {
                    m5122s(m104h, 1, "consent_signals", c1712t1.m4217B0());
                }
                List<C1508c2> m4245l1 = c1712t1.m4245l1();
                if (m4245l1 != null) {
                    for (C1508c2 c1508c2 : m4245l1) {
                        if (c1508c2 != null) {
                            m5119p(m104h, 2);
                            m104h.append("user_property {\n");
                            m5122s(m104h, 2, "set_timestamp_millis", c1508c2.m3695s() ? Long.valueOf(c1508c2.m3696t()) : null);
                            m5122s(m104h, 2, Const.TableSchema.COLUMN_NAME, ((C1948n4) this.f8145k).m4974u().m4796r(c1508c2.m3697u()));
                            m5122s(m104h, 2, "string_value", c1508c2.m3699w());
                            m5122s(m104h, 2, "int_value", c1508c2.m3700x() ? Long.valueOf(c1508c2.m3701y()) : null);
                            m5122s(m104h, 2, "double_value", c1508c2.m3702z() ? Double.valueOf(c1508c2.m3694A()) : null);
                            m5119p(m104h, 2);
                            m104h.append("}\n");
                        }
                    }
                }
                List<C1580i1> m4230K = c1712t1.m4230K();
                if (m4230K != null) {
                    for (C1580i1 c1580i1 : m4230K) {
                        if (c1580i1 != null) {
                            m5119p(m104h, 2);
                            m104h.append("audience_membership {\n");
                            if (c1580i1.m3851s()) {
                                m5122s(m104h, 2, "audience_id", Integer.valueOf(c1580i1.m3852t()));
                            }
                            if (c1580i1.m3856x()) {
                                m5122s(m104h, 2, "new_audience", Boolean.valueOf(c1580i1.m3857y()));
                            }
                            m5121r(m104h, "current_data", c1580i1.m3853u());
                            if (c1580i1.m3854v()) {
                                m5121r(m104h, "previous_data", c1580i1.m3855w());
                            }
                            m5119p(m104h, 2);
                            m104h.append("}\n");
                        }
                    }
                }
                List<C1628m1> m4242i1 = c1712t1.m4242i1();
                if (m4242i1 != null) {
                    for (C1628m1 c1628m1 : m4242i1) {
                        if (c1628m1 != null) {
                            m5119p(m104h, 2);
                            m104h.append("event {\n");
                            m5122s(m104h, 2, Const.TableSchema.COLUMN_NAME, ((C1948n4) this.f8145k).m4974u().m4794p(c1628m1.m3927v()));
                            if (c1628m1.m3928w()) {
                                m5122s(m104h, 2, "timestamp_millis", Long.valueOf(c1628m1.m3929x()));
                            }
                            if (c1628m1.m3930y()) {
                                m5122s(m104h, 2, "previous_timestamp_millis", Long.valueOf(c1628m1.m3931z()));
                            }
                            if (c1628m1.m3921A()) {
                                m5122s(m104h, 2, "count", Integer.valueOf(c1628m1.m3922B()));
                            }
                            if (c1628m1.m3925t() != 0) {
                                m5128n(m104h, 2, c1628m1.m3924s());
                            }
                            m5119p(m104h, 2);
                            m104h.append("}\n");
                        }
                    }
                }
                m5119p(m104h, 1);
                m104h.append("}\n");
            }
        }
        m104h.append("}\n");
        return m104h.toString();
    }

    /* renamed from: y */
    public final String m5134y(C1771y0 c1771y0) {
        StringBuilder m104h = C0052a.m104h("\nproperty_filter {\n");
        if (c1771y0.m4430s()) {
            m5122s(m104h, 0, "filter_id", Integer.valueOf(c1771y0.m4431t()));
        }
        m5122s(m104h, 0, "property_name", ((C1948n4) this.f8145k).m4974u().m4796r(c1771y0.m4432u()));
        String m5120q = m5120q(c1771y0.m4434w(), c1771y0.m4435x(), c1771y0.m4437z());
        if (!m5120q.isEmpty()) {
            m5122s(m104h, 0, "filter_type", m5120q);
        }
        m5129o(m104h, 1, c1771y0.m4433v());
        m104h.append("}\n");
        return m104h.toString();
    }

    /* renamed from: z */
    public final <T extends Parcelable> T m5135z(byte[] bArr, Parcelable.Creator<T> creator) {
        if (bArr == null) {
            return null;
        }
        Parcel obtain = Parcel.obtain();
        try {
            obtain.unmarshall(bArr, 0, bArr.length);
            obtain.setDataPosition(0);
            return creator.createFromParcel(obtain);
        } catch (C1147b.a unused) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4841b("Failed to load parcelable from buffer");
            return null;
        } finally {
            obtain.recycle();
        }
    }
}
