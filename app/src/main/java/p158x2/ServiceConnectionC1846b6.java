package p158x2;

import android.content.ComponentName;
import android.content.Context;
import android.content.ServiceConnection;
import android.os.DeadObjectException;
import android.os.IBinder;
import android.os.IInterface;
import android.os.RemoteException;
import java.util.Objects;
import p073k2.C1009b;
import p092n2.AbstractC1099b;
import p106p2.C1234a;
import p153w3.C1798e;

/* renamed from: x2.b6 */
/* loaded from: classes.dex */
public final class ServiceConnectionC1846b6 implements ServiceConnection, AbstractC1099b.a, AbstractC1099b.b {

    /* renamed from: a */
    public volatile boolean f7587a;

    /* renamed from: b */
    public volatile C1879f3 f7588b;

    /* renamed from: c */
    public final /* synthetic */ C1855c6 f7589c;

    public ServiceConnectionC1846b6(C1855c6 c1855c6) {
        this.f7589c = c1855c6;
    }

    @Override // p092n2.AbstractC1099b.b
    /* renamed from: a */
    public final void mo2858a(C1009b c1009b) {
        C1798e.m4552n("MeasurementServiceConnection.onConnectionFailed");
        C1948n4 c1948n4 = (C1948n4) this.f7589c.f8145k;
        C1915j3 c1915j3 = c1948n4.f7925r;
        C1915j3 c1915j32 = (c1915j3 == null || !c1915j3.m5152k()) ? null : c1948n4.f7925r;
        if (c1915j32 != null) {
            c1915j32.f7787s.m4842c("Service connection failed", c1009b);
        }
        synchronized (this) {
            this.f7587a = false;
            this.f7588b = null;
        }
        ((C1948n4) this.f7589c.f8145k).mo4959b().m4918q(new RunnableC1845b5(this, 2));
    }

    @Override // p092n2.AbstractC1099b.a
    /* renamed from: b */
    public final void mo2856b() {
        C1798e.m4552n("MeasurementServiceConnection.onConnectionSuspended");
        ((C1948n4) this.f7589c.f8145k).mo4962e().f7791w.m4841b("Service connection suspended");
        ((C1948n4) this.f7589c.f8145k).mo4959b().m4918q(new RunnableC1854c5(this, 2));
    }

    @Override // p092n2.AbstractC1099b.a
    /* renamed from: c */
    public final void mo2857c() {
        C1798e.m4552n("MeasurementServiceConnection.onConnected");
        synchronized (this) {
            try {
                C1798e.m4560r(this.f7588b);
                ((C1948n4) this.f7589c.f8145k).mo4959b().m4918q(new RunnableC1837a6(this, this.f7588b.m2851b(), 1));
            } catch (DeadObjectException | IllegalStateException unused) {
                this.f7588b = null;
                this.f7587a = false;
            }
        }
    }

    @Override // android.content.ServiceConnection
    public final void onServiceConnected(ComponentName componentName, IBinder iBinder) {
        C1798e.m4552n("MeasurementServiceConnection.onServiceConnected");
        synchronized (this) {
            if (iBinder == null) {
                this.f7587a = false;
                ((C1948n4) this.f7589c.f8145k).mo4962e().f7784p.m4841b("Service connected with null binder");
                return;
            }
            InterfaceC1834a3 interfaceC1834a3 = null;
            try {
                String interfaceDescriptor = iBinder.getInterfaceDescriptor();
                if ("com.google.android.gms.measurement.internal.IMeasurementService".equals(interfaceDescriptor)) {
                    IInterface queryLocalInterface = iBinder.queryLocalInterface("com.google.android.gms.measurement.internal.IMeasurementService");
                    interfaceC1834a3 = queryLocalInterface instanceof InterfaceC1834a3 ? (InterfaceC1834a3) queryLocalInterface : new C2034y2(iBinder);
                    ((C1948n4) this.f7589c.f8145k).mo4962e().f7792x.m4841b("Bound to IMeasurementService interface");
                } else {
                    ((C1948n4) this.f7589c.f8145k).mo4962e().f7784p.m4842c("Got binder with a wrong descriptor", interfaceDescriptor);
                }
            } catch (RemoteException unused) {
                ((C1948n4) this.f7589c.f8145k).mo4962e().f7784p.m4841b("Service connect failed to get IMeasurementService");
            }
            if (interfaceC1834a3 == null) {
                this.f7587a = false;
                try {
                    C1234a m3104b = C1234a.m3104b();
                    C1855c6 c1855c6 = this.f7589c;
                    Context context = ((C1948n4) c1855c6.f8145k).f7917j;
                    ServiceConnectionC1846b6 serviceConnectionC1846b6 = c1855c6.f7619m;
                    Objects.requireNonNull(m3104b);
                    context.unbindService(serviceConnectionC1846b6);
                } catch (IllegalArgumentException unused2) {
                }
            } else {
                ((C1948n4) this.f7589c.f8145k).mo4959b().m4918q(new RunnableC1837a6(this, interfaceC1834a3, 0));
            }
        }
    }

    @Override // android.content.ServiceConnection
    public final void onServiceDisconnected(ComponentName componentName) {
        C1798e.m4552n("MeasurementServiceConnection.onServiceDisconnected");
        ((C1948n4) this.f7589c.f8145k).mo4962e().f7791w.m4841b("Service disconnected");
        ((C1948n4) this.f7589c.f8145k).mo4959b().m4918q(new RunnableC1940m4(this, componentName, 3));
    }
}
