package p158x2;

import android.app.Application;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import org.checkerframework.dataflow.qual.Pure;
import p008b0.C0385m;
import p021d.C0690o;
import p090n.C1094g;
import p110q.C1251d;
import p113q2.InterfaceC1273a;
import p152w2.AbstractC1773y2;
import p152w2.C1603k0;
import p152w2.C1605k2;
import p152w2.C1629m2;
import p152w2.C1677q2;
import p152w2.C1785z2;
import p153w3.C1798e;

/* renamed from: x2.n4 */
/* loaded from: classes.dex */
public final class C1948n4 implements InterfaceC2036y4 {

    /* renamed from: R */
    public static volatile C1948n4 f7899R;

    /* renamed from: A */
    public final C1965p5 f7900A;

    /* renamed from: B */
    public final String f7901B;

    /* renamed from: C */
    public C1861d3 f7902C;

    /* renamed from: D */
    public C1855c6 f7903D;

    /* renamed from: E */
    public C1927l f7904E;

    /* renamed from: F */
    public C1843b3 f7905F;

    /* renamed from: G */
    public C1835a4 f7906G;

    /* renamed from: I */
    public Boolean f7908I;

    /* renamed from: J */
    public long f7909J;

    /* renamed from: K */
    public volatile Boolean f7910K;

    /* renamed from: L */
    public Boolean f7911L;

    /* renamed from: M */
    public Boolean f7912M;

    /* renamed from: N */
    public volatile boolean f7913N;

    /* renamed from: O */
    public int f7914O;

    /* renamed from: Q */
    public final long f7916Q;

    /* renamed from: j */
    public final Context f7917j;

    /* renamed from: k */
    public final String f7918k;

    /* renamed from: l */
    public final String f7919l;

    /* renamed from: m */
    public final String f7920m;

    /* renamed from: n */
    public final boolean f7921n;

    /* renamed from: o */
    public final C1251d f7922o;

    /* renamed from: p */
    public final C1866e f7923p;

    /* renamed from: q */
    public final C2027x3 f7924q;

    /* renamed from: r */
    public final C1915j3 f7925r;

    /* renamed from: s */
    public final C1932l4 f7926s;

    /* renamed from: t */
    public final C1934l6 f7927t;

    /* renamed from: u */
    public final C1838a7 f7928u;

    /* renamed from: v */
    public final C1870e3 f7929v;

    /* renamed from: w */
    public final C1798e f7930w;

    /* renamed from: x */
    public final C1997t5 f7931x;

    /* renamed from: y */
    public final C1933l5 f7932y;

    /* renamed from: z */
    public final C2025x1 f7933z;

    /* renamed from: H */
    public boolean f7907H = false;

    /* renamed from: P */
    public final AtomicInteger f7915P = new AtomicInteger(0);

    public C1948n4(C1836a5 c1836a5) {
        Context context;
        C1897h3 c1897h3;
        String str;
        Bundle bundle;
        Context context2 = c1836a5.f7541a;
        C1251d c1251d = new C1251d();
        this.f7922o = c1251d;
        C0385m.f2345j = c1251d;
        this.f7917j = context2;
        this.f7918k = c1836a5.f7542b;
        this.f7919l = c1836a5.f7543c;
        this.f7920m = c1836a5.f7544d;
        this.f7921n = c1836a5.f7548h;
        this.f7910K = c1836a5.f7545e;
        this.f7901B = c1836a5.f7550j;
        boolean z5 = true;
        this.f7913N = true;
        C1603k0 c1603k0 = c1836a5.f7547g;
        if (c1603k0 != null && (bundle = c1603k0.f7051p) != null) {
            Object obj = bundle.get("measurementEnabled");
            if (obj instanceof Boolean) {
                this.f7911L = (Boolean) obj;
            }
            Object obj2 = c1603k0.f7051p.get("measurementDeactivated");
            if (obj2 instanceof Boolean) {
                this.f7912M = (Boolean) obj2;
            }
        }
        synchronized (AbstractC1773y2.f7281f) {
            C1605k2 c1605k2 = AbstractC1773y2.f7282g;
            Context applicationContext = context2.getApplicationContext();
            if (applicationContext == null) {
                applicationContext = context2;
            }
            if (c1605k2 == null || c1605k2.f7053a != applicationContext) {
                C1629m2.m3933c();
                C1785z2.m4474a();
                synchronized (C1677q2.class) {
                    C1677q2 c1677q2 = C1677q2.f7141c;
                    if (c1677q2 != null && (context = c1677q2.f7142a) != null && c1677q2.f7143b != null) {
                        context.getContentResolver().unregisterContentObserver(C1677q2.f7141c.f7143b);
                    }
                    C1677q2.f7141c = null;
                }
                AbstractC1773y2.f7282g = new C1605k2(applicationContext, C1798e.m4533Z(new C0690o(applicationContext, 8)));
                AbstractC1773y2.f7283h.incrementAndGet();
            }
        }
        this.f7930w = C1798e.f7348v;
        Long l6 = c1836a5.f7549i;
        this.f7916Q = l6 != null ? l6.longValue() : System.currentTimeMillis();
        this.f7923p = new C1866e(this);
        C2027x3 c2027x3 = new C2027x3(this);
        c2027x3.m5154m();
        this.f7924q = c2027x3;
        C1915j3 c1915j3 = new C1915j3(this);
        c1915j3.m5154m();
        this.f7925r = c1915j3;
        C1838a7 c1838a7 = new C1838a7(this);
        c1838a7.m5154m();
        this.f7928u = c1838a7;
        C1870e3 c1870e3 = new C1870e3(this);
        c1870e3.m5154m();
        this.f7929v = c1870e3;
        this.f7933z = new C2025x1(this);
        C1997t5 c1997t5 = new C1997t5(this);
        c1997t5.m5103j();
        this.f7931x = c1997t5;
        C1933l5 c1933l5 = new C1933l5(this);
        c1933l5.m5103j();
        this.f7932y = c1933l5;
        C1934l6 c1934l6 = new C1934l6(this);
        c1934l6.m5103j();
        this.f7927t = c1934l6;
        C1965p5 c1965p5 = new C1965p5(this);
        c1965p5.m5154m();
        this.f7900A = c1965p5;
        C1932l4 c1932l4 = new C1932l4(this);
        c1932l4.m5154m();
        this.f7926s = c1932l4;
        C1603k0 c1603k02 = c1836a5.f7547g;
        if (c1603k02 != null && c1603k02.f7046k != 0) {
            z5 = false;
        }
        if (context2.getApplicationContext() instanceof Application) {
            C1933l5 m4972s = m4972s();
            if (((C1948n4) m4972s.f8145k).f7917j.getApplicationContext() instanceof Application) {
                Application application = (Application) ((C1948n4) m4972s.f8145k).f7917j.getApplicationContext();
                if (m4972s.f7842m == null) {
                    m4972s.f7842m = new C1925k5(m4972s);
                }
                if (z5) {
                    application.unregisterActivityLifecycleCallbacks(m4972s.f7842m);
                    application.registerActivityLifecycleCallbacks(m4972s.f7842m);
                    c1897h3 = ((C1948n4) m4972s.f8145k).mo4962e().f7792x;
                    str = "Registered activity lifecycle callback";
                }
            }
            c1932l4.m4918q(new RunnableC1940m4(this, c1836a5, 0));
        }
        c1897h3 = mo4962e().f7787s;
        str = "Application context is not an Application";
        c1897h3.m4841b(str);
        c1932l4.m4918q(new RunnableC1940m4(this, c1836a5, 0));
    }

    /* renamed from: h */
    public static C1948n4 m4953h(Context context, C1603k0 c1603k0) {
        Bundle bundle;
        if (c1603k0 != null && (c1603k0.f7049n == null || c1603k0.f7050o == null)) {
            c1603k0 = new C1603k0(c1603k0.f7045j, c1603k0.f7046k, c1603k0.f7047l, c1603k0.f7048m, null, null, c1603k0.f7051p, null);
        }
        Objects.requireNonNull(context, "null reference");
        C1798e.m4560r(context.getApplicationContext());
        if (f7899R == null) {
            synchronized (C1948n4.class) {
                if (f7899R == null) {
                    f7899R = new C1948n4(new C1836a5(context, c1603k0));
                }
            }
        } else if (c1603k0 != null && (bundle = c1603k0.f7051p) != null && bundle.containsKey("dataCollectionDefaultEnabled")) {
            C1798e.m4560r(f7899R);
            f7899R.f7910K = Boolean.valueOf(c1603k0.f7051p.getBoolean("dataCollectionDefaultEnabled"));
        }
        C1798e.m4560r(f7899R);
        return f7899R;
    }

    /* renamed from: m */
    public static final void m4954m(C2020w4 c2020w4) {
        if (c2020w4 == null) {
            throw new IllegalStateException("Component not created");
        }
    }

    /* renamed from: n */
    public static final void m4955n(AbstractC2011v3 abstractC2011v3) {
        if (abstractC2011v3 == null) {
            throw new IllegalStateException("Component not created");
        }
        if (abstractC2011v3.f8128l) {
            return;
        }
        String valueOf = String.valueOf(abstractC2011v3.getClass());
        throw new IllegalStateException(C1094g.m2839c(new StringBuilder(valueOf.length() + 27), "Component not initialized: ", valueOf));
    }

    /* renamed from: o */
    public static final void m4956o(AbstractC2028x4 abstractC2028x4) {
        if (abstractC2028x4 == null) {
            throw new IllegalStateException("Component not created");
        }
        if (abstractC2028x4.m5152k()) {
            return;
        }
        String valueOf = String.valueOf(abstractC2028x4.getClass());
        throw new IllegalStateException(C1094g.m2839c(new StringBuilder(valueOf.length() + 27), "Component not initialized: ", valueOf));
    }

    @Pure
    /* renamed from: A */
    public final C1927l m4957A() {
        m4956o(this.f7904E);
        return this.f7904E;
    }

    @Override // p158x2.InterfaceC2036y4
    @Pure
    /* renamed from: a */
    public final C1251d mo4958a() {
        return this.f7922o;
    }

    @Override // p158x2.InterfaceC2036y4
    @Pure
    /* renamed from: b */
    public final C1932l4 mo4959b() {
        m4956o(this.f7926s);
        return this.f7926s;
    }

    @Pure
    /* renamed from: c */
    public final C1843b3 m4960c() {
        m4955n(this.f7905F);
        return this.f7905F;
    }

    @Override // p158x2.InterfaceC2036y4
    @Pure
    /* renamed from: d */
    public final Context mo4961d() {
        return this.f7917j;
    }

    @Override // p158x2.InterfaceC2036y4
    @Pure
    /* renamed from: e */
    public final C1915j3 mo4962e() {
        m4956o(this.f7925r);
        return this.f7925r;
    }

    @Override // p158x2.InterfaceC2036y4
    @Pure
    /* renamed from: f */
    public final InterfaceC1273a mo4963f() {
        return this.f7930w;
    }

    @Pure
    /* renamed from: g */
    public final C2025x1 m4964g() {
        C2025x1 c2025x1 = this.f7933z;
        if (c2025x1 != null) {
            return c2025x1;
        }
        throw new IllegalStateException("Component not created");
    }

    /* renamed from: i */
    public final boolean m4965i() {
        return m4966j() == 0;
    }

    /* renamed from: j */
    public final int m4966j() {
        mo4959b().mo4915h();
        if (this.f7923p.m4787t()) {
            return 1;
        }
        Boolean bool = this.f7912M;
        if (bool != null && bool.booleanValue()) {
            return 2;
        }
        mo4959b().mo4915h();
        if (!this.f7913N) {
            return 8;
        }
        Boolean m5147q = m4970q().m5147q();
        if (m5147q != null) {
            return m5147q.booleanValue() ? 0 : 3;
        }
        C1866e c1866e = this.f7923p;
        C1251d c1251d = ((C1948n4) c1866e.f8145k).f7922o;
        Boolean m4786s = c1866e.m4786s("firebase_analytics_collection_enabled");
        if (m4786s != null) {
            return m4786s.booleanValue() ? 0 : 4;
        }
        Boolean bool2 = this.f7911L;
        return bool2 != null ? bool2.booleanValue() ? 0 : 5 : (!this.f7923p.m4784q(null, C2026x2.f8173N) || this.f7910K == null || this.f7910K.booleanValue()) ? 0 : 7;
    }

    /* renamed from: k */
    public final void m4967k() {
        this.f7915P.incrementAndGet();
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0031, code lost:
    
        if (java.lang.Math.abs(android.os.SystemClock.elapsedRealtime() - r7.f7909J) > 1000) goto L51;
     */
    /* renamed from: l */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean m4968l() {
        /*
            r7 = this;
            boolean r0 = r7.f7907H
            if (r0 == 0) goto Ld2
            x2.l4 r0 = r7.mo4959b()
            r0.mo4915h()
            java.lang.Boolean r0 = r7.f7908I
            if (r0 == 0) goto L33
            long r1 = r7.f7909J
            r3 = 0
            int r1 = (r1 > r3 ? 1 : (r1 == r3 ? 0 : -1))
            if (r1 == 0) goto L33
            boolean r0 = r0.booleanValue()
            if (r0 != 0) goto Lcb
            w3.e r0 = r7.f7930w
            java.util.Objects.requireNonNull(r0)
            long r0 = android.os.SystemClock.elapsedRealtime()
            long r2 = r7.f7909J
            long r0 = r0 - r2
            long r0 = java.lang.Math.abs(r0)
            r2 = 1000(0x3e8, double:4.94E-321)
            int r0 = (r0 > r2 ? 1 : (r0 == r2 ? 0 : -1))
            if (r0 <= 0) goto Lcb
        L33:
            w3.e r0 = r7.f7930w
            java.util.Objects.requireNonNull(r0)
            long r0 = android.os.SystemClock.elapsedRealtime()
            r7.f7909J = r0
            x2.a7 r0 = r7.m4973t()
            java.lang.String r1 = "android.permission.INTERNET"
            boolean r0 = r0.m4708E(r1)
            r1 = 1
            r2 = 0
            if (r0 == 0) goto L7e
            x2.a7 r0 = r7.m4973t()
            java.lang.String r3 = "android.permission.ACCESS_NETWORK_STATE"
            boolean r0 = r0.m4708E(r3)
            if (r0 == 0) goto L7e
            android.content.Context r0 = r7.f7917j
            r2.b r0 = p118r2.C1306c.m3210a(r0)
            boolean r0 = r0.m3209c()
            if (r0 != 0) goto L7c
            x2.e r0 = r7.f7923p
            boolean r0 = r0.m4791x()
            if (r0 != 0) goto L7c
            android.content.Context r0 = r7.f7917j
            boolean r0 = p158x2.C1838a7.m4702P(r0)
            if (r0 == 0) goto L7e
            android.content.Context r0 = r7.f7917j
            boolean r0 = p158x2.C1838a7.m4699D(r0)
            if (r0 == 0) goto L7e
        L7c:
            r0 = r1
            goto L7f
        L7e:
            r0 = r2
        L7f:
            java.lang.Boolean r0 = java.lang.Boolean.valueOf(r0)
            r7.f7908I = r0
            boolean r0 = r0.booleanValue()
            if (r0 == 0) goto Lcb
            x2.a7 r0 = r7.m4973t()
            x2.b3 r3 = r7.m4960c()
            java.lang.String r3 = r3.m4749n()
            x2.b3 r4 = r7.m4960c()
            r4.m5102i()
            java.lang.String r4 = r4.f7583v
            x2.b3 r5 = r7.m4960c()
            r5.m5102i()
            java.lang.String r6 = r5.f7584w
            p153w3.C1798e.m4560r(r6)
            java.lang.String r5 = r5.f7584w
            boolean r0 = r0.m4734o(r3, r4, r5)
            if (r0 != 0) goto Lc5
            x2.b3 r0 = r7.m4960c()
            r0.m5102i()
            java.lang.String r0 = r0.f7583v
            boolean r0 = android.text.TextUtils.isEmpty(r0)
            if (r0 != 0) goto Lc4
            goto Lc5
        Lc4:
            r1 = r2
        Lc5:
            java.lang.Boolean r0 = java.lang.Boolean.valueOf(r1)
            r7.f7908I = r0
        Lcb:
            java.lang.Boolean r0 = r7.f7908I
            boolean r0 = r0.booleanValue()
            return r0
        Ld2:
            java.lang.IllegalStateException r0 = new java.lang.IllegalStateException
            java.lang.String r1 = "AppMeasurement is not initialized"
            r0.<init>(r1)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1948n4.m4968l():boolean");
    }

    @Pure
    /* renamed from: p */
    public final C1866e m4969p() {
        return this.f7923p;
    }

    @Pure
    /* renamed from: q */
    public final C2027x3 m4970q() {
        m4954m(this.f7924q);
        return this.f7924q;
    }

    @Pure
    /* renamed from: r */
    public final C1934l6 m4971r() {
        m4955n(this.f7927t);
        return this.f7927t;
    }

    @Pure
    /* renamed from: s */
    public final C1933l5 m4972s() {
        m4955n(this.f7932y);
        return this.f7932y;
    }

    @Pure
    /* renamed from: t */
    public final C1838a7 m4973t() {
        m4954m(this.f7928u);
        return this.f7928u;
    }

    @Pure
    /* renamed from: u */
    public final C1870e3 m4974u() {
        m4954m(this.f7929v);
        return this.f7929v;
    }

    @Pure
    /* renamed from: v */
    public final C1861d3 m4975v() {
        m4955n(this.f7902C);
        return this.f7902C;
    }

    @Pure
    /* renamed from: w */
    public final C1965p5 m4976w() {
        m4956o(this.f7900A);
        return this.f7900A;
    }

    @Pure
    /* renamed from: x */
    public final boolean m4977x() {
        return TextUtils.isEmpty(this.f7918k);
    }

    @Pure
    /* renamed from: y */
    public final C1997t5 m4978y() {
        m4955n(this.f7931x);
        return this.f7931x;
    }

    @Pure
    /* renamed from: z */
    public final C1855c6 m4979z() {
        m4955n(this.f7903D);
        return this.f7903D;
    }
}
