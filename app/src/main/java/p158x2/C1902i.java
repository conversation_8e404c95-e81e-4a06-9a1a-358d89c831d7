package p158x2;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteException;
import android.os.SystemClock;
import android.text.TextUtils;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import org.litepal.parser.LitePalParser;
import org.litepal.util.Const;
import p152w2.C1489a9;
import p152w2.C1616l1;
import p152w2.C1628m1;
import p152w2.C1700s1;
import p152w2.C1712t1;
import p153w3.C1798e;

/* renamed from: x2.i */
/* loaded from: classes.dex */
public final class C1902i extends AbstractC1966p6 {

    /* renamed from: p */
    public static final String[] f7746p = {"last_bundled_timestamp", "ALTER TABLE events ADD COLUMN last_bundled_timestamp INTEGER;", "last_bundled_day", "ALTER TABLE events ADD COLUMN last_bundled_day INTEGER;", "last_sampled_complex_event_id", "ALTER TABLE events ADD COLUMN last_sampled_complex_event_id INTEGER;", "last_sampling_rate", "ALTER TABLE events ADD COLUMN last_sampling_rate INTEGER;", "last_exempt_from_sampling", "ALTER TABLE events ADD COLUMN last_exempt_from_sampling INTEGER;", "current_session_count", "ALTER TABLE events ADD COLUMN current_session_count INTEGER;"};

    /* renamed from: q */
    public static final String[] f7747q = {"origin", "ALTER TABLE user_attributes ADD COLUMN origin TEXT;"};

    /* renamed from: r */
    public static final String[] f7748r = {"app_version", "ALTER TABLE apps ADD COLUMN app_version TEXT;", "app_store", "ALTER TABLE apps ADD COLUMN app_store TEXT;", "gmp_version", "ALTER TABLE apps ADD COLUMN gmp_version INTEGER;", "dev_cert_hash", "ALTER TABLE apps ADD COLUMN dev_cert_hash INTEGER;", "measurement_enabled", "ALTER TABLE apps ADD COLUMN measurement_enabled INTEGER;", "last_bundle_start_timestamp", "ALTER TABLE apps ADD COLUMN last_bundle_start_timestamp INTEGER;", "day", "ALTER TABLE apps ADD COLUMN day INTEGER;", "daily_public_events_count", "ALTER TABLE apps ADD COLUMN daily_public_events_count INTEGER;", "daily_events_count", "ALTER TABLE apps ADD COLUMN daily_events_count INTEGER;", "daily_conversions_count", "ALTER TABLE apps ADD COLUMN daily_conversions_count INTEGER;", "remote_config", "ALTER TABLE apps ADD COLUMN remote_config BLOB;", "config_fetched_time", "ALTER TABLE apps ADD COLUMN config_fetched_time INTEGER;", "failed_config_fetch_time", "ALTER TABLE apps ADD COLUMN failed_config_fetch_time INTEGER;", "app_version_int", "ALTER TABLE apps ADD COLUMN app_version_int INTEGER;", "firebase_instance_id", "ALTER TABLE apps ADD COLUMN firebase_instance_id TEXT;", "daily_error_events_count", "ALTER TABLE apps ADD COLUMN daily_error_events_count INTEGER;", "daily_realtime_events_count", "ALTER TABLE apps ADD COLUMN daily_realtime_events_count INTEGER;", "health_monitor_sample", "ALTER TABLE apps ADD COLUMN health_monitor_sample TEXT;", "android_id", "ALTER TABLE apps ADD COLUMN android_id INTEGER;", "adid_reporting_enabled", "ALTER TABLE apps ADD COLUMN adid_reporting_enabled INTEGER;", "ssaid_reporting_enabled", "ALTER TABLE apps ADD COLUMN ssaid_reporting_enabled INTEGER;", "admob_app_id", "ALTER TABLE apps ADD COLUMN admob_app_id TEXT;", "linked_admob_app_id", "ALTER TABLE apps ADD COLUMN linked_admob_app_id TEXT;", "dynamite_version", "ALTER TABLE apps ADD COLUMN dynamite_version INTEGER;", "safelisted_events", "ALTER TABLE apps ADD COLUMN safelisted_events TEXT;", "ga_app_id", "ALTER TABLE apps ADD COLUMN ga_app_id TEXT;", "config_last_modified_time", "ALTER TABLE apps ADD COLUMN config_last_modified_time TEXT;"};

    /* renamed from: s */
    public static final String[] f7749s = {"realtime", "ALTER TABLE raw_events ADD COLUMN realtime INTEGER;"};

    /* renamed from: t */
    public static final String[] f7750t = {"has_realtime", "ALTER TABLE queue ADD COLUMN has_realtime INTEGER;", "retry_count", "ALTER TABLE queue ADD COLUMN retry_count INTEGER;"};

    /* renamed from: u */
    public static final String[] f7751u = {"session_scoped", "ALTER TABLE event_filters ADD COLUMN session_scoped BOOLEAN;"};

    /* renamed from: v */
    public static final String[] f7752v = {"session_scoped", "ALTER TABLE property_filters ADD COLUMN session_scoped BOOLEAN;"};

    /* renamed from: w */
    public static final String[] f7753w = {"previous_install_count", "ALTER TABLE app2 ADD COLUMN previous_install_count INTEGER;"};

    /* renamed from: n */
    public final C1893h f7754n;

    /* renamed from: o */
    public final C1942m6 f7755o;

    public C1902i(C1998t6 c1998t6) {
        super(c1998t6);
        this.f7755o = new C1942m6(((C1948n4) this.f8145k).f7930w);
        Objects.requireNonNull((C1948n4) this.f8145k);
        this.f7754n = new C1893h(this, ((C1948n4) this.f8145k).f7917j);
    }

    /* renamed from: u */
    public static final void m4848u(ContentValues contentValues, Object obj) {
        C1798e.m4554o(LitePalParser.ATTR_VALUE);
        Objects.requireNonNull(obj, "null reference");
        if (obj instanceof String) {
            contentValues.put(LitePalParser.ATTR_VALUE, (String) obj);
        } else if (obj instanceof Long) {
            contentValues.put(LitePalParser.ATTR_VALUE, (Long) obj);
        } else {
            if (!(obj instanceof Double)) {
                throw new IllegalArgumentException("Invalid value type");
            }
            contentValues.put(LitePalParser.ATTR_VALUE, (Double) obj);
        }
    }

    /* renamed from: A */
    public final SQLiteDatabase m4849A() {
        mo4915h();
        try {
            return this.f7754n.getWritableDatabase();
        } catch (SQLiteException e6) {
            ((C1948n4) this.f8145k).mo4962e().f7787s.m4842c("Error opening database", e6);
            throw e6;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:52:0x0152  */
    /* renamed from: B */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final p158x2.C1943n m4850B(java.lang.String r28, java.lang.String r29) {
        /*
            Method dump skipped, instructions count: 342
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1902i.m4850B(java.lang.String, java.lang.String):x2.n");
    }

    /* renamed from: C */
    public final void m4851C(C1943n c1943n) {
        Objects.requireNonNull(c1943n, "null reference");
        mo4915h();
        m4994i();
        ContentValues contentValues = new ContentValues();
        contentValues.put("app_id", c1943n.f7879a);
        contentValues.put(Const.TableSchema.COLUMN_NAME, c1943n.f7880b);
        contentValues.put("lifetime_count", Long.valueOf(c1943n.f7881c));
        contentValues.put("current_bundle_count", Long.valueOf(c1943n.f7882d));
        contentValues.put("last_fire_timestamp", Long.valueOf(c1943n.f7884f));
        contentValues.put("last_bundled_timestamp", Long.valueOf(c1943n.f7885g));
        contentValues.put("last_bundled_day", c1943n.f7886h);
        contentValues.put("last_sampled_complex_event_id", c1943n.f7887i);
        contentValues.put("last_sampling_rate", c1943n.f7888j);
        contentValues.put("current_session_count", Long.valueOf(c1943n.f7883e));
        Boolean bool = c1943n.f7889k;
        contentValues.put("last_exempt_from_sampling", (bool == null || !bool.booleanValue()) ? null : 1L);
        try {
            if (m4849A().insertWithOnConflict("events", null, contentValues, 5) == -1) {
                ((C1948n4) this.f8145k).mo4962e().f7784p.m4842c("Failed to insert/update event aggregates (got -1). appId", C1915j3.m4886t(c1943n.f7879a));
            }
        } catch (SQLiteException e6) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4843d("Error storing event aggregates. appId", C1915j3.m4886t(c1943n.f7879a), e6);
        }
    }

    /* renamed from: D */
    public final void m4852D(String str, String str2) {
        C1798e.m4554o(str);
        C1798e.m4554o(str2);
        mo4915h();
        m4994i();
        try {
            m4849A().delete("user_attributes", "app_id=? and name=?", new String[]{str, str2});
        } catch (SQLiteException e6) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4844e("Error deleting user property. appId", C1915j3.m4886t(str), ((C1948n4) this.f8145k).m4974u().m4796r(str2), e6);
        }
    }

    /* renamed from: E */
    public final boolean m4853E(C2038y6 c2038y6) {
        mo4915h();
        m4994i();
        if (m4854F(c2038y6.f8268a, c2038y6.f8270c) == null) {
            if (C1838a7.m4706X(c2038y6.f8270c)) {
                if (m4878v("select count(1) from user_attributes where app_id=? and name not like '!_%' escape '!'", new String[]{c2038y6.f8268a}) >= ((C1948n4) this.f8145k).f7923p.m4783p(c2038y6.f8268a, C2026x2.f8165F, 25, 100)) {
                    return false;
                }
            } else if (!"_npa".equals(c2038y6.f8270c)) {
                long m4878v = m4878v("select count(1) from user_attributes where app_id=? and origin=? AND name like '!_%' escape '!'", new String[]{c2038y6.f8268a, c2038y6.f8269b});
                Objects.requireNonNull((C1948n4) this.f8145k);
                if (m4878v >= 25) {
                    return false;
                }
            }
        }
        ContentValues contentValues = new ContentValues();
        contentValues.put("app_id", c2038y6.f8268a);
        contentValues.put("origin", c2038y6.f8269b);
        contentValues.put(Const.TableSchema.COLUMN_NAME, c2038y6.f8270c);
        contentValues.put("set_timestamp", Long.valueOf(c2038y6.f8271d));
        m4848u(contentValues, c2038y6.f8272e);
        try {
            if (m4849A().insertWithOnConflict("user_attributes", null, contentValues, 5) == -1) {
                ((C1948n4) this.f8145k).mo4962e().f7784p.m4842c("Failed to insert/update user property (got -1). appId", C1915j3.m4886t(c2038y6.f8268a));
            }
        } catch (SQLiteException e6) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4843d("Error storing user property. appId", C1915j3.m4886t(c2038y6.f8268a), e6);
        }
        return true;
    }

    /* JADX WARN: Not initialized variable reg: 11, insn: 0x00a6: MOVE (r10 I:??[OBJECT, ARRAY]) = (r11 I:??[OBJECT, ARRAY]), block:B:27:0x00a6 */
    /* JADX WARN: Removed duplicated region for block: B:29:0x00a9  */
    /* renamed from: F */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final p158x2.C2038y6 m4854F(java.lang.String r20, java.lang.String r21) {
        /*
            r19 = this;
            r1 = r19
            r9 = r21
            p153w3.C1798e.m4554o(r20)
            p153w3.C1798e.m4554o(r21)
            r19.mo4915h()
            r19.m4994i()
            r10 = 0
            android.database.sqlite.SQLiteDatabase r11 = r19.m4849A()     // Catch: java.lang.Throwable -> L7c android.database.sqlite.SQLiteException -> L7e
            java.lang.String r0 = "set_timestamp"
            java.lang.String r2 = "value"
            java.lang.String r3 = "origin"
            java.lang.String[] r13 = new java.lang.String[]{r0, r2, r3}     // Catch: java.lang.Throwable -> L7c android.database.sqlite.SQLiteException -> L7e
            r0 = 2
            java.lang.String[] r15 = new java.lang.String[r0]     // Catch: java.lang.Throwable -> L7c android.database.sqlite.SQLiteException -> L7e
            r2 = 0
            r15[r2] = r20     // Catch: java.lang.Throwable -> L7c android.database.sqlite.SQLiteException -> L7e
            r3 = 1
            r15[r3] = r9     // Catch: java.lang.Throwable -> L7c android.database.sqlite.SQLiteException -> L7e
            java.lang.String r12 = "user_attributes"
            java.lang.String r14 = "app_id=? and name=?"
            r16 = 0
            r17 = 0
            r18 = 0
            android.database.Cursor r11 = r11.query(r12, r13, r14, r15, r16, r17, r18)     // Catch: java.lang.Throwable -> L7c android.database.sqlite.SQLiteException -> L7e
            boolean r4 = r11.moveToFirst()     // Catch: android.database.sqlite.SQLiteException -> L76 java.lang.Throwable -> La5
            if (r4 != 0) goto L40
            r11.close()
            return r10
        L40:
            long r6 = r11.getLong(r2)     // Catch: android.database.sqlite.SQLiteException -> L76 java.lang.Throwable -> La5
            java.lang.Object r8 = r1.m4871n(r11, r3)     // Catch: android.database.sqlite.SQLiteException -> L76 java.lang.Throwable -> La5
            if (r8 != 0) goto L4e
            r11.close()
            return r10
        L4e:
            java.lang.String r4 = r11.getString(r0)     // Catch: android.database.sqlite.SQLiteException -> L76 java.lang.Throwable -> La5
            x2.y6 r0 = new x2.y6     // Catch: android.database.sqlite.SQLiteException -> L76 java.lang.Throwable -> La5
            r2 = r0
            r3 = r20
            r5 = r21
            r2.<init>(r3, r4, r5, r6, r8)     // Catch: android.database.sqlite.SQLiteException -> L76 java.lang.Throwable -> La5
            boolean r2 = r11.moveToNext()     // Catch: android.database.sqlite.SQLiteException -> L76 java.lang.Throwable -> La5
            if (r2 == 0) goto L78
            java.lang.Object r2 = r1.f8145k     // Catch: android.database.sqlite.SQLiteException -> L76 java.lang.Throwable -> La5
            x2.n4 r2 = (p158x2.C1948n4) r2     // Catch: android.database.sqlite.SQLiteException -> L76 java.lang.Throwable -> La5
            x2.j3 r2 = r2.mo4962e()     // Catch: android.database.sqlite.SQLiteException -> L76 java.lang.Throwable -> La5
            x2.h3 r2 = r2.f7784p     // Catch: android.database.sqlite.SQLiteException -> L76 java.lang.Throwable -> La5
            java.lang.String r3 = "Got multiple records for user property, expected one. appId"
            java.lang.Object r4 = p158x2.C1915j3.m4886t(r20)     // Catch: android.database.sqlite.SQLiteException -> L76 java.lang.Throwable -> La5
            r2.m4842c(r3, r4)     // Catch: android.database.sqlite.SQLiteException -> L76 java.lang.Throwable -> La5
            goto L78
        L76:
            r0 = move-exception
            goto L80
        L78:
            r11.close()
            return r0
        L7c:
            r0 = move-exception
            goto La7
        L7e:
            r0 = move-exception
            r11 = r10
        L80:
            java.lang.Object r2 = r1.f8145k     // Catch: java.lang.Throwable -> La5
            x2.n4 r2 = (p158x2.C1948n4) r2     // Catch: java.lang.Throwable -> La5
            x2.j3 r2 = r2.mo4962e()     // Catch: java.lang.Throwable -> La5
            x2.h3 r2 = r2.f7784p     // Catch: java.lang.Throwable -> La5
            java.lang.String r3 = "Error querying user property. appId"
            java.lang.Object r4 = p158x2.C1915j3.m4886t(r20)     // Catch: java.lang.Throwable -> La5
            java.lang.Object r5 = r1.f8145k     // Catch: java.lang.Throwable -> La5
            x2.n4 r5 = (p158x2.C1948n4) r5     // Catch: java.lang.Throwable -> La5
            x2.e3 r5 = r5.m4974u()     // Catch: java.lang.Throwable -> La5
            java.lang.String r5 = r5.m4796r(r9)     // Catch: java.lang.Throwable -> La5
            r2.m4844e(r3, r4, r5, r0)     // Catch: java.lang.Throwable -> La5
            if (r11 == 0) goto La4
            r11.close()
        La4:
            return r10
        La5:
            r0 = move-exception
            r10 = r11
        La7:
            if (r10 == 0) goto Lac
            r10.close()
        Lac:
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1902i.m4854F(java.lang.String, java.lang.String):x2.y6");
    }

    /* renamed from: G */
    public final List<C2038y6> m4855G(String str) {
        C1798e.m4554o(str);
        mo4915h();
        m4994i();
        ArrayList arrayList = new ArrayList();
        Cursor cursor = null;
        try {
            try {
                Objects.requireNonNull((C1948n4) this.f8145k);
                cursor = m4849A().query("user_attributes", new String[]{Const.TableSchema.COLUMN_NAME, "origin", "set_timestamp", LitePalParser.ATTR_VALUE}, "app_id=?", new String[]{str}, null, null, "rowid", "1000");
                if (!cursor.moveToFirst()) {
                    cursor.close();
                    return arrayList;
                }
                do {
                    String string = cursor.getString(0);
                    String string2 = cursor.getString(1);
                    if (string2 == null) {
                        string2 = "";
                    }
                    String str2 = string2;
                    long j6 = cursor.getLong(2);
                    Object m4871n = m4871n(cursor, 3);
                    if (m4871n == null) {
                        ((C1948n4) this.f8145k).mo4962e().f7784p.m4842c("Read invalid user property value, ignoring it. appId", C1915j3.m4886t(str));
                    } else {
                        arrayList.add(new C2038y6(str, str2, string, j6, m4871n));
                    }
                } while (cursor.moveToNext());
                cursor.close();
                return arrayList;
            } catch (SQLiteException e6) {
                ((C1948n4) this.f8145k).mo4962e().f7784p.m4843d("Error querying user properties. appId", C1915j3.m4886t(str), e6);
                List<C2038y6> emptyList = Collections.emptyList();
                if (cursor != null) {
                    cursor.close();
                }
                return emptyList;
            }
        } catch (Throwable th) {
            if (cursor != null) {
                cursor.close();
            }
            throw th;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:32:0x00a0, code lost:
    
        r2 = ((p158x2.C1948n4) r16.f8145k).mo4962e().f7784p;
        java.util.Objects.requireNonNull((p158x2.C1948n4) r16.f8145k);
        r2.m4842c("Read more than the max allowed user properties, ignoring excess", 1000);
     */
    /* JADX WARN: Removed duplicated region for block: B:38:0x0124  */
    /* renamed from: H */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final java.util.List<p158x2.C2038y6> m4856H(java.lang.String r17, java.lang.String r18, java.lang.String r19) {
        /*
            Method dump skipped, instructions count: 302
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1902i.m4856H(java.lang.String, java.lang.String, java.lang.String):java.util.List");
    }

    /* renamed from: I */
    public final boolean m4857I(C1839b c1839b) {
        mo4915h();
        m4994i();
        String str = c1839b.f7560j;
        C1798e.m4560r(str);
        if (m4854F(str, c1839b.f7562l.f8149k) == null) {
            long m4878v = m4878v("SELECT COUNT(1) FROM conditional_properties WHERE app_id=?", new String[]{str});
            Objects.requireNonNull((C1948n4) this.f8145k);
            if (m4878v >= 1000) {
                return false;
            }
        }
        ContentValues contentValues = new ContentValues();
        contentValues.put("app_id", str);
        contentValues.put("origin", c1839b.f7561k);
        contentValues.put(Const.TableSchema.COLUMN_NAME, c1839b.f7562l.f8149k);
        Object m5138c = c1839b.f7562l.m5138c();
        Objects.requireNonNull(m5138c, "null reference");
        m4848u(contentValues, m5138c);
        contentValues.put("active", Boolean.valueOf(c1839b.f7564n));
        contentValues.put("trigger_event_name", c1839b.f7565o);
        contentValues.put("trigger_timeout", Long.valueOf(c1839b.f7567q));
        contentValues.put("timed_out_event", ((C1948n4) this.f8145k).m4973t().m4713L(c1839b.f7566p));
        contentValues.put("creation_timestamp", Long.valueOf(c1839b.f7563m));
        contentValues.put("triggered_event", ((C1948n4) this.f8145k).m4973t().m4713L(c1839b.f7568r));
        contentValues.put("triggered_timestamp", Long.valueOf(c1839b.f7562l.f8150l));
        contentValues.put("time_to_live", Long.valueOf(c1839b.f7569s));
        contentValues.put("expired_event", ((C1948n4) this.f8145k).m4973t().m4713L(c1839b.f7570t));
        try {
            if (m4849A().insertWithOnConflict("conditional_properties", null, contentValues, 5) == -1) {
                ((C1948n4) this.f8145k).mo4962e().f7784p.m4842c("Failed to insert/update conditional user property (got -1)", C1915j3.m4886t(str));
            }
        } catch (SQLiteException e6) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4843d("Error storing conditional user property", C1915j3.m4886t(str), e6);
        }
        return true;
    }

    /* JADX WARN: Removed duplicated region for block: B:29:0x0127  */
    /* renamed from: J */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final p158x2.C1839b m4858J(java.lang.String r31, java.lang.String r32) {
        /*
            Method dump skipped, instructions count: 299
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1902i.m4858J(java.lang.String, java.lang.String):x2.b");
    }

    /* renamed from: K */
    public final int m4859K(String str, String str2) {
        C1798e.m4554o(str);
        C1798e.m4554o(str2);
        mo4915h();
        m4994i();
        try {
            return m4849A().delete("conditional_properties", "app_id=? and name=?", new String[]{str, str2});
        } catch (SQLiteException e6) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4844e("Error deleting conditional property", C1915j3.m4886t(str), ((C1948n4) this.f8145k).m4974u().m4796r(str2), e6);
            return 0;
        }
    }

    /* renamed from: L */
    public final List<C1839b> m4860L(String str, String str2, String str3) {
        C1798e.m4554o(str);
        mo4915h();
        m4994i();
        ArrayList arrayList = new ArrayList(3);
        arrayList.add(str);
        StringBuilder sb = new StringBuilder("app_id=?");
        if (!TextUtils.isEmpty(str2)) {
            arrayList.add(str2);
            sb.append(" and origin=?");
        }
        if (!TextUtils.isEmpty(str3)) {
            arrayList.add(String.valueOf(str3).concat("*"));
            sb.append(" and name glob ?");
        }
        return m4861M(sb.toString(), (String[]) arrayList.toArray(new String[arrayList.size()]));
    }

    /* JADX WARN: Code restructure failed: missing block: B:19:0x005c, code lost:
    
        r2 = ((p158x2.C1948n4) r27.f8145k).mo4962e().f7784p;
        java.util.Objects.requireNonNull((p158x2.C1948n4) r27.f8145k);
        r2.m4842c("Read more than the max allowed conditional properties, ignoring extra", 1000);
     */
    /* renamed from: M */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final java.util.List<p158x2.C1839b> m4861M(java.lang.String r28, java.lang.String[] r29) {
        /*
            Method dump skipped, instructions count: 311
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1902i.m4861M(java.lang.String, java.lang.String[]):java.util.List");
    }

    /* JADX WARN: Not initialized variable reg: 4, insn: 0x0292: MOVE (r3 I:??[OBJECT, ARRAY]) = (r4 I:??[OBJECT, ARRAY]), block:B:81:0x0292 */
    /* JADX WARN: Removed duplicated region for block: B:18:0x00ee  */
    /* JADX WARN: Removed duplicated region for block: B:21:0x010d  */
    /* JADX WARN: Removed duplicated region for block: B:24:0x012c  */
    /* JADX WARN: Removed duplicated region for block: B:27:0x014b  */
    /* JADX WARN: Removed duplicated region for block: B:30:0x016d  */
    /* JADX WARN: Removed duplicated region for block: B:33:0x0199  */
    /* JADX WARN: Removed duplicated region for block: B:36:0x01b8  */
    /* JADX WARN: Removed duplicated region for block: B:39:0x01d9 A[Catch: SQLiteException -> 0x0272, all -> 0x0291, TryCatch #0 {all -> 0x0291, blocks: (B:5:0x005f, B:10:0x0069, B:12:0x00ca, B:16:0x00d4, B:19:0x00f1, B:22:0x0110, B:25:0x012f, B:28:0x014e, B:31:0x0176, B:34:0x019c, B:37:0x01bb, B:39:0x01d9, B:42:0x01e7, B:43:0x01e3, B:44:0x01ea, B:46:0x01f2, B:50:0x01fa, B:53:0x0213, B:55:0x021e, B:56:0x0230, B:58:0x0241, B:59:0x024a, B:61:0x025b, B:65:0x020f, B:68:0x0171, B:75:0x0278), top: B:2:0x000e }] */
    /* JADX WARN: Removed duplicated region for block: B:52:0x020e  */
    /* JADX WARN: Removed duplicated region for block: B:55:0x021e A[Catch: SQLiteException -> 0x0272, all -> 0x0291, TryCatch #0 {all -> 0x0291, blocks: (B:5:0x005f, B:10:0x0069, B:12:0x00ca, B:16:0x00d4, B:19:0x00f1, B:22:0x0110, B:25:0x012f, B:28:0x014e, B:31:0x0176, B:34:0x019c, B:37:0x01bb, B:39:0x01d9, B:42:0x01e7, B:43:0x01e3, B:44:0x01ea, B:46:0x01f2, B:50:0x01fa, B:53:0x0213, B:55:0x021e, B:56:0x0230, B:58:0x0241, B:59:0x024a, B:61:0x025b, B:65:0x020f, B:68:0x0171, B:75:0x0278), top: B:2:0x000e }] */
    /* JADX WARN: Removed duplicated region for block: B:58:0x0241 A[Catch: SQLiteException -> 0x0272, all -> 0x0291, TryCatch #0 {all -> 0x0291, blocks: (B:5:0x005f, B:10:0x0069, B:12:0x00ca, B:16:0x00d4, B:19:0x00f1, B:22:0x0110, B:25:0x012f, B:28:0x014e, B:31:0x0176, B:34:0x019c, B:37:0x01bb, B:39:0x01d9, B:42:0x01e7, B:43:0x01e3, B:44:0x01ea, B:46:0x01f2, B:50:0x01fa, B:53:0x0213, B:55:0x021e, B:56:0x0230, B:58:0x0241, B:59:0x024a, B:61:0x025b, B:65:0x020f, B:68:0x0171, B:75:0x0278), top: B:2:0x000e }] */
    /* JADX WARN: Removed duplicated region for block: B:61:0x025b A[Catch: SQLiteException -> 0x0272, all -> 0x0291, TRY_LEAVE, TryCatch #0 {all -> 0x0291, blocks: (B:5:0x005f, B:10:0x0069, B:12:0x00ca, B:16:0x00d4, B:19:0x00f1, B:22:0x0110, B:25:0x012f, B:28:0x014e, B:31:0x0176, B:34:0x019c, B:37:0x01bb, B:39:0x01d9, B:42:0x01e7, B:43:0x01e3, B:44:0x01ea, B:46:0x01f2, B:50:0x01fa, B:53:0x0213, B:55:0x021e, B:56:0x0230, B:58:0x0241, B:59:0x024a, B:61:0x025b, B:65:0x020f, B:68:0x0171, B:75:0x0278), top: B:2:0x000e }] */
    /* JADX WARN: Removed duplicated region for block: B:65:0x020f A[Catch: SQLiteException -> 0x0272, all -> 0x0291, TryCatch #0 {all -> 0x0291, blocks: (B:5:0x005f, B:10:0x0069, B:12:0x00ca, B:16:0x00d4, B:19:0x00f1, B:22:0x0110, B:25:0x012f, B:28:0x014e, B:31:0x0176, B:34:0x019c, B:37:0x01bb, B:39:0x01d9, B:42:0x01e7, B:43:0x01e3, B:44:0x01ea, B:46:0x01f2, B:50:0x01fa, B:53:0x0213, B:55:0x021e, B:56:0x0230, B:58:0x0241, B:59:0x024a, B:61:0x025b, B:65:0x020f, B:68:0x0171, B:75:0x0278), top: B:2:0x000e }] */
    /* JADX WARN: Removed duplicated region for block: B:66:0x01ba  */
    /* JADX WARN: Removed duplicated region for block: B:67:0x019b  */
    /* JADX WARN: Removed duplicated region for block: B:68:0x0171 A[Catch: SQLiteException -> 0x0272, all -> 0x0291, TryCatch #0 {all -> 0x0291, blocks: (B:5:0x005f, B:10:0x0069, B:12:0x00ca, B:16:0x00d4, B:19:0x00f1, B:22:0x0110, B:25:0x012f, B:28:0x014e, B:31:0x0176, B:34:0x019c, B:37:0x01bb, B:39:0x01d9, B:42:0x01e7, B:43:0x01e3, B:44:0x01ea, B:46:0x01f2, B:50:0x01fa, B:53:0x0213, B:55:0x021e, B:56:0x0230, B:58:0x0241, B:59:0x024a, B:61:0x025b, B:65:0x020f, B:68:0x0171, B:75:0x0278), top: B:2:0x000e }] */
    /* JADX WARN: Removed duplicated region for block: B:69:0x014d  */
    /* JADX WARN: Removed duplicated region for block: B:70:0x012e  */
    /* JADX WARN: Removed duplicated region for block: B:71:0x010f  */
    /* JADX WARN: Removed duplicated region for block: B:72:0x00f0  */
    /* JADX WARN: Removed duplicated region for block: B:83:0x0295  */
    /* renamed from: N */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final p158x2.C1980r4 m4862N(java.lang.String r34) {
        /*
            Method dump skipped, instructions count: 665
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1902i.m4862N(java.lang.String):x2.r4");
    }

    /* renamed from: O */
    public final void m4863O(C1980r4 c1980r4) {
        mo4915h();
        m4994i();
        String m5046y = c1980r4.m5046y();
        C1798e.m4560r(m5046y);
        ContentValues contentValues = new ContentValues();
        contentValues.put("app_id", m5046y);
        contentValues.put("app_instance_id", c1980r4.m5047z());
        contentValues.put("gmp_app_id", c1980r4.m5001B());
        contentValues.put("resettable_device_id_hash", c1980r4.m5007H());
        contentValues.put("last_bundle_index", Long.valueOf(c1980r4.m5030i()));
        contentValues.put("last_bundle_start_timestamp", Long.valueOf(c1980r4.m5011L()));
        contentValues.put("last_bundle_end_timestamp", Long.valueOf(c1980r4.m5013N()));
        contentValues.put("app_version", c1980r4.m5015P());
        contentValues.put("app_store", c1980r4.m5019T());
        contentValues.put("gmp_version", Long.valueOf(c1980r4.m5021V()));
        contentValues.put("dev_cert_hash", Long.valueOf(c1980r4.m5023b()));
        contentValues.put("measurement_enabled", Boolean.valueOf(c1980r4.m5027f()));
        c1980r4.f7994a.mo4959b().mo4915h();
        contentValues.put("day", Long.valueOf(c1980r4.f8016w));
        c1980r4.f7994a.mo4959b().mo4915h();
        contentValues.put("daily_public_events_count", Long.valueOf(c1980r4.f8017x));
        c1980r4.f7994a.mo4959b().mo4915h();
        contentValues.put("daily_events_count", Long.valueOf(c1980r4.f8018y));
        c1980r4.f7994a.mo4959b().mo4915h();
        contentValues.put("daily_conversions_count", Long.valueOf(c1980r4.f8019z));
        c1980r4.f7994a.mo4959b().mo4915h();
        contentValues.put("config_fetched_time", Long.valueOf(c1980r4.f7992E));
        c1980r4.f7994a.mo4959b().mo4915h();
        contentValues.put("failed_config_fetch_time", Long.valueOf(c1980r4.f7993F));
        contentValues.put("app_version_int", Long.valueOf(c1980r4.m5017R()));
        contentValues.put("firebase_instance_id", c1980r4.m5009J());
        c1980r4.f7994a.mo4959b().mo4915h();
        contentValues.put("daily_error_events_count", Long.valueOf(c1980r4.f7988A));
        c1980r4.f7994a.mo4959b().mo4915h();
        contentValues.put("daily_realtime_events_count", Long.valueOf(c1980r4.f7989B));
        c1980r4.f7994a.mo4959b().mo4915h();
        contentValues.put("health_monitor_sample", c1980r4.f7990C);
        contentValues.put("android_id", Long.valueOf(c1980r4.m5038q()));
        contentValues.put("adid_reporting_enabled", Boolean.valueOf(c1980r4.m5040s()));
        contentValues.put("admob_app_id", c1980r4.m5003D());
        contentValues.put("dynamite_version", Long.valueOf(c1980r4.m5025d()));
        List<String> m5044w = c1980r4.m5044w();
        if (m5044w != null) {
            if (m5044w.size() == 0) {
                ((C1948n4) this.f8145k).mo4962e().f7787s.m4842c("Safelisted events should not be an empty list. appId", m5046y);
            } else {
                contentValues.put("safelisted_events", TextUtils.join(",", m5044w));
            }
        }
        C1489a9.m3647b();
        if (((C1948n4) this.f8145k).f7923p.m4784q(m5046y, C2026x2.f8187a0)) {
            contentValues.put("ga_app_id", c1980r4.m5005F());
        }
        try {
            SQLiteDatabase m4849A = m4849A();
            if (m4849A.update("apps", contentValues, "app_id = ?", new String[]{m5046y}) == 0 && m4849A.insertWithOnConflict("apps", null, contentValues, 5) == -1) {
                ((C1948n4) this.f8145k).mo4962e().f7784p.m4842c("Failed to insert/update app (got -1). appId", C1915j3.m4886t(m5046y));
            }
        } catch (SQLiteException e6) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4843d("Error storing app. appId", C1915j3.m4886t(m5046y), e6);
        }
    }

    /* renamed from: P */
    public final C1884g m4864P(long j6, String str, boolean z5, boolean z6) {
        return m4865Q(j6, str, 1L, false, false, z5, false, z6);
    }

    /* renamed from: Q */
    public final C1884g m4865Q(long j6, String str, long j7, boolean z5, boolean z6, boolean z7, boolean z8, boolean z9) {
        C1798e.m4554o(str);
        mo4915h();
        m4994i();
        String[] strArr = {str};
        C1884g c1884g = new C1884g();
        Cursor cursor = null;
        try {
            try {
                SQLiteDatabase m4849A = m4849A();
                Cursor query = m4849A.query("apps", new String[]{"day", "daily_events_count", "daily_public_events_count", "daily_conversions_count", "daily_error_events_count", "daily_realtime_events_count"}, "app_id=?", new String[]{str}, null, null, null);
                if (!query.moveToFirst()) {
                    ((C1948n4) this.f8145k).mo4962e().f7787s.m4842c("Not updating daily counts, app is not known. appId", C1915j3.m4886t(str));
                    query.close();
                    return c1884g;
                }
                if (query.getLong(0) == j6) {
                    c1884g.f7694b = query.getLong(1);
                    c1884g.f7693a = query.getLong(2);
                    c1884g.f7695c = query.getLong(3);
                    c1884g.f7696d = query.getLong(4);
                    c1884g.f7697e = query.getLong(5);
                }
                if (z5) {
                    c1884g.f7694b += j7;
                }
                if (z6) {
                    c1884g.f7693a += j7;
                }
                if (z7) {
                    c1884g.f7695c += j7;
                }
                if (z8) {
                    c1884g.f7696d += j7;
                }
                if (z9) {
                    c1884g.f7697e += j7;
                }
                ContentValues contentValues = new ContentValues();
                contentValues.put("day", Long.valueOf(j6));
                contentValues.put("daily_public_events_count", Long.valueOf(c1884g.f7693a));
                contentValues.put("daily_events_count", Long.valueOf(c1884g.f7694b));
                contentValues.put("daily_conversions_count", Long.valueOf(c1884g.f7695c));
                contentValues.put("daily_error_events_count", Long.valueOf(c1884g.f7696d));
                contentValues.put("daily_realtime_events_count", Long.valueOf(c1884g.f7697e));
                m4849A.update("apps", contentValues, "app_id=?", strArr);
                query.close();
                return c1884g;
            } catch (SQLiteException e6) {
                ((C1948n4) this.f8145k).mo4962e().f7784p.m4843d("Error updating daily counts. appId", C1915j3.m4886t(str), e6);
                if (0 != 0) {
                    cursor.close();
                }
                return c1884g;
            }
        } catch (Throwable th) {
            if (0 != 0) {
                cursor.close();
            }
            throw th;
        }
    }

    /* renamed from: R */
    public final void m4866R(String str, byte[] bArr, String str2) {
        C1798e.m4554o(str);
        mo4915h();
        m4994i();
        ContentValues contentValues = new ContentValues();
        contentValues.put("remote_config", bArr);
        contentValues.put("config_last_modified_time", str2);
        try {
            if (m4849A().update("apps", contentValues, "app_id = ?", new String[]{str}) == 0) {
                ((C1948n4) this.f8145k).mo4962e().f7784p.m4842c("Failed to update remote config (got 0). appId", C1915j3.m4886t(str));
            }
        } catch (SQLiteException e6) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4843d("Error storing remote config. appId", C1915j3.m4886t(str), e6);
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:6:0x004a, code lost:
    
        if (r2 > (p158x2.C1866e.m4775i() + r0)) goto L8;
     */
    /* renamed from: S */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean m4867S(p152w2.C1712t1 r7, boolean r8) {
        /*
            Method dump skipped, instructions count: 303
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1902i.m4867S(w2.t1, boolean):boolean");
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:23:0x0040  */
    /* JADX WARN: Type inference failed for: r1v0 */
    /* JADX WARN: Type inference failed for: r1v1, types: [android.database.Cursor] */
    /* JADX WARN: Type inference failed for: r1v3 */
    /* renamed from: T */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final java.lang.String m4868T() {
        /*
            r6 = this;
            android.database.sqlite.SQLiteDatabase r0 = r6.m4849A()
            r1 = 0
            java.lang.String r2 = "select app_id from queue order by has_realtime desc, rowid asc limit 1;"
            android.database.Cursor r0 = r0.rawQuery(r2, r1)     // Catch: java.lang.Throwable -> L20 android.database.sqlite.SQLiteException -> L22
            boolean r2 = r0.moveToFirst()     // Catch: android.database.sqlite.SQLiteException -> L1e java.lang.Throwable -> L3a
            if (r2 == 0) goto L1a
            r2 = 0
            java.lang.String r1 = r0.getString(r2)     // Catch: android.database.sqlite.SQLiteException -> L1e java.lang.Throwable -> L3a
            r0.close()
            return r1
        L1a:
            r0.close()
            return r1
        L1e:
            r2 = move-exception
            goto L25
        L20:
            r0 = move-exception
            goto L3e
        L22:
            r0 = move-exception
            r2 = r0
            r0 = r1
        L25:
            java.lang.Object r3 = r6.f8145k     // Catch: java.lang.Throwable -> L3a
            x2.n4 r3 = (p158x2.C1948n4) r3     // Catch: java.lang.Throwable -> L3a
            x2.j3 r3 = r3.mo4962e()     // Catch: java.lang.Throwable -> L3a
            x2.h3 r3 = r3.f7784p     // Catch: java.lang.Throwable -> L3a
            java.lang.String r4 = "Database error getting next bundle app id"
            r3.m4842c(r4, r2)     // Catch: java.lang.Throwable -> L3a
            if (r0 == 0) goto L39
            r0.close()
        L39:
            return r1
        L3a:
            r1 = move-exception
            r5 = r1
            r1 = r0
            r0 = r5
        L3e:
            if (r1 == 0) goto L43
            r1.close()
        L43:
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1902i.m4868T():java.lang.String");
    }

    @Override // p158x2.AbstractC1966p6
    /* renamed from: j */
    public final void mo4768j() {
    }

    /* renamed from: l */
    public final void m4869l() {
        mo4915h();
        m4994i();
        if (m4876s()) {
            long m5050a = this.f7954l.f8090r.f7646q.m5050a();
            Objects.requireNonNull(((C1948n4) this.f8145k).f7930w);
            long elapsedRealtime = SystemClock.elapsedRealtime();
            long abs = Math.abs(elapsedRealtime - m5050a);
            Objects.requireNonNull((C1948n4) this.f8145k);
            if (abs > C2026x2.f8229x.m5101a(null).longValue()) {
                this.f7954l.f8090r.f7646q.m5051b(elapsedRealtime);
                mo4915h();
                m4994i();
                if (m4876s()) {
                    SQLiteDatabase m4849A = m4849A();
                    Objects.requireNonNull(((C1948n4) this.f8145k).f7930w);
                    Objects.requireNonNull((C1948n4) this.f8145k);
                    int delete = m4849A.delete("queue", "abs(bundle_end_timestamp - ?) > cast(? as integer)", new String[]{String.valueOf(System.currentTimeMillis()), String.valueOf(C1866e.m4775i())});
                    if (delete > 0) {
                        ((C1948n4) this.f8145k).mo4962e().f7792x.m4842c("Deleted stale rows. rowsDeleted", Integer.valueOf(delete));
                    }
                }
            }
        }
    }

    /* renamed from: m */
    public final void m4870m(List<Long> list) {
        mo4915h();
        m4994i();
        if (list.size() == 0) {
            throw new IllegalArgumentException("Given Integer is zero");
        }
        if (m4876s()) {
            String join = TextUtils.join(",", list);
            StringBuilder sb = new StringBuilder(String.valueOf(join).length() + 2);
            sb.append("(");
            sb.append(join);
            sb.append(")");
            String sb2 = sb.toString();
            StringBuilder sb3 = new StringBuilder(String.valueOf(sb2).length() + 80);
            sb3.append("SELECT COUNT(1) FROM queue WHERE rowid IN ");
            sb3.append(sb2);
            sb3.append(" AND retry_count =  2147483647 LIMIT 1");
            if (m4878v(sb3.toString(), null) > 0) {
                ((C1948n4) this.f8145k).mo4962e().f7787s.m4841b("The number of upload retries exceeds the limit. Will remain unchanged.");
            }
            try {
                SQLiteDatabase m4849A = m4849A();
                StringBuilder sb4 = new StringBuilder(String.valueOf(sb2).length() + 127);
                sb4.append("UPDATE queue SET retry_count = IFNULL(retry_count, 0) + 1 WHERE rowid IN ");
                sb4.append(sb2);
                sb4.append(" AND (retry_count IS NULL OR retry_count < ");
                sb4.append(Integer.MAX_VALUE);
                sb4.append(")");
                m4849A.execSQL(sb4.toString());
            } catch (SQLiteException e6) {
                ((C1948n4) this.f8145k).mo4962e().f7784p.m4842c("Error incrementing retry count. error", e6);
            }
        }
    }

    /* renamed from: n */
    public final Object m4871n(Cursor cursor, int i6) {
        int type = cursor.getType(i6);
        if (type == 0) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4841b("Loaded invalid null value from database");
            return null;
        }
        if (type == 1) {
            return Long.valueOf(cursor.getLong(i6));
        }
        if (type == 2) {
            return Double.valueOf(cursor.getDouble(i6));
        }
        if (type == 3) {
            return cursor.getString(i6);
        }
        if (type != 4) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4842c("Loaded invalid unknown value type, ignoring it", Integer.valueOf(type));
            return null;
        }
        ((C1948n4) this.f8145k).mo4962e().f7784p.m4841b("Loaded invalid blob type value, ignoring it");
        return null;
    }

    /* renamed from: o */
    public final long m4872o(String str) {
        long j6;
        SQLiteException e6;
        ContentValues contentValues;
        C1798e.m4554o(str);
        C1798e.m4554o("first_open_count");
        mo4915h();
        m4994i();
        SQLiteDatabase m4849A = m4849A();
        m4849A.beginTransaction();
        try {
            try {
                j6 = m4879w("select first_open_count from app2 where app_id=?", new String[]{str}, -1L);
                if (j6 == -1) {
                    ContentValues contentValues2 = new ContentValues();
                    contentValues2.put("app_id", str);
                    contentValues2.put("first_open_count", (Integer) 0);
                    contentValues2.put("previous_install_count", (Integer) 0);
                    if (m4849A.insertWithOnConflict("app2", null, contentValues2, 5) == -1) {
                        ((C1948n4) this.f8145k).mo4962e().f7784p.m4843d("Failed to insert column (got -1). appId", C1915j3.m4886t(str), "first_open_count");
                        return -1L;
                    }
                    j6 = 0;
                }
                try {
                    contentValues = new ContentValues();
                    contentValues.put("app_id", str);
                    contentValues.put("first_open_count", Long.valueOf(1 + j6));
                } catch (SQLiteException e7) {
                    e6 = e7;
                    ((C1948n4) this.f8145k).mo4962e().f7784p.m4844e("Error inserting column. appId", C1915j3.m4886t(str), "first_open_count", e6);
                    return j6;
                }
            } catch (SQLiteException e8) {
                j6 = 0;
                e6 = e8;
            }
            if (m4849A.update("app2", contentValues, "app_id = ?", new String[]{str}) == 0) {
                ((C1948n4) this.f8145k).mo4962e().f7784p.m4843d("Failed to update column (got 0). appId", C1915j3.m4886t(str), "first_open_count");
                return -1L;
            }
            m4849A.setTransactionSuccessful();
            return j6;
        } finally {
            m4849A.endTransaction();
        }
    }

    /* renamed from: p */
    public final long m4873p(String str) {
        C1798e.m4554o(str);
        return m4879w("select count(1) from events where app_id=? and name not like '!_%' escape '!'", new String[]{str}, 0L);
    }

    /* renamed from: q */
    public final boolean m4874q(String str, Long l6, long j6, C1628m1 c1628m1) {
        mo4915h();
        m4994i();
        Objects.requireNonNull(c1628m1, "null reference");
        C1798e.m4554o(str);
        byte[] m3943f = c1628m1.m3943f();
        ((C1948n4) this.f8145k).mo4962e().f7792x.m4843d("Saving complex main event, appId, data size", ((C1948n4) this.f8145k).m4974u().m4794p(str), Integer.valueOf(m3943f.length));
        ContentValues contentValues = new ContentValues();
        contentValues.put("app_id", str);
        contentValues.put("event_id", l6);
        contentValues.put("children_to_process", Long.valueOf(j6));
        contentValues.put("main_event", m3943f);
        try {
            if (m4849A().insertWithOnConflict("main_event_params", null, contentValues, 5) != -1) {
                return true;
            }
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4842c("Failed to insert complex main event (got -1). appId", C1915j3.m4886t(str));
            return false;
        } catch (SQLiteException e6) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4843d("Error storing complex main event. appId", C1915j3.m4886t(str), e6);
            return false;
        }
    }

    /* JADX WARN: Not initialized variable reg: 1, insn: 0x00d7: MOVE (r0 I:??[OBJECT, ARRAY]) = (r1 I:??[OBJECT, ARRAY]), block:B:57:0x00d7 */
    /* JADX WARN: Removed duplicated region for block: B:59:0x00da  */
    /* renamed from: r */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final android.os.Bundle m4875r(java.lang.String r8) {
        /*
            r7 = this;
            r7.mo4915h()
            r7.m4994i()
            r0 = 0
            android.database.sqlite.SQLiteDatabase r1 = r7.m4849A()     // Catch: java.lang.Throwable -> Lbd android.database.sqlite.SQLiteException -> Lbf
            r2 = 1
            java.lang.String[] r2 = new java.lang.String[r2]     // Catch: java.lang.Throwable -> Lbd android.database.sqlite.SQLiteException -> Lbf
            r3 = 0
            r2[r3] = r8     // Catch: java.lang.Throwable -> Lbd android.database.sqlite.SQLiteException -> Lbf
            java.lang.String r4 = "select parameters from default_event_params where app_id=?"
            android.database.Cursor r1 = r1.rawQuery(r4, r2)     // Catch: java.lang.Throwable -> Lbd android.database.sqlite.SQLiteException -> Lbf
            boolean r2 = r1.moveToFirst()     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            if (r2 != 0) goto L33
            java.lang.Object r8 = r7.f8145k     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            x2.n4 r8 = (p158x2.C1948n4) r8     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            x2.j3 r8 = r8.mo4962e()     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            x2.h3 r8 = r8.f7792x     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            java.lang.String r2 = "Default event parameters not found"
            r8.m4841b(r2)     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            r1.close()
            return r0
        L30:
            r8 = move-exception
            goto Lc1
        L33:
            byte[] r2 = r1.getBlob(r3)     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            w2.l1 r3 = p152w2.C1628m1.m3911C()     // Catch: android.database.sqlite.SQLiteException -> L30 java.io.IOException -> La5 java.lang.Throwable -> Ld6
            w2.m3 r2 = p158x2.C2014v6.m5111H(r3, r2)     // Catch: android.database.sqlite.SQLiteException -> L30 java.io.IOException -> La5 java.lang.Throwable -> Ld6
            w2.l1 r2 = (p152w2.C1616l1) r2     // Catch: android.database.sqlite.SQLiteException -> L30 java.io.IOException -> La5 java.lang.Throwable -> Ld6
            w2.r4 r2 = r2.m3947f()     // Catch: android.database.sqlite.SQLiteException -> L30 java.io.IOException -> La5 java.lang.Throwable -> Ld6
            w2.m1 r2 = (p152w2.C1628m1) r2     // Catch: android.database.sqlite.SQLiteException -> L30 java.io.IOException -> La5 java.lang.Throwable -> Ld6
            x2.t6 r8 = r7.f7954l     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            r8.m5071I()     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            java.util.List r8 = r2.m3924s()     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            android.os.Bundle r2 = new android.os.Bundle     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            r2.<init>()     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            java.util.Iterator r8 = r8.iterator()     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
        L59:
            boolean r3 = r8.hasNext()     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            if (r3 == 0) goto La1
            java.lang.Object r3 = r8.next()     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            w2.p1 r3 = (p152w2.C1664p1) r3     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            java.lang.String r4 = r3.m4001t()     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            boolean r5 = r3.m3996A()     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            if (r5 == 0) goto L77
            double r5 = r3.m3997B()     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            r2.putDouble(r4, r5)     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            goto L59
        L77:
            boolean r5 = r3.m4006y()     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            if (r5 == 0) goto L85
            float r3 = r3.m4007z()     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            r2.putFloat(r4, r3)     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            goto L59
        L85:
            boolean r5 = r3.m4002u()     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            if (r5 == 0) goto L93
            java.lang.String r3 = r3.m4003v()     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            r2.putString(r4, r3)     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            goto L59
        L93:
            boolean r5 = r3.m4004w()     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            if (r5 == 0) goto L59
            long r5 = r3.m4005x()     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            r2.putLong(r4, r5)     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            goto L59
        La1:
            r1.close()
            return r2
        La5:
            r2 = move-exception
            java.lang.Object r3 = r7.f8145k     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            x2.n4 r3 = (p158x2.C1948n4) r3     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            x2.j3 r3 = r3.mo4962e()     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            x2.h3 r3 = r3.f7784p     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            java.lang.String r4 = "Failed to retrieve default event parameters. appId"
            java.lang.Object r8 = p158x2.C1915j3.m4886t(r8)     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            r3.m4843d(r4, r8, r2)     // Catch: android.database.sqlite.SQLiteException -> L30 java.lang.Throwable -> Ld6
            r1.close()
            return r0
        Lbd:
            r8 = move-exception
            goto Ld8
        Lbf:
            r8 = move-exception
            r1 = r0
        Lc1:
            java.lang.Object r2 = r7.f8145k     // Catch: java.lang.Throwable -> Ld6
            x2.n4 r2 = (p158x2.C1948n4) r2     // Catch: java.lang.Throwable -> Ld6
            x2.j3 r2 = r2.mo4962e()     // Catch: java.lang.Throwable -> Ld6
            x2.h3 r2 = r2.f7784p     // Catch: java.lang.Throwable -> Ld6
            java.lang.String r3 = "Error selecting default event parameters"
            r2.m4842c(r3, r8)     // Catch: java.lang.Throwable -> Ld6
            if (r1 == 0) goto Ld5
            r1.close()
        Ld5:
            return r0
        Ld6:
            r8 = move-exception
            r0 = r1
        Ld8:
            if (r0 == 0) goto Ldd
            r0.close()
        Ldd:
            throw r8
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1902i.m4875r(java.lang.String):android.os.Bundle");
    }

    /* renamed from: s */
    public final boolean m4876s() {
        Object obj = this.f8145k;
        Context context = ((C1948n4) obj).f7917j;
        Objects.requireNonNull((C1948n4) obj);
        return context.getDatabasePath("google_app_measurement.db").exists();
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r4v1 */
    /* JADX WARN: Type inference failed for: r4v12, types: [android.database.Cursor] */
    /* JADX WARN: Type inference failed for: r4v13 */
    /* JADX WARN: Type inference failed for: r4v2, types: [android.database.Cursor] */
    /* JADX WARN: Type inference failed for: r4v3, types: [boolean] */
    /* JADX WARN: Type inference failed for: r4v4 */
    /* JADX WARN: Type inference failed for: r4v43 */
    /* JADX WARN: Type inference failed for: r4v44 */
    /* renamed from: t */
    public final void m4877t(long j6, long j7, C1990s6 c1990s6) {
        Object obj;
        SQLiteDatabase m4849A;
        String str;
        String string;
        String str2;
        String str3;
        int i6;
        String str4;
        String[] strArr;
        mo4915h();
        m4994i();
        Cursor cursor = null;
        r3 = null;
        r3 = null;
        String str5 = null;
        try {
            try {
                try {
                    m4849A = m4849A();
                    obj = TextUtils.isEmpty(null);
                    try {
                        if (obj != 0) {
                            String[] strArr2 = j7 != -1 ? new String[]{String.valueOf(j7), String.valueOf(j6)} : new String[]{String.valueOf(j6)};
                            str = j7 != -1 ? "rowid <= ? and " : "";
                            StringBuilder sb = new StringBuilder(str.length() + 148);
                            sb.append("select app_id, metadata_fingerprint from raw_events where ");
                            sb.append(str);
                            sb.append("app_id in (select app_id from apps where config_fetched_time >= ?) order by rowid limit 1;");
                            Cursor rawQuery = m4849A.rawQuery(sb.toString(), strArr2);
                            if (!rawQuery.moveToFirst()) {
                                rawQuery.close();
                                return;
                            } else {
                                str5 = rawQuery.getString(0);
                                string = rawQuery.getString(1);
                                obj = rawQuery;
                            }
                        } else {
                            String[] strArr3 = j7 != -1 ? new String[]{null, String.valueOf(j7)} : new String[]{null};
                            str = j7 != -1 ? " and rowid <= ?" : "";
                            StringBuilder sb2 = new StringBuilder(str.length() + 84);
                            sb2.append("select metadata_fingerprint from raw_events where app_id = ?");
                            sb2.append(str);
                            sb2.append(" order by rowid limit 1;");
                            Cursor rawQuery2 = m4849A.rawQuery(sb2.toString(), strArr3);
                            if (!rawQuery2.moveToFirst()) {
                                rawQuery2.close();
                                return;
                            } else {
                                string = rawQuery2.getString(0);
                                obj = rawQuery2;
                            }
                        }
                        obj.close();
                        str2 = str5;
                        str3 = string;
                    } catch (SQLiteException e6) {
                        e = e6;
                    }
                } catch (Throwable th) {
                    th = th;
                    if (cursor != null) {
                        cursor.close();
                    }
                    throw th;
                }
            } catch (SQLiteException e7) {
                e = e7;
                obj = 0;
            }
            try {
                Cursor query = m4849A.query("raw_events_metadata", new String[]{"metadata"}, "app_id = ? and metadata_fingerprint = ?", new String[]{str2, str3}, null, null, "rowid", "2");
                if (!query.moveToFirst()) {
                    ((C1948n4) this.f8145k).mo4962e().f7784p.m4842c("Raw event metadata record is missing. appId", C1915j3.m4886t(str2));
                    query.close();
                    return;
                }
                try {
                    C1712t1 m3947f = ((C1700s1) C2014v6.m5111H(C1712t1.m4158C0(), query.getBlob(0))).m3947f();
                    if (query.moveToNext()) {
                        ((C1948n4) this.f8145k).mo4962e().f7787s.m4842c("Get multiple raw event metadata records, expected one. appId", C1915j3.m4886t(str2));
                    }
                    query.close();
                    c1990s6.f8045a = m3947f;
                    if (j7 != -1) {
                        str4 = "app_id = ? and metadata_fingerprint = ? and rowid <= ?";
                        i6 = 1;
                        strArr = new String[]{str2, str3, String.valueOf(j7)};
                    } else {
                        i6 = 1;
                        str4 = "app_id = ? and metadata_fingerprint = ?";
                        strArr = new String[]{str2, str3};
                    }
                    Cursor query2 = m4849A.query("raw_events", new String[]{"rowid", Const.TableSchema.COLUMN_NAME, "timestamp", "data"}, str4, strArr, null, null, "rowid", null);
                    if (!query2.moveToFirst()) {
                        ((C1948n4) this.f8145k).mo4962e().f7787s.m4842c("Raw event data disappeared while in transaction. appId", C1915j3.m4886t(str2));
                        query2.close();
                        return;
                    }
                    do {
                        long j8 = query2.getLong(0);
                        try {
                            C1616l1 c1616l1 = (C1616l1) C2014v6.m5111H(C1628m1.m3911C(), query2.getBlob(3));
                            c1616l1.m3898s(query2.getString(i6));
                            long j9 = query2.getLong(2);
                            if (c1616l1.f7100l) {
                                c1616l1.m3950i();
                                c1616l1.f7100l = false;
                            }
                            C1628m1.m3919K((C1628m1) c1616l1.f7099k, j9);
                            if (!c1990s6.m5049a(j8, c1616l1.m3947f())) {
                                query2.close();
                                return;
                            }
                        } catch (IOException e8) {
                            ((C1948n4) this.f8145k).mo4962e().f7784p.m4843d("Data loss. Failed to merge raw event. appId", C1915j3.m4886t(str2), e8);
                        }
                    } while (query2.moveToNext());
                    query2.close();
                } catch (IOException e9) {
                    ((C1948n4) this.f8145k).mo4962e().f7784p.m4843d("Data loss. Failed to merge raw event metadata. appId", C1915j3.m4886t(str2), e9);
                    query.close();
                }
            } catch (SQLiteException e10) {
                e = e10;
                obj = obj;
                str5 = str2;
                ((C1948n4) this.f8145k).mo4962e().f7784p.m4843d("Data loss. Error selecting raw event. appId", C1915j3.m4886t(str5), e);
                if (obj != 0) {
                    obj.close();
                }
            }
        } catch (Throwable th2) {
            th = th2;
            cursor = obj;
        }
    }

    /* renamed from: v */
    public final long m4878v(String str, String[] strArr) {
        Cursor cursor = null;
        try {
            try {
                Cursor rawQuery = m4849A().rawQuery(str, strArr);
                if (!rawQuery.moveToFirst()) {
                    throw new SQLiteException("Database returned empty set");
                }
                long j6 = rawQuery.getLong(0);
                rawQuery.close();
                return j6;
            } catch (SQLiteException e6) {
                ((C1948n4) this.f8145k).mo4962e().f7784p.m4843d("Database error", str, e6);
                throw e6;
            }
        } catch (Throwable th) {
            if (0 != 0) {
                cursor.close();
            }
            throw th;
        }
    }

    /* renamed from: w */
    public final long m4879w(String str, String[] strArr, long j6) {
        Cursor cursor = null;
        try {
            try {
                cursor = m4849A().rawQuery(str, strArr);
                if (!cursor.moveToFirst()) {
                    cursor.close();
                    return j6;
                }
                long j7 = cursor.getLong(0);
                cursor.close();
                return j7;
            } catch (SQLiteException e6) {
                ((C1948n4) this.f8145k).mo4962e().f7784p.m4843d("Database error", str, e6);
                throw e6;
            }
        } catch (Throwable th) {
            if (cursor != null) {
                cursor.close();
            }
            throw th;
        }
    }

    /* renamed from: x */
    public final void m4880x() {
        m4994i();
        m4849A().beginTransaction();
    }

    /* renamed from: y */
    public final void m4881y() {
        m4994i();
        m4849A().setTransactionSuccessful();
    }

    /* renamed from: z */
    public final void m4882z() {
        m4994i();
        m4849A().endTransaction();
    }
}
