package p158x2;

import java.util.Objects;

/* renamed from: x2.h6 */
/* loaded from: classes.dex */
public final class RunnableC1900h6 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ long f7742j;

    /* renamed from: k */
    public final /* synthetic */ C1934l6 f7743k;

    public RunnableC1900h6(C1934l6 c1934l6, long j6) {
        this.f7743k = c1934l6;
        this.f7742j = j6;
    }

    @Override // java.lang.Runnable
    public final void run() {
        C1934l6 c1934l6 = this.f7743k;
        long j6 = this.f7742j;
        c1934l6.mo4915h();
        c1934l6.m4935l();
        ((C1948n4) c1934l6.f8145k).mo4962e().f7792x.m4842c("Activity resumed, time", Long.valueOf(j6));
        C1866e c1866e = ((C1948n4) c1934l6.f8145k).f7923p;
        C2010v2<Boolean> c2010v2 = C2026x2.f8209l0;
        if (c1866e.m4784q(null, c2010v2)) {
            if (((C1948n4) c1934l6.f8145k).f7923p.m4788u() || ((C1948n4) c1934l6.f8145k).m4970q().f8233A.m4998a()) {
                C1918j6 c1918j6 = c1934l6.f7857o;
                c1918j6.f7806d.mo4915h();
                c1918j6.f7805c.m4900c();
                c1918j6.f7803a = j6;
                c1918j6.f7804b = j6;
            }
            c1934l6.f7858p.m2124g();
        } else {
            c1934l6.f7858p.m2124g();
            if (((C1948n4) c1934l6.f8145k).f7923p.m4788u()) {
                C1918j6 c1918j62 = c1934l6.f7857o;
                c1918j62.f7806d.mo4915h();
                c1918j62.f7805c.m4900c();
                c1918j62.f7803a = j6;
                c1918j62.f7804b = j6;
            }
        }
        C1926k6 c1926k6 = c1934l6.f7856n;
        c1926k6.f7824a.mo4915h();
        if (((C1948n4) c1926k6.f7824a.f8145k).m4965i()) {
            if (!((C1948n4) c1926k6.f7824a.f8145k).f7923p.m4784q(null, c2010v2)) {
                ((C1948n4) c1926k6.f7824a.f8145k).m4970q().f8233A.m4999b(false);
            }
            Objects.requireNonNull(((C1948n4) c1926k6.f7824a.f8145k).f7930w);
            c1926k6.m4908b(System.currentTimeMillis(), false);
        }
    }
}
