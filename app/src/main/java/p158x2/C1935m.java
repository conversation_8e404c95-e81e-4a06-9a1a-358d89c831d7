package p158x2;

import android.os.Bundle;
import android.text.TextUtils;
import java.util.Iterator;
import java.util.Objects;
import p153w3.C1798e;

/* renamed from: x2.m */
/* loaded from: classes.dex */
public final class C1935m {

    /* renamed from: a */
    public final String f7859a;

    /* renamed from: b */
    public final String f7860b;

    /* renamed from: c */
    public final String f7861c;

    /* renamed from: d */
    public final long f7862d;

    /* renamed from: e */
    public final long f7863e;

    /* renamed from: f */
    public final C1951o f7864f;

    public C1935m(C1948n4 c1948n4, String str, String str2, String str3, long j6, long j7, C1951o c1951o) {
        C1798e.m4554o(str2);
        C1798e.m4554o(str3);
        Objects.requireNonNull(c1951o, "null reference");
        this.f7859a = str2;
        this.f7860b = str3;
        this.f7861c = true == TextUtils.isEmpty(str) ? null : str;
        this.f7862d = j6;
        this.f7863e = j7;
        if (j7 != 0 && j7 > j6) {
            c1948n4.mo4962e().f7787s.m4843d("Event created with reverse previous/current timestamps. appId, name", C1915j3.m4886t(str2), C1915j3.m4886t(str3));
        }
        this.f7864f = c1951o;
    }

    /* renamed from: a */
    public final C1935m m4936a(C1948n4 c1948n4, long j6) {
        return new C1935m(c1948n4, this.f7861c, this.f7859a, this.f7860b, this.f7862d, j6, this.f7864f);
    }

    public final String toString() {
        String str = this.f7859a;
        String str2 = this.f7860b;
        String valueOf = String.valueOf(this.f7864f);
        int length = String.valueOf(str).length();
        StringBuilder sb = new StringBuilder(length + 33 + String.valueOf(str2).length() + valueOf.length());
        sb.append("Event{appId='");
        sb.append(str);
        sb.append("', name='");
        sb.append(str2);
        sb.append("', params=");
        sb.append(valueOf);
        sb.append('}');
        return sb.toString();
    }

    public C1935m(C1948n4 c1948n4, String str, String str2, String str3, long j6, Bundle bundle) {
        C1951o c1951o;
        C1798e.m4554o(str2);
        C1798e.m4554o(str3);
        this.f7859a = str2;
        this.f7860b = str3;
        this.f7861c = true == TextUtils.isEmpty(str) ? null : str;
        this.f7862d = j6;
        this.f7863e = 0L;
        if (bundle == null || bundle.isEmpty()) {
            c1951o = new C1951o(new Bundle());
        } else {
            Bundle bundle2 = new Bundle(bundle);
            Iterator<String> it = bundle2.keySet().iterator();
            while (it.hasNext()) {
                String next = it.next();
                if (next == null) {
                    c1948n4.mo4962e().f7784p.m4841b("Param name can't be null");
                } else {
                    Object m4738s = c1948n4.m4973t().m4738s(next, bundle2.get(next));
                    if (m4738s == null) {
                        c1948n4.mo4962e().f7787s.m4842c("Param value can't be null", c1948n4.m4974u().m4795q(next));
                    } else {
                        c1948n4.m4973t().m4745z(bundle2, next, m4738s);
                    }
                }
                it.remove();
            }
            c1951o = new C1951o(bundle2);
        }
        this.f7864f = c1951o;
    }
}
