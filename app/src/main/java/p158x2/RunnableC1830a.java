package p158x2;

import java.util.Map;
import p153w3.C1798e;

/* renamed from: x2.a */
/* loaded from: classes.dex */
public final class RunnableC1830a implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f7533j;

    /* renamed from: k */
    public final /* synthetic */ Object f7534k;

    /* renamed from: l */
    public final /* synthetic */ long f7535l;

    /* renamed from: m */
    public final /* synthetic */ C2018w2 f7536m;

    public /* synthetic */ RunnableC1830a(C2018w2 c2018w2, Object obj, long j6, int i6) {
        this.f7533j = i6;
        this.f7536m = c2018w2;
        this.f7534k = obj;
        this.f7535l = j6;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v15, types: [l.g] */
    /* JADX WARN: Type inference failed for: r0v23, types: [java.util.Map<java.lang.String, java.lang.Integer>, l.g] */
    /* JADX WARN: Type inference failed for: r0v28 */
    /* JADX WARN: Type inference failed for: r0v29 */
    /* JADX WARN: Type inference failed for: r2v11, types: [java.util.Map<java.lang.String, java.lang.Long>, l.g] */
    /* JADX WARN: Type inference failed for: r2v12, types: [java.util.Map<java.lang.String, java.lang.Integer>, l.g] */
    /* JADX WARN: Type inference failed for: r2v3, types: [java.util.Map<java.lang.String, java.lang.Integer>, l.g] */
    /* JADX WARN: Type inference failed for: r5v0, types: [java.util.Map<java.lang.String, java.lang.Integer>, l.g] */
    /* JADX WARN: Type inference failed for: r5v2, types: [java.util.Map<java.lang.String, java.lang.Integer>, l.g] */
    /* JADX WARN: Type inference failed for: r6v1, types: [java.util.Map<java.lang.String, java.lang.Integer>, l.g] */
    /* JADX WARN: Type inference failed for: r6v6, types: [java.util.Map<java.lang.String, java.lang.Integer>, l.g] */
    /* JADX WARN: Type inference failed for: r6v7, types: [java.util.Map<java.lang.String, java.lang.Long>, l.g] */
    @Override // java.lang.Runnable
    public final void run() {
        Object valueOf;
        ?? r0;
        Object obj = null;
        switch (this.f7533j) {
            case 0:
                C2025x1 c2025x1 = (C2025x1) this.f7536m;
                String str = (String) this.f7534k;
                long j6 = this.f7535l;
                c2025x1.mo4915h();
                C1798e.m4554o(str);
                if (c2025x1.f8158m.isEmpty()) {
                    c2025x1.f8159n = j6;
                }
                Integer num = (Integer) c2025x1.f8158m.getOrDefault(str, null);
                if (num != null) {
                    Map<String, Integer> map = c2025x1.f8158m;
                    valueOf = Integer.valueOf(num.intValue() + 1);
                    r0 = map;
                } else {
                    ?? r22 = c2025x1.f8158m;
                    if (r22.f5038l >= 100) {
                        ((C1948n4) c2025x1.f8145k).mo4962e().f7787s.m4841b("Too many ads visible");
                        break;
                    } else {
                        r22.put(str, 1);
                        Map<String, Long> map2 = c2025x1.f8157l;
                        valueOf = Long.valueOf(j6);
                        r0 = map2;
                    }
                }
                r0.put(str, valueOf);
                break;
            case 1:
                C2025x1 c2025x12 = (C2025x1) this.f7536m;
                String str2 = (String) this.f7534k;
                long j7 = this.f7535l;
                c2025x12.mo4915h();
                C1798e.m4554o(str2);
                Integer num2 = (Integer) c2025x12.f8158m.getOrDefault(str2, null);
                if (num2 == null) {
                    ((C1948n4) c2025x12.f8145k).mo4962e().f7784p.m4842c("Call to endAdUnitExposure for unknown ad unit id", str2);
                    break;
                } else {
                    C1981r5 m5056o = ((C1948n4) c2025x12.f8145k).m4978y().m5056o(false);
                    int intValue = num2.intValue() - 1;
                    if (intValue != 0) {
                        c2025x12.f8158m.put(str2, Integer.valueOf(intValue));
                        break;
                    } else {
                        c2025x12.f8158m.remove(str2);
                        Long l6 = (Long) c2025x12.f8157l.getOrDefault(str2, null);
                        if (l6 == null) {
                            ((C1948n4) c2025x12.f8145k).mo4962e().f7784p.m4841b("First ad unit exposure time was never set");
                        } else {
                            long longValue = l6.longValue();
                            c2025x12.f8157l.remove(str2);
                            c2025x12.m5141k(str2, j7 - longValue, m5056o);
                        }
                        if (c2025x12.f8158m.isEmpty()) {
                            long j8 = c2025x12.f8159n;
                            if (j8 != 0) {
                                c2025x12.m5140j(j7 - j8, m5056o);
                                c2025x12.f8159n = 0L;
                                break;
                            } else {
                                ((C1948n4) c2025x12.f8145k).mo4962e().f7784p.m4841b("First ad exposure time was never set");
                                break;
                            }
                        }
                    }
                }
                break;
            default:
                ((C1997t5) this.f7536m).m5054m((C1981r5) this.f7534k, false, this.f7535l);
                C1997t5 c1997t5 = (C1997t5) this.f7536m;
                c1997t5.f8062o = null;
                C1855c6 m4979z = ((C1948n4) c1997t5.f8145k).m4979z();
                m4979z.mo4915h();
                m4979z.m5102i();
                m4979z.m4757s(new RunnableC1911j(m4979z, obj, 3));
                break;
        }
    }
}
