package p158x2;

import java.net.URL;
import java.util.List;
import java.util.Map;
import p153w3.C1798e;

/* renamed from: x2.o5 */
/* loaded from: classes.dex */
public final class RunnableC1957o5 implements Runnable {

    /* renamed from: j */
    public final URL f7950j;

    /* renamed from: k */
    public final String f7951k;

    /* renamed from: l */
    public final /* synthetic */ C1965p5 f7952l;

    /* renamed from: m */
    public final C1891g6 f7953m;

    public RunnableC1957o5(C1965p5 c1965p5, String str, URL url, C1891g6 c1891g6) {
        this.f7952l = c1965p5;
        C1798e.m4554o(str);
        this.f7950j = url;
        this.f7953m = c1891g6;
        this.f7951k = str;
    }

    /* renamed from: a */
    public final void m4992a(final int i6, final Exception exc, final byte[] bArr, final Map<String, List<String>> map) {
        ((C1948n4) this.f7952l.f8145k).mo4959b().m4918q(new Runnable(this, i6, exc, bArr, map) { // from class: x2.n5

            /* renamed from: j */
            public final RunnableC1957o5 f7934j;

            /* renamed from: k */
            public final int f7935k;

            /* renamed from: l */
            public final Exception f7936l;

            /* renamed from: m */
            public final byte[] f7937m;

            /* renamed from: n */
            public final Map f7938n;

            {
                this.f7934j = this;
                this.f7935k = i6;
                this.f7936l = exc;
                this.f7937m = bArr;
                this.f7938n = map;
            }

            @Override // java.lang.Runnable
            public final void run() {
                RunnableC1957o5 runnableC1957o5 = this.f7934j;
                runnableC1957o5.f7953m.mo2121c(runnableC1957o5.f7951k, this.f7935k, this.f7936l, this.f7937m, this.f7938n);
            }
        });
    }

    /* JADX WARN: Removed duplicated region for block: B:34:0x0071  */
    /* JADX WARN: Removed duplicated region for block: B:39:0x0063  */
    @Override // java.lang.Runnable
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void run() {
        /*
            r10 = this;
            x2.p5 r0 = r10.f7952l
            r0.mo4914g()
            r0 = 0
            r1 = 0
            x2.p5 r2 = r10.f7952l     // Catch: java.lang.Throwable -> L5c java.io.IOException -> L6a
            java.net.URL r3 = r10.f7950j     // Catch: java.lang.Throwable -> L5c java.io.IOException -> L6a
            java.net.HttpURLConnection r2 = r2.m4993o(r3)     // Catch: java.lang.Throwable -> L5c java.io.IOException -> L6a
            int r3 = r2.getResponseCode()     // Catch: java.lang.Throwable -> L50 java.io.IOException -> L56
            java.util.Map r4 = r2.getHeaderFields()     // Catch: java.lang.Throwable -> L4a java.io.IOException -> L4d
            java.io.ByteArrayOutputStream r5 = new java.io.ByteArrayOutputStream     // Catch: java.lang.Throwable -> L3e
            r5.<init>()     // Catch: java.lang.Throwable -> L3e
            java.io.InputStream r6 = r2.getInputStream()     // Catch: java.lang.Throwable -> L3e
            r7 = 1024(0x400, float:1.435E-42)
            byte[] r7 = new byte[r7]     // Catch: java.lang.Throwable -> L3c
        L24:
            int r8 = r6.read(r7)     // Catch: java.lang.Throwable -> L3c
            if (r8 <= 0) goto L2e
            r5.write(r7, r0, r8)     // Catch: java.lang.Throwable -> L3c
            goto L24
        L2e:
            byte[] r0 = r5.toByteArray()     // Catch: java.lang.Throwable -> L3c
            r6.close()     // Catch: java.lang.Throwable -> L46 java.io.IOException -> L48
            r2.disconnect()
            r10.m4992a(r3, r1, r0, r4)
            return
        L3c:
            r0 = move-exception
            goto L40
        L3e:
            r0 = move-exception
            r6 = r1
        L40:
            if (r6 == 0) goto L45
            r6.close()     // Catch: java.lang.Throwable -> L46 java.io.IOException -> L48
        L45:
            throw r0     // Catch: java.lang.Throwable -> L46 java.io.IOException -> L48
        L46:
            r0 = move-exception
            goto L61
        L48:
            r0 = move-exception
            goto L6f
        L4a:
            r0 = move-exception
            r4 = r1
            goto L61
        L4d:
            r0 = move-exception
            r4 = r1
            goto L6f
        L50:
            r3 = move-exception
            r4 = r1
            r9 = r3
            r3 = r0
            r0 = r9
            goto L61
        L56:
            r3 = move-exception
            r4 = r1
            r9 = r3
            r3 = r0
            r0 = r9
            goto L6f
        L5c:
            r2 = move-exception
            r3 = r0
            r4 = r1
            r0 = r2
            r2 = r4
        L61:
            if (r2 == 0) goto L66
            r2.disconnect()
        L66:
            r10.m4992a(r3, r1, r1, r4)
            throw r0
        L6a:
            r2 = move-exception
            r3 = r0
            r4 = r1
            r0 = r2
            r2 = r4
        L6f:
            if (r2 == 0) goto L74
            r2.disconnect()
        L74:
            r10.m4992a(r3, r0, r1, r4)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.RunnableC1957o5.run():void");
    }
}
