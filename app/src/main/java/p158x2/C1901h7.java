package p158x2;

import p152w2.C1771y0;

/* renamed from: x2.h7 */
/* loaded from: classes.dex */
public final class C1901h7 extends AbstractC1892g7 {

    /* renamed from: g */
    public final C1771y0 f7744g;

    /* renamed from: h */
    public final /* synthetic */ C1910i7 f7745h;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C1901h7(C1910i7 c1910i7, String str, int i6, C1771y0 c1771y0) {
        super(str, i6);
        this.f7745h = c1910i7;
        this.f7744g = c1771y0;
    }

    @Override // p158x2.AbstractC1892g7
    /* renamed from: a */
    public final int mo4810a() {
        return this.f7744g.m4431t();
    }

    @Override // p158x2.AbstractC1892g7
    /* renamed from: b */
    public final boolean mo4811b() {
        return true;
    }

    @Override // p158x2.AbstractC1892g7
    /* renamed from: c */
    public final boolean mo4812c() {
        return false;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:23:0x01aa  */
    /* JADX WARN: Removed duplicated region for block: B:26:0x01b5 A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:27:0x01b6  */
    /* JADX WARN: Removed duplicated region for block: B:56:0x01ad  */
    /* renamed from: i */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean m4847i(java.lang.Long r12, java.lang.Long r13, p152w2.C1508c2 r14, boolean r15) {
        /*
            Method dump skipped, instructions count: 534
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1901h7.m4847i(java.lang.Long, java.lang.Long, w2.c2, boolean):boolean");
    }
}
