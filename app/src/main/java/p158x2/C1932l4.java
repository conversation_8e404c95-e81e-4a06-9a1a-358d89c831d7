package p158x2;

import java.util.Objects;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.PriorityBlockingQueue;
import java.util.concurrent.Semaphore;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/* renamed from: x2.l4 */
/* loaded from: classes.dex */
public final class C1932l4 extends AbstractC2028x4 {

    /* renamed from: u */
    public static final AtomicLong f7833u = new AtomicLong(Long.MIN_VALUE);

    /* renamed from: m */
    public C1924k4 f7834m;

    /* renamed from: n */
    public C1924k4 f7835n;

    /* renamed from: o */
    public final PriorityBlockingQueue<C1916j4<?>> f7836o;

    /* renamed from: p */
    public final BlockingQueue<C1916j4<?>> f7837p;

    /* renamed from: q */
    public final C1907i4 f7838q;

    /* renamed from: r */
    public final C1907i4 f7839r;

    /* renamed from: s */
    public final Object f7840s;

    /* renamed from: t */
    public final Semaphore f7841t;

    public C1932l4(C1948n4 c1948n4) {
        super(c1948n4);
        this.f7840s = new Object();
        this.f7841t = new Semaphore(2);
        this.f7836o = new PriorityBlockingQueue<>();
        this.f7837p = new LinkedBlockingQueue();
        this.f7838q = new C1907i4(this, "Thread death: Uncaught exception on worker thread");
        this.f7839r = new C1907i4(this, "Thread death: Uncaught exception on network thread");
    }

    @Override // p158x2.C2020w4
    /* renamed from: g */
    public final void mo4914g() {
        if (Thread.currentThread() != this.f7835n) {
            throw new IllegalStateException("Call expected from network thread");
        }
    }

    @Override // p158x2.C2020w4
    /* renamed from: h */
    public final void mo4915h() {
        if (Thread.currentThread() != this.f7834m) {
            throw new IllegalStateException("Call expected from worker thread");
        }
    }

    @Override // p158x2.AbstractC2028x4
    /* renamed from: i */
    public final boolean mo4731i() {
        return false;
    }

    /* renamed from: o */
    public final boolean m4916o() {
        return Thread.currentThread() == this.f7834m;
    }

    /* renamed from: p */
    public final <V> Future<V> m4917p(Callable<V> callable) {
        m5153l();
        C1916j4<?> c1916j4 = new C1916j4<>(this, callable, false);
        if (Thread.currentThread() == this.f7834m) {
            if (!this.f7836o.isEmpty()) {
                ((C1948n4) this.f8145k).mo4962e().f7787s.m4841b("Callable skipped the worker queue.");
            }
            c1916j4.run();
        } else {
            m4922u(c1916j4);
        }
        return c1916j4;
    }

    /* renamed from: q */
    public final void m4918q(Runnable runnable) {
        m5153l();
        Objects.requireNonNull(runnable, "null reference");
        m4922u(new C1916j4<>(this, runnable, false, "Task exception on worker thread"));
    }

    /* renamed from: r */
    public final Object m4919r(AtomicReference atomicReference, String str, Runnable runnable) {
        synchronized (atomicReference) {
            ((C1948n4) this.f8145k).mo4959b().m4918q(runnable);
            try {
                atomicReference.wait(5000L);
            } catch (InterruptedException unused) {
                ((C1948n4) this.f8145k).mo4962e().f7787s.m4841b(str.length() != 0 ? "Interrupted waiting for ".concat(str) : new String("Interrupted waiting for "));
                return null;
            }
        }
        Object obj = atomicReference.get();
        if (obj == null) {
            ((C1948n4) this.f8145k).mo4962e().f7787s.m4841b(str.length() != 0 ? "Timed out waiting for ".concat(str) : new String("Timed out waiting for "));
        }
        return obj;
    }

    /* renamed from: s */
    public final void m4920s(Runnable runnable) {
        m5153l();
        m4922u(new C1916j4<>(this, runnable, true, "Task exception on worker thread"));
    }

    /* renamed from: t */
    public final void m4921t(Runnable runnable) {
        m5153l();
        C1916j4<?> c1916j4 = new C1916j4<>(this, runnable, false, "Task exception on network thread");
        synchronized (this.f7840s) {
            this.f7837p.add(c1916j4);
            C1924k4 c1924k4 = this.f7835n;
            if (c1924k4 == null) {
                C1924k4 c1924k42 = new C1924k4(this, "Measurement Network", this.f7837p);
                this.f7835n = c1924k42;
                c1924k42.setUncaughtExceptionHandler(this.f7839r);
                this.f7835n.start();
            } else {
                synchronized (c1924k4.f7819j) {
                    c1924k4.f7819j.notifyAll();
                }
            }
        }
    }

    /* renamed from: u */
    public final void m4922u(C1916j4<?> c1916j4) {
        synchronized (this.f7840s) {
            this.f7836o.add(c1916j4);
            C1924k4 c1924k4 = this.f7834m;
            if (c1924k4 == null) {
                C1924k4 c1924k42 = new C1924k4(this, "Measurement Worker", this.f7836o);
                this.f7834m = c1924k42;
                c1924k42.setUncaughtExceptionHandler(this.f7838q);
                this.f7834m.start();
            } else {
                synchronized (c1924k4.f7819j) {
                    c1924k4.f7819j.notifyAll();
                }
            }
        }
    }
}
