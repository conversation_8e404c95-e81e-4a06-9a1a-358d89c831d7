package p158x2;

import android.text.TextUtils;
import android.util.Log;
import java.util.Objects;
import org.checkerframework.checker.nullness.qual.EnsuresNonNull;
import p153w3.C1798e;

/* renamed from: x2.j3 */
/* loaded from: classes.dex */
public final class C1915j3 extends AbstractC2028x4 {

    /* renamed from: m */
    public char f7781m;

    /* renamed from: n */
    public long f7782n;

    /* renamed from: o */
    public String f7783o;

    /* renamed from: p */
    public final C1897h3 f7784p;

    /* renamed from: q */
    public final C1897h3 f7785q;

    /* renamed from: r */
    public final C1897h3 f7786r;

    /* renamed from: s */
    public final C1897h3 f7787s;

    /* renamed from: t */
    public final C1897h3 f7788t;

    /* renamed from: u */
    public final C1897h3 f7789u;

    /* renamed from: v */
    public final C1897h3 f7790v;

    /* renamed from: w */
    public final C1897h3 f7791w;

    /* renamed from: x */
    public final C1897h3 f7792x;

    public C1915j3(C1948n4 c1948n4) {
        super(c1948n4);
        this.f7781m = (char) 0;
        this.f7782n = -1L;
        this.f7784p = new C1897h3(this, 6, false, false);
        this.f7785q = new C1897h3(this, 6, true, false);
        this.f7786r = new C1897h3(this, 6, false, true);
        this.f7787s = new C1897h3(this, 5, false, false);
        this.f7788t = new C1897h3(this, 5, true, false);
        this.f7789u = new C1897h3(this, 5, false, true);
        this.f7790v = new C1897h3(this, 4, false, false);
        this.f7791w = new C1897h3(this, 3, false, false);
        this.f7792x = new C1897h3(this, 2, false, false);
    }

    /* renamed from: t */
    public static Object m4886t(String str) {
        if (str == null) {
            return null;
        }
        return new C1906i3(str);
    }

    /* renamed from: w */
    public static String m4887w(boolean z5, String str, Object obj, Object obj2, Object obj3) {
        String str2 = "";
        if (str == null) {
            str = "";
        }
        String m4888x = m4888x(z5, obj);
        String m4888x2 = m4888x(z5, obj2);
        String m4888x3 = m4888x(z5, obj3);
        StringBuilder sb = new StringBuilder();
        if (!TextUtils.isEmpty(str)) {
            sb.append(str);
            str2 = ": ";
        }
        String str3 = ", ";
        if (!TextUtils.isEmpty(m4888x)) {
            sb.append(str2);
            sb.append(m4888x);
            str2 = ", ";
        }
        if (TextUtils.isEmpty(m4888x2)) {
            str3 = str2;
        } else {
            sb.append(str2);
            sb.append(m4888x2);
        }
        if (!TextUtils.isEmpty(m4888x3)) {
            sb.append(str3);
            sb.append(m4888x3);
        }
        return sb.toString();
    }

    /* renamed from: x */
    public static String m4888x(boolean z5, Object obj) {
        String className;
        if (obj == null) {
            return "";
        }
        if (obj instanceof Integer) {
            obj = Long.valueOf(((Integer) obj).intValue());
        }
        int i6 = 0;
        if (obj instanceof Long) {
            if (!z5) {
                return String.valueOf(obj);
            }
            Long l6 = (Long) obj;
            if (Math.abs(l6.longValue()) < 100) {
                return String.valueOf(obj);
            }
            String str = String.valueOf(obj).charAt(0) == '-' ? "-" : "";
            String valueOf = String.valueOf(Math.abs(l6.longValue()));
            long round = Math.round(Math.pow(10.0d, valueOf.length() - 1));
            long round2 = Math.round(Math.pow(10.0d, valueOf.length()) - 1.0d);
            StringBuilder sb = new StringBuilder(str.length() + 43 + str.length());
            sb.append(str);
            sb.append(round);
            sb.append("...");
            sb.append(str);
            sb.append(round2);
            return sb.toString();
        }
        if (obj instanceof Boolean) {
            return String.valueOf(obj);
        }
        if (!(obj instanceof Throwable)) {
            return obj instanceof C1906i3 ? ((C1906i3) obj).f7759a : z5 ? "-" : String.valueOf(obj);
        }
        Throwable th = (Throwable) obj;
        StringBuilder sb2 = new StringBuilder(z5 ? th.getClass().getName() : th.toString());
        String m4889y = m4889y(C1948n4.class.getCanonicalName());
        StackTraceElement[] stackTrace = th.getStackTrace();
        int length = stackTrace.length;
        while (true) {
            if (i6 >= length) {
                break;
            }
            StackTraceElement stackTraceElement = stackTrace[i6];
            if (!stackTraceElement.isNativeMethod() && (className = stackTraceElement.getClassName()) != null && m4889y(className).equals(m4889y)) {
                sb2.append(": ");
                sb2.append(stackTraceElement);
                break;
            }
            i6++;
        }
        return sb2.toString();
    }

    /* renamed from: y */
    public static String m4889y(String str) {
        if (TextUtils.isEmpty(str)) {
            return "";
        }
        int lastIndexOf = str.lastIndexOf(46);
        return lastIndexOf == -1 ? str : str.substring(0, lastIndexOf);
    }

    @Override // p158x2.AbstractC2028x4
    /* renamed from: i */
    public final boolean mo4731i() {
        return false;
    }

    /* renamed from: o */
    public final C1897h3 m4890o() {
        return this.f7784p;
    }

    /* renamed from: p */
    public final C1897h3 m4891p() {
        return this.f7787s;
    }

    /* renamed from: q */
    public final C1897h3 m4892q() {
        return this.f7789u;
    }

    /* renamed from: r */
    public final C1897h3 m4893r() {
        return this.f7791w;
    }

    /* renamed from: s */
    public final C1897h3 m4894s() {
        return this.f7792x;
    }

    /* renamed from: u */
    public final void m4895u(int i6, boolean z5, boolean z6, String str, Object obj, Object obj2, Object obj3) {
        String m4896v;
        String str2;
        if (!z5 && Log.isLoggable(m4896v(), i6)) {
            Log.println(i6, m4896v(), m4887w(false, str, obj, obj2, obj3));
        }
        if (z6 || i6 < 5) {
            return;
        }
        Objects.requireNonNull(str, "null reference");
        C1932l4 c1932l4 = ((C1948n4) this.f8145k).f7926s;
        if (c1932l4 == null) {
            m4896v = m4896v();
            str2 = "Scheduler not set. Not logging error/warn";
        } else {
            if (c1932l4.m5152k()) {
                if (i6 >= 9) {
                    i6 = 8;
                }
                c1932l4.m4918q(new RunnableC1888g3(this, i6, str, obj, obj2, obj3));
                return;
            }
            m4896v = m4896v();
            str2 = "Scheduler not initialized. Not logging error/warn";
        }
        Log.println(6, m4896v, str2);
    }

    @EnsuresNonNull({"logTagDoNotUseDirectly"})
    /* renamed from: v */
    public final String m4896v() {
        String str;
        String str2;
        synchronized (this) {
            try {
                if (this.f7783o == null) {
                    Object obj = this.f8145k;
                    if (((C1948n4) obj).f7920m != null) {
                        str2 = ((C1948n4) obj).f7920m;
                    } else {
                        Objects.requireNonNull((C1948n4) ((C1948n4) obj).f7923p.f8145k);
                        str2 = "FA";
                    }
                    this.f7783o = str2;
                }
                C1798e.m4560r(this.f7783o);
                str = this.f7783o;
            } catch (Throwable th) {
                throw th;
            }
        }
        return str;
    }
}
