package p158x2;

import android.os.RemoteException;
import android.text.TextUtils;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import p153w3.C1798e;

/* renamed from: x2.z5 */
/* loaded from: classes.dex */
public final class RunnableC2045z5 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ AtomicReference f8278j;

    /* renamed from: k */
    public final /* synthetic */ String f8279k;

    /* renamed from: l */
    public final /* synthetic */ String f8280l;

    /* renamed from: m */
    public final /* synthetic */ C1847b7 f8281m;

    /* renamed from: n */
    public final /* synthetic */ boolean f8282n;

    /* renamed from: o */
    public final /* synthetic */ C1855c6 f8283o;

    public RunnableC2045z5(C1855c6 c1855c6, AtomicReference atomicReference, String str, String str2, C1847b7 c1847b7, boolean z5) {
        this.f8283o = c1855c6;
        this.f8278j = atomicReference;
        this.f8279k = str;
        this.f8280l = str2;
        this.f8281m = c1847b7;
        this.f8282n = z5;
    }

    @Override // java.lang.Runnable
    public final void run() {
        C1855c6 c1855c6;
        InterfaceC1834a3 interfaceC1834a3;
        AtomicReference atomicReference;
        List<C2022w6> mo4695z;
        synchronized (this.f8278j) {
            try {
                try {
                    c1855c6 = this.f8283o;
                    interfaceC1834a3 = c1855c6.f7620n;
                } catch (RemoteException e6) {
                    ((C1948n4) this.f8283o.f8145k).mo4962e().f7784p.m4844e("(legacy) Failed to get user properties; remote exception", null, this.f8279k, e6);
                    this.f8278j.set(Collections.emptyList());
                }
                if (interfaceC1834a3 == null) {
                    ((C1948n4) c1855c6.f8145k).mo4962e().f7784p.m4844e("(legacy) Failed to get user properties; not connected to service", null, this.f8279k, this.f8280l);
                    this.f8278j.set(Collections.emptyList());
                    return;
                }
                if (TextUtils.isEmpty(null)) {
                    C1798e.m4560r(this.f8281m);
                    atomicReference = this.f8278j;
                    mo4695z = interfaceC1834a3.mo4683d(this.f8279k, this.f8280l, this.f8282n, this.f8281m);
                } else {
                    atomicReference = this.f8278j;
                    mo4695z = interfaceC1834a3.mo4695z(null, this.f8279k, this.f8280l, this.f8282n);
                }
                atomicReference.set(mo4695z);
                this.f8283o.m4756r();
                this.f8278j.notify();
            } finally {
                this.f8278j.notify();
            }
        }
    }
}
