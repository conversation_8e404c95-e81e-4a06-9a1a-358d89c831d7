package p158x2;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import p099o2.C1147b;

/* renamed from: x2.p */
/* loaded from: classes.dex */
public final class C1959p implements Parcelable.Creator<C1951o> {
    @Override // android.os.Parcelable.Creator
    public final C1951o createFromParcel(Parcel parcel) {
        int m2977l = C1147b.m2977l(parcel);
        Bundle bundle = null;
        while (parcel.dataPosition() < m2977l) {
            int readInt = parcel.readInt();
            if ((65535 & readInt) != 2) {
                C1147b.m2976k(parcel, readInt);
            } else {
                bundle = C1147b.m2966a(parcel, readInt);
            }
        }
        C1147b.m2970e(parcel, m2977l);
        return new C1951o(bundle);
    }

    @Override // android.os.Parcelable.Creator
    public final /* bridge */ /* synthetic */ C1951o[] newArray(int i6) {
        return new C1951o[i6];
    }
}
