package p158x2;

import java.util.List;
import p153w3.C1798e;

/* renamed from: x2.b3 */
/* loaded from: classes.dex */
public final class C1843b3 extends AbstractC2011v3 {

    /* renamed from: m */
    public String f7574m;

    /* renamed from: n */
    public String f7575n;

    /* renamed from: o */
    public int f7576o;

    /* renamed from: p */
    public String f7577p;

    /* renamed from: q */
    public long f7578q;

    /* renamed from: r */
    public final long f7579r;

    /* renamed from: s */
    public List<String> f7580s;

    /* renamed from: t */
    public int f7581t;

    /* renamed from: u */
    public String f7582u;

    /* renamed from: v */
    public String f7583v;

    /* renamed from: w */
    public String f7584w;

    public C1843b3(C1948n4 c1948n4, long j6) {
        super(c1948n4);
        this.f7579r = j6;
    }

    @Override // p158x2.AbstractC2011v3
    /* renamed from: k */
    public final boolean mo4746k() {
        return true;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:11:0x00d7  */
    /* JADX WARN: Removed duplicated region for block: B:14:0x0115  */
    /* JADX WARN: Removed duplicated region for block: B:19:0x0133  */
    /* JADX WARN: Removed duplicated region for block: B:23:0x014b A[Catch: IllegalStateException -> 0x01d2, TRY_ENTER, TryCatch #0 {IllegalStateException -> 0x01d2, blocks: (B:17:0x011e, B:20:0x0136, B:23:0x014b, B:26:0x0168, B:29:0x0175, B:31:0x017d, B:34:0x01b3, B:36:0x01c9, B:37:0x01ce, B:39:0x01cc, B:75:0x0183, B:76:0x01af, B:77:0x0164, B:78:0x0188, B:80:0x018e, B:83:0x01ab, B:84:0x01a7), top: B:16:0x011e }] */
    /* JADX WARN: Removed duplicated region for block: B:34:0x01b3 A[Catch: IllegalStateException -> 0x01d2, TryCatch #0 {IllegalStateException -> 0x01d2, blocks: (B:17:0x011e, B:20:0x0136, B:23:0x014b, B:26:0x0168, B:29:0x0175, B:31:0x017d, B:34:0x01b3, B:36:0x01c9, B:37:0x01ce, B:39:0x01cc, B:75:0x0183, B:76:0x01af, B:77:0x0164, B:78:0x0188, B:80:0x018e, B:83:0x01ab, B:84:0x01a7), top: B:16:0x011e }] */
    /* JADX WARN: Removed duplicated region for block: B:42:0x0203  */
    /* JADX WARN: Removed duplicated region for block: B:46:0x0252  */
    /* JADX WARN: Removed duplicated region for block: B:50:0x028d  */
    /* JADX WARN: Removed duplicated region for block: B:53:0x029a  */
    /* JADX WARN: Removed duplicated region for block: B:64:0x0225 A[EXC_TOP_SPLITTER, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:72:0x0213  */
    /* JADX WARN: Removed duplicated region for block: B:78:0x0188 A[Catch: IllegalStateException -> 0x01d2, TryCatch #0 {IllegalStateException -> 0x01d2, blocks: (B:17:0x011e, B:20:0x0136, B:23:0x014b, B:26:0x0168, B:29:0x0175, B:31:0x017d, B:34:0x01b3, B:36:0x01c9, B:37:0x01ce, B:39:0x01cc, B:75:0x0183, B:76:0x01af, B:77:0x0164, B:78:0x0188, B:80:0x018e, B:83:0x01ab, B:84:0x01a7), top: B:16:0x011e }] */
    /* JADX WARN: Removed duplicated region for block: B:85:0x0135  */
    /* JADX WARN: Removed duplicated region for block: B:88:0x00dc  */
    /* JADX WARN: Removed duplicated region for block: B:89:0x00e1  */
    /* JADX WARN: Removed duplicated region for block: B:90:0x00e6  */
    /* JADX WARN: Removed duplicated region for block: B:91:0x00eb  */
    /* JADX WARN: Removed duplicated region for block: B:92:0x00f0  */
    /* JADX WARN: Removed duplicated region for block: B:93:0x00f5  */
    /* JADX WARN: Removed duplicated region for block: B:94:0x00fa  */
    /* JADX WARN: Removed duplicated region for block: B:95:0x00ff  */
    @org.checkerframework.checker.nullness.qual.EnsuresNonNull({"appId", "appStore", "appName", "gmpAppId", "gaAppId"})
    /* renamed from: l */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m4747l() {
        /*
            Method dump skipped, instructions count: 690
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1843b3.m4747l():void");
    }

    /* renamed from: m */
    public final String m4748m() {
        m5102i();
        C1798e.m4560r(this.f7574m);
        return this.f7574m;
    }

    /* renamed from: n */
    public final String m4749n() {
        m5102i();
        C1798e.m4560r(this.f7582u);
        return this.f7582u;
    }
}
