package p158x2;

import android.os.Bundle;
import android.util.Log;
import androidx.activity.result.C0052a;
import androidx.appcompat.widget.C0174y;
import java.util.ArrayList;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import p008b0.C0385m;
import p153w3.C1798e;

/* renamed from: x2.e3 */
/* loaded from: classes.dex */
public final class C1870e3 extends AbstractC2028x4 {

    /* renamed from: m */
    public static final AtomicReference<String[]> f7658m = new AtomicReference<>();

    /* renamed from: n */
    public static final AtomicReference<String[]> f7659n = new AtomicReference<>();

    /* renamed from: o */
    public static final AtomicReference<String[]> f7660o = new AtomicReference<>();

    public C1870e3(C1948n4 c1948n4) {
        super(c1948n4);
    }

    /* renamed from: u */
    public static final String m4792u(String str, String[] strArr, String[] strArr2, AtomicReference<String[]> atomicReference) {
        String str2;
        Objects.requireNonNull(atomicReference, "null reference");
        C1798e.m4550m(strArr.length == strArr2.length);
        for (int i6 = 0; i6 < strArr.length; i6++) {
            if (C1838a7.m4701G(str, strArr[i6])) {
                synchronized (atomicReference) {
                    String[] strArr3 = atomicReference.get();
                    if (strArr3 == null) {
                        strArr3 = new String[strArr2.length];
                        atomicReference.set(strArr3);
                    }
                    str2 = strArr3[i6];
                    if (str2 == null) {
                        str2 = strArr2[i6] + "(" + strArr[i6] + ")";
                        strArr3[i6] = str2;
                    }
                }
                return str2;
            }
        }
        return str;
    }

    @Override // p158x2.AbstractC2028x4
    /* renamed from: i */
    public final boolean mo4731i() {
        return false;
    }

    /* renamed from: o */
    public final boolean m4793o() {
        Objects.requireNonNull((C1948n4) this.f8145k);
        return ((C1948n4) this.f8145k).m4977x() && Log.isLoggable(((C1948n4) this.f8145k).mo4962e().m4896v(), 3);
    }

    /* renamed from: p */
    public final String m4794p(String str) {
        if (str == null) {
            return null;
        }
        return !m4793o() ? str : m4792u(str, C1798e.f7331J, C1798e.f7329H, f7658m);
    }

    /* renamed from: q */
    public final String m4795q(String str) {
        if (str == null) {
            return null;
        }
        return !m4793o() ? str : m4792u(str, C0385m.f2331V, C0385m.f2330U, f7659n);
    }

    /* renamed from: r */
    public final String m4796r(String str) {
        if (str == null) {
            return null;
        }
        return !m4793o() ? str : str.startsWith("_exp_") ? C0174y.m491i("experiment_id(", str, ")") : m4792u(str, C1798e.f7334M, C1798e.f7333L, f7660o);
    }

    /* renamed from: s */
    public final String m4797s(Bundle bundle) {
        if (bundle == null) {
            return null;
        }
        if (!m4793o()) {
            return bundle.toString();
        }
        StringBuilder m104h = C0052a.m104h("Bundle[{");
        for (String str : bundle.keySet()) {
            if (m104h.length() != 8) {
                m104h.append(", ");
            }
            m104h.append(m4795q(str));
            m104h.append("=");
            Object obj = bundle.get(str);
            m104h.append(obj instanceof Bundle ? m4798t(new Object[]{obj}) : obj instanceof Object[] ? m4798t((Object[]) obj) : obj instanceof ArrayList ? m4798t(((ArrayList) obj).toArray()) : String.valueOf(obj));
        }
        m104h.append("}]");
        return m104h.toString();
    }

    /* renamed from: t */
    public final String m4798t(Object[] objArr) {
        if (objArr == null) {
            return "[]";
        }
        StringBuilder m104h = C0052a.m104h("[");
        for (Object obj : objArr) {
            String m4797s = obj instanceof Bundle ? m4797s((Bundle) obj) : String.valueOf(obj);
            if (m4797s != null) {
                if (m104h.length() != 1) {
                    m104h.append(", ");
                }
                m104h.append(m4797s);
            }
        }
        m104h.append("]");
        return m104h.toString();
    }
}
