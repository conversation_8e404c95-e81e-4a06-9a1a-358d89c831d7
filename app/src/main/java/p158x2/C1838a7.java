package p158x2;

import android.content.ComponentName;
import android.content.Context;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.ServiceInfo;
import android.content.pm.Signature;
import android.net.Uri;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;
import android.util.Log;
import java.io.ByteArrayInputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Random;
import java.util.TreeSet;
import java.util.concurrent.atomic.AtomicLong;
import javax.security.auth.x500.X500Principal;
import org.checkerframework.checker.nullness.qual.EnsuresNonNull;
import org.litepal.crud.LitePalSupport;
import p008b0.C0385m;
import p073k2.C1011d;
import p073k2.C1013f;
import p118r2.C1306c;
import p152w2.C1489a9;
import p153w3.C1798e;

/* renamed from: x2.a7 */
/* loaded from: classes.dex */
public final class C1838a7 extends AbstractC2028x4 {

    /* renamed from: q */
    public static final String[] f7554q = {"firebase_", "google_", "ga_"};

    /* renamed from: r */
    public static final String[] f7555r = {"_err"};

    /* renamed from: m */
    public SecureRandom f7556m;

    /* renamed from: n */
    public final AtomicLong f7557n;

    /* renamed from: o */
    public int f7558o;

    /* renamed from: p */
    public Integer f7559p;

    public C1838a7(C1948n4 c1948n4) {
        super(c1948n4);
        this.f7559p = null;
        this.f7557n = new AtomicLong(0L);
    }

    /* renamed from: B */
    public static MessageDigest m4697B() {
        MessageDigest messageDigest;
        for (int i6 = 0; i6 < 2; i6++) {
            try {
                messageDigest = MessageDigest.getInstance(LitePalSupport.MD5);
            } catch (NoSuchAlgorithmException unused) {
            }
            if (messageDigest != null) {
                return messageDigest;
            }
        }
        return null;
    }

    /* renamed from: C */
    public static long m4698C(byte[] bArr) {
        Objects.requireNonNull(bArr, "null reference");
        int length = bArr.length;
        int i6 = 0;
        if (!(length > 0)) {
            throw new IllegalStateException();
        }
        long j6 = 0;
        for (int i7 = length - 1; i7 >= 0 && i7 >= bArr.length - 8; i7--) {
            j6 += (bArr[i7] & 255) << i6;
            i6 += 8;
        }
        return j6;
    }

    /* renamed from: D */
    public static boolean m4699D(Context context) {
        Objects.requireNonNull(context, "null reference");
        return m4704T(context);
    }

    /* renamed from: F */
    public static boolean m4700F(String str) {
        return !TextUtils.isEmpty(str) && str.startsWith("_");
    }

    /* renamed from: G */
    public static boolean m4701G(String str, String str2) {
        if (str == null && str2 == null) {
            return true;
        }
        if (str == null) {
            return false;
        }
        return str.equals(str2);
    }

    /* renamed from: P */
    public static boolean m4702P(Context context) {
        ActivityInfo receiverInfo;
        Objects.requireNonNull(context, "null reference");
        try {
            PackageManager packageManager = context.getPackageManager();
            if (packageManager != null && (receiverInfo = packageManager.getReceiverInfo(new ComponentName(context, "com.google.android.gms.measurement.AppMeasurementReceiver"), 0)) != null) {
                if (receiverInfo.enabled) {
                    return true;
                }
            }
        } catch (PackageManager.NameNotFoundException unused) {
        }
        return false;
    }

    /* renamed from: Q */
    public static final boolean m4703Q(Bundle bundle, int i6) {
        if (bundle.getLong("_err") != 0) {
            return false;
        }
        bundle.putLong("_err", i6);
        return true;
    }

    /* renamed from: T */
    public static boolean m4704T(Context context) {
        ServiceInfo serviceInfo;
        try {
            PackageManager packageManager = context.getPackageManager();
            if (packageManager != null && (serviceInfo = packageManager.getServiceInfo(new ComponentName(context, "com.google.android.gms.measurement.AppMeasurementJobService"), 0)) != null) {
                if (serviceInfo.enabled) {
                    return true;
                }
            }
        } catch (PackageManager.NameNotFoundException unused) {
        }
        return false;
    }

    /* renamed from: U */
    public static boolean m4705U(String str, String[] strArr) {
        Objects.requireNonNull(strArr, "null reference");
        for (String str2 : strArr) {
            if (m4701G(str, str2)) {
                return true;
            }
        }
        return false;
    }

    /* renamed from: X */
    public static boolean m4706X(String str) {
        C1798e.m4554o(str);
        return str.charAt(0) != '_' || str.equals("_ep");
    }

    /* renamed from: A */
    public final void m4707A(InterfaceC2046z6 interfaceC2046z6, String str, int i6, String str2, String str3, int i7, boolean z5) {
        Bundle bundle = new Bundle();
        m4703Q(bundle, i6);
        if (!TextUtils.isEmpty(str2) && !TextUtils.isEmpty(str3)) {
            bundle.putString(str2, str3);
        }
        if (i6 == 6 || i6 == 7 || i6 == 2) {
            bundle.putLong("_el", i7);
        }
        if (!z5) {
            Objects.requireNonNull((C1948n4) this.f8145k);
            ((C1948n4) this.f8145k).m4972s().m4930s("auto", "_err", bundle);
            return;
        }
        C2020w4 c2020w4 = (C2020w4) interfaceC2046z6;
        switch (c2020w4.f8144j) {
            case 1:
                if (!TextUtils.isEmpty(str)) {
                    throw new IllegalStateException("Unexpected call on client side");
                }
                ((C1933l5) c2020w4.f8145k).m4930s("auto", "_err", bundle);
                return;
            default:
                if (!TextUtils.isEmpty(str)) {
                    ((C1998t6) c2020w4.f8145k).mo4959b().m4918q(new RunnableC1982r6(c2020w4, str, bundle));
                    return;
                }
                C1948n4 c1948n4 = ((C1998t6) c2020w4.f8145k).f8092t;
                if (c1948n4 != null) {
                    c1948n4.mo4962e().f7784p.m4842c("AppId not known when logging event", "_err");
                    return;
                }
                return;
        }
    }

    /* renamed from: E */
    public final boolean m4708E(String str) {
        mo4915h();
        if (C1306c.m3210a(((C1948n4) this.f8145k).f7917j).f6085a.checkCallingOrSelfPermission(str) == 0) {
            return true;
        }
        ((C1948n4) this.f8145k).mo4962e().f7791w.m4842c("Permission not granted", str);
        return false;
    }

    /* renamed from: H */
    public final boolean m4709H(String str) {
        if (TextUtils.isEmpty(str)) {
            return false;
        }
        String m4777j = ((C1948n4) this.f8145k).f7923p.m4777j("debug.firebase.analytics.app");
        Objects.requireNonNull((C1948n4) this.f8145k);
        return m4777j.equals(str);
    }

    /* renamed from: I */
    public final Bundle m4710I(Bundle bundle) {
        Bundle bundle2 = new Bundle();
        if (bundle != null) {
            for (String str : bundle.keySet()) {
                Object m4738s = m4738s(str, bundle.get(str));
                if (m4738s == null) {
                    ((C1948n4) this.f8145k).mo4962e().f7789u.m4842c("Param value can't be null", ((C1948n4) this.f8145k).m4974u().m4795q(str));
                } else {
                    m4745z(bundle2, str, m4738s);
                }
            }
        }
        return bundle2;
    }

    /* renamed from: J */
    public final C1967q m4711J(String str, String str2, Bundle bundle, String str3, long j6, boolean z5) {
        if (TextUtils.isEmpty(str2)) {
            return null;
        }
        if (m4726d0(str2) != 0) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4842c("Invalid conditional property event name", ((C1948n4) this.f8145k).m4974u().m4796r(str2));
            throw new IllegalArgumentException();
        }
        Bundle bundle2 = bundle != null ? new Bundle(bundle) : new Bundle();
        bundle2.putString("_o", str3);
        Bundle m4739t = m4739t(str, str2, bundle2, Collections.singletonList("_o"), false);
        if (z5) {
            m4739t = m4710I(m4739t);
        }
        return new C1967q(str2, new C1951o(m4739t), str3, j6);
    }

    /* renamed from: K */
    public final boolean m4712K(Context context, String str) {
        C1897h3 c1897h3;
        String str2;
        Signature[] signatureArr;
        X500Principal x500Principal = new X500Principal("CN=Android Debug,O=Android,C=US");
        try {
            PackageInfo m3208b = C1306c.m3210a(context).m3208b(str, 64);
            if (m3208b == null || (signatureArr = m3208b.signatures) == null || signatureArr.length <= 0) {
                return true;
            }
            return ((X509Certificate) CertificateFactory.getInstance("X.509").generateCertificate(new ByteArrayInputStream(signatureArr[0].toByteArray()))).getSubjectX500Principal().equals(x500Principal);
        } catch (PackageManager.NameNotFoundException e6) {
            e = e6;
            c1897h3 = ((C1948n4) this.f8145k).mo4962e().f7784p;
            str2 = "Package name not found";
            c1897h3.m4842c(str2, e);
            return true;
        } catch (CertificateException e7) {
            e = e7;
            c1897h3 = ((C1948n4) this.f8145k).mo4962e().f7784p;
            str2 = "Error obtaining certificate";
            c1897h3.m4842c(str2, e);
            return true;
        }
    }

    /* renamed from: L */
    public final byte[] m4713L(Parcelable parcelable) {
        if (parcelable == null) {
            return null;
        }
        Parcel obtain = Parcel.obtain();
        try {
            parcelable.writeToParcel(obtain, 0);
            return obtain.marshall();
        } finally {
            obtain.recycle();
        }
    }

    @EnsuresNonNull({"this.apkVersion"})
    /* renamed from: M */
    public final int m4714M() {
        if (this.f7559p == null) {
            C1011d c1011d = C1011d.f4934b;
            Context context = ((C1948n4) this.f8145k).f7917j;
            Objects.requireNonNull(c1011d);
            boolean z5 = C1013f.f4935a;
            int i6 = 0;
            try {
                i6 = context.getPackageManager().getPackageInfo("com.google.android.gms", 0).versionCode;
            } catch (PackageManager.NameNotFoundException unused) {
                Log.w("GooglePlayServicesUtil", "Google Play services is missing.");
            }
            this.f7559p = Integer.valueOf(i6 / 1000);
        }
        return this.f7559p.intValue();
    }

    /* renamed from: N */
    public final long m4715N(long j6, long j7) {
        return ((j7 * 60000) + j6) / 86400000;
    }

    /* renamed from: O */
    public final void m4716O(Bundle bundle, long j6) {
        long j7 = bundle.getLong("_et");
        if (j7 != 0) {
            ((C1948n4) this.f8145k).mo4962e().f7787s.m4842c("Params already contained engagement", Long.valueOf(j7));
        }
        bundle.putLong("_et", j6 + j7);
    }

    /* renamed from: R */
    public final Object m4717R(int i6, Object obj, boolean z5, boolean z6) {
        if (obj == null) {
            return null;
        }
        if ((obj instanceof Long) || (obj instanceof Double)) {
            return obj;
        }
        if (obj instanceof Integer) {
            return Long.valueOf(((Integer) obj).intValue());
        }
        if (obj instanceof Byte) {
            return Long.valueOf(((Byte) obj).byteValue());
        }
        if (obj instanceof Short) {
            return Long.valueOf(((Short) obj).shortValue());
        }
        if (obj instanceof Boolean) {
            return Long.valueOf(true != ((Boolean) obj).booleanValue() ? 0L : 1L);
        }
        if (obj instanceof Float) {
            return Double.valueOf(((Float) obj).doubleValue());
        }
        if ((obj instanceof String) || (obj instanceof Character) || (obj instanceof CharSequence)) {
            return m4736q(String.valueOf(obj), i6, z5);
        }
        if (!z6 || (!(obj instanceof Bundle[]) && !(obj instanceof Parcelable[]))) {
            return null;
        }
        ArrayList arrayList = new ArrayList();
        for (Parcelable parcelable : (Parcelable[]) obj) {
            if (parcelable instanceof Bundle) {
                Bundle m4710I = m4710I((Bundle) parcelable);
                if (!m4710I.isEmpty()) {
                    arrayList.add(m4710I);
                }
            }
        }
        return arrayList.toArray(new Bundle[arrayList.size()]);
    }

    /* renamed from: S */
    public final int m4718S(String str) {
        if ("_ldl".equals(str)) {
            Objects.requireNonNull((C1948n4) this.f8145k);
            return 2048;
        }
        if ("_id".equals(str)) {
            Objects.requireNonNull((C1948n4) this.f8145k);
            return 256;
        }
        if (((C1948n4) this.f8145k).f7923p.m4784q(null, C2026x2.f8184Y) && "_lgclid".equals(str)) {
            Objects.requireNonNull((C1948n4) this.f8145k);
            return 100;
        }
        Objects.requireNonNull((C1948n4) this.f8145k);
        return 36;
    }

    /* renamed from: V */
    public final long m4719V() {
        long andIncrement;
        long j6;
        if (this.f7557n.get() != 0) {
            synchronized (this.f7557n) {
                this.f7557n.compareAndSet(-1L, 1L);
                andIncrement = this.f7557n.getAndIncrement();
            }
            return andIncrement;
        }
        synchronized (this.f7557n) {
            long nanoTime = System.nanoTime();
            Objects.requireNonNull(((C1948n4) this.f8145k).f7930w);
            long nextLong = new Random(nanoTime ^ System.currentTimeMillis()).nextLong();
            int i6 = this.f7558o + 1;
            this.f7558o = i6;
            j6 = nextLong + i6;
        }
        return j6;
    }

    @EnsuresNonNull({"this.secureRandom"})
    /* renamed from: W */
    public final SecureRandom m4720W() {
        mo4915h();
        if (this.f7556m == null) {
            this.f7556m = new SecureRandom();
        }
        return this.f7556m;
    }

    /* renamed from: Y */
    public final Bundle m4721Y(Uri uri) {
        String str;
        String str2;
        String str3;
        String str4;
        if (uri != null) {
            try {
                if (uri.isHierarchical()) {
                    str = uri.getQueryParameter("utm_campaign");
                    str2 = uri.getQueryParameter("utm_source");
                    str3 = uri.getQueryParameter("utm_medium");
                    str4 = uri.getQueryParameter("gclid");
                } else {
                    str = null;
                    str2 = null;
                    str3 = null;
                    str4 = null;
                }
                if (TextUtils.isEmpty(str) && TextUtils.isEmpty(str2) && TextUtils.isEmpty(str3) && TextUtils.isEmpty(str4)) {
                    return null;
                }
                Bundle bundle = new Bundle();
                if (!TextUtils.isEmpty(str)) {
                    bundle.putString("campaign", str);
                }
                if (!TextUtils.isEmpty(str2)) {
                    bundle.putString("source", str2);
                }
                if (!TextUtils.isEmpty(str3)) {
                    bundle.putString("medium", str3);
                }
                if (!TextUtils.isEmpty(str4)) {
                    bundle.putString("gclid", str4);
                }
                String queryParameter = uri.getQueryParameter("utm_term");
                if (!TextUtils.isEmpty(queryParameter)) {
                    bundle.putString("term", queryParameter);
                }
                String queryParameter2 = uri.getQueryParameter("utm_content");
                if (!TextUtils.isEmpty(queryParameter2)) {
                    bundle.putString("content", queryParameter2);
                }
                String queryParameter3 = uri.getQueryParameter("aclid");
                if (!TextUtils.isEmpty(queryParameter3)) {
                    bundle.putString("aclid", queryParameter3);
                }
                String queryParameter4 = uri.getQueryParameter("cp1");
                if (!TextUtils.isEmpty(queryParameter4)) {
                    bundle.putString("cp1", queryParameter4);
                }
                String queryParameter5 = uri.getQueryParameter("anid");
                if (!TextUtils.isEmpty(queryParameter5)) {
                    bundle.putString("anid", queryParameter5);
                }
                return bundle;
            } catch (UnsupportedOperationException e6) {
                ((C1948n4) this.f8145k).mo4962e().f7787s.m4842c("Install referrer url isn't a hierarchical URI", e6);
            }
        }
        return null;
    }

    /* renamed from: Z */
    public final boolean m4722Z(String str, String str2) {
        if (str2 == null) {
            ((C1948n4) this.f8145k).mo4962e().f7786r.m4842c("Name is required and can't be null. Type", str);
            return false;
        }
        if (str2.length() == 0) {
            ((C1948n4) this.f8145k).mo4962e().f7786r.m4842c("Name is required and can't be empty. Type", str);
            return false;
        }
        int codePointAt = str2.codePointAt(0);
        if (!Character.isLetter(codePointAt)) {
            ((C1948n4) this.f8145k).mo4962e().f7786r.m4843d("Name must start with a letter. Type, name", str, str2);
            return false;
        }
        int length = str2.length();
        int charCount = Character.charCount(codePointAt);
        while (charCount < length) {
            int codePointAt2 = str2.codePointAt(charCount);
            if (codePointAt2 != 95 && !Character.isLetterOrDigit(codePointAt2)) {
                ((C1948n4) this.f8145k).mo4962e().f7786r.m4843d("Name must consist of letters, digits or _ (underscores). Type, name", str, str2);
                return false;
            }
            charCount += Character.charCount(codePointAt2);
        }
        return true;
    }

    /* renamed from: a0 */
    public final boolean m4723a0(String str, String str2) {
        if (str2 == null) {
            ((C1948n4) this.f8145k).mo4962e().f7786r.m4842c("Name is required and can't be null. Type", str);
            return false;
        }
        if (str2.length() == 0) {
            ((C1948n4) this.f8145k).mo4962e().f7786r.m4842c("Name is required and can't be empty. Type", str);
            return false;
        }
        int codePointAt = str2.codePointAt(0);
        if (!Character.isLetter(codePointAt)) {
            if (codePointAt != 95) {
                ((C1948n4) this.f8145k).mo4962e().f7786r.m4843d("Name must start with a letter or _ (underscore). Type, name", str, str2);
                return false;
            }
            codePointAt = 95;
        }
        int length = str2.length();
        int charCount = Character.charCount(codePointAt);
        while (charCount < length) {
            int codePointAt2 = str2.codePointAt(charCount);
            if (codePointAt2 != 95 && !Character.isLetterOrDigit(codePointAt2)) {
                ((C1948n4) this.f8145k).mo4962e().f7786r.m4843d("Name must consist of letters, digits or _ (underscores). Type, name", str, str2);
                return false;
            }
            charCount += Character.charCount(codePointAt2);
        }
        return true;
    }

    /* renamed from: b0 */
    public final boolean m4724b0(String str, String[] strArr, String[] strArr2, String str2) {
        if (str2 == null) {
            ((C1948n4) this.f8145k).mo4962e().f7786r.m4842c("Name is required and can't be null. Type", str);
            return false;
        }
        String[] strArr3 = f7554q;
        for (int i6 = 0; i6 < 3; i6++) {
            if (str2.startsWith(strArr3[i6])) {
                ((C1948n4) this.f8145k).mo4962e().f7786r.m4843d("Name starts with reserved prefix. Type, name", str, str2);
                return false;
            }
        }
        if (strArr == null || !m4705U(str2, strArr)) {
            return true;
        }
        if (strArr2 != null && m4705U(str2, strArr2)) {
            return true;
        }
        ((C1948n4) this.f8145k).mo4962e().f7786r.m4843d("Name is reserved. Type, name", str, str2);
        return false;
    }

    /* renamed from: c0 */
    public final boolean m4725c0(String str, int i6, String str2) {
        if (str2 == null) {
            ((C1948n4) this.f8145k).mo4962e().f7786r.m4842c("Name is required and can't be null. Type", str);
            return false;
        }
        if (str2.codePointCount(0, str2.length()) <= i6) {
            return true;
        }
        ((C1948n4) this.f8145k).mo4962e().f7786r.m4844e("Name is too long. Type, maximum supported length, name", str, Integer.valueOf(i6), str2);
        return false;
    }

    /* renamed from: d0 */
    public final int m4726d0(String str) {
        if (!m4723a0("event", str)) {
            return 2;
        }
        if (!m4724b0("event", C1798e.f7329H, C1798e.f7330I, str)) {
            return 13;
        }
        Objects.requireNonNull((C1948n4) this.f8145k);
        return !m4725c0("event", 40, str) ? 2 : 0;
    }

    /* renamed from: e0 */
    public final int m4727e0(String str) {
        if (!m4723a0("user property", str)) {
            return 6;
        }
        if (!m4724b0("user property", C1798e.f7333L, null, str)) {
            return 15;
        }
        Objects.requireNonNull((C1948n4) this.f8145k);
        return !m4725c0("user property", 24, str) ? 6 : 0;
    }

    /* renamed from: f0 */
    public final int m4728f0(String str) {
        if (!m4722Z("event param", str)) {
            return 3;
        }
        if (!m4724b0("event param", null, null, str)) {
            return 14;
        }
        Objects.requireNonNull((C1948n4) this.f8145k);
        return !m4725c0("event param", 40, str) ? 3 : 0;
    }

    /* renamed from: g0 */
    public final int m4729g0(String str) {
        if (!m4723a0("event param", str)) {
            return 3;
        }
        if (!m4724b0("event param", null, null, str)) {
            return 14;
        }
        Objects.requireNonNull((C1948n4) this.f8145k);
        return !m4725c0("event param", 40, str) ? 3 : 0;
    }

    /* renamed from: h0 */
    public final boolean m4730h0(String str, String str2, int i6, Object obj) {
        if (obj != null && !(obj instanceof Long) && !(obj instanceof Float) && !(obj instanceof Integer) && !(obj instanceof Byte) && !(obj instanceof Short) && !(obj instanceof Boolean) && !(obj instanceof Double)) {
            if (!(obj instanceof String) && !(obj instanceof Character) && !(obj instanceof CharSequence)) {
                return false;
            }
            String valueOf = String.valueOf(obj);
            if (valueOf.codePointCount(0, valueOf.length()) > i6) {
                ((C1948n4) this.f8145k).mo4962e().f7789u.m4844e("Value is too long; discarded. Value kind, name, value length", str, str2, Integer.valueOf(valueOf.length()));
                return false;
            }
        }
        return true;
    }

    @Override // p158x2.AbstractC2028x4
    /* renamed from: i */
    public final boolean mo4731i() {
        return true;
    }

    /* renamed from: i0 */
    public final void m4732i0(String str, String str2, String str3, Bundle bundle, List<String> list, boolean z5) {
        int m4728f0;
        String str4;
        int m4737r;
        if (bundle == null) {
            return;
        }
        Objects.requireNonNull((C1948n4) this.f8145k);
        Iterator it = new TreeSet(bundle.keySet()).iterator();
        int i6 = 0;
        while (it.hasNext()) {
            String str5 = (String) it.next();
            if (list == null || !list.contains(str5)) {
                m4728f0 = z5 ? m4728f0(str5) : 0;
                if (m4728f0 == 0) {
                    m4728f0 = m4729g0(str5);
                }
            } else {
                m4728f0 = 0;
            }
            if (m4728f0 != 0) {
                m4742w(bundle, m4728f0, str5, m4728f0 == 3 ? str5 : null);
                bundle.remove(str5);
            } else {
                Object obj = bundle.get(str5);
                if ((obj instanceof Parcelable[]) || (obj instanceof ArrayList) || (obj instanceof Bundle)) {
                    ((C1948n4) this.f8145k).mo4962e().f7789u.m4844e("Nested Bundle parameters are not allowed; discarded. event name, param name, child param name", str2, str3, str5);
                    m4737r = 22;
                    str4 = str5;
                } else {
                    str4 = str5;
                    m4737r = m4737r(str, str2, str5, bundle.get(str5), bundle, list, z5, false);
                }
                if (m4737r != 0 && !"_ev".equals(str4)) {
                    m4742w(bundle, m4737r, str4, bundle.get(str4));
                } else if (m4706X(str4) && !m4705U(str4, C0385m.f2333X) && (i6 = i6 + 1) > 0) {
                    ((C1948n4) this.f8145k).mo4962e().f7786r.m4843d("Item cannot contain custom parameters", ((C1948n4) this.f8145k).m4974u().m4794p(str2), ((C1948n4) this.f8145k).m4974u().m4797s(bundle));
                    m4703Q(bundle, 23);
                }
                bundle.remove(str4);
            }
        }
    }

    @Override // p158x2.AbstractC2028x4
    /* renamed from: j */
    public final void mo4733j() {
        mo4915h();
        SecureRandom secureRandom = new SecureRandom();
        long nextLong = secureRandom.nextLong();
        if (nextLong == 0) {
            nextLong = secureRandom.nextLong();
            if (nextLong == 0) {
                ((C1948n4) this.f8145k).mo4962e().f7787s.m4841b("Utils falling back to Random for random id");
            }
        }
        this.f7557n.set(nextLong);
    }

    /* renamed from: o */
    public final boolean m4734o(String str, String str2, String str3) {
        if (!TextUtils.isEmpty(str)) {
            Objects.requireNonNull(str, "null reference");
            if (str.matches("^(1:\\d+:android:[a-f0-9]+|ca-app-pub-.*)$")) {
                return true;
            }
            if (((C1948n4) this.f8145k).m4977x()) {
                ((C1948n4) this.f8145k).mo4962e().f7786r.m4842c("Invalid google_app_id. Firebase Analytics disabled. See https://goo.gl/NAOOOI. provided id", C1915j3.m4886t(str));
            }
            return false;
        }
        C1489a9.m3647b();
        if (((C1948n4) this.f8145k).f7923p.m4784q(null, C2026x2.f8187a0) && !TextUtils.isEmpty(str3)) {
            return true;
        }
        if (TextUtils.isEmpty(str2)) {
            if (((C1948n4) this.f8145k).m4977x()) {
                ((C1948n4) this.f8145k).mo4962e().f7786r.m4841b("Missing google_app_id. Firebase Analytics disabled. See https://goo.gl/NAOOOI");
            }
            return false;
        }
        Objects.requireNonNull(str2, "null reference");
        if (str2.matches("^(1:\\d+:android:[a-f0-9]+|ca-app-pub-.*)$")) {
            return true;
        }
        ((C1948n4) this.f8145k).mo4962e().f7786r.m4842c("Invalid admob_app_id. Analytics disabled.", C1915j3.m4886t(str2));
        return false;
    }

    /* renamed from: p */
    public final boolean m4735p(String str, String str2, String str3, String str4) {
        boolean isEmpty = TextUtils.isEmpty(str);
        boolean isEmpty2 = TextUtils.isEmpty(str2);
        if (!isEmpty && !isEmpty2) {
            Objects.requireNonNull(str, "null reference");
            return !str.equals(str2);
        }
        if (isEmpty && isEmpty2) {
            return (TextUtils.isEmpty(str3) || TextUtils.isEmpty(str4)) ? !TextUtils.isEmpty(str4) : !str3.equals(str4);
        }
        if (isEmpty) {
            return TextUtils.isEmpty(str3) || !str3.equals(str4);
        }
        if (TextUtils.isEmpty(str4)) {
            return false;
        }
        return TextUtils.isEmpty(str3) || !str3.equals(str4);
    }

    /* renamed from: q */
    public final String m4736q(String str, int i6, boolean z5) {
        if (str == null) {
            return null;
        }
        if (str.codePointCount(0, str.length()) <= i6) {
            return str;
        }
        if (z5) {
            return String.valueOf(str.substring(0, str.offsetByCodePoints(0, i6))).concat("...");
        }
        return null;
    }

    /* JADX WARN: Removed duplicated region for block: B:38:0x00f3 A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:39:0x00f4  */
    /* renamed from: r */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final int m4737r(java.lang.String r15, java.lang.String r16, java.lang.String r17, java.lang.Object r18, android.os.Bundle r19, java.util.List<java.lang.String> r20, boolean r21, boolean r22) {
        /*
            Method dump skipped, instructions count: 388
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1838a7.m4737r(java.lang.String, java.lang.String, java.lang.String, java.lang.Object, android.os.Bundle, java.util.List, boolean, boolean):int");
    }

    /* renamed from: s */
    public final Object m4738s(String str, Object obj) {
        int i6 = 256;
        if ("_ev".equals(str)) {
            Objects.requireNonNull((C1948n4) this.f8145k);
            return m4717R(256, obj, true, true);
        }
        if (m4700F(str)) {
            Objects.requireNonNull((C1948n4) this.f8145k);
        } else {
            Objects.requireNonNull((C1948n4) this.f8145k);
            i6 = 100;
        }
        return m4717R(i6, obj, false, true);
    }

    /* renamed from: t */
    public final Bundle m4739t(String str, String str2, Bundle bundle, List<String> list, boolean z5) {
        int m4728f0;
        List<String> list2 = list;
        boolean m4705U = m4705U(str2, C1798e.f7332K);
        Bundle bundle2 = new Bundle(bundle);
        C1838a7 m4973t = ((C1948n4) ((C1948n4) this.f8145k).f7923p.f8145k).m4973t();
        Boolean bool = ((C1948n4) m4973t.f8145k).m4979z().f7621o;
        int i6 = (m4973t.m4714M() >= 201500 || !(bool == null || bool.booleanValue())) ? 100 : 25;
        int i7 = 0;
        for (String str3 : ((C1948n4) this.f8145k).f7923p.m4784q(str, C2026x2.f8176Q) ? new TreeSet<>(bundle.keySet()) : bundle.keySet()) {
            if (list2 == null || !list2.contains(str3)) {
                m4728f0 = z5 ? m4728f0(str3) : 0;
                if (m4728f0 == 0) {
                    m4728f0 = m4729g0(str3);
                }
            } else {
                m4728f0 = 0;
            }
            if (m4728f0 != 0) {
                m4742w(bundle2, m4728f0, str3, m4728f0 == 3 ? str3 : null);
                bundle2.remove(str3);
            } else {
                int m4737r = m4737r(str, str2, str3, bundle.get(str3), bundle2, list, z5, m4705U);
                if (m4737r == 17) {
                    m4742w(bundle2, 17, str3, Boolean.FALSE);
                } else if (m4737r != 0 && !"_ev".equals(str3)) {
                    m4742w(bundle2, m4737r, m4737r == 21 ? str2 : str3, bundle.get(str3));
                    bundle2.remove(str3);
                }
                if (m4706X(str3)) {
                    int i8 = i7 + 1;
                    if (i8 > i6) {
                        StringBuilder sb = new StringBuilder(48);
                        sb.append("Event can't contain more than ");
                        sb.append(i6);
                        sb.append(" params");
                        ((C1948n4) this.f8145k).mo4962e().f7786r.m4843d(sb.toString(), ((C1948n4) this.f8145k).m4974u().m4794p(str2), ((C1948n4) this.f8145k).m4974u().m4797s(bundle));
                        m4703Q(bundle2, 5);
                        bundle2.remove(str3);
                    }
                    list2 = list;
                    i7 = i8;
                }
            }
            list2 = list;
        }
        return bundle2;
    }

    /* renamed from: u */
    public final void m4740u(C1923k3 c1923k3, int i6) {
        Iterator it = new TreeSet(((Bundle) c1923k3.f7818e).keySet()).iterator();
        int i7 = 0;
        while (it.hasNext()) {
            String str = (String) it.next();
            if (m4706X(str) && (i7 = i7 + 1) > i6) {
                StringBuilder sb = new StringBuilder(48);
                sb.append("Event can't contain more than ");
                sb.append(i6);
                sb.append(" params");
                ((C1948n4) this.f8145k).mo4962e().f7786r.m4843d(sb.toString(), ((C1948n4) this.f8145k).m4974u().m4794p((String) c1923k3.f7816c), ((C1948n4) this.f8145k).m4974u().m4797s((Bundle) c1923k3.f7818e));
                m4703Q((Bundle) c1923k3.f7818e, 5);
                ((Bundle) c1923k3.f7818e).remove(str);
            }
        }
    }

    /* renamed from: v */
    public final void m4741v(Bundle bundle, Bundle bundle2) {
        if (bundle2 == null) {
            return;
        }
        for (String str : bundle2.keySet()) {
            if (!bundle.containsKey(str)) {
                ((C1948n4) this.f8145k).m4973t().m4745z(bundle, str, bundle2.get(str));
            }
        }
    }

    /* renamed from: w */
    public final void m4742w(Bundle bundle, int i6, String str, Object obj) {
        if (m4703Q(bundle, i6)) {
            Objects.requireNonNull((C1948n4) this.f8145k);
            bundle.putString("_ev", m4736q(str, 40, true));
            if (obj != null) {
                if ((obj instanceof String) || (obj instanceof CharSequence)) {
                    bundle.putLong("_el", String.valueOf(obj).length());
                }
            }
        }
    }

    /* renamed from: x */
    public final int m4743x(String str, Object obj) {
        int m4718S;
        String str2;
        if ("_ldl".equals(str)) {
            m4718S = m4718S(str);
            str2 = "user property referrer";
        } else {
            m4718S = m4718S(str);
            str2 = "user property";
        }
        return m4730h0(str2, str, m4718S, obj) ? 0 : 7;
    }

    /* renamed from: y */
    public final Object m4744y(String str, Object obj) {
        boolean equals = "_ldl".equals(str);
        int m4718S = m4718S(str);
        return equals ? m4717R(m4718S, obj, true, false) : m4717R(m4718S, obj, false, false);
    }

    /* renamed from: z */
    public final void m4745z(Bundle bundle, String str, Object obj) {
        if (bundle == null) {
            return;
        }
        if (obj instanceof Long) {
            bundle.putLong(str, ((Long) obj).longValue());
            return;
        }
        if (obj instanceof String) {
            bundle.putString(str, String.valueOf(obj));
            return;
        }
        if (obj instanceof Double) {
            bundle.putDouble(str, ((Double) obj).doubleValue());
        } else if (obj instanceof Bundle[]) {
            bundle.putParcelableArray(str, (Bundle[]) obj);
        } else if (str != null) {
            ((C1948n4) this.f8145k).mo4962e().f7789u.m4843d("Not putting event parameter. Invalid value type. name, type", ((C1948n4) this.f8145k).m4974u().m4795q(str), obj != null ? obj.getClass().getSimpleName() : null);
        }
    }
}
