package p158x2;

import android.content.Context;
import android.os.Binder;
import android.os.Bundle;
import android.text.TextUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import p073k2.C1013f;
import p073k2.C1014g;
import p113q2.C1279g;
import p153w3.C1798e;

/* renamed from: x2.v4 */
/* loaded from: classes.dex */
public final class BinderC2012v4 extends AbstractBinderC2042z2 {

    /* renamed from: a */
    public final C1998t6 f8129a;

    /* renamed from: b */
    public Boolean f8130b;

    /* renamed from: c */
    public String f8131c;

    public BinderC2012v4(C1998t6 c1998t6) {
        Objects.requireNonNull(c1998t6, "null reference");
        this.f8129a = c1998t6;
        this.f8131c = null;
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: A */
    public final void mo4682A(Bundle bundle, C1847b7 c1847b7) {
        m5104C(c1847b7);
        String str = c1847b7.f7595j;
        C1798e.m4560r(str);
        m5106E(new RunnableC1956o4(this, str, bundle, 0));
    }

    /* renamed from: C */
    public final void m5104C(C1847b7 c1847b7) {
        Objects.requireNonNull(c1847b7, "null reference");
        C1798e.m4554o(c1847b7.f7595j);
        m5105D(c1847b7.f7595j, false);
        this.f8129a.m5073K().m4734o(c1847b7.f7596k, c1847b7.f7611z, c1847b7.f7593D);
    }

    /* renamed from: D */
    public final void m5105D(String str, boolean z5) {
        boolean z6;
        if (TextUtils.isEmpty(str)) {
            this.f8129a.mo4962e().f7784p.m4841b("Measurement Service called without app package");
            throw new SecurityException("Measurement Service called without app package");
        }
        if (z5) {
            try {
                if (this.f8130b == null) {
                    if (!"com.google.android.gms".equals(this.f8131c) && !C1279g.m3167a(this.f8129a.f8092t.f7917j, Binder.getCallingUid()) && !C1014g.m2607a(this.f8129a.f8092t.f7917j).m2610b(Binder.getCallingUid())) {
                        z6 = false;
                        this.f8130b = Boolean.valueOf(z6);
                    }
                    z6 = true;
                    this.f8130b = Boolean.valueOf(z6);
                }
                if (this.f8130b.booleanValue()) {
                    return;
                }
            } catch (SecurityException e6) {
                this.f8129a.mo4962e().f7784p.m4842c("Measurement Service called with invalid calling package. appId", C1915j3.m4886t(str));
                throw e6;
            }
        }
        if (this.f8131c == null) {
            Context context = this.f8129a.f8092t.f7917j;
            int callingUid = Binder.getCallingUid();
            boolean z7 = C1013f.f4935a;
            if (C1279g.m3168b(context, callingUid, str)) {
                this.f8131c = str;
            }
        }
        if (str.equals(this.f8131c)) {
        } else {
            throw new SecurityException(String.format("Unknown calling package name '%s'.", str));
        }
    }

    /* renamed from: E */
    public final void m5106E(Runnable runnable) {
        if (this.f8129a.mo4959b().m4916o()) {
            runnable.run();
        } else {
            this.f8129a.mo4959b().m4918q(runnable);
        }
    }

    /* renamed from: F */
    public final void m5107F(C1967q c1967q, C1847b7 c1847b7) {
        this.f8129a.m5086j();
        this.f8129a.m5080R(c1967q, c1847b7);
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: d */
    public final List<C2022w6> mo4683d(String str, String str2, boolean z5, C1847b7 c1847b7) {
        m5104C(c1847b7);
        String str3 = c1847b7.f7595j;
        C1798e.m4560r(str3);
        try {
            List<C2038y6> list = (List) ((FutureTask) this.f8129a.mo4959b().m4917p(new CallableC1964p4(this, str3, str, str2, 0))).get();
            ArrayList arrayList = new ArrayList(list.size());
            for (C2038y6 c2038y6 : list) {
                if (z5 || !C1838a7.m4700F(c2038y6.f8270c)) {
                    arrayList.add(new C2022w6(c2038y6));
                }
            }
            return arrayList;
        } catch (InterruptedException | ExecutionException e6) {
            this.f8129a.mo4962e().f7784p.m4843d("Failed to query user properties. appId", C1915j3.m4886t(c1847b7.f7595j), e6);
            return Collections.emptyList();
        }
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: f */
    public final void mo4684f(C1847b7 c1847b7) {
        C1798e.m4554o(c1847b7.f7595j);
        m5105D(c1847b7.f7595j, false);
        m5106E(new RunnableC1988s4(this, c1847b7, 0));
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: g */
    public final void mo4685g(C1839b c1839b, C1847b7 c1847b7) {
        Objects.requireNonNull(c1839b, "null reference");
        C1798e.m4560r(c1839b.f7562l);
        m5104C(c1847b7);
        C1839b c1839b2 = new C1839b(c1839b);
        c1839b2.f7560j = c1847b7.f7595j;
        m5106E(new RunnableC2035y3(this, c1839b2, c1847b7, 1));
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: k */
    public final void mo4686k(C1847b7 c1847b7) {
        m5104C(c1847b7);
        m5106E(new RunnableC1940m4(this, c1847b7, 1));
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: l */
    public final void mo4687l(long j6, String str, String str2, String str3) {
        m5106E(new RunnableC2004u4(this, str2, str3, str, j6, 0));
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: m */
    public final void mo4688m(C1847b7 c1847b7) {
        C1798e.m4554o(c1847b7.f7595j);
        C1798e.m4560r(c1847b7.f7594E);
        RunnableC1988s4 runnableC1988s4 = new RunnableC1988s4(this, c1847b7, 1);
        if (this.f8129a.mo4959b().m4916o()) {
            runnableC1988s4.run();
        } else {
            this.f8129a.mo4959b().m4920s(runnableC1988s4);
        }
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: o */
    public final void mo4689o(C1847b7 c1847b7) {
        m5104C(c1847b7);
        m5106E(new RunnableC1988s4(this, c1847b7, 2));
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: p */
    public final List<C1839b> mo4690p(String str, String str2, C1847b7 c1847b7) {
        m5104C(c1847b7);
        String str3 = c1847b7.f7595j;
        C1798e.m4560r(str3);
        try {
            return (List) ((FutureTask) this.f8129a.mo4959b().m4917p(new CallableC1972q4(this, str3, str, str2, 1))).get();
        } catch (InterruptedException | ExecutionException e6) {
            this.f8129a.mo4962e().f7784p.m4842c("Failed to get conditional user properties", e6);
            return Collections.emptyList();
        }
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: t */
    public final void mo4691t(C2022w6 c2022w6, C1847b7 c1847b7) {
        Objects.requireNonNull(c2022w6, "null reference");
        m5104C(c1847b7);
        m5106E(new RunnableC1956o4(this, c2022w6, c1847b7, 2));
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: u */
    public final List<C1839b> mo4692u(String str, String str2, String str3) {
        m5105D(str, true);
        try {
            return (List) ((FutureTask) this.f8129a.mo4959b().m4917p(new CallableC1964p4(this, str, str2, str3, 1))).get();
        } catch (InterruptedException | ExecutionException e6) {
            this.f8129a.mo4962e().f7784p.m4842c("Failed to get conditional user properties as", e6);
            return Collections.emptyList();
        }
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: v */
    public final String mo4693v(C1847b7 c1847b7) {
        m5104C(c1847b7);
        C1998t6 c1998t6 = this.f8129a;
        try {
            return (String) ((FutureTask) c1998t6.mo4959b().m4917p(new CallableC1862d4(c1998t6, c1847b7))).get(30000L, TimeUnit.MILLISECONDS);
        } catch (InterruptedException | ExecutionException | TimeoutException e6) {
            c1998t6.mo4962e().f7784p.m4843d("Failed to get app instance id. appId", C1915j3.m4886t(c1847b7.f7595j), e6);
            return null;
        }
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: y */
    public final void mo4694y(C1967q c1967q, C1847b7 c1847b7) {
        Objects.requireNonNull(c1967q, "null reference");
        m5104C(c1847b7);
        m5106E(new RunnableC2035y3(this, c1967q, c1847b7, 2));
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: z */
    public final List<C2022w6> mo4695z(String str, String str2, String str3, boolean z5) {
        m5105D(str, true);
        try {
            List<C2038y6> list = (List) ((FutureTask) this.f8129a.mo4959b().m4917p(new CallableC1972q4(this, str, str2, str3, 0))).get();
            ArrayList arrayList = new ArrayList(list.size());
            for (C2038y6 c2038y6 : list) {
                if (z5 || !C1838a7.m4700F(c2038y6.f8270c)) {
                    arrayList.add(new C2022w6(c2038y6));
                }
            }
            return arrayList;
        } catch (InterruptedException | ExecutionException e6) {
            this.f8129a.mo4962e().f7784p.m4843d("Failed to get user properties as. appId", C1915j3.m4886t(str), e6);
            return Collections.emptyList();
        }
    }
}
