package p158x2;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/* renamed from: x2.m3 */
/* loaded from: classes.dex */
public final class RunnableC1939m3 implements Runnable {

    /* renamed from: j */
    public final InterfaceC1931l3 f7868j;

    /* renamed from: k */
    public final int f7869k;

    /* renamed from: l */
    public final Throwable f7870l;

    /* renamed from: m */
    public final byte[] f7871m;

    /* renamed from: n */
    public final String f7872n;

    /* renamed from: o */
    public final Map<String, List<String>> f7873o;

    public RunnableC1939m3(String str, InterfaceC1931l3 interfaceC1931l3, int i6, Throwable th, byte[] bArr, Map map) {
        Objects.requireNonNull(interfaceC1931l3, "null reference");
        this.f7868j = interfaceC1931l3;
        this.f7869k = i6;
        this.f7870l = th;
        this.f7871m = bArr;
        this.f7872n = str;
        this.f7873o = map;
    }

    @Override // java.lang.Runnable
    public final void run() {
        this.f7868j.mo2121c(this.f7872n, this.f7869k, this.f7870l, this.f7871m, this.f7873o);
    }
}
