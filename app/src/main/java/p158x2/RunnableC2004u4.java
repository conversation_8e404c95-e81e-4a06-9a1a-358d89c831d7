package p158x2;

import android.os.Bundle;

/* renamed from: x2.u4 */
/* loaded from: classes.dex */
public final class RunnableC2004u4 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f8107j;

    /* renamed from: k */
    public final /* synthetic */ Object f8108k;

    /* renamed from: l */
    public final /* synthetic */ Object f8109l;

    /* renamed from: m */
    public final /* synthetic */ long f8110m;

    /* renamed from: n */
    public final /* synthetic */ Object f8111n;

    /* renamed from: o */
    public final /* synthetic */ Object f8112o;

    public /* synthetic */ RunnableC2004u4(Object obj, Object obj2, Object obj3, Object obj4, long j6, int i6) {
        this.f8107j = i6;
        this.f8112o = obj;
        this.f8108k = obj2;
        this.f8109l = obj3;
        this.f8111n = obj4;
        this.f8110m = j6;
    }

    @Override // java.lang.Runnable
    public final void run() {
        switch (this.f8107j) {
            case 0:
                if (((String) this.f8108k) == null) {
                    ((BinderC2012v4) this.f8112o).f8129a.f8092t.m4978y().m5058r((String) this.f8109l);
                    return;
                }
                C1997t5 m4978y = ((BinderC2012v4) this.f8112o).f8129a.f8092t.m4978y();
                String str = (String) this.f8109l;
                m4978y.mo4915h();
                synchronized (m4978y) {
                    String str2 = m4978y.f8070w;
                    if (str2 != null) {
                        str2.equals(str);
                    }
                    m4978y.f8070w = str;
                }
                return;
            case 1:
                ((C1933l5) this.f8112o).m4925m((String) this.f8108k, (String) this.f8109l, this.f8111n, this.f8110m);
                return;
            default:
                C1997t5 c1997t5 = (C1997t5) this.f8112o;
                Bundle bundle = (Bundle) this.f8108k;
                C1981r5 c1981r5 = (C1981r5) this.f8109l;
                C1981r5 c1981r52 = (C1981r5) this.f8111n;
                long j6 = this.f8110m;
                bundle.remove("screen_name");
                bundle.remove("screen_class");
                c1997t5.m5053l(c1981r5, c1981r52, j6, true, ((C1948n4) c1997t5.f8145k).m4973t().m4739t(null, "screen_view", bundle, null, true));
                return;
        }
    }
}
