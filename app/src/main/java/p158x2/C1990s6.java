package p158x2;

import java.util.ArrayList;
import java.util.List;
import p152w2.C1628m1;
import p152w2.C1712t1;

/* renamed from: x2.s6 */
/* loaded from: classes.dex */
public final class C1990s6 {

    /* renamed from: a */
    public C1712t1 f8045a;

    /* renamed from: b */
    public List<Long> f8046b;

    /* renamed from: c */
    public List<C1628m1> f8047c;

    /* renamed from: d */
    public long f8048d;

    /* renamed from: e */
    public final /* synthetic */ C1998t6 f8049e;

    public /* synthetic */ C1990s6(C1998t6 c1998t6) {
        this.f8049e = c1998t6;
    }

    /* JADX WARN: Type inference failed for: r0v12, types: [java.util.ArrayList, java.util.List<w2.m1>] */
    /* JADX WARN: Type inference failed for: r0v13, types: [java.util.ArrayList, java.util.List<w2.m1>] */
    /* JADX WARN: Type inference failed for: r0v2, types: [java.util.ArrayList, java.util.List<w2.m1>] */
    /* JADX WARN: Type inference failed for: r11v2, types: [java.util.ArrayList, java.util.List<w2.m1>] */
    /* JADX WARN: Type inference failed for: r13v1, types: [java.util.ArrayList, java.util.List<java.lang.Long>] */
    /* renamed from: a */
    public final boolean m5049a(long j6, C1628m1 c1628m1) {
        if (this.f8047c == null) {
            this.f8047c = new ArrayList();
        }
        if (this.f8046b == null) {
            this.f8046b = new ArrayList();
        }
        if (this.f8047c.size() > 0 && ((((C1628m1) this.f8047c.get(0)).m3929x() / 1000) / 60) / 60 != ((c1628m1.m3929x() / 1000) / 60) / 60) {
            return false;
        }
        long mo4062b = this.f8048d + c1628m1.mo4062b();
        this.f8049e.m5068F();
        if (mo4062b >= Math.max(0, C2026x2.f8202i.m5101a(null).intValue())) {
            return false;
        }
        this.f8048d = mo4062b;
        this.f8047c.add(c1628m1);
        this.f8046b.add(Long.valueOf(j6));
        int size = this.f8047c.size();
        this.f8049e.m5068F();
        return size < Math.max(1, C2026x2.f8204j.m5101a(null).intValue());
    }
}
