package p158x2;

import android.os.Bundle;
import p090n.C1094g;

/* renamed from: x2.k3 */
/* loaded from: classes.dex */
public final class C1923k3 {

    /* renamed from: a */
    public final /* synthetic */ int f7814a = 0;

    /* renamed from: b */
    public long f7815b;

    /* renamed from: c */
    public Object f7816c;

    /* renamed from: d */
    public Object f7817d;

    /* renamed from: e */
    public final Object f7818e;

    public C1923k3(String str, String str2, Bundle bundle, long j6) {
        this.f7816c = str;
        this.f7817d = str2;
        this.f7818e = bundle;
        this.f7815b = j6;
    }

    /* renamed from: b */
    public static C1923k3 m4902b(C1967q c1967q) {
        return new C1923k3(c1967q.f7965j, c1967q.f7967l, c1967q.f7966k.m4989u(), c1967q.f7968m);
    }

    /* JADX WARN: Code restructure failed: missing block: B:67:0x0106, code lost:
    
        if (r14 == null) goto L38;
     */
    /* JADX WARN: Not initialized variable reg: 14, insn: 0x0200: MOVE (r5 I:??[OBJECT, ARRAY]) = (r14 I:??[OBJECT, ARRAY]), block:B:70:0x0200 */
    /* JADX WARN: Removed duplicated region for block: B:72:0x0203  */
    /* renamed from: a */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final p152w2.C1628m1 m4903a(java.lang.String r18, p152w2.C1628m1 r19) {
        /*
            Method dump skipped, instructions count: 647
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1923k3.m4903a(java.lang.String, w2.m1):w2.m1");
    }

    /* renamed from: c */
    public final C1967q m4904c() {
        return new C1967q((String) this.f7816c, new C1951o(new Bundle((Bundle) this.f7818e)), (String) this.f7817d, this.f7815b);
    }

    public final String toString() {
        switch (this.f7814a) {
            case 0:
                String str = (String) this.f7817d;
                String str2 = (String) this.f7816c;
                String valueOf = String.valueOf((Bundle) this.f7818e);
                int length = String.valueOf(str).length();
                StringBuilder sb = new StringBuilder(length + 21 + String.valueOf(str2).length() + valueOf.length());
                sb.append("origin=");
                sb.append(str);
                sb.append(",name=");
                sb.append(str2);
                return C1094g.m2839c(sb, ",params=", valueOf);
            default:
                return super.toString();
        }
    }
}
