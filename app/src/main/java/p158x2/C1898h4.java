package p158x2;

import android.content.Context;
import android.content.res.Resources;
import com.liaoyuan.aicast.R;

/* renamed from: x2.h4 */
/* loaded from: classes.dex */
public final class C1898h4 {
    /* renamed from: a */
    public static String m4845a(Context context) {
        try {
            return context.getResources().getResourcePackageName(R.string.common_google_play_services_unknown_issue);
        } catch (Resources.NotFoundException unused) {
            return context.getPackageName();
        }
    }

    /* renamed from: b */
    public static final String m4846b(String str, Resources resources, String str2) {
        int identifier = resources.getIdentifier(str, "string", str2);
        if (identifier != 0) {
            try {
                return resources.getString(identifier);
            } catch (Resources.NotFoundException unused) {
            }
        }
        return null;
    }
}
