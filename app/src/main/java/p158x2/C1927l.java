package p158x2;

import android.accounts.Account;
import android.accounts.AccountManager;
import android.accounts.AuthenticatorException;
import android.accounts.OperationCanceledException;
import java.io.IOException;
import java.util.Calendar;
import java.util.Locale;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import p142v.C1450a;

/* renamed from: x2.l */
/* loaded from: classes.dex */
public final class C1927l extends AbstractC2028x4 {

    /* renamed from: m */
    public long f7825m;

    /* renamed from: n */
    public String f7826n;

    /* renamed from: o */
    public AccountManager f7827o;

    /* renamed from: p */
    public Boolean f7828p;

    /* renamed from: q */
    public long f7829q;

    public C1927l(C1948n4 c1948n4) {
        super(c1948n4);
    }

    @Override // p158x2.AbstractC2028x4
    /* renamed from: i */
    public final boolean mo4731i() {
        Calendar calendar = Calendar.getInstance();
        this.f7825m = TimeUnit.MINUTES.convert(calendar.get(16) + calendar.get(15), TimeUnit.MILLISECONDS);
        Locale locale = Locale.getDefault();
        String language = locale.getLanguage();
        Locale locale2 = Locale.ENGLISH;
        String lowerCase = language.toLowerCase(locale2);
        String lowerCase2 = locale.getCountry().toLowerCase(locale2);
        StringBuilder sb = new StringBuilder(String.valueOf(lowerCase).length() + 1 + String.valueOf(lowerCase2).length());
        sb.append(lowerCase);
        sb.append("-");
        sb.append(lowerCase2);
        this.f7826n = sb.toString();
        return false;
    }

    /* renamed from: o */
    public final long m4910o() {
        m5153l();
        return this.f7825m;
    }

    /* renamed from: p */
    public final String m4911p() {
        m5153l();
        return this.f7826n;
    }

    /* renamed from: q */
    public final long m4912q() {
        mo4915h();
        return this.f7829q;
    }

    /* renamed from: r */
    public final boolean m4913r() {
        mo4915h();
        Objects.requireNonNull(((C1948n4) this.f8145k).f7930w);
        long currentTimeMillis = System.currentTimeMillis();
        if (currentTimeMillis - this.f7829q > ********) {
            this.f7828p = null;
        }
        Boolean bool = this.f7828p;
        if (bool != null) {
            return bool.booleanValue();
        }
        if (C1450a.m3520a(((C1948n4) this.f8145k).f7917j, "android.permission.GET_ACCOUNTS") != 0) {
            ((C1948n4) this.f8145k).mo4962e().f7788t.m4841b("Permission error checking for dasher/unicorn accounts");
        } else {
            if (this.f7827o == null) {
                this.f7827o = AccountManager.get(((C1948n4) this.f8145k).f7917j);
            }
            try {
                Account[] result = this.f7827o.getAccountsByTypeAndFeatures("com.google", new String[]{"service_HOSTED"}, null, null).getResult();
                if (result != null && result.length > 0) {
                    this.f7828p = Boolean.TRUE;
                    this.f7829q = currentTimeMillis;
                    return true;
                }
                Account[] result2 = this.f7827o.getAccountsByTypeAndFeatures("com.google", new String[]{"service_uca"}, null, null).getResult();
                if (result2 != null && result2.length > 0) {
                    this.f7828p = Boolean.TRUE;
                    this.f7829q = currentTimeMillis;
                    return true;
                }
            } catch (AuthenticatorException | OperationCanceledException | IOException e6) {
                ((C1948n4) this.f8145k).mo4962e().f7785q.m4842c("Exception checking account types", e6);
            }
        }
        this.f7829q = currentTimeMillis;
        this.f7828p = Boolean.FALSE;
        return false;
    }
}
