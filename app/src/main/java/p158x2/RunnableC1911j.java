package p158x2;

import android.os.Bundle;
import android.os.RemoteException;
import java.util.Objects;
import org.litepal.parser.LitePalParser;
import org.litepal.util.Const;
import p110q.C1251d;
import p153w3.C1798e;

/* renamed from: x2.j */
/* loaded from: classes.dex */
public final class RunnableC1911j implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f7775j;

    /* renamed from: k */
    public final /* synthetic */ Object f7776k;

    /* renamed from: l */
    public final /* synthetic */ Object f7777l;

    public /* synthetic */ RunnableC1911j(Object obj, Object obj2, int i6) {
        this.f7775j = i6;
        this.f7777l = obj;
        this.f7776k = obj2;
    }

    @Override // java.lang.Runnable
    public final void run() {
        String packageName;
        String str;
        String str2;
        long j6 = 0;
        switch (this.f7775j) {
            case 0:
                ((InterfaceC2036y4) this.f7776k).mo4958a();
                if (!C1251d.m3139e()) {
                    boolean z5 = ((AbstractC1919k) this.f7777l).f7810c != 0;
                    ((AbstractC1919k) this.f7777l).f7810c = 0L;
                    if (z5) {
                        ((AbstractC1919k) this.f7777l).mo4898a();
                        break;
                    }
                } else {
                    ((InterfaceC2036y4) this.f7776k).mo4959b().m4918q(this);
                    break;
                }
                break;
            case 1:
                ((BinderC2012v4) this.f7777l).f8129a.m5086j();
                if (((C1839b) this.f7776k).f7562l.m5138c() != null) {
                    C1998t6 c1998t6 = ((BinderC2012v4) this.f7777l).f8129a;
                    C1839b c1839b = (C1839b) this.f7776k;
                    Objects.requireNonNull(c1998t6);
                    String str3 = c1839b.f7560j;
                    Objects.requireNonNull(str3, "null reference");
                    C1847b7 m5066C = c1998t6.m5066C(str3);
                    if (m5066C != null) {
                        c1998t6.m5091o(c1839b, m5066C);
                        break;
                    }
                } else {
                    C1998t6 c1998t62 = ((BinderC2012v4) this.f7777l).f8129a;
                    C1839b c1839b2 = (C1839b) this.f7776k;
                    Objects.requireNonNull(c1998t62);
                    String str4 = c1839b2.f7560j;
                    Objects.requireNonNull(str4, "null reference");
                    C1847b7 m5066C2 = c1998t62.m5066C(str4);
                    if (m5066C2 != null) {
                        c1998t62.m5092p(c1839b2, m5066C2);
                        break;
                    }
                }
                break;
            case 2:
                C1933l5 c1933l5 = (C1933l5) this.f7777l;
                Bundle bundle = (Bundle) this.f7776k;
                c1933l5.mo4915h();
                c1933l5.m5102i();
                C1798e.m4560r(bundle);
                String string = bundle.getString(Const.TableSchema.COLUMN_NAME);
                String string2 = bundle.getString("origin");
                C1798e.m4554o(string);
                C1798e.m4554o(string2);
                C1798e.m4560r(bundle.get(LitePalParser.ATTR_VALUE));
                if (!((C1948n4) c1933l5.f8145k).m4965i()) {
                    ((C1948n4) c1933l5.f8145k).mo4962e().f7792x.m4841b("Conditional property not set since app measurement is disabled");
                    break;
                } else {
                    try {
                        ((C1948n4) c1933l5.f8145k).m4979z().m4762x(new C1839b(bundle.getString("app_id"), string2, new C2022w6(string, bundle.getLong("triggered_timestamp"), bundle.get(LitePalParser.ATTR_VALUE), string2), bundle.getLong("creation_timestamp"), false, bundle.getString("trigger_event_name"), ((C1948n4) c1933l5.f8145k).m4973t().m4711J(bundle.getString("app_id"), bundle.getString("timed_out_event_name"), bundle.getBundle("timed_out_event_params"), string2, 0L, true), bundle.getLong("trigger_timeout"), ((C1948n4) c1933l5.f8145k).m4973t().m4711J(bundle.getString("app_id"), bundle.getString("triggered_event_name"), bundle.getBundle("triggered_event_params"), string2, 0L, true), bundle.getLong("time_to_live"), ((C1948n4) c1933l5.f8145k).m4973t().m4711J(bundle.getString("app_id"), bundle.getString("expired_event_name"), bundle.getBundle("expired_event_params"), string2, 0L, true)));
                        break;
                    } catch (IllegalArgumentException unused) {
                        return;
                    }
                }
            case 3:
                Object obj = this.f7777l;
                C1855c6 c1855c6 = (C1855c6) obj;
                InterfaceC1834a3 interfaceC1834a3 = c1855c6.f7620n;
                if (interfaceC1834a3 == null) {
                    ((C1948n4) c1855c6.f8145k).mo4962e().f7784p.m4841b("Failed to send current screen to service");
                    break;
                } else {
                    try {
                        C1981r5 c1981r5 = (C1981r5) this.f7776k;
                        if (c1981r5 == null) {
                            packageName = ((C1948n4) ((C1855c6) obj).f8145k).f7917j.getPackageName();
                            str2 = null;
                            str = null;
                        } else {
                            j6 = c1981r5.f8022c;
                            String str5 = c1981r5.f8020a;
                            String str6 = c1981r5.f8021b;
                            packageName = ((C1948n4) ((C1855c6) obj).f8145k).f7917j.getPackageName();
                            str = str6;
                            str2 = str5;
                        }
                        interfaceC1834a3.mo4687l(j6, str2, str, packageName);
                        ((C1855c6) this.f7777l).m4756r();
                        break;
                    } catch (RemoteException e6) {
                        ((C1948n4) ((C1855c6) this.f7777l).f8145k).mo4962e().f7784p.m4842c("Failed to send current screen to the service", e6);
                        return;
                    }
                }
            case 4:
                C1855c6 c1855c62 = (C1855c6) this.f7777l;
                InterfaceC1834a3 interfaceC1834a32 = c1855c62.f7620n;
                if (interfaceC1834a32 == null) {
                    ((C1948n4) c1855c62.f8145k).mo4962e().f7784p.m4841b("Failed to send measurementEnabled to service");
                    break;
                } else {
                    try {
                        C1798e.m4560r((C1847b7) this.f7776k);
                        interfaceC1834a32.mo4686k((C1847b7) this.f7776k);
                        ((C1855c6) this.f7777l).m4756r();
                        break;
                    } catch (RemoteException e7) {
                        ((C1948n4) ((C1855c6) this.f7777l).f8145k).mo4962e().f7784p.m4842c("Failed to send measurementEnabled to the service", e7);
                    }
                }
            default:
                C1998t6 c1998t63 = (C1998t6) this.f7777l;
                c1998t63.mo4959b().mo4915h();
                C1902i c1902i = new C1902i(c1998t63);
                c1902i.m4995k();
                c1998t63.f8084l = c1902i;
                C1866e m5068F = c1998t63.m5068F();
                C1889g4 c1889g4 = c1998t63.f8082j;
                Objects.requireNonNull(c1889g4, "null reference");
                m5068F.f7653m = c1889g4;
                C1864d6 c1864d6 = new C1864d6(c1998t63);
                c1864d6.m4995k();
                c1998t63.f8090r = c1864d6;
                C1910i7 c1910i7 = new C1910i7(c1998t63);
                c1910i7.m4995k();
                c1998t63.f8087o = c1910i7;
                C1973q5 c1973q5 = new C1973q5(c1998t63);
                c1973q5.m4995k();
                c1998t63.f8089q = c1973q5;
                C1950n6 c1950n6 = new C1950n6(c1998t63);
                c1950n6.m4995k();
                c1998t63.f8086n = c1950n6;
                c1998t63.f8085m = new C1971q3(c1998t63);
                if (c1998t63.f8097y != c1998t63.f8098z) {
                    c1998t63.mo4962e().f7784p.m4843d("Not all upload components initialized", Integer.valueOf(c1998t63.f8097y), Integer.valueOf(c1998t63.f8098z));
                }
                c1998t63.f8093u = true;
                C1998t6 c1998t64 = (C1998t6) this.f7777l;
                c1998t64.mo4959b().mo4915h();
                C1902i c1902i2 = c1998t64.f8084l;
                C1998t6.m5060E(c1902i2);
                c1902i2.m4869l();
                if (c1998t64.f8090r.f7648s.m5050a() == 0) {
                    C1995t3 c1995t3 = c1998t64.f8090r.f7648s;
                    Objects.requireNonNull((C1798e) c1998t64.mo4963f());
                    c1995t3.m5051b(System.currentTimeMillis());
                }
                c1998t64.m5099z();
                break;
        }
    }
}
