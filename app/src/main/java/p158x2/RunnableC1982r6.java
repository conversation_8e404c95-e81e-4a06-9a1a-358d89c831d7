package p158x2;

import android.os.Bundle;
import java.util.Objects;
import p153w3.C1798e;

/* renamed from: x2.r6 */
/* loaded from: classes.dex */
public final class RunnableC1982r6 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ String f8026j;

    /* renamed from: k */
    public final /* synthetic */ Bundle f8027k;

    /* renamed from: l */
    public final /* synthetic */ C2020w4 f8028l;

    public RunnableC1982r6(C2020w4 c2020w4, String str, Bundle bundle) {
        this.f8028l = c2020w4;
        this.f8026j = str;
        this.f8027k = bundle;
    }

    @Override // java.lang.Runnable
    public final void run() {
        C1838a7 m5073K = ((C1998t6) this.f8028l.f8145k).m5073K();
        String str = this.f8026j;
        Bundle bundle = this.f8027k;
        Objects.requireNonNull((C1798e) ((C1998t6) this.f8028l.f8145k).mo4963f());
        C1967q m4711J = m5073K.m4711J(str, "_err", bundle, "auto", System.currentTimeMillis(), false);
        C1998t6 c1998t6 = (C1998t6) this.f8028l.f8145k;
        Objects.requireNonNull(m4711J, "null reference");
        c1998t6.m5078P(m4711J, this.f8026j);
    }
}
