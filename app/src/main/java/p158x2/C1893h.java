package p158x2;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteException;
import android.database.sqlite.SQLiteOpenHelper;
import android.os.SystemClock;
import java.util.Objects;
import p008b0.C0385m;
import p153w3.C1798e;

/* renamed from: x2.h */
/* loaded from: classes.dex */
public final class C1893h extends SQLiteOpenHelper {

    /* renamed from: j */
    public final /* synthetic */ C1902i f7729j;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C1893h(C1902i c1902i, Context context) {
        super(context, "google_app_measurement.db", (SQLiteDatabase.CursorFactory) null, 1);
        this.f7729j = c1902i;
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public final SQLiteDatabase getWritableDatabase() {
        C1902i c1902i = this.f7729j;
        C1942m6 c1942m6 = c1902i.f7755o;
        Objects.requireNonNull((C1948n4) c1902i.f8145k);
        boolean z5 = true;
        if (c1942m6.f7878b != 0) {
            Objects.requireNonNull((C1798e) c1942m6.f7877a);
            if (SystemClock.elapsedRealtime() - c1942m6.f7878b < 3600000) {
                z5 = false;
            }
        }
        if (!z5) {
            throw new SQLiteException("Database open failed");
        }
        try {
            return super.getWritableDatabase();
        } catch (SQLiteException unused) {
            C1942m6 c1942m62 = this.f7729j.f7755o;
            Objects.requireNonNull((C1798e) c1942m62.f7877a);
            c1942m62.f7878b = SystemClock.elapsedRealtime();
            ((C1948n4) this.f7729j.f8145k).mo4962e().f7784p.m4841b("Opening the database failed, dropping and recreating it");
            Objects.requireNonNull((C1948n4) this.f7729j.f8145k);
            if (!((C1948n4) this.f7729j.f8145k).f7917j.getDatabasePath("google_app_measurement.db").delete()) {
                ((C1948n4) this.f7729j.f8145k).mo4962e().f7784p.m4842c("Failed to delete corrupted db file", "google_app_measurement.db");
            }
            try {
                SQLiteDatabase writableDatabase = super.getWritableDatabase();
                this.f7729j.f7755o.f7878b = 0L;
                return writableDatabase;
            } catch (SQLiteException e6) {
                ((C1948n4) this.f7729j.f8145k).mo4962e().f7784p.m4842c("Failed to open freshly created database", e6);
                throw e6;
            }
        }
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public final void onCreate(SQLiteDatabase sQLiteDatabase) {
        C0385m.m1431x(((C1948n4) this.f7729j.f8145k).mo4962e(), sQLiteDatabase);
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public final void onDowngrade(SQLiteDatabase sQLiteDatabase, int i6, int i7) {
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public final void onOpen(SQLiteDatabase sQLiteDatabase) {
        C0385m.m1427s(((C1948n4) this.f7729j.f8145k).mo4962e(), sQLiteDatabase, "events", "CREATE TABLE IF NOT EXISTS events ( app_id TEXT NOT NULL, name TEXT NOT NULL, lifetime_count INTEGER NOT NULL, current_bundle_count INTEGER NOT NULL, last_fire_timestamp INTEGER NOT NULL, PRIMARY KEY (app_id, name)) ;", "app_id,name,lifetime_count,current_bundle_count,last_fire_timestamp", C1902i.f7746p);
        C0385m.m1427s(((C1948n4) this.f7729j.f8145k).mo4962e(), sQLiteDatabase, "conditional_properties", "CREATE TABLE IF NOT EXISTS conditional_properties ( app_id TEXT NOT NULL, origin TEXT NOT NULL, name TEXT NOT NULL, value BLOB NOT NULL, creation_timestamp INTEGER NOT NULL, active INTEGER NOT NULL, trigger_event_name TEXT, trigger_timeout INTEGER NOT NULL, timed_out_event BLOB,triggered_event BLOB, triggered_timestamp INTEGER NOT NULL, time_to_live INTEGER NOT NULL, expired_event BLOB, PRIMARY KEY (app_id, name)) ;", "app_id,origin,name,value,active,trigger_event_name,trigger_timeout,creation_timestamp,timed_out_event,triggered_event,triggered_timestamp,time_to_live,expired_event", null);
        C0385m.m1427s(((C1948n4) this.f7729j.f8145k).mo4962e(), sQLiteDatabase, "user_attributes", "CREATE TABLE IF NOT EXISTS user_attributes ( app_id TEXT NOT NULL, name TEXT NOT NULL, set_timestamp INTEGER NOT NULL, value BLOB NOT NULL, PRIMARY KEY (app_id, name)) ;", "app_id,name,set_timestamp,value", C1902i.f7747q);
        C0385m.m1427s(((C1948n4) this.f7729j.f8145k).mo4962e(), sQLiteDatabase, "apps", "CREATE TABLE IF NOT EXISTS apps ( app_id TEXT NOT NULL, app_instance_id TEXT, gmp_app_id TEXT, resettable_device_id_hash TEXT, last_bundle_index INTEGER NOT NULL, last_bundle_end_timestamp INTEGER NOT NULL, PRIMARY KEY (app_id)) ;", "app_id,app_instance_id,gmp_app_id,resettable_device_id_hash,last_bundle_index,last_bundle_end_timestamp", C1902i.f7748r);
        C0385m.m1427s(((C1948n4) this.f7729j.f8145k).mo4962e(), sQLiteDatabase, "queue", "CREATE TABLE IF NOT EXISTS queue ( app_id TEXT NOT NULL, bundle_end_timestamp INTEGER NOT NULL, data BLOB NOT NULL);", "app_id,bundle_end_timestamp,data", C1902i.f7750t);
        C0385m.m1427s(((C1948n4) this.f7729j.f8145k).mo4962e(), sQLiteDatabase, "raw_events_metadata", "CREATE TABLE IF NOT EXISTS raw_events_metadata ( app_id TEXT NOT NULL, metadata_fingerprint INTEGER NOT NULL, metadata BLOB NOT NULL, PRIMARY KEY (app_id, metadata_fingerprint));", "app_id,metadata_fingerprint,metadata", null);
        C0385m.m1427s(((C1948n4) this.f7729j.f8145k).mo4962e(), sQLiteDatabase, "raw_events", "CREATE TABLE IF NOT EXISTS raw_events ( app_id TEXT NOT NULL, name TEXT NOT NULL, timestamp INTEGER NOT NULL, metadata_fingerprint INTEGER NOT NULL, data BLOB NOT NULL);", "app_id,name,timestamp,metadata_fingerprint,data", C1902i.f7749s);
        C0385m.m1427s(((C1948n4) this.f7729j.f8145k).mo4962e(), sQLiteDatabase, "event_filters", "CREATE TABLE IF NOT EXISTS event_filters ( app_id TEXT NOT NULL, audience_id INTEGER NOT NULL, filter_id INTEGER NOT NULL, event_name TEXT NOT NULL, data BLOB NOT NULL, PRIMARY KEY (app_id, event_name, audience_id, filter_id));", "app_id,audience_id,filter_id,event_name,data", C1902i.f7751u);
        C0385m.m1427s(((C1948n4) this.f7729j.f8145k).mo4962e(), sQLiteDatabase, "property_filters", "CREATE TABLE IF NOT EXISTS property_filters ( app_id TEXT NOT NULL, audience_id INTEGER NOT NULL, filter_id INTEGER NOT NULL, property_name TEXT NOT NULL, data BLOB NOT NULL, PRIMARY KEY (app_id, property_name, audience_id, filter_id));", "app_id,audience_id,filter_id,property_name,data", C1902i.f7752v);
        C0385m.m1427s(((C1948n4) this.f7729j.f8145k).mo4962e(), sQLiteDatabase, "audience_filter_values", "CREATE TABLE IF NOT EXISTS audience_filter_values ( app_id TEXT NOT NULL, audience_id INTEGER NOT NULL, current_results BLOB, PRIMARY KEY (app_id, audience_id));", "app_id,audience_id,current_results", null);
        C0385m.m1427s(((C1948n4) this.f7729j.f8145k).mo4962e(), sQLiteDatabase, "app2", "CREATE TABLE IF NOT EXISTS app2 ( app_id TEXT NOT NULL, first_open_count INTEGER NOT NULL, PRIMARY KEY (app_id));", "app_id,first_open_count", C1902i.f7753w);
        C0385m.m1427s(((C1948n4) this.f7729j.f8145k).mo4962e(), sQLiteDatabase, "main_event_params", "CREATE TABLE IF NOT EXISTS main_event_params ( app_id TEXT NOT NULL, event_id TEXT NOT NULL, children_to_process INTEGER NOT NULL, main_event BLOB NOT NULL, PRIMARY KEY (app_id));", "app_id,event_id,children_to_process,main_event", null);
        C0385m.m1427s(((C1948n4) this.f7729j.f8145k).mo4962e(), sQLiteDatabase, "default_event_params", "CREATE TABLE IF NOT EXISTS default_event_params ( app_id TEXT NOT NULL, parameters BLOB NOT NULL, PRIMARY KEY (app_id));", "app_id,parameters", null);
        C0385m.m1427s(((C1948n4) this.f7729j.f8145k).mo4962e(), sQLiteDatabase, "consent_settings", "CREATE TABLE IF NOT EXISTS consent_settings ( app_id TEXT NOT NULL, consent_state TEXT NOT NULL, PRIMARY KEY (app_id));", "app_id,consent_state", null);
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public final void onUpgrade(SQLiteDatabase sQLiteDatabase, int i6, int i7) {
    }
}
