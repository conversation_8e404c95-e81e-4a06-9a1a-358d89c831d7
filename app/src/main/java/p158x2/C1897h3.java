package p158x2;

import androidx.activity.result.C0052a;
import java.net.UnknownServiceException;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import javax.net.ssl.SSLSocket;
import p102o5.C1164g;
import p102o5.C1166i;
import p102o5.C1178u;
import p109p5.AbstractC1244a;
import p109p5.C1246c;

/* renamed from: x2.h3 */
/* loaded from: classes.dex */
public final class C1897h3 {

    /* renamed from: a */
    public int f7733a;

    /* renamed from: b */
    public boolean f7734b;

    /* renamed from: c */
    public boolean f7735c;

    /* renamed from: d */
    public final Object f7736d;

    public /* synthetic */ C1897h3(List list) {
        this.f7733a = 0;
        this.f7736d = list;
    }

    public /* synthetic */ C1897h3(C1915j3 c1915j3, int i6, boolean z5, boolean z6) {
        this.f7736d = c1915j3;
        this.f7733a = i6;
        this.f7734b = z5;
        this.f7735c = z6;
    }

    /* renamed from: a */
    public final C1166i m4840a(SSLSocket sSLSocket) {
        C1166i c1166i;
        boolean z5;
        int i6 = this.f7733a;
        int size = ((List) this.f7736d).size();
        while (true) {
            if (i6 >= size) {
                c1166i = null;
                break;
            }
            c1166i = (C1166i) ((List) this.f7736d).get(i6);
            if (c1166i.m3001a(sSLSocket)) {
                this.f7733a = i6 + 1;
                break;
            }
            i6++;
        }
        if (c1166i == null) {
            StringBuilder m104h = C0052a.m104h("Unable to find acceptable protocols. isFallback=");
            m104h.append(this.f7735c);
            m104h.append(", modes=");
            m104h.append((List) this.f7736d);
            m104h.append(", supported protocols=");
            m104h.append(Arrays.toString(sSLSocket.getEnabledProtocols()));
            throw new UnknownServiceException(m104h.toString());
        }
        int i7 = this.f7733a;
        while (true) {
            if (i7 >= ((List) this.f7736d).size()) {
                z5 = false;
                break;
            }
            if (((C1166i) ((List) this.f7736d).get(i7)).m3001a(sSLSocket)) {
                z5 = true;
                break;
            }
            i7++;
        }
        this.f7734b = z5;
        C1178u.a aVar = AbstractC1244a.f5896a;
        boolean z6 = this.f7735c;
        Objects.requireNonNull(aVar);
        String[] m3129s = c1166i.f5639c != null ? C1246c.m3129s(C1164g.f5611b, sSLSocket.getEnabledCipherSuites(), c1166i.f5639c) : sSLSocket.getEnabledCipherSuites();
        String[] m3129s2 = c1166i.f5640d != null ? C1246c.m3129s(C1246c.f5912o, sSLSocket.getEnabledProtocols(), c1166i.f5640d) : sSLSocket.getEnabledProtocols();
        String[] supportedCipherSuites = sSLSocket.getSupportedCipherSuites();
        Comparator<String> comparator = C1164g.f5611b;
        byte[] bArr = C1246c.f5898a;
        int length = supportedCipherSuites.length;
        int i8 = 0;
        while (true) {
            if (i8 >= length) {
                i8 = -1;
                break;
            }
            if (comparator.compare(supportedCipherSuites[i8], "TLS_FALLBACK_SCSV") == 0) {
                break;
            }
            i8++;
        }
        if (z6 && i8 != -1) {
            String str = supportedCipherSuites[i8];
            int length2 = m3129s.length + 1;
            String[] strArr = new String[length2];
            System.arraycopy(m3129s, 0, strArr, 0, m3129s.length);
            strArr[length2 - 1] = str;
            m3129s = strArr;
        }
        C1166i.a aVar2 = new C1166i.a(c1166i);
        aVar2.m3003b(m3129s);
        aVar2.m3004c(m3129s2);
        C1166i c1166i2 = new C1166i(aVar2);
        String[] strArr2 = c1166i2.f5640d;
        if (strArr2 != null) {
            sSLSocket.setEnabledProtocols(strArr2);
        }
        String[] strArr3 = c1166i2.f5639c;
        if (strArr3 != null) {
            sSLSocket.setEnabledCipherSuites(strArr3);
        }
        return c1166i;
    }

    /* renamed from: b */
    public final void m4841b(String str) {
        ((C1915j3) this.f7736d).m4895u(this.f7733a, this.f7734b, this.f7735c, str, null, null, null);
    }

    /* renamed from: c */
    public final void m4842c(String str, Object obj) {
        ((C1915j3) this.f7736d).m4895u(this.f7733a, this.f7734b, this.f7735c, str, obj, null, null);
    }

    /* renamed from: d */
    public final void m4843d(String str, Object obj, Object obj2) {
        ((C1915j3) this.f7736d).m4895u(this.f7733a, this.f7734b, this.f7735c, str, obj, obj2, null);
    }

    /* renamed from: e */
    public final void m4844e(String str, Object obj, Object obj2, Object obj3) {
        ((C1915j3) this.f7736d).m4895u(this.f7733a, this.f7734b, this.f7735c, str, obj, obj2, obj3);
    }
}
