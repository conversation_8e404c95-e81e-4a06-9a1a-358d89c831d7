package p158x2;

import android.os.Parcel;
import android.os.Parcelable;
import p099o2.C1147b;
import p153w3.C1798e;

/* renamed from: x2.x6 */
/* loaded from: classes.dex */
public final class C2030x6 implements Parcelable.Creator<C2022w6> {
    /* renamed from: a */
    public static void m5156a(C2022w6 c2022w6, Parcel parcel) {
        int m4526S = C1798e.m4526S(parcel, 20293);
        int i6 = c2022w6.f8148j;
        C1798e.m4540g0(parcel, 1, 4);
        parcel.writeInt(i6);
        C1798e.m4524Q(parcel, 2, c2022w6.f8149k);
        long j6 = c2022w6.f8150l;
        C1798e.m4540g0(parcel, 3, 8);
        parcel.writeLong(j6);
        Long l6 = c2022w6.f8151m;
        if (l6 != null) {
            C1798e.m4540g0(parcel, 4, 8);
            parcel.writeLong(l6.longValue());
        }
        C1798e.m4524Q(parcel, 6, c2022w6.f8152n);
        C1798e.m4524Q(parcel, 7, c2022w6.f8153o);
        Double d6 = c2022w6.f8154p;
        if (d6 != null) {
            C1798e.m4540g0(parcel, 8, 8);
            parcel.writeDouble(d6.doubleValue());
        }
        C1798e.m4539f0(parcel, m4526S);
    }

    @Override // android.os.Parcelable.Creator
    public final C2022w6 createFromParcel(Parcel parcel) {
        int m2977l = C1147b.m2977l(parcel);
        String str = null;
        Long l6 = null;
        Float f6 = null;
        String str2 = null;
        String str3 = null;
        Double d6 = null;
        int i6 = 0;
        long j6 = 0;
        while (parcel.dataPosition() < m2977l) {
            int readInt = parcel.readInt();
            switch (65535 & readInt) {
                case 1:
                    i6 = C1147b.m2973h(parcel, readInt);
                    break;
                case 2:
                    str = C1147b.m2968c(parcel, readInt);
                    break;
                case 3:
                    j6 = C1147b.m2974i(parcel, readInt);
                    break;
                case 4:
                    int m2975j = C1147b.m2975j(parcel, readInt);
                    if (m2975j != 0) {
                        C1147b.m2979n(parcel, m2975j, 8);
                        l6 = Long.valueOf(parcel.readLong());
                        break;
                    } else {
                        l6 = null;
                        break;
                    }
                case 5:
                    int m2975j2 = C1147b.m2975j(parcel, readInt);
                    if (m2975j2 != 0) {
                        C1147b.m2979n(parcel, m2975j2, 4);
                        f6 = Float.valueOf(parcel.readFloat());
                        break;
                    } else {
                        f6 = null;
                        break;
                    }
                case 6:
                    str2 = C1147b.m2968c(parcel, readInt);
                    break;
                case 7:
                    str3 = C1147b.m2968c(parcel, readInt);
                    break;
                case 8:
                    int m2975j3 = C1147b.m2975j(parcel, readInt);
                    if (m2975j3 != 0) {
                        C1147b.m2979n(parcel, m2975j3, 8);
                        d6 = Double.valueOf(parcel.readDouble());
                        break;
                    } else {
                        d6 = null;
                        break;
                    }
                default:
                    C1147b.m2976k(parcel, readInt);
                    break;
            }
        }
        C1147b.m2970e(parcel, m2977l);
        return new C2022w6(i6, str, j6, l6, f6, str2, str3, d6);
    }

    @Override // android.os.Parcelable.Creator
    public final /* bridge */ /* synthetic */ C2022w6[] newArray(int i6) {
        return new C2022w6[i6];
    }
}
