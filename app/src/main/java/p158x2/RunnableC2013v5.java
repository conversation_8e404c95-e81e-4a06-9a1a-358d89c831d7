package p158x2;

import android.os.RemoteException;
import p153w3.C1798e;

/* renamed from: x2.v5 */
/* loaded from: classes.dex */
public final class RunnableC2013v5 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f8132j;

    /* renamed from: k */
    public final /* synthetic */ C1847b7 f8133k;

    /* renamed from: l */
    public final /* synthetic */ C1855c6 f8134l;

    public /* synthetic */ RunnableC2013v5(C1855c6 c1855c6, C1847b7 c1847b7, int i6) {
        this.f8132j = i6;
        this.f8134l = c1855c6;
        this.f8133k = c1847b7;
    }

    @Override // java.lang.Runnable
    public final void run() {
        switch (this.f8132j) {
            case 0:
                C1855c6 c1855c6 = this.f8134l;
                InterfaceC1834a3 interfaceC1834a3 = c1855c6.f7620n;
                if (interfaceC1834a3 != null) {
                    try {
                        C1798e.m4560r(this.f8133k);
                        interfaceC1834a3.mo4684f(this.f8133k);
                    } catch (RemoteException e6) {
                        ((C1948n4) this.f8134l.f8145k).mo4962e().f7784p.m4842c("Failed to reset data on the service: remote exception", e6);
                    }
                    this.f8134l.m4756r();
                    break;
                } else {
                    ((C1948n4) c1855c6.f8145k).mo4962e().f7784p.m4841b("Failed to reset data on the service: not connected to service");
                    break;
                }
            case 1:
                C1855c6 c1855c62 = this.f8134l;
                InterfaceC1834a3 interfaceC1834a32 = c1855c62.f7620n;
                if (interfaceC1834a32 == null) {
                    ((C1948n4) c1855c62.f8145k).mo4962e().f7784p.m4841b("Discarding data. Failed to send app launch");
                    break;
                } else {
                    try {
                        C1798e.m4560r(this.f8133k);
                        interfaceC1834a32.mo4689o(this.f8133k);
                        ((C1948n4) this.f8134l.f8145k).m4975v().m4764m();
                        this.f8134l.m4761w(interfaceC1834a32, null, this.f8133k);
                        this.f8134l.m4756r();
                        break;
                    } catch (RemoteException e7) {
                        ((C1948n4) this.f8134l.f8145k).mo4962e().f7784p.m4842c("Failed to send app launch to the service", e7);
                    }
                }
            default:
                C1855c6 c1855c63 = this.f8134l;
                InterfaceC1834a3 interfaceC1834a33 = c1855c63.f7620n;
                if (interfaceC1834a33 == null) {
                    ((C1948n4) c1855c63.f8145k).mo4962e().f7784p.m4841b("Failed to send consent settings to service");
                    break;
                } else {
                    try {
                        C1798e.m4560r(this.f8133k);
                        interfaceC1834a33.mo4688m(this.f8133k);
                        this.f8134l.m4756r();
                        break;
                    } catch (RemoteException e8) {
                        ((C1948n4) this.f8134l.f8145k).mo4962e().f7784p.m4842c("Failed to send consent settings to the service", e8);
                        return;
                    }
                }
        }
    }
}
