package p158x2;

import android.content.Context;
import android.os.Bundle;
import java.util.Objects;
import p152w2.C1603k0;
import p153w3.C1798e;

/* renamed from: x2.a5 */
/* loaded from: classes.dex */
public final class C1836a5 {

    /* renamed from: a */
    public final Context f7541a;

    /* renamed from: b */
    public String f7542b;

    /* renamed from: c */
    public String f7543c;

    /* renamed from: d */
    public String f7544d;

    /* renamed from: e */
    public Boolean f7545e;

    /* renamed from: f */
    public long f7546f;

    /* renamed from: g */
    public C1603k0 f7547g;

    /* renamed from: h */
    public boolean f7548h;

    /* renamed from: i */
    public final Long f7549i;

    /* renamed from: j */
    public String f7550j;

    public C1836a5(Context context, C1603k0 c1603k0) {
        this.f7548h = true;
        Objects.requireNonNull(context, "null reference");
        Context applicationContext = context.getApplicationContext();
        C1798e.m4560r(applicationContext);
        this.f7541a = applicationContext;
        this.f7549i = null;
        if (c1603k0 != null) {
            this.f7547g = c1603k0;
            this.f7542b = c1603k0.f7050o;
            this.f7543c = c1603k0.f7049n;
            this.f7544d = c1603k0.f7048m;
            this.f7548h = c1603k0.f7047l;
            this.f7546f = c1603k0.f7046k;
            this.f7550j = c1603k0.f7052q;
            Bundle bundle = c1603k0.f7051p;
            if (bundle != null) {
                this.f7545e = Boolean.valueOf(bundle.getBoolean("dataCollectionDefaultEnabled", true));
            }
        }
    }
}
