package p158x2;

/* renamed from: x2.v3 */
/* loaded from: classes.dex */
public abstract class AbstractC2011v3 extends C2018w2 {

    /* renamed from: l */
    public boolean f8128l;

    public AbstractC2011v3(C1948n4 c1948n4) {
        super(c1948n4);
        ((C1948n4) this.f8145k).f7914O++;
    }

    /* renamed from: i */
    public final void m5102i() {
        if (!this.f8128l) {
            throw new IllegalStateException("Not initialized");
        }
    }

    /* renamed from: j */
    public final void m5103j() {
        if (this.f8128l) {
            throw new IllegalStateException("Can't initialize twice");
        }
        if (mo4746k()) {
            return;
        }
        ((C1948n4) this.f8145k).m4967k();
        this.f8128l = true;
    }

    /* renamed from: k */
    public abstract boolean mo4746k();
}
