package p158x2;

import android.app.job.JobParameters;
import android.content.ContentValues;
import android.content.Context;
import android.database.sqlite.SQLiteException;
import android.os.Bundle;
import android.os.RemoteException;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import p153w3.C1798e;

/* renamed from: x2.o4 */
/* loaded from: classes.dex */
public final /* synthetic */ class RunnableC1956o4 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f7946j;

    /* renamed from: k */
    public final Object f7947k;

    /* renamed from: l */
    public final Object f7948l;

    /* renamed from: m */
    public final Object f7949m;

    public /* synthetic */ RunnableC1956o4(Object obj, Object obj2, Object obj3, int i6) {
        this.f7946j = i6;
        this.f7948l = obj;
        this.f7947k = obj2;
        this.f7949m = obj3;
    }

    @Override // java.lang.Runnable
    public final void run() {
        AtomicReference atomicReference;
        switch (this.f7946j) {
            case 0:
                BinderC2012v4 binderC2012v4 = (BinderC2012v4) this.f7948l;
                String str = (String) this.f7947k;
                Bundle bundle = (Bundle) this.f7949m;
                C1902i c1902i = binderC2012v4.f8129a.f8084l;
                C1998t6.m5060E(c1902i);
                c1902i.mo4915h();
                c1902i.m4994i();
                C1935m c1935m = new C1935m((C1948n4) c1902i.f8145k, "", str, "dep", 0L, bundle);
                C2014v6 c2014v6 = c1902i.f7954l.f8088p;
                C1998t6.m5060E(c2014v6);
                byte[] m3943f = c2014v6.m5132w(c1935m).m3943f();
                ((C1948n4) c1902i.f8145k).mo4962e().f7792x.m4843d("Saving default event parameters, appId, data size", ((C1948n4) c1902i.f8145k).m4974u().m4794p(str), Integer.valueOf(m3943f.length));
                ContentValues contentValues = new ContentValues();
                contentValues.put("app_id", str);
                contentValues.put("parameters", m3943f);
                try {
                    if (c1902i.m4849A().insertWithOnConflict("default_event_params", null, contentValues, 5) == -1) {
                        ((C1948n4) c1902i.f8145k).mo4962e().f7784p.m4842c("Failed to insert default event parameters (got -1). appId", C1915j3.m4886t(str));
                        return;
                    }
                    return;
                } catch (SQLiteException e6) {
                    ((C1948n4) c1902i.f8145k).mo4962e().f7784p.m4843d("Error storing default event parameters. appId", C1915j3.m4886t(str), e6);
                    return;
                }
            case 1:
                ((BinderC2012v4) this.f7948l).f8129a.m5086j();
                ((BinderC2012v4) this.f7948l).f8129a.m5078P((C1967q) this.f7949m, (String) this.f7947k);
                return;
            case 2:
                ((BinderC2012v4) this.f7948l).f8129a.m5086j();
                if (((C2022w6) this.f7947k).m5138c() == null) {
                    ((BinderC2012v4) this.f7948l).f8129a.m5089m((C2022w6) this.f7947k, (C1847b7) this.f7949m);
                    return;
                } else {
                    ((BinderC2012v4) this.f7948l).f8129a.m5088l((C2022w6) this.f7947k, (C1847b7) this.f7949m);
                    return;
                }
            case 3:
                synchronized (((AtomicReference) this.f7948l)) {
                    try {
                        try {
                        } catch (RemoteException e7) {
                            ((C1948n4) ((C1855c6) this.f7949m).f8145k).mo4962e().f7784p.m4842c("Failed to get app instance id", e7);
                        }
                        if (((C1948n4) ((C1855c6) this.f7949m).f8145k).m4970q().m5149s().m4807d()) {
                            Object obj = this.f7949m;
                            InterfaceC1834a3 interfaceC1834a3 = ((C1855c6) obj).f7620n;
                            if (interfaceC1834a3 == null) {
                                ((C1948n4) ((C1855c6) obj).f8145k).mo4962e().f7784p.m4841b("Failed to get app instance id");
                                atomicReference = (AtomicReference) this.f7948l;
                            } else {
                                C1798e.m4560r((C1847b7) this.f7947k);
                                ((AtomicReference) this.f7948l).set(interfaceC1834a3.mo4693v((C1847b7) this.f7947k));
                                String str2 = (String) ((AtomicReference) this.f7948l).get();
                                if (str2 != null) {
                                    ((C1948n4) ((C1855c6) this.f7949m).f8145k).m4972s().m4926n(str2);
                                    ((C1948n4) ((C1855c6) this.f7949m).f8145k).m4970q().f8244q.m5137b(str2);
                                }
                                ((C1855c6) this.f7949m).m4756r();
                                atomicReference = (AtomicReference) this.f7948l;
                            }
                        } else {
                            ((C1948n4) ((C1855c6) this.f7949m).f8145k).mo4962e().f7789u.m4841b("Analytics storage consent denied; will not get app instance id");
                            ((C1948n4) ((C1855c6) this.f7949m).f8145k).m4972s().m4926n(null);
                            ((C1948n4) ((C1855c6) this.f7949m).f8145k).m4970q().f8244q.m5137b(null);
                            ((AtomicReference) this.f7948l).set(null);
                            atomicReference = (AtomicReference) this.f7948l;
                        }
                        atomicReference.notify();
                    } catch (Throwable th) {
                        ((AtomicReference) this.f7948l).notify();
                        throw th;
                    }
                }
                return;
            default:
                C1891g6 c1891g6 = (C1891g6) this.f7948l;
                C1915j3 c1915j3 = (C1915j3) this.f7947k;
                JobParameters jobParameters = (JobParameters) this.f7949m;
                Objects.requireNonNull(c1891g6);
                c1915j3.f7792x.m4841b("AppMeasurementJobService processed last upload request.");
                ((InterfaceC1882f6) ((Context) c1891g6.f7722k)).mo1558c(jobParameters);
                return;
        }
    }

    public RunnableC1956o4(BinderC2012v4 binderC2012v4, C1967q c1967q, String str) {
        this.f7946j = 1;
        this.f7948l = binderC2012v4;
        this.f7949m = c1967q;
        this.f7947k = str;
    }

    public RunnableC1956o4(C1855c6 c1855c6, AtomicReference atomicReference, C1847b7 c1847b7) {
        this.f7946j = 3;
        this.f7949m = c1855c6;
        this.f7948l = atomicReference;
        this.f7947k = c1847b7;
    }
}
