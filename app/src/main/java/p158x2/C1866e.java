package p158x2;

import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.text.TextUtils;
import java.lang.reflect.InvocationTargetException;
import java.util.Objects;
import p008b0.C0385m;
import p118r2.C1306c;
import p152w2.C1612k9;
import p153w3.C1798e;

/* renamed from: x2.e */
/* loaded from: classes.dex */
public final class C1866e extends C2020w4 {

    /* renamed from: l */
    public Boolean f7652l;

    /* renamed from: m */
    public InterfaceC1857d f7653m;

    /* renamed from: n */
    public Boolean f7654n;

    public C1866e(C1948n4 c1948n4) {
        super(c1948n4);
        this.f7653m = C0385m.f2328S;
    }

    /* renamed from: i */
    public static final long m4775i() {
        return C2026x2.f8162C.m5101a(null).longValue();
    }

    /* renamed from: y */
    public static final long m4776y() {
        return C2026x2.f8192d.m5101a(null).longValue();
    }

    /* renamed from: j */
    public final String m4777j(String str) {
        C1897h3 c1897h3;
        String str2;
        try {
            String str3 = (String) Class.forName("android.os.SystemProperties").getMethod("get", String.class, String.class).invoke(null, str, "");
            C1798e.m4560r(str3);
            return str3;
        } catch (ClassNotFoundException e6) {
            e = e6;
            c1897h3 = ((C1948n4) this.f8145k).mo4962e().f7784p;
            str2 = "Could not find SystemProperties class";
            c1897h3.m4842c(str2, e);
            return "";
        } catch (IllegalAccessException e7) {
            e = e7;
            c1897h3 = ((C1948n4) this.f8145k).mo4962e().f7784p;
            str2 = "Could not access SystemProperties.get()";
            c1897h3.m4842c(str2, e);
            return "";
        } catch (NoSuchMethodException e8) {
            e = e8;
            c1897h3 = ((C1948n4) this.f8145k).mo4962e().f7784p;
            str2 = "Could not find SystemProperties.get() method";
            c1897h3.m4842c(str2, e);
            return "";
        } catch (InvocationTargetException e9) {
            e = e9;
            c1897h3 = ((C1948n4) this.f8145k).mo4962e().f7784p;
            str2 = "SystemProperties.get() threw an exception";
            c1897h3.m4842c(str2, e);
            return "";
        }
    }

    /* renamed from: k */
    public final int m4778k(String str) {
        return m4783p(str, C2026x2.f8167H, 25, 100);
    }

    /* renamed from: l */
    public final int m4779l(String str) {
        return m4783p(str, C2026x2.f8166G, 500, 2000);
    }

    /* renamed from: m */
    public final void m4780m() {
        Objects.requireNonNull((C1948n4) this.f8145k);
    }

    /* renamed from: n */
    public final long m4781n(String str, C2010v2<Long> c2010v2) {
        if (str != null) {
            String mo1435c = this.f7653m.mo1435c(str, c2010v2.f8122a);
            if (!TextUtils.isEmpty(mo1435c)) {
                try {
                    return c2010v2.m5101a(Long.valueOf(Long.parseLong(mo1435c))).longValue();
                } catch (NumberFormatException unused) {
                }
            }
        }
        return c2010v2.m5101a(null).longValue();
    }

    /* renamed from: o */
    public final int m4782o(String str, C2010v2<Integer> c2010v2) {
        if (str != null) {
            String mo1435c = this.f7653m.mo1435c(str, c2010v2.f8122a);
            if (!TextUtils.isEmpty(mo1435c)) {
                try {
                    return c2010v2.m5101a(Integer.valueOf(Integer.parseInt(mo1435c))).intValue();
                } catch (NumberFormatException unused) {
                }
            }
        }
        return c2010v2.m5101a(null).intValue();
    }

    /* renamed from: p */
    public final int m4783p(String str, C2010v2<Integer> c2010v2, int i6, int i7) {
        return Math.max(Math.min(m4782o(str, c2010v2), i7), i6);
    }

    /* renamed from: q */
    public final boolean m4784q(String str, C2010v2<Boolean> c2010v2) {
        Boolean m5101a;
        if (str != null) {
            String mo1435c = this.f7653m.mo1435c(str, c2010v2.f8122a);
            if (!TextUtils.isEmpty(mo1435c)) {
                m5101a = c2010v2.m5101a(Boolean.valueOf(Boolean.parseBoolean(mo1435c)));
                return m5101a.booleanValue();
            }
        }
        m5101a = c2010v2.m5101a(null);
        return m5101a.booleanValue();
    }

    /* renamed from: r */
    public final Bundle m4785r() {
        try {
            if (((C1948n4) this.f8145k).f7917j.getPackageManager() == null) {
                ((C1948n4) this.f8145k).mo4962e().f7784p.m4841b("Failed to load metadata: PackageManager is null");
                return null;
            }
            ApplicationInfo m3207a = C1306c.m3210a(((C1948n4) this.f8145k).f7917j).m3207a(((C1948n4) this.f8145k).f7917j.getPackageName(), 128);
            if (m3207a != null) {
                return m3207a.metaData;
            }
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4841b("Failed to load metadata: ApplicationInfo is null");
            return null;
        } catch (PackageManager.NameNotFoundException e6) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4842c("Failed to load metadata: Package name not found", e6);
            return null;
        }
    }

    /* renamed from: s */
    public final Boolean m4786s(String str) {
        C1798e.m4554o(str);
        Bundle m4785r = m4785r();
        if (m4785r == null) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4841b("Failed to load metadata: Metadata bundle is null");
            return null;
        }
        if (m4785r.containsKey(str)) {
            return Boolean.valueOf(m4785r.getBoolean(str));
        }
        return null;
    }

    /* renamed from: t */
    public final boolean m4787t() {
        Objects.requireNonNull((C1948n4) this.f8145k);
        Boolean m4786s = m4786s("firebase_analytics_collection_deactivated");
        return m4786s != null && m4786s.booleanValue();
    }

    /* renamed from: u */
    public final boolean m4788u() {
        Boolean m4786s;
        C1612k9.f7064k.mo2029a().mo3908a();
        return !m4784q(null, C2026x2.f8207k0) || (m4786s = m4786s("google_analytics_automatic_screen_reporting_enabled")) == null || m4786s.booleanValue();
    }

    /* renamed from: v */
    public final boolean m4789v(String str) {
        return "1".equals(this.f7653m.mo1435c(str, "gaia_collection_enabled"));
    }

    /* renamed from: w */
    public final boolean m4790w(String str) {
        return "1".equals(this.f7653m.mo1435c(str, "measurement.event_sampling_enabled"));
    }

    /* renamed from: x */
    public final boolean m4791x() {
        if (this.f7652l == null) {
            Boolean m4786s = m4786s("app_measurement_lite");
            this.f7652l = m4786s;
            if (m4786s == null) {
                this.f7652l = Boolean.FALSE;
            }
        }
        return this.f7652l.booleanValue() || !((C1948n4) this.f8145k).f7921n;
    }
}
