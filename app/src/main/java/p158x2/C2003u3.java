package p158x2;

import android.content.SharedPreferences;
import java.util.Objects;
import p153w3.C1798e;

/* renamed from: x2.u3 */
/* loaded from: classes.dex */
public final class C2003u3 {

    /* renamed from: a */
    public final String f8102a;

    /* renamed from: b */
    public final String f8103b;

    /* renamed from: c */
    public final String f8104c;

    /* renamed from: d */
    public final long f8105d;

    /* renamed from: e */
    public final /* synthetic */ C2027x3 f8106e;

    public /* synthetic */ C2003u3(C2027x3 c2027x3, long j6) {
        this.f8106e = c2027x3;
        C1798e.m4554o("health_monitor");
        C1798e.m4550m(j6 > 0);
        this.f8102a = "health_monitor:start";
        this.f8103b = "health_monitor:count";
        this.f8104c = "health_monitor:value";
        this.f8105d = j6;
    }

    /* renamed from: a */
    public final void m5100a() {
        this.f8106e.mo4915h();
        Objects.requireNonNull(((C1948n4) this.f8106e.f8145k).f7930w);
        long currentTimeMillis = System.currentTimeMillis();
        SharedPreferences.Editor edit = this.f8106e.m5145o().edit();
        edit.remove(this.f8103b);
        edit.remove(this.f8104c);
        edit.putLong(this.f8102a, currentTimeMillis);
        edit.apply();
    }
}
