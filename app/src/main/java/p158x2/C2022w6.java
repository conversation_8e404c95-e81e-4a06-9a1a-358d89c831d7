package p158x2;

import android.os.Parcel;
import android.os.Parcelable;
import p099o2.AbstractC1146a;
import p153w3.C1798e;

/* renamed from: x2.w6 */
/* loaded from: classes.dex */
public final class C2022w6 extends AbstractC1146a {
    public static final Parcelable.Creator<C2022w6> CREATOR = new C2030x6();

    /* renamed from: j */
    public final int f8148j;

    /* renamed from: k */
    public final String f8149k;

    /* renamed from: l */
    public final long f8150l;

    /* renamed from: m */
    public final Long f8151m;

    /* renamed from: n */
    public final String f8152n;

    /* renamed from: o */
    public final String f8153o;

    /* renamed from: p */
    public final Double f8154p;

    public C2022w6(int i6, String str, long j6, Long l6, Float f6, String str2, String str3, Double d6) {
        this.f8148j = i6;
        this.f8149k = str;
        this.f8150l = j6;
        this.f8151m = l6;
        if (i6 == 1) {
            this.f8154p = f6 != null ? Double.valueOf(f6.doubleValue()) : null;
        } else {
            this.f8154p = d6;
        }
        this.f8152n = str2;
        this.f8153o = str3;
    }

    public C2022w6(String str, long j6, Object obj, String str2) {
        C1798e.m4554o(str);
        this.f8148j = 2;
        this.f8149k = str;
        this.f8150l = j6;
        this.f8153o = str2;
        if (obj == null) {
            this.f8151m = null;
            this.f8154p = null;
            this.f8152n = null;
            return;
        }
        if (obj instanceof Long) {
            this.f8151m = (Long) obj;
            this.f8154p = null;
            this.f8152n = null;
        } else if (obj instanceof String) {
            this.f8151m = null;
            this.f8154p = null;
            this.f8152n = (String) obj;
        } else {
            if (!(obj instanceof Double)) {
                throw new IllegalArgumentException("User attribute given of un-supported type");
            }
            this.f8151m = null;
            this.f8154p = (Double) obj;
            this.f8152n = null;
        }
    }

    public C2022w6(C2038y6 c2038y6) {
        this(c2038y6.f8270c, c2038y6.f8271d, c2038y6.f8272e, c2038y6.f8269b);
    }

    /* renamed from: c */
    public final Object m5138c() {
        Long l6 = this.f8151m;
        if (l6 != null) {
            return l6;
        }
        Double d6 = this.f8154p;
        if (d6 != null) {
            return d6;
        }
        String str = this.f8152n;
        if (str != null) {
            return str;
        }
        return null;
    }

    @Override // android.os.Parcelable
    public final void writeToParcel(Parcel parcel, int i6) {
        C2030x6.m5156a(this, parcel);
    }
}
