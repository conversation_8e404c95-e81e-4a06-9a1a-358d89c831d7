package p158x2;

/* renamed from: x2.x5 */
/* loaded from: classes.dex */
public final class C2029x5 extends AbstractC1919k {

    /* renamed from: e */
    public final /* synthetic */ C1855c6 f8255e;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C2029x5(C1855c6 c1855c6, InterfaceC2036y4 interfaceC2036y4) {
        super(interfaceC2036y4);
        this.f8255e = c1855c6;
    }

    @Override // p158x2.AbstractC1919k
    /* renamed from: a */
    public final void mo4898a() {
        ((C1948n4) this.f8255e.f8145k).mo4962e().f7787s.m4841b("Tasks have been queued for a long time");
    }
}
