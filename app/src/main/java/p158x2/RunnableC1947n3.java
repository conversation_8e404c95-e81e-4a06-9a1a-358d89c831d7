package p158x2;

import java.net.URL;
import java.util.Map;
import p153w3.C1798e;

/* renamed from: x2.n3 */
/* loaded from: classes.dex */
public final class RunnableC1947n3 implements Runnable {

    /* renamed from: j */
    public final URL f7893j;

    /* renamed from: k */
    public final byte[] f7894k;

    /* renamed from: l */
    public final InterfaceC1931l3 f7895l;

    /* renamed from: m */
    public final String f7896m;

    /* renamed from: n */
    public final Map<String, String> f7897n;

    /* renamed from: o */
    public final /* synthetic */ C1955o3 f7898o;

    public RunnableC1947n3(C1955o3 c1955o3, String str, URL url, byte[] bArr, Map<String, String> map, InterfaceC1931l3 interfaceC1931l3) {
        this.f7898o = c1955o3;
        C1798e.m4554o(str);
        this.f7893j = url;
        this.f7894k = bArr;
        this.f7895l = interfaceC1931l3;
        this.f7896m = str;
        this.f7897n = map;
    }

    /* JADX WARN: Not initialized variable reg: 12, insn: 0x00d8: MOVE (r9 I:??[OBJECT, ARRAY]) = (r12 I:??[OBJECT, ARRAY]), block:B:73:0x00d6 */
    /* JADX WARN: Removed duplicated region for block: B:22:0x0150  */
    /* JADX WARN: Removed duplicated region for block: B:27:0x0133 A[EXC_TOP_SPLITTER, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:37:0x010e  */
    /* JADX WARN: Removed duplicated region for block: B:40:0x00f1 A[EXC_TOP_SPLITTER, SYNTHETIC] */
    @Override // java.lang.Runnable
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void run() {
        /*
            Method dump skipped, instructions count: 364
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.RunnableC1947n3.run():void");
    }
}
