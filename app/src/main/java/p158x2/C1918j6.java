package p158x2;

import android.os.Bundle;
import android.os.SystemClock;
import java.util.Objects;
import p152w2.C1695r8;

/* renamed from: x2.j6 */
/* loaded from: classes.dex */
public final class C1918j6 {

    /* renamed from: a */
    public long f7803a;

    /* renamed from: b */
    public long f7804b;

    /* renamed from: c */
    public final C2021w5 f7805c;

    /* renamed from: d */
    public final /* synthetic */ C1934l6 f7806d;

    public C1918j6(C1934l6 c1934l6) {
        this.f7806d = c1934l6;
        this.f7805c = new C2021w5(this, (C1948n4) c1934l6.f8145k, 1);
        Objects.requireNonNull(((C1948n4) c1934l6.f8145k).f7930w);
        long elapsedRealtime = SystemClock.elapsedRealtime();
        this.f7803a = elapsedRealtime;
        this.f7804b = elapsedRealtime;
    }

    /* renamed from: a */
    public final boolean m4897a(boolean z5, boolean z6, long j6) {
        this.f7806d.mo4915h();
        this.f7806d.m5102i();
        C1695r8.m4069b();
        if (!((C1948n4) this.f7806d.f8145k).f7923p.m4784q(null, C2026x2.f8201h0) || ((C1948n4) this.f7806d.f8145k).m4965i()) {
            C1995t3 c1995t3 = ((C1948n4) this.f7806d.f8145k).m4970q().f8252y;
            Objects.requireNonNull(((C1948n4) this.f7806d.f8145k).f7930w);
            c1995t3.m5051b(System.currentTimeMillis());
        }
        long j7 = j6 - this.f7803a;
        if (!z5 && j7 < 1000) {
            ((C1948n4) this.f7806d.f8145k).mo4962e().f7792x.m4842c("Screen exposed for less than 1000 ms. Event not sent. time", Long.valueOf(j7));
            return false;
        }
        if (!z6) {
            j7 = j6 - this.f7804b;
            this.f7804b = j6;
        }
        ((C1948n4) this.f7806d.f8145k).mo4962e().f7792x.m4842c("Recording user engagement, ms", Long.valueOf(j7));
        Bundle bundle = new Bundle();
        bundle.putLong("_et", j7);
        C1997t5.m5052q(((C1948n4) this.f7806d.f8145k).m4978y().m5056o(!((C1948n4) this.f7806d.f8145k).f7923p.m4788u()), bundle, true);
        C1866e c1866e = ((C1948n4) this.f7806d.f8145k).f7923p;
        C2010v2<Boolean> c2010v2 = C2026x2.f8174O;
        if (!c1866e.m4784q(null, c2010v2) && z6) {
            bundle.putLong("_fr", 1L);
        }
        if (!((C1948n4) this.f7806d.f8145k).f7923p.m4784q(null, c2010v2) || !z6) {
            ((C1948n4) this.f7806d.f8145k).m4972s().m4930s("auto", "_e", bundle);
        }
        this.f7803a = j6;
        this.f7805c.m4900c();
        this.f7805c.m4899b(3600000L);
        return true;
    }
}
