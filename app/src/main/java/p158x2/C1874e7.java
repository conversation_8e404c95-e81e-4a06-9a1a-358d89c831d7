package p158x2;

import java.util.ArrayList;
import java.util.BitSet;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import p077l.AbstractC1052f;
import p077l.C1047a;
import p152w2.C1482a2;
import p152w2.C1568h1;
import p152w2.C1580i1;
import p152w2.C1592j1;
import p152w2.C1604k1;
import p152w2.C1623l8;
import p152w2.C1760x1;
import p152w2.C1772y1;
import p152w2.C1784z1;

/* renamed from: x2.e7 */
/* loaded from: classes.dex */
public final class C1874e7 {

    /* renamed from: a */
    public String f7670a;

    /* renamed from: b */
    public boolean f7671b;

    /* renamed from: c */
    public C1772y1 f7672c;

    /* renamed from: d */
    public BitSet f7673d;

    /* renamed from: e */
    public BitSet f7674e;

    /* renamed from: f */
    public Map<Integer, Long> f7675f;

    /* renamed from: g */
    public Map<Integer, List<Long>> f7676g;

    /* renamed from: h */
    public final /* synthetic */ C1910i7 f7677h;

    public /* synthetic */ C1874e7(C1910i7 c1910i7, String str) {
        this.f7677h = c1910i7;
        this.f7670a = str;
        this.f7671b = true;
        this.f7673d = new BitSet();
        this.f7674e = new BitSet();
        this.f7675f = new C1047a();
        this.f7676g = new C1047a();
    }

    /* JADX WARN: Type inference failed for: r5v3, types: [java.util.Map<java.lang.Integer, java.util.List<java.lang.Long>>, l.g] */
    public /* synthetic */ C1874e7(C1910i7 c1910i7, String str, C1772y1 c1772y1, BitSet bitSet, BitSet bitSet2, Map map, Map map2) {
        this.f7677h = c1910i7;
        this.f7670a = str;
        this.f7673d = bitSet;
        this.f7674e = bitSet2;
        this.f7675f = map;
        this.f7676g = new C1047a();
        for (Integer num : map2.keySet()) {
            ArrayList arrayList = new ArrayList();
            arrayList.add((Long) map2.get(num));
            this.f7676g.put(num, arrayList);
        }
        this.f7671b = false;
        this.f7672c = c1772y1;
    }

    /* JADX WARN: Type inference failed for: r1v4, types: [java.util.Map<java.lang.Integer, java.util.List<java.lang.Long>>, l.g] */
    /* JADX WARN: Type inference failed for: r4v5, types: [java.util.Map<java.lang.Integer, java.util.List<java.lang.Long>>, l.g] */
    /* renamed from: a */
    public final void m4799a(AbstractC1892g7 abstractC1892g7) {
        int mo4810a = abstractC1892g7.mo4810a();
        Boolean bool = abstractC1892g7.f7725c;
        if (bool != null) {
            this.f7674e.set(mo4810a, bool.booleanValue());
        }
        Boolean bool2 = abstractC1892g7.f7726d;
        if (bool2 != null) {
            this.f7673d.set(mo4810a, bool2.booleanValue());
        }
        if (abstractC1892g7.f7727e != null) {
            Map<Integer, Long> map = this.f7675f;
            Integer valueOf = Integer.valueOf(mo4810a);
            Long l6 = map.get(valueOf);
            long longValue = abstractC1892g7.f7727e.longValue() / 1000;
            if (l6 == null || longValue > l6.longValue()) {
                this.f7675f.put(valueOf, Long.valueOf(longValue));
            }
        }
        if (abstractC1892g7.f7728f != null) {
            ?? r12 = this.f7676g;
            Integer valueOf2 = Integer.valueOf(mo4810a);
            List list = (List) r12.getOrDefault(valueOf2, null);
            if (list == null) {
                list = new ArrayList();
                this.f7676g.put(valueOf2, list);
            }
            if (abstractC1892g7.mo4811b()) {
                list.clear();
            }
            C1623l8.m3906b();
            C1866e c1866e = ((C1948n4) this.f7677h.f8145k).f7923p;
            String str = this.f7670a;
            C2010v2<Boolean> c2010v2 = C2026x2.f8179T;
            if (c1866e.m4784q(str, c2010v2) && abstractC1892g7.mo4812c()) {
                list.clear();
            }
            C1623l8.m3906b();
            boolean m4784q = ((C1948n4) this.f7677h.f8145k).f7923p.m4784q(this.f7670a, c2010v2);
            Long valueOf3 = Long.valueOf(abstractC1892g7.f7728f.longValue() / 1000);
            if (!m4784q) {
                list.add(valueOf3);
            } else {
                if (list.contains(valueOf3)) {
                    return;
                }
                list.add(valueOf3);
            }
        }
    }

    /* JADX WARN: Type inference failed for: r1v15, types: [java.util.Map<java.lang.Integer, java.util.List<java.lang.Long>>, l.g] */
    /* JADX WARN: Type inference failed for: r7v3, types: [java.util.Map<java.lang.Integer, java.util.List<java.lang.Long>>, l.g] */
    /* renamed from: b */
    public final C1580i1 m4800b(int i6) {
        ArrayList arrayList;
        List list;
        C1568h1 m3850z = C1580i1.m3850z();
        if (m3850z.f7100l) {
            m3850z.m3950i();
            m3850z.f7100l = false;
        }
        C1580i1.m3846B((C1580i1) m3850z.f7099k, i6);
        boolean z5 = this.f7671b;
        if (m3850z.f7100l) {
            m3850z.m3950i();
            m3850z.f7100l = false;
        }
        C1580i1.m3849E((C1580i1) m3850z.f7099k, z5);
        C1772y1 c1772y1 = this.f7672c;
        if (c1772y1 != null) {
            if (m3850z.f7100l) {
                m3850z.m3950i();
                m3850z.f7100l = false;
            }
            C1580i1.m3848D((C1580i1) m3850z.f7099k, c1772y1);
        }
        C1760x1 m4438C = C1772y1.m4438C();
        List<Long> m5110C = C2014v6.m5110C(this.f7673d);
        if (m4438C.f7100l) {
            m4438C.m3950i();
            m4438C.f7100l = false;
        }
        C1772y1.m4443H((C1772y1) m4438C.f7099k, m5110C);
        List<Long> m5110C2 = C2014v6.m5110C(this.f7674e);
        if (m4438C.f7100l) {
            m4438C.m3950i();
            m4438C.f7100l = false;
        }
        C1772y1.m4441F((C1772y1) m4438C.f7099k, m5110C2);
        Map<Integer, Long> map = this.f7675f;
        if (map == null) {
            arrayList = null;
        } else {
            arrayList = new ArrayList(map.size());
            Iterator<Integer> it = this.f7675f.keySet().iterator();
            while (it.hasNext()) {
                int intValue = it.next().intValue();
                Long l6 = this.f7675f.get(Integer.valueOf(intValue));
                if (l6 != null) {
                    C1592j1 m3875w = C1604k1.m3875w();
                    if (m3875w.f7100l) {
                        m3875w.m3950i();
                        m3875w.f7100l = false;
                    }
                    C1604k1.m3877y((C1604k1) m3875w.f7099k, intValue);
                    long longValue = l6.longValue();
                    if (m3875w.f7100l) {
                        m3875w.m3950i();
                        m3875w.f7100l = false;
                    }
                    C1604k1.m3878z((C1604k1) m3875w.f7099k, longValue);
                    arrayList.add(m3875w.m3947f());
                }
            }
        }
        if (arrayList != null) {
            if (m4438C.f7100l) {
                m4438C.m3950i();
                m4438C.f7100l = false;
            }
            C1772y1.m4445J((C1772y1) m4438C.f7099k, arrayList);
        }
        ?? r12 = this.f7676g;
        if (r12 == 0) {
            list = Collections.emptyList();
        } else {
            ArrayList arrayList2 = new ArrayList(r12.f5038l);
            Iterator it2 = ((AbstractC1052f.c) this.f7676g.keySet()).iterator();
            while (it2.hasNext()) {
                Integer num = (Integer) it2.next();
                C1784z1 m3614x = C1482a2.m3614x();
                int intValue2 = num.intValue();
                if (m3614x.f7100l) {
                    m3614x.m3950i();
                    m3614x.f7100l = false;
                }
                C1482a2.m3616z((C1482a2) m3614x.f7099k, intValue2);
                List list2 = (List) this.f7676g.getOrDefault(num, null);
                if (list2 != null) {
                    Collections.sort(list2);
                    if (m3614x.f7100l) {
                        m3614x.m3950i();
                        m3614x.f7100l = false;
                    }
                    C1482a2.m3613A((C1482a2) m3614x.f7099k, list2);
                }
                arrayList2.add(m3614x.m3947f());
            }
            list = arrayList2;
        }
        if (m4438C.f7100l) {
            m4438C.m3950i();
            m4438C.f7100l = false;
        }
        C1772y1.m4447L((C1772y1) m4438C.f7099k, list);
        if (m3850z.f7100l) {
            m3850z.m3950i();
            m3850z.f7100l = false;
        }
        C1580i1.m3847C((C1580i1) m3850z.f7099k, m4438C.m3947f());
        return m3850z.m3947f();
    }
}
