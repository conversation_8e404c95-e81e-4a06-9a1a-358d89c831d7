package p158x2;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

/* renamed from: x2.q3 */
/* loaded from: classes.dex */
public final class C1971q3 extends BroadcastReceiver {

    /* renamed from: a */
    public final C1998t6 f7972a;

    /* renamed from: b */
    public boolean f7973b;

    /* renamed from: c */
    public boolean f7974c;

    public C1971q3(C1998t6 c1998t6) {
        this.f7972a = c1998t6;
    }

    /* renamed from: a */
    public final void m4996a() {
        this.f7972a.m5074L();
        this.f7972a.mo4959b().mo4915h();
        this.f7972a.mo4959b().mo4915h();
        if (this.f7973b) {
            this.f7972a.mo4962e().f7792x.m4841b("Unregistering connectivity change receiver");
            this.f7973b = false;
            this.f7974c = false;
            try {
                this.f7972a.f8092t.f7917j.unregisterReceiver(this);
            } catch (IllegalArgumentException e6) {
                this.f7972a.mo4962e().f7784p.m4842c("Failed to unregister the network broadcast receiver", e6);
            }
        }
    }

    @Override // android.content.BroadcastReceiver
    public final void onReceive(Context context, Intent intent) {
        this.f7972a.m5074L();
        String action = intent.getAction();
        this.f7972a.mo4962e().f7792x.m4842c("NetworkBroadcastReceiver received action", action);
        if (!"android.net.conn.CONNECTIVITY_CHANGE".equals(action)) {
            this.f7972a.mo4962e().f7787s.m4842c("NetworkBroadcastReceiver received unknown action", action);
            return;
        }
        C1955o3 c1955o3 = this.f7972a.f8083k;
        C1998t6.m5060E(c1955o3);
        boolean m4990l = c1955o3.m4990l();
        if (this.f7974c != m4990l) {
            this.f7974c = m4990l;
            this.f7972a.mo4959b().m4918q(new RunnableC1963p3(this, m4990l));
        }
    }
}
