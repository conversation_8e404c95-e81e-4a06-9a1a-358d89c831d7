package p158x2;

import android.text.TextUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import p153w3.C1798e;

/* renamed from: x2.r4 */
/* loaded from: classes.dex */
public final class C1980r4 {

    /* renamed from: A */
    public long f7988A;

    /* renamed from: B */
    public long f7989B;

    /* renamed from: C */
    public String f7990C;

    /* renamed from: D */
    public boolean f7991D;

    /* renamed from: E */
    public long f7992E;

    /* renamed from: F */
    public long f7993F;

    /* renamed from: a */
    public final C1948n4 f7994a;

    /* renamed from: b */
    public final String f7995b;

    /* renamed from: c */
    public String f7996c;

    /* renamed from: d */
    public String f7997d;

    /* renamed from: e */
    public String f7998e;

    /* renamed from: f */
    public String f7999f;

    /* renamed from: g */
    public long f8000g;

    /* renamed from: h */
    public long f8001h;

    /* renamed from: i */
    public long f8002i;

    /* renamed from: j */
    public String f8003j;

    /* renamed from: k */
    public long f8004k;

    /* renamed from: l */
    public String f8005l;

    /* renamed from: m */
    public long f8006m;

    /* renamed from: n */
    public long f8007n;

    /* renamed from: o */
    public boolean f8008o;

    /* renamed from: p */
    public long f8009p;

    /* renamed from: q */
    public boolean f8010q;

    /* renamed from: r */
    public String f8011r;

    /* renamed from: s */
    public Boolean f8012s;

    /* renamed from: t */
    public long f8013t;

    /* renamed from: u */
    public List<String> f8014u;

    /* renamed from: v */
    public String f8015v;

    /* renamed from: w */
    public long f8016w;

    /* renamed from: x */
    public long f8017x;

    /* renamed from: y */
    public long f8018y;

    /* renamed from: z */
    public long f8019z;

    public C1980r4(C1948n4 c1948n4, String str) {
        Objects.requireNonNull(c1948n4, "null reference");
        C1798e.m4554o(str);
        this.f7994a = c1948n4;
        this.f7995b = str;
        c1948n4.mo4959b().mo4915h();
    }

    /* renamed from: A */
    public final void m5000A(String str) {
        this.f7994a.mo4959b().mo4915h();
        this.f7991D |= !C1838a7.m4701G(this.f7996c, str);
        this.f7996c = str;
    }

    /* renamed from: B */
    public final String m5001B() {
        this.f7994a.mo4959b().mo4915h();
        return this.f7997d;
    }

    /* renamed from: C */
    public final void m5002C(String str) {
        this.f7994a.mo4959b().mo4915h();
        if (true == TextUtils.isEmpty(str)) {
            str = null;
        }
        this.f7991D |= true ^ C1838a7.m4701G(this.f7997d, str);
        this.f7997d = str;
    }

    /* renamed from: D */
    public final String m5003D() {
        this.f7994a.mo4959b().mo4915h();
        return this.f8011r;
    }

    /* renamed from: E */
    public final void m5004E(String str) {
        this.f7994a.mo4959b().mo4915h();
        if (true == TextUtils.isEmpty(str)) {
            str = null;
        }
        this.f7991D |= true ^ C1838a7.m4701G(this.f8011r, str);
        this.f8011r = str;
    }

    /* renamed from: F */
    public final String m5005F() {
        this.f7994a.mo4959b().mo4915h();
        return this.f8015v;
    }

    /* renamed from: G */
    public final void m5006G(String str) {
        this.f7994a.mo4959b().mo4915h();
        if (true == TextUtils.isEmpty(str)) {
            str = null;
        }
        this.f7991D |= true ^ C1838a7.m4701G(this.f8015v, str);
        this.f8015v = str;
    }

    /* renamed from: H */
    public final String m5007H() {
        this.f7994a.mo4959b().mo4915h();
        return this.f7998e;
    }

    /* renamed from: I */
    public final void m5008I(String str) {
        this.f7994a.mo4959b().mo4915h();
        this.f7991D |= !C1838a7.m4701G(this.f7998e, str);
        this.f7998e = str;
    }

    /* renamed from: J */
    public final String m5009J() {
        this.f7994a.mo4959b().mo4915h();
        return this.f7999f;
    }

    /* renamed from: K */
    public final void m5010K(String str) {
        this.f7994a.mo4959b().mo4915h();
        this.f7991D |= !C1838a7.m4701G(this.f7999f, str);
        this.f7999f = str;
    }

    /* renamed from: L */
    public final long m5011L() {
        this.f7994a.mo4959b().mo4915h();
        return this.f8001h;
    }

    /* renamed from: M */
    public final void m5012M(long j6) {
        this.f7994a.mo4959b().mo4915h();
        this.f7991D |= this.f8001h != j6;
        this.f8001h = j6;
    }

    /* renamed from: N */
    public final long m5013N() {
        this.f7994a.mo4959b().mo4915h();
        return this.f8002i;
    }

    /* renamed from: O */
    public final void m5014O(long j6) {
        this.f7994a.mo4959b().mo4915h();
        this.f7991D |= this.f8002i != j6;
        this.f8002i = j6;
    }

    /* renamed from: P */
    public final String m5015P() {
        this.f7994a.mo4959b().mo4915h();
        return this.f8003j;
    }

    /* renamed from: Q */
    public final void m5016Q(String str) {
        this.f7994a.mo4959b().mo4915h();
        this.f7991D |= !C1838a7.m4701G(this.f8003j, str);
        this.f8003j = str;
    }

    /* renamed from: R */
    public final long m5017R() {
        this.f7994a.mo4959b().mo4915h();
        return this.f8004k;
    }

    /* renamed from: S */
    public final void m5018S(long j6) {
        this.f7994a.mo4959b().mo4915h();
        this.f7991D |= this.f8004k != j6;
        this.f8004k = j6;
    }

    /* renamed from: T */
    public final String m5019T() {
        this.f7994a.mo4959b().mo4915h();
        return this.f8005l;
    }

    /* renamed from: U */
    public final void m5020U(String str) {
        this.f7994a.mo4959b().mo4915h();
        this.f7991D |= !C1838a7.m4701G(this.f8005l, str);
        this.f8005l = str;
    }

    /* renamed from: V */
    public final long m5021V() {
        this.f7994a.mo4959b().mo4915h();
        return this.f8006m;
    }

    /* renamed from: a */
    public final void m5022a(long j6) {
        this.f7994a.mo4959b().mo4915h();
        this.f7991D |= this.f8006m != j6;
        this.f8006m = j6;
    }

    /* renamed from: b */
    public final long m5023b() {
        this.f7994a.mo4959b().mo4915h();
        return this.f8007n;
    }

    /* renamed from: c */
    public final void m5024c(long j6) {
        this.f7994a.mo4959b().mo4915h();
        this.f7991D |= this.f8007n != j6;
        this.f8007n = j6;
    }

    /* renamed from: d */
    public final long m5025d() {
        this.f7994a.mo4959b().mo4915h();
        return this.f8013t;
    }

    /* renamed from: e */
    public final void m5026e(long j6) {
        this.f7994a.mo4959b().mo4915h();
        this.f7991D |= this.f8013t != j6;
        this.f8013t = j6;
    }

    /* renamed from: f */
    public final boolean m5027f() {
        this.f7994a.mo4959b().mo4915h();
        return this.f8008o;
    }

    /* renamed from: g */
    public final void m5028g(boolean z5) {
        this.f7994a.mo4959b().mo4915h();
        this.f7991D |= this.f8008o != z5;
        this.f8008o = z5;
    }

    /* renamed from: h */
    public final void m5029h(long j6) {
        C1798e.m4550m(j6 >= 0);
        this.f7994a.mo4959b().mo4915h();
        this.f7991D |= this.f8000g != j6;
        this.f8000g = j6;
    }

    /* renamed from: i */
    public final long m5030i() {
        this.f7994a.mo4959b().mo4915h();
        return this.f8000g;
    }

    /* renamed from: j */
    public final long m5031j() {
        this.f7994a.mo4959b().mo4915h();
        return this.f7992E;
    }

    /* renamed from: k */
    public final void m5032k(long j6) {
        this.f7994a.mo4959b().mo4915h();
        this.f7991D |= this.f7992E != j6;
        this.f7992E = j6;
    }

    /* renamed from: l */
    public final long m5033l() {
        this.f7994a.mo4959b().mo4915h();
        return this.f7993F;
    }

    /* renamed from: m */
    public final void m5034m(long j6) {
        this.f7994a.mo4959b().mo4915h();
        this.f7991D |= this.f7993F != j6;
        this.f7993F = j6;
    }

    /* renamed from: n */
    public final void m5035n() {
        this.f7994a.mo4959b().mo4915h();
        long j6 = this.f8000g + 1;
        if (j6 > 2147483647L) {
            this.f7994a.mo4962e().f7787s.m4842c("Bundle index overflow. appId", C1915j3.m4886t(this.f7995b));
            j6 = 0;
        }
        this.f7991D = true;
        this.f8000g = j6;
    }

    /* renamed from: o */
    public final String m5036o() {
        this.f7994a.mo4959b().mo4915h();
        String str = this.f7990C;
        m5037p(null);
        return str;
    }

    /* renamed from: p */
    public final void m5037p(String str) {
        this.f7994a.mo4959b().mo4915h();
        this.f7991D |= !C1838a7.m4701G(this.f7990C, str);
        this.f7990C = str;
    }

    /* renamed from: q */
    public final long m5038q() {
        this.f7994a.mo4959b().mo4915h();
        return this.f8009p;
    }

    /* renamed from: r */
    public final void m5039r(long j6) {
        this.f7994a.mo4959b().mo4915h();
        this.f7991D |= this.f8009p != j6;
        this.f8009p = j6;
    }

    /* renamed from: s */
    public final boolean m5040s() {
        this.f7994a.mo4959b().mo4915h();
        return this.f8010q;
    }

    /* renamed from: t */
    public final void m5041t(boolean z5) {
        this.f7994a.mo4959b().mo4915h();
        this.f7991D |= this.f8010q != z5;
        this.f8010q = z5;
    }

    /* renamed from: u */
    public final Boolean m5042u() {
        this.f7994a.mo4959b().mo4915h();
        return this.f8012s;
    }

    /* renamed from: v */
    public final void m5043v(Boolean bool) {
        this.f7994a.mo4959b().mo4915h();
        boolean z5 = this.f7991D;
        Boolean bool2 = this.f8012s;
        this.f7991D = z5 | (!((bool2 == null && bool == null) ? true : bool2 == null ? false : bool2.equals(bool)));
        this.f8012s = bool;
    }

    /* renamed from: w */
    public final List<String> m5044w() {
        this.f7994a.mo4959b().mo4915h();
        return this.f8014u;
    }

    /* renamed from: x */
    public final void m5045x(List<String> list) {
        this.f7994a.mo4959b().mo4915h();
        List<String> list2 = this.f8014u;
        if (list2 == null && list == null) {
            return;
        }
        if (list2 != null && list2.equals(list)) {
            return;
        }
        this.f7991D = true;
        this.f8014u = list != null ? new ArrayList(list) : null;
    }

    /* renamed from: y */
    public final String m5046y() {
        this.f7994a.mo4959b().mo4915h();
        return this.f7995b;
    }

    /* renamed from: z */
    public final String m5047z() {
        this.f7994a.mo4959b().mo4915h();
        return this.f7996c;
    }
}
