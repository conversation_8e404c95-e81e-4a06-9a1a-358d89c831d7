package p158x2;

import java.util.concurrent.Callable;

/* renamed from: x2.p4 */
/* loaded from: classes.dex */
public final class CallableC1964p4 implements Callable {

    /* renamed from: a */
    public final /* synthetic */ int f7959a;

    /* renamed from: b */
    public final /* synthetic */ String f7960b;

    /* renamed from: c */
    public final /* synthetic */ String f7961c;

    /* renamed from: d */
    public final /* synthetic */ String f7962d;

    /* renamed from: e */
    public final /* synthetic */ BinderC2012v4 f7963e;

    public /* synthetic */ CallableC1964p4(BinderC2012v4 binderC2012v4, String str, String str2, String str3, int i6) {
        this.f7959a = i6;
        this.f7963e = binderC2012v4;
        this.f7960b = str;
        this.f7961c = str2;
        this.f7962d = str3;
    }

    @Override // java.util.concurrent.Callable
    public final Object call() {
        switch (this.f7959a) {
            case 0:
                this.f7963e.f8129a.m5086j();
                C1902i c1902i = this.f7963e.f8129a.f8084l;
                C1998t6.m5060E(c1902i);
                return c1902i.m4856H(this.f7960b, this.f7961c, this.f7962d);
            default:
                this.f7963e.f8129a.m5086j();
                C1902i c1902i2 = this.f7963e.f8129a.f8084l;
                C1998t6.m5060E(c1902i2);
                return c1902i2.m4860L(this.f7960b, this.f7961c, this.f7962d);
        }
    }
}
