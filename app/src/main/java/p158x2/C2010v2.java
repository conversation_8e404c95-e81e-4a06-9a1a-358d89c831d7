package p158x2;

import p008b0.C0385m;
import p110q.C1251d;

/* renamed from: x2.v2 */
/* loaded from: classes.dex */
public final class C2010v2<V> {

    /* renamed from: g */
    public static final Object f8121g = new Object();

    /* renamed from: a */
    public final String f8122a;

    /* renamed from: b */
    public final InterfaceC2002u2<V> f8123b;

    /* renamed from: c */
    public final V f8124c;

    /* renamed from: d */
    public final V f8125d;

    /* renamed from: e */
    public final Object f8126e = new Object();

    /* renamed from: f */
    public volatile V f8127f = null;

    /* JADX WARN: Multi-variable type inference failed */
    public /* synthetic */ C2010v2(String str, Object obj, Object obj2, InterfaceC2002u2 interfaceC2002u2) {
        this.f8122a = str;
        this.f8124c = obj;
        this.f8125d = obj2;
        this.f8123b = interfaceC2002u2;
    }

    /* renamed from: a */
    public final V m5101a(V v6) {
        synchronized (this.f8126e) {
        }
        if (v6 != null) {
            return v6;
        }
        if (C0385m.f2345j == null) {
            return this.f8124c;
        }
        synchronized (f8121g) {
            if (C1251d.m3139e()) {
                return this.f8127f == null ? this.f8124c : this.f8127f;
            }
            try {
                for (C2010v2<?> c2010v2 : C2026x2.f8186a) {
                    if (C1251d.m3139e()) {
                        throw new IllegalStateException("Refreshing flag cache must be done on a worker thread.");
                    }
                    V v7 = null;
                    try {
                        InterfaceC2002u2<?> interfaceC2002u2 = c2010v2.f8123b;
                        if (interfaceC2002u2 != null) {
                            v7 = (V) interfaceC2002u2.mo1434a();
                        }
                    } catch (IllegalStateException unused) {
                    }
                    synchronized (f8121g) {
                        c2010v2.f8127f = v7;
                    }
                }
            } catch (SecurityException unused2) {
            }
            InterfaceC2002u2<V> interfaceC2002u22 = this.f8123b;
            if (interfaceC2002u22 == null) {
                return this.f8124c;
            }
            try {
                return interfaceC2002u22.mo1434a();
            } catch (IllegalStateException unused3) {
                return this.f8124c;
            } catch (SecurityException unused4) {
                return this.f8124c;
            }
        }
    }
}
