package p158x2;

import android.os.Parcel;
import android.os.Parcelable;
import java.util.Objects;
import p099o2.AbstractC1146a;
import p153w3.C1798e;

/* renamed from: x2.b */
/* loaded from: classes.dex */
public final class C1839b extends AbstractC1146a {
    public static final Parcelable.Creator<C1839b> CREATOR = new C1848c();

    /* renamed from: j */
    public String f7560j;

    /* renamed from: k */
    public String f7561k;

    /* renamed from: l */
    public C2022w6 f7562l;

    /* renamed from: m */
    public long f7563m;

    /* renamed from: n */
    public boolean f7564n;

    /* renamed from: o */
    public String f7565o;

    /* renamed from: p */
    public final C1967q f7566p;

    /* renamed from: q */
    public long f7567q;

    /* renamed from: r */
    public C1967q f7568r;

    /* renamed from: s */
    public final long f7569s;

    /* renamed from: t */
    public final C1967q f7570t;

    public C1839b(String str, String str2, C2022w6 c2022w6, long j6, boolean z5, String str3, C1967q c1967q, long j7, C1967q c1967q2, long j8, C1967q c1967q3) {
        this.f7560j = str;
        this.f7561k = str2;
        this.f7562l = c2022w6;
        this.f7563m = j6;
        this.f7564n = z5;
        this.f7565o = str3;
        this.f7566p = c1967q;
        this.f7567q = j7;
        this.f7568r = c1967q2;
        this.f7569s = j8;
        this.f7570t = c1967q3;
    }

    public C1839b(C1839b c1839b) {
        Objects.requireNonNull(c1839b, "null reference");
        this.f7560j = c1839b.f7560j;
        this.f7561k = c1839b.f7561k;
        this.f7562l = c1839b.f7562l;
        this.f7563m = c1839b.f7563m;
        this.f7564n = c1839b.f7564n;
        this.f7565o = c1839b.f7565o;
        this.f7566p = c1839b.f7566p;
        this.f7567q = c1839b.f7567q;
        this.f7568r = c1839b.f7568r;
        this.f7569s = c1839b.f7569s;
        this.f7570t = c1839b.f7570t;
    }

    @Override // android.os.Parcelable
    public final void writeToParcel(Parcel parcel, int i6) {
        int m4526S = C1798e.m4526S(parcel, 20293);
        C1798e.m4524Q(parcel, 2, this.f7560j);
        C1798e.m4524Q(parcel, 3, this.f7561k);
        C1798e.m4523P(parcel, 4, this.f7562l, i6);
        long j6 = this.f7563m;
        C1798e.m4540g0(parcel, 5, 8);
        parcel.writeLong(j6);
        boolean z5 = this.f7564n;
        C1798e.m4540g0(parcel, 6, 4);
        parcel.writeInt(z5 ? 1 : 0);
        C1798e.m4524Q(parcel, 7, this.f7565o);
        C1798e.m4523P(parcel, 8, this.f7566p, i6);
        long j7 = this.f7567q;
        C1798e.m4540g0(parcel, 9, 8);
        parcel.writeLong(j7);
        C1798e.m4523P(parcel, 10, this.f7568r, i6);
        long j8 = this.f7569s;
        C1798e.m4540g0(parcel, 11, 8);
        parcel.writeLong(j8);
        C1798e.m4523P(parcel, 12, this.f7570t, i6);
        C1798e.m4539f0(parcel, m4526S);
    }
}
