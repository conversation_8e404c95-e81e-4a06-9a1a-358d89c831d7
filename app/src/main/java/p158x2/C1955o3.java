package p158x2;

import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.Objects;

/* renamed from: x2.o3 */
/* loaded from: classes.dex */
public final class C1955o3 extends AbstractC1966p6 {
    public C1955o3(C1998t6 c1998t6) {
        super(c1998t6);
    }

    @Override // p158x2.AbstractC1966p6
    /* renamed from: j */
    public final void mo4768j() {
    }

    /* renamed from: l */
    public final boolean m4990l() {
        m4994i();
        ConnectivityManager connectivityManager = (ConnectivityManager) ((C1948n4) this.f8145k).f7917j.getSystemService("connectivity");
        NetworkInfo networkInfo = null;
        if (connectivityManager != null) {
            try {
                networkInfo = connectivityManager.getActiveNetworkInfo();
            } catch (SecurityException unused) {
            }
        }
        return networkInfo != null && networkInfo.isConnected();
    }

    /* renamed from: m */
    public final HttpURLConnection m4991m(URL url) {
        URLConnection openConnection = url.openConnection();
        if (!(openConnection instanceof HttpURLConnection)) {
            throw new IOException("Failed to obtain HTTP connection");
        }
        HttpURLConnection httpURLConnection = (HttpURLConnection) openConnection;
        httpURLConnection.setDefaultUseCaches(false);
        Objects.requireNonNull((C1948n4) this.f8145k);
        httpURLConnection.setConnectTimeout(60000);
        Objects.requireNonNull((C1948n4) this.f8145k);
        httpURLConnection.setReadTimeout(61000);
        httpURLConnection.setInstanceFollowRedirects(false);
        httpURLConnection.setDoInput(true);
        return httpURLConnection;
    }
}
