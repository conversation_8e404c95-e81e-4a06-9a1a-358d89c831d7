package p158x2;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.Objects;

/* renamed from: x2.p5 */
/* loaded from: classes.dex */
public final class C1965p5 extends AbstractC2028x4 {
    public C1965p5(C1948n4 c1948n4) {
        super(c1948n4);
    }

    @Override // p158x2.AbstractC2028x4
    /* renamed from: i */
    public final boolean mo4731i() {
        return false;
    }

    /* renamed from: o */
    public final HttpURLConnection m4993o(URL url) {
        URLConnection openConnection = url.openConnection();
        if (!(openConnection instanceof HttpURLConnection)) {
            throw new IOException("Failed to obtain HTTP connection");
        }
        HttpURLConnection httpURLConnection = (HttpURLConnection) openConnection;
        httpURLConnection.setDefaultUseCaches(false);
        Objects.requireNonNull((C1948n4) this.f8145k);
        httpURLConnection.setConnectTimeout(60000);
        Objects.requireNonNull((C1948n4) this.f8145k);
        httpURLConnection.setReadTimeout(61000);
        httpURLConnection.setInstanceFollowRedirects(false);
        httpURLConnection.setDoInput(true);
        return httpURLConnection;
    }
}
