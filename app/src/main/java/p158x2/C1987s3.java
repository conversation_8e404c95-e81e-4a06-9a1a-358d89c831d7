package p158x2;

import android.os.Bundle;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import p153w3.C1798e;

/* renamed from: x2.s3 */
/* loaded from: classes.dex */
public final class C1987s3 {

    /* renamed from: a */
    public final String f8033a;

    /* renamed from: b */
    public final Bundle f8034b;

    /* renamed from: c */
    public Bundle f8035c;

    /* renamed from: d */
    public final /* synthetic */ C2027x3 f8036d;

    public C1987s3(C2027x3 c2027x3) {
        this.f8036d = c2027x3;
        C1798e.m4554o("default_event_parameters");
        this.f8033a = "default_event_parameters";
        this.f8034b = new Bundle();
    }

    /* renamed from: a */
    public final Bundle m5048a() {
        char c;
        if (this.f8035c == null) {
            String string = this.f8036d.m5145o().getString(this.f8033a, null);
            if (string != null) {
                try {
                    Bundle bundle = new Bundle();
                    JSONArray jSONArray = new JSONArray(string);
                    for (int i6 = 0; i6 < jSONArray.length(); i6++) {
                        try {
                            JSONObject jSONObject = jSONArray.getJSONObject(i6);
                            String string2 = jSONObject.getString("n");
                            String string3 = jSONObject.getString("t");
                            int hashCode = string3.hashCode();
                            if (hashCode == 100) {
                                if (string3.equals("d")) {
                                    c = 1;
                                }
                                c = 65535;
                            } else if (hashCode != 108) {
                                if (hashCode == 115 && string3.equals("s")) {
                                    c = 0;
                                }
                                c = 65535;
                            } else {
                                if (string3.equals("l")) {
                                    c = 2;
                                }
                                c = 65535;
                            }
                            if (c == 0) {
                                bundle.putString(string2, jSONObject.getString("v"));
                            } else if (c == 1) {
                                bundle.putDouble(string2, Double.parseDouble(jSONObject.getString("v")));
                            } else if (c != 2) {
                                ((C1948n4) this.f8036d.f8145k).mo4962e().f7784p.m4842c("Unrecognized persisted bundle type. Type", string3);
                            } else {
                                bundle.putLong(string2, Long.parseLong(jSONObject.getString("v")));
                            }
                        } catch (NumberFormatException | JSONException unused) {
                            ((C1948n4) this.f8036d.f8145k).mo4962e().f7784p.m4841b("Error reading value from SharedPreferences. Value dropped");
                        }
                    }
                    this.f8035c = bundle;
                } catch (JSONException unused2) {
                    ((C1948n4) this.f8036d.f8145k).mo4962e().f7784p.m4841b("Error loading bundle from SharedPreferences. Values will be lost");
                }
            }
            if (this.f8035c == null) {
                this.f8035c = this.f8034b;
            }
        }
        return this.f8035c;
    }
}
