package p158x2;

import android.content.ComponentName;
import android.content.Context;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Pair;
import java.util.Objects;
import p023d1.C0722t;

/* renamed from: x2.c5 */
/* loaded from: classes.dex */
public final class RunnableC1854c5 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f7617j;

    /* renamed from: k */
    public final Object f7618k;

    public /* synthetic */ RunnableC1854c5(Object obj, int i6) {
        this.f7617j = i6;
        this.f7618k = obj;
    }

    @Override // java.lang.Runnable
    public final void run() {
        switch (this.f7617j) {
            case 0:
                C1865d7 c1865d7 = ((C1933l5) this.f7618k).f7852w;
                c1865d7.f7651a.mo4959b().mo4915h();
                if (c1865d7.m4774c()) {
                    if (c1865d7.m4773b()) {
                        c1865d7.f7651a.m4970q().f8237E.m5137b(null);
                        Bundle bundle = new Bundle();
                        bundle.putString("source", "(not set)");
                        bundle.putString("medium", "(not set)");
                        bundle.putString("_cis", "intent");
                        bundle.putLong("_cc", 1L);
                        c1865d7.f7651a.m4972s().m4930s("auto", "_cmpx", bundle);
                    } else {
                        String m5136a = c1865d7.f7651a.m4970q().f8237E.m5136a();
                        if (TextUtils.isEmpty(m5136a)) {
                            c1865d7.f7651a.mo4962e().f7785q.m4841b("Cache still valid but referrer not found");
                        } else {
                            long m5050a = ((c1865d7.f7651a.m4970q().f8238F.m5050a() / 3600000) - 1) * 3600000;
                            Uri parse = Uri.parse(m5136a);
                            Bundle bundle2 = new Bundle();
                            Pair pair = new Pair(parse.getPath(), bundle2);
                            for (String str : parse.getQueryParameterNames()) {
                                bundle2.putString(str, parse.getQueryParameter(str));
                            }
                            ((Bundle) pair.second).putLong("_cc", m5050a);
                            c1865d7.f7651a.m4972s().m4930s((String) pair.first, "_cmp", (Bundle) pair.second);
                        }
                        c1865d7.f7651a.m4970q().f8237E.m5137b(null);
                    }
                    c1865d7.f7651a.m4970q().f8238F.m5051b(0L);
                    break;
                }
                break;
            case 1:
                C1997t5 c1997t5 = (C1997t5) this.f7618k;
                c1997t5.f8062o = c1997t5.f8067t;
                break;
            case 2:
                C1855c6 c1855c6 = ((ServiceConnectionC1846b6) this.f7618k).f7589c;
                Context context = ((C1948n4) c1855c6.f8145k).f7917j;
                Objects.requireNonNull((C1948n4) ((ServiceConnectionC1846b6) this.f7618k).f7589c.f8145k);
                C1855c6.m4750p(c1855c6, new ComponentName(context, "com.google.android.gms.measurement.AppMeasurementService"));
                break;
            default:
                RunnableC1909i6 runnableC1909i6 = (RunnableC1909i6) this.f7618k;
                C0722t c0722t = runnableC1909i6.f7769l;
                long j6 = runnableC1909i6.f7767j;
                long j7 = runnableC1909i6.f7768k;
                ((C1934l6) c0722t.f3916k).mo4915h();
                ((C1948n4) ((C1934l6) c0722t.f3916k).f8145k).mo4962e().f7791w.m4841b("Application going to the background");
                boolean z5 = true;
                if (((C1948n4) ((C1934l6) c0722t.f3916k).f8145k).f7923p.m4784q(null, C2026x2.f8209l0)) {
                    ((C1948n4) ((C1934l6) c0722t.f3916k).f8145k).m4970q().f8233A.m4999b(true);
                }
                Bundle bundle3 = new Bundle();
                if (!((C1948n4) ((C1934l6) c0722t.f3916k).f8145k).f7923p.m4788u()) {
                    ((C1934l6) c0722t.f3916k).f7857o.f7805c.m4900c();
                    if (((C1948n4) ((C1934l6) c0722t.f3916k).f8145k).f7923p.m4784q(null, C2026x2.f8193d0)) {
                        C1918j6 c1918j6 = ((C1934l6) c0722t.f3916k).f7857o;
                        long j8 = c1918j6.f7804b;
                        c1918j6.f7804b = j7;
                        bundle3.putLong("_et", j7 - j8);
                        C1997t5.m5052q(((C1948n4) ((C1934l6) c0722t.f3916k).f8145k).m4978y().m5056o(true), bundle3, true);
                    } else {
                        z5 = false;
                    }
                    ((C1934l6) c0722t.f3916k).f7857o.m4897a(false, z5, j7);
                }
                ((C1948n4) ((C1934l6) c0722t.f3916k).f8145k).m4972s().m4931t("auto", "_ab", j6, bundle3);
                break;
        }
    }
}
