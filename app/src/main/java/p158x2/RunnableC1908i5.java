package p158x2;

/* renamed from: x2.i5 */
/* loaded from: classes.dex */
public final class RunnableC1908i5 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ C1875f f7762j;

    /* renamed from: k */
    public final /* synthetic */ int f7763k;

    /* renamed from: l */
    public final /* synthetic */ long f7764l;

    /* renamed from: m */
    public final /* synthetic */ boolean f7765m;

    /* renamed from: n */
    public final /* synthetic */ C1933l5 f7766n;

    public RunnableC1908i5(C1933l5 c1933l5, C1875f c1875f, int i6, long j6, boolean z5) {
        this.f7766n = c1933l5;
        this.f7762j = c1875f;
        this.f7763k = i6;
        this.f7764l = j6;
        this.f7765m = z5;
    }

    @Override // java.lang.Runnable
    public final void run() {
        this.f7766n.m4929r(this.f7762j);
        C1933l5.m4923p(this.f7766n, this.f7762j, this.f7763k, this.f7764l, false, this.f7765m);
    }
}
