package p158x2;

import android.os.Parcel;
import android.os.Parcelable;
import java.util.ArrayList;
import p099o2.C1147b;

/* renamed from: x2.c7 */
/* loaded from: classes.dex */
public final class C1856c7 implements Parcelable.Creator<C1847b7> {
    @Override // android.os.Parcelable.Creator
    public final C1847b7 createFromParcel(Parcel parcel) {
        int m2977l = C1147b.m2977l(parcel);
        boolean z5 = true;
        boolean z6 = true;
        boolean z7 = false;
        int i6 = 0;
        boolean z8 = false;
        long j6 = 0;
        long j7 = 0;
        long j8 = 0;
        long j9 = 0;
        long j10 = 0;
        String str = null;
        String str2 = null;
        String str3 = null;
        String str4 = null;
        String str5 = null;
        String str6 = null;
        String str7 = null;
        Boolean bool = null;
        ArrayList<String> arrayList = null;
        String str8 = null;
        String str9 = "";
        long j11 = -2147483648L;
        while (parcel.dataPosition() < m2977l) {
            int readInt = parcel.readInt();
            switch (65535 & readInt) {
                case 2:
                    str = C1147b.m2968c(parcel, readInt);
                    break;
                case 3:
                    str2 = C1147b.m2968c(parcel, readInt);
                    break;
                case 4:
                    str3 = C1147b.m2968c(parcel, readInt);
                    break;
                case 5:
                    str4 = C1147b.m2968c(parcel, readInt);
                    break;
                case 6:
                    j6 = C1147b.m2974i(parcel, readInt);
                    break;
                case 7:
                    j7 = C1147b.m2974i(parcel, readInt);
                    break;
                case 8:
                    str5 = C1147b.m2968c(parcel, readInt);
                    break;
                case 9:
                    z5 = C1147b.m2971f(parcel, readInt);
                    break;
                case 10:
                    z7 = C1147b.m2971f(parcel, readInt);
                    break;
                case 11:
                    j11 = C1147b.m2974i(parcel, readInt);
                    break;
                case 12:
                    str6 = C1147b.m2968c(parcel, readInt);
                    break;
                case 13:
                    j8 = C1147b.m2974i(parcel, readInt);
                    break;
                case 14:
                    j9 = C1147b.m2974i(parcel, readInt);
                    break;
                case 15:
                    i6 = C1147b.m2973h(parcel, readInt);
                    break;
                case 16:
                    z6 = C1147b.m2971f(parcel, readInt);
                    break;
                case 17:
                case 20:
                default:
                    C1147b.m2976k(parcel, readInt);
                    break;
                case 18:
                    z8 = C1147b.m2971f(parcel, readInt);
                    break;
                case 19:
                    str7 = C1147b.m2968c(parcel, readInt);
                    break;
                case 21:
                    int m2975j = C1147b.m2975j(parcel, readInt);
                    if (m2975j != 0) {
                        C1147b.m2979n(parcel, m2975j, 4);
                        bool = Boolean.valueOf(parcel.readInt() != 0);
                        break;
                    } else {
                        bool = null;
                        break;
                    }
                case 22:
                    j10 = C1147b.m2974i(parcel, readInt);
                    break;
                case 23:
                    int m2975j2 = C1147b.m2975j(parcel, readInt);
                    int dataPosition = parcel.dataPosition();
                    if (m2975j2 != 0) {
                        arrayList = parcel.createStringArrayList();
                        parcel.setDataPosition(dataPosition + m2975j2);
                        break;
                    } else {
                        arrayList = null;
                        break;
                    }
                case 24:
                    str8 = C1147b.m2968c(parcel, readInt);
                    break;
                case 25:
                    str9 = C1147b.m2968c(parcel, readInt);
                    break;
            }
        }
        C1147b.m2970e(parcel, m2977l);
        return new C1847b7(str, str2, str3, str4, j6, j7, str5, z5, z7, j11, str6, j8, j9, i6, z6, z8, str7, bool, j10, arrayList, str8, str9);
    }

    @Override // android.os.Parcelable.Creator
    public final /* bridge */ /* synthetic */ C1847b7[] newArray(int i6) {
        return new C1847b7[i6];
    }
}
