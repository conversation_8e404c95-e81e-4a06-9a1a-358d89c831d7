package p158x2;

import android.annotation.TargetApi;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.app.job.JobScheduler;
import android.content.Context;
import android.content.Intent;
import p152w2.C1555g0;

/* renamed from: x2.n6 */
/* loaded from: classes.dex */
public final class C1950n6 extends AbstractC1966p6 {

    /* renamed from: n */
    public final AlarmManager f7939n;

    /* renamed from: o */
    public C2021w5 f7940o;

    /* renamed from: p */
    public Integer f7941p;

    public C1950n6(C1998t6 c1998t6) {
        super(c1998t6);
        this.f7939n = (AlarmManager) ((C1948n4) this.f8145k).f7917j.getSystemService("alarm");
    }

    @Override // p158x2.AbstractC1966p6
    /* renamed from: j */
    public final void mo4768j() {
        AlarmManager alarmManager = this.f7939n;
        if (alarmManager != null) {
            alarmManager.cancel(m4984p());
        }
        m4982n();
    }

    /* renamed from: l */
    public final void m4980l() {
        m4994i();
        ((C1948n4) this.f8145k).mo4962e().f7792x.m4841b("Unscheduling upload");
        AlarmManager alarmManager = this.f7939n;
        if (alarmManager != null) {
            alarmManager.cancel(m4984p());
        }
        m4981m().m4900c();
        m4982n();
    }

    /* renamed from: m */
    public final AbstractC1919k m4981m() {
        if (this.f7940o == null) {
            this.f7940o = new C2021w5(this, this.f7954l.f8092t, 2);
        }
        return this.f7940o;
    }

    @TargetApi(24)
    /* renamed from: n */
    public final void m4982n() {
        JobScheduler jobScheduler = (JobScheduler) ((C1948n4) this.f8145k).f7917j.getSystemService("jobscheduler");
        if (jobScheduler != null) {
            jobScheduler.cancel(m4983o());
        }
    }

    /* renamed from: o */
    public final int m4983o() {
        if (this.f7941p == null) {
            String valueOf = String.valueOf(((C1948n4) this.f8145k).f7917j.getPackageName());
            this.f7941p = Integer.valueOf((valueOf.length() != 0 ? "measurement".concat(valueOf) : new String("measurement")).hashCode());
        }
        return this.f7941p.intValue();
    }

    /* renamed from: p */
    public final PendingIntent m4984p() {
        Context context = ((C1948n4) this.f8145k).f7917j;
        return PendingIntent.getBroadcast(context, 0, new Intent().setClassName(context, "com.google.android.gms.measurement.AppMeasurementReceiver").setAction("com.google.android.gms.measurement.UPLOAD"), C1555g0.f6980a);
    }
}
