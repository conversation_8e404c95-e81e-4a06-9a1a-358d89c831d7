package p158x2;

import java.util.concurrent.Callable;

/* renamed from: x2.q4 */
/* loaded from: classes.dex */
public final class CallableC1972q4 implements Callable {

    /* renamed from: a */
    public final /* synthetic */ int f7975a;

    /* renamed from: b */
    public final /* synthetic */ String f7976b;

    /* renamed from: c */
    public final /* synthetic */ String f7977c;

    /* renamed from: d */
    public final /* synthetic */ String f7978d;

    /* renamed from: e */
    public final /* synthetic */ BinderC2012v4 f7979e;

    public /* synthetic */ CallableC1972q4(BinderC2012v4 binderC2012v4, String str, String str2, String str3, int i6) {
        this.f7975a = i6;
        this.f7979e = binderC2012v4;
        this.f7976b = str;
        this.f7977c = str2;
        this.f7978d = str3;
    }

    @Override // java.util.concurrent.Callable
    public final Object call() {
        switch (this.f7975a) {
            case 0:
                this.f7979e.f8129a.m5086j();
                C1902i c1902i = this.f7979e.f8129a.f8084l;
                C1998t6.m5060E(c1902i);
                return c1902i.m4856H(this.f7976b, this.f7977c, this.f7978d);
            default:
                this.f7979e.f8129a.m5086j();
                C1902i c1902i2 = this.f7979e.f8129a.f8084l;
                C1998t6.m5060E(c1902i2);
                return c1902i2.m4860L(this.f7976b, this.f7977c, this.f7978d);
        }
    }
}
