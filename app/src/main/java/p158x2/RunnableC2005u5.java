package p158x2;

import p153w3.C1798e;

/* renamed from: x2.u5 */
/* loaded from: classes.dex */
public final class RunnableC2005u5 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ C1847b7 f8113j;

    /* renamed from: k */
    public final /* synthetic */ boolean f8114k;

    /* renamed from: l */
    public final /* synthetic */ C2022w6 f8115l;

    /* renamed from: m */
    public final /* synthetic */ C1855c6 f8116m;

    public RunnableC2005u5(C1855c6 c1855c6, C1847b7 c1847b7, boolean z5, C2022w6 c2022w6) {
        this.f8116m = c1855c6;
        this.f8113j = c1847b7;
        this.f8114k = z5;
        this.f8115l = c2022w6;
    }

    @Override // java.lang.Runnable
    public final void run() {
        C1855c6 c1855c6 = this.f8116m;
        InterfaceC1834a3 interfaceC1834a3 = c1855c6.f7620n;
        if (interfaceC1834a3 == null) {
            ((C1948n4) c1855c6.f8145k).mo4962e().f7784p.m4841b("Discarding data. Failed to set user property");
            return;
        }
        C1798e.m4560r(this.f8113j);
        this.f8116m.m4761w(interfaceC1834a3, this.f8114k ? null : this.f8115l, this.f8113j);
        this.f8116m.m4756r();
    }
}
