package p158x2;

import android.app.ActivityManager;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.TextUtils;
import java.util.Objects;
import p152w2.C1551f8;

/* renamed from: x2.k6 */
/* loaded from: classes.dex */
public final class C1926k6 {

    /* renamed from: a */
    public final /* synthetic */ C1934l6 f7824a;

    public C1926k6(C1934l6 c1934l6) {
        this.f7824a = c1934l6;
    }

    /* renamed from: a */
    public final void m4907a() {
        this.f7824a.mo4915h();
        C2027x3 m4970q = ((C1948n4) this.f7824a.f8145k).m4970q();
        Objects.requireNonNull(((C1948n4) this.f7824a.f8145k).f7930w);
        if (m4970q.m5151u(System.currentTimeMillis())) {
            ((C1948n4) this.f7824a.f8145k).m4970q().f8249v.m4999b(true);
            ActivityManager.RunningAppProcessInfo runningAppProcessInfo = new ActivityManager.RunningAppProcessInfo();
            ActivityManager.getMyMemoryState(runningAppProcessInfo);
            if (runningAppProcessInfo.importance == 100) {
                ((C1948n4) this.f7824a.f8145k).mo4962e().f7792x.m4841b("Detected application was in foreground");
                Objects.requireNonNull(((C1948n4) this.f7824a.f8145k).f7930w);
                m4909c(System.currentTimeMillis(), false);
            }
        }
    }

    /* renamed from: b */
    public final void m4908b(long j6, boolean z5) {
        this.f7824a.mo4915h();
        this.f7824a.m4935l();
        if (((C1948n4) this.f7824a.f8145k).m4970q().m5151u(j6)) {
            ((C1948n4) this.f7824a.f8145k).m4970q().f8249v.m4999b(true);
        }
        ((C1948n4) this.f7824a.f8145k).m4970q().f8252y.m5051b(j6);
        if (((C1948n4) this.f7824a.f8145k).m4970q().f8249v.m4998a()) {
            m4909c(j6, z5);
        }
    }

    /* renamed from: c */
    public final void m4909c(long j6, boolean z5) {
        this.f7824a.mo4915h();
        if (((C1948n4) this.f7824a.f8145k).m4965i()) {
            ((C1948n4) this.f7824a.f8145k).m4970q().f8252y.m5051b(j6);
            Objects.requireNonNull(((C1948n4) this.f7824a.f8145k).f7930w);
            ((C1948n4) this.f7824a.f8145k).mo4962e().f7792x.m4842c("Session started, time", Long.valueOf(SystemClock.elapsedRealtime()));
            Long valueOf = Long.valueOf(j6 / 1000);
            ((C1948n4) this.f7824a.f8145k).m4972s().m4925m("auto", "_sid", valueOf, j6);
            ((C1948n4) this.f7824a.f8145k).m4970q().f8249v.m4999b(false);
            Bundle bundle = new Bundle();
            bundle.putLong("_sid", valueOf.longValue());
            if (((C1948n4) this.f7824a.f8145k).f7923p.m4784q(null, C2026x2.f8189b0) && z5) {
                bundle.putLong("_aib", 1L);
            }
            ((C1948n4) this.f7824a.f8145k).m4972s().m4931t("auto", "_s", j6, bundle);
            C1551f8.m3824b();
            if (((C1948n4) this.f7824a.f8145k).f7923p.m4784q(null, C2026x2.f8199g0)) {
                String m5136a = ((C1948n4) this.f7824a.f8145k).m4970q().f8236D.m5136a();
                if (TextUtils.isEmpty(m5136a)) {
                    return;
                }
                Bundle bundle2 = new Bundle();
                bundle2.putString("_ffr", m5136a);
                ((C1948n4) this.f7824a.f8145k).m4972s().m4931t("auto", "_ssr", j6, bundle2);
            }
        }
    }
}
