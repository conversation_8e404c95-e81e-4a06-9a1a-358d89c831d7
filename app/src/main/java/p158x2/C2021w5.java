package p158x2;

import android.os.SystemClock;
import java.util.Objects;

/* renamed from: x2.w5 */
/* loaded from: classes.dex */
public final class C2021w5 extends AbstractC1919k {

    /* renamed from: e */
    public final /* synthetic */ int f8146e;

    /* renamed from: f */
    public final /* synthetic */ Object f8147f;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public /* synthetic */ C2021w5(Object obj, InterfaceC2036y4 interfaceC2036y4, int i6) {
        super(interfaceC2036y4);
        this.f8146e = i6;
        this.f8147f = obj;
    }

    @Override // p158x2.AbstractC1919k
    /* renamed from: a */
    public final void mo4898a() {
        switch (this.f8146e) {
            case 0:
                C1855c6 c1855c6 = (C1855c6) this.f8147f;
                c1855c6.mo4915h();
                if (c1855c6.m4760v()) {
                    ((C1948n4) c1855c6.f8145k).mo4962e().f7792x.m4841b("Inactivity, disconnecting from the service");
                    c1855c6.m4753n();
                    break;
                }
                break;
            case 1:
                C1918j6 c1918j6 = (C1918j6) this.f8147f;
                c1918j6.f7806d.mo4915h();
                Objects.requireNonNull(((C1948n4) c1918j6.f7806d.f8145k).f7930w);
                c1918j6.m4897a(false, false, SystemClock.elapsedRealtime());
                C2025x1 m4964g = ((C1948n4) c1918j6.f7806d.f8145k).m4964g();
                Objects.requireNonNull(((C1948n4) c1918j6.f7806d.f8145k).f7930w);
                m4964g.m5139i(SystemClock.elapsedRealtime());
                break;
            default:
                ((C1950n6) this.f8147f).m4980l();
                ((C1948n4) ((C1950n6) this.f8147f).f8145k).mo4962e().f7792x.m4841b("Starting upload from DelayedRunnable");
                ((C1950n6) this.f8147f).f7954l.m5083g();
                break;
        }
    }
}
