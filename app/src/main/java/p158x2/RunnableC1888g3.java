package p158x2;

import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.util.Log;
import java.util.Objects;
import p113q2.C1277e;

/* renamed from: x2.g3 */
/* loaded from: classes.dex */
public final class RunnableC1888g3 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f7701j;

    /* renamed from: k */
    public final /* synthetic */ String f7702k;

    /* renamed from: l */
    public final /* synthetic */ Object f7703l;

    /* renamed from: m */
    public final /* synthetic */ Object f7704m;

    /* renamed from: n */
    public final /* synthetic */ Object f7705n;

    /* renamed from: o */
    public final /* synthetic */ C1915j3 f7706o;

    public RunnableC1888g3(C1915j3 c1915j3, int i6, String str, Object obj, Object obj2, Object obj3) {
        this.f7706o = c1915j3;
        this.f7701j = i6;
        this.f7702k = str;
        this.f7703l = obj;
        this.f7704m = obj2;
        this.f7705n = obj3;
    }

    @Override // java.lang.Runnable
    public final void run() {
        C1915j3 c1915j3;
        char c;
        C2027x3 m4970q = ((C1948n4) this.f7706o.f8145k).m4970q();
        if (!m4970q.m5152k()) {
            Log.println(6, this.f7706o.m4896v(), "Persisted config not initialized. Not logging error/warn");
            return;
        }
        C1915j3 c1915j32 = this.f7706o;
        if (c1915j32.f7781m == 0) {
            C1866e c1866e = ((C1948n4) c1915j32.f8145k).f7923p;
            if (c1866e.f7654n == null) {
                synchronized (c1866e) {
                    if (c1866e.f7654n == null) {
                        ApplicationInfo applicationInfo = ((C1948n4) c1866e.f8145k).f7917j.getApplicationInfo();
                        String m3165a = C1277e.m3165a();
                        if (applicationInfo != null) {
                            String str = applicationInfo.processName;
                            c1866e.f7654n = Boolean.valueOf(str != null && str.equals(m3165a));
                        }
                        if (c1866e.f7654n == null) {
                            c1866e.f7654n = Boolean.TRUE;
                            ((C1948n4) c1866e.f8145k).mo4962e().f7784p.m4841b("My process not in the list of running processes");
                        }
                    }
                }
            }
            if (c1866e.f7654n.booleanValue()) {
                c1915j3 = this.f7706o;
                Objects.requireNonNull((C1948n4) c1915j3.f8145k);
                c = 'C';
            } else {
                c1915j3 = this.f7706o;
                Objects.requireNonNull((C1948n4) c1915j3.f8145k);
                c = 'c';
            }
            c1915j3.f7781m = c;
        }
        C1915j3 c1915j33 = this.f7706o;
        if (c1915j33.f7782n < 0) {
            ((C1948n4) c1915j33.f8145k).f7923p.m4780m();
            c1915j33.f7782n = 42004L;
        }
        char charAt = "01VDIWEA?".charAt(this.f7701j);
        C1915j3 c1915j34 = this.f7706o;
        char c6 = c1915j34.f7781m;
        long j6 = c1915j34.f7782n;
        String m4887w = C1915j3.m4887w(true, this.f7702k, this.f7703l, this.f7704m, this.f7705n);
        StringBuilder sb = new StringBuilder(String.valueOf(m4887w).length() + 24);
        sb.append("2");
        sb.append(charAt);
        sb.append(c6);
        sb.append(j6);
        sb.append(":");
        sb.append(m4887w);
        String sb2 = sb.toString();
        if (sb2.length() > 1024) {
            sb2 = this.f7702k.substring(0, 1024);
        }
        C2003u3 c2003u3 = m4970q.f8241n;
        if (c2003u3 != null) {
            c2003u3.f8106e.mo4915h();
            if (c2003u3.f8106e.m5145o().getLong(c2003u3.f8102a, 0L) == 0) {
                c2003u3.m5100a();
            }
            if (sb2 == null) {
                sb2 = "";
            }
            long j7 = c2003u3.f8106e.m5145o().getLong(c2003u3.f8103b, 0L);
            if (j7 <= 0) {
                SharedPreferences.Editor edit = c2003u3.f8106e.m5145o().edit();
                edit.putString(c2003u3.f8104c, sb2);
                edit.putLong(c2003u3.f8103b, 1L);
                edit.apply();
                return;
            }
            long nextLong = ((C1948n4) c2003u3.f8106e.f8145k).m4973t().m4720W().nextLong();
            long j8 = j7 + 1;
            long j9 = Long.MAX_VALUE / j8;
            SharedPreferences.Editor edit2 = c2003u3.f8106e.m5145o().edit();
            if ((Long.MAX_VALUE & nextLong) < j9) {
                edit2.putString(c2003u3.f8104c, sb2);
            }
            edit2.putLong(c2003u3.f8103b, j8);
            edit2.apply();
        }
    }
}
