package p158x2;

import java.util.concurrent.Callable;
import java.util.concurrent.FutureTask;

/* renamed from: x2.j4 */
/* loaded from: classes.dex */
public final class C1916j4<V> extends FutureTask<V> implements Comparable<C1916j4<V>> {

    /* renamed from: j */
    public final long f7793j;

    /* renamed from: k */
    public final boolean f7794k;

    /* renamed from: l */
    public final String f7795l;

    /* renamed from: m */
    public final /* synthetic */ C1932l4 f7796m;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C1916j4(C1932l4 c1932l4, Runnable runnable, boolean z5, String str) {
        super(runnable, null);
        this.f7796m = c1932l4;
        long andIncrement = C1932l4.f7833u.getAndIncrement();
        this.f7793j = andIncrement;
        this.f7795l = str;
        this.f7794k = z5;
        if (andIncrement == Long.MAX_VALUE) {
            ((C1948n4) c1932l4.f8145k).mo4962e().f7784p.m4841b("Tasks index overflow");
        }
    }

    @Override // java.lang.Comparable
    public final int compareTo(Object obj) {
        C1916j4 c1916j4 = (C1916j4) obj;
        boolean z5 = this.f7794k;
        if (z5 != c1916j4.f7794k) {
            return !z5 ? 1 : -1;
        }
        long j6 = this.f7793j;
        long j7 = c1916j4.f7793j;
        if (j6 < j7) {
            return -1;
        }
        if (j6 > j7) {
            return 1;
        }
        ((C1948n4) this.f7796m.f8145k).mo4962e().f7785q.m4842c("Two tasks share the same index. index", Long.valueOf(this.f7793j));
        return 0;
    }

    @Override // java.util.concurrent.FutureTask
    public final void setException(Throwable th) {
        ((C1948n4) this.f7796m.f8145k).mo4962e().f7784p.m4842c(this.f7795l, th);
        super.setException(th);
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C1916j4(C1932l4 c1932l4, Callable callable, boolean z5) {
        super(callable);
        this.f7796m = c1932l4;
        long andIncrement = C1932l4.f7833u.getAndIncrement();
        this.f7793j = andIncrement;
        this.f7795l = "Task exception on worker thread";
        this.f7794k = z5;
        if (andIncrement == Long.MAX_VALUE) {
            ((C1948n4) c1932l4.f8145k).mo4962e().f7784p.m4841b("Tasks index overflow");
        }
    }
}
