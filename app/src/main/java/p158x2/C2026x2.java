package p158x2;

import android.content.Context;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import p008b0.C0385m;
import p152w2.C1629m2;
import p152w2.C1713t2;
import p153w3.C1798e;

/* renamed from: x2.x2 */
/* loaded from: classes.dex */
public final class C2026x2 {

    /* renamed from: A */
    public static final C2010v2<Long> f8160A;

    /* renamed from: B */
    public static final C2010v2<Integer> f8161B;

    /* renamed from: C */
    public static final C2010v2<Long> f8162C;

    /* renamed from: D */
    public static final C2010v2<Integer> f8163D;

    /* renamed from: E */
    public static final C2010v2<Integer> f8164E;

    /* renamed from: F */
    public static final C2010v2<Integer> f8165F;

    /* renamed from: G */
    public static final C2010v2<Integer> f8166G;

    /* renamed from: H */
    public static final C2010v2<Integer> f8167H;

    /* renamed from: I */
    public static final C2010v2<Long> f8168I;

    /* renamed from: J */
    public static final C2010v2<Integer> f8169J;

    /* renamed from: K */
    public static final C2010v2<Integer> f8170K;

    /* renamed from: L */
    public static final C2010v2<Long> f8171L;

    /* renamed from: M */
    public static final C2010v2<Boolean> f8172M;

    /* renamed from: N */
    public static final C2010v2<Boolean> f8173N;

    /* renamed from: O */
    public static final C2010v2<Boolean> f8174O;

    /* renamed from: P */
    public static final C2010v2<Boolean> f8175P;

    /* renamed from: Q */
    public static final C2010v2<Boolean> f8176Q;

    /* renamed from: R */
    public static final C2010v2<Boolean> f8177R;

    /* renamed from: S */
    public static final C2010v2<Boolean> f8178S;

    /* renamed from: T */
    public static final C2010v2<Boolean> f8179T;

    /* renamed from: U */
    public static final C2010v2<Boolean> f8180U;

    /* renamed from: V */
    public static final C2010v2<Boolean> f8181V;

    /* renamed from: W */
    public static final C2010v2<Boolean> f8182W;

    /* renamed from: X */
    public static final C2010v2<Boolean> f8183X;

    /* renamed from: Y */
    public static final C2010v2<Boolean> f8184Y;

    /* renamed from: Z */
    public static final C2010v2<Boolean> f8185Z;

    /* renamed from: a */
    public static final List<C2010v2<?>> f8186a = Collections.synchronizedList(new ArrayList());

    /* renamed from: a0 */
    public static final C2010v2<Boolean> f8187a0;

    /* renamed from: b */
    public static final C2010v2<Long> f8188b;

    /* renamed from: b0 */
    public static final C2010v2<Boolean> f8189b0;

    /* renamed from: c */
    public static final C2010v2<Long> f8190c;

    /* renamed from: c0 */
    public static final C2010v2<Boolean> f8191c0;

    /* renamed from: d */
    public static final C2010v2<Long> f8192d;

    /* renamed from: d0 */
    public static final C2010v2<Boolean> f8193d0;

    /* renamed from: e */
    public static final C2010v2<String> f8194e;

    /* renamed from: e0 */
    public static final C2010v2<Boolean> f8195e0;

    /* renamed from: f */
    public static final C2010v2<String> f8196f;

    /* renamed from: f0 */
    public static final C2010v2<Boolean> f8197f0;

    /* renamed from: g */
    public static final C2010v2<Integer> f8198g;

    /* renamed from: g0 */
    public static final C2010v2<Boolean> f8199g0;

    /* renamed from: h */
    public static final C2010v2<Integer> f8200h;

    /* renamed from: h0 */
    public static final C2010v2<Boolean> f8201h0;

    /* renamed from: i */
    public static final C2010v2<Integer> f8202i;

    /* renamed from: i0 */
    public static final C2010v2<Boolean> f8203i0;

    /* renamed from: j */
    public static final C2010v2<Integer> f8204j;

    /* renamed from: j0 */
    public static final C2010v2<Boolean> f8205j0;

    /* renamed from: k */
    public static final C2010v2<Integer> f8206k;

    /* renamed from: k0 */
    public static final C2010v2<Boolean> f8207k0;

    /* renamed from: l */
    public static final C2010v2<Integer> f8208l;

    /* renamed from: l0 */
    public static final C2010v2<Boolean> f8209l0;

    /* renamed from: m */
    public static final C2010v2<Integer> f8210m;

    /* renamed from: m0 */
    public static final C2010v2<Boolean> f8211m0;

    /* renamed from: n */
    public static final C2010v2<Integer> f8212n;

    /* renamed from: n0 */
    public static final C2010v2<Integer> f8213n0;

    /* renamed from: o */
    public static final C2010v2<Integer> f8214o;

    /* renamed from: o0 */
    public static final C2010v2<Boolean> f8215o0;

    /* renamed from: p */
    public static final C2010v2<Integer> f8216p;

    /* renamed from: p0 */
    public static final C2010v2<Boolean> f8217p0;

    /* renamed from: q */
    public static final C2010v2<String> f8218q;

    /* renamed from: q0 */
    public static final C2010v2<Boolean> f8219q0;

    /* renamed from: r */
    public static final C2010v2<Long> f8220r;

    /* renamed from: r0 */
    public static final C2010v2<Boolean> f8221r0;

    /* renamed from: s */
    public static final C2010v2<Long> f8222s;

    /* renamed from: s0 */
    public static final C2010v2<Boolean> f8223s0;

    /* renamed from: t */
    public static final C2010v2<Long> f8224t;

    /* renamed from: t0 */
    public static final C2010v2<Boolean> f8225t0;

    /* renamed from: u */
    public static final C2010v2<Long> f8226u;

    /* renamed from: v */
    public static final C2010v2<Long> f8227v;

    /* renamed from: w */
    public static final C2010v2<Long> f8228w;

    /* renamed from: x */
    public static final C2010v2<Long> f8229x;

    /* renamed from: y */
    public static final C2010v2<Long> f8230y;

    /* renamed from: z */
    public static final C2010v2<Long> f8231z;

    static {
        Collections.synchronizedSet(new HashSet());
        f8188b = m5144b("measurement.ad_id_cache_time", 10000L, 10000L, C0385m.f2329T);
        f8190c = m5144b("measurement.monitoring.sample_period_millis", 86400000L, 86400000L, C1840b0.f7571j);
        f8192d = m5144b("measurement.config.cache_time", 86400000L, 3600000L, C1936m0.f7865j);
        f8194e = m5144b("measurement.config.url_scheme", "https", "https", C2032y0.f8257j);
        f8196f = m5144b("measurement.config.url_authority", "app-measurement.com", "app-measurement.com", C1913j1.f7779j);
        f8198g = m5144b("measurement.upload.max_bundles", 100, 100, C2001u1.f8101j);
        f8200h = m5144b("measurement.upload.max_batch_size", 65536, 65536, C1887g2.f7700j);
        f8202i = m5144b("measurement.upload.max_bundle_size", 65536, 65536, C1978r2.f7982j);
        f8204j = m5144b("measurement.upload.max_events_per_bundle", 1000, 1000, C1986s2.f8032j);
        f8206k = m5144b("measurement.upload.max_events_per_day", 100000, 100000, C1994t2.f8053j);
        f8208l = m5144b("measurement.upload.max_error_events_per_day", 1000, 1000, C1798e.f7328G);
        f8210m = m5144b("measurement.upload.max_public_events_per_day", 50000, 50000, C1983s.f8029j);
        f8212n = m5144b("measurement.upload.max_conversions_per_day", 10000, 10000, C1991t.f8050j);
        f8214o = m5144b("measurement.upload.max_realtime_events_per_day", 10, 10, C1999u.f8099j);
        f8216p = m5144b("measurement.store.max_stored_events_per_app", 100000, 100000, C2007v.f8118j);
        f8218q = m5144b("measurement.upload.url", "https://app-measurement.com/a", "https://app-measurement.com/a", C2015w.f8135j);
        f8220r = m5144b("measurement.upload.backoff_period", 43200000L, 43200000L, C2023x.f8155j);
        m5144b("measurement.upload.window_interval", 3600000L, 3600000L, C2031y.f8256j);
        f8222s = m5144b("measurement.upload.interval", 3600000L, 3600000L, C2039z.f8273j);
        f8224t = m5144b("measurement.upload.realtime_upload_interval", 10000L, 10000L, C1831a0.f7537j);
        f8226u = m5144b("measurement.upload.debug_upload_interval", 1000L, 1000L, C1849c0.f7612j);
        f8227v = m5144b("measurement.upload.minimum_delay", 500L, 500L, C1858d0.f7626j);
        f8228w = m5144b("measurement.alarm_manager.minimum_interval", 60000L, 60000L, C1867e0.f7655j);
        f8229x = m5144b("measurement.upload.stale_data_deletion_interval", 86400000L, 86400000L, C1876f0.f7681j);
        f8230y = m5144b("measurement.upload.refresh_blacklisted_config_interval", 604800000L, 604800000L, C1885g0.f7698j);
        f8231z = m5144b("measurement.upload.initial_upload_delay_time", 15000L, 15000L, C1894h0.f7730j);
        f8160A = m5144b("measurement.upload.retry_time", 1800000L, 1800000L, C1903i0.f7756j);
        f8161B = m5144b("measurement.upload.retry_count", 6, 6, C1912j0.f7778j);
        f8162C = m5144b("measurement.upload.max_queue_time", 2419200000L, 2419200000L, C1920k0.f7811j);
        f8163D = m5144b("measurement.lifetimevalue.max_currency_tracked", 4, 4, C1928l0.f7830j);
        f8164E = m5144b("measurement.audience.filter_result_max_count", 200, 200, C1944n0.f7890j);
        f8165F = m5144b("measurement.upload.max_public_user_properties", 25, 25, null);
        f8166G = m5144b("measurement.upload.max_event_name_cardinality", 500, 500, null);
        f8167H = m5144b("measurement.upload.max_public_event_params", 25, 25, null);
        f8168I = m5144b("measurement.service_client.idle_disconnect_millis", 5000L, 5000L, C1952o0.f7943j);
        Boolean bool = Boolean.FALSE;
        m5144b("measurement.test.boolean_flag", bool, bool, C1960p0.f7955j);
        m5144b("measurement.test.string_flag", "---", "---", C1968q0.f7969j);
        m5144b("measurement.test.long_flag", -1L, -1L, C1976r0.f7980j);
        m5144b("measurement.test.int_flag", -2, -2, C1984s0.f8030j);
        Double valueOf = Double.valueOf(-3.0d);
        m5144b("measurement.test.double_flag", valueOf, valueOf, C1992t0.f8051j);
        f8169J = m5144b("measurement.experiment.max_ids", 50, 50, C2000u0.f8100j);
        f8170K = m5144b("measurement.max_bundles_per_iteration", 100, 100, C2008v0.f8119j);
        f8171L = m5144b("measurement.sdk.attribution.cache.ttl", 604800000L, 604800000L, C2024x0.f8156j);
        f8172M = m5144b("measurement.validation.internal_limits_internal_event_params", bool, bool, C2040z0.f8274j);
        Boolean bool2 = Boolean.TRUE;
        f8173N = m5144b("measurement.collection.firebase_global_collection_flag_enabled", bool2, bool2, C1832a1.f7538j);
        f8174O = m5144b("measurement.collection.redundant_engagement_removal_enabled", bool, bool, C1841b1.f7572j);
        m5144b("measurement.collection.log_event_and_bundle_v2", bool2, bool2, C1850c1.f7613j);
        f8175P = m5144b("measurement.quality.checksum", bool, bool, null);
        f8176Q = m5144b("measurement.sdk.collection.validate_param_names_alphabetical", bool2, bool2, C1859d1.f7627j);
        f8177R = m5144b("measurement.audience.use_bundle_end_timestamp_for_non_sequence_property_filters", bool, bool, C1868e1.f7656j);
        f8178S = m5144b("measurement.audience.refresh_event_count_filters_timestamp", bool, bool, C1877f1.f7682j);
        f8179T = m5144b("measurement.audience.use_bundle_timestamp_for_event_count_filters", bool, bool, C1886g1.f7699j);
        f8180U = m5144b("measurement.sdk.collection.retrieve_deeplink_from_bow_2", bool2, bool2, C1895h1.f7731j);
        f8181V = m5144b("measurement.sdk.collection.last_deep_link_referrer2", bool2, bool2, C1904i1.f7757j);
        f8182W = m5144b("measurement.sdk.collection.last_deep_link_referrer_campaign2", bool, bool, C1921k1.f7812j);
        f8183X = m5144b("measurement.sdk.collection.last_gclid_from_referrer2", bool, bool, C1929l1.f7831j);
        f8184Y = m5144b("measurement.sdk.collection.enable_extend_user_property_size", bool2, bool2, C1937m1.f7866j);
        f8185Z = m5144b("measurement.upload.file_lock_state_check", bool2, bool2, C1945n1.f7891j);
        f8187a0 = m5144b("measurement.ga.ga_app_id", bool, bool, C1953o1.f7944j);
        f8189b0 = m5144b("measurement.lifecycle.app_in_background_parameter", bool, bool, C1961p1.f7956j);
        f8191c0 = m5144b("measurement.integration.disable_firebase_instance_id", bool, bool, C1969q1.f7970j);
        f8193d0 = m5144b("measurement.lifecycle.app_backgrounded_engagement", bool, bool, C1977r1.f7981j);
        f8195e0 = m5144b("measurement.collection.service.update_with_analytics_fix", bool, bool, C1985s1.f8031j);
        f8197f0 = m5144b("measurement.service.use_appinfo_modified", bool2, bool2, C1993t1.f8052j);
        f8199g0 = m5144b("measurement.client.firebase_feature_rollout.v1.enable", bool2, bool2, C2009v1.f8120j);
        f8201h0 = m5144b("measurement.client.sessions.check_on_reset_and_enable2", bool2, bool2, C2017w1.f8139j);
        f8203i0 = m5144b("measurement.scheduler.task_thread.cleanup_on_exit", bool, bool, C2033y1.f8258j);
        f8205j0 = m5144b("measurement.upload.file_truncate_fix", bool, bool, C2041z1.f8275j);
        f8207k0 = m5144b("measurement.sdk.screen.disabling_automatic_reporting", bool2, bool2, C1833a2.f7539j);
        f8209l0 = m5144b("measurement.sdk.screen.manual_screen_view_logging", bool2, bool2, C1842b2.f7573j);
        m5144b("measurement.collection.synthetic_data_mitigation", bool, bool, C1851c2.f7614j);
        f8211m0 = m5144b("measurement.androidId.delete_feature", bool2, bool2, C1860d2.f7628j);
        f8213n0 = m5144b("measurement.service.storage_consent_support_version", 203600, 203600, C1869e2.f7657j);
        f8215o0 = m5144b("measurement.upload.directly_maybe_log_error_events", bool2, bool2, C1878f2.f7683j);
        f8217p0 = m5144b("measurement.frontend.directly_maybe_log_error_events", bool, bool, C1896h2.f7732j);
        f8219q0 = m5144b("measurement.client.properties.non_null_origin", bool2, bool2, C1905i2.f7758j);
        m5144b("measurement.client.click_identifier_control.dev", bool, bool, C1914j2.f7780j);
        m5144b("measurement.service.click_identifier_control", bool, bool, C1922k2.f7813j);
        m5144b("measurement.client.reject_blank_user_id", bool2, bool2, C1930l2.f7832j);
        f8221r0 = m5144b("measurement.config.persist_last_modified", bool2, bool2, C1938m2.f7867j);
        f8223s0 = m5144b("measurement.client.consent.suppress_1p_in_ga4f_install", bool2, bool2, C1946n2.f7892j);
        f8225t0 = m5144b("measurement.module.pixie.ees", bool, bool, C1954o2.f7945j);
        m5144b("measurement.euid.client.dev", bool, bool, C1962p2.f7957j);
        m5144b("measurement.euid.service", bool, bool, C1970q2.f7971j);
    }

    /* renamed from: a */
    public static Map<String, String> m5143a(Context context) {
        C1629m2 m3932a = C1629m2.m3932a(context.getContentResolver(), C1713t2.m4275a());
        return m3932a == null ? Collections.emptyMap() : m3932a.m3934b();
    }

    /* renamed from: b */
    public static <V> C2010v2<V> m5144b(String str, V v6, V v7, InterfaceC2002u2<V> interfaceC2002u2) {
        C2010v2<V> c2010v2 = new C2010v2<>(str, v6, v7, interfaceC2002u2);
        f8186a.add(c2010v2);
        return c2010v2;
    }
}
