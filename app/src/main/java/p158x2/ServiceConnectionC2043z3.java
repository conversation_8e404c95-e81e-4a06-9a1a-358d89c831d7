package p158x2;

import android.content.ComponentName;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.IInterface;
import p152w2.AbstractBinderC1531e0;
import p152w2.C1519d0;
import p152w2.InterfaceC1543f0;

/* renamed from: x2.z3 */
/* loaded from: classes.dex */
public final class ServiceConnectionC2043z3 implements ServiceConnection {

    /* renamed from: a */
    public final String f8276a;

    /* renamed from: b */
    public final /* synthetic */ C1835a4 f8277b;

    public ServiceConnectionC2043z3(C1835a4 c1835a4, String str) {
        this.f8277b = c1835a4;
        this.f8276a = str;
    }

    @Override // android.content.ServiceConnection
    public final void onServiceConnected(ComponentName componentName, IBinder iBinder) {
        if (iBinder == null) {
            this.f8277b.f7540a.mo4962e().f7787s.m4841b("Install Referrer connection returned with null binder");
            return;
        }
        try {
            int i6 = AbstractBinderC1531e0.f6956a;
            IInterface queryLocalInterface = iBinder.queryLocalInterface("com.google.android.finsky.externalreferrer.IGetInstallReferrerService");
            Object c1519d0 = queryLocalInterface instanceof InterfaceC1543f0 ? (InterfaceC1543f0) queryLocalInterface : new C1519d0(iBinder);
            if (c1519d0 == null) {
                this.f8277b.f7540a.mo4962e().f7787s.m4841b("Install Referrer Service implementation was not found");
            } else {
                this.f8277b.f7540a.mo4962e().f7792x.m4841b("Install Referrer Service connected");
                this.f8277b.f7540a.mo4959b().m4918q(new RunnableC2035y3(this, c1519d0, this, 0));
            }
        } catch (RuntimeException e6) {
            this.f8277b.f7540a.mo4962e().f7787s.m4842c("Exception occurred while calling Install Referrer API", e6);
        }
    }

    @Override // android.content.ServiceConnection
    public final void onServiceDisconnected(ComponentName componentName) {
        this.f8277b.f7540a.mo4962e().f7792x.m4841b("Install Referrer Service disconnected");
    }
}
