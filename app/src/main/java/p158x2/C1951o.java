package p158x2;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import java.util.Iterator;
import org.litepal.parser.LitePalParser;
import p099o2.AbstractC1146a;
import p152w2.C1717t6;
import p153w3.C1798e;

/* renamed from: x2.o */
/* loaded from: classes.dex */
public final class C1951o extends AbstractC1146a implements Iterable<String> {
    public static final Parcelable.Creator<C1951o> CREATOR = new C1959p();

    /* renamed from: j */
    public final Bundle f7942j;

    public C1951o(Bundle bundle) {
        this.f7942j = bundle;
    }

    /* renamed from: c */
    public final Object m4985c(String str) {
        return this.f7942j.get(str);
    }

    @Override // java.lang.Iterable
    public final Iterator<String> iterator() {
        return new C1717t6(this);
    }

    /* renamed from: r */
    public final Long m4986r() {
        return Long.valueOf(this.f7942j.getLong(LitePalParser.ATTR_VALUE));
    }

    /* renamed from: s */
    public final Double m4987s() {
        return Double.valueOf(this.f7942j.getDouble(LitePalParser.ATTR_VALUE));
    }

    /* renamed from: t */
    public final String m4988t(String str) {
        return this.f7942j.getString(str);
    }

    public final String toString() {
        return this.f7942j.toString();
    }

    /* renamed from: u */
    public final Bundle m4989u() {
        return new Bundle(this.f7942j);
    }

    @Override // android.os.Parcelable
    public final void writeToParcel(Parcel parcel, int i6) {
        int m4526S = C1798e.m4526S(parcel, 20293);
        C1798e.m4522O(parcel, 2, m4989u());
        C1798e.m4539f0(parcel, m4526S);
    }
}
