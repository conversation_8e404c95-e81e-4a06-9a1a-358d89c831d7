package p158x2;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.text.TextUtils;
import java.util.Objects;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import p152w2.C1695r8;

/* renamed from: x2.g5 */
/* loaded from: classes.dex */
public final class RunnableC1890g5 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ C1875f f7715j;

    /* renamed from: k */
    public final /* synthetic */ long f7716k;

    /* renamed from: l */
    public final /* synthetic */ int f7717l;

    /* renamed from: m */
    public final /* synthetic */ long f7718m;

    /* renamed from: n */
    public final /* synthetic */ boolean f7719n;

    /* renamed from: o */
    public final /* synthetic */ C1933l5 f7720o;

    public RunnableC1890g5(C1933l5 c1933l5, C1875f c1875f, long j6, int i6, long j7, boolean z5) {
        this.f7720o = c1933l5;
        this.f7715j = c1875f;
        this.f7716k = j6;
        this.f7717l = i6;
        this.f7718m = j7;
        this.f7719n = z5;
    }

    @Override // java.lang.Runnable
    public final void run() {
        String str;
        this.f7720o.m4929r(this.f7715j);
        C1933l5 c1933l5 = this.f7720o;
        long j6 = this.f7716k;
        c1933l5.mo4915h();
        c1933l5.m5102i();
        ((C1948n4) c1933l5.f8145k).mo4962e().f7791w.m4841b("Resetting analytics data (FE)");
        C1934l6 m4971r = ((C1948n4) c1933l5.f8145k).m4971r();
        m4971r.mo4915h();
        C1918j6 c1918j6 = m4971r.f7857o;
        c1918j6.f7805c.m4900c();
        c1918j6.f7803a = 0L;
        c1918j6.f7804b = 0L;
        boolean m4965i = ((C1948n4) c1933l5.f8145k).m4965i();
        C2027x3 m4970q = ((C1948n4) c1933l5.f8145k).m4970q();
        m4970q.f8242o.m5051b(j6);
        if (!TextUtils.isEmpty(((C1948n4) m4970q.f8145k).m4970q().f8236D.m5136a())) {
            m4970q.f8236D.m5137b(null);
        }
        C1695r8.m4069b();
        if (((C1948n4) m4970q.f8145k).f7923p.m4784q(null, C2026x2.f8201h0)) {
            m4970q.f8252y.m5051b(0L);
        }
        if (!((C1948n4) m4970q.f8145k).f7923p.m4787t()) {
            m4970q.m5150t(!m4965i);
        }
        m4970q.f8237E.m5137b(null);
        m4970q.f8238F.m5051b(0L);
        C1987s3 c1987s3 = m4970q.f8239G;
        Objects.requireNonNull(c1987s3);
        Bundle bundle = new Bundle();
        SharedPreferences.Editor edit = c1987s3.f8036d.m5145o().edit();
        if (bundle.size() == 0) {
            edit.remove(c1987s3.f8033a);
        } else {
            String str2 = c1987s3.f8033a;
            JSONArray jSONArray = new JSONArray();
            for (String str3 : bundle.keySet()) {
                Object obj = bundle.get(str3);
                if (obj != null) {
                    try {
                        JSONObject jSONObject = new JSONObject();
                        jSONObject.put("n", str3);
                        jSONObject.put("v", String.valueOf(obj));
                        if (obj instanceof String) {
                            str = "s";
                        } else if (obj instanceof Long) {
                            str = "l";
                        } else if (obj instanceof Double) {
                            str = "d";
                        } else {
                            ((C1948n4) c1987s3.f8036d.f8145k).mo4962e().f7784p.m4842c("Cannot serialize bundle value to SharedPreferences. Type", obj.getClass());
                        }
                        jSONObject.put("t", str);
                        jSONArray.put(jSONObject);
                    } catch (JSONException e6) {
                        ((C1948n4) c1987s3.f8036d.f8145k).mo4962e().f7784p.m4842c("Cannot serialize bundle value to SharedPreferences", e6);
                    }
                }
            }
            edit.putString(str2, jSONArray.toString());
        }
        edit.apply();
        c1987s3.f8035c = bundle;
        C1695r8.m4069b();
        if (((C1948n4) c1933l5.f8145k).f7923p.m4784q(null, C2026x2.f8201h0)) {
            ((C1948n4) c1933l5.f8145k).m4971r().f7856n.m4907a();
        }
        c1933l5.f7853x = !m4965i;
        C1933l5.m4923p(this.f7720o, this.f7715j, this.f7717l, this.f7718m, true, this.f7719n);
    }
}
