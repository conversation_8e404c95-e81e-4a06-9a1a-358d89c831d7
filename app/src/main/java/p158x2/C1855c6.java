package p158x2;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ResolveInfo;
import android.os.Looper;
import android.os.SystemClock;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import p092n2.AbstractC1099b;
import p106p2.C1234a;
import p153w3.C1798e;

/* renamed from: x2.c6 */
/* loaded from: classes.dex */
public final class C1855c6 extends AbstractC2011v3 {

    /* renamed from: m */
    public final ServiceConnectionC1846b6 f7619m;

    /* renamed from: n */
    public InterfaceC1834a3 f7620n;

    /* renamed from: o */
    public volatile Boolean f7621o;

    /* renamed from: p */
    public final C2021w5 f7622p;

    /* renamed from: q */
    public final C1942m6 f7623q;

    /* renamed from: r */
    public final List<Runnable> f7624r;

    /* renamed from: s */
    public final C2029x5 f7625s;

    public C1855c6(C1948n4 c1948n4) {
        super(c1948n4);
        this.f7624r = new ArrayList();
        this.f7623q = new C1942m6(c1948n4.f7930w);
        this.f7619m = new ServiceConnectionC1846b6(this);
        this.f7622p = new C2021w5(this, c1948n4, 0);
        this.f7625s = new C2029x5(this, c1948n4);
    }

    /* renamed from: p */
    public static void m4750p(C1855c6 c1855c6, ComponentName componentName) {
        c1855c6.mo4915h();
        if (c1855c6.f7620n != null) {
            c1855c6.f7620n = null;
            ((C1948n4) c1855c6.f8145k).mo4962e().f7792x.m4842c("Disconnected from device MeasurementService", componentName);
            c1855c6.mo4915h();
            c1855c6.m4751l();
        }
    }

    @Override // p158x2.AbstractC2011v3
    /* renamed from: k */
    public final boolean mo4746k() {
        return false;
    }

    /* renamed from: l */
    public final void m4751l() {
        C1897h3 c1897h3;
        String str;
        mo4915h();
        m5102i();
        if (m4760v()) {
            return;
        }
        if (m4752m()) {
            ServiceConnectionC1846b6 serviceConnectionC1846b6 = this.f7619m;
            serviceConnectionC1846b6.f7589c.mo4915h();
            Context context = ((C1948n4) serviceConnectionC1846b6.f7589c.f8145k).f7917j;
            synchronized (serviceConnectionC1846b6) {
                if (serviceConnectionC1846b6.f7587a) {
                    c1897h3 = ((C1948n4) serviceConnectionC1846b6.f7589c.f8145k).mo4962e().f7792x;
                    str = "Connection attempt already in progress";
                } else if (serviceConnectionC1846b6.f7588b == null || !(serviceConnectionC1846b6.f7588b.m2853d() || serviceConnectionC1846b6.f7588b.m2852c())) {
                    serviceConnectionC1846b6.f7588b = new C1879f3(context, Looper.getMainLooper(), serviceConnectionC1846b6, serviceConnectionC1846b6);
                    ((C1948n4) serviceConnectionC1846b6.f7589c.f8145k).mo4962e().f7792x.m4841b("Connecting to remote service");
                    serviceConnectionC1846b6.f7587a = true;
                    C1798e.m4560r(serviceConnectionC1846b6.f7588b);
                    serviceConnectionC1846b6.f7588b.m2850a();
                } else {
                    c1897h3 = ((C1948n4) serviceConnectionC1846b6.f7589c.f8145k).mo4962e().f7792x;
                    str = "Already awaiting connection attempt";
                }
                c1897h3.m4841b(str);
            }
            return;
        }
        if (((C1948n4) this.f8145k).f7923p.m4791x()) {
            return;
        }
        Objects.requireNonNull((C1948n4) this.f8145k);
        List<ResolveInfo> queryIntentServices = ((C1948n4) this.f8145k).f7917j.getPackageManager().queryIntentServices(new Intent().setClassName(((C1948n4) this.f8145k).f7917j, "com.google.android.gms.measurement.AppMeasurementService"), 65536);
        if (queryIntentServices == null || queryIntentServices.size() <= 0) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4841b("Unable to use remote or local measurement implementation. Please register the AppMeasurementService service in the app manifest");
            return;
        }
        Intent intent = new Intent("com.google.android.gms.measurement.START");
        C1948n4 c1948n4 = (C1948n4) this.f8145k;
        Context context2 = c1948n4.f7917j;
        Objects.requireNonNull(c1948n4);
        intent.setComponent(new ComponentName(context2, "com.google.android.gms.measurement.AppMeasurementService"));
        ServiceConnectionC1846b6 serviceConnectionC1846b62 = this.f7619m;
        serviceConnectionC1846b62.f7589c.mo4915h();
        Context context3 = ((C1948n4) serviceConnectionC1846b62.f7589c.f8145k).f7917j;
        C1234a m3104b = C1234a.m3104b();
        synchronized (serviceConnectionC1846b62) {
            if (serviceConnectionC1846b62.f7587a) {
                ((C1948n4) serviceConnectionC1846b62.f7589c.f8145k).mo4962e().f7792x.m4841b("Connection attempt already in progress");
            } else {
                ((C1948n4) serviceConnectionC1846b62.f7589c.f8145k).mo4962e().f7792x.m4841b("Using local app measurement service");
                serviceConnectionC1846b62.f7587a = true;
                m3104b.m3105a(context3, intent, serviceConnectionC1846b62.f7589c.f7619m, 129);
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:32:0x013b  */
    /* renamed from: m */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean m4752m() {
        /*
            Method dump skipped, instructions count: 354
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1855c6.m4752m():boolean");
    }

    /* renamed from: n */
    public final void m4753n() {
        mo4915h();
        m5102i();
        ServiceConnectionC1846b6 serviceConnectionC1846b6 = this.f7619m;
        if (serviceConnectionC1846b6.f7588b != null && (serviceConnectionC1846b6.f7588b.m2852c() || serviceConnectionC1846b6.f7588b.m2853d())) {
            C1879f3 c1879f3 = serviceConnectionC1846b6.f7588b;
            c1879f3.f5276t.incrementAndGet();
            synchronized (c1879f3.f5267k) {
                int size = c1879f3.f5267k.size();
                for (int i6 = 0; i6 < size; i6++) {
                    AbstractC1099b.g<?> gVar = c1879f3.f5267k.get(i6);
                    synchronized (gVar) {
                        gVar.f5282a = null;
                    }
                }
                c1879f3.f5267k.clear();
            }
            synchronized (c1879f3.f5263g) {
                c1879f3.f5264h = null;
            }
            c1879f3.m2854e(1, null);
        }
        serviceConnectionC1846b6.f7588b = null;
        try {
            C1234a m3104b = C1234a.m3104b();
            Context context = ((C1948n4) this.f8145k).f7917j;
            ServiceConnectionC1846b6 serviceConnectionC1846b62 = this.f7619m;
            Objects.requireNonNull(m3104b);
            context.unbindService(serviceConnectionC1846b62);
        } catch (IllegalArgumentException | IllegalStateException unused) {
        }
        this.f7620n = null;
    }

    /* renamed from: o */
    public final boolean m4754o() {
        mo4915h();
        m5102i();
        return !m4752m() || ((C1948n4) this.f8145k).m4973t().m4714M() >= C2026x2.f8213n0.m5101a(null).intValue();
    }

    /* renamed from: q */
    public final void m4755q() {
        Objects.requireNonNull((C1948n4) this.f8145k);
    }

    /* renamed from: r */
    public final void m4756r() {
        mo4915h();
        C1942m6 c1942m6 = this.f7623q;
        Objects.requireNonNull((C1798e) c1942m6.f7877a);
        c1942m6.f7878b = SystemClock.elapsedRealtime();
        C2021w5 c2021w5 = this.f7622p;
        Objects.requireNonNull((C1948n4) this.f8145k);
        c2021w5.m4899b(C2026x2.f8168I.m5101a(null).longValue());
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [java.util.ArrayList, java.util.List<java.lang.Runnable>] */
    /* JADX WARN: Type inference failed for: r0v5, types: [java.util.ArrayList, java.util.List<java.lang.Runnable>] */
    /* renamed from: s */
    public final void m4757s(Runnable runnable) {
        mo4915h();
        if (m4760v()) {
            runnable.run();
            return;
        }
        int size = this.f7624r.size();
        Objects.requireNonNull((C1948n4) this.f8145k);
        if (size >= 1000) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4841b("Discarding data. Max runnable queue size reached");
            return;
        }
        this.f7624r.add(runnable);
        this.f7625s.m4899b(60000L);
        m4751l();
    }

    /* JADX WARN: Type inference failed for: r0v4, types: [java.util.ArrayList, java.util.List<java.lang.Runnable>] */
    /* JADX WARN: Type inference failed for: r0v6, types: [java.util.ArrayList, java.util.List<java.lang.Runnable>] */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.util.ArrayList, java.util.List<java.lang.Runnable>] */
    /* renamed from: t */
    public final void m4758t() {
        mo4915h();
        ((C1948n4) this.f8145k).mo4962e().f7792x.m4842c("Processing queued up service tasks", Integer.valueOf(this.f7624r.size()));
        Iterator it = this.f7624r.iterator();
        while (it.hasNext()) {
            try {
                ((Runnable) it.next()).run();
            } catch (RuntimeException e6) {
                ((C1948n4) this.f8145k).mo4962e().f7784p.m4842c("Task exception while flushing queue", e6);
            }
        }
        this.f7624r.clear();
        this.f7625s.m4900c();
    }

    /* JADX WARN: Removed duplicated region for block: B:24:0x0119  */
    /* JADX WARN: Removed duplicated region for block: B:31:0x01cf  */
    /* JADX WARN: Removed duplicated region for block: B:44:0x0268  */
    /* JADX WARN: Removed duplicated region for block: B:47:0x028c  */
    /* JADX WARN: Removed duplicated region for block: B:52:0x02c0  */
    /* JADX WARN: Removed duplicated region for block: B:55:0x02e7  */
    /* JADX WARN: Removed duplicated region for block: B:59:0x02f4  */
    /* JADX WARN: Removed duplicated region for block: B:60:0x02c3  */
    /* JADX WARN: Removed duplicated region for block: B:62:0x026f  */
    /* JADX WARN: Removed duplicated region for block: B:87:0x01ab  */
    /* renamed from: u */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final p158x2.C1847b7 m4759u(boolean r38) {
        /*
            Method dump skipped, instructions count: 801
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1855c6.m4759u(boolean):x2.b7");
    }

    /* renamed from: v */
    public final boolean m4760v() {
        mo4915h();
        m5102i();
        return this.f7620n != null;
    }

    /* JADX WARN: Removed duplicated region for block: B:150:0x02fe  */
    /* JADX WARN: Removed duplicated region for block: B:153:0x030c A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:157:0x0318  */
    /* JADX WARN: Removed duplicated region for block: B:188:0x0307  */
    /* JADX WARN: Removed duplicated region for block: B:59:0x02a4  */
    /* JADX WARN: Removed duplicated region for block: B:64:0x02cc A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:70:0x02c4  */
    /* JADX WARN: Removed duplicated region for block: B:72:0x02cc A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:76:0x0272 A[Catch: all -> 0x02d9, TRY_ENTER, TryCatch #38 {all -> 0x02d9, blocks: (B:31:0x00d8, B:33:0x00de, B:36:0x00eb, B:38:0x00f1, B:46:0x0107, B:48:0x0198, B:76:0x0272, B:78:0x0278, B:79:0x027b, B:68:0x02b2, B:56:0x029d, B:90:0x012a, B:91:0x012d, B:87:0x0125, B:99:0x0133, B:102:0x0147, B:110:0x0163, B:111:0x0166, B:108:0x015c, B:113:0x0169, B:116:0x017d, B:121:0x019c, B:122:0x019f, B:124:0x0192, B:127:0x01a3, B:128:0x01bc, B:130:0x01b0, B:138:0x01d6, B:141:0x01e2, B:145:0x01f2, B:146:0x0201), top: B:30:0x00d8 }] */
    /* JADX WARN: Removed duplicated region for block: B:81:0x028d  */
    /* JADX WARN: Removed duplicated region for block: B:84:0x02cc A[SYNTHETIC] */
    /* renamed from: w */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m4761w(p158x2.InterfaceC1834a3 r28, p099o2.AbstractC1146a r29, p158x2.C1847b7 r30) {
        /*
            Method dump skipped, instructions count: 899
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1855c6.m4761w(x2.a3, o2.a, x2.b7):void");
    }

    /* renamed from: x */
    public final void m4762x(C1839b c1839b) {
        boolean m4767p;
        mo4915h();
        m5102i();
        Objects.requireNonNull((C1948n4) this.f8145k);
        C1861d3 m4975v = ((C1948n4) this.f8145k).m4975v();
        byte[] m4713L = ((C1948n4) m4975v.f8145k).m4973t().m4713L(c1839b);
        if (m4713L.length > 131072) {
            ((C1948n4) m4975v.f8145k).mo4962e().f7785q.m4841b("Conditional user property too long for local database. Sending directly to service");
            m4767p = false;
        } else {
            m4767p = m4975v.m4767p(2, m4713L);
        }
        C1839b c1839b2 = new C1839b(c1839b);
        m4757s(new RunnableC1917j5(this, m4759u(true), m4767p, c1839b2, c1839b));
    }
}
