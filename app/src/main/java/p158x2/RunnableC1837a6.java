package p158x2;

import java.util.Objects;

/* renamed from: x2.a6 */
/* loaded from: classes.dex */
public final class RunnableC1837a6 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f7551j;

    /* renamed from: k */
    public final /* synthetic */ InterfaceC1834a3 f7552k;

    /* renamed from: l */
    public final /* synthetic */ ServiceConnectionC1846b6 f7553l;

    public /* synthetic */ RunnableC1837a6(ServiceConnectionC1846b6 serviceConnectionC1846b6, InterfaceC1834a3 interfaceC1834a3, int i6) {
        this.f7551j = i6;
        this.f7553l = serviceConnectionC1846b6;
        this.f7552k = interfaceC1834a3;
    }

    @Override // java.lang.Runnable
    public final void run() {
        switch (this.f7551j) {
            case 0:
                synchronized (this.f7553l) {
                    this.f7553l.f7587a = false;
                    if (!this.f7553l.f7589c.m4760v()) {
                        ((C1948n4) this.f7553l.f7589c.f8145k).mo4962e().f7792x.m4841b("Connected to service");
                        C1855c6 c1855c6 = this.f7553l.f7589c;
                        InterfaceC1834a3 interfaceC1834a3 = this.f7552k;
                        c1855c6.mo4915h();
                        Objects.requireNonNull(interfaceC1834a3, "null reference");
                        c1855c6.f7620n = interfaceC1834a3;
                        c1855c6.m4756r();
                        c1855c6.m4758t();
                    }
                }
                return;
            default:
                synchronized (this.f7553l) {
                    this.f7553l.f7587a = false;
                    if (!this.f7553l.f7589c.m4760v()) {
                        ((C1948n4) this.f7553l.f7589c.f8145k).mo4962e().f7791w.m4841b("Connected to remote service");
                        C1855c6 c1855c62 = this.f7553l.f7589c;
                        InterfaceC1834a3 interfaceC1834a32 = this.f7552k;
                        c1855c62.mo4915h();
                        Objects.requireNonNull(interfaceC1834a32, "null reference");
                        c1855c62.f7620n = interfaceC1834a32;
                        c1855c62.m4756r();
                        c1855c62.m4758t();
                    }
                }
                return;
        }
    }
}
