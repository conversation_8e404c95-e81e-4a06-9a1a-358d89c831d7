package p158x2;

/* renamed from: x2.m4 */
/* loaded from: classes.dex */
public final class RunnableC1940m4 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f7874j;

    /* renamed from: k */
    public final /* synthetic */ Object f7875k;

    /* renamed from: l */
    public final /* synthetic */ Object f7876l;

    public /* synthetic */ RunnableC1940m4(Object obj, Object obj2, int i6) {
        this.f7874j = i6;
        this.f7876l = obj;
        this.f7875k = obj2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:106:0x052f, code lost:
    
        if (android.text.TextUtils.isEmpty(r2.f7583v) == false) goto L279;
     */
    /* JADX WARN: Code restructure failed: missing block: B:90:0x03df, code lost:
    
        if (android.text.TextUtils.isEmpty(r3.f7583v) == false) goto L257;
     */
    /* JADX WARN: Type inference failed for: r1v5, types: [java.util.ArrayList, java.util.List<java.lang.Runnable>] */
    @Override // java.lang.Runnable
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void run() {
        /*
            Method dump skipped, instructions count: 1522
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.RunnableC1940m4.run():void");
    }

    public RunnableC1940m4(C1998t6 c1998t6, Runnable runnable) {
        this.f7874j = 4;
        this.f7875k = c1998t6;
        this.f7876l = runnable;
    }
}
