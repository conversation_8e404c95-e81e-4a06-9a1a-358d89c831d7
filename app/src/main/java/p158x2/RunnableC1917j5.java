package p158x2;

import android.net.Uri;
import android.os.Bundle;
import android.os.Parcelable;
import android.text.TextUtils;
import p153w3.C1798e;

/* renamed from: x2.j5 */
/* loaded from: classes.dex */
public final class RunnableC1917j5 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f7797j = 1;

    /* renamed from: k */
    public final /* synthetic */ boolean f7798k;

    /* renamed from: l */
    public final /* synthetic */ Parcelable f7799l;

    /* renamed from: m */
    public final /* synthetic */ Object f7800m;

    /* renamed from: n */
    public final /* synthetic */ Object f7801n;

    /* renamed from: o */
    public final /* synthetic */ Object f7802o;

    public RunnableC1917j5(C1925k5 c1925k5, boolean z5, Uri uri, String str, String str2) {
        this.f7802o = c1925k5;
        this.f7798k = z5;
        this.f7799l = uri;
        this.f7800m = str;
        this.f7801n = str2;
    }

    @Override // java.lang.Runnable
    public final void run() {
        Bundle bundle;
        r2 = null;
        r2 = null;
        Bundle m4721Y = null;
        switch (this.f7797j) {
            case 0:
                C1925k5 c1925k5 = (C1925k5) this.f7802o;
                boolean z5 = this.f7798k;
                Uri uri = (Uri) this.f7799l;
                String str = (String) this.f7800m;
                String str2 = (String) this.f7801n;
                c1925k5.f7823j.mo4915h();
                try {
                    C1866e c1866e = ((C1948n4) c1925k5.f7823j.f8145k).f7923p;
                    C2010v2<Boolean> c2010v2 = C2026x2.f8181V;
                    if (c1866e.m4784q(null, c2010v2) || ((C1948n4) c1925k5.f7823j.f8145k).f7923p.m4784q(null, C2026x2.f8183X) || ((C1948n4) c1925k5.f7823j.f8145k).f7923p.m4784q(null, C2026x2.f8182W)) {
                        C1838a7 m4973t = ((C1948n4) c1925k5.f7823j.f8145k).m4973t();
                        if (!TextUtils.isEmpty(str2)) {
                            if (str2.contains("gclid") || str2.contains("utm_campaign") || str2.contains("utm_source") || str2.contains("utm_medium")) {
                                m4721Y = m4973t.m4721Y(Uri.parse(str2.length() != 0 ? "https://google.com/search?".concat(str2) : new String("https://google.com/search?")));
                                if (m4721Y != null) {
                                    m4721Y.putString("_cis", "referrer");
                                }
                            } else {
                                ((C1948n4) m4973t.f8145k).mo4962e().f7791w.m4841b("Activity created with data 'referrer' without required params");
                            }
                        }
                    }
                    if (z5) {
                        bundle = ((C1948n4) c1925k5.f7823j.f8145k).m4973t().m4721Y(uri);
                        if (bundle != null) {
                            bundle.putString("_cis", "intent");
                            if (((C1948n4) c1925k5.f7823j.f8145k).f7923p.m4784q(null, c2010v2) && !bundle.containsKey("gclid") && m4721Y != null && m4721Y.containsKey("gclid")) {
                                bundle.putString("_cer", String.format("gclid=%s", m4721Y.getString("gclid")));
                            }
                            c1925k5.f7823j.m4930s(str, "_cmp", bundle);
                            c1925k5.f7823j.f7852w.m4772a(str, bundle);
                        }
                    } else {
                        bundle = null;
                    }
                    if (((C1948n4) c1925k5.f7823j.f8145k).f7923p.m4784q(null, C2026x2.f8183X) && !((C1948n4) c1925k5.f7823j.f8145k).f7923p.m4784q(null, C2026x2.f8182W) && m4721Y != null && m4721Y.containsKey("gclid") && (bundle == null || !bundle.containsKey("gclid"))) {
                        c1925k5.f7823j.m4934w("_lgclid", m4721Y.getString("gclid"));
                    }
                    if (!TextUtils.isEmpty(str2)) {
                        ((C1948n4) c1925k5.f7823j.f8145k).mo4962e().f7791w.m4842c("Activity created with referrer", str2);
                        if (!((C1948n4) c1925k5.f7823j.f8145k).f7923p.m4784q(null, C2026x2.f8182W)) {
                            if (!str2.contains("gclid") || (!str2.contains("utm_campaign") && !str2.contains("utm_source") && !str2.contains("utm_medium") && !str2.contains("utm_term") && !str2.contains("utm_content"))) {
                                ((C1948n4) c1925k5.f7823j.f8145k).mo4962e().f7791w.m4841b("Activity created with data 'referrer' without required params");
                                break;
                            } else if (!TextUtils.isEmpty(str2)) {
                                c1925k5.f7823j.m4934w("_ldl", str2);
                                break;
                            }
                        } else {
                            if (m4721Y != null) {
                                c1925k5.f7823j.m4930s(str, "_cmp", m4721Y);
                                c1925k5.f7823j.f7852w.m4772a(str, m4721Y);
                            } else {
                                ((C1948n4) c1925k5.f7823j.f8145k).mo4962e().f7791w.m4842c("Referrer does not contain valid parameters", str2);
                            }
                            c1925k5.f7823j.m4934w("_ldl", null);
                            break;
                        }
                    } else {
                        break;
                    }
                } catch (RuntimeException e6) {
                    ((C1948n4) c1925k5.f7823j.f8145k).mo4962e().f7784p.m4842c("Throwable caught in handleReferrerForOnActivityCreated", e6);
                }
                break;
            default:
                C1855c6 c1855c6 = (C1855c6) this.f7802o;
                InterfaceC1834a3 interfaceC1834a3 = c1855c6.f7620n;
                if (interfaceC1834a3 != null) {
                    C1798e.m4560r((C1847b7) this.f7799l);
                    ((C1855c6) this.f7802o).m4761w(interfaceC1834a3, this.f7798k ? null : (C1839b) this.f7800m, (C1847b7) this.f7799l);
                    ((C1855c6) this.f7802o).m4756r();
                    break;
                } else {
                    ((C1948n4) c1855c6.f8145k).mo4962e().f7784p.m4841b("Discarding data. Failed to send conditional user property to service");
                    break;
                }
        }
    }

    public RunnableC1917j5(C1855c6 c1855c6, C1847b7 c1847b7, boolean z5, C1839b c1839b, C1839b c1839b2) {
        this.f7802o = c1855c6;
        this.f7799l = c1847b7;
        this.f7798k = z5;
        this.f7800m = c1839b;
        this.f7801n = c1839b2;
    }
}
