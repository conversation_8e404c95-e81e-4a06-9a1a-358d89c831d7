package p158x2;

import java.util.concurrent.Callable;

/* renamed from: x2.t4 */
/* loaded from: classes.dex */
public final class CallableC1996t4 implements Callable<byte[]> {

    /* renamed from: a */
    public final /* synthetic */ BinderC2012v4 f8059a;

    public CallableC1996t4(BinderC2012v4 binderC2012v4, C1967q c1967q, String str) {
        this.f8059a = binderC2012v4;
    }

    @Override // java.util.concurrent.Callable
    public final byte[] call() {
        this.f8059a.f8129a.m5086j();
        C1973q5 c1973q5 = this.f8059a.f8129a.f8089q;
        C1998t6.m5060E(c1973q5);
        c1973q5.mo4915h();
        throw new IllegalStateException("Unexpected call on client side");
    }
}
