package p158x2;

import android.content.SharedPreferences;
import p153w3.C1798e;

/* renamed from: x2.r3 */
/* loaded from: classes.dex */
public final class C1979r3 {

    /* renamed from: a */
    public final String f7983a;

    /* renamed from: b */
    public final boolean f7984b;

    /* renamed from: c */
    public boolean f7985c;

    /* renamed from: d */
    public boolean f7986d;

    /* renamed from: e */
    public final /* synthetic */ C2027x3 f7987e;

    public C1979r3(C2027x3 c2027x3, String str, boolean z5) {
        this.f7987e = c2027x3;
        C1798e.m4554o(str);
        this.f7983a = str;
        this.f7984b = z5;
    }

    /* renamed from: a */
    public final boolean m4998a() {
        if (!this.f7985c) {
            this.f7985c = true;
            this.f7986d = this.f7987e.m5145o().getBoolean(this.f7983a, this.f7984b);
        }
        return this.f7986d;
    }

    /* renamed from: b */
    public final void m4999b(boolean z5) {
        SharedPreferences.Editor edit = this.f7987e.m5145o().edit();
        edit.putBoolean(this.f7983a, z5);
        edit.apply();
        this.f7986d = z5;
    }
}
