package p158x2;

import java.util.concurrent.atomic.AtomicReference;
import p153w3.C1798e;

/* renamed from: x2.f5 */
/* loaded from: classes.dex */
public final class RunnableC1881f5 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f7685j = 1;

    /* renamed from: k */
    public final /* synthetic */ boolean f7686k;

    /* renamed from: l */
    public final /* synthetic */ String f7687l;

    /* renamed from: m */
    public final /* synthetic */ Object f7688m;

    /* renamed from: n */
    public final /* synthetic */ Object f7689n;

    /* renamed from: o */
    public final /* synthetic */ AbstractC2011v3 f7690o;

    public RunnableC1881f5(C1933l5 c1933l5, AtomicReference atomicReference, String str, String str2, boolean z5) {
        this.f7690o = c1933l5;
        this.f7688m = atomicReference;
        this.f7687l = str;
        this.f7689n = str2;
        this.f7686k = z5;
    }

    @Override // java.lang.Runnable
    public final void run() {
        switch (this.f7685j) {
            case 0:
                C1855c6 m4979z = ((C1948n4) ((C1933l5) this.f7690o).f8145k).m4979z();
                AtomicReference atomicReference = (AtomicReference) this.f7688m;
                String str = this.f7687l;
                String str2 = (String) this.f7689n;
                boolean z5 = this.f7686k;
                m4979z.mo4915h();
                m4979z.m5102i();
                m4979z.m4757s(new RunnableC2045z5(m4979z, atomicReference, str, str2, m4979z.m4759u(false), z5));
                break;
            default:
                C1855c6 c1855c6 = (C1855c6) this.f7690o;
                InterfaceC1834a3 interfaceC1834a3 = c1855c6.f7620n;
                if (interfaceC1834a3 != null) {
                    C1798e.m4560r((C1847b7) this.f7688m);
                    ((C1855c6) this.f7690o).m4761w(interfaceC1834a3, this.f7686k ? null : (C1967q) this.f7689n, (C1847b7) this.f7688m);
                    ((C1855c6) this.f7690o).m4756r();
                    break;
                } else {
                    ((C1948n4) c1855c6.f8145k).mo4962e().f7784p.m4841b("Discarding data. Failed to send event to service");
                    break;
                }
        }
    }

    public RunnableC1881f5(C1855c6 c1855c6, C1847b7 c1847b7, boolean z5, C1967q c1967q, String str) {
        this.f7690o = c1855c6;
        this.f7688m = c1847b7;
        this.f7686k = z5;
        this.f7689n = c1967q;
        this.f7687l = str;
    }
}
