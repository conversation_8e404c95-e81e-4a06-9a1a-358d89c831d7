package p158x2;

import android.app.Activity;
import android.os.Bundle;
import android.os.SystemClock;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import org.litepal.util.Const;

/* renamed from: x2.t5 */
/* loaded from: classes.dex */
public final class C1997t5 extends AbstractC2011v3 {

    /* renamed from: m */
    public volatile C1981r5 f8060m;

    /* renamed from: n */
    public C1981r5 f8061n;

    /* renamed from: o */
    public C1981r5 f8062o;

    /* renamed from: p */
    public final Map<Activity, C1981r5> f8063p;

    /* renamed from: q */
    public Activity f8064q;

    /* renamed from: r */
    public volatile boolean f8065r;

    /* renamed from: s */
    public volatile C1981r5 f8066s;

    /* renamed from: t */
    public C1981r5 f8067t;

    /* renamed from: u */
    public boolean f8068u;

    /* renamed from: v */
    public final Object f8069v;

    /* renamed from: w */
    public String f8070w;

    public C1997t5(C1948n4 c1948n4) {
        super(c1948n4);
        this.f8069v = new Object();
        this.f8063p = new ConcurrentHashMap();
    }

    /* renamed from: q */
    public static void m5052q(C1981r5 c1981r5, Bundle bundle, boolean z5) {
        if (c1981r5 != null) {
            if (!bundle.containsKey("_sc") || z5) {
                String str = c1981r5.f8020a;
                if (str != null) {
                    bundle.putString("_sn", str);
                } else {
                    bundle.remove("_sn");
                }
                String str2 = c1981r5.f8021b;
                if (str2 != null) {
                    bundle.putString("_sc", str2);
                } else {
                    bundle.remove("_sc");
                }
                bundle.putLong("_si", c1981r5.f8022c);
                return;
            }
            z5 = false;
        }
        if (c1981r5 == null && z5) {
            bundle.remove("_sn");
            bundle.remove("_sc");
            bundle.remove("_si");
        }
    }

    @Override // p158x2.AbstractC2011v3
    /* renamed from: k */
    public final boolean mo4746k() {
        return false;
    }

    /* JADX WARN: Removed duplicated region for block: B:54:0x00d1  */
    /* JADX WARN: Removed duplicated region for block: B:61:0x00f4  */
    /* renamed from: l */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m5053l(p158x2.C1981r5 r19, p158x2.C1981r5 r20, long r21, boolean r23, android.os.Bundle r24) {
        /*
            Method dump skipped, instructions count: 326
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1997t5.m5053l(x2.r5, x2.r5, long, boolean, android.os.Bundle):void");
    }

    /* renamed from: m */
    public final void m5054m(C1981r5 c1981r5, boolean z5, long j6) {
        C2025x1 m4964g = ((C1948n4) this.f8145k).m4964g();
        Objects.requireNonNull(((C1948n4) this.f8145k).f7930w);
        m4964g.m5139i(SystemClock.elapsedRealtime());
        if (!((C1948n4) this.f8145k).m4971r().f7857o.m4897a(c1981r5 != null && c1981r5.f8023d, z5, j6) || c1981r5 == null) {
            return;
        }
        c1981r5.f8023d = false;
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [java.util.Map<android.app.Activity, x2.r5>, java.util.concurrent.ConcurrentHashMap] */
    /* JADX WARN: Type inference failed for: r0v7, types: [java.util.Map<android.app.Activity, x2.r5>, java.util.concurrent.ConcurrentHashMap] */
    /* renamed from: n */
    public final C1981r5 m5055n(Activity activity) {
        Objects.requireNonNull(activity, "null reference");
        C1981r5 c1981r5 = (C1981r5) this.f8063p.get(activity);
        if (c1981r5 == null) {
            C1981r5 c1981r52 = new C1981r5(null, m5057p(activity.getClass()), ((C1948n4) this.f8145k).m4973t().m4719V());
            this.f8063p.put(activity, c1981r52);
            c1981r5 = c1981r52;
        }
        return (((C1948n4) this.f8145k).f7923p.m4784q(null, C2026x2.f8209l0) && this.f8066s != null) ? this.f8066s : c1981r5;
    }

    /* renamed from: o */
    public final C1981r5 m5056o(boolean z5) {
        m5102i();
        mo4915h();
        if (!((C1948n4) this.f8145k).f7923p.m4784q(null, C2026x2.f8209l0) || !z5) {
            return this.f8062o;
        }
        C1981r5 c1981r5 = this.f8062o;
        return c1981r5 != null ? c1981r5 : this.f8067t;
    }

    /* renamed from: p */
    public final String m5057p(Class cls) {
        String canonicalName = cls.getCanonicalName();
        if (canonicalName == null) {
            return "Activity";
        }
        String[] split = canonicalName.split("\\.");
        int length = split.length;
        String str = length > 0 ? split[length - 1] : "";
        int length2 = str.length();
        Objects.requireNonNull((C1948n4) this.f8145k);
        if (length2 <= 100) {
            return str;
        }
        Objects.requireNonNull((C1948n4) this.f8145k);
        return str.substring(0, 100);
    }

    /* renamed from: r */
    public final void m5058r(String str) {
        mo4915h();
        synchronized (this) {
            String str2 = this.f8070w;
            if (str2 == null || str2.equals(str)) {
                this.f8070w = str;
            }
        }
    }

    /* JADX WARN: Type inference failed for: r7v2, types: [java.util.Map<android.app.Activity, x2.r5>, java.util.concurrent.ConcurrentHashMap] */
    /* renamed from: s */
    public final void m5059s(Activity activity, Bundle bundle) {
        Bundle bundle2;
        if (!((C1948n4) this.f8145k).f7923p.m4788u() || bundle == null || (bundle2 = bundle.getBundle("com.google.app_measurement.screen_service")) == null) {
            return;
        }
        this.f8063p.put(activity, new C1981r5(bundle2.getString(Const.TableSchema.COLUMN_NAME), bundle2.getString("referrer_name"), bundle2.getLong("id")));
    }
}
