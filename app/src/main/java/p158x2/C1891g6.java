package p158x2;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.ResolveInfo;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.json.JSONException;
import org.json.JSONObject;

/* renamed from: x2.g6 */
/* loaded from: classes.dex */
public final class C1891g6 implements InterfaceC1931l3 {

    /* renamed from: j */
    public final /* synthetic */ int f7721j;

    /* renamed from: k */
    public final Object f7722k;

    public C1891g6(Context context) {
        this.f7721j = 0;
        Objects.requireNonNull(context, "null reference");
        this.f7722k = context;
    }

    /* renamed from: a */
    public final void m4828a() {
        C1948n4.m4953h((Context) this.f7722k, null).mo4962e().f7792x.m4841b("Local AppMeasurementService is starting up");
    }

    /* renamed from: b */
    public final void m4829b(int i6, String str, List list, boolean z5, boolean z6) {
        C1897h3 c1897h3;
        int i7 = i6 - 1;
        if (i7 == 0) {
            c1897h3 = ((C1948n4) ((C1889g4) this.f7722k).f8145k).mo4962e().f7791w;
        } else if (i7 == 1) {
            C1915j3 mo4962e = ((C1948n4) ((C1889g4) this.f7722k).f8145k).mo4962e();
            c1897h3 = z5 ? mo4962e.f7785q : !z6 ? mo4962e.f7786r : mo4962e.f7784p;
        } else if (i7 == 3) {
            c1897h3 = ((C1948n4) ((C1889g4) this.f7722k).f8145k).mo4962e().f7792x;
        } else if (i7 != 4) {
            c1897h3 = ((C1948n4) ((C1889g4) this.f7722k).f8145k).mo4962e().f7790v;
        } else {
            C1915j3 mo4962e2 = ((C1948n4) ((C1889g4) this.f7722k).f8145k).mo4962e();
            c1897h3 = z5 ? mo4962e2.f7788t : !z6 ? mo4962e2.f7789u : mo4962e2.f7787s;
        }
        int size = list.size();
        if (size == 1) {
            c1897h3.m4842c(str, list.get(0));
            return;
        }
        if (size == 2) {
            c1897h3.m4843d(str, list.get(0), list.get(1));
        } else if (size != 3) {
            c1897h3.m4841b(str);
        } else {
            c1897h3.m4844e(str, list.get(0), list.get(1), list.get(2));
        }
    }

    @Override // p158x2.InterfaceC1931l3
    /* renamed from: c */
    public final void mo2121c(String str, int i6, Throwable th, byte[] bArr, Map map) {
        List<ResolveInfo> queryIntentActivities;
        switch (this.f7721j) {
            case 2:
                C1948n4 c1948n4 = (C1948n4) this.f7722k;
                if (i6 != 200 && i6 != 204) {
                    if (i6 == 304) {
                        i6 = 304;
                    }
                    c1948n4.mo4962e().f7787s.m4843d("Network Request for Deferred Deep Link failed. response, exception", Integer.valueOf(i6), th);
                    break;
                }
                if (th == null) {
                    c1948n4.m4970q().f8234B.m4999b(true);
                    if (bArr == null || bArr.length == 0) {
                        c1948n4.mo4962e().f7791w.m4841b("Deferred Deep Link response empty.");
                        break;
                    } else {
                        try {
                            JSONObject jSONObject = new JSONObject(new String(bArr));
                            String optString = jSONObject.optString("deeplink", "");
                            String optString2 = jSONObject.optString("gclid", "");
                            double optDouble = jSONObject.optDouble("timestamp", 0.0d);
                            if (TextUtils.isEmpty(optString)) {
                                c1948n4.mo4962e().f7791w.m4841b("Deferred Deep Link is empty.");
                            } else {
                                C1838a7 m4973t = c1948n4.m4973t();
                                Object obj = m4973t.f8145k;
                                if (!TextUtils.isEmpty(optString) && (queryIntentActivities = ((C1948n4) m4973t.f8145k).f7917j.getPackageManager().queryIntentActivities(new Intent("android.intent.action.VIEW", Uri.parse(optString)), 0)) != null && !queryIntentActivities.isEmpty()) {
                                    Bundle bundle = new Bundle();
                                    bundle.putString("gclid", optString2);
                                    bundle.putString("_cis", "ddp");
                                    c1948n4.f7932y.m4930s("auto", "_cmp", bundle);
                                    C1838a7 m4973t2 = c1948n4.m4973t();
                                    if (!TextUtils.isEmpty(optString)) {
                                        try {
                                            SharedPreferences.Editor edit = ((C1948n4) m4973t2.f8145k).f7917j.getSharedPreferences("google.analytics.deferred.deeplink.prefs", 0).edit();
                                            edit.putString("deeplink", optString);
                                            edit.putLong("timestamp", Double.doubleToRawLongBits(optDouble));
                                            if (edit.commit()) {
                                                ((C1948n4) m4973t2.f8145k).f7917j.sendBroadcast(new Intent("android.google.analytics.action.DEEPLINK_ACTION"));
                                            }
                                        } catch (RuntimeException e6) {
                                            ((C1948n4) m4973t2.f8145k).mo4962e().f7784p.m4842c("Failed to persist Deferred Deep Link. exception", e6);
                                        }
                                    }
                                }
                                c1948n4.mo4962e().f7787s.m4843d("Deferred Deep Link validation failed. gclid, deep link", optString2, optString);
                            }
                            break;
                        } catch (JSONException e7) {
                            c1948n4.mo4962e().f7784p.m4842c("Failed to parse the Deferred Deep Link response. exception", e7);
                            return;
                        }
                    }
                }
                c1948n4.mo4962e().f7787s.m4843d("Network Request for Deferred Deep Link failed. response, exception", Integer.valueOf(i6), th);
                break;
            default:
                ((C1998t6) this.f7722k).m5085i(str, i6, th, bArr, map);
                break;
        }
    }

    /* renamed from: d */
    public final void m4830d() {
        C1948n4.m4953h((Context) this.f7722k, null).mo4962e().f7792x.m4841b("Local AppMeasurementService is shutting down");
    }

    /* renamed from: e */
    public final void m4831e(Runnable runnable) {
        C1998t6 m5063t = C1998t6.m5063t((Context) this.f7722k);
        m5063t.mo4959b().m4918q(new RunnableC1940m4(m5063t, runnable));
    }

    /* renamed from: f */
    public final void m4832f(Intent intent) {
        if (intent == null) {
            m4834h().f7784p.m4841b("onUnbind called with null intent");
        } else {
            m4834h().f7792x.m4842c("onUnbind called for intent. action", intent.getAction());
        }
    }

    /* renamed from: g */
    public final void m4833g(Intent intent) {
        if (intent == null) {
            m4834h().f7784p.m4841b("onRebind called with null intent");
        } else {
            m4834h().f7792x.m4842c("onRebind called. action", intent.getAction());
        }
    }

    /* renamed from: h */
    public final C1915j3 m4834h() {
        return C1948n4.m4953h((Context) this.f7722k, null).mo4962e();
    }

    public /* synthetic */ C1891g6(Object obj, int i6) {
        this.f7721j = i6;
        this.f7722k = obj;
    }
}
