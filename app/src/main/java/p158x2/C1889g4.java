package p158x2;

import android.text.TextUtils;
import androidx.lifecycle.C0258k;
import java.util.Iterator;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;
import p077l.C1047a;
import p077l.C1051e;
import p152w2.C1485a5;
import p152w2.C1494b1;
import p152w2.C1507c1;
import p152w2.C1520d1;
import p152w2.C1532e1;
import p152w2.C1533e2;
import p152w2.C1544f1;
import p152w2.C1545f2;
import p152w2.C1591j0;
import p152w2.C1627m0;
import p152w2.C1655o4;
import p152w2.C1792z9;
import p153w3.C1798e;

/* renamed from: x2.g4 */
/* loaded from: classes.dex */
public final class C1889g4 extends AbstractC1966p6 implements InterfaceC1857d {

    /* renamed from: n */
    public final Map<String, Map<String, String>> f7707n;

    /* renamed from: o */
    public final Map<String, Map<String, Boolean>> f7708o;

    /* renamed from: p */
    public final Map<String, Map<String, Boolean>> f7709p;

    /* renamed from: q */
    public final Map<String, C1532e1> f7710q;

    /* renamed from: r */
    public final Map<String, Map<String, Integer>> f7711r;

    /* renamed from: s */
    public final C1051e<String, C1591j0> f7712s;

    /* renamed from: t */
    public final C1891g6 f7713t;

    /* renamed from: u */
    public final Map<String, String> f7714u;

    public C1889g4(C1998t6 c1998t6) {
        super(c1998t6);
        this.f7707n = new C1047a();
        this.f7708o = new C1047a();
        this.f7709p = new C1047a();
        this.f7710q = new C1047a();
        this.f7714u = new C1047a();
        this.f7711r = new C1047a();
        this.f7712s = new C1880f4(this);
        this.f7713t = new C1891g6(this, 1);
    }

    /* renamed from: y */
    public static final Map<String, String> m4814y(C1532e1 c1532e1) {
        C1047a c1047a = new C1047a();
        for (C1544f1 c1544f1 : c1532e1.m3738w()) {
            c1047a.put(c1544f1.m3779s(), c1544f1.m3780t());
        }
        return c1047a;
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.Map<java.lang.String, java.util.Map<java.lang.String, java.lang.String>>, l.g] */
    @Override // p158x2.InterfaceC1857d
    /* renamed from: c */
    public final String mo1435c(String str, String str2) {
        mo4915h();
        m4824u(str);
        Map map = (Map) this.f7707n.getOrDefault(str, null);
        if (map != null) {
            return (String) map.get(str2);
        }
        return null;
    }

    @Override // p158x2.AbstractC1966p6
    /* renamed from: j */
    public final void mo4768j() {
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.Map<java.lang.String, w2.e1>, l.g] */
    /* renamed from: l */
    public final C1532e1 m4815l(String str) {
        m4994i();
        mo4915h();
        C1798e.m4554o(str);
        m4824u(str);
        return (C1532e1) this.f7710q.getOrDefault(str, null);
    }

    /* renamed from: m */
    public final boolean m4816m(String str) {
        mo4915h();
        C1532e1 m4815l = m4815l(str);
        if (m4815l == null) {
            return false;
        }
        return m4815l.m3731A();
    }

    /* JADX WARN: Type inference failed for: r0v5, types: [java.util.Map<java.lang.String, w2.e1>, l.g] */
    /* renamed from: n */
    public final boolean m4817n(String str) {
        C1532e1 c1532e1;
        C1792z9.m4507b();
        return (!((C1948n4) this.f8145k).f7923p.m4784q(null, C2026x2.f8225t0) || TextUtils.isEmpty(str) || (c1532e1 = (C1532e1) this.f7710q.getOrDefault(str, null)) == null || c1532e1.m3733C() == 0) ? false : true;
    }

    /* JADX WARN: Code restructure failed: missing block: B:100:0x02bd, code lost:
    
        r9 = r0.m3981x().iterator();
     */
    /* JADX WARN: Code restructure failed: missing block: B:102:0x02c9, code lost:
    
        r5 = "app_id";
        r21 = r3;
     */
    /* JADX WARN: Code restructure failed: missing block: B:103:0x02d7, code lost:
    
        if (r9.hasNext() == false) goto L213;
     */
    /* JADX WARN: Code restructure failed: missing block: B:104:0x02d9, code lost:
    
        r11 = r9.next();
        r6.m4994i();
        r6.mo4915h();
        p153w3.C1798e.m4554o(r28);
        p153w3.C1798e.m4560r(r11);
     */
    /* JADX WARN: Code restructure failed: missing block: B:105:0x02f3, code lost:
    
        if (android.text.TextUtils.isEmpty(r11.m4043u()) == false) goto L92;
     */
    /* JADX WARN: Code restructure failed: missing block: B:106:0x0322, code lost:
    
        r3 = r11.m3943f();
        r23 = r9;
        r9 = new android.content.ContentValues();
        r9.put("app_id", r28);
        r9.put("audience_id", java.lang.Integer.valueOf(r8));
     */
    /* JADX WARN: Code restructure failed: missing block: B:107:0x033b, code lost:
    
        if (r11.m4041s() == false) goto L95;
     */
    /* JADX WARN: Code restructure failed: missing block: B:108:0x033d, code lost:
    
        r5 = java.lang.Integer.valueOf(r11.m4042t());
     */
    /* JADX WARN: Code restructure failed: missing block: B:109:0x0347, code lost:
    
        r9.put("filter_id", r5);
        r9.put("event_name", r11.m4043u());
     */
    /* JADX WARN: Code restructure failed: missing block: B:110:0x0357, code lost:
    
        if (r11.m4039C() == false) goto L99;
     */
    /* JADX WARN: Code restructure failed: missing block: B:111:0x0359, code lost:
    
        r5 = java.lang.Boolean.valueOf(r11.m4040D());
     */
    /* JADX WARN: Code restructure failed: missing block: B:112:0x0363, code lost:
    
        r9.put("session_scoped", r5);
        r9.put("data", r3);
     */
    /* JADX WARN: Code restructure failed: missing block: B:115:0x0375, code lost:
    
        if (r6.m4849A().insertWithOnConflict(r19, null, r9, 5) != (-1)) goto L215;
     */
    /* JADX WARN: Code restructure failed: missing block: B:116:0x0377, code lost:
    
        ((p158x2.C1948n4) r6.f8145k).mo4962e().f7784p.m4842c("Failed to insert event filter (got -1). appId", p158x2.C1915j3.m4886t(r28));
     */
    /* JADX WARN: Code restructure failed: missing block: B:118:0x038a, code lost:
    
        r3 = r21;
        r9 = r23;
     */
    /* JADX WARN: Code restructure failed: missing block: B:121:0x0390, code lost:
    
        r0 = e;
     */
    /* JADX WARN: Code restructure failed: missing block: B:122:0x0391, code lost:
    
        r3 = ((p158x2.C1948n4) r6.f8145k).mo4962e().f7784p;
        r5 = "Error storing event filter. appId";
     */
    /* JADX WARN: Code restructure failed: missing block: B:123:0x0474, code lost:
    
        r3.m4843d(r5, p158x2.C1915j3.m4886t(r28), r0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:124:0x047b, code lost:
    
        r6.m4994i();
        r6.mo4915h();
        p153w3.C1798e.m4554o(r28);
        r0 = r6.m4849A();
        r11 = r18;
        r0.delete("property_filters", r11, new java.lang.String[]{r28, java.lang.String.valueOf(r8)});
        r0.delete(r19, r11, new java.lang.String[]{r28, java.lang.String.valueOf(r8)});
     */
    /* JADX WARN: Code restructure failed: missing block: B:125:0x04aa, code lost:
    
        r18 = r11;
        r3 = r21;
     */
    /* JADX WARN: Code restructure failed: missing block: B:127:0x0362, code lost:
    
        r5 = null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:128:0x0346, code lost:
    
        r5 = null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:130:0x02f5, code lost:
    
        r0 = ((p158x2.C1948n4) r6.f8145k).mo4962e().f7787s;
        r5 = p158x2.C1915j3.m4886t(r28);
        r9 = java.lang.Integer.valueOf(r8);
     */
    /* JADX WARN: Code restructure failed: missing block: B:131:0x030d, code lost:
    
        if (r11.m4041s() == false) goto L90;
     */
    /* JADX WARN: Code restructure failed: missing block: B:132:0x030f, code lost:
    
        r11 = java.lang.Integer.valueOf(r11.m4042t());
     */
    /* JADX WARN: Code restructure failed: missing block: B:133:0x0319, code lost:
    
        r0.m4844e("Event filter had no event name. Audience definition ignored. appId, audienceId, filterId", r5, r9, java.lang.String.valueOf(r11));
     */
    /* JADX WARN: Code restructure failed: missing block: B:134:0x0318, code lost:
    
        r11 = null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:136:0x039f, code lost:
    
        r0 = r0.m3978u().iterator();
     */
    /* JADX WARN: Code restructure failed: missing block: B:138:0x03ab, code lost:
    
        if (r0.hasNext() == false) goto L218;
     */
    /* JADX WARN: Code restructure failed: missing block: B:139:0x03ad, code lost:
    
        r3 = r0.next();
        r6.m4994i();
        r6.mo4915h();
        p153w3.C1798e.m4554o(r28);
        p153w3.C1798e.m4560r(r3);
     */
    /* JADX WARN: Code restructure failed: missing block: B:140:0x03c7, code lost:
    
        if (android.text.TextUtils.isEmpty(r3.m4432u()) == false) goto L117;
     */
    /* JADX WARN: Code restructure failed: missing block: B:141:0x03f6, code lost:
    
        r9 = r3.m3943f();
        r11 = new android.content.ContentValues();
        r11.put(r5, r28);
        r23 = r0;
        r11.put("audience_id", java.lang.Integer.valueOf(r8));
     */
    /* JADX WARN: Code restructure failed: missing block: B:142:0x040f, code lost:
    
        if (r3.m4430s() == false) goto L120;
     */
    /* JADX WARN: Code restructure failed: missing block: B:143:0x0411, code lost:
    
        r0 = java.lang.Integer.valueOf(r3.m4431t());
     */
    /* JADX WARN: Code restructure failed: missing block: B:144:0x041b, code lost:
    
        r11.put("filter_id", r0);
        r24 = r5;
        r11.put("property_name", r3.m4432u());
     */
    /* JADX WARN: Code restructure failed: missing block: B:145:0x042d, code lost:
    
        if (r3.m4436y() == false) goto L124;
     */
    /* JADX WARN: Code restructure failed: missing block: B:146:0x042f, code lost:
    
        r0 = java.lang.Boolean.valueOf(r3.m4437z());
     */
    /* JADX WARN: Code restructure failed: missing block: B:147:0x0439, code lost:
    
        r11.put("session_scoped", r0);
        r11.put("data", r9);
     */
    /* JADX WARN: Code restructure failed: missing block: B:150:0x044b, code lost:
    
        if (r6.m4849A().insertWithOnConflict("property_filters", null, r11, 5) != (-1)) goto L130;
     */
    /* JADX WARN: Code restructure failed: missing block: B:151:0x0461, code lost:
    
        r0 = r23;
        r5 = r24;
     */
    /* JADX WARN: Code restructure failed: missing block: B:153:0x044d, code lost:
    
        ((p158x2.C1948n4) r6.f8145k).mo4962e().f7784p.m4842c("Failed to insert property filter (got -1). appId", p158x2.C1915j3.m4886t(r28));
     */
    /* JADX WARN: Code restructure failed: missing block: B:155:0x0467, code lost:
    
        r0 = e;
     */
    /* JADX WARN: Code restructure failed: missing block: B:156:0x0468, code lost:
    
        r3 = ((p158x2.C1948n4) r6.f8145k).mo4962e().f7784p;
        r5 = "Error storing property filter. appId";
     */
    /* JADX WARN: Code restructure failed: missing block: B:157:0x0438, code lost:
    
        r0 = null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:158:0x041a, code lost:
    
        r0 = null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:160:0x03c9, code lost:
    
        r0 = ((p158x2.C1948n4) r6.f8145k).mo4962e().f7787s;
        r9 = p158x2.C1915j3.m4886t(r28);
        r11 = java.lang.Integer.valueOf(r8);
     */
    /* JADX WARN: Code restructure failed: missing block: B:161:0x03e1, code lost:
    
        if (r3.m4430s() == false) goto L115;
     */
    /* JADX WARN: Code restructure failed: missing block: B:162:0x03e3, code lost:
    
        r3 = java.lang.Integer.valueOf(r3.m4431t());
     */
    /* JADX WARN: Code restructure failed: missing block: B:163:0x03ed, code lost:
    
        r0.m4844e("Property filter had no property name. Audience definition ignored. appId, audienceId, filterId", r9, r11, java.lang.String.valueOf(r3));
     */
    /* JADX WARN: Code restructure failed: missing block: B:164:0x03ec, code lost:
    
        r3 = null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:166:0x04a8, code lost:
    
        r11 = r18;
     */
    /* JADX WARN: Code restructure failed: missing block: B:86:0x02b4, code lost:
    
        r0.m4843d(r5, p158x2.C1915j3.m4886t(r28), java.lang.Integer.valueOf(r8));
     */
    /* JADX WARN: Code restructure failed: missing block: B:91:0x028d, code lost:
    
        r9 = r0.m3978u().iterator();
     */
    /* JADX WARN: Code restructure failed: missing block: B:93:0x0299, code lost:
    
        if (r9.hasNext() == false) goto L202;
     */
    /* JADX WARN: Code restructure failed: missing block: B:95:0x02a5, code lost:
    
        if (r9.next().m4430s() != false) goto L211;
     */
    /* JADX WARN: Code restructure failed: missing block: B:97:0x02a7, code lost:
    
        r0 = ((p158x2.C1948n4) r6.f8145k).mo4962e().f7787s;
        r5 = "Property filter with no ID. Audience definition ignored. appId, audienceId";
     */
    /* JADX WARN: Type inference failed for: r0v22, types: [java.util.Map<java.lang.String, w2.e1>, l.g] */
    /* JADX WARN: Type inference failed for: r0v6, types: [java.util.Map<java.lang.String, w2.e1>, l.g] */
    /* JADX WARN: Type inference failed for: r0v7, types: [java.util.Map<java.lang.String, java.lang.String>, l.g] */
    /* JADX WARN: Type inference failed for: r0v8, types: [java.util.Map<java.lang.String, java.util.Map<java.lang.String, java.lang.String>>, l.g] */
    /* renamed from: o */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m4818o(java.lang.String r28, byte[] r29, java.lang.String r30) {
        /*
            Method dump skipped, instructions count: 1564
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1889g4.m4818o(java.lang.String, byte[], java.lang.String):void");
    }

    /* JADX WARN: Type inference failed for: r0v2, types: [java.util.Map<java.lang.String, java.util.Map<java.lang.String, java.lang.Boolean>>, l.g] */
    /* renamed from: p */
    public final boolean m4819p(String str, String str2) {
        Boolean bool;
        mo4915h();
        m4824u(str);
        if (m4822s(str) && C1838a7.m4700F(str2)) {
            return true;
        }
        if (m4823t(str) && C1838a7.m4706X(str2)) {
            return true;
        }
        Map map = (Map) this.f7708o.getOrDefault(str, null);
        if (map == null || (bool = (Boolean) map.get(str2)) == null) {
            return false;
        }
        return bool.booleanValue();
    }

    /* JADX WARN: Type inference failed for: r0v6, types: [java.util.Map<java.lang.String, java.util.Map<java.lang.String, java.lang.Boolean>>, l.g] */
    /* renamed from: q */
    public final boolean m4820q(String str, String str2) {
        Boolean bool;
        mo4915h();
        m4824u(str);
        if ("ecommerce_purchase".equals(str2) || "purchase".equals(str2) || "refund".equals(str2)) {
            return true;
        }
        Map map = (Map) this.f7709p.getOrDefault(str, null);
        if (map == null || (bool = (Boolean) map.get(str2)) == null) {
            return false;
        }
        return bool.booleanValue();
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.Map<java.lang.String, java.util.Map<java.lang.String, java.lang.Integer>>, l.g] */
    /* renamed from: r */
    public final int m4821r(String str, String str2) {
        Integer num;
        mo4915h();
        m4824u(str);
        Map map = (Map) this.f7711r.getOrDefault(str, null);
        if (map == null || (num = (Integer) map.get(str2)) == null) {
            return 1;
        }
        return num.intValue();
    }

    /* renamed from: s */
    public final boolean m4822s(String str) {
        return "1".equals(mo1435c(str, "measurement.upload.blacklist_internal"));
    }

    /* renamed from: t */
    public final boolean m4823t(String str) {
        return "1".equals(mo1435c(str, "measurement.upload.blacklist_public"));
    }

    /* JADX WARN: Code restructure failed: missing block: B:34:0x008e, code lost:
    
        if (r2 == null) goto L25;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:13:0x0096  */
    /* JADX WARN: Removed duplicated region for block: B:17:0x00b2  */
    /* JADX WARN: Removed duplicated region for block: B:39:0x011f  */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.Map<java.lang.String, w2.e1>, l.g] */
    /* JADX WARN: Type inference failed for: r0v23, types: [l.g] */
    /* JADX WARN: Type inference failed for: r0v24, types: [java.util.Map<java.lang.String, java.util.Map<java.lang.String, java.lang.String>>, l.g] */
    /* JADX WARN: Type inference failed for: r0v25, types: [java.util.Map<java.lang.String, java.util.Map<java.lang.String, java.lang.Boolean>>, l.g] */
    /* JADX WARN: Type inference failed for: r0v26, types: [java.util.Map<java.lang.String, java.util.Map<java.lang.String, java.lang.Boolean>>, l.g] */
    /* JADX WARN: Type inference failed for: r0v27, types: [java.util.Map<java.lang.String, w2.e1>, l.g] */
    /* JADX WARN: Type inference failed for: r0v28, types: [java.util.Map<java.lang.String, java.lang.String>, l.g] */
    /* JADX WARN: Type inference failed for: r0v30 */
    /* JADX WARN: Type inference failed for: r0v31 */
    /* JADX WARN: Type inference failed for: r0v32 */
    /* JADX WARN: Type inference failed for: r2v4, types: [java.util.Map<java.lang.String, java.util.Map<java.lang.String, java.lang.String>>, l.g] */
    /* JADX WARN: Type inference failed for: r2v5, types: [java.util.Map<java.lang.String, w2.e1>, l.g] */
    /* renamed from: u */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m4824u(java.lang.String r13) {
        /*
            Method dump skipped, instructions count: 292
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1889g4.m4824u(java.lang.String):void");
    }

    /* JADX WARN: Type inference failed for: r11v1, types: [java.util.Map<java.lang.String, java.util.Map<java.lang.String, java.lang.Boolean>>, l.g] */
    /* JADX WARN: Type inference failed for: r11v2, types: [java.util.Map<java.lang.String, java.util.Map<java.lang.String, java.lang.Boolean>>, l.g] */
    /* JADX WARN: Type inference failed for: r11v3, types: [java.util.Map<java.lang.String, java.util.Map<java.lang.String, java.lang.Integer>>, l.g] */
    /* renamed from: v */
    public final void m4825v(String str, C1520d1 c1520d1) {
        C1047a c1047a = new C1047a();
        C1047a c1047a2 = new C1047a();
        C1047a c1047a3 = new C1047a();
        for (int i6 = 0; i6 < ((C1532e1) c1520d1.f7099k).m3739x(); i6++) {
            C1494b1 m4066n = ((C1532e1) c1520d1.f7099k).m3740y(i6).m4066n();
            if (TextUtils.isEmpty(m4066n.m3654l())) {
                ((C1948n4) this.f8145k).mo4962e().f7787s.m4841b("EventConfig contained null event name");
            } else {
                String m3654l = m4066n.m3654l();
                String m4537d0 = C1798e.m4537d0(m4066n.m3654l());
                if (!TextUtils.isEmpty(m4537d0)) {
                    if (m4066n.f7100l) {
                        m4066n.m3950i();
                        m4066n.f7100l = false;
                    }
                    C1507c1.m3678y((C1507c1) m4066n.f7099k, m4537d0);
                    if (c1520d1.f7100l) {
                        c1520d1.m3950i();
                        c1520d1.f7100l = false;
                    }
                    C1532e1.m3729G((C1532e1) c1520d1.f7099k, i6, m4066n.m3947f());
                }
                c1047a.put(m3654l, Boolean.valueOf(((C1507c1) m4066n.f7099k).m3680t()));
                c1047a2.put(m4066n.m3654l(), Boolean.valueOf(((C1507c1) m4066n.f7099k).m3681u()));
                if (((C1507c1) m4066n.f7099k).m3682v()) {
                    if (m4066n.m3655m() < 2 || m4066n.m3655m() > 65535) {
                        ((C1948n4) this.f8145k).mo4962e().f7787s.m4843d("Invalid sampling rate. Event name, sample rate", m4066n.m3654l(), Integer.valueOf(m4066n.m3655m()));
                    } else {
                        c1047a3.put(m4066n.m3654l(), Integer.valueOf(m4066n.m3655m()));
                    }
                }
            }
        }
        this.f7708o.put(str, c1047a);
        this.f7709p.put(str, c1047a2);
        this.f7711r.put(str, c1047a3);
    }

    /* renamed from: w */
    public final void m4826w(String str, C1532e1 c1532e1) {
        if (c1532e1.m3733C() == 0) {
            C1051e<String, C1591j0> c1051e = this.f7712s;
            Objects.requireNonNull(c1051e);
            Objects.requireNonNull(str, "key == null");
            synchronized (c1051e) {
                if (c1051e.f5012a.remove(str) != null) {
                    c1051e.f5013b--;
                }
            }
            return;
        }
        ((C1948n4) this.f8145k).mo4962e().f7792x.m4842c("EES programs found", Integer.valueOf(c1532e1.m3733C()));
        int i6 = 0;
        C1545f2 c1545f2 = c1532e1.m3732B().get(0);
        try {
            C1591j0 c1591j0 = new C1591j0();
            ((C0258k) c1591j0.f7017a.f6356m).m889a("internal.remoteConfig", new CallableC1862d4(this, str, i6));
            ((C0258k) c1591j0.f7017a.f6356m).m889a("internal.logger", new Callable(this) { // from class: x2.e4

                /* renamed from: a */
                public final C1889g4 f7661a;

                {
                    this.f7661a = this;
                }

                @Override // java.util.concurrent.Callable
                public final Object call() {
                    return new C1655o4(this.f7661a.f7713t);
                }
            });
            c1591j0.m3870b(c1545f2);
            this.f7712s.m2678c(str, c1591j0);
            ((C1948n4) this.f8145k).mo4962e().f7792x.m4843d("EES program loaded for appId, activities", str, Integer.valueOf(c1545f2.m3783t().m3712t()));
            Iterator<C1533e2> it = c1545f2.m3783t().m3711s().iterator();
            while (it.hasNext()) {
                ((C1948n4) this.f8145k).mo4962e().f7792x.m4842c("EES program activity", it.next().m3743s());
            }
        } catch (C1627m0 unused) {
            ((C1948n4) this.f8145k).mo4962e().f7784p.m4842c("Failed to load EES program. appId", str);
        }
    }

    /* renamed from: x */
    public final C1532e1 m4827x(String str, byte[] bArr) {
        C1915j3 mo4962e;
        if (bArr == null) {
            return C1532e1.m3727E();
        }
        try {
            C1532e1 m3947f = ((C1520d1) C2014v6.m5111H(C1532e1.m3726D(), bArr)).m3947f();
            ((C1948n4) this.f8145k).mo4962e().f7792x.m4843d("Parsed config. version, gmp_app_id", m3947f.m3734s() ? Long.valueOf(m3947f.m3735t()) : null, m3947f.m3736u() ? m3947f.m3737v() : null);
            return m3947f;
        } catch (RuntimeException e6) {
            e = e6;
            mo4962e = ((C1948n4) this.f8145k).mo4962e();
            mo4962e.f7787s.m4843d("Unable to merge remote config. appId", C1915j3.m4886t(str), e);
            return C1532e1.m3727E();
        } catch (C1485a5 e7) {
            e = e7;
            mo4962e = ((C1948n4) this.f8145k).mo4962e();
            mo4962e.f7787s.m4843d("Unable to merge remote config. appId", C1915j3.m4886t(str), e);
            return C1532e1.m3727E();
        }
    }
}
