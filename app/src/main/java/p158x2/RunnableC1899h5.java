package p158x2;

/* renamed from: x2.h5 */
/* loaded from: classes.dex */
public final class RunnableC1899h5 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ C1875f f7737j;

    /* renamed from: k */
    public final /* synthetic */ int f7738k;

    /* renamed from: l */
    public final /* synthetic */ long f7739l;

    /* renamed from: m */
    public final /* synthetic */ boolean f7740m;

    /* renamed from: n */
    public final /* synthetic */ C1933l5 f7741n;

    public RunnableC1899h5(C1933l5 c1933l5, C1875f c1875f, int i6, long j6, boolean z5) {
        this.f7741n = c1933l5;
        this.f7737j = c1875f;
        this.f7738k = i6;
        this.f7739l = j6;
        this.f7740m = z5;
    }

    @Override // java.lang.Runnable
    public final void run() {
        this.f7741n.m4929r(this.f7737j);
        C1933l5.m4923p(this.f7741n, this.f7737j, this.f7738k, this.f7739l, false, this.f7740m);
    }
}
