package p158x2;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.os.SystemClock;
import android.text.TextUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import p113q2.C1278f;
import p152w2.C1551f8;
import p152w2.C1695r8;
import p153w3.C1798e;

/* renamed from: x2.l5 */
/* loaded from: classes.dex */
public final class C1933l5 extends AbstractC2011v3 {

    /* renamed from: m */
    public C1925k5 f7842m;

    /* renamed from: n */
    public final Set<InterfaceC2044z4> f7843n;

    /* renamed from: o */
    public boolean f7844o;

    /* renamed from: p */
    public final AtomicReference<String> f7845p;

    /* renamed from: q */
    public final Object f7846q;

    /* renamed from: r */
    public C1875f f7847r;

    /* renamed from: s */
    public int f7848s;

    /* renamed from: t */
    public final AtomicLong f7849t;

    /* renamed from: u */
    public long f7850u;

    /* renamed from: v */
    public int f7851v;

    /* renamed from: w */
    public final C1865d7 f7852w;

    /* renamed from: x */
    public boolean f7853x;

    /* renamed from: y */
    public final C2020w4 f7854y;

    public C1933l5(C1948n4 c1948n4) {
        super(c1948n4);
        this.f7843n = new CopyOnWriteArraySet();
        this.f7846q = new Object();
        this.f7853x = true;
        this.f7854y = new C2020w4(this, 1);
        this.f7845p = new AtomicReference<>();
        this.f7847r = new C1875f(null, null);
        this.f7848s = 100;
        this.f7850u = -1L;
        this.f7851v = 100;
        this.f7849t = new AtomicLong(0L);
        this.f7852w = new C1865d7(c1948n4);
    }

    /* renamed from: p */
    public static void m4923p(C1933l5 c1933l5, C1875f c1875f, int i6, long j6, boolean z5, boolean z6) {
        String str;
        Object obj;
        C1897h3 c1897h3;
        c1933l5.mo4915h();
        c1933l5.m5102i();
        if (j6 <= c1933l5.f7850u) {
            if (c1933l5.f7851v <= i6) {
                str = "Dropped out-of-date consent setting, proposed settings";
                c1897h3 = ((C1948n4) c1933l5.f8145k).mo4962e().f7790v;
                obj = c1875f;
                c1897h3.m4842c(str, obj);
                return;
            }
        }
        C2027x3 m4970q = ((C1948n4) c1933l5.f8145k).m4970q();
        Object obj2 = m4970q.f8145k;
        m4970q.mo4915h();
        if (!m4970q.m5148r(i6)) {
            C1897h3 c1897h32 = ((C1948n4) c1933l5.f8145k).mo4962e().f7790v;
            Object valueOf = Integer.valueOf(i6);
            str = "Lower precedence consent source ignored, proposed source";
            c1897h3 = c1897h32;
            obj = valueOf;
            c1897h3.m4842c(str, obj);
            return;
        }
        SharedPreferences.Editor edit = m4970q.m5145o().edit();
        edit.putString("consent_settings", c1875f.m4805b());
        edit.putInt("consent_source", i6);
        edit.apply();
        c1933l5.f7850u = j6;
        c1933l5.f7851v = i6;
        C1855c6 m4979z = ((C1948n4) c1933l5.f8145k).m4979z();
        m4979z.mo4915h();
        m4979z.m5102i();
        if (z5) {
            m4979z.m4755q();
            ((C1948n4) m4979z.f8145k).m4975v().m4763l();
        }
        if (m4979z.m4754o()) {
            m4979z.m4757s(new RunnableC2013v5(m4979z, m4979z.m4759u(false), 2));
        }
        if (z6) {
            C1855c6 m4979z2 = ((C1948n4) c1933l5.f8145k).m4979z();
            AtomicReference atomicReference = new AtomicReference();
            m4979z2.mo4915h();
            m4979z2.m5102i();
            m4979z2.m4757s(new RunnableC1956o4(m4979z2, atomicReference, m4979z2.m4759u(false)));
        }
    }

    @Override // p158x2.AbstractC2011v3
    /* renamed from: k */
    public final boolean mo4746k() {
        return false;
    }

    /* renamed from: l */
    public final void m4924l(String str, String str2, long j6, Object obj) {
        ((C1948n4) this.f8145k).mo4959b().m4918q(new RunnableC2004u4(this, str, str2, obj, j6, 1));
    }

    /* renamed from: m */
    public final void m4925m(String str, String str2, Object obj, long j6) {
        C1798e.m4554o(str);
        C1798e.m4554o(str2);
        mo4915h();
        m5102i();
        if ("allow_personalized_ads".equals(str2)) {
            if (obj instanceof String) {
                String str3 = (String) obj;
                if (!TextUtils.isEmpty(str3)) {
                    Long valueOf = Long.valueOf(true != "false".equals(str3.toLowerCase(Locale.ENGLISH)) ? 0L : 1L);
                    ((C1948n4) this.f8145k).m4970q().f8250w.m5137b(valueOf.longValue() == 1 ? "true" : "false");
                    obj = valueOf;
                    str2 = "_npa";
                }
            }
            if (obj == null) {
                ((C1948n4) this.f8145k).m4970q().f8250w.m5137b("unset");
                str2 = "_npa";
            }
        }
        String str4 = str2;
        Object obj2 = obj;
        if (!((C1948n4) this.f8145k).m4965i()) {
            ((C1948n4) this.f8145k).mo4962e().f7792x.m4841b("User property not set since app measurement is disabled");
            return;
        }
        if (((C1948n4) this.f8145k).m4968l()) {
            C2022w6 c2022w6 = new C2022w6(str4, j6, obj2, str);
            C1855c6 m4979z = ((C1948n4) this.f8145k).m4979z();
            m4979z.mo4915h();
            m4979z.m5102i();
            m4979z.m4755q();
            C1861d3 m4975v = ((C1948n4) m4979z.f8145k).m4975v();
            Objects.requireNonNull(m4975v);
            Parcel obtain = Parcel.obtain();
            boolean z5 = false;
            C2030x6.m5156a(c2022w6, obtain);
            byte[] marshall = obtain.marshall();
            obtain.recycle();
            if (marshall.length > 131072) {
                ((C1948n4) m4975v.f8145k).mo4962e().f7785q.m4841b("User property too long for local database. Sending directly to service");
            } else {
                z5 = m4975v.m4767p(1, marshall);
            }
            m4979z.m4757s(new RunnableC2005u5(m4979z, m4979z.m4759u(true), z5, c2022w6));
        }
    }

    /* renamed from: n */
    public final void m4926n(String str) {
        this.f7845p.set(str);
    }

    /* renamed from: o */
    public final void m4927o() {
        mo4915h();
        m5102i();
        if (((C1948n4) this.f8145k).m4968l()) {
            if (((C1948n4) this.f8145k).f7923p.m4784q(null, C2026x2.f8180U)) {
                C1866e c1866e = ((C1948n4) this.f8145k).f7923p;
                Objects.requireNonNull((C1948n4) c1866e.f8145k);
                Boolean m4786s = c1866e.m4786s("google_analytics_deferred_deep_link_enabled");
                if (m4786s != null && m4786s.booleanValue()) {
                    ((C1948n4) this.f8145k).mo4962e().f7791w.m4841b("Deferred Deep Link feature enabled.");
                    ((C1948n4) this.f8145k).mo4959b().m4918q(new RunnableC1845b5(this, 0));
                }
            }
            C1855c6 m4979z = ((C1948n4) this.f8145k).m4979z();
            m4979z.mo4915h();
            m4979z.m5102i();
            C1847b7 m4759u = m4979z.m4759u(true);
            ((C1948n4) m4979z.f8145k).m4975v().m4767p(3, new byte[0]);
            m4979z.m4757s(new RunnableC2013v5(m4979z, m4759u, 1));
            this.f7853x = false;
            C2027x3 m4970q = ((C1948n4) this.f8145k).m4970q();
            m4970q.mo4915h();
            String string = m4970q.m5145o().getString("previous_os_version", null);
            ((C1948n4) m4970q.f8145k).m4957A().m5153l();
            String str = Build.VERSION.RELEASE;
            if (!TextUtils.isEmpty(str) && !str.equals(string)) {
                SharedPreferences.Editor edit = m4970q.m5145o().edit();
                edit.putString("previous_os_version", str);
                edit.apply();
            }
            if (TextUtils.isEmpty(string)) {
                return;
            }
            ((C1948n4) this.f8145k).m4957A().m5153l();
            if (string.equals(str)) {
                return;
            }
            Bundle bundle = new Bundle();
            bundle.putString("_po", string);
            m4930s("auto", "_ou", bundle);
        }
    }

    /* renamed from: q */
    public final void m4928q(C1875f c1875f, int i6, long j6) {
        boolean z5;
        boolean z6;
        C1875f c1875f2;
        boolean z7;
        m5102i();
        if (i6 != -10 && c1875f.f7679a == null && c1875f.f7680b == null) {
            ((C1948n4) this.f8145k).mo4962e().f7789u.m4841b("Discarding empty consent settings");
            return;
        }
        synchronized (this.f7846q) {
            z5 = true;
            boolean z8 = false;
            if (i6 <= this.f7848s) {
                z6 = c1875f.m4808e(this.f7847r);
                if (c1875f.m4807d() && !this.f7847r.m4807d()) {
                    z8 = true;
                }
                C1875f c1875f3 = this.f7847r;
                Boolean bool = c1875f.f7679a;
                if (bool == null) {
                    bool = c1875f3.f7679a;
                }
                Boolean bool2 = c1875f.f7680b;
                if (bool2 == null) {
                    bool2 = c1875f3.f7680b;
                }
                C1875f c1875f4 = new C1875f(bool, bool2);
                this.f7847r = c1875f4;
                this.f7848s = i6;
                z7 = z8;
                c1875f2 = c1875f4;
            } else {
                z6 = false;
                z5 = false;
                c1875f2 = c1875f;
                z7 = false;
            }
        }
        if (!z5) {
            ((C1948n4) this.f8145k).mo4962e().f7790v.m4842c("Ignoring lower-priority consent settings, proposed settings", c1875f2);
            return;
        }
        long andIncrement = this.f7849t.getAndIncrement();
        if (z6) {
            this.f7845p.set(null);
            ((C1948n4) this.f8145k).mo4959b().m4920s(new RunnableC1890g5(this, c1875f2, j6, i6, andIncrement, z7));
        } else if (i6 == 30 || i6 == -10) {
            ((C1948n4) this.f8145k).mo4959b().m4920s(new RunnableC1899h5(this, c1875f2, i6, andIncrement, z7));
        } else {
            ((C1948n4) this.f8145k).mo4959b().m4918q(new RunnableC1908i5(this, c1875f2, i6, andIncrement, z7));
        }
    }

    /* renamed from: r */
    public final void m4929r(C1875f c1875f) {
        Long valueOf;
        mo4915h();
        boolean z5 = (c1875f.m4807d() && c1875f.m4806c()) || ((C1948n4) this.f8145k).m4979z().m4754o();
        C1948n4 c1948n4 = (C1948n4) this.f8145k;
        c1948n4.mo4959b().mo4915h();
        if (z5 != c1948n4.f7913N) {
            C1948n4 c1948n42 = (C1948n4) this.f8145k;
            c1948n42.mo4959b().mo4915h();
            c1948n42.f7913N = z5;
            C2027x3 m4970q = ((C1948n4) this.f8145k).m4970q();
            Object obj = m4970q.f8145k;
            m4970q.mo4915h();
            Boolean valueOf2 = m4970q.m5145o().contains("measurement_enabled_from_api") ? Boolean.valueOf(m4970q.m5145o().getBoolean("measurement_enabled_from_api", true)) : null;
            if (!z5 || valueOf2 == null || valueOf2.booleanValue()) {
                Boolean valueOf3 = Boolean.valueOf(z5);
                mo4915h();
                m5102i();
                ((C1948n4) this.f8145k).mo4962e().f7791w.m4842c("Setting app measurement enabled (FE)", valueOf3);
                ((C1948n4) this.f8145k).m4970q().m5146p(valueOf3);
                C1948n4 c1948n43 = (C1948n4) this.f8145k;
                c1948n43.mo4959b().mo4915h();
                if (c1948n43.f7913N || !(valueOf3 == null || valueOf3.booleanValue())) {
                    mo4915h();
                    String m5136a = ((C1948n4) this.f8145k).m4970q().f8250w.m5136a();
                    if (m5136a != null) {
                        if ("unset".equals(m5136a)) {
                            valueOf = null;
                        } else {
                            valueOf = Long.valueOf(true != "true".equals(m5136a) ? 0L : 1L);
                        }
                        Objects.requireNonNull(((C1948n4) this.f8145k).f7930w);
                        m4925m("app", "_npa", valueOf, System.currentTimeMillis());
                    }
                    if (!((C1948n4) this.f8145k).m4965i() || !this.f7853x) {
                        ((C1948n4) this.f8145k).mo4962e().f7791w.m4841b("Updating Scion state (FE)");
                        C1855c6 m4979z = ((C1948n4) this.f8145k).m4979z();
                        m4979z.mo4915h();
                        m4979z.m5102i();
                        m4979z.m4757s(new RunnableC1911j(m4979z, m4979z.m4759u(true), 4));
                        return;
                    }
                    ((C1948n4) this.f8145k).mo4962e().f7791w.m4841b("Recording app launch after enabling measurement for the first time (FE)");
                    m4927o();
                    C1695r8.m4069b();
                    if (((C1948n4) this.f8145k).f7923p.m4784q(null, C2026x2.f8201h0)) {
                        ((C1948n4) this.f8145k).m4971r().f7856n.m4907a();
                    }
                    ((C1948n4) this.f8145k).mo4959b().m4918q(new RunnableC1854c5(this, 0));
                }
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:30:0x0091, code lost:
    
        if (r4 > 100) goto L105;
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x00c5, code lost:
    
        if (r5 > 100) goto L113;
     */
    /* renamed from: s */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void m4930s(java.lang.String r26, java.lang.String r27, android.os.Bundle r28) {
        /*
            Method dump skipped, instructions count: 405
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1933l5.m4930s(java.lang.String, java.lang.String, android.os.Bundle):void");
    }

    /* renamed from: t */
    public final void m4931t(String str, String str2, long j6, Bundle bundle) {
        mo4915h();
        m4932u(str, str2, j6, bundle, true, true, false, null);
    }

    /* JADX WARN: Type inference failed for: r0v27, types: [java.util.Set<x2.z4>, java.util.concurrent.CopyOnWriteArraySet] */
    /* renamed from: u */
    public final void m4932u(String str, String str2, long j6, Bundle bundle, boolean z5, boolean z6, boolean z7, String str3) {
        Bundle bundle2;
        boolean m4767p;
        ArrayList arrayList;
        Bundle[] bundleArr;
        Object[] array;
        C1798e.m4554o(str);
        Objects.requireNonNull(bundle, "null reference");
        mo4915h();
        m5102i();
        if (!((C1948n4) this.f8145k).m4965i()) {
            ((C1948n4) this.f8145k).mo4962e().f7791w.m4841b("Event not sent since app measurement is disabled");
            return;
        }
        List<String> list = ((C1948n4) this.f8145k).m4960c().f7580s;
        if (list != null && !list.contains(str2)) {
            ((C1948n4) this.f8145k).mo4962e().f7791w.m4843d("Dropping non-safelisted event. event name, origin", str2, str);
            return;
        }
        if (!this.f7844o) {
            this.f7844o = true;
            try {
                Object obj = this.f8145k;
                try {
                    (!((C1948n4) obj).f7921n ? Class.forName("com.google.android.gms.tagmanager.TagManagerService", true, ((C1948n4) obj).f7917j.getClassLoader()) : Class.forName("com.google.android.gms.tagmanager.TagManagerService")).getDeclaredMethod("initialize", Context.class).invoke(null, ((C1948n4) this.f8145k).f7917j);
                } catch (Exception e6) {
                    ((C1948n4) this.f8145k).mo4962e().f7787s.m4842c("Failed to invoke Tag Manager's initialize() method", e6);
                }
            } catch (ClassNotFoundException unused) {
                ((C1948n4) this.f8145k).mo4962e().f7790v.m4841b("Tag Manager is not found and thus will not be used");
            }
        }
        if (((C1948n4) this.f8145k).f7923p.m4784q(null, C2026x2.f8181V) && "_cmp".equals(str2) && bundle.containsKey("gclid")) {
            Objects.requireNonNull((C1948n4) this.f8145k);
            String string = bundle.getString("gclid");
            Objects.requireNonNull(((C1948n4) this.f8145k).f7930w);
            m4925m("auto", "_lgclid", string, System.currentTimeMillis());
        }
        Objects.requireNonNull((C1948n4) this.f8145k);
        if (z5 && (!C1838a7.f7555r[0].equals(str2))) {
            ((C1948n4) this.f8145k).m4973t().m4741v(bundle, ((C1948n4) this.f8145k).m4970q().f8239G.m5048a());
        }
        if (z7) {
            Objects.requireNonNull((C1948n4) this.f8145k);
            if (!"_iap".equals(str2)) {
                C1838a7 m4973t = ((C1948n4) this.f8145k).m4973t();
                int i6 = 2;
                if (m4973t.m4722Z("event", str2)) {
                    if (m4973t.m4724b0("event", C1798e.f7329H, C1798e.f7330I, str2)) {
                        Objects.requireNonNull((C1948n4) m4973t.f8145k);
                        if (m4973t.m4725c0("event", 40, str2)) {
                            i6 = 0;
                        }
                    } else {
                        i6 = 13;
                    }
                }
                if (i6 != 0) {
                    ((C1948n4) this.f8145k).mo4962e().f7786r.m4842c("Invalid public event name. Event will not be logged (FE)", ((C1948n4) this.f8145k).m4974u().m4794p(str2));
                    C1838a7 m4973t2 = ((C1948n4) this.f8145k).m4973t();
                    Objects.requireNonNull((C1948n4) this.f8145k);
                    ((C1948n4) this.f8145k).m4973t().m4707A(this.f7854y, null, i6, "_ev", m4973t2.m4736q(str2, 40, true), str2 != null ? str2.length() : 0, ((C1948n4) this.f8145k).f7923p.m4784q(null, C2026x2.f8217p0));
                    return;
                }
            }
        }
        Objects.requireNonNull((C1948n4) this.f8145k);
        C1981r5 m5056o = ((C1948n4) this.f8145k).m4978y().m5056o(false);
        if (m5056o != null && !bundle.containsKey("_sc")) {
            m5056o.f8023d = true;
        }
        C1997t5.m5052q(m5056o, bundle, z5 && z7);
        boolean equals = "am".equals(str);
        C1838a7.m4700F(str2);
        if (((C1948n4) this.f8145k).m4968l()) {
            int m4726d0 = ((C1948n4) this.f8145k).m4973t().m4726d0(str2);
            if (m4726d0 != 0) {
                ((C1948n4) this.f8145k).mo4962e().f7786r.m4842c("Invalid event name. Event will not be logged (FE)", ((C1948n4) this.f8145k).m4974u().m4794p(str2));
                C1838a7 m4973t3 = ((C1948n4) this.f8145k).m4973t();
                Objects.requireNonNull((C1948n4) this.f8145k);
                ((C1948n4) this.f8145k).m4973t().m4707A(this.f7854y, str3, m4726d0, "_ev", m4973t3.m4736q(str2, 40, true), str2 != null ? str2.length() : 0, ((C1948n4) this.f8145k).f7923p.m4784q(null, C2026x2.f8217p0));
                return;
            }
            String str4 = "_o";
            Bundle m4739t = ((C1948n4) this.f8145k).m4973t().m4739t(str3, str2, bundle, Collections.unmodifiableList(Arrays.asList("_o", "_sn", "_sc", "_si")), z7);
            if (m4739t.containsKey("_sc") && m4739t.containsKey("_si")) {
                m4739t.getString("_sn");
                m4739t.getString("_sc");
                m4739t.getLong("_si");
            }
            Objects.requireNonNull((C1948n4) this.f8145k);
            if (((C1948n4) this.f8145k).m4978y().m5056o(false) != null && "_ae".equals(str2)) {
                C1918j6 c1918j6 = ((C1948n4) this.f8145k).m4971r().f7857o;
                Objects.requireNonNull(((C1948n4) c1918j6.f7806d.f8145k).f7930w);
                long elapsedRealtime = SystemClock.elapsedRealtime();
                long j7 = elapsedRealtime - c1918j6.f7804b;
                c1918j6.f7804b = elapsedRealtime;
                if (j7 > 0) {
                    ((C1948n4) this.f8145k).m4973t().m4716O(m4739t, j7);
                }
            }
            C1551f8.m3824b();
            if (((C1948n4) this.f8145k).f7923p.m4784q(null, C2026x2.f8199g0)) {
                if (!"auto".equals(str) && "_ssr".equals(str2)) {
                    C1838a7 m4973t4 = ((C1948n4) this.f8145k).m4973t();
                    String string2 = m4739t.getString("_ffr");
                    int i7 = C1278f.f5988a;
                    if (string2 == null || string2.trim().isEmpty()) {
                        string2 = null;
                    } else if (string2 != null) {
                        string2 = string2.trim();
                    }
                    if (C1838a7.m4701G(string2, ((C1948n4) m4973t4.f8145k).m4970q().f8236D.m5136a())) {
                        ((C1948n4) m4973t4.f8145k).mo4962e().f7791w.m4841b("Not logging duplicate session_start_with_rollout event");
                        return;
                    }
                    ((C1948n4) m4973t4.f8145k).m4970q().f8236D.m5137b(string2);
                } else if ("_ae".equals(str2)) {
                    String m5136a = ((C1948n4) ((C1948n4) this.f8145k).m4973t().f8145k).m4970q().f8236D.m5136a();
                    if (!TextUtils.isEmpty(m5136a)) {
                        m4739t.putString("_ffr", m5136a);
                    }
                }
            }
            ArrayList arrayList2 = new ArrayList();
            arrayList2.add(m4739t);
            if (((C1948n4) this.f8145k).m4970q().f8252y.m5050a() > 0 && ((C1948n4) this.f8145k).m4970q().m5151u(j6) && ((C1948n4) this.f8145k).m4970q().f8233A.m4998a()) {
                ((C1948n4) this.f8145k).mo4962e().f7792x.m4841b("Current session is expired, remove the session number, ID, and engagement time");
                Objects.requireNonNull(((C1948n4) this.f8145k).f7930w);
                bundle2 = m4739t;
                m4925m("auto", "_sid", null, System.currentTimeMillis());
                Objects.requireNonNull(((C1948n4) this.f8145k).f7930w);
                m4925m("auto", "_sno", null, System.currentTimeMillis());
                Objects.requireNonNull(((C1948n4) this.f8145k).f7930w);
                m4925m("auto", "_se", null, System.currentTimeMillis());
            } else {
                bundle2 = m4739t;
            }
            if (bundle2.getLong("extend_session", 0L) == 1) {
                ((C1948n4) this.f8145k).mo4962e().f7792x.m4841b("EXTEND_SESSION param attached: initiate a new session or extend the current active session");
                ((C1948n4) this.f8145k).m4971r().f7856n.m4908b(j6, true);
            }
            ArrayList arrayList3 = new ArrayList(bundle2.keySet());
            Collections.sort(arrayList3);
            int size = arrayList3.size();
            int i8 = 0;
            while (i8 < size) {
                String str5 = (String) arrayList3.get(i8);
                if (str5 != null) {
                    ((C1948n4) this.f8145k).m4973t();
                    Object obj2 = bundle2.get(str5);
                    arrayList = arrayList3;
                    if (obj2 instanceof Bundle) {
                        bundleArr = new Bundle[]{(Bundle) obj2};
                    } else {
                        if (obj2 instanceof Parcelable[]) {
                            Parcelable[] parcelableArr = (Parcelable[]) obj2;
                            array = Arrays.copyOf(parcelableArr, parcelableArr.length, Bundle[].class);
                        } else if (obj2 instanceof ArrayList) {
                            ArrayList arrayList4 = (ArrayList) obj2;
                            array = arrayList4.toArray(new Bundle[arrayList4.size()]);
                        } else {
                            bundleArr = null;
                        }
                        bundleArr = (Bundle[]) array;
                    }
                    if (bundleArr != null) {
                        bundle2.putParcelableArray(str5, bundleArr);
                    }
                } else {
                    arrayList = arrayList3;
                }
                i8++;
                arrayList3 = arrayList;
            }
            int i9 = 0;
            while (i9 < arrayList2.size()) {
                Bundle bundle3 = (Bundle) arrayList2.get(i9);
                String str6 = i9 != 0 ? "_ep" : str2;
                bundle3.putString(str4, str);
                if (z6) {
                    bundle3 = ((C1948n4) this.f8145k).m4973t().m4710I(bundle3);
                }
                Bundle bundle4 = bundle3;
                String str7 = str4;
                C1967q c1967q = new C1967q(str6, new C1951o(bundle4), str, j6);
                C1855c6 m4979z = ((C1948n4) this.f8145k).m4979z();
                Objects.requireNonNull(m4979z);
                m4979z.mo4915h();
                m4979z.m5102i();
                m4979z.m4755q();
                C1861d3 m4975v = ((C1948n4) m4979z.f8145k).m4975v();
                Objects.requireNonNull(m4975v);
                Parcel obtain = Parcel.obtain();
                C1975r.m4997a(c1967q, obtain, 0);
                byte[] marshall = obtain.marshall();
                obtain.recycle();
                if (marshall.length > 131072) {
                    ((C1948n4) m4975v.f8145k).mo4962e().f7785q.m4841b("Event is too long for local database. Sending event directly to service");
                    m4767p = false;
                } else {
                    m4767p = m4975v.m4767p(0, marshall);
                }
                m4979z.m4757s(new RunnableC1881f5(m4979z, m4979z.m4759u(true), m4767p, c1967q, str3));
                if (!equals) {
                    Iterator it = this.f7843n.iterator();
                    while (it.hasNext()) {
                        InterfaceC2044z4 interfaceC2044z4 = (InterfaceC2044z4) it.next();
                        new Bundle(bundle4);
                        interfaceC2044z4.m5157a();
                    }
                }
                i9++;
                str4 = str7;
            }
            Objects.requireNonNull((C1948n4) this.f8145k);
            if (((C1948n4) this.f8145k).m4978y().m5056o(false) == null || !"_ae".equals(str2)) {
                return;
            }
            C1934l6 m4971r = ((C1948n4) this.f8145k).m4971r();
            Objects.requireNonNull(((C1948n4) this.f8145k).f7930w);
            m4971r.f7857o.m4897a(true, true, SystemClock.elapsedRealtime());
        }
    }

    /* renamed from: v */
    public final void m4933v(String str, String str2, long j6, Bundle bundle, boolean z5) {
        Bundle bundle2 = new Bundle(bundle);
        for (String str3 : bundle2.keySet()) {
            Object obj = bundle2.get(str3);
            if (obj instanceof Bundle) {
                bundle2.putBundle(str3, new Bundle((Bundle) obj));
            } else {
                int i6 = 0;
                if (obj instanceof Parcelable[]) {
                    Parcelable[] parcelableArr = (Parcelable[]) obj;
                    while (i6 < parcelableArr.length) {
                        Parcelable parcelable = parcelableArr[i6];
                        if (parcelable instanceof Bundle) {
                            parcelableArr[i6] = new Bundle((Bundle) parcelable);
                        }
                        i6++;
                    }
                } else if (obj instanceof List) {
                    List list = (List) obj;
                    while (i6 < list.size()) {
                        Object obj2 = list.get(i6);
                        if (obj2 instanceof Bundle) {
                            list.set(i6, new Bundle((Bundle) obj2));
                        }
                        i6++;
                    }
                }
            }
        }
        ((C1948n4) this.f8145k).mo4959b().m4918q(new RunnableC1863d5(this, str, str2, j6, bundle2, z5));
    }

    /* renamed from: w */
    public final void m4934w(String str, Object obj) {
        Object obj2;
        Objects.requireNonNull(((C1948n4) this.f8145k).f7930w);
        long currentTimeMillis = System.currentTimeMillis();
        int m4727e0 = ((C1948n4) this.f8145k).m4973t().m4727e0(str);
        if (m4727e0 != 0) {
            C1838a7 m4973t = ((C1948n4) this.f8145k).m4973t();
            Objects.requireNonNull((C1948n4) this.f8145k);
            ((C1948n4) this.f8145k).m4973t().m4707A(this.f7854y, null, m4727e0, "_ev", m4973t.m4736q(str, 24, true), str.length(), ((C1948n4) this.f8145k).f7923p.m4784q(null, C2026x2.f8217p0));
            return;
        }
        if (obj != null) {
            int m4743x = ((C1948n4) this.f8145k).m4973t().m4743x(str, obj);
            if (m4743x != 0) {
                C1838a7 m4973t2 = ((C1948n4) this.f8145k).m4973t();
                Objects.requireNonNull((C1948n4) this.f8145k);
                ((C1948n4) this.f8145k).m4973t().m4707A(this.f7854y, null, m4743x, "_ev", m4973t2.m4736q(str, 24, true), ((obj instanceof String) || (obj instanceof CharSequence)) ? String.valueOf(obj).length() : 0, ((C1948n4) this.f8145k).f7923p.m4784q(null, C2026x2.f8217p0));
                return;
            }
            Object m4744y = ((C1948n4) this.f8145k).m4973t().m4744y(str, obj);
            if (m4744y == null) {
                return;
            } else {
                obj2 = m4744y;
            }
        } else {
            obj2 = null;
        }
        m4924l("auto", str, currentTimeMillis, obj2);
    }
}
