package p158x2;

import p118r2.C1305b;
import p118r2.C1306c;

/* renamed from: x2.a4 */
/* loaded from: classes.dex */
public final class C1835a4 {

    /* renamed from: a */
    public final C1948n4 f7540a;

    public C1835a4(C1948n4 c1948n4) {
        this.f7540a = c1948n4;
    }

    /* renamed from: a */
    public final boolean m4696a() {
        try {
            C1305b m3210a = C1306c.m3210a(this.f7540a.f7917j);
            if (m3210a != null) {
                return m3210a.m3208b("com.android.vending", 128).versionCode >= 80837300;
            }
            this.f7540a.mo4962e().f7792x.m4841b("Failed to get PackageManager for Install Referrer Play Store compatibility check");
            return false;
        } catch (Exception e6) {
            this.f7540a.mo4962e().f7792x.m4842c("Failed to retrieve Play Store version for Install Referrer", e6);
            return false;
        }
    }
}
