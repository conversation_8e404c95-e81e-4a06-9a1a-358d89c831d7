package p158x2;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;
import java.util.List;
import p099o2.AbstractC1146a;
import p153w3.C1798e;

/* renamed from: x2.b7 */
/* loaded from: classes.dex */
public final class C1847b7 extends AbstractC1146a {
    public static final Parcelable.Creator<C1847b7> CREATOR = new C1856c7();

    /* renamed from: A */
    public final Boolean f7590A;

    /* renamed from: B */
    public final long f7591B;

    /* renamed from: C */
    public final List<String> f7592C;

    /* renamed from: D */
    public final String f7593D;

    /* renamed from: E */
    public final String f7594E;

    /* renamed from: j */
    public final String f7595j;

    /* renamed from: k */
    public final String f7596k;

    /* renamed from: l */
    public final String f7597l;

    /* renamed from: m */
    public final String f7598m;

    /* renamed from: n */
    public final long f7599n;

    /* renamed from: o */
    public final long f7600o;

    /* renamed from: p */
    public final String f7601p;

    /* renamed from: q */
    public final boolean f7602q;

    /* renamed from: r */
    public final boolean f7603r;

    /* renamed from: s */
    public final long f7604s;

    /* renamed from: t */
    public final String f7605t;

    /* renamed from: u */
    public final long f7606u;

    /* renamed from: v */
    public final long f7607v;

    /* renamed from: w */
    public final int f7608w;

    /* renamed from: x */
    public final boolean f7609x;

    /* renamed from: y */
    public final boolean f7610y;

    /* renamed from: z */
    public final String f7611z;

    public C1847b7(String str, String str2, String str3, long j6, String str4, long j7, long j8, String str5, boolean z5, boolean z6, String str6, long j9, long j10, int i6, boolean z7, boolean z8, String str7, Boolean bool, long j11, List<String> list, String str8, String str9) {
        C1798e.m4554o(str);
        this.f7595j = str;
        this.f7596k = true != TextUtils.isEmpty(str2) ? str2 : null;
        this.f7597l = str3;
        this.f7604s = j6;
        this.f7598m = str4;
        this.f7599n = j7;
        this.f7600o = j8;
        this.f7601p = str5;
        this.f7602q = z5;
        this.f7603r = z6;
        this.f7605t = str6;
        this.f7606u = j9;
        this.f7607v = j10;
        this.f7608w = i6;
        this.f7609x = z7;
        this.f7610y = z8;
        this.f7611z = str7;
        this.f7590A = bool;
        this.f7591B = j11;
        this.f7592C = list;
        this.f7593D = str8;
        this.f7594E = str9;
    }

    public C1847b7(String str, String str2, String str3, String str4, long j6, long j7, String str5, boolean z5, boolean z6, long j8, String str6, long j9, long j10, int i6, boolean z7, boolean z8, String str7, Boolean bool, long j11, List<String> list, String str8, String str9) {
        this.f7595j = str;
        this.f7596k = str2;
        this.f7597l = str3;
        this.f7604s = j8;
        this.f7598m = str4;
        this.f7599n = j6;
        this.f7600o = j7;
        this.f7601p = str5;
        this.f7602q = z5;
        this.f7603r = z6;
        this.f7605t = str6;
        this.f7606u = j9;
        this.f7607v = j10;
        this.f7608w = i6;
        this.f7609x = z7;
        this.f7610y = z8;
        this.f7611z = str7;
        this.f7590A = bool;
        this.f7591B = j11;
        this.f7592C = list;
        this.f7593D = str8;
        this.f7594E = str9;
    }

    @Override // android.os.Parcelable
    public final void writeToParcel(Parcel parcel, int i6) {
        int m4526S = C1798e.m4526S(parcel, 20293);
        C1798e.m4524Q(parcel, 2, this.f7595j);
        C1798e.m4524Q(parcel, 3, this.f7596k);
        C1798e.m4524Q(parcel, 4, this.f7597l);
        C1798e.m4524Q(parcel, 5, this.f7598m);
        long j6 = this.f7599n;
        C1798e.m4540g0(parcel, 6, 8);
        parcel.writeLong(j6);
        long j7 = this.f7600o;
        C1798e.m4540g0(parcel, 7, 8);
        parcel.writeLong(j7);
        C1798e.m4524Q(parcel, 8, this.f7601p);
        boolean z5 = this.f7602q;
        C1798e.m4540g0(parcel, 9, 4);
        parcel.writeInt(z5 ? 1 : 0);
        boolean z6 = this.f7603r;
        C1798e.m4540g0(parcel, 10, 4);
        parcel.writeInt(z6 ? 1 : 0);
        long j8 = this.f7604s;
        C1798e.m4540g0(parcel, 11, 8);
        parcel.writeLong(j8);
        C1798e.m4524Q(parcel, 12, this.f7605t);
        long j9 = this.f7606u;
        C1798e.m4540g0(parcel, 13, 8);
        parcel.writeLong(j9);
        long j10 = this.f7607v;
        C1798e.m4540g0(parcel, 14, 8);
        parcel.writeLong(j10);
        int i7 = this.f7608w;
        C1798e.m4540g0(parcel, 15, 4);
        parcel.writeInt(i7);
        boolean z7 = this.f7609x;
        C1798e.m4540g0(parcel, 16, 4);
        parcel.writeInt(z7 ? 1 : 0);
        boolean z8 = this.f7610y;
        C1798e.m4540g0(parcel, 18, 4);
        parcel.writeInt(z8 ? 1 : 0);
        C1798e.m4524Q(parcel, 19, this.f7611z);
        Boolean bool = this.f7590A;
        if (bool != null) {
            C1798e.m4540g0(parcel, 21, 4);
            parcel.writeInt(bool.booleanValue() ? 1 : 0);
        }
        long j11 = this.f7591B;
        C1798e.m4540g0(parcel, 22, 8);
        parcel.writeLong(j11);
        List<String> list = this.f7592C;
        if (list != null) {
            int m4526S2 = C1798e.m4526S(parcel, 23);
            parcel.writeStringList(list);
            C1798e.m4539f0(parcel, m4526S2);
        }
        C1798e.m4524Q(parcel, 24, this.f7593D);
        C1798e.m4524Q(parcel, 25, this.f7594E);
        C1798e.m4539f0(parcel, m4526S);
    }
}
