package p158x2;

import java.util.Objects;
import p023d1.C0722t;

/* renamed from: x2.w0 */
/* loaded from: classes.dex */
public final class RunnableC2016w0 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ int f8136j;

    /* renamed from: k */
    public final /* synthetic */ long f8137k;

    /* renamed from: l */
    public final /* synthetic */ C2018w2 f8138l;

    public /* synthetic */ RunnableC2016w0(C2018w2 c2018w2, long j6, int i6) {
        this.f8136j = i6;
        this.f8138l = c2018w2;
        this.f8137k = j6;
    }

    @Override // java.lang.Runnable
    public final void run() {
        switch (this.f8136j) {
            case 0:
                ((C2025x1) this.f8138l).m5142l(this.f8137k);
                break;
            case 1:
                ((C1948n4) ((C1997t5) this.f8138l).f8145k).m4964g().m5139i(this.f8137k);
                ((C1997t5) this.f8138l).f8062o = null;
                break;
            default:
                C1934l6 c1934l6 = (C1934l6) this.f8138l;
                long j6 = this.f8137k;
                c1934l6.mo4915h();
                c1934l6.m4935l();
                ((C1948n4) c1934l6.f8145k).mo4962e().f7792x.m4842c("Activity paused, time", Long.valueOf(j6));
                C0722t c0722t = c1934l6.f7858p;
                Objects.requireNonNull(((C1948n4) ((C1934l6) c0722t.f3916k).f8145k).f7930w);
                RunnableC1909i6 runnableC1909i6 = new RunnableC1909i6(c0722t, System.currentTimeMillis(), j6);
                c0722t.f3915j = runnableC1909i6;
                ((C1934l6) c0722t.f3916k).f7855m.postDelayed(runnableC1909i6, 2000L);
                if (((C1948n4) c1934l6.f8145k).f7923p.m4788u()) {
                    c1934l6.f7857o.f7805c.m4900c();
                }
                C1926k6 c1926k6 = c1934l6.f7856n;
                if (!((C1948n4) c1926k6.f7824a.f8145k).f7923p.m4784q(null, C2026x2.f8209l0)) {
                    ((C1948n4) c1926k6.f7824a.f8145k).m4970q().f8233A.m4999b(true);
                    break;
                }
                break;
        }
    }
}
