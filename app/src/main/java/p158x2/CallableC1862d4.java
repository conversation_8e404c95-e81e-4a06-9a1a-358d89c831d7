package p158x2;

import java.util.Objects;
import java.util.concurrent.Callable;
import p023d1.C0722t;
import p152w2.C1655o4;

/* renamed from: x2.d4 */
/* loaded from: classes.dex */
public final /* synthetic */ class CallableC1862d4 implements Callable {

    /* renamed from: a */
    public final /* synthetic */ int f7631a;

    /* renamed from: b */
    public final Object f7632b;

    /* renamed from: c */
    public final Object f7633c;

    public /* synthetic */ CallableC1862d4(Object obj, String str, int i6) {
        this.f7631a = i6;
        this.f7633c = obj;
        this.f7632b = str;
    }

    @Override // java.util.concurrent.Callable
    public final Object call() {
        switch (this.f7631a) {
            case 0:
                return new C1655o4(new C0722t(this.f7633c, (String) this.f7632b));
            case 1:
                ((BinderC2012v4) this.f7633c).f8129a.m5086j();
                C1902i c1902i = ((BinderC2012v4) this.f7633c).f8129a.f8084l;
                C1998t6.m5060E(c1902i);
                return c1902i.m4855G((String) this.f7632b);
            default:
                C1998t6 c1998t6 = (C1998t6) this.f7632b;
                String str = ((C1847b7) this.f7633c).f7595j;
                Objects.requireNonNull(str, "null reference");
                if (c1998t6.m5076N(str).m4807d() && C1875f.m4801a(((C1847b7) this.f7633c).f7594E).m4807d()) {
                    return ((C1998t6) this.f7632b).m5093q((C1847b7) this.f7633c).m5047z();
                }
                ((C1998t6) this.f7632b).mo4962e().f7792x.m4841b("Analytics storage consent denied. Returning null app instance id");
                return null;
        }
    }

    public CallableC1862d4(C1998t6 c1998t6, C1847b7 c1847b7) {
        this.f7631a = 2;
        this.f7632b = c1998t6;
        this.f7633c = c1847b7;
    }
}
