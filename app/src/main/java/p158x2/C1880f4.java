package p158x2;

import java.util.LinkedHashMap;
import p077l.C1051e;
import p152w2.C1532e1;
import p152w2.C1591j0;
import p152w2.C1792z9;
import p153w3.C1798e;

/* renamed from: x2.f4 */
/* loaded from: classes.dex */
public final class C1880f4 extends C1051e<String, C1591j0> {

    /* renamed from: f */
    public final /* synthetic */ C1889g4 f7684f;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C1880f4(C1889g4 c1889g4) {
        super(20);
        this.f7684f = c1889g4;
    }

    /* JADX WARN: Type inference failed for: r1v10, types: [java.util.Map<java.lang.String, w2.e1>, l.g] */
    /* JADX WARN: Type inference failed for: r1v5, types: [java.util.Map<java.lang.String, w2.e1>, l.g] */
    /* JADX WARN: Type inference failed for: r1v8, types: [java.util.Map<java.lang.String, w2.e1>, l.g] */
    @Override // p077l.C1051e
    /* renamed from: a */
    public final C1591j0 mo2676a(String str) {
        LinkedHashMap linkedHashMap;
        String str2 = str;
        C1798e.m4554o(str2);
        C1889g4 c1889g4 = this.f7684f;
        c1889g4.m4994i();
        C1798e.m4554o(str2);
        C1792z9.m4507b();
        if (!((C1948n4) c1889g4.f8145k).f7923p.m4784q(null, C2026x2.f8225t0) || !c1889g4.m4817n(str2)) {
            return null;
        }
        if (!c1889g4.f7710q.containsKey(str2) || c1889g4.f7710q.getOrDefault(str2, null) == null) {
            c1889g4.m4824u(str2);
        } else {
            c1889g4.m4826w(str2, (C1532e1) c1889g4.f7710q.getOrDefault(str2, null));
        }
        C1051e<String, C1591j0> c1051e = c1889g4.f7712s;
        synchronized (c1051e) {
            linkedHashMap = new LinkedHashMap(c1051e.f5012a);
        }
        return (C1591j0) linkedHashMap.get(str2);
    }
}
