package p158x2;

import android.os.Looper;
import p023d1.C0722t;
import p152w2.HandlerC1579i0;

/* renamed from: x2.l6 */
/* loaded from: classes.dex */
public final class C1934l6 extends AbstractC2011v3 {

    /* renamed from: m */
    public HandlerC1579i0 f7855m;

    /* renamed from: n */
    public final C1926k6 f7856n;

    /* renamed from: o */
    public final C1918j6 f7857o;

    /* renamed from: p */
    public final C0722t f7858p;

    public C1934l6(C1948n4 c1948n4) {
        super(c1948n4);
        this.f7856n = new C1926k6(this);
        this.f7857o = new C1918j6(this);
        this.f7858p = new C0722t(this);
    }

    @Override // p158x2.AbstractC2011v3
    /* renamed from: k */
    public final boolean mo4746k() {
        return false;
    }

    /* renamed from: l */
    public final void m4935l() {
        mo4915h();
        if (this.f7855m == null) {
            this.f7855m = new HandlerC1579i0(Looper.getMainLooper());
        }
    }
}
