package p158x2;

import android.os.Process;
import java.util.Objects;
import java.util.concurrent.BlockingQueue;

/* renamed from: x2.k4 */
/* loaded from: classes.dex */
public final class C1924k4 extends Thread {

    /* renamed from: j */
    public final Object f7819j;

    /* renamed from: k */
    public final BlockingQueue<C1916j4<?>> f7820k;

    /* renamed from: l */
    public boolean f7821l = false;

    /* renamed from: m */
    public final /* synthetic */ C1932l4 f7822m;

    public C1924k4(C1932l4 c1932l4, String str, BlockingQueue<C1916j4<?>> blockingQueue) {
        this.f7822m = c1932l4;
        Objects.requireNonNull(blockingQueue, "null reference");
        this.f7819j = new Object();
        this.f7820k = blockingQueue;
        setName(str);
    }

    /* renamed from: a */
    public final void m4905a() {
        synchronized (this.f7822m.f7840s) {
            try {
                if (!this.f7821l) {
                    this.f7822m.f7841t.release();
                    this.f7822m.f7840s.notifyAll();
                    C1932l4 c1932l4 = this.f7822m;
                    if (this == c1932l4.f7834m) {
                        c1932l4.f7834m = null;
                    } else if (this == c1932l4.f7835n) {
                        c1932l4.f7835n = null;
                    } else {
                        ((C1948n4) c1932l4.f8145k).mo4962e().f7784p.m4841b("Current scheduler thread is neither worker nor network");
                    }
                    this.f7821l = true;
                }
            } catch (Throwable th) {
                throw th;
            }
        }
    }

    /* renamed from: b */
    public final void m4906b(InterruptedException interruptedException) {
        ((C1948n4) this.f7822m.f8145k).mo4962e().f7787s.m4842c(String.valueOf(getName()).concat(" was interrupted"), interruptedException);
    }

    @Override // java.lang.Thread, java.lang.Runnable
    public final void run() {
        boolean z5 = false;
        while (!z5) {
            try {
                this.f7822m.f7841t.acquire();
                z5 = true;
            } catch (InterruptedException e6) {
                m4906b(e6);
            }
        }
        try {
            int threadPriority = Process.getThreadPriority(Process.myTid());
            while (true) {
                C1916j4<?> poll = this.f7820k.poll();
                if (poll == null) {
                    synchronized (this.f7819j) {
                        try {
                            if (this.f7820k.peek() == null) {
                                Objects.requireNonNull(this.f7822m);
                                this.f7819j.wait(30000L);
                            }
                        } catch (InterruptedException e7) {
                            m4906b(e7);
                        } finally {
                        }
                    }
                    synchronized (this.f7822m.f7840s) {
                        if (this.f7820k.peek() == null) {
                            break;
                        }
                    }
                } else {
                    Process.setThreadPriority(true != poll.f7794k ? 10 : threadPriority);
                    poll.run();
                }
            }
            if (((C1948n4) this.f7822m.f8145k).f7923p.m4784q(null, C2026x2.f8203i0)) {
                m4905a();
            }
        } finally {
            m4905a();
        }
    }
}
