package p158x2;

import android.os.RemoteException;
import android.text.TextUtils;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import p153w3.C1798e;

/* renamed from: x2.y5 */
/* loaded from: classes.dex */
public final class RunnableC2037y5 implements Runnable {

    /* renamed from: j */
    public final /* synthetic */ AtomicReference f8263j;

    /* renamed from: k */
    public final /* synthetic */ String f8264k;

    /* renamed from: l */
    public final /* synthetic */ String f8265l;

    /* renamed from: m */
    public final /* synthetic */ C1847b7 f8266m;

    /* renamed from: n */
    public final /* synthetic */ C1855c6 f8267n;

    public RunnableC2037y5(C1855c6 c1855c6, AtomicReference atomicReference, String str, String str2, C1847b7 c1847b7) {
        this.f8267n = c1855c6;
        this.f8263j = atomicReference;
        this.f8264k = str;
        this.f8265l = str2;
        this.f8266m = c1847b7;
    }

    @Override // java.lang.Runnable
    public final void run() {
        C1855c6 c1855c6;
        InterfaceC1834a3 interfaceC1834a3;
        AtomicReference atomicReference;
        List<C1839b> mo4692u;
        synchronized (this.f8263j) {
            try {
                try {
                    c1855c6 = this.f8267n;
                    interfaceC1834a3 = c1855c6.f7620n;
                } catch (RemoteException e6) {
                    ((C1948n4) this.f8267n.f8145k).mo4962e().f7784p.m4844e("(legacy) Failed to get conditional properties; remote exception", null, this.f8264k, e6);
                    this.f8263j.set(Collections.emptyList());
                }
                if (interfaceC1834a3 == null) {
                    ((C1948n4) c1855c6.f8145k).mo4962e().f7784p.m4844e("(legacy) Failed to get conditional properties; not connected to service", null, this.f8264k, this.f8265l);
                    this.f8263j.set(Collections.emptyList());
                    return;
                }
                if (TextUtils.isEmpty(null)) {
                    C1798e.m4560r(this.f8266m);
                    atomicReference = this.f8263j;
                    mo4692u = interfaceC1834a3.mo4690p(this.f8264k, this.f8265l, this.f8266m);
                } else {
                    atomicReference = this.f8263j;
                    mo4692u = interfaceC1834a3.mo4692u(null, this.f8264k, this.f8265l);
                }
                atomicReference.set(mo4692u);
                this.f8267n.m4756r();
                this.f8263j.notify();
            } finally {
                this.f8263j.notify();
            }
        }
    }
}
