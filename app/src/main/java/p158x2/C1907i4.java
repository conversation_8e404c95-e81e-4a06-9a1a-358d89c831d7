package p158x2;

import java.lang.Thread;

/* renamed from: x2.i4 */
/* loaded from: classes.dex */
public final class C1907i4 implements Thread.UncaughtExceptionHandler {

    /* renamed from: a */
    public final String f7760a;

    /* renamed from: b */
    public final /* synthetic */ C1932l4 f7761b;

    public C1907i4(C1932l4 c1932l4, String str) {
        this.f7761b = c1932l4;
        this.f7760a = str;
    }

    @Override // java.lang.Thread.UncaughtExceptionHandler
    public final synchronized void uncaughtException(Thread thread, Throwable th) {
        ((C1948n4) this.f7761b.f8145k).mo4962e().f7784p.m4842c(this.f7760a, th);
    }
}
