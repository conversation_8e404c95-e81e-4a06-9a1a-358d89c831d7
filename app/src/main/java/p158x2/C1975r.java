package p158x2;

import android.os.Parcel;
import android.os.Parcelable;
import p099o2.C1147b;
import p153w3.C1798e;

/* renamed from: x2.r */
/* loaded from: classes.dex */
public final class C1975r implements Parcelable.Creator<C1967q> {
    /* renamed from: a */
    public static void m4997a(C1967q c1967q, Parcel parcel, int i6) {
        int m4526S = C1798e.m4526S(parcel, 20293);
        C1798e.m4524Q(parcel, 2, c1967q.f7965j);
        C1798e.m4523P(parcel, 3, c1967q.f7966k, i6);
        C1798e.m4524Q(parcel, 4, c1967q.f7967l);
        long j6 = c1967q.f7968m;
        C1798e.m4540g0(parcel, 5, 8);
        parcel.writeLong(j6);
        C1798e.m4539f0(parcel, m4526S);
    }

    @Override // android.os.Parcelable.Creator
    public final C1967q createFromParcel(Parcel parcel) {
        int m2977l = C1147b.m2977l(parcel);
        String str = null;
        C1951o c1951o = null;
        String str2 = null;
        long j6 = 0;
        while (parcel.dataPosition() < m2977l) {
            int readInt = parcel.readInt();
            int i6 = 65535 & readInt;
            if (i6 == 2) {
                str = C1147b.m2968c(parcel, readInt);
            } else if (i6 == 3) {
                c1951o = (C1951o) C1147b.m2967b(parcel, readInt, C1951o.CREATOR);
            } else if (i6 == 4) {
                str2 = C1147b.m2968c(parcel, readInt);
            } else if (i6 != 5) {
                C1147b.m2976k(parcel, readInt);
            } else {
                j6 = C1147b.m2974i(parcel, readInt);
            }
        }
        C1147b.m2970e(parcel, m2977l);
        return new C1967q(str, c1951o, str2, j6);
    }

    @Override // android.os.Parcelable.Creator
    public final /* bridge */ /* synthetic */ C1967q[] newArray(int i6) {
        return new C1967q[i6];
    }
}
