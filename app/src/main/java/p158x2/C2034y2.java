package p158x2;

import android.os.Bundle;
import android.os.IBinder;
import android.os.Parcel;
import java.util.ArrayList;
import java.util.List;
import p145v2.C1456a;
import p152w2.C1506c0;

/* renamed from: x2.y2 */
/* loaded from: classes.dex */
public final class C2034y2 extends C1456a implements InterfaceC1834a3 {
    public C2034y2(IBinder iBinder) {
        super(iBinder, "com.google.android.gms.measurement.internal.IMeasurementService", 1);
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: A */
    public final void mo4682A(Bundle bundle, C1847b7 c1847b7) {
        Parcel m3559D = m3559D();
        C1506c0.m3676b(m3559D, bundle);
        C1506c0.m3676b(m3559D, c1847b7);
        m3561F(19, m3559D);
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: d */
    public final List<C2022w6> mo4683d(String str, String str2, boolean z5, C1847b7 c1847b7) {
        Parcel m3559D = m3559D();
        m3559D.writeString(str);
        m3559D.writeString(str2);
        int i6 = C1506c0.f6910a;
        m3559D.writeInt(z5 ? 1 : 0);
        C1506c0.m3676b(m3559D, c1847b7);
        Parcel m3558C = m3558C(14, m3559D);
        ArrayList createTypedArrayList = m3558C.createTypedArrayList(C2022w6.CREATOR);
        m3558C.recycle();
        return createTypedArrayList;
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: f */
    public final void mo4684f(C1847b7 c1847b7) {
        Parcel m3559D = m3559D();
        C1506c0.m3676b(m3559D, c1847b7);
        m3561F(18, m3559D);
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: g */
    public final void mo4685g(C1839b c1839b, C1847b7 c1847b7) {
        Parcel m3559D = m3559D();
        C1506c0.m3676b(m3559D, c1839b);
        C1506c0.m3676b(m3559D, c1847b7);
        m3561F(12, m3559D);
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: k */
    public final void mo4686k(C1847b7 c1847b7) {
        Parcel m3559D = m3559D();
        C1506c0.m3676b(m3559D, c1847b7);
        m3561F(6, m3559D);
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: l */
    public final void mo4687l(long j6, String str, String str2, String str3) {
        Parcel m3559D = m3559D();
        m3559D.writeLong(j6);
        m3559D.writeString(str);
        m3559D.writeString(str2);
        m3559D.writeString(str3);
        m3561F(10, m3559D);
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: m */
    public final void mo4688m(C1847b7 c1847b7) {
        Parcel m3559D = m3559D();
        C1506c0.m3676b(m3559D, c1847b7);
        m3561F(20, m3559D);
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: o */
    public final void mo4689o(C1847b7 c1847b7) {
        Parcel m3559D = m3559D();
        C1506c0.m3676b(m3559D, c1847b7);
        m3561F(4, m3559D);
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: p */
    public final List<C1839b> mo4690p(String str, String str2, C1847b7 c1847b7) {
        Parcel m3559D = m3559D();
        m3559D.writeString(str);
        m3559D.writeString(str2);
        C1506c0.m3676b(m3559D, c1847b7);
        Parcel m3558C = m3558C(16, m3559D);
        ArrayList createTypedArrayList = m3558C.createTypedArrayList(C1839b.CREATOR);
        m3558C.recycle();
        return createTypedArrayList;
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: t */
    public final void mo4691t(C2022w6 c2022w6, C1847b7 c1847b7) {
        Parcel m3559D = m3559D();
        C1506c0.m3676b(m3559D, c2022w6);
        C1506c0.m3676b(m3559D, c1847b7);
        m3561F(2, m3559D);
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: u */
    public final List<C1839b> mo4692u(String str, String str2, String str3) {
        Parcel m3559D = m3559D();
        m3559D.writeString(null);
        m3559D.writeString(str2);
        m3559D.writeString(str3);
        Parcel m3558C = m3558C(17, m3559D);
        ArrayList createTypedArrayList = m3558C.createTypedArrayList(C1839b.CREATOR);
        m3558C.recycle();
        return createTypedArrayList;
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: v */
    public final String mo4693v(C1847b7 c1847b7) {
        Parcel m3559D = m3559D();
        C1506c0.m3676b(m3559D, c1847b7);
        Parcel m3558C = m3558C(11, m3559D);
        String readString = m3558C.readString();
        m3558C.recycle();
        return readString;
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: y */
    public final void mo4694y(C1967q c1967q, C1847b7 c1847b7) {
        Parcel m3559D = m3559D();
        C1506c0.m3676b(m3559D, c1967q);
        C1506c0.m3676b(m3559D, c1847b7);
        m3561F(1, m3559D);
    }

    @Override // p158x2.InterfaceC1834a3
    /* renamed from: z */
    public final List<C2022w6> mo4695z(String str, String str2, String str3, boolean z5) {
        Parcel m3559D = m3559D();
        m3559D.writeString(null);
        m3559D.writeString(str2);
        m3559D.writeString(str3);
        int i6 = C1506c0.f6910a;
        m3559D.writeInt(z5 ? 1 : 0);
        Parcel m3558C = m3558C(15, m3559D);
        ArrayList createTypedArrayList = m3558C.createTypedArrayList(C2022w6.CREATOR);
        m3558C.recycle();
        return createTypedArrayList;
    }
}
