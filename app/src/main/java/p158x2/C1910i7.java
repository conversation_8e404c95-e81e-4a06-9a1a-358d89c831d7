package p158x2;

import java.util.Map;
import java.util.Set;

/* renamed from: x2.i7 */
/* loaded from: classes.dex */
public final class C1910i7 extends AbstractC1966p6 {

    /* renamed from: n */
    public String f7770n;

    /* renamed from: o */
    public Set<Integer> f7771o;

    /* renamed from: p */
    public Map<Integer, C1874e7> f7772p;

    /* renamed from: q */
    public Long f7773q;

    /* renamed from: r */
    public Long f7774r;

    public C1910i7(C1998t6 c1998t6) {
        super(c1998t6);
    }

    @Override // p158x2.AbstractC1966p6
    /* renamed from: j */
    public final void mo4768j() {
    }

    /* JADX WARN: Can't wrap try/catch for region: R(12:0|1|(2:2|(2:4|(2:6|7)(1:504))(2:505|506))|8|(3:10|11|12)|16|(16:(6:19|20|21|22|23|(20:(7:25|26|27|28|(1:30)(3:481|(1:483)(1:485)|484)|31|(1:34)(1:33))|35|36|37|38|39|40|(2:42|43)(3:441|(6:442|443|444|445|446|(1:449)(1:448))|450)|44|(1:46)(6:275|(3:277|(10:279|280|281|282|283|(2:(3:285|(1:287)|288)|291)(1:335)|292|293|(6:296|(1:331)(2:300|(8:306|307|(4:310|(2:312|313)(1:315)|314|308)|316|317|(4:320|(2:322|323)(1:325)|324|318)|326|327)(4:302|303|304|305))|328|329|305|294)|333)|348)(1:440)|349|(10:352|(3:356|(4:359|(5:361|362|(1:364)(1:368)|365|366)(1:369)|367|357)|370)|371|(3:375|(4:378|(3:383|384|385)|386|376)|389)|390|(3:392|(6:395|(2:397|(3:399|400|401))(1:404)|402|403|401|393)|405)|406|(3:415|(8:418|(1:420)|421|(1:423)|424|(3:426|427|428)(1:430)|429|416)|431)|432|350)|438|439)|47|(3:175|(4:178|(7:180|(1:182)(1:271)|183|(9:185|186|187|188|189|190|191|192|(3:(12:194|195|196|197|198|199|200|201|(2:242|243)(1:203)|204|205|(1:208)(1:207))|209|210)(5:250|251|252|241|210))(1:270)|211|(4:214|(3:232|233|234)(6:216|217|(2:218|(2:220|(1:222)(2:223|224))(2:230|231))|(1:226)|227|228)|229|212)|235)(2:272|273)|236|176)|274)|49|50|(3:77|(6:80|(6:82|83|84|85|86|(3:(9:88|89|90|91|92|(1:94)(1:151)|95|96|(1:99)(1:98))|100|101)(4:158|159|150|101))(1:173)|102|(2:103|(2:105|(3:141|142|143)(6:107|(2:108|(4:110|(3:112|(1:114)(1:137)|115)(1:138)|116|(1:1)(2:120|(1:122)(2:123|124)))(2:139|140))|(2:129|128)|126|127|128))(0))|144|78)|174)|52|53|(9:56|57|58|59|60|61|(2:63|64)(1:66)|65|54)|74|75)(2:489|490))|39|40|(0)(0)|44|(0)(0)|47|(0)|49|50|(0)|52|53|(1:54)|74|75)|503|36|37|38|(5:(0)|(0)|(0)|(0)|(0))) */
    /* JADX WARN: Can't wrap try/catch for region: R(27:0|1|(2:2|(2:4|(2:6|7)(1:504))(2:505|506))|8|(3:10|11|12)|16|(6:19|20|21|22|23|(20:(7:25|26|27|28|(1:30)(3:481|(1:483)(1:485)|484)|31|(1:34)(1:33))|35|36|37|38|39|40|(2:42|43)(3:441|(6:442|443|444|445|446|(1:449)(1:448))|450)|44|(1:46)(6:275|(3:277|(10:279|280|281|282|283|(2:(3:285|(1:287)|288)|291)(1:335)|292|293|(6:296|(1:331)(2:300|(8:306|307|(4:310|(2:312|313)(1:315)|314|308)|316|317|(4:320|(2:322|323)(1:325)|324|318)|326|327)(4:302|303|304|305))|328|329|305|294)|333)|348)(1:440)|349|(10:352|(3:356|(4:359|(5:361|362|(1:364)(1:368)|365|366)(1:369)|367|357)|370)|371|(3:375|(4:378|(3:383|384|385)|386|376)|389)|390|(3:392|(6:395|(2:397|(3:399|400|401))(1:404)|402|403|401|393)|405)|406|(3:415|(8:418|(1:420)|421|(1:423)|424|(3:426|427|428)(1:430)|429|416)|431)|432|350)|438|439)|47|(3:175|(4:178|(7:180|(1:182)(1:271)|183|(9:185|186|187|188|189|190|191|192|(3:(12:194|195|196|197|198|199|200|201|(2:242|243)(1:203)|204|205|(1:208)(1:207))|209|210)(5:250|251|252|241|210))(1:270)|211|(4:214|(3:232|233|234)(6:216|217|(2:218|(2:220|(1:222)(2:223|224))(2:230|231))|(1:226)|227|228)|229|212)|235)(2:272|273)|236|176)|274)|49|50|(3:77|(6:80|(6:82|83|84|85|86|(3:(9:88|89|90|91|92|(1:94)(1:151)|95|96|(1:99)(1:98))|100|101)(4:158|159|150|101))(1:173)|102|(2:103|(2:105|(3:141|142|143)(6:107|(2:108|(4:110|(3:112|(1:114)(1:137)|115)(1:138)|116|(1:1)(2:120|(1:122)(2:123|124)))(2:139|140))|(2:129|128)|126|127|128))(0))|144|78)|174)|52|53|(9:56|57|58|59|60|61|(2:63|64)(1:66)|65|54)|74|75)(2:489|490))|503|36|37|38|39|40|(0)(0)|44|(0)(0)|47|(0)|49|50|(0)|52|53|(1:54)|74|75|(5:(0)|(0)|(0)|(0)|(0))) */
    /* JADX WARN: Code restructure failed: missing block: B:131:0x0a28, code lost:
    
        r0 = ((p158x2.C1948n4) r63.f8145k).mo4962e().m4891p();
        r6 = p158x2.C1915j3.m4886t(r63.f7770n);
     */
    /* JADX WARN: Code restructure failed: missing block: B:132:0x0a3e, code lost:
    
        if (r7.m4430s() == false) goto L379;
     */
    /* JADX WARN: Code restructure failed: missing block: B:133:0x0a40, code lost:
    
        r7 = java.lang.Integer.valueOf(r7.m4431t());
     */
    /* JADX WARN: Code restructure failed: missing block: B:134:0x0a4a, code lost:
    
        r0.m4843d("Invalid property filter ID. appId, id", r6, java.lang.String.valueOf(r7));
     */
    /* JADX WARN: Code restructure failed: missing block: B:135:0x0a49, code lost:
    
        r7 = null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:149:0x091c, code lost:
    
        if (r13 == null) goto L343;
     */
    /* JADX WARN: Code restructure failed: missing block: B:240:0x0757, code lost:
    
        if (r4 == null) goto L275;
     */
    /* JADX WARN: Code restructure failed: missing block: B:338:0x030c, code lost:
    
        if (r5 == null) goto L118;
     */
    /* JADX WARN: Code restructure failed: missing block: B:469:0x022d, code lost:
    
        r0 = e;
     */
    /* JADX WARN: Code restructure failed: missing block: B:470:0x022e, code lost:
    
        r18 = "audience_id";
     */
    /* JADX WARN: Code restructure failed: missing block: B:477:0x0235, code lost:
    
        r0 = e;
     */
    /* JADX WARN: Code restructure failed: missing block: B:478:0x0236, code lost:
    
        r18 = "audience_id";
        r19 = "data";
        r4 = null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:479:0x0231, code lost:
    
        r0 = th;
     */
    /* JADX WARN: Code restructure failed: missing block: B:480:0x0232, code lost:
    
        r5 = null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:493:0x0176, code lost:
    
        if (r5 == null) goto L54;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:175:0x05af  */
    /* JADX WARN: Removed duplicated region for block: B:275:0x026c  */
    /* JADX WARN: Removed duplicated region for block: B:342:0x03dd  */
    /* JADX WARN: Removed duplicated region for block: B:42:0x01b9 A[Catch: SQLiteException -> 0x022d, all -> 0x0b1d, TRY_LEAVE, TryCatch #21 {SQLiteException -> 0x022d, blocks: (B:40:0x01b3, B:42:0x01b9, B:441:0x01c7, B:442:0x01cc, B:444:0x01d6, B:445:0x01e6, B:459:0x01f5), top: B:39:0x01b3 }] */
    /* JADX WARN: Removed duplicated region for block: B:441:0x01c7 A[Catch: SQLiteException -> 0x022d, all -> 0x0b1d, TRY_ENTER, TryCatch #21 {SQLiteException -> 0x022d, blocks: (B:40:0x01b3, B:42:0x01b9, B:441:0x01c7, B:442:0x01cc, B:444:0x01d6, B:445:0x01e6, B:459:0x01f5), top: B:39:0x01b3 }] */
    /* JADX WARN: Removed duplicated region for block: B:455:0x0256  */
    /* JADX WARN: Removed duplicated region for block: B:46:0x0261  */
    /* JADX WARN: Removed duplicated region for block: B:497:0x017f  */
    /* JADX WARN: Removed duplicated region for block: B:56:0x0a89  */
    /* JADX WARN: Removed duplicated region for block: B:77:0x082e  */
    /* JADX WARN: Type inference failed for: r0v133, types: [java.util.HashSet, java.util.Set<java.lang.Integer>] */
    /* JADX WARN: Type inference failed for: r0v141, types: [java.util.HashSet, java.util.Set<java.lang.Integer>] */
    /* JADX WARN: Type inference failed for: r0v48, types: [java.util.Map] */
    /* JADX WARN: Type inference failed for: r0v68, types: [java.util.Map] */
    /* JADX WARN: Type inference failed for: r0v88, types: [java.util.HashSet, java.util.Set<java.lang.Integer>] */
    /* JADX WARN: Type inference failed for: r0v90, types: [java.util.HashSet, java.util.Set<java.lang.Integer>] */
    /* JADX WARN: Type inference failed for: r10v11, types: [java.util.HashSet, java.util.Set<java.lang.Integer>] */
    /* JADX WARN: Type inference failed for: r1v11, types: [java.util.Map<java.lang.Integer, x2.e7>, l.g] */
    /* JADX WARN: Type inference failed for: r3v30, types: [java.util.Map<java.lang.Integer, x2.e7>, l.g] */
    /* JADX WARN: Type inference failed for: r4v45, types: [android.database.sqlite.SQLiteDatabase] */
    /* JADX WARN: Type inference failed for: r5v45, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r5v46 */
    /* JADX WARN: Type inference failed for: r5v47, types: [java.lang.String[]] */
    /* JADX WARN: Type inference failed for: r5v5, types: [android.database.sqlite.SQLiteDatabase] */
    /* JADX WARN: Type inference failed for: r5v6 */
    /* JADX WARN: Type inference failed for: r5v8, types: [android.database.Cursor] */
    /* JADX WARN: Type inference failed for: r6v21, types: [java.util.HashSet, java.util.Set<java.lang.Integer>] */
    /* renamed from: l */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final java.util.List<p152w2.C1580i1> m4883l(java.lang.String r64, java.util.List<p152w2.C1628m1> r65, java.util.List<p152w2.C1508c2> r66, java.lang.Long r67, java.lang.Long r68) {
        /*
            Method dump skipped, instructions count: 2853
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p158x2.C1910i7.m4883l(java.lang.String, java.util.List, java.util.List, java.lang.Long, java.lang.Long):java.util.List");
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.Map<java.lang.Integer, x2.e7>, l.g] */
    /* JADX WARN: Type inference failed for: r0v3, types: [java.util.Map<java.lang.Integer, x2.e7>, l.g] */
    /* JADX WARN: Type inference failed for: r1v1, types: [java.util.Map<java.lang.Integer, x2.e7>, l.g] */
    /* renamed from: m */
    public final C1874e7 m4884m(Integer num) {
        if (this.f7772p.containsKey(num)) {
            return (C1874e7) this.f7772p.getOrDefault(num, null);
        }
        C1874e7 c1874e7 = new C1874e7(this, this.f7770n);
        this.f7772p.put(num, c1874e7);
        return c1874e7;
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.Map<java.lang.Integer, x2.e7>, l.g] */
    /* renamed from: n */
    public final boolean m4885n(int i6, int i7) {
        C1874e7 c1874e7 = (C1874e7) this.f7772p.getOrDefault(Integer.valueOf(i6), null);
        if (c1874e7 == null) {
            return false;
        }
        return c1874e7.f7673d.get(i7);
    }
}
