package p158x2;

import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import java.util.Objects;

/* renamed from: x2.d7 */
/* loaded from: classes.dex */
public final class C1865d7 {

    /* renamed from: a */
    public final C1948n4 f7651a;

    public C1865d7(C1948n4 c1948n4) {
        this.f7651a = c1948n4;
    }

    /* renamed from: a */
    public final void m4772a(String str, Bundle bundle) {
        String uri;
        this.f7651a.mo4959b().mo4915h();
        if (this.f7651a.m4965i()) {
            return;
        }
        if (bundle.isEmpty()) {
            uri = null;
        } else {
            if (true == str.isEmpty()) {
                str = "auto";
            }
            Uri.Builder builder = new Uri.Builder();
            builder.path(str);
            for (String str2 : bundle.keySet()) {
                builder.appendQueryParameter(str2, bundle.getString(str2));
            }
            uri = builder.build().toString();
        }
        if (TextUtils.isEmpty(uri)) {
            return;
        }
        this.f7651a.m4970q().f8237E.m5137b(uri);
        C1995t3 c1995t3 = this.f7651a.m4970q().f8238F;
        Objects.requireNonNull(this.f7651a.f7930w);
        c1995t3.m5051b(System.currentTimeMillis());
    }

    /* renamed from: b */
    public final boolean m4773b() {
        if (!m4774c()) {
            return false;
        }
        Objects.requireNonNull(this.f7651a.f7930w);
        return System.currentTimeMillis() - this.f7651a.m4970q().f8238F.m5050a() > this.f7651a.f7923p.m4781n(null, C2026x2.f8171L);
    }

    /* renamed from: c */
    public final boolean m4774c() {
        return this.f7651a.m4970q().f8238F.m5050a() > 0;
    }
}
