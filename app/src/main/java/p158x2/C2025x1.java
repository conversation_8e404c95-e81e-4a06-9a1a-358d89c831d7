package p158x2;

import android.os.Bundle;
import java.util.Iterator;
import java.util.Map;
import p077l.AbstractC1052f;
import p077l.C1047a;

/* renamed from: x2.x1 */
/* loaded from: classes.dex */
public final class C2025x1 extends C2018w2 {

    /* renamed from: l */
    public final Map<String, Long> f8157l;

    /* renamed from: m */
    public final Map<String, Integer> f8158m;

    /* renamed from: n */
    public long f8159n;

    public C2025x1(C1948n4 c1948n4) {
        super(c1948n4);
        this.f8158m = new C1047a();
        this.f8157l = new C1047a();
    }

    /* JADX WARN: Type inference failed for: r1v5, types: [java.util.Map<java.lang.String, java.lang.Long>, l.g] */
    /* JADX WARN: Type inference failed for: r3v0, types: [java.util.Map<java.lang.String, java.lang.Long>, l.g] */
    /* renamed from: i */
    public final void m5139i(long j6) {
        C1981r5 m5056o = ((C1948n4) this.f8145k).m4978y().m5056o(false);
        Iterator it = ((AbstractC1052f.c) this.f8157l.keySet()).iterator();
        while (it.hasNext()) {
            String str = (String) it.next();
            m5141k(str, j6 - ((Long) this.f8157l.getOrDefault(str, null)).longValue(), m5056o);
        }
        if (!this.f8157l.isEmpty()) {
            m5140j(j6 - this.f8159n, m5056o);
        }
        m5142l(j6);
    }

    /* renamed from: j */
    public final void m5140j(long j6, C1981r5 c1981r5) {
        if (c1981r5 == null) {
            ((C1948n4) this.f8145k).mo4962e().f7792x.m4841b("Not logging ad exposure. No active activity");
            return;
        }
        if (j6 < 1000) {
            ((C1948n4) this.f8145k).mo4962e().f7792x.m4842c("Not logging ad exposure. Less than 1000 ms. exposure", Long.valueOf(j6));
            return;
        }
        Bundle bundle = new Bundle();
        bundle.putLong("_xt", j6);
        C1997t5.m5052q(c1981r5, bundle, true);
        ((C1948n4) this.f8145k).m4972s().m4930s("am", "_xa", bundle);
    }

    /* renamed from: k */
    public final void m5141k(String str, long j6, C1981r5 c1981r5) {
        if (c1981r5 == null) {
            ((C1948n4) this.f8145k).mo4962e().f7792x.m4841b("Not logging ad unit exposure. No active activity");
            return;
        }
        if (j6 < 1000) {
            ((C1948n4) this.f8145k).mo4962e().f7792x.m4842c("Not logging ad unit exposure. Less than 1000 ms. exposure", Long.valueOf(j6));
            return;
        }
        Bundle bundle = new Bundle();
        bundle.putString("_ai", str);
        bundle.putLong("_xt", j6);
        C1997t5.m5052q(c1981r5, bundle, true);
        ((C1948n4) this.f8145k).m4972s().m4930s("am", "_xu", bundle);
    }

    /* JADX WARN: Type inference failed for: r0v4, types: [java.util.Map<java.lang.String, java.lang.Long>, l.g] */
    /* JADX WARN: Type inference failed for: r2v0, types: [java.util.Map<java.lang.String, java.lang.Long>, l.g] */
    /* renamed from: l */
    public final void m5142l(long j6) {
        Iterator it = ((AbstractC1052f.c) this.f8157l.keySet()).iterator();
        while (it.hasNext()) {
            this.f8157l.put((String) it.next(), Long.valueOf(j6));
        }
        if (this.f8157l.isEmpty()) {
            return;
        }
        this.f8159n = j6;
    }
}
