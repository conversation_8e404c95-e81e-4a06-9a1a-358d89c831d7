package p158x2;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteDatabaseLockedException;
import android.database.sqlite.SQLiteException;
import android.database.sqlite.SQLiteOpenHelper;
import java.util.Objects;
import p008b0.C0385m;

/* renamed from: x2.c3 */
/* loaded from: classes.dex */
public final class C1852c3 extends SQLiteOpenHelper {

    /* renamed from: j */
    public final /* synthetic */ C1861d3 f7615j;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C1852c3(C1861d3 c1861d3, Context context) {
        super(context, "google_app_measurement_local.db", (SQLiteDatabase.CursorFactory) null, 1);
        this.f7615j = c1861d3;
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public final SQLiteDatabase getWritableDatabase() {
        try {
            return super.getWritableDatabase();
        } catch (SQLiteDatabaseLockedException e6) {
            throw e6;
        } catch (SQLiteException unused) {
            ((C1948n4) this.f7615j.f8145k).mo4962e().f7784p.m4841b("Opening the local database failed, dropping and recreating it");
            Objects.requireNonNull((C1948n4) this.f7615j.f8145k);
            if (!((C1948n4) this.f7615j.f8145k).f7917j.getDatabasePath("google_app_measurement_local.db").delete()) {
                ((C1948n4) this.f7615j.f8145k).mo4962e().f7784p.m4842c("Failed to delete corrupted local db file", "google_app_measurement_local.db");
            }
            try {
                return super.getWritableDatabase();
            } catch (SQLiteException e7) {
                ((C1948n4) this.f7615j.f8145k).mo4962e().f7784p.m4842c("Failed to open local database. Events will bypass local storage", e7);
                return null;
            }
        }
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public final void onCreate(SQLiteDatabase sQLiteDatabase) {
        C0385m.m1431x(((C1948n4) this.f7615j.f8145k).mo4962e(), sQLiteDatabase);
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public final void onDowngrade(SQLiteDatabase sQLiteDatabase, int i6, int i7) {
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public final void onOpen(SQLiteDatabase sQLiteDatabase) {
        C0385m.m1427s(((C1948n4) this.f7615j.f8145k).mo4962e(), sQLiteDatabase, "messages", "create table if not exists messages ( type INTEGER NOT NULL, entry BLOB NOT NULL)", "type,entry", null);
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public final void onUpgrade(SQLiteDatabase sQLiteDatabase, int i6, int i7) {
    }
}
