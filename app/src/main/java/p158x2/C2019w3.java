package p158x2;

import android.content.SharedPreferences;
import p153w3.C1798e;

/* renamed from: x2.w3 */
/* loaded from: classes.dex */
public final class C2019w3 {

    /* renamed from: a */
    public final String f8140a;

    /* renamed from: b */
    public boolean f8141b;

    /* renamed from: c */
    public String f8142c;

    /* renamed from: d */
    public final /* synthetic */ C2027x3 f8143d;

    public C2019w3(C2027x3 c2027x3, String str) {
        this.f8143d = c2027x3;
        C1798e.m4554o(str);
        this.f8140a = str;
    }

    /* renamed from: a */
    public final String m5136a() {
        if (!this.f8141b) {
            this.f8141b = true;
            this.f8142c = this.f8143d.m5145o().getString(this.f8140a, null);
        }
        return this.f8142c;
    }

    /* renamed from: b */
    public final void m5137b(String str) {
        SharedPreferences.Editor edit = this.f8143d.m5145o().edit();
        edit.putString(this.f8140a, str);
        edit.apply();
        this.f8142c = str;
    }
}
