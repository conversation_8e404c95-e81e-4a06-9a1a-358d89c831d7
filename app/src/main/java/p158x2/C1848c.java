package p158x2;

import android.os.Parcel;
import android.os.Parcelable;
import p099o2.C1147b;

/* renamed from: x2.c */
/* loaded from: classes.dex */
public final class C1848c implements Parcelable.Creator<C1839b> {
    @Override // android.os.Parcelable.Creator
    public final C1839b createFromParcel(Parcel parcel) {
        int m2977l = C1147b.m2977l(parcel);
        long j6 = 0;
        long j7 = 0;
        long j8 = 0;
        String str = null;
        String str2 = null;
        C2022w6 c2022w6 = null;
        String str3 = null;
        C1967q c1967q = null;
        C1967q c1967q2 = null;
        C1967q c1967q3 = null;
        boolean z5 = false;
        while (parcel.dataPosition() < m2977l) {
            int readInt = parcel.readInt();
            switch (65535 & readInt) {
                case 2:
                    str = C1147b.m2968c(parcel, readInt);
                    break;
                case 3:
                    str2 = C1147b.m2968c(parcel, readInt);
                    break;
                case 4:
                    c2022w6 = (C2022w6) C1147b.m2967b(parcel, readInt, C2022w6.CREATOR);
                    break;
                case 5:
                    j6 = C1147b.m2974i(parcel, readInt);
                    break;
                case 6:
                    z5 = C1147b.m2971f(parcel, readInt);
                    break;
                case 7:
                    str3 = C1147b.m2968c(parcel, readInt);
                    break;
                case 8:
                    c1967q = (C1967q) C1147b.m2967b(parcel, readInt, C1967q.CREATOR);
                    break;
                case 9:
                    j7 = C1147b.m2974i(parcel, readInt);
                    break;
                case 10:
                    c1967q2 = (C1967q) C1147b.m2967b(parcel, readInt, C1967q.CREATOR);
                    break;
                case 11:
                    j8 = C1147b.m2974i(parcel, readInt);
                    break;
                case 12:
                    c1967q3 = (C1967q) C1147b.m2967b(parcel, readInt, C1967q.CREATOR);
                    break;
                default:
                    C1147b.m2976k(parcel, readInt);
                    break;
            }
        }
        C1147b.m2970e(parcel, m2977l);
        return new C1839b(str, str2, c2022w6, j6, z5, str3, c1967q, j7, c1967q2, j8, c1967q3);
    }

    @Override // android.os.Parcelable.Creator
    public final /* bridge */ /* synthetic */ C1839b[] newArray(int i6) {
        return new C1839b[i6];
    }
}
