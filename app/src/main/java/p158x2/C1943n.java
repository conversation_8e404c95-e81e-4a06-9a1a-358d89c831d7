package p158x2;

import p153w3.C1798e;

/* renamed from: x2.n */
/* loaded from: classes.dex */
public final class C1943n {

    /* renamed from: a */
    public final String f7879a;

    /* renamed from: b */
    public final String f7880b;

    /* renamed from: c */
    public final long f7881c;

    /* renamed from: d */
    public final long f7882d;

    /* renamed from: e */
    public final long f7883e;

    /* renamed from: f */
    public final long f7884f;

    /* renamed from: g */
    public final long f7885g;

    /* renamed from: h */
    public final Long f7886h;

    /* renamed from: i */
    public final Long f7887i;

    /* renamed from: j */
    public final Long f7888j;

    /* renamed from: k */
    public final Boolean f7889k;

    public C1943n(String str, String str2, long j6, long j7, long j8, long j9, long j10, Long l6, Long l7, Long l8, Boolean bool) {
        C1798e.m4554o(str);
        C1798e.m4554o(str2);
        C1798e.m4550m(j6 >= 0);
        C1798e.m4550m(j7 >= 0);
        C1798e.m4550m(j8 >= 0);
        C1798e.m4550m(j10 >= 0);
        this.f7879a = str;
        this.f7880b = str2;
        this.f7881c = j6;
        this.f7882d = j7;
        this.f7883e = j8;
        this.f7884f = j9;
        this.f7885g = j10;
        this.f7886h = l6;
        this.f7887i = l7;
        this.f7888j = l8;
        this.f7889k = bool;
    }

    /* renamed from: a */
    public final C1943n m4950a(long j6) {
        return new C1943n(this.f7879a, this.f7880b, this.f7881c, this.f7882d, this.f7883e, j6, this.f7885g, this.f7886h, this.f7887i, this.f7888j, this.f7889k);
    }

    /* renamed from: b */
    public final C1943n m4951b(long j6, long j7) {
        return new C1943n(this.f7879a, this.f7880b, this.f7881c, this.f7882d, this.f7883e, this.f7884f, j6, Long.valueOf(j7), this.f7887i, this.f7888j, this.f7889k);
    }

    /* renamed from: c */
    public final C1943n m4952c(Long l6, Long l7, Boolean bool) {
        return new C1943n(this.f7879a, this.f7880b, this.f7881c, this.f7882d, this.f7883e, this.f7884f, this.f7885g, this.f7886h, l6, l7, (bool == null || bool.booleanValue()) ? bool : null);
    }
}
