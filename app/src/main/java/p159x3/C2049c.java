package p159x3;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;
import com.liaoyuan.aicast.R;
import java.util.WeakHashMap;
import p008b0.C0385m;
import p012b4.C0409a;
import p029e0.C0766p;
import p029e0.C0769s;
import p114q3.C1293n;
import p132t3.C1401c;
import p153w3.C1798e;

/* renamed from: x3.c */
/* loaded from: classes.dex */
public class C2049c extends FrameLayout {

    /* renamed from: q */
    public static final a f8284q = new a();

    /* renamed from: j */
    public InterfaceC2048b f8285j;

    /* renamed from: k */
    public InterfaceC2047a f8286k;

    /* renamed from: l */
    public int f8287l;

    /* renamed from: m */
    public final float f8288m;

    /* renamed from: n */
    public final float f8289n;

    /* renamed from: o */
    public ColorStateList f8290o;

    /* renamed from: p */
    public PorterDuff.Mode f8291p;

    /* renamed from: x3.c$a */
    public static class a implements View.OnTouchListener {
        @Override // android.view.View.OnTouchListener
        @SuppressLint({"ClickableViewAccessibility"})
        public final boolean onTouch(View view, MotionEvent motionEvent) {
            return true;
        }
    }

    public C2049c(Context context, AttributeSet attributeSet) {
        super(C0409a.m1460a(context, attributeSet, 0, 0), attributeSet);
        Context context2 = getContext();
        TypedArray obtainStyledAttributes = context2.obtainStyledAttributes(attributeSet, C0385m.f2305A0);
        if (obtainStyledAttributes.hasValue(6)) {
            float dimensionPixelSize = obtainStyledAttributes.getDimensionPixelSize(6, 0);
            WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
            setElevation(dimensionPixelSize);
        }
        this.f8287l = obtainStyledAttributes.getInt(2, 0);
        this.f8288m = obtainStyledAttributes.getFloat(3, 1.0f);
        setBackgroundTintList(C1401c.m3414a(context2, obtainStyledAttributes, 4));
        setBackgroundTintMode(C1293n.m3197c(obtainStyledAttributes.getInt(5, -1), PorterDuff.Mode.SRC_IN));
        this.f8289n = obtainStyledAttributes.getFloat(1, 1.0f);
        obtainStyledAttributes.recycle();
        setOnTouchListener(f8284q);
        setFocusable(true);
        if (getBackground() == null) {
            float dimension = getResources().getDimension(R.dimen.mtrl_snackbar_background_corner_radius);
            GradientDrawable gradientDrawable = new GradientDrawable();
            gradientDrawable.setShape(0);
            gradientDrawable.setCornerRadius(dimension);
            gradientDrawable.setColor(C1798e.m4514F(C1798e.m4510A(this, R.attr.colorSurface), C1798e.m4510A(this, R.attr.colorOnSurface), getBackgroundOverlayColorAlpha()));
            ColorStateList colorStateList = this.f8290o;
            if (colorStateList != null) {
                gradientDrawable.setTintList(colorStateList);
            }
            WeakHashMap<View, C0769s> weakHashMap2 = C0766p.f4041a;
            setBackground(gradientDrawable);
        }
    }

    public float getActionTextColorAlpha() {
        return this.f8289n;
    }

    public int getAnimationMode() {
        return this.f8287l;
    }

    public float getBackgroundOverlayColorAlpha() {
        return this.f8288m;
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onAttachedToWindow() {
        super.onAttachedToWindow();
        InterfaceC2047a interfaceC2047a = this.f8286k;
        if (interfaceC2047a != null) {
            interfaceC2047a.m5159b();
        }
        WeakHashMap<View, C0769s> weakHashMap = C0766p.f4041a;
        requestApplyInsets();
    }

    @Override // android.view.ViewGroup, android.view.View
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        InterfaceC2047a interfaceC2047a = this.f8286k;
        if (interfaceC2047a != null) {
            interfaceC2047a.m5158a();
        }
    }

    @Override // android.widget.FrameLayout, android.view.ViewGroup, android.view.View
    public final void onLayout(boolean z5, int i6, int i7, int i8, int i9) {
        super.onLayout(z5, i6, i7, i8, i9);
        InterfaceC2048b interfaceC2048b = this.f8285j;
        if (interfaceC2048b != null) {
            interfaceC2048b.m5160a();
        }
    }

    public void setAnimationMode(int i6) {
        this.f8287l = i6;
    }

    @Override // android.view.View
    public void setBackground(Drawable drawable) {
        setBackgroundDrawable(drawable);
    }

    @Override // android.view.View
    public void setBackgroundDrawable(Drawable drawable) {
        if (drawable != null && this.f8290o != null) {
            drawable = drawable.mutate();
            drawable.setTintList(this.f8290o);
            drawable.setTintMode(this.f8291p);
        }
        super.setBackgroundDrawable(drawable);
    }

    @Override // android.view.View
    public void setBackgroundTintList(ColorStateList colorStateList) {
        this.f8290o = colorStateList;
        if (getBackground() != null) {
            Drawable mutate = getBackground().mutate();
            mutate.setTintList(colorStateList);
            mutate.setTintMode(this.f8291p);
            if (mutate != getBackground()) {
                super.setBackgroundDrawable(mutate);
            }
        }
    }

    @Override // android.view.View
    public void setBackgroundTintMode(PorterDuff.Mode mode) {
        this.f8291p = mode;
        if (getBackground() != null) {
            Drawable mutate = getBackground().mutate();
            mutate.setTintMode(mode);
            if (mutate != getBackground()) {
                super.setBackgroundDrawable(mutate);
            }
        }
    }

    public void setOnAttachStateChangeListener(InterfaceC2047a interfaceC2047a) {
        this.f8286k = interfaceC2047a;
    }

    @Override // android.view.View
    public void setOnClickListener(View.OnClickListener onClickListener) {
        setOnTouchListener(onClickListener != null ? null : f8284q);
        super.setOnClickListener(onClickListener);
    }

    public void setOnLayoutChangeListener(InterfaceC2048b interfaceC2048b) {
        this.f8285j = interfaceC2048b;
    }
}
