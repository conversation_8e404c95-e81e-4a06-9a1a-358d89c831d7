package p159x3;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import java.util.Objects;

/* renamed from: x3.d */
/* loaded from: classes.dex */
public final class C2050d {

    /* renamed from: b */
    public static C2050d f8292b;

    /* renamed from: a */
    public final Object f8293a = new Object();

    /* renamed from: x3.d$a */
    public class a implements Handler.Callback {
        public a() {
        }

        @Override // android.os.Handler.Callback
        public final boolean handleMessage(Message message) {
            if (message.what != 0) {
                return false;
            }
            C2050d c2050d = C2050d.this;
            b bVar = (b) message.obj;
            synchronized (c2050d.f8293a) {
                if (bVar == null) {
                    Objects.requireNonNull(bVar);
                    throw null;
                }
            }
            return true;
        }
    }

    /* renamed from: x3.d$b */
    public static class b {
    }

    public C2050d() {
        new Handler(Looper.getMainLooper(), new a());
    }
}
