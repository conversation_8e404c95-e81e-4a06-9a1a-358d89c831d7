package p152w2;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import javax.annotation.Nullable;

/* renamed from: w2.y2 */
/* loaded from: classes.dex */
public abstract class AbstractC1773y2<T> {

    /* renamed from: f */
    public static final Object f7281f = new Object();

    /* renamed from: g */
    @Nullable
    public static volatile C1605k2 f7282g;

    /* renamed from: h */
    public static final AtomicInteger f7283h;

    /* renamed from: a */
    public final C1749w2 f7284a;

    /* renamed from: b */
    public final String f7285b;

    /* renamed from: c */
    public final T f7286c;

    /* renamed from: d */
    public volatile int f7287d = -1;

    /* renamed from: e */
    public volatile T f7288e;

    static {
        new AtomicReference();
        f7283h = new AtomicInteger();
    }

    /* JADX WARN: Multi-variable type inference failed */
    public /* synthetic */ AbstractC1773y2(C1749w2 c1749w2, String str, Object obj) {
        if (c1749w2.f7243a == null) {
            throw new IllegalArgumentException("Must pass a valid SharedPreferences file name or ContentProvider URI");
        }
        this.f7284a = c1749w2;
        this.f7285b = str;
        this.f7286c = obj;
    }

    /* renamed from: a */
    public abstract T mo4284a(Object obj);

    /* renamed from: b */
    public final String m4459b() {
        Objects.requireNonNull(this.f7284a);
        return this.f7285b;
    }

    /* JADX WARN: Removed duplicated region for block: B:22:0x009c  */
    /* JADX WARN: Removed duplicated region for block: B:25:0x00cd A[Catch: all -> 0x0123, TryCatch #0 {, blocks: (B:5:0x000b, B:7:0x000f, B:9:0x0015, B:11:0x0029, B:13:0x0035, B:15:0x003e, B:17:0x0050, B:18:0x005b, B:19:0x0055, B:23:0x00bf, B:25:0x00cd, B:27:0x00de, B:30:0x00ed, B:32:0x00f9, B:33:0x0103, B:34:0x00fe, B:36:0x010c, B:37:0x010f, B:38:0x0113, B:39:0x009d, B:41:0x00b5, B:43:0x00bd, B:45:0x005f, B:47:0x0065, B:49:0x006d, B:51:0x0084, B:53:0x0094, B:55:0x0118, B:56:0x011a, B:58:0x011b, B:59:0x0120, B:60:0x0121), top: B:4:0x000b }] */
    /* JADX WARN: Removed duplicated region for block: B:39:0x009d A[Catch: all -> 0x0123, TryCatch #0 {, blocks: (B:5:0x000b, B:7:0x000f, B:9:0x0015, B:11:0x0029, B:13:0x0035, B:15:0x003e, B:17:0x0050, B:18:0x005b, B:19:0x0055, B:23:0x00bf, B:25:0x00cd, B:27:0x00de, B:30:0x00ed, B:32:0x00f9, B:33:0x0103, B:34:0x00fe, B:36:0x010c, B:37:0x010f, B:38:0x0113, B:39:0x009d, B:41:0x00b5, B:43:0x00bd, B:45:0x005f, B:47:0x0065, B:49:0x006d, B:51:0x0084, B:53:0x0094, B:55:0x0118, B:56:0x011a, B:58:0x011b, B:59:0x0120, B:60:0x0121), top: B:4:0x000b }] */
    /* renamed from: c */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final T m4460c() {
        /*
            Method dump skipped, instructions count: 297
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.AbstractC1773y2.m4460c():java.lang.Object");
    }
}
