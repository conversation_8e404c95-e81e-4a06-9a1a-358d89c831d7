package p152w2;

/* renamed from: w2.t0 */
/* loaded from: classes.dex */
public final class C1711t0 extends AbstractC1691r4<C1711t0, C1699s0> implements InterfaceC1728u5 {
    private static final C1711t0 zzi;
    private int zza;
    private C1481a1 zze;
    private C1747w0 zzf;
    private boolean zzg;
    private String zzh = "";

    static {
        C1711t0 c1711t0 = new C1711t0();
        zzi = c1711t0;
        AbstractC1691r4.m4061q(C1711t0.class, c1711t0);
    }

    /* renamed from: A */
    public static C1711t0 m4147A() {
        return zzi;
    }

    /* renamed from: C */
    public static /* synthetic */ void m4149C(C1711t0 c1711t0, String str) {
        c1711t0.zza |= 8;
        c1711t0.zzh = str;
    }

    @Override // p152w2.AbstractC1691r4
    /* renamed from: r */
    public final Object mo3604r(int i6) {
        int i7 = i6 - 1;
        if (i7 == 0) {
            return (byte) 1;
        }
        if (i7 == 2) {
            return new C1525d6(zzi, "\u0001\u0004\u0000\u0001\u0001\u0004\u0004\u0000\u0000\u0000\u0001ဉ\u0000\u0002ဉ\u0001\u0003ဇ\u0002\u0004ဈ\u0003", new Object[]{"zza", "zze", "zzf", "zzg", "zzh"});
        }
        if (i7 == 3) {
            return new C1711t0();
        }
        if (i7 == 4) {
            return new C1699s0(null);
        }
        if (i7 != 5) {
            return null;
        }
        return zzi;
    }

    /* renamed from: s */
    public final boolean m4150s() {
        return (this.zza & 1) != 0;
    }

    /* renamed from: t */
    public final C1481a1 m4151t() {
        C1481a1 c1481a1 = this.zze;
        return c1481a1 == null ? C1481a1.m3602A() : c1481a1;
    }

    /* renamed from: u */
    public final boolean m4152u() {
        return (this.zza & 2) != 0;
    }

    /* renamed from: v */
    public final C1747w0 m4153v() {
        C1747w0 c1747w0 = this.zzf;
        return c1747w0 == null ? C1747w0.m4341C() : c1747w0;
    }

    /* renamed from: w */
    public final boolean m4154w() {
        return (this.zza & 4) != 0;
    }

    /* renamed from: x */
    public final boolean m4155x() {
        return this.zzg;
    }

    /* renamed from: y */
    public final boolean m4156y() {
        return (this.zza & 8) != 0;
    }

    /* renamed from: z */
    public final String m4157z() {
        return this.zzh;
    }
}
