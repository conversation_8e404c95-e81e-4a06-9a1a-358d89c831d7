package p152w2;

import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.util.Objects;

/* renamed from: w2.y4 */
/* loaded from: classes.dex */
public final class C1775y4 {

    /* renamed from: a */
    public static final Charset f7289a = Charset.forName("UTF-8");

    /* renamed from: b */
    public static final byte[] f7290b;

    static {
        Charset.forName("ISO-8859-1");
        byte[] bArr = new byte[0];
        f7290b = bArr;
        ByteBuffer.wrap(bArr);
    }

    /* renamed from: a */
    public static int m4461a(long j6) {
        return (int) (j6 ^ (j6 >>> 32));
    }

    /* renamed from: b */
    public static int m4462b(boolean z5) {
        return z5 ? 1231 : 1237;
    }

    /* renamed from: c */
    public static Object m4463c(Object obj, Object obj2) {
        AbstractC1630m3 mo4064e = ((InterfaceC1716t5) obj).mo4064e();
        InterfaceC1716t5 interfaceC1716t5 = (InterfaceC1716t5) obj2;
        Objects.requireNonNull(mo4064e);
        C1643n4 c1643n4 = (C1643n4) mo4064e;
        if (!c1643n4.f7098j.getClass().isInstance(interfaceC1716t5)) {
            throw new IllegalArgumentException("mergeFrom(MessageLite) can only merge messages of the same type.");
        }
        c1643n4.m3948g((AbstractC1691r4) ((AbstractC1642n3) interfaceC1716t5));
        return c1643n4.m3952k();
    }
}
