package p152w2;

/* renamed from: w2.m4 */
/* loaded from: classes.dex */
public final class C1631m4 implements InterfaceC1704s5 {

    /* renamed from: a */
    public static final C1631m4 f7087a = new C1631m4();

    @Override // p152w2.InterfaceC1704s5
    /* renamed from: a */
    public final boolean mo3871a(Class<?> cls) {
        return AbstractC1691r4.class.isAssignableFrom(cls);
    }

    @Override // p152w2.InterfaceC1704s5
    /* renamed from: b */
    public final InterfaceC1692r5 mo3872b(Class<?> cls) {
        if (!AbstractC1691r4.class.isAssignableFrom(cls)) {
            String name = cls.getName();
            throw new IllegalArgumentException(name.length() != 0 ? "Unsupported message type: ".concat(name) : new String("Unsupported message type: "));
        }
        try {
            return (InterfaceC1692r5) AbstractC1691r4.m4060p(cls.asSubclass(AbstractC1691r4.class)).mo3604r(3);
        } catch (Exception e6) {
            String name2 = cls.getName();
            throw new RuntimeException(name2.length() != 0 ? "Unable to get message info for ".concat(name2) : new String("Unable to get message info for "), e6);
        }
    }
}
