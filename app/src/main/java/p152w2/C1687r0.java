package p152w2;

import java.util.List;

/* renamed from: w2.r0 */
/* loaded from: classes.dex */
public final class C1687r0 extends AbstractC1691r4<C1687r0, C1675q0> implements InterfaceC1728u5 {
    private static final C1687r0 zzm;
    private int zza;
    private int zze;
    private String zzf = "";
    private InterfaceC1763x4<C1711t0> zzg = C1512c6.f6916m;
    private boolean zzh;
    private C1747w0 zzi;
    private boolean zzj;
    private boolean zzk;
    private boolean zzl;

    static {
        C1687r0 c1687r0 = new C1687r0();
        zzm = c1687r0;
        AbstractC1691r4.m4061q(C1687r0.class, c1687r0);
    }

    /* renamed from: E */
    public static C1675q0 m4033E() {
        return zzm.m4065m();
    }

    /* renamed from: G */
    public static /* synthetic */ void m4035G(C1687r0 c1687r0, String str) {
        c1687r0.zza |= 2;
        c1687r0.zzf = str;
    }

    /* renamed from: H */
    public static /* synthetic */ void m4036H(C1687r0 c1687r0, int i6, C1711t0 c1711t0) {
        InterfaceC1763x4<C1711t0> interfaceC1763x4 = c1687r0.zzg;
        if (!interfaceC1763x4.mo3965a()) {
            c1687r0.zzg = AbstractC1691r4.m4059l(interfaceC1763x4);
        }
        c1687r0.zzg.set(i6, c1711t0);
    }

    /* renamed from: A */
    public final boolean m4037A() {
        return this.zzj;
    }

    /* renamed from: B */
    public final boolean m4038B() {
        return this.zzk;
    }

    /* renamed from: C */
    public final boolean m4039C() {
        return (this.zza & 64) != 0;
    }

    /* renamed from: D */
    public final boolean m4040D() {
        return this.zzl;
    }

    @Override // p152w2.AbstractC1691r4
    /* renamed from: r */
    public final Object mo3604r(int i6) {
        int i7 = i6 - 1;
        if (i7 == 0) {
            return (byte) 1;
        }
        if (i7 == 2) {
            return new C1525d6(zzm, "\u0001\b\u0000\u0001\u0001\b\b\u0000\u0001\u0000\u0001င\u0000\u0002ဈ\u0001\u0003\u001b\u0004ဇ\u0002\u0005ဉ\u0003\u0006ဇ\u0004\u0007ဇ\u0005\bဇ\u0006", new Object[]{"zza", "zze", "zzf", "zzg", C1711t0.class, "zzh", "zzi", "zzj", "zzk", "zzl"});
        }
        if (i7 == 3) {
            return new C1687r0();
        }
        if (i7 == 4) {
            return new C1675q0(null);
        }
        if (i7 != 5) {
            return null;
        }
        return zzm;
    }

    /* renamed from: s */
    public final boolean m4041s() {
        return (this.zza & 1) != 0;
    }

    /* renamed from: t */
    public final int m4042t() {
        return this.zze;
    }

    /* renamed from: u */
    public final String m4043u() {
        return this.zzf;
    }

    /* renamed from: v */
    public final List<C1711t0> m4044v() {
        return this.zzg;
    }

    /* renamed from: w */
    public final int m4045w() {
        return this.zzg.size();
    }

    /* renamed from: x */
    public final C1711t0 m4046x(int i6) {
        return this.zzg.get(i6);
    }

    /* renamed from: y */
    public final boolean m4047y() {
        return (this.zza & 8) != 0;
    }

    /* renamed from: z */
    public final C1747w0 m4048z() {
        C1747w0 c1747w0 = this.zzi;
        return c1747w0 == null ? C1747w0.m4341C() : c1747w0;
    }
}
