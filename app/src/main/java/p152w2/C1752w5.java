package p152w2;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import p008b0.C0385m;
import p090n.C1094g;
import p153w3.C1798e;
import sun.misc.Unsafe;

/* renamed from: w2.w5 */
/* loaded from: classes.dex */
public final class C1752w5<T> implements InterfaceC1537e6<T> {

    /* renamed from: n */
    public static final int[] f7246n = new int[0];

    /* renamed from: o */
    public static final Unsafe f7247o = C1789z6.m4500r();

    /* renamed from: a */
    public final int[] f7248a;

    /* renamed from: b */
    public final Object[] f7249b;

    /* renamed from: c */
    public final int f7250c;

    /* renamed from: d */
    public final int f7251d;

    /* renamed from: e */
    public final InterfaceC1716t5 f7252e;

    /* renamed from: f */
    public final boolean f7253f;

    /* renamed from: g */
    public final boolean f7254g;

    /* renamed from: h */
    public final int[] f7255h;

    /* renamed from: i */
    public final int f7256i;

    /* renamed from: j */
    public final int f7257j;

    /* renamed from: k */
    public final AbstractC1572h5 f7258k;

    /* renamed from: l */
    public final AbstractC1669p6<?, ?> f7259l;

    /* renamed from: m */
    public final AbstractC1535e4<?> f7260m;

    public C1752w5(int[] iArr, Object[] objArr, int i6, int i7, InterfaceC1716t5 interfaceC1716t5, boolean z5, int[] iArr2, int i8, int i9, AbstractC1572h5 abstractC1572h5, AbstractC1669p6 abstractC1669p6, AbstractC1535e4 abstractC1535e4, C1668p5 c1668p5) {
        this.f7248a = iArr;
        this.f7249b = objArr;
        this.f7250c = i6;
        this.f7251d = i7;
        this.f7254g = z5;
        this.f7253f = abstractC1535e4 != null && abstractC1535e4.mo3745a(interfaceC1716t5);
        this.f7255h = iArr2;
        this.f7256i = i8;
        this.f7257j = i9;
        this.f7258k = abstractC1572h5;
        this.f7259l = abstractC1669p6;
        this.f7260m = abstractC1535e4;
        this.f7252e = interfaceC1716t5;
    }

    /* renamed from: A */
    public static C1752w5 m4359A(InterfaceC1692r5 interfaceC1692r5, C1776y5 c1776y5, AbstractC1572h5 abstractC1572h5, AbstractC1669p6 abstractC1669p6, AbstractC1535e4 abstractC1535e4, C1668p5 c1668p5) {
        if (interfaceC1692r5 instanceof C1525d6) {
            return m4360B((C1525d6) interfaceC1692r5, c1776y5, abstractC1572h5, abstractC1669p6, abstractC1535e4, c1668p5);
        }
        throw null;
    }

    /* JADX WARN: Removed duplicated region for block: B:66:0x0275  */
    /* JADX WARN: Removed duplicated region for block: B:69:0x0290  */
    /* JADX WARN: Removed duplicated region for block: B:82:0x0293  */
    /* JADX WARN: Removed duplicated region for block: B:83:0x0278  */
    /* renamed from: B */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static <T> p152w2.C1752w5<T> m4360B(p152w2.C1525d6 r33, p152w2.C1776y5 r34, p152w2.AbstractC1572h5 r35, p152w2.AbstractC1669p6<?, ?> r36, p152w2.AbstractC1535e4<?> r37, p152w2.C1668p5 r38) {
        /*
            Method dump skipped, instructions count: 1037
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1752w5.m4360B(w2.d6, w2.y5, w2.h5, w2.p6, w2.e4, w2.p5):w2.w5");
    }

    /* renamed from: C */
    public static Field m4361C(Class<?> cls, String str) {
        try {
            return cls.getDeclaredField(str);
        } catch (NoSuchFieldException unused) {
            Field[] declaredFields = cls.getDeclaredFields();
            for (Field field : declaredFields) {
                if (str.equals(field.getName())) {
                    return field;
                }
            }
            String name = cls.getName();
            String arrays = Arrays.toString(declaredFields);
            StringBuilder sb = new StringBuilder(String.valueOf(str).length() + 40 + name.length() + String.valueOf(arrays).length());
            sb.append("Field ");
            sb.append(str);
            sb.append(" for ");
            sb.append(name);
            throw new RuntimeException(C1094g.m2839c(sb, " not found. Known fields are ", arrays));
        }
    }

    /* renamed from: l */
    public static <T> double m4362l(T t, long j6) {
        return ((Double) C1789z6.m4498p(t, j6)).doubleValue();
    }

    /* renamed from: m */
    public static <T> float m4363m(T t, long j6) {
        return ((Float) C1789z6.m4498p(t, j6)).floatValue();
    }

    /* renamed from: n */
    public static <T> int m4364n(T t, long j6) {
        return ((Integer) C1789z6.m4498p(t, j6)).intValue();
    }

    /* renamed from: o */
    public static <T> long m4365o(T t, long j6) {
        return ((Long) C1789z6.m4498p(t, j6)).longValue();
    }

    /* renamed from: p */
    public static <T> boolean m4366p(T t, long j6) {
        return ((Boolean) C1789z6.m4498p(t, j6)).booleanValue();
    }

    /* renamed from: x */
    public static final void m4367x(int i6, Object obj, C1484a4 c1484a4) {
        if (!(obj instanceof String)) {
            c1484a4.m3635l(i6, (AbstractC1750w3) obj);
        } else {
            c1484a4.f6873a.mo4412S(i6, (String) obj);
        }
    }

    /* renamed from: y */
    public static C1681q6 m4368y(Object obj) {
        AbstractC1691r4 abstractC1691r4 = (AbstractC1691r4) obj;
        C1681q6 c1681q6 = abstractC1691r4.zzc;
        if (c1681q6 != C1681q6.f7150f) {
            return c1681q6;
        }
        C1681q6 m4027a = C1681q6.m4027a();
        abstractC1691r4.zzc = m4027a;
        return m4027a;
    }

    /* renamed from: D */
    public final void m4369D(T t, T t6, int i6) {
        long m4380j = m4380j(i6) & 1048575;
        if (m4383r(t6, i6)) {
            Object m4498p = C1789z6.m4498p(t, m4380j);
            Object m4498p2 = C1789z6.m4498p(t6, m4380j);
            if (m4498p != null && m4498p2 != null) {
                m4498p2 = C1775y4.m4463c(m4498p, m4498p2);
            } else if (m4498p2 == null) {
                return;
            }
            C1789z6.m4499q(t, m4380j, m4498p2);
            m4384s(t, i6);
        }
    }

    /* renamed from: E */
    public final void m4370E(T t, T t6, int i6) {
        int m4380j = m4380j(i6);
        int i7 = this.f7248a[i6];
        long j6 = m4380j & 1048575;
        if (m4385t(t6, i7, i6)) {
            Object m4498p = m4385t(t, i7, i6) ? C1789z6.m4498p(t, j6) : null;
            Object m4498p2 = C1789z6.m4498p(t6, j6);
            if (m4498p != null && m4498p2 != null) {
                m4498p2 = C1775y4.m4463c(m4498p, m4498p2);
            } else if (m4498p2 == null) {
                return;
            }
            C1789z6.m4499q(t, j6, m4498p2);
            m4386u(t, i7, i6);
        }
    }

    /* renamed from: F */
    public final int m4371F(T t) {
        int i6;
        int m4480c0;
        int m4481d0;
        int m4480c02;
        Object object;
        int m4480c03;
        int m4482e0;
        int m3793H;
        int i7;
        int m4480c04;
        int m4480c05;
        int m4480c06;
        int m4481d02;
        int size;
        int m3819w;
        int m4478a0;
        int m4480c07;
        int i8;
        Object object2;
        int m4364n;
        Unsafe unsafe = f7247o;
        int i9 = 1048575;
        int i10 = 1048575;
        int i11 = 0;
        int i12 = 0;
        int i13 = 0;
        while (i11 < this.f7248a.length) {
            int m4380j = m4380j(i11);
            int[] iArr = this.f7248a;
            int i14 = iArr[i11];
            int i15 = (m4380j >>> 20) & 255;
            if (i15 <= 17) {
                int i16 = iArr[i11 + 2];
                int i17 = i16 & i9;
                i6 = 1 << (i16 >>> 20);
                if (i17 != i10) {
                    i13 = unsafe.getInt(t, i17);
                    i10 = i17;
                }
            } else {
                i6 = 0;
            }
            long j6 = m4380j & i9;
            switch (i15) {
                case 0:
                    if ((i13 & i6) == 0) {
                        break;
                    }
                    m4480c05 = AbstractC1786z3.m4480c0(i14 << 3);
                    m3793H = m4480c05 + 8;
                    i12 += m3793H;
                    break;
                case 1:
                    if ((i13 & i6) == 0) {
                        break;
                    }
                    m4480c04 = AbstractC1786z3.m4480c0(i14 << 3);
                    m3793H = m4480c04 + 4;
                    i12 += m3793H;
                    break;
                case 2:
                    if ((i13 & i6) == 0) {
                        break;
                    }
                    long j7 = unsafe.getLong(t, j6);
                    m4480c0 = AbstractC1786z3.m4480c0(i14 << 3);
                    m4481d0 = AbstractC1786z3.m4481d0(j7);
                    m3793H = m4481d0 + m4480c0;
                    i12 += m3793H;
                    break;
                case 3:
                    if ((i13 & i6) == 0) {
                        break;
                    }
                    long j72 = unsafe.getLong(t, j6);
                    m4480c0 = AbstractC1786z3.m4480c0(i14 << 3);
                    m4481d0 = AbstractC1786z3.m4481d0(j72);
                    m3793H = m4481d0 + m4480c0;
                    i12 += m3793H;
                    break;
                case 4:
                    if ((i13 & i6) == 0) {
                        break;
                    }
                    int i18 = unsafe.getInt(t, j6);
                    m4480c03 = AbstractC1786z3.m4480c0(i14 << 3);
                    m4482e0 = AbstractC1786z3.m4479b0(i18);
                    m3793H = m4482e0 + m4480c03;
                    i12 += m3793H;
                    break;
                case 5:
                    if ((i13 & i6) == 0) {
                        break;
                    }
                    m4480c05 = AbstractC1786z3.m4480c0(i14 << 3);
                    m3793H = m4480c05 + 8;
                    i12 += m3793H;
                    break;
                case 6:
                    if ((i13 & i6) == 0) {
                        break;
                    }
                    m4480c04 = AbstractC1786z3.m4480c0(i14 << 3);
                    m3793H = m4480c04 + 4;
                    i12 += m3793H;
                    break;
                case 7:
                    if ((i13 & i6) == 0) {
                        break;
                    } else {
                        m4480c02 = AbstractC1786z3.m4480c0(i14 << 3);
                        m3793H = m4480c02 + 1;
                        i12 += m3793H;
                        break;
                    }
                case 8:
                    if ((i13 & i6) == 0) {
                        break;
                    } else {
                        object = unsafe.getObject(t, j6);
                        if (!(object instanceof AbstractC1750w3)) {
                            m4480c03 = AbstractC1786z3.m4480c0(i14 << 3);
                            m4482e0 = AbstractC1786z3.m4482e0((String) object);
                            m3793H = m4482e0 + m4480c03;
                            i12 += m3793H;
                            break;
                        }
                        int m4480c08 = AbstractC1786z3.m4480c0(i14 << 3);
                        int mo4287g = ((AbstractC1750w3) object).mo4287g();
                        i12 += AbstractC1786z3.m4480c0(mo4287g) + mo4287g + m4480c08;
                        break;
                    }
                case 9:
                    if ((i13 & i6) == 0) {
                        break;
                    } else {
                        m3793H = C1549f6.m3793H(i14, unsafe.getObject(t, j6), m4376K(i11));
                        i12 += m3793H;
                        break;
                    }
                case 10:
                    if ((i13 & i6) == 0) {
                        break;
                    } else {
                        object = unsafe.getObject(t, j6);
                        int m4480c082 = AbstractC1786z3.m4480c0(i14 << 3);
                        int mo4287g2 = ((AbstractC1750w3) object).mo4287g();
                        i12 += AbstractC1786z3.m4480c0(mo4287g2) + mo4287g2 + m4480c082;
                        break;
                    }
                case 11:
                    if ((i13 & i6) == 0) {
                        break;
                    } else {
                        i7 = unsafe.getInt(t, j6);
                        m4480c03 = AbstractC1786z3.m4480c0(i14 << 3);
                        m4482e0 = AbstractC1786z3.m4480c0(i7);
                        m3793H = m4482e0 + m4480c03;
                        i12 += m3793H;
                        break;
                    }
                case 12:
                    if ((i13 & i6) == 0) {
                        break;
                    }
                    int i182 = unsafe.getInt(t, j6);
                    m4480c03 = AbstractC1786z3.m4480c0(i14 << 3);
                    m4482e0 = AbstractC1786z3.m4479b0(i182);
                    m3793H = m4482e0 + m4480c03;
                    i12 += m3793H;
                    break;
                case 13:
                    if ((i13 & i6) == 0) {
                        break;
                    }
                    m4480c04 = AbstractC1786z3.m4480c0(i14 << 3);
                    m3793H = m4480c04 + 4;
                    i12 += m3793H;
                    break;
                case 14:
                    if ((i13 & i6) == 0) {
                        break;
                    }
                    m4480c05 = AbstractC1786z3.m4480c0(i14 << 3);
                    m3793H = m4480c05 + 8;
                    i12 += m3793H;
                    break;
                case 15:
                    if ((i13 & i6) == 0) {
                        break;
                    } else {
                        int i19 = unsafe.getInt(t, j6);
                        m4480c03 = AbstractC1786z3.m4480c0(i14 << 3);
                        i7 = (i19 >> 31) ^ (i19 + i19);
                        m4482e0 = AbstractC1786z3.m4480c0(i7);
                        m3793H = m4482e0 + m4480c03;
                        i12 += m3793H;
                        break;
                    }
                case 16:
                    if ((i6 & i13) == 0) {
                        break;
                    } else {
                        long j8 = unsafe.getLong(t, j6);
                        m4480c06 = AbstractC1786z3.m4480c0(i14 << 3);
                        m4481d02 = AbstractC1786z3.m4481d0((j8 >> 63) ^ (j8 + j8));
                        m3793H = m4481d02 + m4480c06;
                        i12 += m3793H;
                        break;
                    }
                case 17:
                    if ((i13 & i6) == 0) {
                        break;
                    } else {
                        m3793H = AbstractC1786z3.m4477K(i14, (InterfaceC1716t5) unsafe.getObject(t, j6), m4376K(i11));
                        i12 += m3793H;
                        break;
                    }
                case 18:
                case 23:
                case 32:
                    m3793H = C1549f6.m3791F(i14, (List) unsafe.getObject(t, j6));
                    i12 += m3793H;
                    break;
                case 19:
                case 24:
                case 31:
                    m3793H = C1549f6.m3789D(i14, (List) unsafe.getObject(t, j6));
                    i12 += m3793H;
                    break;
                case 20:
                    m3793H = C1549f6.m3818v(i14, (List) unsafe.getObject(t, j6));
                    i12 += m3793H;
                    break;
                case 21:
                    List list = (List) unsafe.getObject(t, j6);
                    Class<?> cls = C1549f6.f6970a;
                    size = list.size();
                    if (size != 0) {
                        m3819w = C1549f6.m3819w(list);
                        m4478a0 = AbstractC1786z3.m4478a0(i14);
                        i8 = m4478a0 * size;
                        m4480c07 = i8 + m3819w;
                        i12 += m4480c07;
                        break;
                    }
                    m4480c07 = 0;
                    i12 += m4480c07;
                case 22:
                    List list2 = (List) unsafe.getObject(t, j6);
                    Class<?> cls2 = C1549f6.f6970a;
                    size = list2.size();
                    if (size != 0) {
                        m3819w = C1549f6.m3822z(list2);
                        m4478a0 = AbstractC1786z3.m4478a0(i14);
                        i8 = m4478a0 * size;
                        m4480c07 = i8 + m3819w;
                        i12 += m4480c07;
                        break;
                    }
                    m4480c07 = 0;
                    i12 += m4480c07;
                case 25:
                    List list3 = (List) unsafe.getObject(t, j6);
                    Class<?> cls3 = C1549f6.f6970a;
                    int size2 = list3.size();
                    if (size2 != 0) {
                        m4480c07 = (AbstractC1786z3.m4480c0(i14 << 3) + 1) * size2;
                        i12 += m4480c07;
                        break;
                    }
                    m4480c07 = 0;
                    i12 += m4480c07;
                case 26:
                    m3793H = C1549f6.m3792G(i14, (List) unsafe.getObject(t, j6));
                    i12 += m3793H;
                    break;
                case 27:
                    m3793H = C1549f6.m3794I(i14, (List) unsafe.getObject(t, j6), m4376K(i11));
                    i12 += m3793H;
                    break;
                case 28:
                    m3793H = C1549f6.m3795J(i14, (List) unsafe.getObject(t, j6));
                    i12 += m3793H;
                    break;
                case 29:
                    List list4 = (List) unsafe.getObject(t, j6);
                    Class<?> cls4 = C1549f6.f6970a;
                    size = list4.size();
                    if (size != 0) {
                        m3819w = C1549f6.m3786A(list4);
                        m4478a0 = AbstractC1786z3.m4478a0(i14);
                        i8 = m4478a0 * size;
                        m4480c07 = i8 + m3819w;
                        i12 += m4480c07;
                        break;
                    }
                    m4480c07 = 0;
                    i12 += m4480c07;
                case 30:
                    List list5 = (List) unsafe.getObject(t, j6);
                    Class<?> cls5 = C1549f6.f6970a;
                    size = list5.size();
                    if (size != 0) {
                        m3819w = C1549f6.m3821y(list5);
                        m4478a0 = AbstractC1786z3.m4478a0(i14);
                        i8 = m4478a0 * size;
                        m4480c07 = i8 + m3819w;
                        i12 += m4480c07;
                        break;
                    }
                    m4480c07 = 0;
                    i12 += m4480c07;
                case 33:
                    List list6 = (List) unsafe.getObject(t, j6);
                    Class<?> cls6 = C1549f6.f6970a;
                    size = list6.size();
                    if (size != 0) {
                        m3819w = C1549f6.m3787B(list6);
                        m4478a0 = AbstractC1786z3.m4478a0(i14);
                        i8 = m4478a0 * size;
                        m4480c07 = i8 + m3819w;
                        i12 += m4480c07;
                        break;
                    }
                    m4480c07 = 0;
                    i12 += m4480c07;
                case 34:
                    List list7 = (List) unsafe.getObject(t, j6);
                    Class<?> cls7 = C1549f6.f6970a;
                    size = list7.size();
                    if (size != 0) {
                        m3819w = C1549f6.m3820x(list7);
                        m4478a0 = AbstractC1786z3.m4478a0(i14);
                        i8 = m4478a0 * size;
                        m4480c07 = i8 + m3819w;
                        i12 += m4480c07;
                        break;
                    }
                    m4480c07 = 0;
                    i12 += m4480c07;
                case 35:
                    m3819w = C1549f6.m3790E((List) unsafe.getObject(t, j6));
                    if (m3819w <= 0) {
                        break;
                    }
                    i8 = AbstractC1786z3.m4480c0(m3819w) + AbstractC1786z3.m4478a0(i14);
                    m4480c07 = i8 + m3819w;
                    i12 += m4480c07;
                    break;
                case 36:
                    m3819w = C1549f6.m3788C((List) unsafe.getObject(t, j6));
                    if (m3819w <= 0) {
                        break;
                    }
                    i8 = AbstractC1786z3.m4480c0(m3819w) + AbstractC1786z3.m4478a0(i14);
                    m4480c07 = i8 + m3819w;
                    i12 += m4480c07;
                    break;
                case 37:
                    m3819w = C1549f6.m3817u((List) unsafe.getObject(t, j6));
                    if (m3819w <= 0) {
                        break;
                    }
                    i8 = AbstractC1786z3.m4480c0(m3819w) + AbstractC1786z3.m4478a0(i14);
                    m4480c07 = i8 + m3819w;
                    i12 += m4480c07;
                    break;
                case 38:
                    m3819w = C1549f6.m3819w((List) unsafe.getObject(t, j6));
                    if (m3819w <= 0) {
                        break;
                    }
                    i8 = AbstractC1786z3.m4480c0(m3819w) + AbstractC1786z3.m4478a0(i14);
                    m4480c07 = i8 + m3819w;
                    i12 += m4480c07;
                    break;
                case 39:
                    m3819w = C1549f6.m3822z((List) unsafe.getObject(t, j6));
                    if (m3819w <= 0) {
                        break;
                    }
                    i8 = AbstractC1786z3.m4480c0(m3819w) + AbstractC1786z3.m4478a0(i14);
                    m4480c07 = i8 + m3819w;
                    i12 += m4480c07;
                    break;
                case 40:
                    m3819w = C1549f6.m3790E((List) unsafe.getObject(t, j6));
                    if (m3819w <= 0) {
                        break;
                    }
                    i8 = AbstractC1786z3.m4480c0(m3819w) + AbstractC1786z3.m4478a0(i14);
                    m4480c07 = i8 + m3819w;
                    i12 += m4480c07;
                    break;
                case 41:
                    m3819w = C1549f6.m3788C((List) unsafe.getObject(t, j6));
                    if (m3819w <= 0) {
                        break;
                    }
                    i8 = AbstractC1786z3.m4480c0(m3819w) + AbstractC1786z3.m4478a0(i14);
                    m4480c07 = i8 + m3819w;
                    i12 += m4480c07;
                    break;
                case 42:
                    List list8 = (List) unsafe.getObject(t, j6);
                    Class<?> cls8 = C1549f6.f6970a;
                    m3819w = list8.size();
                    if (m3819w <= 0) {
                        break;
                    }
                    i8 = AbstractC1786z3.m4480c0(m3819w) + AbstractC1786z3.m4478a0(i14);
                    m4480c07 = i8 + m3819w;
                    i12 += m4480c07;
                    break;
                case 43:
                    m3819w = C1549f6.m3786A((List) unsafe.getObject(t, j6));
                    if (m3819w <= 0) {
                        break;
                    }
                    i8 = AbstractC1786z3.m4480c0(m3819w) + AbstractC1786z3.m4478a0(i14);
                    m4480c07 = i8 + m3819w;
                    i12 += m4480c07;
                    break;
                case 44:
                    m3819w = C1549f6.m3821y((List) unsafe.getObject(t, j6));
                    if (m3819w <= 0) {
                        break;
                    }
                    i8 = AbstractC1786z3.m4480c0(m3819w) + AbstractC1786z3.m4478a0(i14);
                    m4480c07 = i8 + m3819w;
                    i12 += m4480c07;
                    break;
                case 45:
                    m3819w = C1549f6.m3788C((List) unsafe.getObject(t, j6));
                    if (m3819w <= 0) {
                        break;
                    }
                    i8 = AbstractC1786z3.m4480c0(m3819w) + AbstractC1786z3.m4478a0(i14);
                    m4480c07 = i8 + m3819w;
                    i12 += m4480c07;
                    break;
                case 46:
                    m3819w = C1549f6.m3790E((List) unsafe.getObject(t, j6));
                    if (m3819w <= 0) {
                        break;
                    }
                    i8 = AbstractC1786z3.m4480c0(m3819w) + AbstractC1786z3.m4478a0(i14);
                    m4480c07 = i8 + m3819w;
                    i12 += m4480c07;
                    break;
                case 47:
                    m3819w = C1549f6.m3787B((List) unsafe.getObject(t, j6));
                    if (m3819w <= 0) {
                        break;
                    }
                    i8 = AbstractC1786z3.m4480c0(m3819w) + AbstractC1786z3.m4478a0(i14);
                    m4480c07 = i8 + m3819w;
                    i12 += m4480c07;
                    break;
                case 48:
                    m3819w = C1549f6.m3820x((List) unsafe.getObject(t, j6));
                    if (m3819w <= 0) {
                        break;
                    }
                    i8 = AbstractC1786z3.m4480c0(m3819w) + AbstractC1786z3.m4478a0(i14);
                    m4480c07 = i8 + m3819w;
                    i12 += m4480c07;
                    break;
                case 49:
                    m3793H = C1549f6.m3796K(i14, (List) unsafe.getObject(t, j6), m4376K(i11));
                    i12 += m3793H;
                    break;
                case 50:
                    C1668p5.m4009a(unsafe.getObject(t, j6), m4377L(i11));
                    break;
                case 51:
                    if (!m4385t(t, i14, i11)) {
                        break;
                    }
                    m4480c05 = AbstractC1786z3.m4480c0(i14 << 3);
                    m3793H = m4480c05 + 8;
                    i12 += m3793H;
                    break;
                case 52:
                    if (!m4385t(t, i14, i11)) {
                        break;
                    }
                    m4480c04 = AbstractC1786z3.m4480c0(i14 << 3);
                    m3793H = m4480c04 + 4;
                    i12 += m3793H;
                    break;
                case 53:
                    if (!m4385t(t, i14, i11)) {
                        break;
                    }
                    long m4365o = m4365o(t, j6);
                    m4480c0 = AbstractC1786z3.m4480c0(i14 << 3);
                    m4481d0 = AbstractC1786z3.m4481d0(m4365o);
                    m3793H = m4481d0 + m4480c0;
                    i12 += m3793H;
                    break;
                case 54:
                    if (!m4385t(t, i14, i11)) {
                        break;
                    }
                    long m4365o2 = m4365o(t, j6);
                    m4480c0 = AbstractC1786z3.m4480c0(i14 << 3);
                    m4481d0 = AbstractC1786z3.m4481d0(m4365o2);
                    m3793H = m4481d0 + m4480c0;
                    i12 += m3793H;
                    break;
                case 55:
                    if (!m4385t(t, i14, i11)) {
                        break;
                    }
                    int m4364n2 = m4364n(t, j6);
                    m4480c03 = AbstractC1786z3.m4480c0(i14 << 3);
                    m4482e0 = AbstractC1786z3.m4479b0(m4364n2);
                    m3793H = m4482e0 + m4480c03;
                    i12 += m3793H;
                    break;
                case 56:
                    if (!m4385t(t, i14, i11)) {
                        break;
                    }
                    m4480c05 = AbstractC1786z3.m4480c0(i14 << 3);
                    m3793H = m4480c05 + 8;
                    i12 += m3793H;
                    break;
                case 57:
                    if (!m4385t(t, i14, i11)) {
                        break;
                    }
                    m4480c04 = AbstractC1786z3.m4480c0(i14 << 3);
                    m3793H = m4480c04 + 4;
                    i12 += m3793H;
                    break;
                case 58:
                    if (!m4385t(t, i14, i11)) {
                        break;
                    } else {
                        m4480c02 = AbstractC1786z3.m4480c0(i14 << 3);
                        m3793H = m4480c02 + 1;
                        i12 += m3793H;
                        break;
                    }
                case 59:
                    if (!m4385t(t, i14, i11)) {
                        break;
                    } else {
                        object2 = unsafe.getObject(t, j6);
                        if (!(object2 instanceof AbstractC1750w3)) {
                            m4480c03 = AbstractC1786z3.m4480c0(i14 << 3);
                            m4482e0 = AbstractC1786z3.m4482e0((String) object2);
                            m3793H = m4482e0 + m4480c03;
                            i12 += m3793H;
                            break;
                        }
                        int m4480c09 = AbstractC1786z3.m4480c0(i14 << 3);
                        int mo4287g3 = ((AbstractC1750w3) object2).mo4287g();
                        m4480c07 = AbstractC1786z3.m4480c0(mo4287g3) + mo4287g3 + m4480c09;
                        i12 += m4480c07;
                        break;
                    }
                case 60:
                    if (!m4385t(t, i14, i11)) {
                        break;
                    } else {
                        m3793H = C1549f6.m3793H(i14, unsafe.getObject(t, j6), m4376K(i11));
                        i12 += m3793H;
                        break;
                    }
                case 61:
                    if (!m4385t(t, i14, i11)) {
                        break;
                    } else {
                        object2 = unsafe.getObject(t, j6);
                        int m4480c092 = AbstractC1786z3.m4480c0(i14 << 3);
                        int mo4287g32 = ((AbstractC1750w3) object2).mo4287g();
                        m4480c07 = AbstractC1786z3.m4480c0(mo4287g32) + mo4287g32 + m4480c092;
                        i12 += m4480c07;
                        break;
                    }
                case 62:
                    if (!m4385t(t, i14, i11)) {
                        break;
                    } else {
                        m4364n = m4364n(t, j6);
                        m4480c03 = AbstractC1786z3.m4480c0(i14 << 3);
                        m4482e0 = AbstractC1786z3.m4480c0(m4364n);
                        m3793H = m4482e0 + m4480c03;
                        i12 += m3793H;
                        break;
                    }
                case 63:
                    if (!m4385t(t, i14, i11)) {
                        break;
                    }
                    int m4364n22 = m4364n(t, j6);
                    m4480c03 = AbstractC1786z3.m4480c0(i14 << 3);
                    m4482e0 = AbstractC1786z3.m4479b0(m4364n22);
                    m3793H = m4482e0 + m4480c03;
                    i12 += m3793H;
                    break;
                case 64:
                    if (!m4385t(t, i14, i11)) {
                        break;
                    }
                    m4480c04 = AbstractC1786z3.m4480c0(i14 << 3);
                    m3793H = m4480c04 + 4;
                    i12 += m3793H;
                    break;
                case 65:
                    if (!m4385t(t, i14, i11)) {
                        break;
                    }
                    m4480c05 = AbstractC1786z3.m4480c0(i14 << 3);
                    m3793H = m4480c05 + 8;
                    i12 += m3793H;
                    break;
                case 66:
                    if (!m4385t(t, i14, i11)) {
                        break;
                    } else {
                        int m4364n3 = m4364n(t, j6);
                        m4480c03 = AbstractC1786z3.m4480c0(i14 << 3);
                        m4364n = (m4364n3 >> 31) ^ (m4364n3 + m4364n3);
                        m4482e0 = AbstractC1786z3.m4480c0(m4364n);
                        m3793H = m4482e0 + m4480c03;
                        i12 += m3793H;
                        break;
                    }
                case 67:
                    if (!m4385t(t, i14, i11)) {
                        break;
                    } else {
                        long m4365o3 = m4365o(t, j6);
                        m4480c06 = AbstractC1786z3.m4480c0(i14 << 3);
                        m4481d02 = AbstractC1786z3.m4481d0((m4365o3 >> 63) ^ (m4365o3 + m4365o3));
                        m3793H = m4481d02 + m4480c06;
                        i12 += m3793H;
                        break;
                    }
                case 68:
                    if (!m4385t(t, i14, i11)) {
                        break;
                    } else {
                        m3793H = AbstractC1786z3.m4477K(i14, (InterfaceC1716t5) unsafe.getObject(t, j6), m4376K(i11));
                        i12 += m3793H;
                        break;
                    }
            }
            i11 += 3;
            i9 = 1048575;
        }
        AbstractC1669p6<?, ?> abstractC1669p6 = this.f7259l;
        int mo4018h = abstractC1669p6.mo4018h(abstractC1669p6.mo4014d(t)) + i12;
        if (!this.f7253f) {
            return mo4018h;
        }
        this.f7260m.mo3746b(t);
        throw null;
    }

    /* JADX WARN: Code restructure failed: missing block: B:205:0x0367, code lost:
    
        if ((r4 instanceof p152w2.AbstractC1750w3) != false) goto L186;
     */
    /* JADX WARN: Code restructure failed: missing block: B:62:0x0091, code lost:
    
        if ((r4 instanceof p152w2.AbstractC1750w3) != false) goto L186;
     */
    /* JADX WARN: Code restructure failed: missing block: B:63:0x037d, code lost:
    
        r5 = p152w2.AbstractC1786z3.m4480c0(r7 << 3);
        r4 = p152w2.AbstractC1786z3.m4482e0((java.lang.String) r4);
     */
    /* renamed from: G */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final int m4372G(T r11) {
        /*
            Method dump skipped, instructions count: 1174
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1752w5.m4372G(java.lang.Object):int");
    }

    /* JADX WARN: Code restructure failed: missing block: B:112:0x0186, code lost:
    
        if (r4 == 0) goto L99;
     */
    /* JADX WARN: Code restructure failed: missing block: B:113:0x0189, code lost:
    
        r13.add(p152w2.AbstractC1750w3.m4357p(r19, r1, r4));
        r1 = r1 + r4;
     */
    /* JADX WARN: Code restructure failed: missing block: B:115:0x0191, code lost:
    
        if (r1 >= r21) goto L308;
     */
    /* JADX WARN: Code restructure failed: missing block: B:116:0x0193, code lost:
    
        r4 = p153w3.C1798e.m4527T(r19, r1, r31);
     */
    /* JADX WARN: Code restructure failed: missing block: B:117:0x0199, code lost:
    
        if (r22 == r31.f7144a) goto L94;
     */
    /* JADX WARN: Code restructure failed: missing block: B:118:0x019c, code lost:
    
        r1 = p153w3.C1798e.m4527T(r19, r4, r31);
        r4 = r31.f7144a;
     */
    /* JADX WARN: Code restructure failed: missing block: B:119:0x01a2, code lost:
    
        if (r4 < 0) goto L309;
     */
    /* JADX WARN: Code restructure failed: missing block: B:121:0x01a6, code lost:
    
        if (r4 > (r19.length - r1)) goto L310;
     */
    /* JADX WARN: Code restructure failed: missing block: B:122:0x01a8, code lost:
    
        if (r4 != 0) goto L311;
     */
    /* JADX WARN: Code restructure failed: missing block: B:123:0x01aa, code lost:
    
        r13.add(p152w2.AbstractC1750w3.f7244k);
     */
    /* JADX WARN: Code restructure failed: missing block: B:127:0x01b4, code lost:
    
        throw p152w2.C1485a5.m3641a();
     */
    /* JADX WARN: Code restructure failed: missing block: B:130:0x01b9, code lost:
    
        throw p152w2.C1485a5.m3642b();
     */
    /* JADX WARN: Code restructure failed: missing block: B:132:0x01ba, code lost:
    
        return r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:134:0x0191, code lost:
    
        r13.add(p152w2.AbstractC1750w3.f7244k);
     */
    /* JADX WARN: Code restructure failed: missing block: B:167:0x0203, code lost:
    
        r13.add("");
     */
    /* JADX WARN: Code restructure failed: missing block: B:222:0x02be, code lost:
    
        if (r31.f7145b != 0) goto L186;
     */
    /* JADX WARN: Code restructure failed: missing block: B:223:0x02c1, code lost:
    
        r4 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:225:0x02c2, code lost:
    
        r13.m4054d(r4);
     */
    /* JADX WARN: Code restructure failed: missing block: B:226:0x02c5, code lost:
    
        if (r1 >= r21) goto L328;
     */
    /* JADX WARN: Code restructure failed: missing block: B:227:0x02c7, code lost:
    
        r4 = p153w3.C1798e.m4527T(r19, r1, r31);
     */
    /* JADX WARN: Code restructure failed: missing block: B:228:0x02cd, code lost:
    
        if (r22 == r31.f7144a) goto L184;
     */
    /* JADX WARN: Code restructure failed: missing block: B:229:0x02d0, code lost:
    
        r1 = p153w3.C1798e.m4541h0(r19, r4, r31);
     */
    /* JADX WARN: Code restructure failed: missing block: B:230:0x02d8, code lost:
    
        if (r31.f7145b == 0) goto L329;
     */
    /* JADX WARN: Code restructure failed: missing block: B:231:0x02da, code lost:
    
        r4 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:234:0x02dc, code lost:
    
        return r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:236:0x02c2, code lost:
    
        r4 = true;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:152:0x0205  */
    /* JADX WARN: Removed duplicated region for block: B:177:0x024d  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:100:0x01a8 -> B:92:0x0189). Please report as a decompilation issue!!! */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:128:0x021b -> B:121:0x01ff). Please report as a decompilation issue!!! */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:144:0x025d -> B:137:0x0237). Please report as a decompilation issue!!! */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:181:0x02d8 -> B:175:0x02c1). Please report as a decompilation issue!!! */
    /* renamed from: H */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final int m4373H(T r18, byte[] r19, int r20, int r21, int r22, int r23, int r24, int r25, long r26, int r28, long r29, p152w2.C1678q3 r31) {
        /*
            Method dump skipped, instructions count: 1194
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1752w5.m4373H(java.lang.Object, byte[], int, int, int, int, int, int, long, int, long, w2.q3):int");
    }

    /* renamed from: I */
    public final int m4374I(Object obj, int i6, long j6) {
        Unsafe unsafe = f7247o;
        Object m4377L = m4377L(i6);
        Object object = unsafe.getObject(obj, j6);
        if (!((C1656o5) object).f7122j) {
            C1656o5 c1656o5 = C1656o5.f7121k;
            C1656o5 c1656o52 = c1656o5.isEmpty() ? new C1656o5() : new C1656o5(c1656o5);
            C1668p5.m4010b(c1656o52, object);
            unsafe.putObject(obj, j6, c1656o52);
        }
        throw null;
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Code restructure failed: missing block: B:10:0x00ca, code lost:
    
        r3 = p152w2.C1775y4.m4463c(r15, r3);
     */
    /* JADX WARN: Code restructure failed: missing block: B:40:0x00c7, code lost:
    
        if (r15 == null) goto L45;
     */
    /* JADX WARN: Code restructure failed: missing block: B:9:0x004e, code lost:
    
        if (r15 == null) goto L45;
     */
    /* renamed from: J */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final int m4375J(T r17, byte[] r18, int r19, int r20, int r21, int r22, int r23, int r24, int r25, long r26, int r28, p152w2.C1678q3 r29) {
        /*
            Method dump skipped, instructions count: 448
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1752w5.m4375J(java.lang.Object, byte[], int, int, int, int, int, int, int, long, int, w2.q3):int");
    }

    /* renamed from: K */
    public final InterfaceC1537e6 m4376K(int i6) {
        int i7 = i6 / 3;
        int i8 = i7 + i7;
        Object[] objArr = this.f7249b;
        InterfaceC1537e6 interfaceC1537e6 = (InterfaceC1537e6) objArr[i8];
        if (interfaceC1537e6 != null) {
            return interfaceC1537e6;
        }
        InterfaceC1537e6<T> m3663a = C1499b6.f6896c.m3663a((Class) objArr[i8 + 1]);
        this.f7249b[i8] = m3663a;
        return m3663a;
    }

    /* renamed from: L */
    public final Object m4377L(int i6) {
        int i7 = i6 / 3;
        return this.f7249b[i7 + i7];
    }

    /* renamed from: M */
    public final InterfaceC1727u4 m4378M(int i6) {
        int i7 = i6 / 3;
        return (InterfaceC1727u4) this.f7249b[i7 + i7 + 1];
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:25:0x009f. Please report as an issue. */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r3v21, types: [int] */
    /* renamed from: N */
    public final int m4379N(T t, byte[] bArr, int i6, int i7, C1678q3 c1678q3) {
        byte b6;
        int i8;
        int m4387v;
        int i9;
        int i10;
        int i11;
        int i12;
        int i13;
        Unsafe unsafe;
        int i14;
        T t6;
        int i15;
        int i16;
        int i17;
        int i18;
        int i19;
        int i20;
        int i21;
        int i22;
        int i23;
        int i24;
        int m4557p0;
        int i25;
        int i26;
        int i27;
        int i28;
        int i29;
        int i30;
        int i31;
        int i32;
        int i33;
        int m4541h0;
        int i34;
        int i35;
        int i36;
        Object obj;
        C1752w5<T> c1752w5 = this;
        T t7 = t;
        byte[] bArr2 = bArr;
        int i37 = i7;
        C1678q3 c1678q32 = c1678q3;
        Unsafe unsafe2 = f7247o;
        int i38 = -1;
        int i39 = 0;
        int i40 = 1048575;
        int i41 = i6;
        int i42 = 1048575;
        int i43 = 0;
        int i44 = 0;
        int i45 = -1;
        while (i41 < i37) {
            int i46 = i41 + 1;
            byte b7 = bArr2[i41];
            if (b7 < 0) {
                i8 = C1798e.m4535b0(b7, bArr2, i46, c1678q32);
                b6 = c1678q32.f7144a;
            } else {
                b6 = b7;
                i8 = i46;
            }
            int i47 = b6 >>> 3;
            int i48 = b6 & 7;
            if (i47 > i45) {
                int i49 = i43 / 3;
                if (i47 >= c1752w5.f7250c && i47 <= c1752w5.f7251d) {
                    m4387v = c1752w5.m4387v(i47, i49);
                    i9 = m4387v;
                }
                i9 = i38;
            } else {
                if (i47 >= c1752w5.f7250c && i47 <= c1752w5.f7251d) {
                    m4387v = c1752w5.m4387v(i47, i39);
                    i9 = m4387v;
                }
                i9 = i38;
            }
            if (i9 == i38) {
                i10 = i47;
                i11 = i8;
                i12 = i42;
                i13 = i39;
                unsafe = unsafe2;
                i14 = i38;
                t6 = t7;
            } else {
                int[] iArr = c1752w5.f7248a;
                int i50 = iArr[i9 + 1];
                int i51 = (i50 >>> 20) & 255;
                long j6 = i50 & i40;
                if (i51 <= 17) {
                    int i52 = iArr[i9 + 2];
                    int i53 = 1 << (i52 >>> 20);
                    int i54 = 1048575;
                    int i55 = i52 & 1048575;
                    int i56 = i8;
                    if (i55 != i42) {
                        if (i42 != 1048575) {
                            unsafe2.putInt(t7, i42, i44);
                            i54 = 1048575;
                        }
                        if (i55 != i54) {
                            i44 = unsafe2.getInt(t7, i55);
                        }
                        i22 = i44;
                        i23 = i55;
                    } else {
                        i22 = i44;
                        i23 = i42;
                    }
                    switch (i51) {
                        case 0:
                            i29 = i7;
                            i28 = i9;
                            i30 = i54;
                            i27 = i56;
                            i10 = i47;
                            if (i48 != 1) {
                                i11 = i27;
                                i39 = i28;
                                i12 = i23;
                                unsafe = unsafe2;
                                t6 = t7;
                                i44 = i22;
                                i13 = 0;
                                i14 = -1;
                                break;
                            } else {
                                C1789z6.m4497o(t7, j6, Double.longBitsToDouble(C1798e.m4549l0(bArr2, i27)));
                                i41 = i27 + 8;
                                i31 = i22 | i53;
                                i43 = i28;
                                i42 = i23;
                                i37 = i29;
                                i40 = i30;
                                i39 = 0;
                                i38 = -1;
                                i44 = i31;
                                i45 = i10;
                                break;
                            }
                        case 1:
                            i29 = i7;
                            i28 = i9;
                            i30 = i54;
                            i32 = i56;
                            i10 = i47;
                            if (i48 != 5) {
                                i27 = i32;
                                i11 = i27;
                                i39 = i28;
                                i12 = i23;
                                unsafe = unsafe2;
                                t6 = t7;
                                i44 = i22;
                                i13 = 0;
                                i14 = -1;
                                break;
                            } else {
                                C1789z6.m4495m(t7, j6, Float.intBitsToFloat(C1798e.m4547k0(bArr2, i32)));
                                i33 = i32 + 4;
                                m4541h0 = i33;
                                i34 = i22 | i53;
                                i31 = i34;
                                i41 = m4541h0;
                                i43 = i28;
                                i42 = i23;
                                i37 = i29;
                                i40 = i30;
                                i39 = 0;
                                i38 = -1;
                                i44 = i31;
                                i45 = i10;
                                break;
                            }
                        case 2:
                        case 3:
                            i29 = i7;
                            i28 = i9;
                            i30 = i54;
                            i32 = i56;
                            i10 = i47;
                            if (i48 != 0) {
                                i27 = i32;
                                i11 = i27;
                                i39 = i28;
                                i12 = i23;
                                unsafe = unsafe2;
                                t6 = t7;
                                i44 = i22;
                                i13 = 0;
                                i14 = -1;
                                break;
                            } else {
                                m4541h0 = C1798e.m4541h0(bArr2, i32, c1678q32);
                                unsafe2.putLong(t, j6, c1678q32.f7145b);
                                i34 = i22 | i53;
                                i28 = i28;
                                i31 = i34;
                                i41 = m4541h0;
                                i43 = i28;
                                i42 = i23;
                                i37 = i29;
                                i40 = i30;
                                i39 = 0;
                                i38 = -1;
                                i44 = i31;
                                i45 = i10;
                                break;
                            }
                        case 4:
                        case 11:
                            i29 = i7;
                            i28 = i9;
                            i30 = i54;
                            i32 = i56;
                            i10 = i47;
                            if (i48 != 0) {
                                i27 = i32;
                                i11 = i27;
                                i39 = i28;
                                i12 = i23;
                                unsafe = unsafe2;
                                t6 = t7;
                                i44 = i22;
                                i13 = 0;
                                i14 = -1;
                                break;
                            } else {
                                i33 = C1798e.m4527T(bArr2, i32, c1678q32);
                                unsafe2.putInt(t7, j6, c1678q32.f7144a);
                                m4541h0 = i33;
                                i34 = i22 | i53;
                                i31 = i34;
                                i41 = m4541h0;
                                i43 = i28;
                                i42 = i23;
                                i37 = i29;
                                i40 = i30;
                                i39 = 0;
                                i38 = -1;
                                i44 = i31;
                                i45 = i10;
                                break;
                            }
                        case 5:
                        case 14:
                            i29 = i7;
                            i35 = i9;
                            i30 = i54;
                            i36 = i56;
                            i10 = i47;
                            if (i48 != 1) {
                                i32 = i36;
                                i28 = i35;
                                i27 = i32;
                                i11 = i27;
                                i39 = i28;
                                i12 = i23;
                                unsafe = unsafe2;
                                t6 = t7;
                                i44 = i22;
                                i13 = 0;
                                i14 = -1;
                                break;
                            } else {
                                unsafe2.putLong(t, j6, C1798e.m4549l0(bArr2, i36));
                                i33 = i36 + 8;
                                i28 = i35;
                                m4541h0 = i33;
                                i34 = i22 | i53;
                                i31 = i34;
                                i41 = m4541h0;
                                i43 = i28;
                                i42 = i23;
                                i37 = i29;
                                i40 = i30;
                                i39 = 0;
                                i38 = -1;
                                i44 = i31;
                                i45 = i10;
                                break;
                            }
                        case 6:
                        case 13:
                            i29 = i7;
                            i35 = i9;
                            i30 = i54;
                            i36 = i56;
                            i10 = i47;
                            if (i48 != 5) {
                                i32 = i36;
                                i28 = i35;
                                i27 = i32;
                                i11 = i27;
                                i39 = i28;
                                i12 = i23;
                                unsafe = unsafe2;
                                t6 = t7;
                                i44 = i22;
                                i13 = 0;
                                i14 = -1;
                                break;
                            } else {
                                unsafe2.putInt(t7, j6, C1798e.m4547k0(bArr2, i36));
                                i41 = i36 + 4;
                                i31 = i22 | i53;
                                i43 = i35;
                                i42 = i23;
                                i37 = i29;
                                i40 = i30;
                                i39 = 0;
                                i38 = -1;
                                i44 = i31;
                                i45 = i10;
                                break;
                            }
                        case 7:
                            i29 = i7;
                            i35 = i9;
                            i30 = i54;
                            i36 = i56;
                            i10 = i47;
                            if (i48 != 0) {
                                i32 = i36;
                                i28 = i35;
                                i27 = i32;
                                i11 = i27;
                                i39 = i28;
                                i12 = i23;
                                unsafe = unsafe2;
                                t6 = t7;
                                i44 = i22;
                                i13 = 0;
                                i14 = -1;
                                break;
                            } else {
                                i41 = C1798e.m4541h0(bArr2, i36, c1678q32);
                                C1789z6.m4493k(t7, j6, c1678q32.f7145b != 0);
                                i31 = i22 | i53;
                                i43 = i35;
                                i42 = i23;
                                i37 = i29;
                                i40 = i30;
                                i39 = 0;
                                i38 = -1;
                                i44 = i31;
                                i45 = i10;
                                break;
                            }
                        case 8:
                            i29 = i7;
                            i21 = i9;
                            i30 = i54;
                            i24 = i56;
                            i10 = i47;
                            if (i48 != 2) {
                                int i57 = i21;
                                i32 = i24;
                                i28 = i57;
                                i27 = i32;
                                i11 = i27;
                                i39 = i28;
                                i12 = i23;
                                unsafe = unsafe2;
                                t6 = t7;
                                i44 = i22;
                                i13 = 0;
                                i14 = -1;
                                break;
                            } else {
                                i33 = (i50 & 536870912) == 0 ? C1798e.m4553n0(bArr2, i24, c1678q32) : C1798e.m4555o0(bArr2, i24, c1678q32);
                                obj = c1678q32.f7146c;
                                unsafe2.putObject(t7, j6, obj);
                                i28 = i21;
                                m4541h0 = i33;
                                i34 = i22 | i53;
                                i31 = i34;
                                i41 = m4541h0;
                                i43 = i28;
                                i42 = i23;
                                i37 = i29;
                                i40 = i30;
                                i39 = 0;
                                i38 = -1;
                                i44 = i31;
                                i45 = i10;
                                break;
                            }
                        case 9:
                            i21 = i9;
                            i30 = i54;
                            i24 = i56;
                            i10 = i47;
                            if (i48 != 2) {
                                int i572 = i21;
                                i32 = i24;
                                i28 = i572;
                                i27 = i32;
                                i11 = i27;
                                i39 = i28;
                                i12 = i23;
                                unsafe = unsafe2;
                                t6 = t7;
                                i44 = i22;
                                i13 = 0;
                                i14 = -1;
                                break;
                            } else {
                                i29 = i7;
                                i33 = C1798e.m4559q0(c1752w5.m4376K(i21), bArr2, i24, i29, c1678q32);
                                Object object = unsafe2.getObject(t7, j6);
                                if (object != null) {
                                    obj = C1775y4.m4463c(object, c1678q32.f7146c);
                                    unsafe2.putObject(t7, j6, obj);
                                    i28 = i21;
                                    m4541h0 = i33;
                                    i34 = i22 | i53;
                                    i31 = i34;
                                    i41 = m4541h0;
                                    i43 = i28;
                                    i42 = i23;
                                    i37 = i29;
                                    i40 = i30;
                                    i39 = 0;
                                    i38 = -1;
                                    i44 = i31;
                                    i45 = i10;
                                    break;
                                }
                                obj = c1678q32.f7146c;
                                unsafe2.putObject(t7, j6, obj);
                                i28 = i21;
                                m4541h0 = i33;
                                i34 = i22 | i53;
                                i31 = i34;
                                i41 = m4541h0;
                                i43 = i28;
                                i42 = i23;
                                i37 = i29;
                                i40 = i30;
                                i39 = 0;
                                i38 = -1;
                                i44 = i31;
                                i45 = i10;
                            }
                        case 10:
                            i21 = i9;
                            i15 = i54;
                            i24 = i56;
                            i10 = i47;
                            if (i48 != 2) {
                                int i5722 = i21;
                                i32 = i24;
                                i28 = i5722;
                                i27 = i32;
                                i11 = i27;
                                i39 = i28;
                                i12 = i23;
                                unsafe = unsafe2;
                                t6 = t7;
                                i44 = i22;
                                i13 = 0;
                                i14 = -1;
                                break;
                            } else {
                                m4557p0 = C1798e.m4557p0(bArr2, i24, c1678q32);
                                unsafe2.putObject(t7, j6, c1678q32.f7146c);
                                i26 = m4557p0;
                                i42 = i23;
                                i44 = i22 | i53;
                                i41 = i26;
                                i43 = i21;
                                i45 = i10;
                                i40 = i15;
                                i39 = 0;
                                i38 = -1;
                                i37 = i7;
                                break;
                            }
                        case 12:
                            i21 = i9;
                            i15 = i54;
                            i24 = i56;
                            i10 = i47;
                            if (i48 != 0) {
                                int i57222 = i21;
                                i32 = i24;
                                i28 = i57222;
                                i27 = i32;
                                i11 = i27;
                                i39 = i28;
                                i12 = i23;
                                unsafe = unsafe2;
                                t6 = t7;
                                i44 = i22;
                                i13 = 0;
                                i14 = -1;
                                break;
                            } else {
                                m4557p0 = C1798e.m4527T(bArr2, i24, c1678q32);
                                i25 = c1678q32.f7144a;
                                unsafe2.putInt(t7, j6, i25);
                                i26 = m4557p0;
                                i42 = i23;
                                i44 = i22 | i53;
                                i41 = i26;
                                i43 = i21;
                                i45 = i10;
                                i40 = i15;
                                i39 = 0;
                                i38 = -1;
                                i37 = i7;
                                break;
                            }
                        case 15:
                            i21 = i9;
                            i15 = i54;
                            i24 = i56;
                            i10 = i47;
                            if (i48 != 0) {
                                int i572222 = i21;
                                i32 = i24;
                                i28 = i572222;
                                i27 = i32;
                                i11 = i27;
                                i39 = i28;
                                i12 = i23;
                                unsafe = unsafe2;
                                t6 = t7;
                                i44 = i22;
                                i13 = 0;
                                i14 = -1;
                                break;
                            } else {
                                m4557p0 = C1798e.m4527T(bArr2, i24, c1678q32);
                                i25 = C0385m.m1428u(c1678q32.f7144a);
                                unsafe2.putInt(t7, j6, i25);
                                i26 = m4557p0;
                                i42 = i23;
                                i44 = i22 | i53;
                                i41 = i26;
                                i43 = i21;
                                i45 = i10;
                                i40 = i15;
                                i39 = 0;
                                i38 = -1;
                                i37 = i7;
                                break;
                            }
                        case 16:
                            if (i48 != 0) {
                                i10 = i47;
                                i27 = i56;
                                i28 = i9;
                                i11 = i27;
                                i39 = i28;
                                i12 = i23;
                                unsafe = unsafe2;
                                t6 = t7;
                                i44 = i22;
                                i13 = 0;
                                i14 = -1;
                                break;
                            } else {
                                i26 = C1798e.m4541h0(bArr2, i56, c1678q32);
                                i10 = i47;
                                i21 = i9;
                                i15 = i54;
                                unsafe2.putLong(t, j6, C0385m.m1432y(c1678q32.f7145b));
                                i42 = i23;
                                i44 = i22 | i53;
                                i41 = i26;
                                i43 = i21;
                                i45 = i10;
                                i40 = i15;
                                i39 = 0;
                                i38 = -1;
                                i37 = i7;
                                break;
                            }
                        default:
                            i28 = i9;
                            i27 = i56;
                            i10 = i47;
                            i11 = i27;
                            i39 = i28;
                            i12 = i23;
                            unsafe = unsafe2;
                            t6 = t7;
                            i44 = i22;
                            i13 = 0;
                            i14 = -1;
                            break;
                    }
                } else {
                    i10 = i47;
                    int i58 = i8;
                    i15 = 1048575;
                    int i59 = i9;
                    if (i51 != 27) {
                        if (i51 <= 49) {
                            i12 = i42;
                            i17 = i44;
                            i13 = 0;
                            unsafe = unsafe2;
                            i14 = -1;
                            i18 = i59;
                            i41 = m4373H(t, bArr, i58, i7, b6, i10, i48, i59, i50, i51, j6, c1678q3);
                            t6 = t;
                            if (i41 != i58) {
                                i19 = i18;
                            } else {
                                i20 = i41;
                                i19 = i18;
                                i11 = i20;
                                i39 = i19;
                                i44 = i17;
                            }
                        } else {
                            i16 = i58;
                            i12 = i42;
                            i17 = i44;
                            unsafe = unsafe2;
                            i18 = i59;
                            i13 = 0;
                            i14 = -1;
                            if (i51 != 50) {
                                i19 = i18;
                                t6 = t;
                                i41 = m4375J(t, bArr, i16, i7, b6, i10, i48, i50, i51, j6, i18, c1678q3);
                                if (i41 == i16) {
                                    i20 = i41;
                                    i11 = i20;
                                    i39 = i19;
                                    i44 = i17;
                                }
                            } else if (i48 == 2) {
                                c1752w5.m4374I(t, i18, j6);
                                throw null;
                            }
                        }
                        i43 = i19;
                        i42 = i12;
                        i44 = i17;
                        bArr2 = bArr;
                        i37 = i7;
                        c1678q32 = c1678q3;
                        t7 = t6;
                        i39 = i13;
                        i45 = i10;
                        i38 = i14;
                        unsafe2 = unsafe;
                        i40 = 1048575;
                        c1752w5 = this;
                    } else if (i48 == 2) {
                        InterfaceC1763x4 interfaceC1763x4 = (InterfaceC1763x4) unsafe2.getObject(t7, j6);
                        if (!interfaceC1763x4.mo3965a()) {
                            int size = interfaceC1763x4.size();
                            interfaceC1763x4 = interfaceC1763x4.mo3662k(size == 0 ? 10 : size + size);
                            unsafe2.putObject(t7, j6, interfaceC1763x4);
                        }
                        i21 = i59;
                        i41 = C1798e.m4567u0(c1752w5.m4376K(i59), b6, bArr, i58, i7, interfaceC1763x4, c1678q3);
                        i44 = i44;
                        i42 = i42;
                        i43 = i21;
                        i45 = i10;
                        i40 = i15;
                        i39 = 0;
                        i38 = -1;
                        i37 = i7;
                    } else {
                        i16 = i58;
                        i18 = i59;
                        i12 = i42;
                        i17 = i44;
                        unsafe = unsafe2;
                        i13 = 0;
                        i14 = -1;
                    }
                    t6 = t;
                    i20 = i16;
                    i19 = i18;
                    i11 = i20;
                    i39 = i19;
                    i44 = i17;
                }
            }
            i41 = C1798e.m4569v0(b6, bArr, i11, i7, m4368y(t), c1678q3);
            i43 = i39;
            i42 = i12;
            bArr2 = bArr;
            i37 = i7;
            c1678q32 = c1678q3;
            t7 = t6;
            i39 = i13;
            i45 = i10;
            i38 = i14;
            unsafe2 = unsafe;
            i40 = 1048575;
            c1752w5 = this;
        }
        int i60 = i44;
        Unsafe unsafe3 = unsafe2;
        T t8 = t7;
        if (i42 != i40) {
            unsafe3.putInt(t8, i42, i60);
        }
        if (i41 == i7) {
            return i41;
        }
        throw C1485a5.m3643c();
    }

    @Override // p152w2.InterfaceC1537e6
    /* renamed from: a */
    public final T mo3748a() {
        return (T) ((AbstractC1691r4) this.f7252e).mo3604r(4);
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // p152w2.InterfaceC1537e6
    /* renamed from: b */
    public final boolean mo3749b(T t) {
        int i6 = 0;
        int i7 = 0;
        int i8 = 1048575;
        while (true) {
            boolean z5 = true;
            if (i6 >= this.f7256i) {
                if (!this.f7253f) {
                    return true;
                }
                this.f7260m.mo3746b(t);
                throw null;
            }
            int i9 = this.f7255h[i6];
            int i10 = this.f7248a[i9];
            int m4380j = m4380j(i9);
            int i11 = this.f7248a[i9 + 2];
            int i12 = i11 & 1048575;
            int i13 = 1 << (i11 >>> 20);
            if (i12 != i8) {
                if (i12 != 1048575) {
                    i7 = f7247o.getInt(t, i12);
                }
                i8 = i12;
            }
            if ((268435456 & m4380j) != 0) {
                if (!(i8 == 1048575 ? m4383r(t, i9) : (i7 & i13) != 0)) {
                    return false;
                }
            }
            int i14 = (m4380j >>> 20) & 255;
            if (i14 == 9 || i14 == 17) {
                if (i8 == 1048575) {
                    z5 = m4383r(t, i9);
                } else if ((i7 & i13) == 0) {
                    z5 = false;
                }
                if (z5 && !m4376K(i9).mo3749b(C1789z6.m4498p(t, m4380j & 1048575))) {
                    return false;
                }
            } else {
                if (i14 != 27) {
                    if (i14 == 60 || i14 == 68) {
                        if (m4385t(t, i10, i9) && !m4376K(i9).mo3749b(C1789z6.m4498p(t, m4380j & 1048575))) {
                            return false;
                        }
                    } else if (i14 != 49) {
                        if (i14 == 50 && !((C1656o5) C1789z6.m4498p(t, m4380j & 1048575)).isEmpty()) {
                            throw null;
                        }
                    }
                }
                List list = (List) C1789z6.m4498p(t, m4380j & 1048575);
                if (list.isEmpty()) {
                    continue;
                } else {
                    InterfaceC1537e6 m4376K = m4376K(i9);
                    for (int i15 = 0; i15 < list.size(); i15++) {
                        if (!m4376K.mo3749b(list.get(i15))) {
                            return false;
                        }
                    }
                }
            }
            i6++;
        }
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Code restructure failed: missing block: B:72:0x00c8, code lost:
    
        if (r3 != null) goto L67;
     */
    /* JADX WARN: Code restructure failed: missing block: B:73:0x00e0, code lost:
    
        r2 = (r2 * 53) + r7;
     */
    /* JADX WARN: Code restructure failed: missing block: B:75:0x00dc, code lost:
    
        r7 = r3.hashCode();
     */
    /* JADX WARN: Code restructure failed: missing block: B:78:0x00da, code lost:
    
        if (r3 != null) goto L67;
     */
    @Override // p152w2.InterfaceC1537e6
    /* renamed from: c */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final int mo3750c(T r9) {
        /*
            Method dump skipped, instructions count: 464
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1752w5.mo3750c(java.lang.Object):int");
    }

    @Override // p152w2.InterfaceC1537e6
    /* renamed from: d */
    public final int mo3751d(T t) {
        return this.f7254g ? m4372G(t) : m4371F(t);
    }

    @Override // p152w2.InterfaceC1537e6
    /* renamed from: e */
    public final void mo3752e(T t, C1484a4 c1484a4) {
        int i6;
        double m4496n;
        float m4494l;
        long m4490h;
        long m4490h2;
        int m4488f;
        long m4490h3;
        int m4488f2;
        boolean m4492j;
        int m4488f3;
        int m4488f4;
        int m4488f5;
        long m4490h4;
        int m4488f6;
        long m4490h5;
        if (!this.f7254g) {
            m4388w(t, c1484a4);
            return;
        }
        if (this.f7253f) {
            this.f7260m.mo3746b(t);
            throw null;
        }
        int length = this.f7248a.length;
        for (0; i6 < length; i6 + 3) {
            int m4380j = m4380j(i6);
            int[] iArr = this.f7248a;
            int i7 = iArr[i6];
            switch ((m4380j >>> 20) & 255) {
                case 0:
                    if (m4383r(t, i6)) {
                        m4496n = C1789z6.m4496n(t, m4380j & 1048575);
                        c1484a4.m3628e(i7, m4496n);
                    }
                case 1:
                    if (m4383r(t, i6)) {
                        m4494l = C1789z6.m4494l(t, m4380j & 1048575);
                        c1484a4.m3627d(i7, m4494l);
                    }
                case 2:
                    if (m4383r(t, i6)) {
                        m4490h = C1789z6.m4490h(t, m4380j & 1048575);
                        c1484a4.m3625b(i7, m4490h);
                    }
                case 3:
                    if (m4383r(t, i6)) {
                        m4490h2 = C1789z6.m4490h(t, m4380j & 1048575);
                        c1484a4.m3630g(i7, m4490h2);
                    }
                case 4:
                    if (m4383r(t, i6)) {
                        m4488f = C1789z6.m4488f(t, m4380j & 1048575);
                        c1484a4.m3631h(i7, m4488f);
                    }
                case 5:
                    if (m4383r(t, i6)) {
                        m4490h3 = C1789z6.m4490h(t, m4380j & 1048575);
                        c1484a4.m3632i(i7, m4490h3);
                    }
                case 6:
                    if (m4383r(t, i6)) {
                        m4488f2 = C1789z6.m4488f(t, m4380j & 1048575);
                        c1484a4.m3633j(i7, m4488f2);
                    }
                case 7:
                    if (m4383r(t, i6)) {
                        m4492j = C1789z6.m4492j(t, m4380j & 1048575);
                        c1484a4.m3634k(i7, m4492j);
                    }
                case 8:
                    i6 = m4383r(t, i6) ? 0 : i6 + 3;
                    m4367x(i7, C1789z6.m4498p(t, m4380j & 1048575), c1484a4);
                case 9:
                    if (!m4383r(t, i6)) {
                    }
                    c1484a4.m3639p(i7, C1789z6.m4498p(t, m4380j & 1048575), m4376K(i6));
                case 10:
                    if (!m4383r(t, i6)) {
                    }
                    c1484a4.m3635l(i7, (AbstractC1750w3) C1789z6.m4498p(t, m4380j & 1048575));
                case 11:
                    if (m4383r(t, i6)) {
                        m4488f3 = C1789z6.m4488f(t, m4380j & 1048575);
                        c1484a4.m3636m(i7, m4488f3);
                    }
                case 12:
                    if (m4383r(t, i6)) {
                        m4488f4 = C1789z6.m4488f(t, m4380j & 1048575);
                        c1484a4.m3629f(i7, m4488f4);
                    }
                case 13:
                    if (m4383r(t, i6)) {
                        m4488f5 = C1789z6.m4488f(t, m4380j & 1048575);
                        c1484a4.m3624a(i7, m4488f5);
                    }
                case 14:
                    if (m4383r(t, i6)) {
                        m4490h4 = C1789z6.m4490h(t, m4380j & 1048575);
                        c1484a4.m3626c(i7, m4490h4);
                    }
                case 15:
                    if (m4383r(t, i6)) {
                        m4488f6 = C1789z6.m4488f(t, m4380j & 1048575);
                        c1484a4.m3637n(i7, m4488f6);
                    }
                case 16:
                    if (m4383r(t, i6)) {
                        m4490h5 = C1789z6.m4490h(t, m4380j & 1048575);
                        c1484a4.m3638o(i7, m4490h5);
                    }
                case 17:
                    if (!m4383r(t, i6)) {
                    }
                    c1484a4.m3640q(i7, C1789z6.m4498p(t, m4380j & 1048575), m4376K(i6));
                case 18:
                    C1549f6.m3798b(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, false);
                case 19:
                    C1549f6.m3799c(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, false);
                case 20:
                    C1549f6.m3800d(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, false);
                case 21:
                    C1549f6.m3801e(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, false);
                case 22:
                    C1549f6.m3805i(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, false);
                case 23:
                    C1549f6.m3803g(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, false);
                case 24:
                    C1549f6.m3808l(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, false);
                case 25:
                    C1549f6.m3811o(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, false);
                case 26:
                    C1549f6.m3812p(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4);
                case 27:
                    C1549f6.m3814r(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, m4376K(i6));
                case 28:
                    C1549f6.m3813q(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4);
                case 29:
                    C1549f6.m3806j(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, false);
                case 30:
                    C1549f6.m3810n(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, false);
                case 31:
                    C1549f6.m3809m(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, false);
                case 32:
                    C1549f6.m3804h(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, false);
                case 33:
                    C1549f6.m3807k(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, false);
                case 34:
                    C1549f6.m3802f(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, false);
                case 35:
                    C1549f6.m3798b(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, true);
                case 36:
                    C1549f6.m3799c(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, true);
                case 37:
                    C1549f6.m3800d(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, true);
                case 38:
                    C1549f6.m3801e(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, true);
                case 39:
                    C1549f6.m3805i(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, true);
                case 40:
                    C1549f6.m3803g(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, true);
                case 41:
                    C1549f6.m3808l(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, true);
                case 42:
                    C1549f6.m3811o(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, true);
                case 43:
                    C1549f6.m3806j(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, true);
                case 44:
                    C1549f6.m3810n(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, true);
                case 45:
                    C1549f6.m3809m(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, true);
                case 46:
                    C1549f6.m3804h(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, true);
                case 47:
                    C1549f6.m3807k(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, true);
                case 48:
                    C1549f6.m3802f(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, true);
                case 49:
                    C1549f6.m3815s(iArr[i6], (List) C1789z6.m4498p(t, m4380j & 1048575), c1484a4, m4376K(i6));
                case 50:
                    if (C1789z6.m4498p(t, m4380j & 1048575) != null) {
                        throw null;
                    }
                case 51:
                    if (m4385t(t, i7, i6)) {
                        m4496n = m4362l(t, m4380j & 1048575);
                        c1484a4.m3628e(i7, m4496n);
                    }
                case 52:
                    if (m4385t(t, i7, i6)) {
                        m4494l = m4363m(t, m4380j & 1048575);
                        c1484a4.m3627d(i7, m4494l);
                    }
                case 53:
                    if (m4385t(t, i7, i6)) {
                        m4490h = m4365o(t, m4380j & 1048575);
                        c1484a4.m3625b(i7, m4490h);
                    }
                case 54:
                    if (m4385t(t, i7, i6)) {
                        m4490h2 = m4365o(t, m4380j & 1048575);
                        c1484a4.m3630g(i7, m4490h2);
                    }
                case 55:
                    if (m4385t(t, i7, i6)) {
                        m4488f = m4364n(t, m4380j & 1048575);
                        c1484a4.m3631h(i7, m4488f);
                    }
                case 56:
                    if (m4385t(t, i7, i6)) {
                        m4490h3 = m4365o(t, m4380j & 1048575);
                        c1484a4.m3632i(i7, m4490h3);
                    }
                case 57:
                    if (m4385t(t, i7, i6)) {
                        m4488f2 = m4364n(t, m4380j & 1048575);
                        c1484a4.m3633j(i7, m4488f2);
                    }
                case 58:
                    if (m4385t(t, i7, i6)) {
                        m4492j = m4366p(t, m4380j & 1048575);
                        c1484a4.m3634k(i7, m4492j);
                    }
                case 59:
                    if (!m4385t(t, i7, i6)) {
                    }
                    m4367x(i7, C1789z6.m4498p(t, m4380j & 1048575), c1484a4);
                case 60:
                    if (!m4385t(t, i7, i6)) {
                    }
                    c1484a4.m3639p(i7, C1789z6.m4498p(t, m4380j & 1048575), m4376K(i6));
                case 61:
                    if (!m4385t(t, i7, i6)) {
                    }
                    c1484a4.m3635l(i7, (AbstractC1750w3) C1789z6.m4498p(t, m4380j & 1048575));
                case 62:
                    if (m4385t(t, i7, i6)) {
                        m4488f3 = m4364n(t, m4380j & 1048575);
                        c1484a4.m3636m(i7, m4488f3);
                    }
                case 63:
                    if (m4385t(t, i7, i6)) {
                        m4488f4 = m4364n(t, m4380j & 1048575);
                        c1484a4.m3629f(i7, m4488f4);
                    }
                case 64:
                    if (m4385t(t, i7, i6)) {
                        m4488f5 = m4364n(t, m4380j & 1048575);
                        c1484a4.m3624a(i7, m4488f5);
                    }
                case 65:
                    if (m4385t(t, i7, i6)) {
                        m4490h4 = m4365o(t, m4380j & 1048575);
                        c1484a4.m3626c(i7, m4490h4);
                    }
                case 66:
                    if (m4385t(t, i7, i6)) {
                        m4488f6 = m4364n(t, m4380j & 1048575);
                        c1484a4.m3637n(i7, m4488f6);
                    }
                case 67:
                    if (m4385t(t, i7, i6)) {
                        m4490h5 = m4365o(t, m4380j & 1048575);
                        c1484a4.m3638o(i7, m4490h5);
                    }
                case 68:
                    if (!m4385t(t, i7, i6)) {
                    }
                    c1484a4.m3640q(i7, C1789z6.m4498p(t, m4380j & 1048575), m4376K(i6));
                default:
            }
        }
        AbstractC1669p6<?, ?> abstractC1669p6 = this.f7259l;
        abstractC1669p6.mo4019i(abstractC1669p6.mo4014d(t), c1484a4);
    }

    @Override // p152w2.InterfaceC1537e6
    /* renamed from: f */
    public final void mo3753f(T t) {
        int i6;
        int i7 = this.f7256i;
        while (true) {
            i6 = this.f7257j;
            if (i7 >= i6) {
                break;
            }
            long m4380j = m4380j(this.f7255h[i7]) & 1048575;
            Object m4498p = C1789z6.m4498p(t, m4380j);
            if (m4498p != null) {
                ((C1656o5) m4498p).f7122j = false;
                C1789z6.m4499q(t, m4380j, m4498p);
            }
            i7++;
        }
        int length = this.f7255h.length;
        while (i6 < length) {
            this.f7258k.mo3784a(t, this.f7255h[i6]);
            i6++;
        }
        this.f7259l.mo4015e(t);
        if (this.f7253f) {
            this.f7260m.mo3747c(t);
        }
    }

    @Override // p152w2.InterfaceC1537e6
    /* renamed from: g */
    public final boolean mo3754g(T t, T t6) {
        int length = this.f7248a.length;
        for (int i6 = 0; i6 < length; i6 += 3) {
            int m4380j = m4380j(i6);
            long j6 = m4380j & 1048575;
            switch ((m4380j >>> 20) & 255) {
                case 0:
                    if (m4382q(t, t6, i6) && Double.doubleToLongBits(C1789z6.m4496n(t, j6)) == Double.doubleToLongBits(C1789z6.m4496n(t6, j6))) {
                        break;
                    }
                    return false;
                case 1:
                    if (m4382q(t, t6, i6) && Float.floatToIntBits(C1789z6.m4494l(t, j6)) == Float.floatToIntBits(C1789z6.m4494l(t6, j6))) {
                        break;
                    }
                    return false;
                case 2:
                    if (m4382q(t, t6, i6) && C1789z6.m4490h(t, j6) == C1789z6.m4490h(t6, j6)) {
                        break;
                    }
                    return false;
                case 3:
                    if (m4382q(t, t6, i6) && C1789z6.m4490h(t, j6) == C1789z6.m4490h(t6, j6)) {
                        break;
                    }
                    return false;
                case 4:
                    if (m4382q(t, t6, i6) && C1789z6.m4488f(t, j6) == C1789z6.m4488f(t6, j6)) {
                        break;
                    }
                    return false;
                case 5:
                    if (m4382q(t, t6, i6) && C1789z6.m4490h(t, j6) == C1789z6.m4490h(t6, j6)) {
                        break;
                    }
                    return false;
                case 6:
                    if (m4382q(t, t6, i6) && C1789z6.m4488f(t, j6) == C1789z6.m4488f(t6, j6)) {
                        break;
                    }
                    return false;
                case 7:
                    if (m4382q(t, t6, i6) && C1789z6.m4492j(t, j6) == C1789z6.m4492j(t6, j6)) {
                        break;
                    }
                    return false;
                case 8:
                    if (m4382q(t, t6, i6) && C1549f6.m3797a(C1789z6.m4498p(t, j6), C1789z6.m4498p(t6, j6))) {
                        break;
                    }
                    return false;
                case 9:
                    if (m4382q(t, t6, i6) && C1549f6.m3797a(C1789z6.m4498p(t, j6), C1789z6.m4498p(t6, j6))) {
                        break;
                    }
                    return false;
                case 10:
                    if (m4382q(t, t6, i6) && C1549f6.m3797a(C1789z6.m4498p(t, j6), C1789z6.m4498p(t6, j6))) {
                        break;
                    }
                    return false;
                case 11:
                    if (m4382q(t, t6, i6) && C1789z6.m4488f(t, j6) == C1789z6.m4488f(t6, j6)) {
                        break;
                    }
                    return false;
                case 12:
                    if (m4382q(t, t6, i6) && C1789z6.m4488f(t, j6) == C1789z6.m4488f(t6, j6)) {
                        break;
                    }
                    return false;
                case 13:
                    if (m4382q(t, t6, i6) && C1789z6.m4488f(t, j6) == C1789z6.m4488f(t6, j6)) {
                        break;
                    }
                    return false;
                case 14:
                    if (m4382q(t, t6, i6) && C1789z6.m4490h(t, j6) == C1789z6.m4490h(t6, j6)) {
                        break;
                    }
                    return false;
                case 15:
                    if (m4382q(t, t6, i6) && C1789z6.m4488f(t, j6) == C1789z6.m4488f(t6, j6)) {
                        break;
                    }
                    return false;
                case 16:
                    if (m4382q(t, t6, i6) && C1789z6.m4490h(t, j6) == C1789z6.m4490h(t6, j6)) {
                        break;
                    }
                    return false;
                case 17:
                    if (m4382q(t, t6, i6) && C1549f6.m3797a(C1789z6.m4498p(t, j6), C1789z6.m4498p(t6, j6))) {
                        break;
                    }
                    return false;
                case 18:
                case 19:
                case 20:
                case 21:
                case 22:
                case 23:
                case 24:
                case 25:
                case 26:
                case 27:
                case 28:
                case 29:
                case 30:
                case 31:
                case 32:
                case 33:
                case 34:
                case 35:
                case 36:
                case 37:
                case 38:
                case 39:
                case 40:
                case 41:
                case 42:
                case 43:
                case 44:
                case 45:
                case 46:
                case 47:
                case 48:
                case 49:
                case 50:
                    if (!C1549f6.m3797a(C1789z6.m4498p(t, j6), C1789z6.m4498p(t6, j6))) {
                        return false;
                    }
                    break;
                case 51:
                case 52:
                case 53:
                case 54:
                case 55:
                case 56:
                case 57:
                case 58:
                case 59:
                case 60:
                case 61:
                case 62:
                case 63:
                case 64:
                case 65:
                case 66:
                case 67:
                case 68:
                    long m4381k = m4381k(i6) & 1048575;
                    if (C1789z6.m4488f(t, m4381k) == C1789z6.m4488f(t6, m4381k) && C1549f6.m3797a(C1789z6.m4498p(t, j6), C1789z6.m4498p(t6, j6))) {
                        break;
                    }
                    return false;
            }
        }
        if (!this.f7259l.mo4014d(t).equals(this.f7259l.mo4014d(t6))) {
            return false;
        }
        if (!this.f7253f) {
            return true;
        }
        this.f7260m.mo3746b(t);
        this.f7260m.mo3746b(t6);
        throw null;
    }

    @Override // p152w2.InterfaceC1537e6
    /* renamed from: h */
    public final void mo3755h(T t, T t6) {
        Objects.requireNonNull(t6);
        for (int i6 = 0; i6 < this.f7248a.length; i6 += 3) {
            int m4380j = m4380j(i6);
            long j6 = 1048575 & m4380j;
            int i7 = this.f7248a[i6];
            switch ((m4380j >>> 20) & 255) {
                case 0:
                    if (m4383r(t6, i6)) {
                        C1789z6.m4497o(t, j6, C1789z6.m4496n(t6, j6));
                        m4384s(t, i6);
                        break;
                    } else {
                        break;
                    }
                case 1:
                    if (m4383r(t6, i6)) {
                        C1789z6.m4495m(t, j6, C1789z6.m4494l(t6, j6));
                        m4384s(t, i6);
                        break;
                    } else {
                        break;
                    }
                case 2:
                    if (!m4383r(t6, i6)) {
                        break;
                    }
                    C1789z6.m4491i(t, j6, C1789z6.m4490h(t6, j6));
                    m4384s(t, i6);
                    break;
                case 3:
                    if (!m4383r(t6, i6)) {
                        break;
                    }
                    C1789z6.m4491i(t, j6, C1789z6.m4490h(t6, j6));
                    m4384s(t, i6);
                    break;
                case 4:
                    if (!m4383r(t6, i6)) {
                        break;
                    }
                    C1789z6.m4489g(t, j6, C1789z6.m4488f(t6, j6));
                    m4384s(t, i6);
                    break;
                case 5:
                    if (!m4383r(t6, i6)) {
                        break;
                    }
                    C1789z6.m4491i(t, j6, C1789z6.m4490h(t6, j6));
                    m4384s(t, i6);
                    break;
                case 6:
                    if (!m4383r(t6, i6)) {
                        break;
                    }
                    C1789z6.m4489g(t, j6, C1789z6.m4488f(t6, j6));
                    m4384s(t, i6);
                    break;
                case 7:
                    if (m4383r(t6, i6)) {
                        C1789z6.m4493k(t, j6, C1789z6.m4492j(t6, j6));
                        m4384s(t, i6);
                        break;
                    } else {
                        break;
                    }
                case 8:
                    if (!m4383r(t6, i6)) {
                        break;
                    }
                    C1789z6.m4499q(t, j6, C1789z6.m4498p(t6, j6));
                    m4384s(t, i6);
                    break;
                case 9:
                case 17:
                    m4369D(t, t6, i6);
                    break;
                case 10:
                    if (!m4383r(t6, i6)) {
                        break;
                    }
                    C1789z6.m4499q(t, j6, C1789z6.m4498p(t6, j6));
                    m4384s(t, i6);
                    break;
                case 11:
                    if (!m4383r(t6, i6)) {
                        break;
                    }
                    C1789z6.m4489g(t, j6, C1789z6.m4488f(t6, j6));
                    m4384s(t, i6);
                    break;
                case 12:
                    if (!m4383r(t6, i6)) {
                        break;
                    }
                    C1789z6.m4489g(t, j6, C1789z6.m4488f(t6, j6));
                    m4384s(t, i6);
                    break;
                case 13:
                    if (!m4383r(t6, i6)) {
                        break;
                    }
                    C1789z6.m4489g(t, j6, C1789z6.m4488f(t6, j6));
                    m4384s(t, i6);
                    break;
                case 14:
                    if (!m4383r(t6, i6)) {
                        break;
                    }
                    C1789z6.m4491i(t, j6, C1789z6.m4490h(t6, j6));
                    m4384s(t, i6);
                    break;
                case 15:
                    if (!m4383r(t6, i6)) {
                        break;
                    }
                    C1789z6.m4489g(t, j6, C1789z6.m4488f(t6, j6));
                    m4384s(t, i6);
                    break;
                case 16:
                    if (!m4383r(t6, i6)) {
                        break;
                    }
                    C1789z6.m4491i(t, j6, C1789z6.m4490h(t6, j6));
                    m4384s(t, i6);
                    break;
                case 18:
                case 19:
                case 20:
                case 21:
                case 22:
                case 23:
                case 24:
                case 25:
                case 26:
                case 27:
                case 28:
                case 29:
                case 30:
                case 31:
                case 32:
                case 33:
                case 34:
                case 35:
                case 36:
                case 37:
                case 38:
                case 39:
                case 40:
                case 41:
                case 42:
                case 43:
                case 44:
                case 45:
                case 46:
                case 47:
                case 48:
                case 49:
                    this.f7258k.mo3785b(t, t6, j6);
                    break;
                case 50:
                    Class<?> cls = C1549f6.f6970a;
                    C1789z6.m4499q(t, j6, C1668p5.m4010b(C1789z6.m4498p(t, j6), C1789z6.m4498p(t6, j6)));
                    break;
                case 51:
                case 52:
                case 53:
                case 54:
                case 55:
                case 56:
                case 57:
                case 58:
                case 59:
                    if (!m4385t(t6, i7, i6)) {
                        break;
                    }
                    C1789z6.m4499q(t, j6, C1789z6.m4498p(t6, j6));
                    m4386u(t, i7, i6);
                    break;
                case 60:
                case 68:
                    m4370E(t, t6, i6);
                    break;
                case 61:
                case 62:
                case 63:
                case 64:
                case 65:
                case 66:
                case 67:
                    if (!m4385t(t6, i7, i6)) {
                        break;
                    }
                    C1789z6.m4499q(t, j6, C1789z6.m4498p(t6, j6));
                    m4386u(t, i7, i6);
                    break;
            }
        }
        AbstractC1669p6<?, ?> abstractC1669p6 = this.f7259l;
        Class<?> cls2 = C1549f6.f6970a;
        abstractC1669p6.mo4013c(t, abstractC1669p6.mo4016f(abstractC1669p6.mo4014d(t), abstractC1669p6.mo4014d(t6)));
        if (this.f7253f) {
            this.f7260m.mo3746b(t6);
            throw null;
        }
    }

    @Override // p152w2.InterfaceC1537e6
    /* renamed from: i */
    public final void mo3756i(T t, byte[] bArr, int i6, int i7, C1678q3 c1678q3) {
        if (this.f7254g) {
            m4379N(t, bArr, i6, i7, c1678q3);
        } else {
            m4389z(t, bArr, i6, i7, 0, c1678q3);
        }
    }

    /* renamed from: j */
    public final int m4380j(int i6) {
        return this.f7248a[i6 + 1];
    }

    /* renamed from: k */
    public final int m4381k(int i6) {
        return this.f7248a[i6 + 2];
    }

    /* renamed from: q */
    public final boolean m4382q(T t, T t6, int i6) {
        return m4383r(t, i6) == m4383r(t6, i6);
    }

    /* renamed from: r */
    public final boolean m4383r(T t, int i6) {
        int m4381k = m4381k(i6);
        long j6 = m4381k & 1048575;
        if (j6 != 1048575) {
            return (C1789z6.m4488f(t, j6) & (1 << (m4381k >>> 20))) != 0;
        }
        int m4380j = m4380j(i6);
        long j7 = m4380j & 1048575;
        switch ((m4380j >>> 20) & 255) {
            case 0:
                return C1789z6.m4496n(t, j7) != 0.0d;
            case 1:
                return C1789z6.m4494l(t, j7) != 0.0f;
            case 2:
                return C1789z6.m4490h(t, j7) != 0;
            case 3:
                return C1789z6.m4490h(t, j7) != 0;
            case 4:
                return C1789z6.m4488f(t, j7) != 0;
            case 5:
                return C1789z6.m4490h(t, j7) != 0;
            case 6:
                return C1789z6.m4488f(t, j7) != 0;
            case 7:
                return C1789z6.m4492j(t, j7);
            case 8:
                Object m4498p = C1789z6.m4498p(t, j7);
                if (m4498p instanceof String) {
                    return !((String) m4498p).isEmpty();
                }
                if (m4498p instanceof AbstractC1750w3) {
                    return !AbstractC1750w3.f7244k.equals(m4498p);
                }
                throw new IllegalArgumentException();
            case 9:
                return C1789z6.m4498p(t, j7) != null;
            case 10:
                return !AbstractC1750w3.f7244k.equals(C1789z6.m4498p(t, j7));
            case 11:
                return C1789z6.m4488f(t, j7) != 0;
            case 12:
                return C1789z6.m4488f(t, j7) != 0;
            case 13:
                return C1789z6.m4488f(t, j7) != 0;
            case 14:
                return C1789z6.m4490h(t, j7) != 0;
            case 15:
                return C1789z6.m4488f(t, j7) != 0;
            case 16:
                return C1789z6.m4490h(t, j7) != 0;
            case 17:
                return C1789z6.m4498p(t, j7) != null;
            default:
                throw new IllegalArgumentException();
        }
    }

    /* renamed from: s */
    public final void m4384s(T t, int i6) {
        int m4381k = m4381k(i6);
        long j6 = 1048575 & m4381k;
        if (j6 == 1048575) {
            return;
        }
        C1789z6.m4489g(t, j6, (1 << (m4381k >>> 20)) | C1789z6.m4488f(t, j6));
    }

    /* renamed from: t */
    public final boolean m4385t(T t, int i6, int i7) {
        return C1789z6.m4488f(t, (long) (m4381k(i7) & 1048575)) == i6;
    }

    /* renamed from: u */
    public final void m4386u(T t, int i6, int i7) {
        C1789z6.m4489g(t, m4381k(i7) & 1048575, i6);
    }

    /* renamed from: v */
    public final int m4387v(int i6, int i7) {
        int length = (this.f7248a.length / 3) - 1;
        while (i7 <= length) {
            int i8 = (length + i7) >>> 1;
            int i9 = i8 * 3;
            int i10 = this.f7248a[i9];
            if (i6 == i10) {
                return i9;
            }
            if (i6 < i10) {
                length = i8 - 1;
            } else {
                i7 = i8 + 1;
            }
        }
        return -1;
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* renamed from: w */
    public final void m4388w(T t, C1484a4 c1484a4) {
        int i6;
        boolean z5;
        if (this.f7253f) {
            this.f7260m.mo3746b(t);
            throw null;
        }
        int length = this.f7248a.length;
        Unsafe unsafe = f7247o;
        int i7 = 1048575;
        int i8 = 1048575;
        int i9 = 0;
        int i10 = 0;
        while (i9 < length) {
            int m4380j = m4380j(i9);
            int[] iArr = this.f7248a;
            int i11 = iArr[i9];
            int i12 = (m4380j >>> 20) & 255;
            if (i12 <= 17) {
                int i13 = iArr[i9 + 2];
                int i14 = i13 & i7;
                if (i14 != i8) {
                    i10 = unsafe.getInt(t, i14);
                    i8 = i14;
                }
                i6 = 1 << (i13 >>> 20);
            } else {
                i6 = 0;
            }
            long j6 = m4380j & i7;
            switch (i12) {
                case 0:
                    if ((i10 & i6) != 0) {
                        c1484a4.m3628e(i11, C1789z6.m4496n(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 1:
                    if ((i10 & i6) != 0) {
                        c1484a4.m3627d(i11, C1789z6.m4494l(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 2:
                    if ((i10 & i6) != 0) {
                        c1484a4.m3625b(i11, unsafe.getLong(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 3:
                    if ((i10 & i6) != 0) {
                        c1484a4.m3630g(i11, unsafe.getLong(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 4:
                    if ((i10 & i6) != 0) {
                        c1484a4.m3631h(i11, unsafe.getInt(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 5:
                    if ((i10 & i6) != 0) {
                        c1484a4.m3632i(i11, unsafe.getLong(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 6:
                    if ((i10 & i6) != 0) {
                        c1484a4.m3633j(i11, unsafe.getInt(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 7:
                    if ((i10 & i6) != 0) {
                        c1484a4.m3634k(i11, C1789z6.m4492j(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 8:
                    if ((i10 & i6) != 0) {
                        m4367x(i11, unsafe.getObject(t, j6), c1484a4);
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 9:
                    if ((i10 & i6) != 0) {
                        c1484a4.m3639p(i11, unsafe.getObject(t, j6), m4376K(i9));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 10:
                    if ((i10 & i6) != 0) {
                        c1484a4.m3635l(i11, (AbstractC1750w3) unsafe.getObject(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 11:
                    if ((i10 & i6) != 0) {
                        c1484a4.m3636m(i11, unsafe.getInt(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 12:
                    if ((i10 & i6) != 0) {
                        c1484a4.m3629f(i11, unsafe.getInt(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 13:
                    if ((i10 & i6) != 0) {
                        c1484a4.m3624a(i11, unsafe.getInt(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 14:
                    if ((i10 & i6) != 0) {
                        c1484a4.m3626c(i11, unsafe.getLong(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 15:
                    if ((i10 & i6) != 0) {
                        c1484a4.m3637n(i11, unsafe.getInt(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 16:
                    if ((i10 & i6) != 0) {
                        c1484a4.m3638o(i11, unsafe.getLong(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 17:
                    if ((i10 & i6) != 0) {
                        c1484a4.m3640q(i11, unsafe.getObject(t, j6), m4376K(i9));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 18:
                    C1549f6.m3798b(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, false);
                    i9 += 3;
                    i7 = 1048575;
                case 19:
                    C1549f6.m3799c(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, false);
                    i9 += 3;
                    i7 = 1048575;
                case 20:
                    C1549f6.m3800d(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, false);
                    i9 += 3;
                    i7 = 1048575;
                case 21:
                    C1549f6.m3801e(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, false);
                    i9 += 3;
                    i7 = 1048575;
                case 22:
                    C1549f6.m3805i(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, false);
                    i9 += 3;
                    i7 = 1048575;
                case 23:
                    C1549f6.m3803g(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, false);
                    i9 += 3;
                    i7 = 1048575;
                case 24:
                    C1549f6.m3808l(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, false);
                    i9 += 3;
                    i7 = 1048575;
                case 25:
                    C1549f6.m3811o(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, false);
                    i9 += 3;
                    i7 = 1048575;
                case 26:
                    C1549f6.m3812p(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4);
                    i9 += 3;
                    i7 = 1048575;
                case 27:
                    C1549f6.m3814r(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, m4376K(i9));
                    i9 += 3;
                    i7 = 1048575;
                case 28:
                    C1549f6.m3813q(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4);
                    i9 += 3;
                    i7 = 1048575;
                case 29:
                    z5 = false;
                    C1549f6.m3806j(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, false);
                    i9 += 3;
                    i7 = 1048575;
                case 30:
                    z5 = false;
                    C1549f6.m3810n(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, false);
                    i9 += 3;
                    i7 = 1048575;
                case 31:
                    z5 = false;
                    C1549f6.m3809m(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, false);
                    i9 += 3;
                    i7 = 1048575;
                case 32:
                    z5 = false;
                    C1549f6.m3804h(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, false);
                    i9 += 3;
                    i7 = 1048575;
                case 33:
                    z5 = false;
                    C1549f6.m3807k(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, false);
                    i9 += 3;
                    i7 = 1048575;
                case 34:
                    z5 = false;
                    C1549f6.m3802f(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, false);
                    i9 += 3;
                    i7 = 1048575;
                case 35:
                    C1549f6.m3798b(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, true);
                    i9 += 3;
                    i7 = 1048575;
                case 36:
                    C1549f6.m3799c(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, true);
                    i9 += 3;
                    i7 = 1048575;
                case 37:
                    C1549f6.m3800d(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, true);
                    i9 += 3;
                    i7 = 1048575;
                case 38:
                    C1549f6.m3801e(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, true);
                    i9 += 3;
                    i7 = 1048575;
                case 39:
                    C1549f6.m3805i(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, true);
                    i9 += 3;
                    i7 = 1048575;
                case 40:
                    C1549f6.m3803g(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, true);
                    i9 += 3;
                    i7 = 1048575;
                case 41:
                    C1549f6.m3808l(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, true);
                    i9 += 3;
                    i7 = 1048575;
                case 42:
                    C1549f6.m3811o(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, true);
                    i9 += 3;
                    i7 = 1048575;
                case 43:
                    C1549f6.m3806j(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, true);
                    i9 += 3;
                    i7 = 1048575;
                case 44:
                    C1549f6.m3810n(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, true);
                    i9 += 3;
                    i7 = 1048575;
                case 45:
                    C1549f6.m3809m(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, true);
                    i9 += 3;
                    i7 = 1048575;
                case 46:
                    C1549f6.m3804h(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, true);
                    i9 += 3;
                    i7 = 1048575;
                case 47:
                    C1549f6.m3807k(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, true);
                    i9 += 3;
                    i7 = 1048575;
                case 48:
                    C1549f6.m3802f(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, true);
                    i9 += 3;
                    i7 = 1048575;
                case 49:
                    C1549f6.m3815s(this.f7248a[i9], (List) unsafe.getObject(t, j6), c1484a4, m4376K(i9));
                    i9 += 3;
                    i7 = 1048575;
                case 50:
                    if (unsafe.getObject(t, j6) != null) {
                        throw null;
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 51:
                    if (m4385t(t, i11, i9)) {
                        c1484a4.m3628e(i11, m4362l(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 52:
                    if (m4385t(t, i11, i9)) {
                        c1484a4.m3627d(i11, m4363m(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 53:
                    if (m4385t(t, i11, i9)) {
                        c1484a4.m3625b(i11, m4365o(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 54:
                    if (m4385t(t, i11, i9)) {
                        c1484a4.m3630g(i11, m4365o(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 55:
                    if (m4385t(t, i11, i9)) {
                        c1484a4.m3631h(i11, m4364n(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 56:
                    if (m4385t(t, i11, i9)) {
                        c1484a4.m3632i(i11, m4365o(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 57:
                    if (m4385t(t, i11, i9)) {
                        c1484a4.m3633j(i11, m4364n(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 58:
                    if (m4385t(t, i11, i9)) {
                        c1484a4.m3634k(i11, m4366p(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 59:
                    if (m4385t(t, i11, i9)) {
                        m4367x(i11, unsafe.getObject(t, j6), c1484a4);
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 60:
                    if (m4385t(t, i11, i9)) {
                        c1484a4.m3639p(i11, unsafe.getObject(t, j6), m4376K(i9));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 61:
                    if (m4385t(t, i11, i9)) {
                        c1484a4.m3635l(i11, (AbstractC1750w3) unsafe.getObject(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 62:
                    if (m4385t(t, i11, i9)) {
                        c1484a4.m3636m(i11, m4364n(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 63:
                    if (m4385t(t, i11, i9)) {
                        c1484a4.m3629f(i11, m4364n(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 64:
                    if (m4385t(t, i11, i9)) {
                        c1484a4.m3624a(i11, m4364n(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 65:
                    if (m4385t(t, i11, i9)) {
                        c1484a4.m3626c(i11, m4365o(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 66:
                    if (m4385t(t, i11, i9)) {
                        c1484a4.m3637n(i11, m4364n(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 67:
                    if (m4385t(t, i11, i9)) {
                        c1484a4.m3638o(i11, m4365o(t, j6));
                    }
                    i9 += 3;
                    i7 = 1048575;
                case 68:
                    if (m4385t(t, i11, i9)) {
                        c1484a4.m3640q(i11, unsafe.getObject(t, j6), m4376K(i9));
                    }
                    i9 += 3;
                    i7 = 1048575;
                default:
                    i9 += 3;
                    i7 = 1048575;
            }
        }
        AbstractC1669p6<?, ?> abstractC1669p6 = this.f7259l;
        abstractC1669p6.mo4019i(abstractC1669p6.mo4014d(t), c1484a4);
    }

    /* JADX WARN: Code restructure failed: missing block: B:41:0x0495, code lost:
    
        if (r5 == 1048575) goto L165;
     */
    /* JADX WARN: Code restructure failed: missing block: B:42:0x0497, code lost:
    
        r29.putInt(r11, r5, r7);
     */
    /* JADX WARN: Code restructure failed: missing block: B:43:0x049d, code lost:
    
        r3 = r14.f7256i;
     */
    /* JADX WARN: Code restructure failed: missing block: B:45:0x04a1, code lost:
    
        if (r3 >= r14.f7257j) goto L250;
     */
    /* JADX WARN: Code restructure failed: missing block: B:46:0x04a3, code lost:
    
        r4 = r14.f7255h[r3];
        r5 = r14.f7248a[r4];
        r5 = p152w2.C1789z6.m4498p(r11, r14.m4380j(r4) & 1048575);
     */
    /* JADX WARN: Code restructure failed: missing block: B:47:0x04b5, code lost:
    
        if (r5 != null) goto L171;
     */
    /* JADX WARN: Code restructure failed: missing block: B:49:0x04bc, code lost:
    
        if (r14.m4378M(r4) != null) goto L251;
     */
    /* JADX WARN: Code restructure failed: missing block: B:51:0x04c1, code lost:
    
        r5 = (p152w2.C1656o5) r5;
        r0 = (p152w2.C1644n5) r14.m4377L(r4);
     */
    /* JADX WARN: Code restructure failed: missing block: B:52:0x04c9, code lost:
    
        throw null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:55:0x04be, code lost:
    
        r3 = r3 + 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:59:0x04cc, code lost:
    
        if (r1 != 0) goto L182;
     */
    /* JADX WARN: Code restructure failed: missing block: B:60:0x04ce, code lost:
    
        if (r0 != r36) goto L180;
     */
    /* JADX WARN: Code restructure failed: missing block: B:62:0x04d5, code lost:
    
        throw p152w2.C1485a5.m3643c();
     */
    /* JADX WARN: Code restructure failed: missing block: B:63:0x04da, code lost:
    
        return r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:64:0x04d6, code lost:
    
        if (r0 > r36) goto L185;
     */
    /* JADX WARN: Code restructure failed: missing block: B:65:0x04d8, code lost:
    
        if (r9 != r1) goto L185;
     */
    /* JADX WARN: Code restructure failed: missing block: B:67:0x04df, code lost:
    
        throw p152w2.C1485a5.m3643c();
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* renamed from: z */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final int m4389z(T r33, byte[] r34, int r35, int r36, int r37, p152w2.C1678q3 r38) {
        /*
            Method dump skipped, instructions count: 1286
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1752w5.m4389z(java.lang.Object, byte[], int, int, int, w2.q3):int");
    }
}
