package p152w2;

/* renamed from: w2.m9 */
/* loaded from: classes.dex */
public final class C1636m9 implements InterfaceC1624l9 {

    /* renamed from: a */
    public static final AbstractC1773y2<Boolean> f7093a;

    /* renamed from: b */
    public static final AbstractC1773y2<Boolean> f7094b;

    static {
        C1749w2 c1749w2 = new C1749w2(C1713t2.m4275a());
        f7093a = (C1737v2) c1749w2.m4355b("measurement.sdk.screen.manual_screen_view_logging", true);
        f7094b = (C1737v2) c1749w2.m4355b("measurement.sdk.screen.disabling_automatic_reporting", true);
    }

    @Override // p152w2.InterfaceC1624l9
    /* renamed from: a */
    public final void mo3908a() {
    }

    @Override // p152w2.InterfaceC1624l9
    /* renamed from: b */
    public final boolean mo3909b() {
        return f7093a.m4460c().booleanValue();
    }

    @Override // p152w2.InterfaceC1624l9
    /* renamed from: c */
    public final boolean mo3910c() {
        return f7094b.m4460c().booleanValue();
    }
}
