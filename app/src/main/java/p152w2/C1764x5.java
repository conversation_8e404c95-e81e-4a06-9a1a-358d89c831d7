package p152w2;

/* renamed from: w2.x5 */
/* loaded from: classes.dex */
public final class C1764x5<T> implements InterfaceC1537e6<T> {

    /* renamed from: a */
    public final InterfaceC1716t5 f7271a;

    /* renamed from: b */
    public final AbstractC1669p6<?, ?> f7272b;

    /* renamed from: c */
    public final boolean f7273c;

    /* renamed from: d */
    public final AbstractC1535e4<?> f7274d;

    public C1764x5(AbstractC1669p6<?, ?> abstractC1669p6, AbstractC1535e4<?> abstractC1535e4, InterfaceC1716t5 interfaceC1716t5) {
        this.f7272b = abstractC1669p6;
        this.f7273c = abstractC1535e4.mo3745a(interfaceC1716t5);
        this.f7274d = abstractC1535e4;
        this.f7271a = interfaceC1716t5;
    }

    @Override // p152w2.InterfaceC1537e6
    /* renamed from: a */
    public final T mo3748a() {
        return (T) ((C1643n4) this.f7271a.mo4063c()).m3952k();
    }

    @Override // p152w2.InterfaceC1537e6
    /* renamed from: b */
    public final boolean mo3749b(T t) {
        this.f7274d.mo3746b(t);
        throw null;
    }

    @Override // p152w2.InterfaceC1537e6
    /* renamed from: c */
    public final int mo3750c(T t) {
        int hashCode = this.f7272b.mo4014d(t).hashCode();
        if (!this.f7273c) {
            return hashCode;
        }
        this.f7274d.mo3746b(t);
        throw null;
    }

    @Override // p152w2.InterfaceC1537e6
    /* renamed from: d */
    public final int mo3751d(T t) {
        AbstractC1669p6<?, ?> abstractC1669p6 = this.f7272b;
        int mo4017g = abstractC1669p6.mo4017g(abstractC1669p6.mo4014d(t));
        if (!this.f7273c) {
            return mo4017g;
        }
        this.f7274d.mo3746b(t);
        throw null;
    }

    @Override // p152w2.InterfaceC1537e6
    /* renamed from: e */
    public final void mo3752e(T t, C1484a4 c1484a4) {
        this.f7274d.mo3746b(t);
        throw null;
    }

    @Override // p152w2.InterfaceC1537e6
    /* renamed from: f */
    public final void mo3753f(T t) {
        this.f7272b.mo4015e(t);
        this.f7274d.mo3747c(t);
    }

    @Override // p152w2.InterfaceC1537e6
    /* renamed from: g */
    public final boolean mo3754g(T t, T t6) {
        if (!this.f7272b.mo4014d(t).equals(this.f7272b.mo4014d(t6))) {
            return false;
        }
        if (!this.f7273c) {
            return true;
        }
        this.f7274d.mo3746b(t);
        this.f7274d.mo3746b(t6);
        throw null;
    }

    @Override // p152w2.InterfaceC1537e6
    /* renamed from: h */
    public final void mo3755h(T t, T t6) {
        AbstractC1669p6<?, ?> abstractC1669p6 = this.f7272b;
        Class<?> cls = C1549f6.f6970a;
        abstractC1669p6.mo4013c(t, abstractC1669p6.mo4016f(abstractC1669p6.mo4014d(t), abstractC1669p6.mo4014d(t6)));
        if (this.f7273c) {
            this.f7274d.mo3746b(t6);
            throw null;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // p152w2.InterfaceC1537e6
    /* renamed from: i */
    public final void mo3756i(T t, byte[] bArr, int i6, int i7, C1678q3 c1678q3) {
        AbstractC1691r4 abstractC1691r4 = (AbstractC1691r4) t;
        if (abstractC1691r4.zzc == C1681q6.f7150f) {
            abstractC1691r4.zzc = C1681q6.m4027a();
        }
        throw null;
    }
}
