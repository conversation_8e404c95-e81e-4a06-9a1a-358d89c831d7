package p152w2;

import java.util.logging.Logger;
import p008b0.C0385m;

/* renamed from: w2.z3 */
/* loaded from: classes.dex */
public abstract class AbstractC1786z3 extends C0385m {

    /* renamed from: H0 */
    public static final Logger f7302H0 = Logger.getLogger(AbstractC1786z3.class.getName());

    /* renamed from: I0 */
    public static final boolean f7303I0 = C1789z6.f7311e;

    /* renamed from: G0 */
    public C1484a4 f7304G0;

    public AbstractC1786z3() {
        super(null);
    }

    public /* synthetic */ AbstractC1786z3(C0385m c0385m) {
        super(null);
    }

    /* renamed from: I */
    public static int m4475I(AbstractC1750w3 abstractC1750w3) {
        int mo4287g = abstractC1750w3.mo4287g();
        return m4480c0(mo4287g) + mo4287g;
    }

    /* renamed from: J */
    public static int m4476J(InterfaceC1716t5 interfaceC1716t5, InterfaceC1537e6 interfaceC1537e6) {
        AbstractC1642n3 abstractC1642n3 = (AbstractC1642n3) interfaceC1716t5;
        int mo3944g = abstractC1642n3.mo3944g();
        if (mo3944g == -1) {
            mo3944g = interfaceC1537e6.mo3751d(abstractC1642n3);
            abstractC1642n3.mo3945h(mo3944g);
        }
        return m4480c0(mo3944g) + mo3944g;
    }

    @Deprecated
    /* renamed from: K */
    public static int m4477K(int i6, InterfaceC1716t5 interfaceC1716t5, InterfaceC1537e6 interfaceC1537e6) {
        int m4480c0 = m4480c0(i6 << 3);
        int i7 = m4480c0 + m4480c0;
        AbstractC1642n3 abstractC1642n3 = (AbstractC1642n3) interfaceC1716t5;
        int mo3944g = abstractC1642n3.mo3944g();
        if (mo3944g == -1) {
            mo3944g = interfaceC1537e6.mo3751d(abstractC1642n3);
            abstractC1642n3.mo3945h(mo3944g);
        }
        return i7 + mo3944g;
    }

    /* renamed from: a0 */
    public static int m4478a0(int i6) {
        return m4480c0(i6 << 3);
    }

    /* renamed from: b0 */
    public static int m4479b0(int i6) {
        if (i6 >= 0) {
            return m4480c0(i6);
        }
        return 10;
    }

    /* renamed from: c0 */
    public static int m4480c0(int i6) {
        if ((i6 & (-128)) == 0) {
            return 1;
        }
        if ((i6 & (-16384)) == 0) {
            return 2;
        }
        if (((-2097152) & i6) == 0) {
            return 3;
        }
        return (i6 & (-268435456)) == 0 ? 4 : 5;
    }

    /* renamed from: d0 */
    public static int m4481d0(long j6) {
        int i6;
        if (((-128) & j6) == 0) {
            return 1;
        }
        if (j6 < 0) {
            return 10;
        }
        if (((-34359738368L) & j6) != 0) {
            j6 >>>= 28;
            i6 = 6;
        } else {
            i6 = 2;
        }
        if (((-2097152) & j6) != 0) {
            i6 += 2;
            j6 >>>= 14;
        }
        return (j6 & (-16384)) != 0 ? i6 + 1 : i6;
    }

    /* renamed from: e0 */
    public static int m4482e0(String str) {
        int length;
        try {
            length = C1500b7.m3665b(str);
        } catch (C1487a7 unused) {
            length = str.getBytes(C1775y4.f7289a).length;
        }
        return m4480c0(length) + length;
    }

    /* renamed from: L */
    public abstract void mo4405L(int i6, int i7);

    /* renamed from: M */
    public abstract void mo4406M(int i6, int i7);

    /* renamed from: N */
    public abstract void mo4407N(int i6, int i7);

    /* renamed from: O */
    public abstract void mo4408O(int i6, int i7);

    /* renamed from: P */
    public abstract void mo4409P(int i6, long j6);

    /* renamed from: Q */
    public abstract void mo4410Q(int i6, long j6);

    /* renamed from: R */
    public abstract void mo4411R(int i6, boolean z5);

    /* renamed from: S */
    public abstract void mo4412S(int i6, String str);

    /* renamed from: T */
    public abstract void mo4413T(int i6, AbstractC1750w3 abstractC1750w3);

    /* renamed from: U */
    public abstract void mo4414U(byte b6);

    /* renamed from: V */
    public abstract void mo4415V(int i6);

    /* renamed from: W */
    public abstract void mo4416W(int i6);

    /* renamed from: X */
    public abstract void mo4417X(int i6);

    /* renamed from: Y */
    public abstract void mo4418Y(long j6);

    /* renamed from: Z */
    public abstract void mo4419Z(long j6);
}
