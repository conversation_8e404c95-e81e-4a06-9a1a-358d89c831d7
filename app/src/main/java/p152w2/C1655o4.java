package p152w2;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import p008b0.C0385m;
import p023d1.C0722t;
import p090n.C1090c;
import p158x2.C1891g6;

/* renamed from: w2.o4 */
/* loaded from: classes.dex */
public final class C1655o4 extends AbstractC1590j {

    /* renamed from: l */
    public final /* synthetic */ int f7119l = 1;

    /* renamed from: m */
    public final Object f7120m;

    /* JADX WARN: Type inference failed for: r0v2, types: [java.util.HashMap, java.util.Map<java.lang.String, w2.p>] */
    public C1655o4(C0722t c0722t) {
        super("internal.remoteConfig");
        this.f7120m = c0722t;
        this.f7016k.put("getValue", new C1597j6(c0722t));
    }

    /* JADX WARN: Type inference failed for: r4v0, types: [java.util.HashMap, java.util.Map<java.lang.String, w2.p>] */
    @Override // p152w2.AbstractC1590j
    /* renamed from: a */
    public final InterfaceC1662p mo3670a(C1090c c1090c, List list) {
        switch (this.f7119l) {
            case 0:
                C0385m.m1426r(this.f7015j, 3, list);
                String mo3761c = c1090c.m2803b((InterfaceC1662p) list.get(0)).mo3761c();
                long m1409F = (long) C0385m.m1409F(c1090c.m2803b((InterfaceC1662p) list.get(1)).mo3762d().doubleValue());
                InterfaceC1662p m2803b = c1090c.m2803b((InterfaceC1662p) list.get(2));
                HashMap hashMap = new HashMap();
                if (m2803b instanceof C1626m) {
                    C1626m c1626m = (C1626m) m2803b;
                    Objects.requireNonNull(c1626m);
                    Iterator it = new ArrayList(c1626m.f7078j.keySet()).iterator();
                    while (it.hasNext()) {
                        String str = (String) it.next();
                        Object m1410G = C0385m.m1410G(c1626m.mo3767m(str));
                        if (m1410G != null) {
                            hashMap.put(str, m1410G);
                        }
                    }
                }
                ((List) ((C1505c) this.f7120m).f6909d).add(new C1492b(mo3761c, m1409F, hashMap));
                break;
        }
        return InterfaceC1662p.f7126b;
    }

    public C1655o4(C1505c c1505c) {
        super("internal.eventLogger");
        this.f7120m = c1505c;
    }

    /* JADX WARN: Type inference failed for: r6v1, types: [java.util.HashMap, java.util.Map<java.lang.String, w2.p>] */
    /* JADX WARN: Type inference failed for: r6v2, types: [java.util.HashMap, java.util.Map<java.lang.String, w2.p>] */
    /* JADX WARN: Type inference failed for: r6v3, types: [java.util.HashMap, java.util.Map<java.lang.String, w2.p>] */
    /* JADX WARN: Type inference failed for: r6v6, types: [java.util.HashMap, java.util.Map<java.lang.String, w2.p>] */
    /* JADX WARN: Type inference failed for: r6v7, types: [java.util.HashMap, java.util.Map<java.lang.String, w2.p>] */
    public C1655o4(C1891g6 c1891g6) {
        super("internal.logger");
        this.f7120m = c1891g6;
        this.f7016k.put("log", new C1504bb(this, false, true));
        this.f7016k.put("silent", new C1552f9(this, "silent"));
        ((AbstractC1590j) this.f7016k.get("silent")).mo3764i("log", new C1504bb(this, true, true));
        this.f7016k.put("unmonitored", new C1565ga());
        ((AbstractC1590j) this.f7016k.get("unmonitored")).mo3764i("log", new C1504bb(this, false, false));
    }
}
