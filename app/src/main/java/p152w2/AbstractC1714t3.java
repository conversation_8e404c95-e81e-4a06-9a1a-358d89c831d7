package p152w2;

import java.util.Iterator;

/* renamed from: w2.t3 */
/* loaded from: classes.dex */
public abstract class AbstractC1714t3 implements Iterator {
    /* renamed from: a */
    public abstract byte mo4139a();

    @Override // java.util.Iterator
    public final /* bridge */ /* synthetic */ Object next() {
        return Byte.valueOf(mo4139a());
    }

    @Override // java.util.Iterator
    public final void remove() {
        throw new UnsupportedOperationException();
    }
}
