package p152w2;

import java.lang.reflect.Field;
import sun.misc.Unsafe;

/* renamed from: w2.y6 */
/* loaded from: classes.dex */
public abstract class AbstractC1777y6 {

    /* renamed from: a */
    public final Unsafe f7291a;

    public AbstractC1777y6(Unsafe unsafe) {
        this.f7291a = unsafe;
    }

    /* renamed from: a */
    public abstract void mo4390a(Object obj, long j6, byte b6);

    /* renamed from: b */
    public abstract boolean mo4391b(Object obj, long j6);

    /* renamed from: c */
    public abstract void mo4392c(Object obj, long j6, boolean z5);

    /* renamed from: d */
    public abstract float mo4393d(Object obj, long j6);

    /* renamed from: e */
    public abstract void mo4394e(Object obj, long j6, float f6);

    /* renamed from: f */
    public abstract double mo4395f(Object obj, long j6);

    /* renamed from: g */
    public abstract void mo4396g(Object obj, long j6, double d6);

    /* renamed from: h */
    public final long m4464h(Field field) {
        return this.f7291a.objectFieldOffset(field);
    }

    /* renamed from: i */
    public final int m4465i(Class<?> cls) {
        return this.f7291a.arrayBaseOffset(cls);
    }

    /* renamed from: j */
    public final int m4466j(Class<?> cls) {
        return this.f7291a.arrayIndexScale(cls);
    }

    /* renamed from: k */
    public final int m4467k(Object obj, long j6) {
        return this.f7291a.getInt(obj, j6);
    }

    /* renamed from: l */
    public final void m4468l(Object obj, long j6, int i6) {
        this.f7291a.putInt(obj, j6, i6);
    }

    /* renamed from: m */
    public final long m4469m(Object obj, long j6) {
        return this.f7291a.getLong(obj, j6);
    }

    /* renamed from: n */
    public final void m4470n(Object obj, long j6, long j7) {
        this.f7291a.putLong(obj, j6, j7);
    }

    /* renamed from: o */
    public final Object m4471o(Object obj, long j6) {
        return this.f7291a.getObject(obj, j6);
    }

    /* renamed from: p */
    public final void m4472p(Object obj, long j6, Object obj2) {
        this.f7291a.putObject(obj, j6, obj2);
    }
}
