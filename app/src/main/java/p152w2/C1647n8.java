package p152w2;

/* renamed from: w2.n8 */
/* loaded from: classes.dex */
public final class C1647n8 implements InterfaceC1635m8 {

    /* renamed from: a */
    public static final AbstractC1773y2<Boolean> f7110a;

    /* renamed from: b */
    public static final AbstractC1773y2<Boolean> f7111b;

    /* renamed from: c */
    public static final AbstractC1773y2<<PERSON><PERSON><PERSON>> f7112c;

    static {
        C1749w2 c1749w2 = new C1749w2(C1713t2.m4275a());
        c1749w2.m4355b("measurement.service.audience.fix_skip_audience_with_failed_filters", true);
        f7110a = (C1737v2) c1749w2.m4355b("measurement.audience.refresh_event_count_filters_timestamp", false);
        f7111b = (C1737v2) c1749w2.m4355b("measurement.audience.use_bundle_end_timestamp_for_non_sequence_property_filters", false);
        f7112c = (C1737v2) c1749w2.m4355b("measurement.audience.use_bundle_timestamp_for_event_count_filters", false);
    }

    @Override // p152w2.InterfaceC1635m8
    /* renamed from: a */
    public final void mo3935a() {
    }

    @Override // p152w2.InterfaceC1635m8
    /* renamed from: b */
    public final boolean mo3936b() {
        return f7110a.m4460c().booleanValue();
    }

    @Override // p152w2.InterfaceC1635m8
    /* renamed from: c */
    public final boolean mo3937c() {
        return f7111b.m4460c().booleanValue();
    }

    @Override // p152w2.InterfaceC1635m8
    /* renamed from: d */
    public final boolean mo3938d() {
        return f7112c.m4460c().booleanValue();
    }
}
