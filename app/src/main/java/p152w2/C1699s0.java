package p152w2;

/* renamed from: w2.s0 */
/* loaded from: classes.dex */
public final class C1699s0 extends C1643n4<C1711t0, C1699s0> {
    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public C1699s0() {
        /*
            r1 = this;
            w2.t0 r0 = p152w2.C1711t0.m4148B()
            r1.<init>(r0)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1699s0.<init>():void");
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public /* synthetic */ C1699s0(p008b0.C0385m r1) {
        /*
            r0 = this;
            w2.t0 r1 = p152w2.C1711t0.m4148B()
            r0.<init>(r1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1699s0.<init>(b0.m):void");
    }
}
