package p152w2;

import java.util.NoSuchElementException;

/* renamed from: w2.s3 */
/* loaded from: classes.dex */
public final class C1702s3 extends AbstractC1714t3 {

    /* renamed from: j */
    public int f7172j = 0;

    /* renamed from: k */
    public final int f7173k;

    /* renamed from: l */
    public final /* synthetic */ AbstractC1750w3 f7174l;

    public C1702s3(AbstractC1750w3 abstractC1750w3) {
        this.f7174l = abstractC1750w3;
        this.f7173k = abstractC1750w3.mo4287g();
    }

    @Override // p152w2.AbstractC1714t3
    /* renamed from: a */
    public final byte mo4139a() {
        int i6 = this.f7172j;
        if (i6 >= this.f7173k) {
            throw new NoSuchElementException();
        }
        this.f7172j = i6 + 1;
        return this.f7174l.mo4286d(i6);
    }

    @Override // java.util.Iterator
    public final boolean hasNext() {
        return this.f7172j < this.f7173k;
    }
}
