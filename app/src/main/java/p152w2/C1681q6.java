package p152w2;

import java.util.Arrays;

/* renamed from: w2.q6 */
/* loaded from: classes.dex */
public final class C1681q6 {

    /* renamed from: f */
    public static final C1681q6 f7150f = new C1681q6(0, new int[0], new Object[0], false);

    /* renamed from: a */
    public int f7151a;

    /* renamed from: b */
    public int[] f7152b;

    /* renamed from: c */
    public Object[] f7153c;

    /* renamed from: d */
    public int f7154d;

    /* renamed from: e */
    public boolean f7155e;

    public C1681q6() {
        this(0, new int[8], new Object[8], true);
    }

    public C1681q6(int i6, int[] iArr, Object[] objArr, boolean z5) {
        this.f7154d = -1;
        this.f7151a = i6;
        this.f7152b = iArr;
        this.f7153c = objArr;
        this.f7155e = z5;
    }

    /* renamed from: a */
    public static C1681q6 m4027a() {
        return new C1681q6(0, new int[8], new Object[8], true);
    }

    /* renamed from: b */
    public final int m4028b() {
        int m4480c0;
        int m4481d0;
        int m4480c02;
        int i6 = this.f7154d;
        if (i6 != -1) {
            return i6;
        }
        int i7 = 0;
        for (int i8 = 0; i8 < this.f7151a; i8++) {
            int i9 = this.f7152b[i8];
            int i10 = i9 >>> 3;
            int i11 = i9 & 7;
            if (i11 != 0) {
                if (i11 == 1) {
                    ((Long) this.f7153c[i8]).longValue();
                    m4480c02 = AbstractC1786z3.m4480c0(i10 << 3) + 8;
                } else if (i11 == 2) {
                    AbstractC1750w3 abstractC1750w3 = (AbstractC1750w3) this.f7153c[i8];
                    int m4480c03 = AbstractC1786z3.m4480c0(i10 << 3);
                    int mo4287g = abstractC1750w3.mo4287g();
                    i7 = AbstractC1786z3.m4480c0(mo4287g) + mo4287g + m4480c03 + i7;
                } else if (i11 == 3) {
                    int m4478a0 = AbstractC1786z3.m4478a0(i10);
                    m4480c0 = m4478a0 + m4478a0;
                    m4481d0 = ((C1681q6) this.f7153c[i8]).m4028b();
                } else {
                    if (i11 != 5) {
                        int i12 = C1485a5.f6874j;
                        throw new IllegalStateException(new C1787z4());
                    }
                    ((Integer) this.f7153c[i8]).intValue();
                    m4480c02 = AbstractC1786z3.m4480c0(i10 << 3) + 4;
                }
                i7 = m4480c02 + i7;
            } else {
                long longValue = ((Long) this.f7153c[i8]).longValue();
                m4480c0 = AbstractC1786z3.m4480c0(i10 << 3);
                m4481d0 = AbstractC1786z3.m4481d0(longValue);
            }
            i7 = m4481d0 + m4480c0 + i7;
        }
        this.f7154d = i7;
        return i7;
    }

    /* renamed from: c */
    public final void m4029c(int i6, Object obj) {
        if (!this.f7155e) {
            throw new UnsupportedOperationException();
        }
        int i7 = this.f7151a;
        int[] iArr = this.f7152b;
        if (i7 == iArr.length) {
            int i8 = i7 + (i7 < 4 ? 8 : i7 >> 1);
            this.f7152b = Arrays.copyOf(iArr, i8);
            this.f7153c = Arrays.copyOf(this.f7153c, i8);
        }
        int[] iArr2 = this.f7152b;
        int i9 = this.f7151a;
        iArr2[i9] = i6;
        this.f7153c[i9] = obj;
        this.f7151a = i9 + 1;
    }

    /* renamed from: d */
    public final void m4030d(C1484a4 c1484a4) {
        if (this.f7151a != 0) {
            for (int i6 = 0; i6 < this.f7151a; i6++) {
                int i7 = this.f7152b[i6];
                Object obj = this.f7153c[i6];
                int i8 = i7 >>> 3;
                int i9 = i7 & 7;
                if (i9 == 0) {
                    c1484a4.m3625b(i8, ((Long) obj).longValue());
                } else if (i9 == 1) {
                    c1484a4.m3632i(i8, ((Long) obj).longValue());
                } else if (i9 == 2) {
                    c1484a4.m3635l(i8, (AbstractC1750w3) obj);
                } else if (i9 == 3) {
                    c1484a4.f6873a.mo4405L(i8, 3);
                    ((C1681q6) obj).m4030d(c1484a4);
                    c1484a4.f6873a.mo4405L(i8, 4);
                } else {
                    if (i9 != 5) {
                        int i10 = C1485a5.f6874j;
                        throw new RuntimeException(new C1787z4());
                    }
                    c1484a4.m3633j(i8, ((Integer) obj).intValue());
                }
            }
        }
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || !(obj instanceof C1681q6)) {
            return false;
        }
        C1681q6 c1681q6 = (C1681q6) obj;
        int i6 = this.f7151a;
        if (i6 == c1681q6.f7151a) {
            int[] iArr = this.f7152b;
            int[] iArr2 = c1681q6.f7152b;
            int i7 = 0;
            while (true) {
                if (i7 >= i6) {
                    Object[] objArr = this.f7153c;
                    Object[] objArr2 = c1681q6.f7153c;
                    int i8 = this.f7151a;
                    for (int i9 = 0; i9 < i8; i9++) {
                        if (objArr[i9].equals(objArr2[i9])) {
                        }
                    }
                    return true;
                }
                if (iArr[i7] != iArr2[i7]) {
                    break;
                }
                i7++;
            }
        }
        return false;
    }

    public final int hashCode() {
        int i6 = this.f7151a;
        int i7 = (i6 + 527) * 31;
        int[] iArr = this.f7152b;
        int i8 = 17;
        int i9 = 17;
        for (int i10 = 0; i10 < i6; i10++) {
            i9 = (i9 * 31) + iArr[i10];
        }
        int i11 = (i7 + i9) * 31;
        Object[] objArr = this.f7153c;
        int i12 = this.f7151a;
        for (int i13 = 0; i13 < i12; i13++) {
            i8 = (i8 * 31) + objArr[i13].hashCode();
        }
        return i11 + i8;
    }
}
