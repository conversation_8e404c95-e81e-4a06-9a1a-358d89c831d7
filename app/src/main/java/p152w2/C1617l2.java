package p152w2;

import android.database.ContentObserver;
import java.util.Iterator;

/* renamed from: w2.l2 */
/* loaded from: classes.dex */
public final class C1617l2 extends ContentObserver {

    /* renamed from: a */
    public final /* synthetic */ C1629m2 f7066a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C1617l2(C1629m2 c1629m2) {
        super(null);
        this.f7066a = c1629m2;
    }

    /* JADX WARN: Type inference failed for: r0v2, types: [java.util.ArrayList, java.util.List<w2.n2>] */
    @Override // android.database.ContentObserver
    public final void onChange(boolean z5) {
        C1629m2 c1629m2 = this.f7066a;
        synchronized (c1629m2.f7084d) {
            c1629m2.f7085e = null;
            AbstractC1773y2.f7283h.incrementAndGet();
        }
        synchronized (c1629m2) {
            Iterator it = c1629m2.f7086f.iterator();
            while (it.hasNext()) {
                ((InterfaceC1641n2) it.next()).m3940a();
            }
        }
    }
}
