package p152w2;

import java.util.List;

/* renamed from: w2.p0 */
/* loaded from: classes.dex */
public final class C1663p0 extends AbstractC1691r4<C1663p0, C1651o0> implements InterfaceC1728u5 {
    private static final C1663p0 zzj;
    private int zza;
    private int zze;
    private InterfaceC1763x4<C1771y0> zzf;
    private InterfaceC1763x4<C1687r0> zzg;
    private boolean zzh;
    private boolean zzi;

    static {
        C1663p0 c1663p0 = new C1663p0();
        zzj = c1663p0;
        AbstractC1691r4.m4061q(C1663p0.class, c1663p0);
    }

    public C1663p0() {
        C1512c6<Object> c1512c6 = C1512c6.f6916m;
        this.zzf = c1512c6;
        this.zzg = c1512c6;
    }

    /* renamed from: B */
    public static /* synthetic */ void m3974B(C1663p0 c1663p0, int i6, C1771y0 c1771y0) {
        InterfaceC1763x4<C1771y0> interfaceC1763x4 = c1663p0.zzf;
        if (!interfaceC1763x4.mo3965a()) {
            c1663p0.zzf = AbstractC1691r4.m4059l(interfaceC1763x4);
        }
        c1663p0.zzf.set(i6, c1771y0);
    }

    /* renamed from: C */
    public static /* synthetic */ void m3975C(C1663p0 c1663p0, int i6, C1687r0 c1687r0) {
        InterfaceC1763x4<C1687r0> interfaceC1763x4 = c1663p0.zzg;
        if (!interfaceC1763x4.mo3965a()) {
            c1663p0.zzg = AbstractC1691r4.m4059l(interfaceC1763x4);
        }
        c1663p0.zzg.set(i6, c1687r0);
    }

    @Override // p152w2.AbstractC1691r4
    /* renamed from: r */
    public final Object mo3604r(int i6) {
        int i7 = i6 - 1;
        if (i7 == 0) {
            return (byte) 1;
        }
        if (i7 == 2) {
            return new C1525d6(zzj, "\u0001\u0005\u0000\u0001\u0001\u0005\u0005\u0000\u0002\u0000\u0001င\u0000\u0002\u001b\u0003\u001b\u0004ဇ\u0001\u0005ဇ\u0002", new Object[]{"zza", "zze", "zzf", C1771y0.class, "zzg", C1687r0.class, "zzh", "zzi"});
        }
        if (i7 == 3) {
            return new C1663p0();
        }
        if (i7 == 4) {
            return new C1651o0(null);
        }
        if (i7 != 5) {
            return null;
        }
        return zzj;
    }

    /* renamed from: s */
    public final boolean m3976s() {
        return (this.zza & 1) != 0;
    }

    /* renamed from: t */
    public final int m3977t() {
        return this.zze;
    }

    /* renamed from: u */
    public final List<C1771y0> m3978u() {
        return this.zzf;
    }

    /* renamed from: v */
    public final int m3979v() {
        return this.zzf.size();
    }

    /* renamed from: w */
    public final C1771y0 m3980w(int i6) {
        return this.zzf.get(i6);
    }

    /* renamed from: x */
    public final List<C1687r0> m3981x() {
        return this.zzg;
    }

    /* renamed from: y */
    public final int m3982y() {
        return this.zzg.size();
    }

    /* renamed from: z */
    public final C1687r0 m3983z(int i6) {
        return this.zzg.get(i6);
    }
}
