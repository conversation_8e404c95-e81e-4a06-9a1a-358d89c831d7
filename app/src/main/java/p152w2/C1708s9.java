package p152w2;

/* renamed from: w2.s9 */
/* loaded from: classes.dex */
public final class C1708s9 implements InterfaceC1696r9 {

    /* renamed from: a */
    public static final AbstractC1773y2<Boolean> f7180a;

    static {
        C1749w2 c1749w2 = new C1749w2(C1713t2.m4275a());
        f7180a = (C1737v2) c1749w2.m4355b("measurement.validation.internal_limits_internal_event_params", false);
        c1749w2.m4354a("measurement.id.validation.internal_limits_internal_event_params", 0L);
    }

    @Override // p152w2.InterfaceC1696r9
    /* renamed from: a */
    public final boolean mo4071a() {
        return f7180a.m4460c().booleanValue();
    }
}
