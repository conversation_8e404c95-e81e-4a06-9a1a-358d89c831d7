package p152w2;

import android.os.Binder;
import android.os.Bundle;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.FutureTask;
import p153w3.C1798e;
import p158x2.AbstractBinderC2042z2;
import p158x2.BinderC2012v4;
import p158x2.C1838a7;
import p158x2.C1839b;
import p158x2.C1847b7;
import p158x2.C1915j3;
import p158x2.C1916j4;
import p158x2.C1932l4;
import p158x2.C1967q;
import p158x2.C2022w6;
import p158x2.C2038y6;
import p158x2.CallableC1862d4;
import p158x2.CallableC1996t4;
import p158x2.RunnableC1911j;
import p158x2.RunnableC1940m4;
import p158x2.RunnableC1956o4;
import p158x2.RunnableC1988s4;
import p158x2.RunnableC2035y3;

/* renamed from: w2.b0 */
/* loaded from: classes.dex */
public class BinderC1493b0 extends Binder implements IInterface {
    public BinderC1493b0() {
        attachInterface(this, "com.google.android.gms.measurement.internal.IMeasurementService");
    }

    @Override // android.os.IInterface
    public final IBinder asBinder() {
        return this;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // android.os.Binder
    public final boolean onTransact(int i6, Parcel parcel, Parcel parcel2, int i7) {
        ArrayList arrayList;
        byte[] bArr;
        List list;
        int i8 = 1;
        if (i6 <= 16777215) {
            parcel.enforceInterface(getInterfaceDescriptor());
        } else if (super.onTransact(i6, parcel, parcel2, i7)) {
            return true;
        }
        AbstractBinderC2042z2 abstractBinderC2042z2 = (AbstractBinderC2042z2) this;
        int i9 = 2;
        switch (i6) {
            case 1:
                C1967q c1967q = (C1967q) C1506c0.m3675a(parcel, C1967q.CREATOR);
                C1847b7 c1847b7 = (C1847b7) C1506c0.m3675a(parcel, C1847b7.CREATOR);
                BinderC2012v4 binderC2012v4 = (BinderC2012v4) abstractBinderC2042z2;
                Objects.requireNonNull(c1967q, "null reference");
                binderC2012v4.m5104C(c1847b7);
                binderC2012v4.m5106E(new RunnableC2035y3(binderC2012v4, c1967q, c1847b7, i9));
                parcel2.writeNoException();
                break;
            case 2:
                C2022w6 c2022w6 = (C2022w6) C1506c0.m3675a(parcel, C2022w6.CREATOR);
                C1847b7 c1847b72 = (C1847b7) C1506c0.m3675a(parcel, C1847b7.CREATOR);
                BinderC2012v4 binderC2012v42 = (BinderC2012v4) abstractBinderC2042z2;
                Objects.requireNonNull(c2022w6, "null reference");
                binderC2012v42.m5104C(c1847b72);
                binderC2012v42.m5106E(new RunnableC1956o4(binderC2012v42, c2022w6, c1847b72, i9));
                parcel2.writeNoException();
                break;
            case 4:
                C1847b7 c1847b73 = (C1847b7) C1506c0.m3675a(parcel, C1847b7.CREATOR);
                BinderC2012v4 binderC2012v43 = (BinderC2012v4) abstractBinderC2042z2;
                binderC2012v43.m5104C(c1847b73);
                binderC2012v43.m5106E(new RunnableC1988s4(binderC2012v43, c1847b73, i9));
                parcel2.writeNoException();
                break;
            case 5:
                C1967q c1967q2 = (C1967q) C1506c0.m3675a(parcel, C1967q.CREATOR);
                String readString = parcel.readString();
                parcel.readString();
                BinderC2012v4 binderC2012v44 = (BinderC2012v4) abstractBinderC2042z2;
                Objects.requireNonNull(c1967q2, "null reference");
                C1798e.m4554o(readString);
                binderC2012v44.m5105D(readString, true);
                binderC2012v44.m5106E(new RunnableC1956o4(binderC2012v44, c1967q2, readString));
                parcel2.writeNoException();
                break;
            case 6:
                C1847b7 c1847b74 = (C1847b7) C1506c0.m3675a(parcel, C1847b7.CREATOR);
                BinderC2012v4 binderC2012v45 = (BinderC2012v4) abstractBinderC2042z2;
                binderC2012v45.m5104C(c1847b74);
                binderC2012v45.m5106E(new RunnableC1940m4(binderC2012v45, c1847b74, i8));
                parcel2.writeNoException();
                break;
            case 7:
                C1847b7 c1847b75 = (C1847b7) C1506c0.m3675a(parcel, C1847b7.CREATOR);
                r7 = parcel.readInt() != 0 ? 1 : 0;
                BinderC2012v4 binderC2012v46 = (BinderC2012v4) abstractBinderC2042z2;
                binderC2012v46.m5104C(c1847b75);
                String str = c1847b75.f7595j;
                C1798e.m4560r(str);
                try {
                    List<C2038y6> list2 = (List) ((FutureTask) binderC2012v46.f8129a.mo4959b().m4917p(new CallableC1862d4(binderC2012v46, str, i8))).get();
                    ArrayList arrayList2 = new ArrayList(list2.size());
                    for (C2038y6 c2038y6 : list2) {
                        if (r7 != 0 || !C1838a7.m4700F(c2038y6.f8270c)) {
                            arrayList2.add(new C2022w6(c2038y6));
                        }
                    }
                    arrayList = arrayList2;
                } catch (InterruptedException | ExecutionException e6) {
                    binderC2012v46.f8129a.mo4962e().f7784p.m4843d("Failed to get user properties. appId", C1915j3.m4886t(c1847b75.f7595j), e6);
                    arrayList = null;
                }
                parcel2.writeNoException();
                parcel2.writeTypedList(arrayList);
                break;
            case 9:
                C1967q c1967q3 = (C1967q) C1506c0.m3675a(parcel, C1967q.CREATOR);
                String readString2 = parcel.readString();
                BinderC2012v4 binderC2012v47 = (BinderC2012v4) abstractBinderC2042z2;
                C1798e.m4554o(readString2);
                Objects.requireNonNull(c1967q3, "null reference");
                binderC2012v47.m5105D(readString2, true);
                binderC2012v47.f8129a.mo4962e().f7791w.m4842c("Log and bundle. event", binderC2012v47.f8129a.m5072J().m4794p(c1967q3.f7965j));
                Objects.requireNonNull((C1798e) binderC2012v47.f8129a.mo4963f());
                long nanoTime = System.nanoTime() / 1000000;
                C1932l4 mo4959b = binderC2012v47.f8129a.mo4959b();
                CallableC1996t4 callableC1996t4 = new CallableC1996t4(binderC2012v47, c1967q3, readString2);
                mo4959b.m5153l();
                C1916j4<?> c1916j4 = new C1916j4<>(mo4959b, callableC1996t4, true);
                if (Thread.currentThread() == mo4959b.f7834m) {
                    c1916j4.run();
                } else {
                    mo4959b.m4922u(c1916j4);
                }
                try {
                    byte[] bArr2 = (byte[]) c1916j4.get();
                    if (bArr2 == null) {
                        binderC2012v47.f8129a.mo4962e().f7784p.m4842c("Log and bundle returned null. appId", C1915j3.m4886t(readString2));
                        bArr2 = new byte[0];
                    }
                    Objects.requireNonNull((C1798e) binderC2012v47.f8129a.mo4963f());
                    binderC2012v47.f8129a.mo4962e().f7791w.m4844e("Log and bundle processed. event, size, time_ms", binderC2012v47.f8129a.m5072J().m4794p(c1967q3.f7965j), Integer.valueOf(bArr2.length), Long.valueOf((System.nanoTime() / 1000000) - nanoTime));
                    bArr = bArr2;
                } catch (InterruptedException | ExecutionException e7) {
                    binderC2012v47.f8129a.mo4962e().f7784p.m4844e("Failed to log and bundle. appId, event, error", C1915j3.m4886t(readString2), binderC2012v47.f8129a.m5072J().m4794p(c1967q3.f7965j), e7);
                    bArr = null;
                }
                parcel2.writeNoException();
                parcel2.writeByteArray(bArr);
                break;
            case 10:
                ((BinderC2012v4) abstractBinderC2042z2).mo4687l(parcel.readLong(), parcel.readString(), parcel.readString(), parcel.readString());
                parcel2.writeNoException();
                break;
            case 11:
                String mo4693v = ((BinderC2012v4) abstractBinderC2042z2).mo4693v((C1847b7) C1506c0.m3675a(parcel, C1847b7.CREATOR));
                parcel2.writeNoException();
                parcel2.writeString(mo4693v);
                break;
            case 12:
                ((BinderC2012v4) abstractBinderC2042z2).mo4685g((C1839b) C1506c0.m3675a(parcel, C1839b.CREATOR), (C1847b7) C1506c0.m3675a(parcel, C1847b7.CREATOR));
                parcel2.writeNoException();
                break;
            case 13:
                C1839b c1839b = (C1839b) C1506c0.m3675a(parcel, C1839b.CREATOR);
                BinderC2012v4 binderC2012v48 = (BinderC2012v4) abstractBinderC2042z2;
                Objects.requireNonNull(c1839b, "null reference");
                C1798e.m4560r(c1839b.f7562l);
                C1798e.m4554o(c1839b.f7560j);
                binderC2012v48.m5105D(c1839b.f7560j, true);
                binderC2012v48.m5106E(new RunnableC1911j(binderC2012v48, new C1839b(c1839b), i8));
                parcel2.writeNoException();
                break;
            case 14:
                String readString3 = parcel.readString();
                String readString4 = parcel.readString();
                int i10 = C1506c0.f6910a;
                list = ((BinderC2012v4) abstractBinderC2042z2).mo4683d(readString3, readString4, parcel.readInt() != 0, (C1847b7) C1506c0.m3675a(parcel, C1847b7.CREATOR));
                parcel2.writeNoException();
                parcel2.writeTypedList(list);
                break;
            case 15:
                String readString5 = parcel.readString();
                String readString6 = parcel.readString();
                String readString7 = parcel.readString();
                int i11 = C1506c0.f6910a;
                list = ((BinderC2012v4) abstractBinderC2042z2).mo4695z(readString5, readString6, readString7, parcel.readInt() != 0);
                parcel2.writeNoException();
                parcel2.writeTypedList(list);
                break;
            case 16:
                list = ((BinderC2012v4) abstractBinderC2042z2).mo4690p(parcel.readString(), parcel.readString(), (C1847b7) C1506c0.m3675a(parcel, C1847b7.CREATOR));
                parcel2.writeNoException();
                parcel2.writeTypedList(list);
                break;
            case 17:
                list = ((BinderC2012v4) abstractBinderC2042z2).mo4692u(parcel.readString(), parcel.readString(), parcel.readString());
                parcel2.writeNoException();
                parcel2.writeTypedList(list);
                break;
            case 18:
                C1847b7 c1847b76 = (C1847b7) C1506c0.m3675a(parcel, C1847b7.CREATOR);
                BinderC2012v4 binderC2012v49 = (BinderC2012v4) abstractBinderC2042z2;
                C1798e.m4554o(c1847b76.f7595j);
                binderC2012v49.m5105D(c1847b76.f7595j, false);
                binderC2012v49.m5106E(new RunnableC1988s4(binderC2012v49, c1847b76, r7));
                parcel2.writeNoException();
                break;
            case 19:
                Bundle bundle = (Bundle) C1506c0.m3675a(parcel, Bundle.CREATOR);
                C1847b7 c1847b77 = (C1847b7) C1506c0.m3675a(parcel, C1847b7.CREATOR);
                BinderC2012v4 binderC2012v410 = (BinderC2012v4) abstractBinderC2042z2;
                binderC2012v410.m5104C(c1847b77);
                String str2 = c1847b77.f7595j;
                C1798e.m4560r(str2);
                binderC2012v410.m5106E(new RunnableC1956o4(binderC2012v410, str2, bundle, r7));
                parcel2.writeNoException();
                break;
            case 20:
                ((BinderC2012v4) abstractBinderC2042z2).mo4688m((C1847b7) C1506c0.m3675a(parcel, C1847b7.CREATOR));
                parcel2.writeNoException();
                break;
        }
        return true;
    }
}
