package p152w2;

import java.util.ArrayList;
import java.util.List;
import p008b0.C0385m;
import p090n.C1090c;

/* renamed from: w2.l5 */
/* loaded from: classes.dex */
public final class C1620l5 extends C1626m {

    /* renamed from: k */
    public final C1505c f7070k;

    public C1620l5(C1505c c1505c) {
        this.f7070k = c1505c;
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Type inference failed for: r1v16, types: [java.util.HashMap, java.util.Map, java.util.Map<java.lang.String, java.lang.Object>] */
    /* JADX WARN: Type inference failed for: r2v19, types: [java.util.HashMap, java.util.Map<java.lang.String, java.lang.Object>] */
    @Override // p152w2.C1626m, p152w2.InterfaceC1662p
    /* renamed from: s */
    public final InterfaceC1662p mo3772s(String str, C1090c c1090c, List<InterfaceC1662p> list) {
        char c;
        switch (str.hashCode()) {
            case 21624207:
                if (str.equals("getEventName")) {
                    c = 0;
                    break;
                }
                c = 65535;
                break;
            case 45521504:
                if (str.equals("getTimestamp")) {
                    c = 3;
                    break;
                }
                c = 65535;
                break;
            case 146575578:
                if (str.equals("getParamValue")) {
                    c = 1;
                    break;
                }
                c = 65535;
                break;
            case 700587132:
                if (str.equals("getParams")) {
                    c = 2;
                    break;
                }
                c = 65535;
                break;
            case 920706790:
                if (str.equals("setParamValue")) {
                    c = 5;
                    break;
                }
                c = 65535;
                break;
            case 1570616835:
                if (str.equals("setEventName")) {
                    c = 4;
                    break;
                }
                c = 65535;
                break;
            default:
                c = 65535;
                break;
        }
        if (c == 0) {
            C0385m.m1426r("getEventName", 0, list);
            return new C1698s(((C1492b) this.f7070k.f6908c).f6878a);
        }
        if (c == 1) {
            C0385m.m1426r("getParamValue", 1, list);
            return C0385m.m1425q(((C1492b) this.f7070k.f6908c).m3652a(c1090c.m2803b((InterfaceC1662p) ((ArrayList) list).get(0)).mo3761c()));
        }
        if (c == 2) {
            C0385m.m1426r("getParams", 0, list);
            ?? r12 = ((C1492b) this.f7070k.f6908c).f6880c;
            C1626m c1626m = new C1626m();
            for (String str2 : r12.keySet()) {
                c1626m.mo3764i(str2, C0385m.m1425q(r12.get(str2)));
            }
            return c1626m;
        }
        if (c == 3) {
            C0385m.m1426r("getTimestamp", 0, list);
            return new C1578i(Double.valueOf(((C1492b) this.f7070k.f6908c).f6879b));
        }
        if (c == 4) {
            C0385m.m1426r("setEventName", 1, list);
            InterfaceC1662p m2803b = c1090c.m2803b((InterfaceC1662p) ((ArrayList) list).get(0));
            if (InterfaceC1662p.f7126b.equals(m2803b) || InterfaceC1662p.f7127c.equals(m2803b)) {
                throw new IllegalArgumentException("Illegal event name");
            }
            ((C1492b) this.f7070k.f6908c).f6878a = m2803b.mo3761c();
            return new C1698s(m2803b.mo3761c());
        }
        if (c != 5) {
            return super.mo3772s(str, c1090c, list);
        }
        C0385m.m1426r("setParamValue", 2, list);
        ArrayList arrayList = (ArrayList) list;
        String mo3761c = c1090c.m2803b((InterfaceC1662p) arrayList.get(0)).mo3761c();
        InterfaceC1662p m2803b2 = c1090c.m2803b((InterfaceC1662p) arrayList.get(1));
        C1492b c1492b = (C1492b) this.f7070k.f6908c;
        Object m1410G = C0385m.m1410G(m2803b2);
        ?? r22 = c1492b.f6880c;
        if (m1410G == null) {
            r22.remove(mo3761c);
        } else {
            r22.put(mo3761c, m1410G);
        }
        return m2803b2;
    }
}
