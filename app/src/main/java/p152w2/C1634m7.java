package p152w2;

/* renamed from: w2.m7 */
/* loaded from: classes.dex */
public final class C1634m7 implements InterfaceC1622l7 {

    /* renamed from: a */
    public static final AbstractC1773y2<Boolean> f7091a;

    /* renamed from: b */
    public static final AbstractC1773y2<Boolean> f7092b;

    static {
        C1749w2 c1749w2 = new C1749w2(C1713t2.m4275a());
        f7091a = (C1737v2) c1749w2.m4355b("measurement.frontend.directly_maybe_log_error_events", false);
        f7092b = (C1737v2) c1749w2.m4355b("measurement.upload.directly_maybe_log_error_events", true);
        c1749w2.m4354a("measurement.id.frontend.directly_maybe_log_error_events", 0L);
    }

    @Override // p152w2.InterfaceC1622l7
    /* renamed from: a */
    public final boolean mo3904a() {
        return f7091a.m4460c().booleanValue();
    }

    @Override // p152w2.InterfaceC1622l7
    /* renamed from: b */
    public final boolean mo3905b() {
        return f7092b.m4460c().booleanValue();
    }
}
