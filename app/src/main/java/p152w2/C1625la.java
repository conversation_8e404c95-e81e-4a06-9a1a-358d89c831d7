package p152w2;

/* renamed from: w2.la */
/* loaded from: classes.dex */
public final class C1625la implements InterfaceC1613ka {

    /* renamed from: a */
    public static final AbstractC1773y2<Boolean> f7077a = (C1737v2) new C1749w2(C1713t2.m4275a()).m4355b("measurement.scheduler.task_thread.cleanup_on_exit", false);

    @Override // p152w2.InterfaceC1613ka
    /* renamed from: a */
    public final boolean mo3890a() {
        return f7077a.m4460c().booleanValue();
    }
}
