package p152w2;

import java.util.Arrays;

/* renamed from: w2.r6 */
/* loaded from: classes.dex */
public final class C1693r6 extends AbstractC1669p6<C1681q6, C1681q6> {
    @Override // p152w2.AbstractC1669p6
    /* renamed from: a */
    public final /* bridge */ /* synthetic */ void mo4011a(C1681q6 c1681q6, int i6, long j6) {
        c1681q6.m4029c(i6 << 3, Long.valueOf(j6));
    }

    @Override // p152w2.AbstractC1669p6
    /* renamed from: b */
    public final /* bridge */ /* synthetic */ C1681q6 mo4012b() {
        return C1681q6.m4027a();
    }

    @Override // p152w2.AbstractC1669p6
    /* renamed from: c */
    public final /* bridge */ /* synthetic */ void mo4013c(Object obj, C1681q6 c1681q6) {
        ((AbstractC1691r4) obj).zzc = c1681q6;
    }

    @Override // p152w2.AbstractC1669p6
    /* renamed from: d */
    public final /* bridge */ /* synthetic */ C1681q6 mo4014d(Object obj) {
        return ((AbstractC1691r4) obj).zzc;
    }

    @Override // p152w2.AbstractC1669p6
    /* renamed from: e */
    public final void mo4015e(Object obj) {
        ((AbstractC1691r4) obj).zzc.f7155e = false;
    }

    @Override // p152w2.AbstractC1669p6
    /* renamed from: f */
    public final C1681q6 mo4016f(C1681q6 c1681q6, C1681q6 c1681q62) {
        C1681q6 c1681q63 = c1681q62;
        if (c1681q63.equals(C1681q6.f7150f)) {
            return c1681q6;
        }
        C1681q6 c1681q64 = c1681q6;
        int i6 = c1681q64.f7151a + c1681q63.f7151a;
        int[] copyOf = Arrays.copyOf(c1681q64.f7152b, i6);
        System.arraycopy(c1681q63.f7152b, 0, copyOf, c1681q64.f7151a, c1681q63.f7151a);
        Object[] copyOf2 = Arrays.copyOf(c1681q64.f7153c, i6);
        System.arraycopy(c1681q63.f7153c, 0, copyOf2, c1681q64.f7151a, c1681q63.f7151a);
        return new C1681q6(i6, copyOf, copyOf2, true);
    }

    @Override // p152w2.AbstractC1669p6
    /* renamed from: g */
    public final int mo4017g(C1681q6 c1681q6) {
        C1681q6 c1681q62 = c1681q6;
        int i6 = c1681q62.f7154d;
        if (i6 != -1) {
            return i6;
        }
        int i7 = 0;
        for (int i8 = 0; i8 < c1681q62.f7151a; i8++) {
            int i9 = c1681q62.f7152b[i8];
            AbstractC1750w3 abstractC1750w3 = (AbstractC1750w3) c1681q62.f7153c[i8];
            int m4480c0 = AbstractC1786z3.m4480c0(8);
            int mo4287g = abstractC1750w3.mo4287g();
            i7 += AbstractC1786z3.m4480c0(mo4287g) + mo4287g + AbstractC1786z3.m4480c0(24) + AbstractC1786z3.m4480c0(i9 >>> 3) + AbstractC1786z3.m4480c0(16) + m4480c0 + m4480c0;
        }
        c1681q62.f7154d = i7;
        return i7;
    }

    @Override // p152w2.AbstractC1669p6
    /* renamed from: h */
    public final /* bridge */ /* synthetic */ int mo4018h(C1681q6 c1681q6) {
        return c1681q6.m4028b();
    }

    @Override // p152w2.AbstractC1669p6
    /* renamed from: i */
    public final /* bridge */ /* synthetic */ void mo4019i(C1681q6 c1681q6, C1484a4 c1484a4) {
        c1681q6.m4030d(c1484a4);
    }
}
