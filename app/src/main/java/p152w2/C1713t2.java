package p152w2;

import android.net.Uri;
import p077l.C1047a;

/* renamed from: w2.t2 */
/* loaded from: classes.dex */
public final class C1713t2 {

    /* renamed from: a */
    public static final C1047a<String, Uri> f7183a = new C1047a<>();

    /* renamed from: a */
    public static synchronized Uri m4275a() {
        Uri orDefault;
        synchronized (C1713t2.class) {
            C1047a<String, Uri> c1047a = f7183a;
            orDefault = c1047a.getOrDefault("com.google.android.gms.measurement", null);
            if (orDefault == null) {
                String valueOf = String.valueOf(Uri.encode("com.google.android.gms.measurement"));
                orDefault = Uri.parse(valueOf.length() != 0 ? "content://com.google.android.gms.phenotype/".concat(valueOf) : new String("content://com.google.android.gms.phenotype/"));
                c1047a.put("com.google.android.gms.measurement", orDefault);
            }
        }
        return orDefault;
    }
}
