package p152w2;

import java.util.Iterator;
import p158x2.C1951o;

/* renamed from: w2.t6 */
/* loaded from: classes.dex */
public final class C1717t6 implements Iterator {

    /* renamed from: j */
    public final /* synthetic */ int f7184j = 0;

    /* renamed from: k */
    public final Iterator f7185k;

    /* renamed from: l */
    public final /* synthetic */ Iterable f7186l;

    public C1717t6(C1729u6 c1729u6) {
        this.f7186l = c1729u6;
        this.f7185k = c1729u6.f7195j.iterator();
    }

    /* renamed from: a */
    public final String m4276a() {
        return (String) this.f7185k.next();
    }

    @Override // java.util.Iterator
    public final boolean hasNext() {
        switch (this.f7184j) {
        }
        return this.f7185k.hasNext();
    }

    @Override // java.util.Iterator
    public final /* bridge */ /* synthetic */ Object next() {
        switch (this.f7184j) {
            case 0:
                return (String) this.f7185k.next();
            default:
                return m4276a();
        }
    }

    @Override // java.util.Iterator
    public final void remove() {
        switch (this.f7184j) {
            case 0:
                throw new UnsupportedOperationException();
            default:
                throw new UnsupportedOperationException("Remove not supported");
        }
    }

    public C1717t6(C1951o c1951o) {
        this.f7186l = c1951o;
        this.f7185k = c1951o.f7942j.keySet().iterator();
    }
}
