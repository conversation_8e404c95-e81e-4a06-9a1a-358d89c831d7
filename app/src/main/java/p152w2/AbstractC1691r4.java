package p152w2;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import p152w2.AbstractC1691r4;
import p152w2.C1643n4;

/* renamed from: w2.r4 */
/* loaded from: classes.dex */
public abstract class AbstractC1691r4<MessageType extends AbstractC1691r4<MessageType, BuilderType>, BuilderType extends C1643n4<MessageType, BuilderType>> extends AbstractC1642n3<MessageType, BuilderType> {
    private static final Map<Object, AbstractC1691r4<?, ?>> zza = new ConcurrentHashMap();
    public C1681q6 zzc = C1681q6.f7150f;
    public int zzd = -1;

    /* renamed from: j */
    public static Object m4057j(Method method, Object obj, Object... objArr) {
        try {
            return method.invoke(obj, objArr);
        } catch (IllegalAccessException e6) {
            throw new RuntimeException("Couldn't use Java reflection to implement protocol message reflection.", e6);
        } catch (InvocationTargetException e7) {
            Throwable cause = e7.getCause();
            if (cause instanceof RuntimeException) {
                throw ((RuntimeException) cause);
            }
            if (cause instanceof Error) {
                throw ((Error) cause);
            }
            throw new RuntimeException("Unexpected exception thrown by generated accessor method.", cause);
        }
    }

    /* renamed from: k */
    public static InterfaceC1751w4 m4058k(InterfaceC1751w4 interfaceC1751w4) {
        C1584i5 c1584i5 = (C1584i5) interfaceC1751w4;
        int i6 = c1584i5.f7011l;
        return c1584i5.mo3662k(i6 == 0 ? 10 : i6 + i6);
    }

    /* renamed from: l */
    public static <E> InterfaceC1763x4<E> m4059l(InterfaceC1763x4<E> interfaceC1763x4) {
        int size = interfaceC1763x4.size();
        return interfaceC1763x4.mo3662k(size == 0 ? 10 : size + size);
    }

    /* renamed from: p */
    public static <T extends AbstractC1691r4> T m4060p(Class<T> cls) {
        Map<Object, AbstractC1691r4<?, ?>> map = zza;
        AbstractC1691r4<?, ?> abstractC1691r4 = map.get(cls);
        if (abstractC1691r4 == null) {
            try {
                Class.forName(cls.getName(), true, cls.getClassLoader());
                abstractC1691r4 = map.get(cls);
            } catch (ClassNotFoundException e6) {
                throw new IllegalStateException("Class initialization cannot fail.", e6);
            }
        }
        if (abstractC1691r4 == null) {
            abstractC1691r4 = (AbstractC1691r4) ((AbstractC1691r4) C1789z6.m4487e(cls)).mo3604r(6);
            if (abstractC1691r4 == null) {
                throw new IllegalStateException();
            }
            map.put(cls, abstractC1691r4);
        }
        return abstractC1691r4;
    }

    /* renamed from: q */
    public static <T extends AbstractC1691r4> void m4061q(Class<T> cls, T t) {
        zza.put(cls, t);
    }

    @Override // p152w2.InterfaceC1716t5
    /* renamed from: b */
    public final int mo4062b() {
        int i6 = this.zzd;
        if (i6 != -1) {
            return i6;
        }
        int mo3751d = C1499b6.f6896c.m3663a(getClass()).mo3751d(this);
        this.zzd = mo3751d;
        return mo3751d;
    }

    @Override // p152w2.InterfaceC1716t5
    /* renamed from: c */
    public final /* bridge */ /* synthetic */ AbstractC1630m3 mo4063c() {
        return (C1643n4) mo3604r(5);
    }

    @Override // p152w2.InterfaceC1728u5
    /* renamed from: d */
    public final /* bridge */ /* synthetic */ InterfaceC1716t5 mo3946d() {
        return (AbstractC1691r4) mo3604r(6);
    }

    @Override // p152w2.InterfaceC1716t5
    /* renamed from: e */
    public final /* bridge */ /* synthetic */ AbstractC1630m3 mo4064e() {
        C1643n4 c1643n4 = (C1643n4) mo3604r(5);
        c1643n4.m3948g(this);
        return c1643n4;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj != null && getClass() == obj.getClass()) {
            return C1499b6.f6896c.m3663a(getClass()).mo3754g(this, (AbstractC1691r4) obj);
        }
        return false;
    }

    @Override // p152w2.AbstractC1642n3
    /* renamed from: g */
    public final int mo3944g() {
        return this.zzd;
    }

    @Override // p152w2.AbstractC1642n3
    /* renamed from: h */
    public final void mo3945h(int i6) {
        this.zzd = i6;
    }

    public final int hashCode() {
        int i6 = this.zzb;
        if (i6 != 0) {
            return i6;
        }
        int mo3750c = C1499b6.f6896c.m3663a(getClass()).mo3750c(this);
        this.zzb = mo3750c;
        return mo3750c;
    }

    /* renamed from: m */
    public final <MessageType extends AbstractC1691r4<MessageType, BuilderType>, BuilderType extends C1643n4<MessageType, BuilderType>> BuilderType m4065m() {
        return (BuilderType) mo3604r(5);
    }

    /* renamed from: n */
    public final BuilderType m4066n() {
        BuilderType buildertype = (BuilderType) mo3604r(5);
        buildertype.m3948g(this);
        return buildertype;
    }

    /* renamed from: o */
    public final void m4067o(AbstractC1786z3 abstractC1786z3) {
        InterfaceC1537e6 m3663a = C1499b6.f6896c.m3663a(getClass());
        C1484a4 c1484a4 = abstractC1786z3.f7304G0;
        if (c1484a4 == null) {
            c1484a4 = new C1484a4(abstractC1786z3);
        }
        m3663a.mo3752e(this, c1484a4);
    }

    /* renamed from: r */
    public abstract Object mo3604r(int i6);

    public final String toString() {
        String obj = super.toString();
        StringBuilder sb = new StringBuilder();
        sb.append("# ");
        sb.append(obj);
        C1740v5.m4333b(this, sb, 0);
        return sb.toString();
    }
}
