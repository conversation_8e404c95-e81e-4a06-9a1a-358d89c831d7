package p152w2;

import java.util.Collections;
import java.util.List;

/* renamed from: w2.l1 */
/* loaded from: classes.dex */
public final class C1616l1 extends C1643n4<C1628m1, C1616l1> {
    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public C1616l1() {
        /*
            r1 = this;
            w2.m1 r0 = p152w2.C1628m1.m3912D()
            r1.<init>(r0)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1616l1.<init>():void");
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public /* synthetic */ C1616l1(p008b0.C0385m r1) {
        /*
            r0 = this;
            w2.m1 r1 = p152w2.C1628m1.m3912D()
            r0.<init>(r1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1616l1.<init>(b0.m):void");
    }

    /* renamed from: l */
    public final List<C1664p1> m3891l() {
        return Collections.unmodifiableList(((C1628m1) this.f7099k).m3924s());
    }

    /* renamed from: m */
    public final int m3892m() {
        return ((C1628m1) this.f7099k).m3925t();
    }

    /* renamed from: n */
    public final C1664p1 m3893n(int i6) {
        return ((C1628m1) this.f7099k).m3926u(i6);
    }

    /* renamed from: o */
    public final C1616l1 m3894o(int i6, C1664p1 c1664p1) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1628m1.m3913E((C1628m1) this.f7099k, i6, c1664p1);
        return this;
    }

    /* renamed from: p */
    public final C1616l1 m3895p(C1652o1 c1652o1) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1628m1.m3914F((C1628m1) this.f7099k, c1652o1.m3947f());
        return this;
    }

    /* renamed from: q */
    public final C1616l1 m3896q(int i6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1628m1.m3917I((C1628m1) this.f7099k, i6);
        return this;
    }

    /* renamed from: r */
    public final String m3897r() {
        return ((C1628m1) this.f7099k).m3927v();
    }

    /* renamed from: s */
    public final C1616l1 m3898s(String str) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1628m1.m3918J((C1628m1) this.f7099k, str);
        return this;
    }

    /* renamed from: t */
    public final long m3899t() {
        return ((C1628m1) this.f7099k).m3929x();
    }

    /* renamed from: u */
    public final long m3900u() {
        return ((C1628m1) this.f7099k).m3931z();
    }
}
