package p152w2;

import java.util.List;
import java.util.Objects;

/* renamed from: w2.p1 */
/* loaded from: classes.dex */
public final class C1664p1 extends AbstractC1691r4<C1664p1, C1652o1> implements InterfaceC1728u5 {
    private static final C1664p1 zzk;
    private int zza;
    private long zzg;
    private float zzh;
    private double zzi;
    private String zze = "";
    private String zzf = "";
    private InterfaceC1763x4<C1664p1> zzj = C1512c6.f6916m;

    static {
        C1664p1 c1664p1 = new C1664p1();
        zzk = c1664p1;
        AbstractC1691r4.m4061q(C1664p1.class, c1664p1);
    }

    /* renamed from: E */
    public static C1652o1 m3984E() {
        return zzk.m4065m();
    }

    /* renamed from: G */
    public static /* synthetic */ void m3986G(C1664p1 c1664p1, String str) {
        Objects.requireNonNull(str);
        c1664p1.zza |= 1;
        c1664p1.zze = str;
    }

    /* renamed from: H */
    public static /* synthetic */ void m3987H(C1664p1 c1664p1, String str) {
        Objects.requireNonNull(str);
        c1664p1.zza |= 2;
        c1664p1.zzf = str;
    }

    /* renamed from: I */
    public static /* synthetic */ void m3988I(C1664p1 c1664p1) {
        c1664p1.zza &= -3;
        c1664p1.zzf = zzk.zzf;
    }

    /* renamed from: J */
    public static /* synthetic */ void m3989J(C1664p1 c1664p1, long j6) {
        c1664p1.zza |= 4;
        c1664p1.zzg = j6;
    }

    /* renamed from: K */
    public static /* synthetic */ void m3990K(C1664p1 c1664p1) {
        c1664p1.zza &= -5;
        c1664p1.zzg = 0L;
    }

    /* renamed from: L */
    public static /* synthetic */ void m3991L(C1664p1 c1664p1, double d6) {
        c1664p1.zza |= 16;
        c1664p1.zzi = d6;
    }

    /* renamed from: M */
    public static /* synthetic */ void m3992M(C1664p1 c1664p1) {
        c1664p1.zza &= -17;
        c1664p1.zzi = 0.0d;
    }

    /* renamed from: N */
    public static void m3993N(C1664p1 c1664p1, C1664p1 c1664p12) {
        InterfaceC1763x4<C1664p1> interfaceC1763x4 = c1664p1.zzj;
        if (!interfaceC1763x4.mo3965a()) {
            c1664p1.zzj = AbstractC1691r4.m4059l(interfaceC1763x4);
        }
        c1664p1.zzj.add(c1664p12);
    }

    /* renamed from: O */
    public static void m3994O(C1664p1 c1664p1, Iterable iterable) {
        InterfaceC1763x4<C1664p1> interfaceC1763x4 = c1664p1.zzj;
        if (!interfaceC1763x4.mo3965a()) {
            c1664p1.zzj = AbstractC1691r4.m4059l(interfaceC1763x4);
        }
        AbstractC1642n3.m3941i(iterable, c1664p1.zzj);
    }

    /* renamed from: P */
    public static void m3995P(C1664p1 c1664p1) {
        c1664p1.zzj = C1512c6.f6916m;
    }

    /* renamed from: A */
    public final boolean m3996A() {
        return (this.zza & 16) != 0;
    }

    /* renamed from: B */
    public final double m3997B() {
        return this.zzi;
    }

    /* renamed from: C */
    public final List<C1664p1> m3998C() {
        return this.zzj;
    }

    /* renamed from: D */
    public final int m3999D() {
        return this.zzj.size();
    }

    @Override // p152w2.AbstractC1691r4
    /* renamed from: r */
    public final Object mo3604r(int i6) {
        int i7 = i6 - 1;
        if (i7 == 0) {
            return (byte) 1;
        }
        if (i7 == 2) {
            return new C1525d6(zzk, "\u0001\u0006\u0000\u0001\u0001\u0006\u0006\u0000\u0001\u0000\u0001ဈ\u0000\u0002ဈ\u0001\u0003ဂ\u0002\u0004ခ\u0003\u0005က\u0004\u0006\u001b", new Object[]{"zza", "zze", "zzf", "zzg", "zzh", "zzi", "zzj", C1664p1.class});
        }
        if (i7 == 3) {
            return new C1664p1();
        }
        if (i7 == 4) {
            return new C1652o1(null);
        }
        if (i7 != 5) {
            return null;
        }
        return zzk;
    }

    /* renamed from: s */
    public final boolean m4000s() {
        return (this.zza & 1) != 0;
    }

    /* renamed from: t */
    public final String m4001t() {
        return this.zze;
    }

    /* renamed from: u */
    public final boolean m4002u() {
        return (this.zza & 2) != 0;
    }

    /* renamed from: v */
    public final String m4003v() {
        return this.zzf;
    }

    /* renamed from: w */
    public final boolean m4004w() {
        return (this.zza & 4) != 0;
    }

    /* renamed from: x */
    public final long m4005x() {
        return this.zzg;
    }

    /* renamed from: y */
    public final boolean m4006y() {
        return (this.zza & 8) != 0;
    }

    /* renamed from: z */
    public final float m4007z() {
        return this.zzh;
    }
}
