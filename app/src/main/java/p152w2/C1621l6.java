package p152w2;

import java.util.Iterator;
import java.util.Map;

/* renamed from: w2.l6 */
/* loaded from: classes.dex */
public final class C1621l6 implements Iterator<Map.Entry> {

    /* renamed from: j */
    public int f7071j = -1;

    /* renamed from: k */
    public boolean f7072k;

    /* renamed from: l */
    public Iterator<Map.Entry> f7073l;

    /* renamed from: m */
    public final /* synthetic */ C1645n6 f7074m;

    /* renamed from: a */
    public final Iterator<Map.Entry> m3903a() {
        if (this.f7073l == null) {
            this.f7073l = this.f7074m.f7104l.entrySet().iterator();
        }
        return this.f7073l;
    }

    @Override // java.util.Iterator
    public final boolean hasNext() {
        if (this.f7071j + 1 >= this.f7074m.f7103k.size()) {
            return !this.f7074m.f7104l.isEmpty() && m3903a().hasNext();
        }
        return true;
    }

    @Override // java.util.Iterator
    public final /* synthetic */ Map.Entry next() {
        this.f7072k = true;
        int i6 = this.f7071j + 1;
        this.f7071j = i6;
        return (Map.Entry) (i6 < this.f7074m.f7103k.size() ? this.f7074m.f7103k.get(this.f7071j) : m3903a().next());
    }

    @Override // java.util.Iterator
    public final void remove() {
        if (!this.f7072k) {
            throw new IllegalStateException("remove() was called before next()");
        }
        this.f7072k = false;
        C1645n6 c1645n6 = this.f7074m;
        int i6 = C1645n6.f7101p;
        c1645n6.m3958g();
        if (this.f7071j >= this.f7074m.f7103k.size()) {
            m3903a().remove();
            return;
        }
        C1645n6 c1645n62 = this.f7074m;
        int i7 = this.f7071j;
        this.f7071j = i7 - 1;
        c1645n62.m3956e(i7);
    }
}
