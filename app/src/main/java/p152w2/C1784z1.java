package p152w2;

/* renamed from: w2.z1 */
/* loaded from: classes.dex */
public final class C1784z1 extends C1643n4<C1482a2, C1784z1> {
    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public C1784z1() {
        /*
            r1 = this;
            w2.a2 r0 = p152w2.C1482a2.m3615y()
            r1.<init>(r0)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1784z1.<init>():void");
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public /* synthetic */ C1784z1(p008b0.C0385m r1) {
        /*
            r0 = this;
            w2.a2 r1 = p152w2.C1482a2.m3615y()
            r0.<init>(r1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1784z1.<init>(b0.m):void");
    }
}
