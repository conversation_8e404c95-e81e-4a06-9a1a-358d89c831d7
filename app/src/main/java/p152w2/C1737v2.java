package p152w2;

import android.util.Log;

/* renamed from: w2.v2 */
/* loaded from: classes.dex */
public final class C1737v2 extends AbstractC1773y2 {

    /* renamed from: i */
    public final /* synthetic */ int f7202i;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public /* synthetic */ C1737v2(C1749w2 c1749w2, String str, Object obj, int i6) {
        super(c1749w2, str, obj);
        this.f7202i = i6;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // p152w2.AbstractC1773y2
    /* renamed from: a */
    public final /* bridge */ /* synthetic */ Object mo4284a(Object obj) {
        switch (this.f7202i) {
            case 0:
                if (C1593j2.f7022b.matcher(obj).matches()) {
                    return Boolean.TRUE;
                }
                if (C1593j2.f7023c.matcher(obj).matches()) {
                    return Boolean.FALSE;
                }
                String m4459b = m4459b();
                String str = (String) obj;
                StringBuilder sb = new StringBuilder(String.valueOf(m4459b).length() + 28 + str.length());
                sb.append("Invalid boolean value for ");
                sb.append(m4459b);
                sb.append(": ");
                sb.append(str);
                Log.e("PhenotypeFlag", sb.toString());
                return null;
            default:
                return obj;
        }
    }
}
