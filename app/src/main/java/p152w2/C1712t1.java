package p152w2;

import android.os.Build;
import java.util.List;
import java.util.Objects;
import java.util.RandomAccess;

/* renamed from: w2.t1 */
/* loaded from: classes.dex */
public final class C1712t1 extends AbstractC1691r4<C1712t1, C1700s1> implements InterfaceC1728u5 {
    public static final /* synthetic */ int zza = 0;
    private static final C1712t1 zzaa;
    private String zzA;
    private long zzB;
    private int zzC;
    private String zzD;
    private String zzE;
    private boolean zzF;
    private InterfaceC1763x4<C1580i1> zzG;
    private String zzH;
    private int zzI;
    private int zzJ;
    private int zzK;
    private String zzL;
    private long zzM;
    private long zzN;
    private String zzO;
    private String zzP;
    private int zzQ;
    private String zzR;
    private C1748w1 zzS;
    private InterfaceC1739v4 zzT;
    private long zzU;
    private long zzV;
    private String zzW;
    private String zzX;
    private int zzY;
    private boolean zzZ;
    private int zze;
    private int zzf;
    private int zzg;
    private InterfaceC1763x4<C1628m1> zzh;
    private InterfaceC1763x4<C1508c2> zzi;
    private long zzj;
    private long zzk;
    private long zzl;
    private long zzm;
    private long zzn;
    private String zzo;
    private String zzp;
    private String zzq;
    private String zzr;
    private int zzs;
    private String zzt;
    private String zzu;
    private String zzv;
    private long zzw;
    private long zzx;
    private String zzy;
    private boolean zzz;

    static {
        C1712t1 c1712t1 = new C1712t1();
        zzaa = c1712t1;
        AbstractC1691r4.m4061q(C1712t1.class, c1712t1);
    }

    public C1712t1() {
        C1512c6<Object> c1512c6 = C1512c6.f6916m;
        this.zzh = c1512c6;
        this.zzi = c1512c6;
        this.zzo = "";
        this.zzp = "";
        this.zzq = "";
        this.zzr = "";
        this.zzt = "";
        this.zzu = "";
        this.zzv = "";
        this.zzy = "";
        this.zzA = "";
        this.zzD = "";
        this.zzE = "";
        this.zzG = c1512c6;
        this.zzH = "";
        this.zzL = "";
        this.zzO = "";
        this.zzP = "";
        this.zzR = "";
        this.zzT = C1703s4.f7175m;
        this.zzW = "";
        this.zzX = "";
    }

    /* renamed from: C0 */
    public static C1700s1 m4158C0() {
        return zzaa.m4065m();
    }

    /* renamed from: E0 */
    public static /* synthetic */ void m4160E0(C1712t1 c1712t1) {
        c1712t1.zze |= 1;
        c1712t1.zzg = 1;
    }

    /* renamed from: F0 */
    public static /* synthetic */ void m4161F0(C1712t1 c1712t1, int i6, C1628m1 c1628m1) {
        c1712t1.m4240T0();
        c1712t1.zzh.set(i6, c1628m1);
    }

    /* renamed from: G0 */
    public static /* synthetic */ void m4162G0(C1712t1 c1712t1, C1628m1 c1628m1) {
        c1712t1.m4240T0();
        c1712t1.zzh.add(c1628m1);
    }

    /* renamed from: H0 */
    public static /* synthetic */ void m4163H0(C1712t1 c1712t1, Iterable iterable) {
        c1712t1.m4240T0();
        AbstractC1642n3.m3941i(iterable, c1712t1.zzh);
    }

    /* renamed from: I0 */
    public static void m4164I0(C1712t1 c1712t1) {
        c1712t1.zzh = C1512c6.f6916m;
    }

    /* renamed from: J0 */
    public static /* synthetic */ void m4165J0(C1712t1 c1712t1, int i6) {
        c1712t1.m4240T0();
        c1712t1.zzh.remove(i6);
    }

    /* renamed from: K0 */
    public static /* synthetic */ void m4166K0(C1712t1 c1712t1, int i6, C1508c2 c1508c2) {
        c1712t1.m4241U0();
        c1712t1.zzi.set(i6, c1508c2);
    }

    /* renamed from: L0 */
    public static /* synthetic */ void m4167L0(C1712t1 c1712t1, C1508c2 c1508c2) {
        c1712t1.m4241U0();
        c1712t1.zzi.add(c1508c2);
    }

    /* renamed from: M0 */
    public static /* synthetic */ void m4168M0(C1712t1 c1712t1, int i6) {
        c1712t1.m4241U0();
        c1712t1.zzi.remove(i6);
    }

    /* renamed from: N0 */
    public static /* synthetic */ void m4169N0(C1712t1 c1712t1, long j6) {
        c1712t1.zze |= 2;
        c1712t1.zzj = j6;
    }

    /* renamed from: O0 */
    public static /* synthetic */ void m4170O0(C1712t1 c1712t1, long j6) {
        c1712t1.zze |= 4;
        c1712t1.zzk = j6;
    }

    /* renamed from: P0 */
    public static /* synthetic */ void m4171P0(C1712t1 c1712t1, long j6) {
        c1712t1.zze |= 8;
        c1712t1.zzl = j6;
    }

    /* renamed from: Q0 */
    public static /* synthetic */ void m4172Q0(C1712t1 c1712t1, long j6) {
        c1712t1.zze |= 16;
        c1712t1.zzm = j6;
    }

    /* renamed from: R0 */
    public static /* synthetic */ void m4173R0(C1712t1 c1712t1) {
        c1712t1.zze &= -17;
        c1712t1.zzm = 0L;
    }

    /* renamed from: T */
    public static /* synthetic */ void m4174T(C1712t1 c1712t1, long j6) {
        c1712t1.zze |= 32;
        c1712t1.zzn = j6;
    }

    /* renamed from: U */
    public static /* synthetic */ void m4175U(C1712t1 c1712t1) {
        c1712t1.zze &= -33;
        c1712t1.zzn = 0L;
    }

    /* renamed from: V */
    public static /* synthetic */ void m4176V(C1712t1 c1712t1) {
        c1712t1.zze |= 64;
        c1712t1.zzo = "android";
    }

    /* renamed from: V0 */
    public static /* synthetic */ void m4177V0(C1712t1 c1712t1, String str) {
        c1712t1.zze |= 16777216;
        c1712t1.zzH = str;
    }

    /* renamed from: W */
    public static /* synthetic */ void m4178W(C1712t1 c1712t1) {
        String str = Build.VERSION.RELEASE;
        Objects.requireNonNull(str);
        c1712t1.zze |= 128;
        c1712t1.zzp = str;
    }

    /* renamed from: W0 */
    public static /* synthetic */ void m4179W0(C1712t1 c1712t1, int i6) {
        c1712t1.zze |= 33554432;
        c1712t1.zzI = i6;
    }

    /* renamed from: X */
    public static /* synthetic */ void m4180X(C1712t1 c1712t1) {
        String str = Build.MODEL;
        Objects.requireNonNull(str);
        c1712t1.zze |= 256;
        c1712t1.zzq = str;
    }

    /* renamed from: X0 */
    public static /* synthetic */ void m4181X0(C1712t1 c1712t1) {
        c1712t1.zze &= -268435457;
        c1712t1.zzL = zzaa.zzL;
    }

    /* renamed from: Y */
    public static /* synthetic */ void m4182Y(C1712t1 c1712t1, String str) {
        Objects.requireNonNull(str);
        c1712t1.zze |= 512;
        c1712t1.zzr = str;
    }

    /* renamed from: Y0 */
    public static /* synthetic */ void m4183Y0(C1712t1 c1712t1, long j6) {
        c1712t1.zze |= 536870912;
        c1712t1.zzM = j6;
    }

    /* renamed from: Z */
    public static /* synthetic */ void m4184Z(C1712t1 c1712t1, int i6) {
        c1712t1.zze |= 1024;
        c1712t1.zzs = i6;
    }

    /* renamed from: Z0 */
    public static /* synthetic */ void m4185Z0(C1712t1 c1712t1, long j6) {
        c1712t1.zze |= 1073741824;
        c1712t1.zzN = j6;
    }

    /* renamed from: a0 */
    public static /* synthetic */ void m4186a0(C1712t1 c1712t1, String str) {
        Objects.requireNonNull(str);
        c1712t1.zze |= 2048;
        c1712t1.zzt = str;
    }

    /* renamed from: a1 */
    public static /* synthetic */ void m4187a1(C1712t1 c1712t1) {
        c1712t1.zze &= Integer.MAX_VALUE;
        c1712t1.zzO = zzaa.zzO;
    }

    /* renamed from: b0 */
    public static /* synthetic */ void m4188b0(C1712t1 c1712t1, String str) {
        Objects.requireNonNull(str);
        c1712t1.zze |= 4096;
        c1712t1.zzu = str;
    }

    /* renamed from: b1 */
    public static /* synthetic */ void m4189b1(C1712t1 c1712t1, int i6) {
        c1712t1.zzf |= 2;
        c1712t1.zzQ = i6;
    }

    /* renamed from: c0 */
    public static /* synthetic */ void m4190c0(C1712t1 c1712t1, String str) {
        Objects.requireNonNull(str);
        c1712t1.zze |= 8192;
        c1712t1.zzv = str;
    }

    /* renamed from: c1 */
    public static /* synthetic */ void m4191c1(C1712t1 c1712t1, String str) {
        Objects.requireNonNull(str);
        c1712t1.zzf |= 4;
        c1712t1.zzR = str;
    }

    /* renamed from: d0 */
    public static /* synthetic */ void m4192d0(C1712t1 c1712t1, long j6) {
        c1712t1.zze |= 16384;
        c1712t1.zzw = j6;
    }

    /* renamed from: d1 */
    public static void m4193d1(C1712t1 c1712t1, Iterable iterable) {
        RandomAccess randomAccess = c1712t1.zzT;
        if (!((AbstractC1654o3) randomAccess).f7118j) {
            C1703s4 c1703s4 = (C1703s4) randomAccess;
            int i6 = c1703s4.f7177l;
            c1712t1.zzT = c1703s4.mo3662k(i6 == 0 ? 10 : i6 + i6);
        }
        AbstractC1642n3.m3941i(iterable, c1712t1.zzT);
    }

    /* renamed from: e0 */
    public static /* synthetic */ void m4194e0(C1712t1 c1712t1) {
        c1712t1.zze |= 32768;
        c1712t1.zzx = 42004L;
    }

    /* renamed from: e1 */
    public static /* synthetic */ void m4195e1(C1712t1 c1712t1, long j6) {
        c1712t1.zzf |= 16;
        c1712t1.zzU = j6;
    }

    /* renamed from: f0 */
    public static /* synthetic */ void m4196f0(C1712t1 c1712t1, String str) {
        Objects.requireNonNull(str);
        c1712t1.zze |= 65536;
        c1712t1.zzy = str;
    }

    /* renamed from: f1 */
    public static /* synthetic */ void m4197f1(C1712t1 c1712t1, long j6) {
        c1712t1.zzf |= 32;
        c1712t1.zzV = j6;
    }

    /* renamed from: g0 */
    public static /* synthetic */ void m4198g0(C1712t1 c1712t1) {
        c1712t1.zze &= -65537;
        c1712t1.zzy = zzaa.zzy;
    }

    /* renamed from: g1 */
    public static /* synthetic */ void m4199g1(C1712t1 c1712t1, String str) {
        Objects.requireNonNull(str);
        c1712t1.zzf |= 64;
        c1712t1.zzW = str;
    }

    /* renamed from: h0 */
    public static /* synthetic */ void m4200h0(C1712t1 c1712t1, boolean z5) {
        c1712t1.zze |= 131072;
        c1712t1.zzz = z5;
    }

    /* renamed from: h1 */
    public static /* synthetic */ void m4201h1(C1712t1 c1712t1, String str) {
        Objects.requireNonNull(str);
        c1712t1.zzf |= 128;
        c1712t1.zzX = str;
    }

    /* renamed from: i0 */
    public static /* synthetic */ void m4202i0(C1712t1 c1712t1) {
        c1712t1.zze &= -131073;
        c1712t1.zzz = false;
    }

    /* renamed from: j0 */
    public static /* synthetic */ void m4203j0(C1712t1 c1712t1, String str) {
        c1712t1.zze |= 262144;
        c1712t1.zzA = str;
    }

    /* renamed from: k0 */
    public static /* synthetic */ void m4204k0(C1712t1 c1712t1) {
        c1712t1.zze &= -262145;
        c1712t1.zzA = zzaa.zzA;
    }

    /* renamed from: l0 */
    public static /* synthetic */ void m4205l0(C1712t1 c1712t1, long j6) {
        c1712t1.zze |= 524288;
        c1712t1.zzB = j6;
    }

    /* renamed from: m0 */
    public static /* synthetic */ void m4206m0(C1712t1 c1712t1, int i6) {
        c1712t1.zze |= 1048576;
        c1712t1.zzC = i6;
    }

    /* renamed from: n0 */
    public static /* synthetic */ void m4207n0(C1712t1 c1712t1, String str) {
        c1712t1.zze |= 2097152;
        c1712t1.zzD = str;
    }

    /* renamed from: o0 */
    public static /* synthetic */ void m4208o0(C1712t1 c1712t1) {
        c1712t1.zze &= -2097153;
        c1712t1.zzD = zzaa.zzD;
    }

    /* renamed from: p0 */
    public static /* synthetic */ void m4209p0(C1712t1 c1712t1, String str) {
        Objects.requireNonNull(str);
        c1712t1.zze |= 4194304;
        c1712t1.zzE = str;
    }

    /* renamed from: q0 */
    public static /* synthetic */ void m4210q0(C1712t1 c1712t1) {
        c1712t1.zze |= 8388608;
        c1712t1.zzF = false;
    }

    /* renamed from: r0 */
    public static /* synthetic */ void m4211r0(C1712t1 c1712t1, Iterable iterable) {
        InterfaceC1763x4<C1580i1> interfaceC1763x4 = c1712t1.zzG;
        if (!interfaceC1763x4.mo3965a()) {
            c1712t1.zzG = AbstractC1691r4.m4059l(interfaceC1763x4);
        }
        AbstractC1642n3.m3941i(iterable, c1712t1.zzG);
    }

    /* renamed from: s0 */
    public static void m4212s0(C1712t1 c1712t1) {
        c1712t1.zzG = C1512c6.f6916m;
    }

    /* renamed from: A */
    public final boolean m4213A() {
        return this.zzz;
    }

    /* renamed from: A0 */
    public final boolean m4214A0() {
        return (this.zzf & 128) != 0;
    }

    /* renamed from: A1 */
    public final String m4215A1() {
        return this.zzq;
    }

    /* renamed from: B */
    public final String m4216B() {
        return this.zzA;
    }

    /* renamed from: B0 */
    public final String m4217B0() {
        return this.zzX;
    }

    /* renamed from: B1 */
    public final String m4218B1() {
        return this.zzr;
    }

    /* renamed from: C */
    public final boolean m4219C() {
        return (this.zze & 524288) != 0;
    }

    /* renamed from: C1 */
    public final boolean m4220C1() {
        return (this.zze & 1024) != 0;
    }

    /* renamed from: D */
    public final long m4221D() {
        return this.zzB;
    }

    /* renamed from: D1 */
    public final int m4222D1() {
        return this.zzs;
    }

    /* renamed from: E */
    public final boolean m4223E() {
        return (this.zze & 1048576) != 0;
    }

    /* renamed from: E1 */
    public final String m4224E1() {
        return this.zzt;
    }

    /* renamed from: F */
    public final int m4225F() {
        return this.zzC;
    }

    /* renamed from: G */
    public final String m4226G() {
        return this.zzD;
    }

    /* renamed from: H */
    public final String m4227H() {
        return this.zzE;
    }

    /* renamed from: I */
    public final boolean m4228I() {
        return (this.zze & 8388608) != 0;
    }

    /* renamed from: J */
    public final boolean m4229J() {
        return this.zzF;
    }

    /* renamed from: K */
    public final List<C1580i1> m4230K() {
        return this.zzG;
    }

    /* renamed from: L */
    public final String m4231L() {
        return this.zzH;
    }

    /* renamed from: M */
    public final boolean m4232M() {
        return (this.zze & 33554432) != 0;
    }

    /* renamed from: N */
    public final int m4233N() {
        return this.zzI;
    }

    /* renamed from: O */
    public final boolean m4234O() {
        return (this.zze & 536870912) != 0;
    }

    /* renamed from: P */
    public final long m4235P() {
        return this.zzM;
    }

    /* renamed from: Q */
    public final boolean m4236Q() {
        return (this.zze & 1073741824) != 0;
    }

    /* renamed from: R */
    public final long m4237R() {
        return this.zzN;
    }

    /* renamed from: S */
    public final boolean m4238S() {
        return (this.zze & 1) != 0;
    }

    /* renamed from: S0 */
    public final int m4239S0() {
        return this.zzg;
    }

    /* renamed from: T0 */
    public final void m4240T0() {
        InterfaceC1763x4<C1628m1> interfaceC1763x4 = this.zzh;
        if (interfaceC1763x4.mo3965a()) {
            return;
        }
        this.zzh = AbstractC1691r4.m4059l(interfaceC1763x4);
    }

    /* renamed from: U0 */
    public final void m4241U0() {
        InterfaceC1763x4<C1508c2> interfaceC1763x4 = this.zzi;
        if (interfaceC1763x4.mo3965a()) {
            return;
        }
        this.zzi = AbstractC1691r4.m4059l(interfaceC1763x4);
    }

    /* renamed from: i1 */
    public final List<C1628m1> m4242i1() {
        return this.zzh;
    }

    /* renamed from: j1 */
    public final int m4243j1() {
        return this.zzh.size();
    }

    /* renamed from: k1 */
    public final C1628m1 m4244k1(int i6) {
        return this.zzh.get(i6);
    }

    /* renamed from: l1 */
    public final List<C1508c2> m4245l1() {
        return this.zzi;
    }

    /* renamed from: m1 */
    public final int m4246m1() {
        return this.zzi.size();
    }

    /* renamed from: n1 */
    public final C1508c2 m4247n1(int i6) {
        return this.zzi.get(i6);
    }

    /* renamed from: o1 */
    public final boolean m4248o1() {
        return (this.zze & 2) != 0;
    }

    /* renamed from: p1 */
    public final long m4249p1() {
        return this.zzj;
    }

    /* renamed from: q1 */
    public final boolean m4250q1() {
        return (this.zze & 4) != 0;
    }

    @Override // p152w2.AbstractC1691r4
    /* renamed from: r */
    public final Object mo3604r(int i6) {
        int i7 = i6 - 1;
        if (i7 == 0) {
            return (byte) 1;
        }
        if (i7 == 2) {
            return new C1525d6(zzaa, "\u0001.\u0000\u0002\u00017.\u0000\u0004\u0000\u0001င\u0000\u0002\u001b\u0003\u001b\u0004ဂ\u0001\u0005ဂ\u0002\u0006ဂ\u0003\u0007ဂ\u0005\bဈ\u0006\tဈ\u0007\nဈ\b\u000bဈ\t\fင\n\rဈ\u000b\u000eဈ\f\u0010ဈ\r\u0011ဂ\u000e\u0012ဂ\u000f\u0013ဈ\u0010\u0014ဇ\u0011\u0015ဈ\u0012\u0016ဂ\u0013\u0017င\u0014\u0018ဈ\u0015\u0019ဈ\u0016\u001aဂ\u0004\u001cဇ\u0017\u001d\u001b\u001eဈ\u0018\u001fင\u0019 င\u001a!င\u001b\"ဈ\u001c#ဂ\u001d$ဂ\u001e%ဈ\u001f&ဈ 'င!)ဈ\",ဉ#-\u001d.ဂ$/ဂ%2ဈ&4ဈ'5ဌ(7ဇ)", new Object[]{"zze", "zzf", "zzg", "zzh", C1628m1.class, "zzi", C1508c2.class, "zzj", "zzk", "zzl", "zzn", "zzo", "zzp", "zzq", "zzr", "zzs", "zzt", "zzu", "zzv", "zzw", "zzx", "zzy", "zzz", "zzA", "zzB", "zzC", "zzD", "zzE", "zzm", "zzF", "zzG", C1580i1.class, "zzH", "zzI", "zzJ", "zzK", "zzL", "zzM", "zzN", "zzO", "zzP", "zzQ", "zzR", "zzS", "zzT", "zzU", "zzV", "zzW", "zzX", "zzY", C1556g1.f6981a, "zzZ"});
        }
        if (i7 == 3) {
            return new C1712t1();
        }
        if (i7 == 4) {
            return new C1700s1(null);
        }
        if (i7 != 5) {
            return null;
        }
        return zzaa;
    }

    /* renamed from: r1 */
    public final long m4251r1() {
        return this.zzk;
    }

    /* renamed from: s */
    public final String m4252s() {
        return this.zzu;
    }

    /* renamed from: s1 */
    public final boolean m4253s1() {
        return (this.zze & 8) != 0;
    }

    /* renamed from: t */
    public final String m4254t() {
        return this.zzv;
    }

    /* renamed from: t0 */
    public final String m4255t0() {
        return this.zzO;
    }

    /* renamed from: t1 */
    public final long m4256t1() {
        return this.zzl;
    }

    /* renamed from: u */
    public final boolean m4257u() {
        return (this.zze & 16384) != 0;
    }

    /* renamed from: u0 */
    public final boolean m4258u0() {
        return (this.zzf & 2) != 0;
    }

    /* renamed from: u1 */
    public final boolean m4259u1() {
        return (this.zze & 16) != 0;
    }

    /* renamed from: v */
    public final long m4260v() {
        return this.zzw;
    }

    /* renamed from: v0 */
    public final int m4261v0() {
        return this.zzQ;
    }

    /* renamed from: v1 */
    public final long m4262v1() {
        return this.zzm;
    }

    /* renamed from: w */
    public final boolean m4263w() {
        return (this.zze & 32768) != 0;
    }

    /* renamed from: w0 */
    public final String m4264w0() {
        return this.zzR;
    }

    /* renamed from: w1 */
    public final boolean m4265w1() {
        return (this.zze & 32) != 0;
    }

    /* renamed from: x */
    public final long m4266x() {
        return this.zzx;
    }

    /* renamed from: x0 */
    public final boolean m4267x0() {
        return (this.zzf & 16) != 0;
    }

    /* renamed from: x1 */
    public final long m4268x1() {
        return this.zzn;
    }

    /* renamed from: y */
    public final String m4269y() {
        return this.zzy;
    }

    /* renamed from: y0 */
    public final long m4270y0() {
        return this.zzU;
    }

    /* renamed from: y1 */
    public final String m4271y1() {
        return this.zzo;
    }

    /* renamed from: z */
    public final boolean m4272z() {
        return (this.zze & 131072) != 0;
    }

    /* renamed from: z0 */
    public final String m4273z0() {
        return this.zzW;
    }

    /* renamed from: z1 */
    public final String m4274z1() {
        return this.zzp;
    }
}
