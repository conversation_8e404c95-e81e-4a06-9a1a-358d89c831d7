package p152w2;

/* renamed from: w2.u2 */
/* loaded from: classes.dex */
public final class C1725u2 extends AbstractC1773y2 {

    /* renamed from: i */
    public final /* synthetic */ int f7193i = 1;

    public C1725u2(C1749w2 c1749w2, Double d6) {
        super(c1749w2, "measurement.test.double_flag", d6);
    }

    /* JADX WARN: Code restructure failed: missing block: B:14:0x0053, code lost:
    
        r0 = m4459b();
        r8 = (java.lang.String) r8;
        r6 = new java.lang.StringBuilder((java.lang.String.valueOf(r0).length() + 27) + r8.length());
        r6.append("Invalid double value for ");
        r6.append(r0);
        r6.append(": ");
        r6.append(r8);
        android.util.Log.e("PhenotypeFlag", r6.toString());
     */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x0017, code lost:
    
        r0 = m4459b();
        r8 = (java.lang.String) r8;
        r6 = new java.lang.StringBuilder((java.lang.String.valueOf(r0).length() + 25) + r8.length());
        r6.append("Invalid long value for ");
        r6.append(r0);
        r6.append(": ");
        r6.append(r8);
        android.util.Log.e("PhenotypeFlag", r6.toString());
     */
    @Override // p152w2.AbstractC1773y2
    /* renamed from: a */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final /* bridge */ /* synthetic */ java.lang.Object mo4284a(java.lang.Object r8) {
        /*
            r7 = this;
            int r0 = r7.f7193i
            r1 = 0
            java.lang.String r2 = "PhenotypeFlag"
            java.lang.String r3 = ": "
            switch(r0) {
                case 0: goto Lb;
                default: goto La;
            }
        La:
            goto L47
        Lb:
            r0 = r8
            java.lang.String r0 = (java.lang.String) r0     // Catch: java.lang.NumberFormatException -> L17
            long r4 = java.lang.Long.parseLong(r0)     // Catch: java.lang.NumberFormatException -> L17
            java.lang.Long r1 = java.lang.Long.valueOf(r4)     // Catch: java.lang.NumberFormatException -> L17
            goto L46
        L17:
            java.lang.String r0 = r7.m4459b()
            java.lang.String r4 = java.lang.String.valueOf(r0)
            int r4 = r4.length()
            java.lang.String r8 = (java.lang.String) r8
            int r5 = r8.length()
            java.lang.StringBuilder r6 = new java.lang.StringBuilder
            int r4 = r4 + 25
            int r4 = r4 + r5
            r6.<init>(r4)
            java.lang.String r4 = "Invalid long value for "
            r6.append(r4)
            r6.append(r0)
            r6.append(r3)
            r6.append(r8)
            java.lang.String r8 = r6.toString()
            android.util.Log.e(r2, r8)
        L46:
            return r1
        L47:
            r0 = r8
            java.lang.String r0 = (java.lang.String) r0     // Catch: java.lang.NumberFormatException -> L53
            double r4 = java.lang.Double.parseDouble(r0)     // Catch: java.lang.NumberFormatException -> L53
            java.lang.Double r1 = java.lang.Double.valueOf(r4)     // Catch: java.lang.NumberFormatException -> L53
            goto L82
        L53:
            java.lang.String r0 = r7.m4459b()
            java.lang.String r4 = java.lang.String.valueOf(r0)
            int r4 = r4.length()
            java.lang.String r8 = (java.lang.String) r8
            int r5 = r8.length()
            java.lang.StringBuilder r6 = new java.lang.StringBuilder
            int r4 = r4 + 27
            int r4 = r4 + r5
            r6.<init>(r4)
            java.lang.String r4 = "Invalid double value for "
            r6.append(r4)
            r6.append(r0)
            r6.append(r3)
            r6.append(r8)
            java.lang.String r8 = r6.toString()
            android.util.Log.e(r2, r8)
        L82:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1725u2.mo4284a(java.lang.Object):java.lang.Object");
    }

    public C1725u2(C1749w2 c1749w2, String str, Long l6) {
        super(c1749w2, str, l6);
    }
}
