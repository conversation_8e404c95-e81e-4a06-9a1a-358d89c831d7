package p152w2;

/* JADX WARN: Enum visitor error
jadx.core.utils.exceptions.JadxRuntimeException: Init of enum field 'EF2' uses external variables
	at jadx.core.dex.visitors.EnumVisitor.createEnumFieldByConstructor(EnumVisitor.java:451)
	at jadx.core.dex.visitors.EnumVisitor.processEnumFieldByRegister(EnumVisitor.java:395)
	at jadx.core.dex.visitors.EnumVisitor.extractEnumFieldsFromFilledArray(EnumVisitor.java:324)
	at jadx.core.dex.visitors.EnumVisitor.extractEnumFieldsFromInsn(EnumVisitor.java:262)
	at jadx.core.dex.visitors.EnumVisitor.convertToEnum(EnumVisitor.java:151)
	at jadx.core.dex.visitors.EnumVisitor.visit(EnumVisitor.java:100)
 */
/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* renamed from: w2.c7 */
/* loaded from: classes.dex */
public final class EnumC1513c7 {

    /* renamed from: k */
    public static final EnumC1513c7 f6919k;

    /* renamed from: l */
    public static final /* synthetic */ EnumC1513c7[] f6920l;

    /* renamed from: j */
    public final EnumC1526d7 f6921j;

    /* JADX INFO: Fake field, exist only in values array */
    EnumC1513c7 EF1;

    /* JADX INFO: Fake field, exist only in values array */
    EnumC1513c7 EF2;

    static {
        EnumC1513c7 enumC1513c7 = new EnumC1513c7("DOUBLE", 0, EnumC1526d7.DOUBLE);
        f6919k = enumC1513c7;
        EnumC1513c7 enumC1513c72 = new EnumC1513c7("FLOAT", 1, EnumC1526d7.FLOAT);
        EnumC1526d7 enumC1526d7 = EnumC1526d7.LONG;
        EnumC1513c7 enumC1513c73 = new EnumC1513c7("INT64", 2, enumC1526d7);
        EnumC1513c7 enumC1513c74 = new EnumC1513c7("UINT64", 3, enumC1526d7);
        EnumC1526d7 enumC1526d72 = EnumC1526d7.INT;
        EnumC1513c7 enumC1513c75 = new EnumC1513c7("INT32", 4, enumC1526d72);
        EnumC1513c7 enumC1513c76 = new EnumC1513c7("FIXED64", 5, enumC1526d7);
        EnumC1513c7 enumC1513c77 = new EnumC1513c7("FIXED32", 6, enumC1526d72);
        EnumC1513c7 enumC1513c78 = new EnumC1513c7("BOOL", 7, EnumC1526d7.BOOLEAN);
        EnumC1513c7 enumC1513c79 = new EnumC1513c7("STRING", 8, EnumC1526d7.STRING);
        EnumC1526d7 enumC1526d73 = EnumC1526d7.MESSAGE;
        f6920l = new EnumC1513c7[]{enumC1513c7, enumC1513c72, enumC1513c73, enumC1513c74, enumC1513c75, enumC1513c76, enumC1513c77, enumC1513c78, enumC1513c79, new EnumC1513c7("GROUP", 9, enumC1526d73), new EnumC1513c7("MESSAGE", 10, enumC1526d73), new EnumC1513c7("BYTES", 11, EnumC1526d7.BYTE_STRING), new EnumC1513c7("UINT32", 12, enumC1526d72), new EnumC1513c7("ENUM", 13, EnumC1526d7.ENUM), new EnumC1513c7("SFIXED32", 14, enumC1526d72), new EnumC1513c7("SFIXED64", 15, enumC1526d7), new EnumC1513c7("SINT32", 16, enumC1526d72), new EnumC1513c7("SINT64", 17, enumC1526d7)};
    }

    public EnumC1513c7(String str, int i6, EnumC1526d7 enumC1526d7) {
        this.f6921j = enumC1526d7;
    }

    public static EnumC1513c7[] values() {
        return (EnumC1513c7[]) f6920l.clone();
    }
}
