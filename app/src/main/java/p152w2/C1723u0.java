package p152w2;

/* renamed from: w2.u0 */
/* loaded from: classes.dex */
public final class C1723u0 extends C1643n4 {
    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public /* synthetic */ C1723u0() {
        /*
            r1 = this;
            w2.f1 r0 = p152w2.C1544f1.m3778u()
            r1.<init>(r0)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1723u0.<init>():void");
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public /* synthetic */ C1723u0(int r2) {
        /*
            r1 = this;
            r0 = 1
            if (r2 == r0) goto Lb
            w2.w0 r2 = p152w2.C1747w0.m4342D()
            r1.<init>(r2)
            return
        Lb:
            w2.a1 r2 = p152w2.C1481a1.m3603B()
            r1.<init>(r2)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1723u0.<init>(int):void");
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public /* synthetic */ C1723u0(p008b0.C0385m r1) {
        /*
            r0 = this;
            w2.n1 r1 = p152w2.C1640n1.m3939s()
            r0.<init>(r1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1723u0.<init>(b0.m):void");
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public /* synthetic */ C1723u0(p008b0.C0385m r1, int r2, androidx.activity.result.C0052a r3) {
        /*
            r0 = this;
            r1 = 4
            if (r2 == r1) goto Lb
            w2.d2 r1 = p152w2.C1521d2.m3710v()
            r0.<init>(r1)
            return
        Lb:
            w2.f2 r1 = p152w2.C1545f2.m3781u()
            r0.<init>(r1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1723u0.<init>(b0.m, int, androidx.activity.result.a):void");
    }
}
