package p152w2;

/* renamed from: w2.s2 */
/* loaded from: classes.dex */
public final class C1701s2 {

    /* renamed from: a */
    public static volatile AbstractC1496b3<Boolean> f7170a = C1483a3.f6872j;

    /* renamed from: b */
    public static final Object f7171b = new Object();

    /* JADX WARN: Can't wrap try/catch for region: R(10:18|(5:32|(2:34|(1:36))|27|28|29)(1:20)|21|22|23|24|(1:26)|27|28|29) */
    /* renamed from: a */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static boolean m4138a(android.content.Context r3, android.net.Uri r4) {
        /*
            java.lang.String r4 = r4.getAuthority()
            java.lang.String r0 = "com.google.android.gms.phenotype"
            boolean r0 = r0.equals(r4)
            r1 = 0
            if (r0 != 0) goto L2e
            java.lang.String r3 = java.lang.String.valueOf(r4)
            int r3 = r3.length()
            java.lang.StringBuilder r0 = new java.lang.StringBuilder
            int r3 = r3 + 91
            r0.<init>(r3)
            r0.append(r4)
            java.lang.String r3 = " is an unsupported authority. Only com.google.android.gms.phenotype authority is supported."
            r0.append(r3)
            java.lang.String r3 = "PhenotypeClientHelper"
            java.lang.String r4 = r0.toString()
            android.util.Log.e(r3, r4)
            return r1
        L2e:
            w2.b3<java.lang.Boolean> r4 = p152w2.C1701s2.f7170a
            boolean r4 = r4.mo3622a()
            if (r4 == 0) goto L43
        L36:
            w2.b3<java.lang.Boolean> r3 = p152w2.C1701s2.f7170a
            java.lang.Object r3 = r3.mo3623b()
            java.lang.Boolean r3 = (java.lang.Boolean) r3
            boolean r3 = r3.booleanValue()
            return r3
        L43:
            java.lang.Object r4 = p152w2.C1701s2.f7171b
            monitor-enter(r4)
            w2.b3<java.lang.Boolean> r0 = p152w2.C1701s2.f7170a     // Catch: java.lang.Throwable -> L9e
            boolean r0 = r0.mo3622a()     // Catch: java.lang.Throwable -> L9e
            if (r0 == 0) goto L5c
            w2.b3<java.lang.Boolean> r3 = p152w2.C1701s2.f7170a     // Catch: java.lang.Throwable -> L9e
            java.lang.Object r3 = r3.mo3623b()     // Catch: java.lang.Throwable -> L9e
            java.lang.Boolean r3 = (java.lang.Boolean) r3     // Catch: java.lang.Throwable -> L9e
            boolean r3 = r3.booleanValue()     // Catch: java.lang.Throwable -> L9e
            monitor-exit(r4)     // Catch: java.lang.Throwable -> L9e
            return r3
        L5c:
            java.lang.String r0 = "com.google.android.gms"
            java.lang.String r2 = r3.getPackageName()     // Catch: java.lang.Throwable -> L9e
            boolean r0 = r0.equals(r2)     // Catch: java.lang.Throwable -> L9e
            if (r0 == 0) goto L69
            goto L80
        L69:
            android.content.pm.PackageManager r0 = r3.getPackageManager()     // Catch: java.lang.Throwable -> L9e
            java.lang.String r2 = "com.google.android.gms.phenotype"
            android.content.pm.ProviderInfo r0 = r0.resolveContentProvider(r2, r1)     // Catch: java.lang.Throwable -> L9e
            if (r0 == 0) goto L91
            java.lang.String r2 = "com.google.android.gms"
            java.lang.String r0 = r0.packageName     // Catch: java.lang.Throwable -> L9e
            boolean r0 = r2.equals(r0)     // Catch: java.lang.Throwable -> L9e
            if (r0 != 0) goto L80
            goto L91
        L80:
            android.content.pm.PackageManager r3 = r3.getPackageManager()     // Catch: java.lang.Throwable -> L9e
            java.lang.String r0 = "com.google.android.gms"
            android.content.pm.ApplicationInfo r3 = r3.getApplicationInfo(r0, r1)     // Catch: android.content.pm.PackageManager.NameNotFoundException -> L91 java.lang.Throwable -> L9e
            int r3 = r3.flags     // Catch: java.lang.Throwable -> L9e
            r3 = r3 & 129(0x81, float:1.81E-43)
            if (r3 == 0) goto L91
            r1 = 1
        L91:
            java.lang.Boolean r3 = java.lang.Boolean.valueOf(r1)     // Catch: java.lang.Throwable -> L9e
            w2.c3 r0 = new w2.c3     // Catch: java.lang.Throwable -> L9e
            r0.<init>(r3)     // Catch: java.lang.Throwable -> L9e
            p152w2.C1701s2.f7170a = r0     // Catch: java.lang.Throwable -> L9e
            monitor-exit(r4)     // Catch: java.lang.Throwable -> L9e
            goto L36
        L9e:
            r3 = move-exception
            monitor-exit(r4)     // Catch: java.lang.Throwable -> L9e
            throw r3
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1701s2.m4138a(android.content.Context, android.net.Uri):boolean");
    }
}
