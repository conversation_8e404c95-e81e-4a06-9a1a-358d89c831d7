package p152w2;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import org.litepal.util.Const;
import p008b0.C0385m;
import p023d1.C0722t;
import p090n.C1090c;
import p124s1.C1336c;
import p158x2.C1889g4;

/* renamed from: w2.j6 */
/* loaded from: classes.dex */
public final class C1597j6 extends AbstractC1590j {

    /* renamed from: l */
    public final /* synthetic */ int f7037l = 0;

    /* renamed from: m */
    public final Object f7038m;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C1597j6(C0722t c0722t) {
        super("getValue");
        this.f7038m = c0722t;
    }

    /* JADX WARN: Type inference failed for: r1v4, types: [java.util.Map<java.lang.String, java.util.Map<java.lang.String, java.lang.String>>, l.g] */
    @Override // p152w2.AbstractC1590j
    /* renamed from: a */
    public final InterfaceC1662p mo3670a(C1090c c1090c, List list) {
        Object obj;
        switch (this.f7037l) {
            case 0:
                C0385m.m1426r("getValue", 2, list);
                InterfaceC1662p m2803b = c1090c.m2803b((InterfaceC1662p) list.get(0));
                InterfaceC1662p m2803b2 = c1090c.m2803b((InterfaceC1662p) list.get(1));
                String mo3761c = m2803b.mo3761c();
                C0722t c0722t = (C0722t) this.f7038m;
                String str = null;
                Map map = (Map) ((C1889g4) c0722t.f3916k).f7707n.getOrDefault((String) c0722t.f3915j, null);
                if (map != null && map.containsKey(mo3761c)) {
                    str = (String) map.get(mo3761c);
                }
                return str != null ? new C1698s(str) : m2803b2;
            default:
                C0385m.m1426r(this.f7015j, 3, list);
                c1090c.m2803b((InterfaceC1662p) list.get(0)).mo3761c();
                InterfaceC1662p m2803b3 = c1090c.m2803b((InterfaceC1662p) list.get(1));
                if (!(m2803b3 instanceof C1650o)) {
                    throw new IllegalArgumentException("Invalid callback type");
                }
                InterfaceC1662p m2803b4 = c1090c.m2803b((InterfaceC1662p) list.get(2));
                if (!(m2803b4 instanceof C1626m)) {
                    throw new IllegalArgumentException("Invalid callback params");
                }
                C1626m c1626m = (C1626m) m2803b4;
                if (!c1626m.mo3765j(Const.TableSchema.COLUMN_TYPE)) {
                    throw new IllegalArgumentException("Undefined rule type");
                }
                String mo3761c2 = c1626m.mo3767m(Const.TableSchema.COLUMN_TYPE).mo3761c();
                int m1407D = c1626m.mo3765j("priority") ? C0385m.m1407D(c1626m.mo3767m("priority").mo3762d().doubleValue()) : 1000;
                C1336c c1336c = (C1336c) this.f7038m;
                C1650o c1650o = (C1650o) m2803b3;
                Objects.requireNonNull(c1336c);
                if ("create".equals(mo3761c2)) {
                    obj = c1336c.f6218b;
                } else {
                    if (!"edit".equals(mo3761c2)) {
                        String valueOf = String.valueOf(mo3761c2);
                        throw new IllegalStateException(valueOf.length() != 0 ? "Unknown callback type: ".concat(valueOf) : new String("Unknown callback type: "));
                    }
                    obj = c1336c.f6217a;
                }
                TreeMap treeMap = (TreeMap) obj;
                if (treeMap.containsKey(Integer.valueOf(m1407D))) {
                    m1407D = ((Integer) treeMap.lastKey()).intValue() + 1;
                }
                treeMap.put(Integer.valueOf(m1407D), c1650o);
                return InterfaceC1662p.f7126b;
        }
    }

    public C1597j6(C1336c c1336c) {
        super("internal.registerCallback");
        this.f7038m = c1336c;
    }
}
