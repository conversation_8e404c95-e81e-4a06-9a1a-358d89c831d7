package p152w2;

import java.util.List;
import p008b0.C0385m;

/* renamed from: w2.r1 */
/* loaded from: classes.dex */
public final class C1688r1 extends AbstractC1691r4<C1688r1, C1676q1> implements InterfaceC1728u5 {
    private static final C1688r1 zze;
    private InterfaceC1763x4<C1712t1> zza = C1512c6.f6916m;

    static {
        C1688r1 c1688r1 = new C1688r1();
        zze = c1688r1;
        AbstractC1691r4.m4061q(C1688r1.class, c1688r1);
    }

    /* renamed from: u */
    public static C1676q1 m4049u() {
        return zze.m4065m();
    }

    /* renamed from: w */
    public static /* synthetic */ void m4051w(C1688r1 c1688r1, C1712t1 c1712t1) {
        InterfaceC1763x4<C1712t1> interfaceC1763x4 = c1688r1.zza;
        if (!interfaceC1763x4.mo3965a()) {
            c1688r1.zza = AbstractC1691r4.m4059l(interfaceC1763x4);
        }
        c1688r1.zza.add(c1712t1);
    }

    @Override // p152w2.AbstractC1691r4
    /* renamed from: r */
    public final Object mo3604r(int i6) {
        int i7 = i6 - 1;
        if (i7 == 0) {
            return (byte) 1;
        }
        if (i7 == 2) {
            return new C1525d6(zze, "\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u001b", new Object[]{"zza", C1712t1.class});
        }
        if (i7 == 3) {
            return new C1688r1();
        }
        C0385m c0385m = null;
        if (i7 == 4) {
            return new C1676q1(c0385m);
        }
        if (i7 != 5) {
            return null;
        }
        return zze;
    }

    /* renamed from: s */
    public final List<C1712t1> m4052s() {
        return this.zza;
    }

    /* renamed from: t */
    public final C1712t1 m4053t() {
        return this.zza.get(0);
    }
}
