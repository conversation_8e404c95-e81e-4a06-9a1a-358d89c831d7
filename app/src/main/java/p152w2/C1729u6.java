package p152w2;

import java.util.AbstractList;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;
import java.util.RandomAccess;

/* renamed from: w2.u6 */
/* loaded from: classes.dex */
public final class C1729u6 extends AbstractList<String> implements RandomAccess, InterfaceC1536e5 {

    /* renamed from: j */
    public final InterfaceC1536e5 f7195j;

    public C1729u6(InterfaceC1536e5 interfaceC1536e5) {
        this.f7195j = interfaceC1536e5;
    }

    @Override // p152w2.InterfaceC1536e5
    /* renamed from: e */
    public final List<?> mo3716e() {
        return this.f7195j.mo3716e();
    }

    @Override // p152w2.InterfaceC1536e5
    /* renamed from: f */
    public final InterfaceC1536e5 mo3717f() {
        return this;
    }

    @Override // java.util.AbstractList, java.util.List
    public final /* bridge */ /* synthetic */ Object get(int i6) {
        return ((C1524d5) this.f7195j).get(i6);
    }

    @Override // p152w2.InterfaceC1536e5
    /* renamed from: h */
    public final void mo3718h(AbstractC1750w3 abstractC1750w3) {
        throw new UnsupportedOperationException();
    }

    @Override // java.util.AbstractList, java.util.AbstractCollection, java.util.Collection, java.lang.Iterable, java.util.List
    public final Iterator<String> iterator() {
        return new C1717t6(this);
    }

    @Override // java.util.AbstractList, java.util.List
    public final ListIterator<String> listIterator(int i6) {
        return new C1705s6(this, i6);
    }

    @Override // p152w2.InterfaceC1536e5
    /* renamed from: n */
    public final Object mo3719n(int i6) {
        return this.f7195j.mo3719n(i6);
    }

    @Override // java.util.AbstractCollection, java.util.Collection, java.util.List
    public final int size() {
        return this.f7195j.size();
    }
}
