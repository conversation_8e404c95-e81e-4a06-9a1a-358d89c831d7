package p152w2;

import java.nio.charset.Charset;
import java.util.AbstractList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Objects;
import java.util.RandomAccess;

/* renamed from: w2.k4 */
/* loaded from: classes.dex */
public final class C1607k4 extends AbstractC1654o3<Float> implements RandomAccess, InterfaceC1486a6 {

    /* renamed from: k */
    public float[] f7055k;

    /* renamed from: l */
    public int f7056l;

    static {
        new C1607k4(new float[0], 0).f7118j = false;
    }

    public C1607k4() {
        this(new float[10], 0);
    }

    public C1607k4(float[] fArr, int i6) {
        this.f7055k = fArr;
        this.f7056l = i6;
    }

    @Override // java.util.AbstractList, java.util.List
    public final /* bridge */ /* synthetic */ void add(int i6, Object obj) {
        int i7;
        float floatValue = ((Float) obj).floatValue();
        m3967c();
        if (i6 < 0 || i6 > (i7 = this.f7056l)) {
            throw new IndexOutOfBoundsException(m3887i(i6));
        }
        float[] fArr = this.f7055k;
        if (i7 < fArr.length) {
            System.arraycopy(fArr, i6, fArr, i6 + 1, i7 - i6);
        } else {
            float[] fArr2 = new float[((i7 * 3) / 2) + 1];
            System.arraycopy(fArr, 0, fArr2, 0, i6);
            System.arraycopy(this.f7055k, i6, fArr2, i6 + 1, this.f7056l - i6);
            this.f7055k = fArr2;
        }
        this.f7055k[i6] = floatValue;
        this.f7056l++;
        ((AbstractList) this).modCount++;
    }

    @Override // p152w2.AbstractC1654o3, java.util.AbstractList, java.util.AbstractCollection, java.util.Collection, java.util.List
    public final /* bridge */ /* synthetic */ boolean add(Object obj) {
        m3885d(((Float) obj).floatValue());
        return true;
    }

    @Override // p152w2.AbstractC1654o3, java.util.AbstractCollection, java.util.Collection, java.util.List
    public final boolean addAll(Collection<? extends Float> collection) {
        m3967c();
        Charset charset = C1775y4.f7289a;
        Objects.requireNonNull(collection);
        if (!(collection instanceof C1607k4)) {
            return super.addAll(collection);
        }
        C1607k4 c1607k4 = (C1607k4) collection;
        int i6 = c1607k4.f7056l;
        if (i6 == 0) {
            return false;
        }
        int i7 = this.f7056l;
        if (Integer.MAX_VALUE - i7 < i6) {
            throw new OutOfMemoryError();
        }
        int i8 = i7 + i6;
        float[] fArr = this.f7055k;
        if (i8 > fArr.length) {
            this.f7055k = Arrays.copyOf(fArr, i8);
        }
        System.arraycopy(c1607k4.f7055k, 0, this.f7055k, this.f7056l, c1607k4.f7056l);
        this.f7056l = i8;
        ((AbstractList) this).modCount++;
        return true;
    }

    @Override // java.util.AbstractCollection, java.util.Collection, java.util.List
    public final boolean contains(Object obj) {
        return indexOf(obj) != -1;
    }

    /* renamed from: d */
    public final void m3885d(float f6) {
        m3967c();
        int i6 = this.f7056l;
        float[] fArr = this.f7055k;
        if (i6 == fArr.length) {
            float[] fArr2 = new float[((i6 * 3) / 2) + 1];
            System.arraycopy(fArr, 0, fArr2, 0, i6);
            this.f7055k = fArr2;
        }
        float[] fArr3 = this.f7055k;
        int i7 = this.f7056l;
        this.f7056l = i7 + 1;
        fArr3[i7] = f6;
    }

    @Override // p152w2.AbstractC1654o3, java.util.AbstractList, java.util.Collection, java.util.List
    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof C1607k4)) {
            return super.equals(obj);
        }
        C1607k4 c1607k4 = (C1607k4) obj;
        if (this.f7056l != c1607k4.f7056l) {
            return false;
        }
        float[] fArr = c1607k4.f7055k;
        for (int i6 = 0; i6 < this.f7056l; i6++) {
            if (Float.floatToIntBits(this.f7055k[i6]) != Float.floatToIntBits(fArr[i6])) {
                return false;
            }
        }
        return true;
    }

    /* renamed from: g */
    public final void m3886g(int i6) {
        if (i6 < 0 || i6 >= this.f7056l) {
            throw new IndexOutOfBoundsException(m3887i(i6));
        }
    }

    @Override // java.util.AbstractList, java.util.List
    public final /* bridge */ /* synthetic */ Object get(int i6) {
        m3886g(i6);
        return Float.valueOf(this.f7055k[i6]);
    }

    @Override // p152w2.AbstractC1654o3, java.util.AbstractList, java.util.Collection, java.util.List
    public final int hashCode() {
        int i6 = 1;
        for (int i7 = 0; i7 < this.f7056l; i7++) {
            i6 = (i6 * 31) + Float.floatToIntBits(this.f7055k[i7]);
        }
        return i6;
    }

    /* renamed from: i */
    public final String m3887i(int i6) {
        int i7 = this.f7056l;
        StringBuilder sb = new StringBuilder(35);
        sb.append("Index:");
        sb.append(i6);
        sb.append(", Size:");
        sb.append(i7);
        return sb.toString();
    }

    @Override // java.util.AbstractList, java.util.List
    public final int indexOf(Object obj) {
        if (!(obj instanceof Float)) {
            return -1;
        }
        float floatValue = ((Float) obj).floatValue();
        int i6 = this.f7056l;
        for (int i7 = 0; i7 < i6; i7++) {
            if (this.f7055k[i7] == floatValue) {
                return i7;
            }
        }
        return -1;
    }

    @Override // p152w2.InterfaceC1763x4
    /* renamed from: k */
    public final /* bridge */ /* synthetic */ InterfaceC1763x4 mo3662k(int i6) {
        if (i6 >= this.f7056l) {
            return new C1607k4(Arrays.copyOf(this.f7055k, i6), this.f7056l);
        }
        throw new IllegalArgumentException();
    }

    @Override // p152w2.AbstractC1654o3, java.util.AbstractList, java.util.List
    public final /* bridge */ /* synthetic */ Object remove(int i6) {
        m3967c();
        m3886g(i6);
        float[] fArr = this.f7055k;
        float f6 = fArr[i6];
        if (i6 < this.f7056l - 1) {
            System.arraycopy(fArr, i6 + 1, fArr, i6, (r2 - i6) - 1);
        }
        this.f7056l--;
        ((AbstractList) this).modCount++;
        return Float.valueOf(f6);
    }

    @Override // java.util.AbstractList
    public final void removeRange(int i6, int i7) {
        m3967c();
        if (i7 < i6) {
            throw new IndexOutOfBoundsException("toIndex < fromIndex");
        }
        float[] fArr = this.f7055k;
        System.arraycopy(fArr, i7, fArr, i6, this.f7056l - i7);
        this.f7056l -= i7 - i6;
        ((AbstractList) this).modCount++;
    }

    @Override // java.util.AbstractList, java.util.List
    public final /* bridge */ /* synthetic */ Object set(int i6, Object obj) {
        float floatValue = ((Float) obj).floatValue();
        m3967c();
        m3886g(i6);
        float[] fArr = this.f7055k;
        float f6 = fArr[i6];
        fArr[i6] = floatValue;
        return Float.valueOf(f6);
    }

    @Override // java.util.AbstractCollection, java.util.Collection, java.util.List
    public final int size() {
        return this.f7056l;
    }
}
