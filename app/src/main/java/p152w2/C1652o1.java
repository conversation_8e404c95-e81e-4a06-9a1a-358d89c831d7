package p152w2;

/* renamed from: w2.o1 */
/* loaded from: classes.dex */
public final class C1652o1 extends C1643n4<C1664p1, C1652o1> {
    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public C1652o1() {
        /*
            r1 = this;
            w2.p1 r0 = p152w2.C1664p1.m3985F()
            r1.<init>(r0)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1652o1.<init>():void");
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public /* synthetic */ C1652o1(p008b0.C0385m r1) {
        /*
            r0 = this;
            w2.p1 r1 = p152w2.C1664p1.m3985F()
            r0.<init>(r1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1652o1.<init>(b0.m):void");
    }

    /* renamed from: l */
    public final C1652o1 m3961l(String str) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1664p1.m3986G((C1664p1) this.f7099k, str);
        return this;
    }

    /* renamed from: m */
    public final C1652o1 m3962m(String str) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1664p1.m3987H((C1664p1) this.f7099k, str);
        return this;
    }

    /* renamed from: n */
    public final C1652o1 m3963n(long j6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1664p1.m3989J((C1664p1) this.f7099k, j6);
        return this;
    }

    /* renamed from: o */
    public final C1652o1 m3964o(double d6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1664p1.m3991L((C1664p1) this.f7099k, d6);
        return this;
    }
}
