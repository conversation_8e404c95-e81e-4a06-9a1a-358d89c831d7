package p152w2;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import p099o2.C1147b;

/* renamed from: w2.l0 */
/* loaded from: classes.dex */
public final class C1615l0 implements Parcelable.Creator<C1603k0> {
    @Override // android.os.Parcelable.Creator
    public final C1603k0 createFromParcel(Parcel parcel) {
        int m2977l = C1147b.m2977l(parcel);
        long j6 = 0;
        long j7 = 0;
        String str = null;
        String str2 = null;
        String str3 = null;
        Bundle bundle = null;
        String str4 = null;
        boolean z5 = false;
        while (parcel.dataPosition() < m2977l) {
            int readInt = parcel.readInt();
            switch (65535 & readInt) {
                case 1:
                    j6 = C1147b.m2974i(parcel, readInt);
                    break;
                case 2:
                    j7 = C1147b.m2974i(parcel, readInt);
                    break;
                case 3:
                    z5 = C1147b.m2971f(parcel, readInt);
                    break;
                case 4:
                    str = C1147b.m2968c(parcel, readInt);
                    break;
                case 5:
                    str2 = C1147b.m2968c(parcel, readInt);
                    break;
                case 6:
                    str3 = C1147b.m2968c(parcel, readInt);
                    break;
                case 7:
                    bundle = C1147b.m2966a(parcel, readInt);
                    break;
                case 8:
                    str4 = C1147b.m2968c(parcel, readInt);
                    break;
                default:
                    C1147b.m2976k(parcel, readInt);
                    break;
            }
        }
        C1147b.m2970e(parcel, m2977l);
        return new C1603k0(j6, j7, z5, str, str2, str3, bundle, str4);
    }

    @Override // android.os.Parcelable.Creator
    public final /* bridge */ /* synthetic */ C1603k0[] newArray(int i6) {
        return new C1603k0[i6];
    }
}
