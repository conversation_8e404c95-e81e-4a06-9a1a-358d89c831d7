package p152w2;

import java.lang.reflect.Field;
import java.nio.Buffer;
import java.security.AccessController;
import java.util.logging.Level;
import java.util.logging.Logger;
import p090n.C1094g;
import sun.misc.Unsafe;

/* renamed from: w2.z6 */
/* loaded from: classes.dex */
public final class C1789z6 {

    /* renamed from: a */
    public static final Unsafe f7307a;

    /* renamed from: b */
    public static final Class<?> f7308b;

    /* renamed from: c */
    public static final AbstractC1777y6 f7309c;

    /* renamed from: d */
    public static final boolean f7310d;

    /* renamed from: e */
    public static final boolean f7311e;

    /* renamed from: f */
    public static final long f7312f;

    /* renamed from: g */
    public static final boolean f7313g;

    /* JADX WARN: Removed duplicated region for block: B:12:0x0068  */
    /* JADX WARN: Removed duplicated region for block: B:15:0x0077  */
    /* JADX WARN: Removed duplicated region for block: B:22:0x0126  */
    /* JADX WARN: Removed duplicated region for block: B:27:0x0136  */
    static {
        /*
            Method dump skipped, instructions count: 314
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1789z6.<clinit>():void");
    }

    /* renamed from: a */
    public static int m4483a(Class<?> cls) {
        if (f7311e) {
            return f7309c.m4466j(cls);
        }
        return -1;
    }

    /* renamed from: b */
    public static Field m4484b() {
        Field field;
        Field field2;
        int i6 = C1666p3.f7134a;
        try {
            field = Buffer.class.getDeclaredField("effectiveDirectAddress");
        } catch (Throwable unused) {
            field = null;
        }
        if (field != null) {
            return field;
        }
        try {
            field2 = Buffer.class.getDeclaredField("address");
        } catch (Throwable unused2) {
            field2 = null;
        }
        if (field2 == null || field2.getType() != Long.TYPE) {
            return null;
        }
        return field2;
    }

    /* renamed from: c */
    public static void m4485c(Object obj, long j6, byte b6) {
        long j7 = (-4) & j6;
        AbstractC1777y6 abstractC1777y6 = f7309c;
        int m4467k = abstractC1777y6.m4467k(obj, j7);
        int i6 = ((~((int) j6)) & 3) << 3;
        abstractC1777y6.m4468l(obj, j7, ((255 & b6) << i6) | (m4467k & (~(255 << i6))));
    }

    /* renamed from: d */
    public static void m4486d(Object obj, long j6, byte b6) {
        long j7 = (-4) & j6;
        AbstractC1777y6 abstractC1777y6 = f7309c;
        int i6 = (((int) j6) & 3) << 3;
        abstractC1777y6.m4468l(obj, j7, ((255 & b6) << i6) | (abstractC1777y6.m4467k(obj, j7) & (~(255 << i6))));
    }

    /* renamed from: e */
    public static <T> T m4487e(Class<T> cls) {
        try {
            return (T) f7307a.allocateInstance(cls);
        } catch (InstantiationException e6) {
            throw new IllegalStateException(e6);
        }
    }

    /* renamed from: f */
    public static int m4488f(Object obj, long j6) {
        return f7309c.m4467k(obj, j6);
    }

    /* renamed from: g */
    public static void m4489g(Object obj, long j6, int i6) {
        f7309c.m4468l(obj, j6, i6);
    }

    /* renamed from: h */
    public static long m4490h(Object obj, long j6) {
        return f7309c.m4469m(obj, j6);
    }

    /* renamed from: i */
    public static void m4491i(Object obj, long j6, long j7) {
        f7309c.m4470n(obj, j6, j7);
    }

    /* renamed from: j */
    public static boolean m4492j(Object obj, long j6) {
        return f7309c.mo4391b(obj, j6);
    }

    /* renamed from: k */
    public static void m4493k(Object obj, long j6, boolean z5) {
        f7309c.mo4392c(obj, j6, z5);
    }

    /* renamed from: l */
    public static float m4494l(Object obj, long j6) {
        return f7309c.mo4393d(obj, j6);
    }

    /* renamed from: m */
    public static void m4495m(Object obj, long j6, float f6) {
        f7309c.mo4394e(obj, j6, f6);
    }

    /* renamed from: n */
    public static double m4496n(Object obj, long j6) {
        return f7309c.mo4395f(obj, j6);
    }

    /* renamed from: o */
    public static void m4497o(Object obj, long j6, double d6) {
        f7309c.mo4396g(obj, j6, d6);
    }

    /* renamed from: p */
    public static Object m4498p(Object obj, long j6) {
        return f7309c.m4471o(obj, j6);
    }

    /* renamed from: q */
    public static void m4499q(Object obj, long j6, Object obj2) {
        f7309c.m4472p(obj, j6, obj2);
    }

    /* renamed from: r */
    public static Unsafe m4500r() {
        try {
            return (Unsafe) AccessController.doPrivileged(new C1741v6());
        } catch (Throwable unused) {
            return null;
        }
    }

    /* renamed from: s */
    public static boolean m4501s(Class<?> cls) {
        int i6 = C1666p3.f7134a;
        try {
            Class<?> cls2 = f7308b;
            Class<?> cls3 = Boolean.TYPE;
            cls2.getMethod("peekLong", cls, cls3);
            cls2.getMethod("pokeLong", cls, Long.TYPE, cls3);
            Class<?> cls4 = Integer.TYPE;
            cls2.getMethod("pokeInt", cls, cls4, cls3);
            cls2.getMethod("peekInt", cls, cls3);
            cls2.getMethod("pokeByte", cls, Byte.TYPE);
            cls2.getMethod("peekByte", cls);
            cls2.getMethod("pokeByteArray", cls, byte[].class, cls4, cls4);
            cls2.getMethod("peekByteArray", cls, byte[].class, cls4, cls4);
            return true;
        } catch (Throwable unused) {
            return false;
        }
    }

    /* renamed from: t */
    public static /* synthetic */ void m4502t(Throwable th) {
        Logger logger = Logger.getLogger(C1789z6.class.getName());
        Level level = Level.WARNING;
        String valueOf = String.valueOf(th);
        logger.logp(level, "com.google.protobuf.UnsafeUtil", "logMissingMethod", C1094g.m2839c(new StringBuilder(valueOf.length() + 71), "platform method missing - proto runtime falling back to safer methods: ", valueOf));
    }

    /* renamed from: u */
    public static /* synthetic */ boolean m4503u(Object obj, long j6) {
        return ((byte) ((f7309c.m4467k(obj, (-4) & j6) >>> ((int) (((~j6) & 3) << 3))) & 255)) != 0;
    }

    /* renamed from: v */
    public static /* synthetic */ boolean m4504v(Object obj, long j6) {
        return ((byte) ((f7309c.m4467k(obj, (-4) & j6) >>> ((int) ((j6 & 3) << 3))) & 255)) != 0;
    }

    /* renamed from: w */
    public static int m4505w(Class<?> cls) {
        if (f7311e) {
            return f7309c.m4465i(cls);
        }
        return -1;
    }
}
