package p152w2;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import p090n.C1090c;
import p153w3.C1798e;

/* renamed from: w2.j */
/* loaded from: classes.dex */
public abstract class AbstractC1590j implements InterfaceC1662p, InterfaceC1614l {

    /* renamed from: j */
    public final String f7015j;

    /* renamed from: k */
    public final Map<String, InterfaceC1662p> f7016k = new HashMap();

    public AbstractC1590j(String str) {
        this.f7015j = str;
    }

    /* renamed from: a */
    public abstract InterfaceC1662p mo3670a(C1090c c1090c, List<InterfaceC1662p> list);

    @Override // p152w2.InterfaceC1662p
    /* renamed from: c */
    public final String mo3761c() {
        return this.f7015j;
    }

    @Override // p152w2.InterfaceC1662p
    /* renamed from: d */
    public final Double mo3762d() {
        return Double.valueOf(Double.NaN);
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof AbstractC1590j)) {
            return false;
        }
        AbstractC1590j abstractC1590j = (AbstractC1590j) obj;
        String str = this.f7015j;
        if (str != null) {
            return str.equals(abstractC1590j.f7015j);
        }
        return false;
    }

    @Override // p152w2.InterfaceC1662p
    /* renamed from: g */
    public final Boolean mo3763g() {
        return Boolean.TRUE;
    }

    public final int hashCode() {
        String str = this.f7015j;
        if (str != null) {
            return str.hashCode();
        }
        return 0;
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.HashMap, java.util.Map<java.lang.String, w2.p>] */
    /* JADX WARN: Type inference failed for: r3v1, types: [java.util.HashMap, java.util.Map<java.lang.String, w2.p>] */
    @Override // p152w2.InterfaceC1614l
    /* renamed from: i */
    public final void mo3764i(String str, InterfaceC1662p interfaceC1662p) {
        if (interfaceC1662p == null) {
            this.f7016k.remove(str);
        } else {
            this.f7016k.put(str, interfaceC1662p);
        }
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.HashMap, java.util.Map<java.lang.String, w2.p>] */
    @Override // p152w2.InterfaceC1614l
    /* renamed from: j */
    public final boolean mo3765j(String str) {
        return this.f7016k.containsKey(str);
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.HashMap, java.util.Map<java.lang.String, w2.p>] */
    @Override // p152w2.InterfaceC1662p
    /* renamed from: l */
    public final Iterator<InterfaceC1662p> mo3766l() {
        return new C1602k(this.f7016k.keySet().iterator());
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.HashMap, java.util.Map<java.lang.String, w2.p>] */
    /* JADX WARN: Type inference failed for: r0v2, types: [java.util.HashMap, java.util.Map<java.lang.String, w2.p>] */
    @Override // p152w2.InterfaceC1614l
    /* renamed from: m */
    public final InterfaceC1662p mo3767m(String str) {
        return this.f7016k.containsKey(str) ? (InterfaceC1662p) this.f7016k.get(str) : InterfaceC1662p.f7126b;
    }

    @Override // p152w2.InterfaceC1662p
    /* renamed from: r */
    public InterfaceC1662p mo3771r() {
        return this;
    }

    @Override // p152w2.InterfaceC1662p
    /* renamed from: s */
    public final InterfaceC1662p mo3772s(String str, C1090c c1090c, List<InterfaceC1662p> list) {
        return "toString".equals(str) ? new C1698s(this.f7015j) : C1798e.m4532Y(this, new C1698s(str), c1090c, list);
    }
}
