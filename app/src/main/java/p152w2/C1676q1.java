package p152w2;

/* renamed from: w2.q1 */
/* loaded from: classes.dex */
public final class C1676q1 extends C1643n4<C1688r1, C1676q1> {
    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public C1676q1() {
        /*
            r1 = this;
            w2.r1 r0 = p152w2.C1688r1.m4050v()
            r1.<init>(r0)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1676q1.<init>():void");
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public /* synthetic */ C1676q1(p008b0.C0385m r1) {
        /*
            r0 = this;
            w2.r1 r1 = p152w2.C1688r1.m4050v()
            r0.<init>(r1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1676q1.<init>(b0.m):void");
    }

    /* renamed from: l */
    public final C1712t1 m4023l() {
        return ((C1688r1) this.f7099k).m4053t();
    }

    /* renamed from: m */
    public final C1676q1 m4024m(C1700s1 c1700s1) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1688r1.m4051w((C1688r1) this.f7099k, c1700s1.m3947f());
        return this;
    }
}
