package p152w2;

import java.util.Iterator;
import java.util.Map;

/* renamed from: w2.p5 */
/* loaded from: classes.dex */
public final class C1668p5 {
    /* renamed from: a */
    public static final void m4009a(Object obj, Object obj2) {
        C1656o5 c1656o5 = (C1656o5) obj;
        if (c1656o5.isEmpty()) {
            return;
        }
        Iterator it = c1656o5.entrySet().iterator();
        if (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next();
            entry.getKey();
            entry.getValue();
            throw null;
        }
    }

    /* renamed from: b */
    public static final Object m4010b(Object obj, Object obj2) {
        C1656o5 c1656o5 = (C1656o5) obj;
        C1656o5 c1656o52 = (C1656o5) obj2;
        if (!c1656o52.isEmpty()) {
            if (!c1656o5.f7122j) {
                c1656o5 = c1656o5.isEmpty() ? new C1656o5() : new C1656o5(c1656o5);
            }
            c1656o5.m3969b();
            if (!c1656o52.isEmpty()) {
                c1656o5.putAll(c1656o52);
            }
        }
        return c1656o5;
    }
}
