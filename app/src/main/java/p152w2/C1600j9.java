package p152w2;

/* renamed from: w2.j9 */
/* loaded from: classes.dex */
public final class C1600j9 implements InterfaceC1588i9 {

    /* renamed from: a */
    public static final AbstractC1773y2<<PERSON>olean> f7040a;

    /* renamed from: b */
    public static final AbstractC1773y2<Boolean> f7041b;

    static {
        C1749w2 c1749w2 = new C1749w2(C1713t2.m4275a());
        c1749w2.m4354a("measurement.id.lifecycle.app_in_background_parameter", 0L);
        f7040a = (C1737v2) c1749w2.m4355b("measurement.lifecycle.app_backgrounded_engagement", false);
        c1749w2.m4355b("measurement.lifecycle.app_backgrounded_tracking", true);
        f7041b = (C1737v2) c1749w2.m4355b("measurement.lifecycle.app_in_background_parameter", false);
        c1749w2.m4354a("measurement.id.lifecycle.app_backgrounded_tracking", 0L);
    }

    @Override // p152w2.InterfaceC1588i9
    /* renamed from: a */
    public final boolean mo3867a() {
        return f7040a.m4460c().booleanValue();
    }

    @Override // p152w2.InterfaceC1588i9
    /* renamed from: b */
    public final boolean mo3868b() {
        return f7041b.m4460c().booleanValue();
    }
}
