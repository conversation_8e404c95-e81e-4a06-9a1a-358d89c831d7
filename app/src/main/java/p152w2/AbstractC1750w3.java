package p152w2;

import java.io.Serializable;
import java.nio.charset.Charset;
import java.util.Iterator;
import java.util.Locale;
import p008b0.C0385m;
import p153w3.C1798e;

/* renamed from: w2.w3 */
/* loaded from: classes.dex */
public abstract class AbstractC1750w3 implements Iterable<Byte>, Serializable {

    /* renamed from: k */
    public static final C1738v3 f7244k = new C1738v3(C1775y4.f7290b);

    /* renamed from: j */
    public int f7245j = 0;

    static {
        int i6 = C1666p3.f7134a;
    }

    /* renamed from: p */
    public static AbstractC1750w3 m4357p(byte[] bArr, int i6, int i7) {
        m4358q(i6, i6 + i7, bArr.length);
        byte[] bArr2 = new byte[i7];
        System.arraycopy(bArr, i6, bArr2, 0, i7);
        return new C1738v3(bArr2);
    }

    /* renamed from: q */
    public static int m4358q(int i6, int i7, int i8) {
        int i9 = i7 - i6;
        if ((i6 | i7 | i9 | (i8 - i7)) >= 0) {
            return i9;
        }
        if (i6 < 0) {
            StringBuilder sb = new StringBuilder(32);
            sb.append("Beginning index: ");
            sb.append(i6);
            sb.append(" < 0");
            throw new IndexOutOfBoundsException(sb.toString());
        }
        if (i7 < i6) {
            StringBuilder sb2 = new StringBuilder(66);
            sb2.append("Beginning index larger than ending index: ");
            sb2.append(i6);
            sb2.append(", ");
            sb2.append(i7);
            throw new IndexOutOfBoundsException(sb2.toString());
        }
        StringBuilder sb3 = new StringBuilder(37);
        sb3.append("End index: ");
        sb3.append(i7);
        sb3.append(" >= ");
        sb3.append(i8);
        throw new IndexOutOfBoundsException(sb3.toString());
    }

    /* renamed from: c */
    public abstract byte mo4285c(int i6);

    /* renamed from: d */
    public abstract byte mo4286d(int i6);

    public abstract boolean equals(Object obj);

    /* renamed from: g */
    public abstract int mo4287g();

    public final int hashCode() {
        int i6 = this.f7245j;
        if (i6 == 0) {
            int mo4287g = mo4287g();
            i6 = mo4331o(mo4287g, mo4287g);
            if (i6 == 0) {
                i6 = 1;
            }
            this.f7245j = i6;
        }
        return i6;
    }

    /* renamed from: i */
    public abstract AbstractC1750w3 mo4327i();

    @Override // java.lang.Iterable
    public final /* bridge */ /* synthetic */ Iterator<Byte> iterator() {
        return new C1702s3(this);
    }

    /* renamed from: j */
    public abstract void mo4328j(C0385m c0385m);

    /* renamed from: l */
    public abstract String mo4329l(Charset charset);

    /* renamed from: m */
    public abstract boolean mo4330m();

    /* renamed from: o */
    public abstract int mo4331o(int i6, int i7);

    public final String toString() {
        Locale locale = Locale.ROOT;
        Object[] objArr = new Object[3];
        objArr[0] = Integer.toHexString(System.identityHashCode(this));
        objArr[1] = Integer.valueOf(mo4287g());
        objArr[2] = mo4287g() <= 50 ? C1798e.m4531X(this) : String.valueOf(C1798e.m4531X(mo4327i())).concat("...");
        return String.format(locale, "<ByteString@%s size=%d contents=\"%s\">", objArr);
    }
}
