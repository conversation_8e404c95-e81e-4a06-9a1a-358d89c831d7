package p152w2;

/* renamed from: w2.w8 */
/* loaded from: classes.dex */
public final class C1755w8 implements InterfaceC1743v8 {

    /* renamed from: a */
    public static final AbstractC1773y2<Boolean> f7263a;

    static {
        C1749w2 c1749w2 = new C1749w2(C1713t2.m4275a());
        c1749w2.m4355b("measurement.collection.efficient_engagement_reporting_enabled_2", true);
        f7263a = (C1737v2) c1749w2.m4355b("measurement.collection.redundant_engagement_removal_enabled", false);
        c1749w2.m4354a("measurement.id.collection.redundant_engagement_removal_enabled", 0L);
    }

    @Override // p152w2.InterfaceC1743v8
    /* renamed from: a */
    public final boolean mo4336a() {
        return f7263a.m4460c().booleanValue();
    }
}
