package p152w2;

import java.util.logging.Level;

/* renamed from: w2.x3 */
/* loaded from: classes.dex */
public final class C1762x3 extends AbstractC1786z3 {

    /* renamed from: J0 */
    public final byte[] f7268J0;

    /* renamed from: K0 */
    public final int f7269K0;

    /* renamed from: L0 */
    public int f7270L0;

    public C1762x3(byte[] bArr, int i6) {
        super(null);
        int length = bArr.length;
        if (((length - i6) | i6) < 0) {
            throw new IllegalArgumentException(String.format("Array range is invalid. Buffer.length=%d, offset=%d, length=%d", Integer.valueOf(length), 0, Integer.valueOf(i6)));
        }
        this.f7268J0 = bArr;
        this.f7270L0 = 0;
        this.f7269K0 = i6;
    }

    @Override // p152w2.AbstractC1786z3
    /* renamed from: L */
    public final void mo4405L(int i6, int i7) {
        mo4416W((i6 << 3) | i7);
    }

    @Override // p152w2.AbstractC1786z3
    /* renamed from: M */
    public final void mo4406M(int i6, int i7) {
        mo4416W(i6 << 3);
        mo4415V(i7);
    }

    @Override // p152w2.AbstractC1786z3
    /* renamed from: N */
    public final void mo4407N(int i6, int i7) {
        mo4416W(i6 << 3);
        mo4416W(i7);
    }

    @Override // p152w2.AbstractC1786z3
    /* renamed from: O */
    public final void mo4408O(int i6, int i7) {
        mo4416W((i6 << 3) | 5);
        mo4417X(i7);
    }

    @Override // p152w2.AbstractC1786z3
    /* renamed from: P */
    public final void mo4409P(int i6, long j6) {
        mo4416W(i6 << 3);
        mo4418Y(j6);
    }

    @Override // p152w2.AbstractC1786z3
    /* renamed from: Q */
    public final void mo4410Q(int i6, long j6) {
        mo4416W((i6 << 3) | 1);
        mo4419Z(j6);
    }

    @Override // p152w2.AbstractC1786z3
    /* renamed from: R */
    public final void mo4411R(int i6, boolean z5) {
        mo4416W(i6 << 3);
        mo4414U(z5 ? (byte) 1 : (byte) 0);
    }

    @Override // p152w2.AbstractC1786z3
    /* renamed from: S */
    public final void mo4412S(int i6, String str) {
        int m3666c;
        mo4416W((i6 << 3) | 2);
        int i7 = this.f7270L0;
        try {
            int m4480c0 = AbstractC1786z3.m4480c0(str.length() * 3);
            int m4480c02 = AbstractC1786z3.m4480c0(str.length());
            if (m4480c02 == m4480c0) {
                int i8 = i7 + m4480c02;
                this.f7270L0 = i8;
                m3666c = C1500b7.m3666c(str, this.f7268J0, i8, this.f7269K0 - i8);
                this.f7270L0 = i7;
                mo4416W((m3666c - i7) - m4480c02);
            } else {
                mo4416W(C1500b7.m3665b(str));
                byte[] bArr = this.f7268J0;
                int i9 = this.f7270L0;
                m3666c = C1500b7.m3666c(str, bArr, i9, this.f7269K0 - i9);
            }
            this.f7270L0 = m3666c;
        } catch (IndexOutOfBoundsException e6) {
            throw new C1774y3(e6);
        } catch (C1487a7 e7) {
            this.f7270L0 = i7;
            AbstractC1786z3.f7302H0.logp(Level.WARNING, "com.google.protobuf.CodedOutputStream", "inefficientWriteStringNoTag", "Converting ill-formed UTF-16. Your Protocol Buffer will not round trip correctly!", (Throwable) e7);
            byte[] bytes = str.getBytes(C1775y4.f7289a);
            try {
                int length = bytes.length;
                mo4416W(length);
                m4420f0(bytes, length);
            } catch (IndexOutOfBoundsException e8) {
                throw new C1774y3(e8);
            } catch (C1774y3 e9) {
                throw e9;
            }
        }
    }

    @Override // p152w2.AbstractC1786z3
    /* renamed from: T */
    public final void mo4413T(int i6, AbstractC1750w3 abstractC1750w3) {
        mo4416W((i6 << 3) | 2);
        mo4416W(abstractC1750w3.mo4287g());
        abstractC1750w3.mo4328j(this);
    }

    @Override // p152w2.AbstractC1786z3
    /* renamed from: U */
    public final void mo4414U(byte b6) {
        try {
            byte[] bArr = this.f7268J0;
            int i6 = this.f7270L0;
            this.f7270L0 = i6 + 1;
            bArr[i6] = b6;
        } catch (IndexOutOfBoundsException e6) {
            throw new C1774y3(String.format("Pos: %d, limit: %d, len: %d", Integer.valueOf(this.f7270L0), Integer.valueOf(this.f7269K0), 1), e6);
        }
    }

    @Override // p152w2.AbstractC1786z3
    /* renamed from: V */
    public final void mo4415V(int i6) {
        if (i6 >= 0) {
            mo4416W(i6);
        } else {
            mo4418Y(i6);
        }
    }

    @Override // p152w2.AbstractC1786z3
    /* renamed from: W */
    public final void mo4416W(int i6) {
        if (AbstractC1786z3.f7303I0) {
            int i7 = C1666p3.f7134a;
        }
        while ((i6 & (-128)) != 0) {
            try {
                byte[] bArr = this.f7268J0;
                int i8 = this.f7270L0;
                this.f7270L0 = i8 + 1;
                bArr[i8] = (byte) ((i6 & 127) | 128);
                i6 >>>= 7;
            } catch (IndexOutOfBoundsException e6) {
                throw new C1774y3(String.format("Pos: %d, limit: %d, len: %d", Integer.valueOf(this.f7270L0), Integer.valueOf(this.f7269K0), 1), e6);
            }
        }
        byte[] bArr2 = this.f7268J0;
        int i9 = this.f7270L0;
        this.f7270L0 = i9 + 1;
        bArr2[i9] = (byte) i6;
    }

    @Override // p152w2.AbstractC1786z3
    /* renamed from: X */
    public final void mo4417X(int i6) {
        try {
            byte[] bArr = this.f7268J0;
            int i7 = this.f7270L0;
            int i8 = i7 + 1;
            this.f7270L0 = i8;
            bArr[i7] = (byte) (i6 & 255);
            int i9 = i8 + 1;
            this.f7270L0 = i9;
            bArr[i8] = (byte) ((i6 >> 8) & 255);
            int i10 = i9 + 1;
            this.f7270L0 = i10;
            bArr[i9] = (byte) ((i6 >> 16) & 255);
            this.f7270L0 = i10 + 1;
            bArr[i10] = (byte) ((i6 >> 24) & 255);
        } catch (IndexOutOfBoundsException e6) {
            throw new C1774y3(String.format("Pos: %d, limit: %d, len: %d", Integer.valueOf(this.f7270L0), Integer.valueOf(this.f7269K0), 1), e6);
        }
    }

    @Override // p152w2.AbstractC1786z3
    /* renamed from: Y */
    public final void mo4418Y(long j6) {
        if (AbstractC1786z3.f7303I0 && this.f7269K0 - this.f7270L0 >= 10) {
            while ((j6 & (-128)) != 0) {
                byte[] bArr = this.f7268J0;
                int i6 = this.f7270L0;
                this.f7270L0 = i6 + 1;
                C1789z6.f7309c.mo4390a(bArr, C1789z6.f7312f + i6, (byte) ((((int) j6) & 127) | 128));
                j6 >>>= 7;
            }
            byte[] bArr2 = this.f7268J0;
            int i7 = this.f7270L0;
            this.f7270L0 = i7 + 1;
            C1789z6.f7309c.mo4390a(bArr2, C1789z6.f7312f + i7, (byte) j6);
            return;
        }
        while ((j6 & (-128)) != 0) {
            try {
                byte[] bArr3 = this.f7268J0;
                int i8 = this.f7270L0;
                this.f7270L0 = i8 + 1;
                bArr3[i8] = (byte) ((((int) j6) & 127) | 128);
                j6 >>>= 7;
            } catch (IndexOutOfBoundsException e6) {
                throw new C1774y3(String.format("Pos: %d, limit: %d, len: %d", Integer.valueOf(this.f7270L0), Integer.valueOf(this.f7269K0), 1), e6);
            }
        }
        byte[] bArr4 = this.f7268J0;
        int i9 = this.f7270L0;
        this.f7270L0 = i9 + 1;
        bArr4[i9] = (byte) j6;
    }

    @Override // p152w2.AbstractC1786z3
    /* renamed from: Z */
    public final void mo4419Z(long j6) {
        try {
            byte[] bArr = this.f7268J0;
            int i6 = this.f7270L0;
            int i7 = i6 + 1;
            this.f7270L0 = i7;
            bArr[i6] = (byte) (((int) j6) & 255);
            int i8 = i7 + 1;
            this.f7270L0 = i8;
            bArr[i7] = (byte) (((int) (j6 >> 8)) & 255);
            int i9 = i8 + 1;
            this.f7270L0 = i9;
            bArr[i8] = (byte) (((int) (j6 >> 16)) & 255);
            int i10 = i9 + 1;
            this.f7270L0 = i10;
            bArr[i9] = (byte) (((int) (j6 >> 24)) & 255);
            int i11 = i10 + 1;
            this.f7270L0 = i11;
            bArr[i10] = (byte) (((int) (j6 >> 32)) & 255);
            int i12 = i11 + 1;
            this.f7270L0 = i12;
            bArr[i11] = (byte) (((int) (j6 >> 40)) & 255);
            int i13 = i12 + 1;
            this.f7270L0 = i13;
            bArr[i12] = (byte) (((int) (j6 >> 48)) & 255);
            this.f7270L0 = i13 + 1;
            bArr[i13] = (byte) (((int) (j6 >> 56)) & 255);
        } catch (IndexOutOfBoundsException e6) {
            throw new C1774y3(String.format("Pos: %d, limit: %d, len: %d", Integer.valueOf(this.f7270L0), Integer.valueOf(this.f7269K0), 1), e6);
        }
    }

    /* renamed from: f0 */
    public final void m4420f0(byte[] bArr, int i6) {
        try {
            System.arraycopy(bArr, 0, this.f7268J0, this.f7270L0, i6);
            this.f7270L0 += i6;
        } catch (IndexOutOfBoundsException e6) {
            throw new C1774y3(String.format("Pos: %d, limit: %d, len: %d", Integer.valueOf(this.f7270L0), Integer.valueOf(this.f7269K0), Integer.valueOf(i6)), e6);
        }
    }
}
