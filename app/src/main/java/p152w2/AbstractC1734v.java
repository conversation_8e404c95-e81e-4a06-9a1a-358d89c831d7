package p152w2;

import java.util.ArrayList;
import java.util.List;
import p008b0.C0385m;
import p090n.C1090c;

/* renamed from: w2.v */
/* loaded from: classes.dex */
public abstract class AbstractC1734v {

    /* renamed from: a */
    public final List<EnumC1480a0> f7199a = new ArrayList();

    /* renamed from: a */
    public abstract InterfaceC1662p mo4283a(String str, C1090c c1090c, List<InterfaceC1662p> list);

    /* JADX WARN: Type inference failed for: r0v0, types: [java.util.ArrayList, java.util.List<w2.a0>] */
    /* renamed from: b */
    public final InterfaceC1662p m4326b(String str) {
        if (!this.f7199a.contains(C0385m.m1405B(str))) {
            throw new IllegalArgumentException("Command not supported");
        }
        String valueOf = String.valueOf(str);
        throw new UnsupportedOperationException(valueOf.length() != 0 ? "Command not implemented: ".concat(valueOf) : new String("Command not implemented: "));
    }
}
