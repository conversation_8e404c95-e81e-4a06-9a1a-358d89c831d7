package p152w2;

import java.nio.charset.Charset;
import java.util.AbstractList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Objects;
import java.util.RandomAccess;

/* renamed from: w2.i5 */
/* loaded from: classes.dex */
public final class C1584i5 extends AbstractC1654o3<Long> implements RandomAccess, InterfaceC1751w4, InterfaceC1486a6 {

    /* renamed from: m */
    public static final C1584i5 f7009m;

    /* renamed from: k */
    public long[] f7010k;

    /* renamed from: l */
    public int f7011l;

    static {
        C1584i5 c1584i5 = new C1584i5(new long[0], 0);
        f7009m = c1584i5;
        c1584i5.f7118j = false;
    }

    public C1584i5() {
        this(new long[10], 0);
    }

    public C1584i5(long[] jArr, int i6) {
        this.f7010k = jArr;
        this.f7011l = i6;
    }

    @Override // java.util.AbstractList, java.util.List
    public final /* bridge */ /* synthetic */ void add(int i6, Object obj) {
        int i7;
        long longValue = ((Long) obj).longValue();
        m3967c();
        if (i6 < 0 || i6 > (i7 = this.f7011l)) {
            throw new IndexOutOfBoundsException(m3864l(i6));
        }
        long[] jArr = this.f7010k;
        if (i7 < jArr.length) {
            System.arraycopy(jArr, i6, jArr, i6 + 1, i7 - i6);
        } else {
            long[] jArr2 = new long[((i7 * 3) / 2) + 1];
            System.arraycopy(jArr, 0, jArr2, 0, i6);
            System.arraycopy(this.f7010k, i6, jArr2, i6 + 1, this.f7011l - i6);
            this.f7010k = jArr2;
        }
        this.f7010k[i6] = longValue;
        this.f7011l++;
        ((AbstractList) this).modCount++;
    }

    @Override // p152w2.AbstractC1654o3, java.util.AbstractList, java.util.AbstractCollection, java.util.Collection, java.util.List
    public final /* bridge */ /* synthetic */ boolean add(Object obj) {
        m3862i(((Long) obj).longValue());
        return true;
    }

    @Override // p152w2.AbstractC1654o3, java.util.AbstractCollection, java.util.Collection, java.util.List
    public final boolean addAll(Collection<? extends Long> collection) {
        m3967c();
        Charset charset = C1775y4.f7289a;
        Objects.requireNonNull(collection);
        if (!(collection instanceof C1584i5)) {
            return super.addAll(collection);
        }
        C1584i5 c1584i5 = (C1584i5) collection;
        int i6 = c1584i5.f7011l;
        if (i6 == 0) {
            return false;
        }
        int i7 = this.f7011l;
        if (Integer.MAX_VALUE - i7 < i6) {
            throw new OutOfMemoryError();
        }
        int i8 = i7 + i6;
        long[] jArr = this.f7010k;
        if (i8 > jArr.length) {
            this.f7010k = Arrays.copyOf(jArr, i8);
        }
        System.arraycopy(c1584i5.f7010k, 0, this.f7010k, this.f7011l, c1584i5.f7011l);
        this.f7011l = i8;
        ((AbstractList) this).modCount++;
        return true;
    }

    @Override // java.util.AbstractCollection, java.util.Collection, java.util.List
    public final boolean contains(Object obj) {
        return indexOf(obj) != -1;
    }

    /* renamed from: d */
    public final long m3860d(int i6) {
        m3863j(i6);
        return this.f7010k[i6];
    }

    @Override // p152w2.AbstractC1654o3, java.util.AbstractList, java.util.Collection, java.util.List
    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof C1584i5)) {
            return super.equals(obj);
        }
        C1584i5 c1584i5 = (C1584i5) obj;
        if (this.f7011l != c1584i5.f7011l) {
            return false;
        }
        long[] jArr = c1584i5.f7010k;
        for (int i6 = 0; i6 < this.f7011l; i6++) {
            if (this.f7010k[i6] != jArr[i6]) {
                return false;
            }
        }
        return true;
    }

    @Override // p152w2.InterfaceC1763x4
    /* renamed from: g, reason: merged with bridge method [inline-methods] */
    public final InterfaceC1751w4 mo3662k(int i6) {
        if (i6 >= this.f7011l) {
            return new C1584i5(Arrays.copyOf(this.f7010k, i6), this.f7011l);
        }
        throw new IllegalArgumentException();
    }

    @Override // java.util.AbstractList, java.util.List
    public final /* bridge */ /* synthetic */ Object get(int i6) {
        m3863j(i6);
        return Long.valueOf(this.f7010k[i6]);
    }

    @Override // p152w2.AbstractC1654o3, java.util.AbstractList, java.util.Collection, java.util.List
    public final int hashCode() {
        int i6 = 1;
        for (int i7 = 0; i7 < this.f7011l; i7++) {
            i6 = (i6 * 31) + C1775y4.m4461a(this.f7010k[i7]);
        }
        return i6;
    }

    /* renamed from: i */
    public final void m3862i(long j6) {
        m3967c();
        int i6 = this.f7011l;
        long[] jArr = this.f7010k;
        if (i6 == jArr.length) {
            long[] jArr2 = new long[((i6 * 3) / 2) + 1];
            System.arraycopy(jArr, 0, jArr2, 0, i6);
            this.f7010k = jArr2;
        }
        long[] jArr3 = this.f7010k;
        int i7 = this.f7011l;
        this.f7011l = i7 + 1;
        jArr3[i7] = j6;
    }

    @Override // java.util.AbstractList, java.util.List
    public final int indexOf(Object obj) {
        if (!(obj instanceof Long)) {
            return -1;
        }
        long longValue = ((Long) obj).longValue();
        int i6 = this.f7011l;
        for (int i7 = 0; i7 < i6; i7++) {
            if (this.f7010k[i7] == longValue) {
                return i7;
            }
        }
        return -1;
    }

    /* renamed from: j */
    public final void m3863j(int i6) {
        if (i6 < 0 || i6 >= this.f7011l) {
            throw new IndexOutOfBoundsException(m3864l(i6));
        }
    }

    /* renamed from: l */
    public final String m3864l(int i6) {
        int i7 = this.f7011l;
        StringBuilder sb = new StringBuilder(35);
        sb.append("Index:");
        sb.append(i6);
        sb.append(", Size:");
        sb.append(i7);
        return sb.toString();
    }

    @Override // p152w2.AbstractC1654o3, java.util.AbstractList, java.util.List
    public final /* bridge */ /* synthetic */ Object remove(int i6) {
        m3967c();
        m3863j(i6);
        long[] jArr = this.f7010k;
        long j6 = jArr[i6];
        if (i6 < this.f7011l - 1) {
            System.arraycopy(jArr, i6 + 1, jArr, i6, (r3 - i6) - 1);
        }
        this.f7011l--;
        ((AbstractList) this).modCount++;
        return Long.valueOf(j6);
    }

    @Override // java.util.AbstractList
    public final void removeRange(int i6, int i7) {
        m3967c();
        if (i7 < i6) {
            throw new IndexOutOfBoundsException("toIndex < fromIndex");
        }
        long[] jArr = this.f7010k;
        System.arraycopy(jArr, i7, jArr, i6, this.f7011l - i7);
        this.f7011l -= i7 - i6;
        ((AbstractList) this).modCount++;
    }

    @Override // java.util.AbstractList, java.util.List
    public final /* bridge */ /* synthetic */ Object set(int i6, Object obj) {
        long longValue = ((Long) obj).longValue();
        m3967c();
        m3863j(i6);
        long[] jArr = this.f7010k;
        long j6 = jArr[i6];
        jArr[i6] = longValue;
        return Long.valueOf(j6);
    }

    @Override // java.util.AbstractCollection, java.util.Collection, java.util.List
    public final int size() {
        return this.f7011l;
    }
}
