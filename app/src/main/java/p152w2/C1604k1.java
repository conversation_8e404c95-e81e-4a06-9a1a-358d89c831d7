package p152w2;

import p008b0.C0385m;

/* renamed from: w2.k1 */
/* loaded from: classes.dex */
public final class C1604k1 extends AbstractC1691r4<C1604k1, C1592j1> implements InterfaceC1728u5 {
    private static final C1604k1 zzg;
    private int zza;
    private int zze;
    private long zzf;

    static {
        C1604k1 c1604k1 = new C1604k1();
        zzg = c1604k1;
        AbstractC1691r4.m4061q(C1604k1.class, c1604k1);
    }

    /* renamed from: w */
    public static C1592j1 m3875w() {
        return zzg.m4065m();
    }

    /* renamed from: y */
    public static /* synthetic */ void m3877y(C1604k1 c1604k1, int i6) {
        c1604k1.zza |= 1;
        c1604k1.zze = i6;
    }

    /* renamed from: z */
    public static /* synthetic */ void m3878z(C1604k1 c1604k1, long j6) {
        c1604k1.zza |= 2;
        c1604k1.zzf = j6;
    }

    @Override // p152w2.AbstractC1691r4
    /* renamed from: r */
    public final Object mo3604r(int i6) {
        int i7 = i6 - 1;
        if (i7 == 0) {
            return (byte) 1;
        }
        if (i7 == 2) {
            return new C1525d6(zzg, "\u0001\u0002\u0000\u0001\u0001\u0002\u0002\u0000\u0000\u0000\u0001င\u0000\u0002ဂ\u0001", new Object[]{"zza", "zze", "zzf"});
        }
        if (i7 == 3) {
            return new C1604k1();
        }
        C0385m c0385m = null;
        if (i7 == 4) {
            return new C1592j1(c0385m);
        }
        if (i7 != 5) {
            return null;
        }
        return zzg;
    }

    /* renamed from: s */
    public final boolean m3879s() {
        return (this.zza & 1) != 0;
    }

    /* renamed from: t */
    public final int m3880t() {
        return this.zze;
    }

    /* renamed from: u */
    public final boolean m3881u() {
        return (this.zza & 2) != 0;
    }

    /* renamed from: v */
    public final long m3882v() {
        return this.zzf;
    }
}
