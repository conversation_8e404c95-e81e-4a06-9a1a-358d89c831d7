package p152w2;

import java.util.Iterator;
import java.util.Map;
import java.util.Objects;
import p077l.AbstractC1052f;
import p077l.C1047a;

/* renamed from: w2.z2 */
/* loaded from: classes.dex */
public final class C1785z2 {

    /* renamed from: a */
    public static final Map<String, C1785z2> f7301a = new C1047a();

    /* JADX WARN: Type inference failed for: r1v1, types: [java.util.Map<java.lang.String, w2.z2>, l.a, l.g] */
    /* renamed from: a */
    public static synchronized void m4474a() {
        synchronized (C1785z2.class) {
            ?? r12 = f7301a;
            Iterator it = ((AbstractC1052f.e) r12.values()).iterator();
            if (it.hasNext()) {
                Objects.requireNonNull((C1785z2) it.next());
                throw null;
            }
            r12.clear();
        }
    }
}
