package p152w2;

/* renamed from: w2.y0 */
/* loaded from: classes.dex */
public final class C1771y0 extends AbstractC1691r4<C1771y0, C1759x0> implements InterfaceC1728u5 {
    private static final C1771y0 zzk;
    private int zza;
    private int zze;
    private String zzf = "";
    private C1711t0 zzg;
    private boolean zzh;
    private boolean zzi;
    private boolean zzj;

    static {
        C1771y0 c1771y0 = new C1771y0();
        zzk = c1771y0;
        AbstractC1691r4.m4061q(C1771y0.class, c1771y0);
    }

    /* renamed from: A */
    public static C1759x0 m4427A() {
        return zzk.m4065m();
    }

    /* renamed from: C */
    public static /* synthetic */ void m4429C(C1771y0 c1771y0, String str) {
        c1771y0.zza |= 2;
        c1771y0.zzf = str;
    }

    @Override // p152w2.AbstractC1691r4
    /* renamed from: r */
    public final Object mo3604r(int i6) {
        int i7 = i6 - 1;
        if (i7 == 0) {
            return (byte) 1;
        }
        if (i7 == 2) {
            return new C1525d6(zzk, "\u0001\u0006\u0000\u0001\u0001\u0006\u0006\u0000\u0000\u0000\u0001င\u0000\u0002ဈ\u0001\u0003ဉ\u0002\u0004ဇ\u0003\u0005ဇ\u0004\u0006ဇ\u0005", new Object[]{"zza", "zze", "zzf", "zzg", "zzh", "zzi", "zzj"});
        }
        if (i7 == 3) {
            return new C1771y0();
        }
        if (i7 == 4) {
            return new C1759x0(null);
        }
        if (i7 != 5) {
            return null;
        }
        return zzk;
    }

    /* renamed from: s */
    public final boolean m4430s() {
        return (this.zza & 1) != 0;
    }

    /* renamed from: t */
    public final int m4431t() {
        return this.zze;
    }

    /* renamed from: u */
    public final String m4432u() {
        return this.zzf;
    }

    /* renamed from: v */
    public final C1711t0 m4433v() {
        C1711t0 c1711t0 = this.zzg;
        return c1711t0 == null ? C1711t0.m4147A() : c1711t0;
    }

    /* renamed from: w */
    public final boolean m4434w() {
        return this.zzh;
    }

    /* renamed from: x */
    public final boolean m4435x() {
        return this.zzi;
    }

    /* renamed from: y */
    public final boolean m4436y() {
        return (this.zza & 32) != 0;
    }

    /* renamed from: z */
    public final boolean m4437z() {
        return this.zzj;
    }
}
