package p152w2;

/* renamed from: w2.y9 */
/* loaded from: classes.dex */
public final class C1780y9 implements InterfaceC1768x9 {

    /* renamed from: a */
    public static final AbstractC1773y2<Boolean> f7293a;

    /* renamed from: b */
    public static final AbstractC1773y2<Double> f7294b;

    /* renamed from: c */
    public static final AbstractC1773y2<Long> f7295c;

    /* renamed from: d */
    public static final AbstractC1773y2<Long> f7296d;

    /* renamed from: e */
    public static final AbstractC1773y2<String> f7297e;

    static {
        C1749w2 c1749w2 = new C1749w2(C1713t2.m4275a());
        f7293a = (C1737v2) c1749w2.m4355b("measurement.test.boolean_flag", false);
        f7294b = new C1725u2(c1749w2, Double.valueOf(-3.0d));
        f7295c = (C1725u2) c1749w2.m4354a("measurement.test.int_flag", -2L);
        f7296d = (C1725u2) c1749w2.m4354a("measurement.test.long_flag", -1L);
        f7297e = (C1737v2) c1749w2.m4356c("measurement.test.string_flag", "---");
    }

    @Override // p152w2.InterfaceC1768x9
    /* renamed from: a */
    public final boolean mo4422a() {
        return f7293a.m4460c().booleanValue();
    }

    @Override // p152w2.InterfaceC1768x9
    /* renamed from: b */
    public final double mo4423b() {
        return f7294b.m4460c().doubleValue();
    }

    @Override // p152w2.InterfaceC1768x9
    /* renamed from: c */
    public final long mo4424c() {
        return f7295c.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1768x9
    /* renamed from: d */
    public final long mo4425d() {
        return f7296d.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1768x9
    /* renamed from: g */
    public final String mo4426g() {
        return f7297e.m4460c();
    }
}
