package p152w2;

import java.nio.charset.Charset;
import java.util.Objects;
import p008b0.C0385m;

/* renamed from: w2.v3 */
/* loaded from: classes.dex */
public class C1738v3 extends AbstractC1750w3 {

    /* renamed from: l */
    public final byte[] f7203l;

    public C1738v3(byte[] bArr) {
        Objects.requireNonNull(bArr);
        this.f7203l = bArr;
    }

    @Override // p152w2.AbstractC1750w3
    /* renamed from: c */
    public byte mo4285c(int i6) {
        return this.f7203l[i6];
    }

    @Override // p152w2.AbstractC1750w3
    /* renamed from: d */
    public byte mo4286d(int i6) {
        return this.f7203l[i6];
    }

    @Override // p152w2.AbstractC1750w3
    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (!(obj instanceof AbstractC1750w3) || mo4287g() != ((AbstractC1750w3) obj).mo4287g()) {
            return false;
        }
        if (mo4287g() == 0) {
            return true;
        }
        if (!(obj instanceof C1738v3)) {
            return obj.equals(this);
        }
        C1738v3 c1738v3 = (C1738v3) obj;
        int i6 = this.f7245j;
        int i7 = c1738v3.f7245j;
        if (i6 != 0 && i7 != 0 && i6 != i7) {
            return false;
        }
        int mo4287g = mo4287g();
        if (mo4287g > c1738v3.mo4287g()) {
            int mo4287g2 = mo4287g();
            StringBuilder sb = new StringBuilder(40);
            sb.append("Length too large: ");
            sb.append(mo4287g);
            sb.append(mo4287g2);
            throw new IllegalArgumentException(sb.toString());
        }
        if (mo4287g > c1738v3.mo4287g()) {
            int mo4287g3 = c1738v3.mo4287g();
            StringBuilder sb2 = new StringBuilder(59);
            sb2.append("Ran off end of other: 0, ");
            sb2.append(mo4287g);
            sb2.append(", ");
            sb2.append(mo4287g3);
            throw new IllegalArgumentException(sb2.toString());
        }
        byte[] bArr = this.f7203l;
        byte[] bArr2 = c1738v3.f7203l;
        c1738v3.mo4288r();
        int i8 = 0;
        int i9 = 0;
        while (i8 < mo4287g) {
            if (bArr[i8] != bArr2[i9]) {
                return false;
            }
            i8++;
            i9++;
        }
        return true;
    }

    @Override // p152w2.AbstractC1750w3
    /* renamed from: g */
    public int mo4287g() {
        return this.f7203l.length;
    }

    @Override // p152w2.AbstractC1750w3
    /* renamed from: i */
    public final AbstractC1750w3 mo4327i() {
        int m4358q = AbstractC1750w3.m4358q(0, 47, mo4287g());
        return m4358q == 0 ? AbstractC1750w3.f7244k : new C1726u3(this.f7203l, m4358q);
    }

    @Override // p152w2.AbstractC1750w3
    /* renamed from: j */
    public final void mo4328j(C0385m c0385m) {
        ((C1762x3) c0385m).m4420f0(this.f7203l, mo4287g());
    }

    @Override // p152w2.AbstractC1750w3
    /* renamed from: l */
    public final String mo4329l(Charset charset) {
        return new String(this.f7203l, 0, mo4287g(), charset);
    }

    @Override // p152w2.AbstractC1750w3
    /* renamed from: m */
    public final boolean mo4330m() {
        return C1500b7.m3664a(this.f7203l, 0, mo4287g());
    }

    @Override // p152w2.AbstractC1750w3
    /* renamed from: o */
    public final int mo4331o(int i6, int i7) {
        byte[] bArr = this.f7203l;
        Charset charset = C1775y4.f7289a;
        for (int i8 = 0; i8 < i7; i8++) {
            i6 = (i6 * 31) + bArr[i8];
        }
        return i6;
    }

    /* renamed from: r */
    public void mo4288r() {
    }
}
