package p152w2;

import p008b0.C0385m;

/* renamed from: w2.w1 */
/* loaded from: classes.dex */
public final class C1748w1 extends AbstractC1691r4<C1748w1, C1724u1> implements InterfaceC1728u5 {
    private static final C1748w1 zzg;
    private int zza;
    private int zze = 1;
    private InterfaceC1763x4<C1640n1> zzf = C1512c6.f6916m;

    static {
        C1748w1 c1748w1 = new C1748w1();
        zzg = c1748w1;
        AbstractC1691r4.m4061q(C1748w1.class, c1748w1);
    }

    @Override // p152w2.AbstractC1691r4
    /* renamed from: r */
    public final Object mo3604r(int i6) {
        int i7 = i6 - 1;
        if (i7 == 0) {
            return (byte) 1;
        }
        if (i7 == 2) {
            return new C1525d6(zzg, "\u0001\u0002\u0000\u0001\u0001\u0002\u0002\u0000\u0001\u0000\u0001ဌ\u0000\u0002\u001b", new Object[]{"zza", "zze", C1736v1.f7201a, "zzf", C1640n1.class});
        }
        if (i7 == 3) {
            return new C1748w1();
        }
        if (i7 == 4) {
            return new C1724u1((C0385m) null);
        }
        if (i7 != 5) {
            return null;
        }
        return zzg;
    }
}
