package p152w2;

import android.content.Context;
import android.util.Log;
import javax.annotation.Nullable;
import p023d1.C0722t;
import p153w3.C1798e;

/* renamed from: w2.q2 */
/* loaded from: classes.dex */
public final class C1677q2 {

    /* renamed from: c */
    public static C1677q2 f7141c;

    /* renamed from: a */
    @Nullable
    public final Context f7142a;

    /* renamed from: b */
    @Nullable
    public final C1665p2 f7143b;

    public C1677q2() {
        this.f7142a = null;
        this.f7143b = null;
    }

    public C1677q2(Context context) {
        this.f7142a = context;
        C1665p2 c1665p2 = new C1665p2();
        this.f7143b = c1665p2;
        context.getContentResolver().registerContentObserver(C1593j2.f7021a, true, c1665p2);
    }

    /* renamed from: a */
    public static C1677q2 m4025a(Context context) {
        C1677q2 c1677q2;
        synchronized (C1677q2.class) {
            if (f7141c == null) {
                f7141c = C1798e.m4562s(context, "com.google.android.providers.gsf.permission.READ_GSERVICES") == 0 ? new C1677q2(context) : new C1677q2();
            }
            c1677q2 = f7141c;
        }
        return c1677q2;
    }

    /* renamed from: b */
    public final String m4026b(String str) {
        if (this.f7142a == null) {
            return null;
        }
        try {
            return (String) C1798e.m4528U(new C0722t((Object) this, (Object) str));
        } catch (IllegalStateException | SecurityException e6) {
            String valueOf = String.valueOf(str);
            Log.e("GservicesLoader", valueOf.length() != 0 ? "Unable to read GServices for: ".concat(valueOf) : new String("Unable to read GServices for: "), e6);
            return null;
        }
    }
}
