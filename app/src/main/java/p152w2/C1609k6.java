package p152w2;

import java.util.Map;

/* renamed from: w2.k6 */
/* loaded from: classes.dex */
public final class C1609k6 implements Map.Entry, Comparable<C1609k6> {

    /* renamed from: j */
    public final Comparable f7058j;

    /* renamed from: k */
    public Object f7059k;

    /* renamed from: l */
    public final /* synthetic */ C1645n6 f7060l;

    public C1609k6(C1645n6 c1645n6, Comparable comparable, Object obj) {
        this.f7060l = c1645n6;
        this.f7058j = comparable;
        this.f7059k = obj;
    }

    @Override // java.lang.Comparable
    public final /* bridge */ /* synthetic */ int compareTo(C1609k6 c1609k6) {
        return this.f7058j.compareTo(c1609k6.f7058j);
    }

    @Override // java.util.Map.Entry
    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (!(obj instanceof Map.Entry)) {
            return false;
        }
        Map.Entry entry = (Map.Entry) obj;
        Comparable comparable = this.f7058j;
        Object key = entry.getKey();
        if (comparable == null ? key == null : comparable.equals(key)) {
            Object obj2 = this.f7059k;
            Object value = entry.getValue();
            if (obj2 == null ? value == null : obj2.equals(value)) {
                return true;
            }
        }
        return false;
    }

    @Override // java.util.Map.Entry
    public final /* bridge */ /* synthetic */ Object getKey() {
        return this.f7058j;
    }

    @Override // java.util.Map.Entry
    public final Object getValue() {
        return this.f7059k;
    }

    @Override // java.util.Map.Entry
    public final int hashCode() {
        Comparable comparable = this.f7058j;
        int hashCode = comparable == null ? 0 : comparable.hashCode();
        Object obj = this.f7059k;
        return hashCode ^ (obj != null ? obj.hashCode() : 0);
    }

    @Override // java.util.Map.Entry
    public final Object setValue(Object obj) {
        C1645n6 c1645n6 = this.f7060l;
        int i6 = C1645n6.f7101p;
        c1645n6.m3958g();
        Object obj2 = this.f7059k;
        this.f7059k = obj;
        return obj2;
    }

    public final String toString() {
        String valueOf = String.valueOf(this.f7058j);
        String valueOf2 = String.valueOf(this.f7059k);
        StringBuilder sb = new StringBuilder(valueOf.length() + 1 + valueOf2.length());
        sb.append(valueOf);
        sb.append("=");
        sb.append(valueOf2);
        return sb.toString();
    }
}
