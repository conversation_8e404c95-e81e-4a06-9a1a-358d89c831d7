package p152w2;

import java.util.Objects;

/* JADX WARN: Enum visitor error
jadx.core.utils.exceptions.JadxRuntimeException: Init of enum field 'EF6' uses external variables
	at jadx.core.dex.visitors.EnumVisitor.createEnumFieldByConstructor(EnumVisitor.java:451)
	at jadx.core.dex.visitors.EnumVisitor.processEnumFieldByRegister(EnumVisitor.java:395)
	at jadx.core.dex.visitors.EnumVisitor.extractEnumFieldsFromFilledArray(EnumVisitor.java:324)
	at jadx.core.dex.visitors.EnumVisitor.extractEnumFieldsFromInsn(EnumVisitor.java:262)
	at jadx.core.dex.visitors.EnumVisitor.convertToEnum(EnumVisitor.java:151)
	at jadx.core.dex.visitors.EnumVisitor.visit(EnumVisitor.java:100)
 */
/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* renamed from: w2.j4 */
/* loaded from: classes.dex */
public final class EnumC1595j4 {

    /* renamed from: k */
    public static final EnumC1595j4 f7032k;

    /* renamed from: l */
    public static final EnumC1595j4 f7033l;

    /* renamed from: m */
    public static final EnumC1595j4[] f7034m;

    /* renamed from: n */
    public static final /* synthetic */ EnumC1595j4[] f7035n;

    /* renamed from: j */
    public final int f7036j;

    /* JADX INFO: Fake field, exist only in values array */
    EnumC1595j4 EF6;

    static {
        EnumC1498b5 enumC1498b5 = EnumC1498b5.DOUBLE;
        EnumC1595j4 enumC1595j4 = new EnumC1595j4("DOUBLE", 0, 0, 1, enumC1498b5);
        EnumC1498b5 enumC1498b52 = EnumC1498b5.FLOAT;
        EnumC1595j4 enumC1595j42 = new EnumC1595j4("FLOAT", 1, 1, 1, enumC1498b52);
        EnumC1498b5 enumC1498b53 = EnumC1498b5.LONG;
        EnumC1595j4 enumC1595j43 = new EnumC1595j4("INT64", 2, 2, 1, enumC1498b53);
        EnumC1595j4 enumC1595j44 = new EnumC1595j4("UINT64", 3, 3, 1, enumC1498b53);
        EnumC1498b5 enumC1498b54 = EnumC1498b5.INT;
        EnumC1595j4 enumC1595j45 = new EnumC1595j4("INT32", 4, 4, 1, enumC1498b54);
        EnumC1595j4 enumC1595j46 = new EnumC1595j4("FIXED64", 5, 5, 1, enumC1498b53);
        EnumC1595j4 enumC1595j47 = new EnumC1595j4("FIXED32", 6, 6, 1, enumC1498b54);
        EnumC1498b5 enumC1498b55 = EnumC1498b5.BOOLEAN;
        EnumC1595j4 enumC1595j48 = new EnumC1595j4("BOOL", 7, 7, 1, enumC1498b55);
        EnumC1498b5 enumC1498b56 = EnumC1498b5.STRING;
        EnumC1595j4 enumC1595j49 = new EnumC1595j4("STRING", 8, 8, 1, enumC1498b56);
        EnumC1498b5 enumC1498b57 = EnumC1498b5.MESSAGE;
        EnumC1595j4 enumC1595j410 = new EnumC1595j4("MESSAGE", 9, 9, 1, enumC1498b57);
        EnumC1498b5 enumC1498b58 = EnumC1498b5.BYTE_STRING;
        EnumC1595j4 enumC1595j411 = new EnumC1595j4("BYTES", 10, 10, 1, enumC1498b58);
        EnumC1595j4 enumC1595j412 = new EnumC1595j4("UINT32", 11, 11, 1, enumC1498b54);
        EnumC1498b5 enumC1498b59 = EnumC1498b5.ENUM;
        EnumC1595j4 enumC1595j413 = new EnumC1595j4("ENUM", 12, 12, 1, enumC1498b59);
        EnumC1595j4 enumC1595j414 = new EnumC1595j4("SFIXED32", 13, 13, 1, enumC1498b54);
        EnumC1595j4 enumC1595j415 = new EnumC1595j4("SFIXED64", 14, 14, 1, enumC1498b53);
        EnumC1595j4 enumC1595j416 = new EnumC1595j4("SINT32", 15, 15, 1, enumC1498b54);
        EnumC1595j4 enumC1595j417 = new EnumC1595j4("SINT64", 16, 16, 1, enumC1498b53);
        EnumC1595j4 enumC1595j418 = new EnumC1595j4("GROUP", 17, 17, 1, enumC1498b57);
        EnumC1595j4 enumC1595j419 = new EnumC1595j4("DOUBLE_LIST", 18, 18, 2, enumC1498b5);
        EnumC1595j4 enumC1595j420 = new EnumC1595j4("FLOAT_LIST", 19, 19, 2, enumC1498b52);
        EnumC1595j4 enumC1595j421 = new EnumC1595j4("INT64_LIST", 20, 20, 2, enumC1498b53);
        EnumC1595j4 enumC1595j422 = new EnumC1595j4("UINT64_LIST", 21, 21, 2, enumC1498b53);
        EnumC1595j4 enumC1595j423 = new EnumC1595j4("INT32_LIST", 22, 22, 2, enumC1498b54);
        EnumC1595j4 enumC1595j424 = new EnumC1595j4("FIXED64_LIST", 23, 23, 2, enumC1498b53);
        EnumC1595j4 enumC1595j425 = new EnumC1595j4("FIXED32_LIST", 24, 24, 2, enumC1498b54);
        EnumC1595j4 enumC1595j426 = new EnumC1595j4("BOOL_LIST", 25, 25, 2, enumC1498b55);
        EnumC1595j4 enumC1595j427 = new EnumC1595j4("STRING_LIST", 26, 26, 2, enumC1498b56);
        EnumC1595j4 enumC1595j428 = new EnumC1595j4("MESSAGE_LIST", 27, 27, 2, enumC1498b57);
        EnumC1595j4 enumC1595j429 = new EnumC1595j4("BYTES_LIST", 28, 28, 2, enumC1498b58);
        EnumC1595j4 enumC1595j430 = new EnumC1595j4("UINT32_LIST", 29, 29, 2, enumC1498b54);
        EnumC1595j4 enumC1595j431 = new EnumC1595j4("ENUM_LIST", 30, 30, 2, enumC1498b59);
        EnumC1595j4 enumC1595j432 = new EnumC1595j4("SFIXED32_LIST", 31, 31, 2, enumC1498b54);
        EnumC1595j4 enumC1595j433 = new EnumC1595j4("SFIXED64_LIST", 32, 32, 2, enumC1498b53);
        EnumC1595j4 enumC1595j434 = new EnumC1595j4("SINT32_LIST", 33, 33, 2, enumC1498b54);
        EnumC1595j4 enumC1595j435 = new EnumC1595j4("SINT64_LIST", 34, 34, 2, enumC1498b53);
        EnumC1595j4 enumC1595j436 = new EnumC1595j4("DOUBLE_LIST_PACKED", 35, 35, 3, enumC1498b5);
        f7032k = enumC1595j436;
        EnumC1595j4 enumC1595j437 = new EnumC1595j4("FLOAT_LIST_PACKED", 36, 36, 3, enumC1498b52);
        EnumC1595j4 enumC1595j438 = new EnumC1595j4("INT64_LIST_PACKED", 37, 37, 3, enumC1498b53);
        EnumC1595j4 enumC1595j439 = new EnumC1595j4("UINT64_LIST_PACKED", 38, 38, 3, enumC1498b53);
        EnumC1595j4 enumC1595j440 = new EnumC1595j4("INT32_LIST_PACKED", 39, 39, 3, enumC1498b54);
        EnumC1595j4 enumC1595j441 = new EnumC1595j4("FIXED64_LIST_PACKED", 40, 40, 3, enumC1498b53);
        EnumC1595j4 enumC1595j442 = new EnumC1595j4("FIXED32_LIST_PACKED", 41, 41, 3, enumC1498b54);
        EnumC1595j4 enumC1595j443 = new EnumC1595j4("BOOL_LIST_PACKED", 42, 42, 3, enumC1498b55);
        EnumC1595j4 enumC1595j444 = new EnumC1595j4("UINT32_LIST_PACKED", 43, 43, 3, enumC1498b54);
        EnumC1595j4 enumC1595j445 = new EnumC1595j4("ENUM_LIST_PACKED", 44, 44, 3, enumC1498b59);
        EnumC1595j4 enumC1595j446 = new EnumC1595j4("SFIXED32_LIST_PACKED", 45, 45, 3, enumC1498b54);
        EnumC1595j4 enumC1595j447 = new EnumC1595j4("SFIXED64_LIST_PACKED", 46, 46, 3, enumC1498b53);
        EnumC1595j4 enumC1595j448 = new EnumC1595j4("SINT32_LIST_PACKED", 47, 47, 3, enumC1498b54);
        EnumC1595j4 enumC1595j449 = new EnumC1595j4("SINT64_LIST_PACKED", 48, 48, 3, enumC1498b53);
        f7033l = enumC1595j449;
        f7035n = new EnumC1595j4[]{enumC1595j4, enumC1595j42, enumC1595j43, enumC1595j44, enumC1595j45, enumC1595j46, enumC1595j47, enumC1595j48, enumC1595j49, enumC1595j410, enumC1595j411, enumC1595j412, enumC1595j413, enumC1595j414, enumC1595j415, enumC1595j416, enumC1595j417, enumC1595j418, enumC1595j419, enumC1595j420, enumC1595j421, enumC1595j422, enumC1595j423, enumC1595j424, enumC1595j425, enumC1595j426, enumC1595j427, enumC1595j428, enumC1595j429, enumC1595j430, enumC1595j431, enumC1595j432, enumC1595j433, enumC1595j434, enumC1595j435, enumC1595j436, enumC1595j437, enumC1595j438, enumC1595j439, enumC1595j440, enumC1595j441, enumC1595j442, enumC1595j443, enumC1595j444, enumC1595j445, enumC1595j446, enumC1595j447, enumC1595j448, enumC1595j449, new EnumC1595j4("GROUP_LIST", 49, 49, 2, enumC1498b57), new EnumC1595j4("MAP", 50, 50, 4, EnumC1498b5.VOID)};
        EnumC1595j4[] values = values();
        f7034m = new EnumC1595j4[values.length];
        for (EnumC1595j4 enumC1595j450 : values) {
            f7034m[enumC1595j450.f7036j] = enumC1595j450;
        }
    }

    public EnumC1595j4(String str, int i6, int i7, int i8, EnumC1498b5 enumC1498b5) {
        this.f7036j = i7;
        EnumC1498b5 enumC1498b52 = EnumC1498b5.VOID;
        int i9 = i8 - 1;
        if (i9 == 1 || i9 == 3) {
            Objects.requireNonNull(enumC1498b5);
        }
        if (i8 == 1) {
            enumC1498b5.ordinal();
        }
    }

    public static EnumC1595j4[] values() {
        return (EnumC1595j4[]) f7035n.clone();
    }
}
