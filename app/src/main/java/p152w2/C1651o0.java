package p152w2;

/* renamed from: w2.o0 */
/* loaded from: classes.dex */
public final class C1651o0 extends C1643n4<C1663p0, C1651o0> {
    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public C1651o0() {
        /*
            r1 = this;
            w2.p0 r0 = p152w2.C1663p0.m3973A()
            r1.<init>(r0)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1651o0.<init>():void");
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public /* synthetic */ C1651o0(p008b0.C0385m r1) {
        /*
            r0 = this;
            w2.p0 r1 = p152w2.C1663p0.m3973A()
            r0.<init>(r1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1651o0.<init>(b0.m):void");
    }
}
