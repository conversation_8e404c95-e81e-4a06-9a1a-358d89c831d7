package p152w2;

import java.nio.charset.Charset;

/* renamed from: w2.m5 */
/* loaded from: classes.dex */
public final class C1632m5 {

    /* renamed from: b */
    public static final C1596j5 f7088b = new C1596j5();

    /* renamed from: a */
    public final C1608k5 f7089a;

    public C1632m5() {
        InterfaceC1704s5 interfaceC1704s5;
        InterfaceC1704s5[] interfaceC1704s5Arr = new InterfaceC1704s5[2];
        interfaceC1704s5Arr[0] = C1631m4.f7087a;
        try {
            interfaceC1704s5 = (InterfaceC1704s5) Class.forName("com.google.protobuf.DescriptorMessageInfoFactory").getDeclaredMethod("getInstance", new Class[0]).invoke(null, new Object[0]);
        } catch (Exception unused) {
            interfaceC1704s5 = f7088b;
        }
        interfaceC1704s5Arr[1] = interfaceC1704s5;
        C1608k5 c1608k5 = new C1608k5(interfaceC1704s5Arr);
        Charset charset = C1775y4.f7289a;
        this.f7089a = c1608k5;
    }
}
