package p152w2;

import java.io.IOException;
import p075k4.C1042m;
import p152w2.AbstractC1691r4;
import p152w2.C1643n4;

/* renamed from: w2.n4 */
/* loaded from: classes.dex */
public class C1643n4<MessageType extends AbstractC1691r4<MessageType, BuilderType>, BuilderType extends C1643n4<MessageType, BuilderType>> extends AbstractC1630m3<MessageType, BuilderType> {

    /* renamed from: j */
    public final MessageType f7098j;

    /* renamed from: k */
    public MessageType f7099k;

    /* renamed from: l */
    public boolean f7100l = false;

    public C1643n4(MessageType messagetype) {
        this.f7098j = messagetype;
        this.f7099k = (MessageType) messagetype.mo3604r(4);
    }

    @Override // p152w2.InterfaceC1728u5
    /* renamed from: d */
    public final /* bridge */ /* synthetic */ InterfaceC1716t5 mo3946d() {
        return this.f7098j;
    }

    /* renamed from: f */
    public final MessageType m3947f() {
        MessageType m3952k = m3952k();
        boolean z5 = true;
        byte byteValue = ((Byte) m3952k.mo3604r(1)).byteValue();
        if (byteValue != 1) {
            if (byteValue == 0) {
                z5 = false;
            } else {
                z5 = C1499b6.f6896c.m3663a(m3952k.getClass()).mo3749b(m3952k);
                m3952k.mo3604r(2);
            }
        }
        if (z5) {
            return m3952k;
        }
        throw new C1042m();
    }

    /* renamed from: g */
    public final BuilderType m3948g(MessageType messagetype) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        MessageType messagetype2 = this.f7099k;
        C1499b6.f6896c.m3663a(messagetype2.getClass()).mo3755h(messagetype2, messagetype);
        return this;
    }

    /* renamed from: h */
    public final C1643n4 m3949h(byte[] bArr, int i6, C1523d4 c1523d4) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        try {
            C1499b6.f6896c.m3663a(this.f7099k.getClass()).mo3756i(this.f7099k, bArr, 0, i6, new C1678q3(c1523d4));
            return this;
        } catch (IndexOutOfBoundsException unused) {
            throw C1485a5.m3641a();
        } catch (C1485a5 e6) {
            throw e6;
        } catch (IOException e7) {
            throw new RuntimeException("Reading from byte array should not throw IOException.", e7);
        }
    }

    /* renamed from: i */
    public final void m3950i() {
        MessageType messagetype = (MessageType) this.f7099k.mo3604r(4);
        C1499b6.f6896c.m3663a(messagetype.getClass()).mo3755h(messagetype, this.f7099k);
        this.f7099k = messagetype;
    }

    /* renamed from: j */
    public final BuilderType clone() {
        BuilderType buildertype = (BuilderType) this.f7098j.mo3604r(5);
        buildertype.m3948g(m3952k());
        return buildertype;
    }

    /* renamed from: k */
    public final MessageType m3952k() {
        if (this.f7100l) {
            return this.f7099k;
        }
        MessageType messagetype = this.f7099k;
        C1499b6.f6896c.m3663a(messagetype.getClass()).mo3753f(messagetype);
        this.f7100l = true;
        return this.f7099k;
    }
}
