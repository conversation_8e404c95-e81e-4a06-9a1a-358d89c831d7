package p152w2;

import java.util.List;

/* renamed from: w2.y1 */
/* loaded from: classes.dex */
public final class C1772y1 extends AbstractC1691r4<C1772y1, C1760x1> implements InterfaceC1728u5 {
    private static final C1772y1 zzh;
    private InterfaceC1751w4 zza;
    private InterfaceC1751w4 zze;
    private InterfaceC1763x4<C1604k1> zzf;
    private InterfaceC1763x4<C1482a2> zzg;

    static {
        C1772y1 c1772y1 = new C1772y1();
        zzh = c1772y1;
        AbstractC1691r4.m4061q(C1772y1.class, c1772y1);
    }

    public C1772y1() {
        C1584i5 c1584i5 = C1584i5.f7009m;
        this.zza = c1584i5;
        this.zze = c1584i5;
        C1512c6<Object> c1512c6 = C1512c6.f6916m;
        this.zzf = c1512c6;
        this.zzg = c1512c6;
    }

    /* renamed from: C */
    public static C1760x1 m4438C() {
        return zzh.m4065m();
    }

    /* renamed from: D */
    public static C1772y1 m4439D() {
        return zzh;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* renamed from: F */
    public static void m4441F(C1772y1 c1772y1, Iterable iterable) {
        InterfaceC1751w4 interfaceC1751w4 = c1772y1.zza;
        if (!((AbstractC1654o3) interfaceC1751w4).f7118j) {
            c1772y1.zza = AbstractC1691r4.m4058k(interfaceC1751w4);
        }
        AbstractC1642n3.m3941i(iterable, c1772y1.zza);
    }

    /* renamed from: G */
    public static void m4442G(C1772y1 c1772y1) {
        c1772y1.zza = C1584i5.f7009m;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* renamed from: H */
    public static void m4443H(C1772y1 c1772y1, Iterable iterable) {
        InterfaceC1751w4 interfaceC1751w4 = c1772y1.zze;
        if (!((AbstractC1654o3) interfaceC1751w4).f7118j) {
            c1772y1.zze = AbstractC1691r4.m4058k(interfaceC1751w4);
        }
        AbstractC1642n3.m3941i(iterable, c1772y1.zze);
    }

    /* renamed from: I */
    public static void m4444I(C1772y1 c1772y1) {
        c1772y1.zze = C1584i5.f7009m;
    }

    /* renamed from: J */
    public static void m4445J(C1772y1 c1772y1, Iterable iterable) {
        InterfaceC1763x4<C1604k1> interfaceC1763x4 = c1772y1.zzf;
        if (!interfaceC1763x4.mo3965a()) {
            c1772y1.zzf = AbstractC1691r4.m4059l(interfaceC1763x4);
        }
        AbstractC1642n3.m3941i(iterable, c1772y1.zzf);
    }

    /* renamed from: K */
    public static void m4446K(C1772y1 c1772y1, int i6) {
        InterfaceC1763x4<C1604k1> interfaceC1763x4 = c1772y1.zzf;
        if (!interfaceC1763x4.mo3965a()) {
            c1772y1.zzf = AbstractC1691r4.m4059l(interfaceC1763x4);
        }
        c1772y1.zzf.remove(i6);
    }

    /* renamed from: L */
    public static void m4447L(C1772y1 c1772y1, Iterable iterable) {
        InterfaceC1763x4<C1482a2> interfaceC1763x4 = c1772y1.zzg;
        if (!interfaceC1763x4.mo3965a()) {
            c1772y1.zzg = AbstractC1691r4.m4059l(interfaceC1763x4);
        }
        AbstractC1642n3.m3941i(iterable, c1772y1.zzg);
    }

    /* renamed from: M */
    public static void m4448M(C1772y1 c1772y1, int i6) {
        InterfaceC1763x4<C1482a2> interfaceC1763x4 = c1772y1.zzg;
        if (!interfaceC1763x4.mo3965a()) {
            c1772y1.zzg = AbstractC1691r4.m4059l(interfaceC1763x4);
        }
        c1772y1.zzg.remove(i6);
    }

    /* renamed from: A */
    public final int m4449A() {
        return this.zzg.size();
    }

    /* renamed from: B */
    public final C1482a2 m4450B(int i6) {
        return this.zzg.get(i6);
    }

    @Override // p152w2.AbstractC1691r4
    /* renamed from: r */
    public final Object mo3604r(int i6) {
        int i7 = i6 - 1;
        if (i7 == 0) {
            return (byte) 1;
        }
        if (i7 == 2) {
            return new C1525d6(zzh, "\u0001\u0004\u0000\u0000\u0001\u0004\u0004\u0000\u0004\u0000\u0001\u0015\u0002\u0015\u0003\u001b\u0004\u001b", new Object[]{"zza", "zze", "zzf", C1604k1.class, "zzg", C1482a2.class});
        }
        if (i7 == 3) {
            return new C1772y1();
        }
        if (i7 == 4) {
            return new C1760x1(null);
        }
        if (i7 != 5) {
            return null;
        }
        return zzh;
    }

    /* renamed from: s */
    public final List<Long> m4451s() {
        return this.zza;
    }

    /* renamed from: t */
    public final int m4452t() {
        return ((C1584i5) this.zza).f7011l;
    }

    /* renamed from: u */
    public final List<Long> m4453u() {
        return this.zze;
    }

    /* renamed from: v */
    public final int m4454v() {
        return ((C1584i5) this.zze).f7011l;
    }

    /* renamed from: w */
    public final List<C1604k1> m4455w() {
        return this.zzf;
    }

    /* renamed from: x */
    public final int m4456x() {
        return this.zzf.size();
    }

    /* renamed from: y */
    public final C1604k1 m4457y(int i6) {
        return this.zzf.get(i6);
    }

    /* renamed from: z */
    public final List<C1482a2> m4458z() {
        return this.zzg;
    }
}
