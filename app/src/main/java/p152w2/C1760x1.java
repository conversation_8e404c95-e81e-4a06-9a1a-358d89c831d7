package p152w2;

/* renamed from: w2.x1 */
/* loaded from: classes.dex */
public final class C1760x1 extends C1643n4<C1772y1, C1760x1> {
    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public C1760x1() {
        /*
            r1 = this;
            w2.y1 r0 = p152w2.C1772y1.m4440E()
            r1.<init>(r0)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1760x1.<init>():void");
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public /* synthetic */ C1760x1(p008b0.C0385m r1) {
        /*
            r0 = this;
            w2.y1 r1 = p152w2.C1772y1.m4440E()
            r0.<init>(r1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1760x1.<init>(b0.m):void");
    }

    /* renamed from: l */
    public final C1760x1 m4399l(Iterable<? extends Long> iterable) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1772y1.m4441F((C1772y1) this.f7099k, iterable);
        return this;
    }

    /* renamed from: m */
    public final C1760x1 m4400m() {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1772y1.m4442G((C1772y1) this.f7099k);
        return this;
    }

    /* renamed from: n */
    public final C1760x1 m4401n(Iterable<? extends Long> iterable) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1772y1.m4443H((C1772y1) this.f7099k, iterable);
        return this;
    }

    /* renamed from: o */
    public final C1760x1 m4402o() {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1772y1.m4444I((C1772y1) this.f7099k);
        return this;
    }

    /* renamed from: p */
    public final C1760x1 m4403p(int i6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1772y1.m4446K((C1772y1) this.f7099k, i6);
        return this;
    }

    /* renamed from: q */
    public final C1760x1 m4404q(int i6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1772y1.m4448M((C1772y1) this.f7099k, i6);
        return this;
    }
}
