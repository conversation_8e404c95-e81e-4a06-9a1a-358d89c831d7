package p152w2;

import android.content.ContentResolver;
import android.database.sqlite.SQLiteException;
import android.net.Uri;
import android.os.StrictMode;
import android.util.Log;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.litepal.parser.LitePalParser;
import p021d.C0690o;
import p077l.AbstractC1052f;
import p077l.C1047a;
import p153w3.C1798e;

/* renamed from: w2.m2 */
/* loaded from: classes.dex */
public final class C1629m2 {

    /* renamed from: g */
    public static final Map<Uri, C1629m2> f7079g = new C1047a();

    /* renamed from: h */
    public static final String[] f7080h = {"key", LitePalParser.ATTR_VALUE};

    /* renamed from: a */
    public final ContentResolver f7081a;

    /* renamed from: b */
    public final Uri f7082b;

    /* renamed from: c */
    public final C1617l2 f7083c;

    /* renamed from: d */
    public final Object f7084d;

    /* renamed from: e */
    public volatile Map<String, String> f7085e;

    /* renamed from: f */
    public final List<InterfaceC1641n2> f7086f;

    public C1629m2(ContentResolver contentResolver, Uri uri) {
        C1617l2 c1617l2 = new C1617l2(this);
        this.f7083c = c1617l2;
        this.f7084d = new Object();
        this.f7086f = new ArrayList();
        Objects.requireNonNull(contentResolver);
        Objects.requireNonNull(uri);
        this.f7081a = contentResolver;
        this.f7082b = uri;
        contentResolver.registerContentObserver(uri, false, c1617l2);
    }

    /* JADX WARN: Type inference failed for: r1v0, types: [java.util.Map<android.net.Uri, w2.m2>, l.g] */
    /* renamed from: a */
    public static C1629m2 m3932a(ContentResolver contentResolver, Uri uri) {
        C1629m2 c1629m2;
        synchronized (C1629m2.class) {
            ?? r12 = f7079g;
            c1629m2 = (C1629m2) r12.getOrDefault(uri, null);
            if (c1629m2 == null) {
                try {
                    C1629m2 c1629m22 = new C1629m2(contentResolver, uri);
                    try {
                        r12.put(uri, c1629m22);
                    } catch (SecurityException unused) {
                    }
                    c1629m2 = c1629m22;
                } catch (SecurityException unused2) {
                }
            }
        }
        return c1629m2;
    }

    /* JADX WARN: Type inference failed for: r1v5, types: [java.util.Map<android.net.Uri, w2.m2>, l.g] */
    /* renamed from: c */
    public static synchronized void m3933c() {
        synchronized (C1629m2.class) {
            Iterator it = ((AbstractC1052f.e) f7079g.values()).iterator();
            while (it.hasNext()) {
                C1629m2 c1629m2 = (C1629m2) it.next();
                c1629m2.f7081a.unregisterContentObserver(c1629m2.f7083c);
            }
            f7079g.clear();
        }
    }

    /* renamed from: b */
    public final Map<String, String> m3934b() {
        Map<String, String> map;
        Map<String, String> map2 = this.f7085e;
        if (map2 == null) {
            synchronized (this.f7084d) {
                map2 = this.f7085e;
                if (map2 == null) {
                    StrictMode.ThreadPolicy allowThreadDiskReads = StrictMode.allowThreadDiskReads();
                    try {
                        try {
                            map = (Map) C1798e.m4528U(new C0690o(this, 7));
                        } finally {
                            StrictMode.setThreadPolicy(allowThreadDiskReads);
                        }
                    } catch (SQLiteException | IllegalStateException | SecurityException unused) {
                        Log.e("ConfigurationContentLoader", "PhenotypeFlag unable to load ContentProvider, using default values");
                        StrictMode.setThreadPolicy(allowThreadDiskReads);
                        map = null;
                    }
                    this.f7085e = map;
                    map2 = map;
                }
            }
        }
        return map2 != null ? map2 : Collections.emptyMap();
    }
}
