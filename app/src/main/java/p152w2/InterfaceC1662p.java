package p152w2;

import java.util.Iterator;
import java.util.List;
import p090n.C1090c;

/* renamed from: w2.p */
/* loaded from: classes.dex */
public interface InterfaceC1662p {

    /* renamed from: b */
    public static final C1710t f7126b = new C1710t();

    /* renamed from: c */
    public static final C1638n f7127c = new C1638n();

    /* renamed from: d */
    public static final C1566h f7128d = new C1566h("continue");

    /* renamed from: e */
    public static final C1566h f7129e = new C1566h("break");

    /* renamed from: f */
    public static final C1566h f7130f = new C1566h("return");

    /* renamed from: g */
    public static final C1554g f7131g = new C1554g(Boolean.TRUE);

    /* renamed from: h */
    public static final C1554g f7132h = new C1554g(Boolean.FALSE);

    /* renamed from: i */
    public static final C1698s f7133i = new C1698s("");

    /* renamed from: c */
    String mo3761c();

    /* renamed from: d */
    Double mo3762d();

    /* renamed from: g */
    Boolean mo3763g();

    /* renamed from: l */
    Iterator<InterfaceC1662p> mo3766l();

    /* renamed from: r */
    InterfaceC1662p mo3771r();

    /* renamed from: s */
    InterfaceC1662p mo3772s(String str, C1090c c1090c, List<InterfaceC1662p> list);
}
