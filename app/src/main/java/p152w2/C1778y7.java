package p152w2;

/* renamed from: w2.y7 */
/* loaded from: classes.dex */
public final class C1778y7 implements InterfaceC1766x7 {

    /* renamed from: a */
    public static final AbstractC1773y2<Long> f7292a;

    static {
        C1749w2 c1749w2 = new C1749w2(C1713t2.m4275a());
        c1749w2.m4355b("measurement.client.consent_state_v1", true);
        c1749w2.m4355b("measurement.client.3p_consent_state_v1", true);
        c1749w2.m4355b("measurement.service.consent_state_v1_W36", true);
        c1749w2.m4354a("measurement.id.service.consent_state_v1_W36", 0L);
        f7292a = (C1725u2) c1749w2.m4354a("measurement.service.storage_consent_support_version", 203590L);
    }

    @Override // p152w2.InterfaceC1766x7
    /* renamed from: a */
    public final long mo4421a() {
        return f7292a.m4460c().longValue();
    }
}
