package p152w2;

/* renamed from: w2.v7 */
/* loaded from: classes.dex */
public final class C1742v7 implements InterfaceC1730u7 {

    /* renamed from: A */
    public static final AbstractC1773y2<Long> f7204A;

    /* renamed from: B */
    public static final AbstractC1773y2<Long> f7205B;

    /* renamed from: C */
    public static final AbstractC1773y2<Long> f7206C;

    /* renamed from: D */
    public static final AbstractC1773y2<Long> f7207D;

    /* renamed from: E */
    public static final AbstractC1773y2<Long> f7208E;

    /* renamed from: F */
    public static final AbstractC1773y2<Long> f7209F;

    /* renamed from: G */
    public static final AbstractC1773y2<Long> f7210G;

    /* renamed from: H */
    public static final AbstractC1773y2<String> f7211H;

    /* renamed from: I */
    public static final AbstractC1773y2<Long> f7212I;

    /* renamed from: a */
    public static final AbstractC1773y2<Long> f7213a;

    /* renamed from: b */
    public static final AbstractC1773y2<Long> f7214b;

    /* renamed from: c */
    public static final AbstractC1773y2<Long> f7215c;

    /* renamed from: d */
    public static final AbstractC1773y2<String> f7216d;

    /* renamed from: e */
    public static final AbstractC1773y2<String> f7217e;

    /* renamed from: f */
    public static final AbstractC1773y2<Long> f7218f;

    /* renamed from: g */
    public static final AbstractC1773y2<Long> f7219g;

    /* renamed from: h */
    public static final AbstractC1773y2<Long> f7220h;

    /* renamed from: i */
    public static final AbstractC1773y2<Long> f7221i;

    /* renamed from: j */
    public static final AbstractC1773y2<Long> f7222j;

    /* renamed from: k */
    public static final AbstractC1773y2<Long> f7223k;

    /* renamed from: l */
    public static final AbstractC1773y2<Long> f7224l;

    /* renamed from: m */
    public static final AbstractC1773y2<Long> f7225m;

    /* renamed from: n */
    public static final AbstractC1773y2<Long> f7226n;

    /* renamed from: o */
    public static final AbstractC1773y2<Long> f7227o;

    /* renamed from: p */
    public static final AbstractC1773y2<Long> f7228p;

    /* renamed from: q */
    public static final AbstractC1773y2<Long> f7229q;

    /* renamed from: r */
    public static final AbstractC1773y2<Long> f7230r;

    /* renamed from: s */
    public static final AbstractC1773y2<Long> f7231s;

    /* renamed from: t */
    public static final AbstractC1773y2<Long> f7232t;

    /* renamed from: u */
    public static final AbstractC1773y2<Long> f7233u;

    /* renamed from: v */
    public static final AbstractC1773y2<Long> f7234v;

    /* renamed from: w */
    public static final AbstractC1773y2<Long> f7235w;

    /* renamed from: x */
    public static final AbstractC1773y2<Long> f7236x;

    /* renamed from: y */
    public static final AbstractC1773y2<Long> f7237y;

    /* renamed from: z */
    public static final AbstractC1773y2<Long> f7238z;

    static {
        C1749w2 c1749w2 = new C1749w2(C1713t2.m4275a());
        f7213a = (C1725u2) c1749w2.m4354a("measurement.ad_id_cache_time", 10000L);
        f7214b = (C1725u2) c1749w2.m4354a("measurement.max_bundles_per_iteration", 100L);
        f7215c = (C1725u2) c1749w2.m4354a("measurement.config.cache_time", 86400000L);
        c1749w2.m4356c("measurement.log_tag", "FA");
        f7216d = (C1737v2) c1749w2.m4356c("measurement.config.url_authority", "app-measurement.com");
        f7217e = (C1737v2) c1749w2.m4356c("measurement.config.url_scheme", "https");
        f7218f = (C1725u2) c1749w2.m4354a("measurement.upload.debug_upload_interval", 1000L);
        f7219g = (C1725u2) c1749w2.m4354a("measurement.lifetimevalue.max_currency_tracked", 4L);
        f7220h = (C1725u2) c1749w2.m4354a("measurement.store.max_stored_events_per_app", 100000L);
        f7221i = (C1725u2) c1749w2.m4354a("measurement.experiment.max_ids", 50L);
        f7222j = (C1725u2) c1749w2.m4354a("measurement.audience.filter_result_max_count", 200L);
        f7223k = (C1725u2) c1749w2.m4354a("measurement.alarm_manager.minimum_interval", 60000L);
        f7224l = (C1725u2) c1749w2.m4354a("measurement.upload.minimum_delay", 500L);
        f7225m = (C1725u2) c1749w2.m4354a("measurement.monitoring.sample_period_millis", 86400000L);
        f7226n = (C1725u2) c1749w2.m4354a("measurement.upload.realtime_upload_interval", 10000L);
        f7227o = (C1725u2) c1749w2.m4354a("measurement.upload.refresh_blacklisted_config_interval", 604800000L);
        c1749w2.m4354a("measurement.config.cache_time.service", 3600000L);
        f7228p = (C1725u2) c1749w2.m4354a("measurement.service_client.idle_disconnect_millis", 5000L);
        c1749w2.m4356c("measurement.log_tag.service", "FA-SVC");
        f7229q = (C1725u2) c1749w2.m4354a("measurement.upload.stale_data_deletion_interval", 86400000L);
        f7230r = (C1725u2) c1749w2.m4354a("measurement.sdk.attribution.cache.ttl", 604800000L);
        f7231s = (C1725u2) c1749w2.m4354a("measurement.upload.backoff_period", 43200000L);
        f7232t = (C1725u2) c1749w2.m4354a("measurement.upload.initial_upload_delay_time", 15000L);
        f7233u = (C1725u2) c1749w2.m4354a("measurement.upload.interval", 3600000L);
        f7234v = (C1725u2) c1749w2.m4354a("measurement.upload.max_bundle_size", 65536L);
        f7235w = (C1725u2) c1749w2.m4354a("measurement.upload.max_bundles", 100L);
        f7236x = (C1725u2) c1749w2.m4354a("measurement.upload.max_conversions_per_day", 500L);
        f7237y = (C1725u2) c1749w2.m4354a("measurement.upload.max_error_events_per_day", 1000L);
        f7238z = (C1725u2) c1749w2.m4354a("measurement.upload.max_events_per_bundle", 1000L);
        f7204A = (C1725u2) c1749w2.m4354a("measurement.upload.max_events_per_day", 100000L);
        f7205B = (C1725u2) c1749w2.m4354a("measurement.upload.max_public_events_per_day", 50000L);
        f7206C = (C1725u2) c1749w2.m4354a("measurement.upload.max_queue_time", 2419200000L);
        f7207D = (C1725u2) c1749w2.m4354a("measurement.upload.max_realtime_events_per_day", 10L);
        f7208E = (C1725u2) c1749w2.m4354a("measurement.upload.max_batch_size", 65536L);
        f7209F = (C1725u2) c1749w2.m4354a("measurement.upload.retry_count", 6L);
        f7210G = (C1725u2) c1749w2.m4354a("measurement.upload.retry_time", 1800000L);
        f7211H = (C1737v2) c1749w2.m4356c("measurement.upload.url", "https://app-measurement.com/a");
        f7212I = (C1725u2) c1749w2.m4354a("measurement.upload.window_interval", 3600000L);
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: A */
    public final long mo4289A() {
        return f7238z.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: B */
    public final long mo4290B() {
        return f7212I.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: C */
    public final long mo4291C() {
        return f7234v.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: D */
    public final long mo4292D() {
        return f7208E.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: E */
    public final long mo4293E() {
        return f7236x.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: F */
    public final long mo4294F() {
        return f7210G.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: G */
    public final long mo4295G() {
        return f7204A.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: H */
    public final long mo4296H() {
        return f7230r.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: I */
    public final long mo4297I() {
        return f7206C.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: a */
    public final long mo4298a() {
        return f7213a.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: b */
    public final long mo4299b() {
        return f7214b.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: c */
    public final long mo4300c() {
        return f7215c.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: d */
    public final String mo4301d() {
        return f7216d.m4460c();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: e */
    public final long mo4302e() {
        return f7220h.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: f */
    public final long mo4303f() {
        return f7221i.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: g */
    public final String mo4304g() {
        return f7217e.m4460c();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: h */
    public final long mo4305h() {
        return f7226n.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: i */
    public final long mo4306i() {
        return f7228p.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: j */
    public final long mo4307j() {
        return f7222j.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: k */
    public final long mo4308k() {
        return f7224l.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: l */
    public final long mo4309l() {
        return f7218f.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: m */
    public final long mo4310m() {
        return f7227o.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: n */
    public final long mo4311n() {
        return f7229q.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: o */
    public final long mo4312o() {
        return f7223k.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: p */
    public final long mo4313p() {
        return f7225m.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: q */
    public final long mo4314q() {
        return f7219g.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: r */
    public final long mo4315r() {
        return f7232t.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: s */
    public final long mo4316s() {
        return f7235w.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: t */
    public final long mo4317t() {
        return f7209F.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: u */
    public final long mo4318u() {
        return f7237y.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: v */
    public final String mo4319v() {
        return f7211H.m4460c();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: w */
    public final long mo4320w() {
        return f7205B.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: x */
    public final long mo4321x() {
        return f7231s.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: y */
    public final long mo4322y() {
        return f7233u.m4460c().longValue();
    }

    @Override // p152w2.InterfaceC1730u7
    /* renamed from: z */
    public final long mo4323z() {
        return f7207D.m4460c().longValue();
    }
}
