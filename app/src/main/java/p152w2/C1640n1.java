package p152w2;

import p008b0.C0385m;

/* renamed from: w2.n1 */
/* loaded from: classes.dex */
public final class C1640n1 extends AbstractC1691r4<C1640n1, C1723u0> implements InterfaceC1728u5 {
    private static final C1640n1 zzg;
    private int zza;
    private String zze = "";
    private long zzf;

    static {
        C1640n1 c1640n1 = new C1640n1();
        zzg = c1640n1;
        AbstractC1691r4.m4061q(C1640n1.class, c1640n1);
    }

    @Override // p152w2.AbstractC1691r4
    /* renamed from: r */
    public final Object mo3604r(int i6) {
        int i7 = i6 - 1;
        if (i7 == 0) {
            return (byte) 1;
        }
        if (i7 == 2) {
            return new C1525d6(zzg, "\u0001\u0002\u0000\u0001\u0001\u0002\u0002\u0000\u0000\u0000\u0001ဈ\u0000\u0002ဂ\u0001", new Object[]{"zza", "zze", "zzf"});
        }
        if (i7 == 3) {
            return new C1640n1();
        }
        if (i7 == 4) {
            return new C1723u0((C0385m) null);
        }
        if (i7 != 5) {
            return null;
        }
        return zzg;
    }
}
