package p152w2;

import java.util.List;
import java.util.Objects;

/* renamed from: w2.m1 */
/* loaded from: classes.dex */
public final class C1628m1 extends AbstractC1691r4<C1628m1, C1616l1> implements InterfaceC1728u5 {
    private static final C1628m1 zzj;
    private int zza;
    private InterfaceC1763x4<C1664p1> zze = C1512c6.f6916m;
    private String zzf = "";
    private long zzg;
    private long zzh;
    private int zzi;

    static {
        C1628m1 c1628m1 = new C1628m1();
        zzj = c1628m1;
        AbstractC1691r4.m4061q(C1628m1.class, c1628m1);
    }

    /* renamed from: C */
    public static C1616l1 m3911C() {
        return zzj.m4065m();
    }

    /* renamed from: E */
    public static /* synthetic */ void m3913E(C1628m1 c1628m1, int i6, C1664p1 c1664p1) {
        c1628m1.m3923M();
        c1628m1.zze.set(i6, c1664p1);
    }

    /* renamed from: F */
    public static /* synthetic */ void m3914F(C1628m1 c1628m1, C1664p1 c1664p1) {
        c1628m1.m3923M();
        c1628m1.zze.add(c1664p1);
    }

    /* renamed from: G */
    public static /* synthetic */ void m3915G(C1628m1 c1628m1, Iterable iterable) {
        c1628m1.m3923M();
        AbstractC1642n3.m3941i(iterable, c1628m1.zze);
    }

    /* renamed from: H */
    public static void m3916H(C1628m1 c1628m1) {
        c1628m1.zze = C1512c6.f6916m;
    }

    /* renamed from: I */
    public static /* synthetic */ void m3917I(C1628m1 c1628m1, int i6) {
        c1628m1.m3923M();
        c1628m1.zze.remove(i6);
    }

    /* renamed from: J */
    public static /* synthetic */ void m3918J(C1628m1 c1628m1, String str) {
        Objects.requireNonNull(str);
        c1628m1.zza |= 1;
        c1628m1.zzf = str;
    }

    /* renamed from: K */
    public static /* synthetic */ void m3919K(C1628m1 c1628m1, long j6) {
        c1628m1.zza |= 2;
        c1628m1.zzg = j6;
    }

    /* renamed from: L */
    public static /* synthetic */ void m3920L(C1628m1 c1628m1, long j6) {
        c1628m1.zza |= 4;
        c1628m1.zzh = j6;
    }

    /* renamed from: A */
    public final boolean m3921A() {
        return (this.zza & 8) != 0;
    }

    /* renamed from: B */
    public final int m3922B() {
        return this.zzi;
    }

    /* renamed from: M */
    public final void m3923M() {
        InterfaceC1763x4<C1664p1> interfaceC1763x4 = this.zze;
        if (interfaceC1763x4.mo3965a()) {
            return;
        }
        this.zze = AbstractC1691r4.m4059l(interfaceC1763x4);
    }

    @Override // p152w2.AbstractC1691r4
    /* renamed from: r */
    public final Object mo3604r(int i6) {
        int i7 = i6 - 1;
        if (i7 == 0) {
            return (byte) 1;
        }
        if (i7 == 2) {
            return new C1525d6(zzj, "\u0001\u0005\u0000\u0001\u0001\u0005\u0005\u0000\u0001\u0000\u0001\u001b\u0002ဈ\u0000\u0003ဂ\u0001\u0004ဂ\u0002\u0005င\u0003", new Object[]{"zza", "zze", C1664p1.class, "zzf", "zzg", "zzh", "zzi"});
        }
        if (i7 == 3) {
            return new C1628m1();
        }
        if (i7 == 4) {
            return new C1616l1(null);
        }
        if (i7 != 5) {
            return null;
        }
        return zzj;
    }

    /* renamed from: s */
    public final List<C1664p1> m3924s() {
        return this.zze;
    }

    /* renamed from: t */
    public final int m3925t() {
        return this.zze.size();
    }

    /* renamed from: u */
    public final C1664p1 m3926u(int i6) {
        return this.zze.get(i6);
    }

    /* renamed from: v */
    public final String m3927v() {
        return this.zzf;
    }

    /* renamed from: w */
    public final boolean m3928w() {
        return (this.zza & 2) != 0;
    }

    /* renamed from: x */
    public final long m3929x() {
        return this.zzg;
    }

    /* renamed from: y */
    public final boolean m3930y() {
        return (this.zza & 4) != 0;
    }

    /* renamed from: z */
    public final long m3931z() {
        return this.zzh;
    }
}
