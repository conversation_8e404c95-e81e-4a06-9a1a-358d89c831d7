package p152w2;

import java.nio.charset.Charset;
import java.util.AbstractList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Objects;
import java.util.RandomAccess;

/* renamed from: w2.r3 */
/* loaded from: classes.dex */
public final class C1690r3 extends AbstractC1654o3<Boolean> implements RandomAccess, InterfaceC1486a6 {

    /* renamed from: k */
    public boolean[] f7164k;

    /* renamed from: l */
    public int f7165l;

    static {
        new C1690r3(new boolean[0], 0).f7118j = false;
    }

    public C1690r3() {
        this(new boolean[10], 0);
    }

    public C1690r3(boolean[] zArr, int i6) {
        this.f7164k = zArr;
        this.f7165l = i6;
    }

    @Override // java.util.AbstractList, java.util.List
    public final /* bridge */ /* synthetic */ void add(int i6, Object obj) {
        int i7;
        boolean booleanValue = ((Bo<PERSON>an) obj).booleanValue();
        m3967c();
        if (i6 < 0 || i6 > (i7 = this.f7165l)) {
            throw new IndexOutOfBoundsException(m4056i(i6));
        }
        boolean[] zArr = this.f7164k;
        if (i7 < zArr.length) {
            System.arraycopy(zArr, i6, zArr, i6 + 1, i7 - i6);
        } else {
            boolean[] zArr2 = new boolean[((i7 * 3) / 2) + 1];
            System.arraycopy(zArr, 0, zArr2, 0, i6);
            System.arraycopy(this.f7164k, i6, zArr2, i6 + 1, this.f7165l - i6);
            this.f7164k = zArr2;
        }
        this.f7164k[i6] = booleanValue;
        this.f7165l++;
        ((AbstractList) this).modCount++;
    }

    @Override // p152w2.AbstractC1654o3, java.util.AbstractList, java.util.AbstractCollection, java.util.Collection, java.util.List
    public final /* bridge */ /* synthetic */ boolean add(Object obj) {
        m4054d(((Boolean) obj).booleanValue());
        return true;
    }

    @Override // p152w2.AbstractC1654o3, java.util.AbstractCollection, java.util.Collection, java.util.List
    public final boolean addAll(Collection<? extends Boolean> collection) {
        m3967c();
        Charset charset = C1775y4.f7289a;
        Objects.requireNonNull(collection);
        if (!(collection instanceof C1690r3)) {
            return super.addAll(collection);
        }
        C1690r3 c1690r3 = (C1690r3) collection;
        int i6 = c1690r3.f7165l;
        if (i6 == 0) {
            return false;
        }
        int i7 = this.f7165l;
        if (Integer.MAX_VALUE - i7 < i6) {
            throw new OutOfMemoryError();
        }
        int i8 = i7 + i6;
        boolean[] zArr = this.f7164k;
        if (i8 > zArr.length) {
            this.f7164k = Arrays.copyOf(zArr, i8);
        }
        System.arraycopy(c1690r3.f7164k, 0, this.f7164k, this.f7165l, c1690r3.f7165l);
        this.f7165l = i8;
        ((AbstractList) this).modCount++;
        return true;
    }

    @Override // java.util.AbstractCollection, java.util.Collection, java.util.List
    public final boolean contains(Object obj) {
        return indexOf(obj) != -1;
    }

    /* renamed from: d */
    public final void m4054d(boolean z5) {
        m3967c();
        int i6 = this.f7165l;
        boolean[] zArr = this.f7164k;
        if (i6 == zArr.length) {
            boolean[] zArr2 = new boolean[((i6 * 3) / 2) + 1];
            System.arraycopy(zArr, 0, zArr2, 0, i6);
            this.f7164k = zArr2;
        }
        boolean[] zArr3 = this.f7164k;
        int i7 = this.f7165l;
        this.f7165l = i7 + 1;
        zArr3[i7] = z5;
    }

    @Override // p152w2.AbstractC1654o3, java.util.AbstractList, java.util.Collection, java.util.List
    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof C1690r3)) {
            return super.equals(obj);
        }
        C1690r3 c1690r3 = (C1690r3) obj;
        if (this.f7165l != c1690r3.f7165l) {
            return false;
        }
        boolean[] zArr = c1690r3.f7164k;
        for (int i6 = 0; i6 < this.f7165l; i6++) {
            if (this.f7164k[i6] != zArr[i6]) {
                return false;
            }
        }
        return true;
    }

    /* renamed from: g */
    public final void m4055g(int i6) {
        if (i6 < 0 || i6 >= this.f7165l) {
            throw new IndexOutOfBoundsException(m4056i(i6));
        }
    }

    @Override // java.util.AbstractList, java.util.List
    public final /* bridge */ /* synthetic */ Object get(int i6) {
        m4055g(i6);
        return Boolean.valueOf(this.f7164k[i6]);
    }

    @Override // p152w2.AbstractC1654o3, java.util.AbstractList, java.util.Collection, java.util.List
    public final int hashCode() {
        int i6 = 1;
        for (int i7 = 0; i7 < this.f7165l; i7++) {
            i6 = (i6 * 31) + C1775y4.m4462b(this.f7164k[i7]);
        }
        return i6;
    }

    /* renamed from: i */
    public final String m4056i(int i6) {
        int i7 = this.f7165l;
        StringBuilder sb = new StringBuilder(35);
        sb.append("Index:");
        sb.append(i6);
        sb.append(", Size:");
        sb.append(i7);
        return sb.toString();
    }

    @Override // java.util.AbstractList, java.util.List
    public final int indexOf(Object obj) {
        if (!(obj instanceof Boolean)) {
            return -1;
        }
        boolean booleanValue = ((Boolean) obj).booleanValue();
        int i6 = this.f7165l;
        for (int i7 = 0; i7 < i6; i7++) {
            if (this.f7164k[i7] == booleanValue) {
                return i7;
            }
        }
        return -1;
    }

    @Override // p152w2.InterfaceC1763x4
    /* renamed from: k */
    public final /* bridge */ /* synthetic */ InterfaceC1763x4 mo3662k(int i6) {
        if (i6 >= this.f7165l) {
            return new C1690r3(Arrays.copyOf(this.f7164k, i6), this.f7165l);
        }
        throw new IllegalArgumentException();
    }

    @Override // p152w2.AbstractC1654o3, java.util.AbstractList, java.util.List
    public final /* bridge */ /* synthetic */ Object remove(int i6) {
        m3967c();
        m4055g(i6);
        boolean[] zArr = this.f7164k;
        boolean z5 = zArr[i6];
        if (i6 < this.f7165l - 1) {
            System.arraycopy(zArr, i6 + 1, zArr, i6, (r2 - i6) - 1);
        }
        this.f7165l--;
        ((AbstractList) this).modCount++;
        return Boolean.valueOf(z5);
    }

    @Override // java.util.AbstractList
    public final void removeRange(int i6, int i7) {
        m3967c();
        if (i7 < i6) {
            throw new IndexOutOfBoundsException("toIndex < fromIndex");
        }
        boolean[] zArr = this.f7164k;
        System.arraycopy(zArr, i7, zArr, i6, this.f7165l - i7);
        this.f7165l -= i7 - i6;
        ((AbstractList) this).modCount++;
    }

    @Override // java.util.AbstractList, java.util.List
    public final /* bridge */ /* synthetic */ Object set(int i6, Object obj) {
        boolean booleanValue = ((Boolean) obj).booleanValue();
        m3967c();
        m4055g(i6);
        boolean[] zArr = this.f7164k;
        boolean z5 = zArr[i6];
        zArr[i6] = booleanValue;
        return Boolean.valueOf(z5);
    }

    @Override // java.util.AbstractCollection, java.util.Collection, java.util.List
    public final int size() {
        return this.f7165l;
    }
}
