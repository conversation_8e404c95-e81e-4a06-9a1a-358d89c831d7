package p152w2;

/* renamed from: w2.k5 */
/* loaded from: classes.dex */
public final class C1608k5 implements InterfaceC1704s5 {

    /* renamed from: a */
    public final InterfaceC1704s5[] f7057a;

    public C1608k5(InterfaceC1704s5... interfaceC1704s5Arr) {
        this.f7057a = interfaceC1704s5Arr;
    }

    @Override // p152w2.InterfaceC1704s5
    /* renamed from: a */
    public final boolean mo3871a(Class<?> cls) {
        InterfaceC1704s5[] interfaceC1704s5Arr = this.f7057a;
        for (int i6 = 0; i6 < 2; i6++) {
            if (interfaceC1704s5Arr[i6].mo3871a(cls)) {
                return true;
            }
        }
        return false;
    }

    @Override // p152w2.InterfaceC1704s5
    /* renamed from: b */
    public final InterfaceC1692r5 mo3872b(Class<?> cls) {
        InterfaceC1704s5[] interfaceC1704s5Arr = this.f7057a;
        for (int i6 = 0; i6 < 2; i6++) {
            InterfaceC1704s5 interfaceC1704s5 = interfaceC1704s5Arr[i6];
            if (interfaceC1704s5.mo3871a(cls)) {
                return interfaceC1704s5.mo3872b(cls);
            }
        }
        String name = cls.getName();
        throw new UnsupportedOperationException(name.length() != 0 ? "No factory is available for message type: ".concat(name) : new String("No factory is available for message type: "));
    }
}
