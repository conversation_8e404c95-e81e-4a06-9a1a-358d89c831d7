package p152w2;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.logging.Logger;
import p152w2.AbstractC1630m3;
import p152w2.AbstractC1642n3;

/* renamed from: w2.n3 */
/* loaded from: classes.dex */
public abstract class AbstractC1642n3<MessageType extends AbstractC1642n3<MessageType, BuilderType>, BuilderType extends AbstractC1630m3<MessageType, BuilderType>> implements InterfaceC1716t5 {
    public int zzb = 0;

    /* JADX WARN: Multi-variable type inference failed */
    /* renamed from: i */
    public static <T> void m3941i(Iterable<T> iterable, List<? super T> list) {
        Charset charset = C1775y4.f7289a;
        Objects.requireNonNull(iterable);
        if (iterable instanceof InterfaceC1536e5) {
            List<?> mo3716e = ((InterfaceC1536e5) iterable).mo3716e();
            InterfaceC1536e5 interfaceC1536e5 = (InterfaceC1536e5) list;
            int size = list.size();
            for (Object obj : mo3716e) {
                if (obj == null) {
                    int size2 = interfaceC1536e5.size();
                    StringBuilder sb = new StringBuilder(37);
                    sb.append("Element at index ");
                    sb.append(size2 - size);
                    sb.append(" is null.");
                    String sb2 = sb.toString();
                    int size3 = interfaceC1536e5.size();
                    while (true) {
                        size3--;
                        if (size3 < size) {
                            throw new NullPointerException(sb2);
                        }
                        interfaceC1536e5.remove(size3);
                    }
                } else if (obj instanceof AbstractC1750w3) {
                    interfaceC1536e5.mo3718h((AbstractC1750w3) obj);
                } else {
                    interfaceC1536e5.add((String) obj);
                }
            }
            return;
        }
        if (iterable instanceof InterfaceC1486a6) {
            list.addAll(iterable);
            return;
        }
        if ((list instanceof ArrayList) && (iterable instanceof Collection)) {
            ((ArrayList) list).ensureCapacity(iterable.size() + list.size());
        }
        int size4 = list.size();
        for (T t : iterable) {
            if (t == null) {
                int size5 = list.size();
                StringBuilder sb3 = new StringBuilder(37);
                sb3.append("Element at index ");
                sb3.append(size5 - size4);
                sb3.append(" is null.");
                String sb4 = sb3.toString();
                int size6 = list.size();
                while (true) {
                    size6--;
                    if (size6 < size4) {
                        throw new NullPointerException(sb4);
                    }
                    list.remove(size6);
                }
            } else {
                list.add(t);
            }
        }
    }

    @Override // p152w2.InterfaceC1716t5
    /* renamed from: a */
    public final AbstractC1750w3 mo3942a() {
        try {
            AbstractC1691r4 abstractC1691r4 = (AbstractC1691r4) this;
            int mo4062b = abstractC1691r4.mo4062b();
            C1738v3 c1738v3 = AbstractC1750w3.f7244k;
            byte[] bArr = new byte[mo4062b];
            Logger logger = AbstractC1786z3.f7302H0;
            C1762x3 c1762x3 = new C1762x3(bArr, mo4062b);
            abstractC1691r4.m4067o(c1762x3);
            if (mo4062b - c1762x3.f7270L0 == 0) {
                return new C1738v3(bArr);
            }
            throw new IllegalStateException("Did not write as much data as expected.");
        } catch (IOException e6) {
            String name = getClass().getName();
            StringBuilder sb = new StringBuilder(name.length() + 72);
            sb.append("Serializing ");
            sb.append(name);
            sb.append(" to a ByteString threw an IOException (should never happen).");
            throw new RuntimeException(sb.toString(), e6);
        }
    }

    /* renamed from: f */
    public final byte[] m3943f() {
        try {
            AbstractC1691r4 abstractC1691r4 = (AbstractC1691r4) this;
            int mo4062b = abstractC1691r4.mo4062b();
            byte[] bArr = new byte[mo4062b];
            Logger logger = AbstractC1786z3.f7302H0;
            C1762x3 c1762x3 = new C1762x3(bArr, mo4062b);
            abstractC1691r4.m4067o(c1762x3);
            if (mo4062b - c1762x3.f7270L0 == 0) {
                return bArr;
            }
            throw new IllegalStateException("Did not write as much data as expected.");
        } catch (IOException e6) {
            String name = getClass().getName();
            StringBuilder sb = new StringBuilder(name.length() + 72);
            sb.append("Serializing ");
            sb.append(name);
            sb.append(" to a byte array threw an IOException (should never happen).");
            throw new RuntimeException(sb.toString(), e6);
        }
    }

    /* renamed from: g */
    public int mo3944g() {
        throw null;
    }

    /* renamed from: h */
    public void mo3945h(int i6) {
        throw null;
    }
}
