package p152w2;

import java.lang.Comparable;
import java.util.AbstractMap;
import java.util.AbstractSet;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;

/* renamed from: w2.n6 */
/* loaded from: classes.dex */
public class C1645n6<K extends Comparable<K>, V> extends AbstractMap<K, V> {

    /* renamed from: p */
    public static final /* synthetic */ int f7101p = 0;

    /* renamed from: j */
    public final int f7102j;

    /* renamed from: m */
    public boolean f7105m;

    /* renamed from: n */
    public volatile C1633m6 f7106n;

    /* renamed from: k */
    public List<C1609k6> f7103k = Collections.emptyList();

    /* renamed from: l */
    public Map<K, V> f7104l = Collections.emptyMap();

    /* renamed from: o */
    public Map<K, V> f7107o = Collections.emptyMap();

    /* renamed from: a */
    public void mo3827a() {
        if (this.f7105m) {
            return;
        }
        this.f7104l = this.f7104l.isEmpty() ? Collections.emptyMap() : Collections.unmodifiableMap(this.f7104l);
        this.f7107o = this.f7107o.isEmpty() ? Collections.emptyMap() : Collections.unmodifiableMap(this.f7107o);
        this.f7105m = true;
    }

    /* renamed from: b */
    public final int m3953b() {
        return this.f7103k.size();
    }

    /* renamed from: c */
    public final Map.Entry<K, V> m3954c(int i6) {
        return this.f7103k.get(i6);
    }

    @Override // java.util.AbstractMap, java.util.Map
    public final void clear() {
        m3958g();
        if (!this.f7103k.isEmpty()) {
            this.f7103k.clear();
        }
        if (this.f7104l.isEmpty()) {
            return;
        }
        this.f7104l.clear();
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // java.util.AbstractMap, java.util.Map
    public final boolean containsKey(Object obj) {
        Comparable comparable = (Comparable) obj;
        return m3957f(comparable) >= 0 || this.f7104l.containsKey(comparable);
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // java.util.AbstractMap, java.util.Map
    /* renamed from: d */
    public final V put(K k6, V v6) {
        m3958g();
        int m3957f = m3957f(k6);
        if (m3957f >= 0) {
            return (V) this.f7103k.get(m3957f).setValue(v6);
        }
        m3958g();
        if (this.f7103k.isEmpty() && !(this.f7103k instanceof ArrayList)) {
            this.f7103k = new ArrayList(this.f7102j);
        }
        int i6 = -(m3957f + 1);
        if (i6 >= this.f7102j) {
            return m3959h().put(k6, v6);
        }
        int size = this.f7103k.size();
        int i7 = this.f7102j;
        if (size == i7) {
            C1609k6 remove = this.f7103k.remove(i7 - 1);
            m3959h().put(remove.f7058j, remove.f7059k);
        }
        this.f7103k.add(i6, new C1609k6(this, k6, v6));
        return null;
    }

    /* renamed from: e */
    public final V m3956e(int i6) {
        m3958g();
        V v6 = (V) this.f7103k.remove(i6).f7059k;
        if (!this.f7104l.isEmpty()) {
            Iterator<Map.Entry<K, V>> it = m3959h().entrySet().iterator();
            List<C1609k6> list = this.f7103k;
            Map.Entry<K, V> next = it.next();
            list.add(new C1609k6(this, next.getKey(), next.getValue()));
            it.remove();
        }
        return v6;
    }

    @Override // java.util.AbstractMap, java.util.Map
    public final Set<Map.Entry<K, V>> entrySet() {
        if (this.f7106n == null) {
            this.f7106n = new C1633m6(this);
        }
        return this.f7106n;
    }

    @Override // java.util.AbstractMap, java.util.Map
    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof C1645n6)) {
            return super.equals(obj);
        }
        C1645n6 c1645n6 = (C1645n6) obj;
        int size = size();
        if (size != c1645n6.size()) {
            return false;
        }
        int m3953b = m3953b();
        if (m3953b != c1645n6.m3953b()) {
            return ((AbstractSet) entrySet()).equals(c1645n6.entrySet());
        }
        for (int i6 = 0; i6 < m3953b; i6++) {
            if (!m3954c(i6).equals(c1645n6.m3954c(i6))) {
                return false;
            }
        }
        if (m3953b != size) {
            return this.f7104l.equals(c1645n6.f7104l);
        }
        return true;
    }

    /* renamed from: f */
    public final int m3957f(K k6) {
        int size = this.f7103k.size() - 1;
        int i6 = 0;
        if (size >= 0) {
            int compareTo = k6.compareTo(this.f7103k.get(size).f7058j);
            if (compareTo > 0) {
                return -(size + 2);
            }
            if (compareTo == 0) {
                return size;
            }
        }
        while (i6 <= size) {
            int i7 = (i6 + size) / 2;
            int compareTo2 = k6.compareTo(this.f7103k.get(i7).f7058j);
            if (compareTo2 < 0) {
                size = i7 - 1;
            } else {
                if (compareTo2 <= 0) {
                    return i7;
                }
                i6 = i7 + 1;
            }
        }
        return -(i6 + 1);
    }

    /* renamed from: g */
    public final void m3958g() {
        if (this.f7105m) {
            throw new UnsupportedOperationException();
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // java.util.AbstractMap, java.util.Map
    public final V get(Object obj) {
        Comparable comparable = (Comparable) obj;
        int m3957f = m3957f(comparable);
        return m3957f >= 0 ? (V) this.f7103k.get(m3957f).f7059k : this.f7104l.get(comparable);
    }

    /* renamed from: h */
    public final SortedMap<K, V> m3959h() {
        m3958g();
        if (this.f7104l.isEmpty() && !(this.f7104l instanceof TreeMap)) {
            TreeMap treeMap = new TreeMap();
            this.f7104l = treeMap;
            this.f7107o = treeMap.descendingMap();
        }
        return (SortedMap) this.f7104l;
    }

    @Override // java.util.AbstractMap, java.util.Map
    public final int hashCode() {
        int m3953b = m3953b();
        int i6 = 0;
        for (int i7 = 0; i7 < m3953b; i7++) {
            i6 += this.f7103k.get(i7).hashCode();
        }
        return this.f7104l.size() > 0 ? this.f7104l.hashCode() + i6 : i6;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // java.util.AbstractMap, java.util.Map
    public final V remove(Object obj) {
        m3958g();
        Comparable comparable = (Comparable) obj;
        int m3957f = m3957f(comparable);
        if (m3957f >= 0) {
            return (V) m3956e(m3957f);
        }
        if (this.f7104l.isEmpty()) {
            return null;
        }
        return this.f7104l.remove(comparable);
    }

    @Override // java.util.AbstractMap, java.util.Map
    public final int size() {
        return this.f7104l.size() + this.f7103k.size();
    }
}
