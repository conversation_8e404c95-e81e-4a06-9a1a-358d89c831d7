package p152w2;

import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import org.litepal.parser.LitePalParser;
import p153w3.C1798e;

/* renamed from: w2.v5 */
/* loaded from: classes.dex */
public final class C1740v5 {
    /* renamed from: a */
    public static final void m4332a(StringBuilder sb, int i6, String str, Object obj) {
        if (obj instanceof List) {
            Iterator it = ((List) obj).iterator();
            while (it.hasNext()) {
                m4332a(sb, i6, str, it.next());
            }
            return;
        }
        if (obj instanceof Map) {
            Iterator it2 = ((Map) obj).entrySet().iterator();
            while (it2.hasNext()) {
                m4332a(sb, i6, str, (Map.Entry) it2.next());
            }
            return;
        }
        sb.append('\n');
        int i7 = 0;
        for (int i8 = 0; i8 < i6; i8++) {
            sb.append(' ');
        }
        sb.append(str);
        if (obj instanceof String) {
            sb.append(": \"");
            C1738v3 c1738v3 = AbstractC1750w3.f7244k;
            sb.append(C1798e.m4531X(new C1738v3(((String) obj).getBytes(C1775y4.f7289a))));
            sb.append('\"');
            return;
        }
        if (obj instanceof AbstractC1750w3) {
            sb.append(": \"");
            sb.append(C1798e.m4531X((AbstractC1750w3) obj));
            sb.append('\"');
            return;
        }
        if (obj instanceof AbstractC1691r4) {
            sb.append(" {");
            m4333b((AbstractC1691r4) obj, sb, i6 + 2);
            sb.append("\n");
            while (i7 < i6) {
                sb.append(' ');
                i7++;
            }
            sb.append("}");
            return;
        }
        if (!(obj instanceof Map.Entry)) {
            sb.append(": ");
            sb.append(obj.toString());
            return;
        }
        sb.append(" {");
        Map.Entry entry = (Map.Entry) obj;
        int i9 = i6 + 2;
        m4332a(sb, i9, "key", entry.getKey());
        m4332a(sb, i9, LitePalParser.ATTR_VALUE, entry.getValue());
        sb.append("\n");
        while (i7 < i6) {
            sb.append(' ');
            i7++;
        }
        sb.append("}");
    }

    /* renamed from: b */
    public static void m4333b(InterfaceC1716t5 interfaceC1716t5, StringBuilder sb, int i6) {
        Object obj;
        HashMap hashMap = new HashMap();
        HashMap hashMap2 = new HashMap();
        TreeSet treeSet = new TreeSet();
        for (Method method : interfaceC1716t5.getClass().getDeclaredMethods()) {
            hashMap2.put(method.getName(), method);
            if (method.getParameterTypes().length == 0) {
                hashMap.put(method.getName(), method);
                if (method.getName().startsWith("get")) {
                    treeSet.add(method.getName());
                }
            }
        }
        Iterator it = treeSet.iterator();
        while (it.hasNext()) {
            String str = (String) it.next();
            String substring = str.startsWith("get") ? str.substring(3) : str;
            if (substring.endsWith("List") && !substring.endsWith("OrBuilderList") && !substring.equals("List")) {
                String valueOf = String.valueOf(substring.substring(0, 1).toLowerCase());
                String valueOf2 = String.valueOf(substring.substring(1, substring.length() - 4));
                String concat = valueOf2.length() != 0 ? valueOf.concat(valueOf2) : new String(valueOf);
                Method method2 = (Method) hashMap.get(str);
                if (method2 != null && method2.getReturnType().equals(List.class)) {
                    m4332a(sb, i6, m4334c(concat), AbstractC1691r4.m4057j(method2, interfaceC1716t5, new Object[0]));
                }
            }
            if (substring.endsWith("Map") && !substring.equals("Map")) {
                String valueOf3 = String.valueOf(substring.substring(0, 1).toLowerCase());
                String valueOf4 = String.valueOf(substring.substring(1, substring.length() - 3));
                String concat2 = valueOf4.length() != 0 ? valueOf3.concat(valueOf4) : new String(valueOf3);
                Method method3 = (Method) hashMap.get(str);
                if (method3 != null && method3.getReturnType().equals(Map.class) && !method3.isAnnotationPresent(Deprecated.class) && Modifier.isPublic(method3.getModifiers())) {
                    m4332a(sb, i6, m4334c(concat2), AbstractC1691r4.m4057j(method3, interfaceC1716t5, new Object[0]));
                }
            }
            if (((Method) hashMap2.get(substring.length() != 0 ? "set".concat(substring) : new String("set"))) != null) {
                if (substring.endsWith("Bytes")) {
                    String valueOf5 = String.valueOf(substring.substring(0, substring.length() - 5));
                    if (!hashMap.containsKey(valueOf5.length() != 0 ? "get".concat(valueOf5) : new String("get"))) {
                    }
                }
                String valueOf6 = String.valueOf(substring.substring(0, 1).toLowerCase());
                String valueOf7 = String.valueOf(substring.substring(1));
                String concat3 = valueOf7.length() != 0 ? valueOf6.concat(valueOf7) : new String(valueOf6);
                Method method4 = (Method) hashMap.get(substring.length() != 0 ? "get".concat(substring) : new String("get"));
                Method method5 = (Method) hashMap.get(substring.length() != 0 ? "has".concat(substring) : new String("has"));
                if (method4 != null) {
                    Object m4057j = AbstractC1691r4.m4057j(method4, interfaceC1716t5, new Object[0]);
                    if (method5 == null) {
                        if (m4057j instanceof Boolean) {
                            if (((Boolean) m4057j).booleanValue()) {
                                m4332a(sb, i6, m4334c(concat3), m4057j);
                            }
                        } else if (m4057j instanceof Integer) {
                            if (((Integer) m4057j).intValue() != 0) {
                                m4332a(sb, i6, m4334c(concat3), m4057j);
                            }
                        } else if (m4057j instanceof Float) {
                            if (((Float) m4057j).floatValue() != 0.0f) {
                                m4332a(sb, i6, m4334c(concat3), m4057j);
                            }
                        } else if (!(m4057j instanceof Double)) {
                            if (m4057j instanceof String) {
                                obj = "";
                            } else if (m4057j instanceof AbstractC1750w3) {
                                obj = AbstractC1750w3.f7244k;
                            } else if (!(m4057j instanceof InterfaceC1716t5)) {
                                if ((m4057j instanceof Enum) && ((Enum) m4057j).ordinal() == 0) {
                                }
                                m4332a(sb, i6, m4334c(concat3), m4057j);
                            } else if (m4057j != ((InterfaceC1716t5) m4057j).mo3946d()) {
                                m4332a(sb, i6, m4334c(concat3), m4057j);
                            }
                            if (!m4057j.equals(obj)) {
                                m4332a(sb, i6, m4334c(concat3), m4057j);
                            }
                        } else if (((Double) m4057j).doubleValue() != 0.0d) {
                            m4332a(sb, i6, m4334c(concat3), m4057j);
                        }
                    } else if (((Boolean) AbstractC1691r4.m4057j(method5, interfaceC1716t5, new Object[0])).booleanValue()) {
                        m4332a(sb, i6, m4334c(concat3), m4057j);
                    }
                }
            }
        }
        if (interfaceC1716t5 instanceof AbstractC1667p4) {
            throw null;
        }
        C1681q6 c1681q6 = ((AbstractC1691r4) interfaceC1716t5).zzc;
        if (c1681q6 != null) {
            for (int i7 = 0; i7 < c1681q6.f7151a; i7++) {
                m4332a(sb, i6, String.valueOf(c1681q6.f7152b[i7] >>> 3), c1681q6.f7153c[i7]);
            }
        }
    }

    /* renamed from: c */
    public static final String m4334c(String str) {
        StringBuilder sb = new StringBuilder();
        for (int i6 = 0; i6 < str.length(); i6++) {
            char charAt = str.charAt(i6);
            if (Character.isUpperCase(charAt)) {
                sb.append("_");
            }
            sb.append(Character.toLowerCase(charAt));
        }
        return sb.toString();
    }
}
