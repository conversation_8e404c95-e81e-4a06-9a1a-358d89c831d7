package p152w2;

/* renamed from: w2.u3 */
/* loaded from: classes.dex */
public final class C1726u3 extends C1738v3 {

    /* renamed from: m */
    public final int f7194m;

    public C1726u3(byte[] bArr, int i6) {
        super(bArr);
        AbstractC1750w3.m4358q(0, i6, bArr.length);
        this.f7194m = i6;
    }

    @Override // p152w2.C1738v3, p152w2.AbstractC1750w3
    /* renamed from: c */
    public final byte mo4285c(int i6) {
        int i7 = this.f7194m;
        if (((i7 - (i6 + 1)) | i6) >= 0) {
            return this.f7203l[i6];
        }
        if (i6 < 0) {
            StringBuilder sb = new StringBuilder(22);
            sb.append("Index < 0: ");
            sb.append(i6);
            throw new ArrayIndexOutOfBoundsException(sb.toString());
        }
        StringBuilder sb2 = new StringBuilder(40);
        sb2.append("Index > length: ");
        sb2.append(i6);
        sb2.append(", ");
        sb2.append(i7);
        throw new ArrayIndexOutOfBoundsException(sb2.toString());
    }

    @Override // p152w2.C1738v3, p152w2.AbstractC1750w3
    /* renamed from: d */
    public final byte mo4286d(int i6) {
        return this.f7203l[i6];
    }

    @Override // p152w2.C1738v3, p152w2.AbstractC1750w3
    /* renamed from: g */
    public final int mo4287g() {
        return this.f7194m;
    }

    @Override // p152w2.C1738v3
    /* renamed from: r */
    public final void mo4288r() {
    }
}
