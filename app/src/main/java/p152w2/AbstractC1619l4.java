package p152w2;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.ServiceConfigurationError;
import java.util.ServiceLoader;
import java.util.logging.Level;
import java.util.logging.Logger;
import p152w2.C1523d4;

/* renamed from: w2.l4 */
/* loaded from: classes.dex */
public abstract class AbstractC1619l4<T extends C1523d4> {

    /* renamed from: a */
    public static final Logger f7068a = Logger.getLogger(AbstractC1786z3.class.getName());

    /* renamed from: b */
    public static final String f7069b = "com.google.protobuf.BlazeGeneratedExtensionRegistryLiteLoader";

    /* renamed from: b */
    public static C1523d4 m3901b() {
        String format;
        ClassLoader classLoader = AbstractC1619l4.class.getClassLoader();
        if (C1523d4.class.equals(C1523d4.class)) {
            format = f7069b;
        } else {
            if (!C1523d4.class.getPackage().equals(AbstractC1619l4.class.getPackage())) {
                throw new IllegalArgumentException(C1523d4.class.getName());
            }
            format = String.format("%s.BlazeGenerated%sLoader", C1523d4.class.getPackage().getName(), C1523d4.class.getSimpleName());
        }
        try {
            try {
                try {
                    try {
                        return (C1523d4) C1523d4.class.cast(((AbstractC1619l4) Class.forName(format, true, classLoader).getConstructor(new Class[0]).newInstance(new Object[0])).m3902a());
                    } catch (InstantiationException e6) {
                        throw new IllegalStateException(e6);
                    } catch (NoSuchMethodException e7) {
                        throw new IllegalStateException(e7);
                    }
                } catch (InvocationTargetException e8) {
                    throw new IllegalStateException(e8);
                }
            } catch (IllegalAccessException e9) {
                throw new IllegalStateException(e9);
            }
        } catch (ClassNotFoundException unused) {
            Iterator it = ServiceLoader.load(AbstractC1619l4.class, classLoader).iterator();
            ArrayList arrayList = new ArrayList();
            while (it.hasNext()) {
                try {
                    arrayList.add(C1523d4.class.cast(((AbstractC1619l4) it.next()).m3902a()));
                } catch (ServiceConfigurationError e10) {
                    Logger logger = f7068a;
                    Level level = Level.SEVERE;
                    String simpleName = C1523d4.class.getSimpleName();
                    logger.logp(level, "com.google.protobuf.GeneratedExtensionRegistryLoader", "load", simpleName.length() != 0 ? "Unable to load ".concat(simpleName) : new String("Unable to load "), (Throwable) e10);
                }
            }
            if (arrayList.size() == 1) {
                return (C1523d4) arrayList.get(0);
            }
            if (arrayList.size() == 0) {
                return null;
            }
            try {
                return (C1523d4) C1523d4.class.getMethod("combine", Collection.class).invoke(null, arrayList);
            } catch (IllegalAccessException e11) {
                throw new IllegalStateException(e11);
            } catch (NoSuchMethodException e12) {
                throw new IllegalStateException(e12);
            } catch (InvocationTargetException e13) {
                throw new IllegalStateException(e13);
            }
        }
    }

    /* renamed from: a */
    public abstract T m3902a();
}
