package p152w2;

/* renamed from: w2.ia */
/* loaded from: classes.dex */
public final class C1589ia implements InterfaceC1577ha {

    /* renamed from: a */
    public static final AbstractC1773y2<Boolean> f7014a = (C1737v2) new C1749w2(C1713t2.m4275a()).m4355b("measurement.client.reject_blank_user_id", true);

    @Override // p152w2.InterfaceC1577ha
    /* renamed from: a */
    public final boolean mo3844a() {
        return f7014a.m4460c().booleanValue();
    }
}
