package p152w2;

/* renamed from: w2.t8 */
/* loaded from: classes.dex */
public final class C1719t8 implements InterfaceC1707s8 {

    /* renamed from: a */
    public static final AbstractC1773y2<Boolean> f7189a;

    static {
        C1749w2 c1749w2 = new C1749w2(C1713t2.m4275a());
        f7189a = (C1737v2) c1749w2.m4355b("measurement.client.sessions.check_on_reset_and_enable2", true);
        c1749w2.m4355b("measurement.client.sessions.check_on_startup", true);
        c1749w2.m4355b("measurement.client.sessions.start_session_before_view_screen", true);
    }

    @Override // p152w2.InterfaceC1707s8
    /* renamed from: a */
    public final void mo4145a() {
    }

    @Override // p152w2.InterfaceC1707s8
    /* renamed from: b */
    public final boolean mo4146b() {
        return f7189a.m4460c().booleanValue();
    }
}
