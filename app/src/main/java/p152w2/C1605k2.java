package p152w2;

import android.content.Context;
import javax.annotation.Nullable;

/* renamed from: w2.k2 */
/* loaded from: classes.dex */
public final class C1605k2 extends AbstractC1761x2 {

    /* renamed from: a */
    public final Context f7053a;

    /* renamed from: b */
    public final InterfaceC1522d3<AbstractC1496b3<C1689r2>> f7054b;

    public C1605k2(Context context, @Nullable InterfaceC1522d3<AbstractC1496b3<C1689r2>> interfaceC1522d3) {
        this.f7053a = context;
        this.f7054b = interfaceC1522d3;
    }

    @Override // p152w2.AbstractC1761x2
    /* renamed from: a */
    public final Context mo3883a() {
        return this.f7053a;
    }

    @Override // p152w2.AbstractC1761x2
    @Nullable
    /* renamed from: b */
    public final InterfaceC1522d3<AbstractC1496b3<C1689r2>> mo3884b() {
        return this.f7054b;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof AbstractC1761x2) {
            AbstractC1761x2 abstractC1761x2 = (AbstractC1761x2) obj;
            if (this.f7053a.equals(abstractC1761x2.mo3883a())) {
                InterfaceC1522d3<AbstractC1496b3<C1689r2>> interfaceC1522d3 = this.f7054b;
                InterfaceC1522d3<AbstractC1496b3<C1689r2>> mo3884b = abstractC1761x2.mo3884b();
                if (interfaceC1522d3 != null ? interfaceC1522d3.equals(mo3884b) : mo3884b == null) {
                    return true;
                }
            }
        }
        return false;
    }

    public final int hashCode() {
        int hashCode = (this.f7053a.hashCode() ^ 1000003) * 1000003;
        InterfaceC1522d3<AbstractC1496b3<C1689r2>> interfaceC1522d3 = this.f7054b;
        return hashCode ^ (interfaceC1522d3 == null ? 0 : interfaceC1522d3.hashCode());
    }

    public final String toString() {
        String valueOf = String.valueOf(this.f7053a);
        String valueOf2 = String.valueOf(this.f7054b);
        StringBuilder sb = new StringBuilder(valueOf.length() + 46 + valueOf2.length());
        sb.append("FlagsContext{context=");
        sb.append(valueOf);
        sb.append(", hermeticFileOverrides=");
        sb.append(valueOf2);
        sb.append("}");
        return sb.toString();
    }
}
