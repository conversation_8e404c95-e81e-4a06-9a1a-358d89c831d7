package p152w2;

import android.net.Uri;
import java.util.HashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Pattern;

/* renamed from: w2.j2 */
/* loaded from: classes.dex */
public final class C1593j2 {

    /* renamed from: a */
    public static final Uri f7021a = Uri.parse("content://com.google.android.gsf.gservices");

    /* renamed from: b */
    public static final Pattern f7022b;

    /* renamed from: c */
    public static final Pattern f7023c;

    /* renamed from: d */
    public static final AtomicBoolean f7024d;

    /* renamed from: e */
    public static HashMap<String, String> f7025e;

    /* renamed from: f */
    public static final HashMap<String, Boolean> f7026f;

    /* renamed from: g */
    public static final HashMap<String, Integer> f7027g;

    /* renamed from: h */
    public static final HashMap<String, Long> f7028h;

    /* renamed from: i */
    public static final HashMap<String, Float> f7029i;

    /* renamed from: j */
    public static Object f7030j;

    /* renamed from: k */
    public static final String[] f7031k;

    static {
        Uri.parse("content://com.google.android.gsf.gservices/prefix");
        f7022b = Pattern.compile("^(1|true|t|on|yes|y)$", 2);
        f7023c = Pattern.compile("^(0|false|f|off|no|n)$", 2);
        f7024d = new AtomicBoolean();
        f7026f = new HashMap<>();
        f7027g = new HashMap<>();
        f7028h = new HashMap<>();
        f7029i = new HashMap<>();
        f7031k = new String[0];
    }
}
