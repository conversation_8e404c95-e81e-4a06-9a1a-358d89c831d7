package p152w2;

import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/* renamed from: w2.o5 */
/* loaded from: classes.dex */
public final class C1656o5<K, V> extends LinkedHashMap<K, V> {

    /* renamed from: k */
    public static final C1656o5 f7121k;

    /* renamed from: j */
    public boolean f7122j;

    static {
        C1656o5 c1656o5 = new C1656o5();
        f7121k = c1656o5;
        c1656o5.f7122j = false;
    }

    public C1656o5() {
        this.f7122j = true;
    }

    public C1656o5(Map<K, V> map) {
        super(map);
        this.f7122j = true;
    }

    /* renamed from: a */
    public static int m3968a(Object obj) {
        if (!(obj instanceof byte[])) {
            if (obj instanceof InterfaceC1715t4) {
                throw new UnsupportedOperationException();
            }
            return obj.hashCode();
        }
        byte[] bArr = (byte[]) obj;
        Charset charset = C1775y4.f7289a;
        int length = bArr.length;
        for (byte b6 : bArr) {
            length = (length * 31) + b6;
        }
        if (length == 0) {
            return 1;
        }
        return length;
    }

    /* renamed from: b */
    public final void m3969b() {
        if (!this.f7122j) {
            throw new UnsupportedOperationException();
        }
    }

    @Override // java.util.LinkedHashMap, java.util.HashMap, java.util.AbstractMap, java.util.Map
    public final void clear() {
        m3969b();
        super.clear();
    }

    @Override // java.util.LinkedHashMap, java.util.HashMap, java.util.AbstractMap, java.util.Map
    public final Set<Map.Entry<K, V>> entrySet() {
        return isEmpty() ? Collections.emptySet() : super.entrySet();
    }

    @Override // java.util.AbstractMap, java.util.Map
    public final boolean equals(Object obj) {
        if (!(obj instanceof Map)) {
            return false;
        }
        Map map = (Map) obj;
        if (this == map) {
            return true;
        }
        if (size() != map.size()) {
            return false;
        }
        for (Map.Entry<K, V> entry : entrySet()) {
            if (!map.containsKey(entry.getKey())) {
                return false;
            }
            V value = entry.getValue();
            Object obj2 = map.get(entry.getKey());
            if (!(((value instanceof byte[]) && (obj2 instanceof byte[])) ? Arrays.equals((byte[]) value, (byte[]) obj2) : value.equals(obj2))) {
                return false;
            }
        }
        return true;
    }

    @Override // java.util.AbstractMap, java.util.Map
    public final int hashCode() {
        int i6 = 0;
        for (Map.Entry<K, V> entry : entrySet()) {
            i6 += m3968a(entry.getValue()) ^ m3968a(entry.getKey());
        }
        return i6;
    }

    @Override // java.util.HashMap, java.util.AbstractMap, java.util.Map
    public final V put(K k6, V v6) {
        m3969b();
        Charset charset = C1775y4.f7289a;
        Objects.requireNonNull(k6);
        Objects.requireNonNull(v6);
        return (V) super.put(k6, v6);
    }

    @Override // java.util.HashMap, java.util.AbstractMap, java.util.Map
    public final void putAll(Map<? extends K, ? extends V> map) {
        m3969b();
        for (K k6 : map.keySet()) {
            Charset charset = C1775y4.f7289a;
            Objects.requireNonNull(k6);
            Objects.requireNonNull(map.get(k6));
        }
        super.putAll(map);
    }

    @Override // java.util.HashMap, java.util.AbstractMap, java.util.Map
    public final V remove(Object obj) {
        m3969b();
        return (V) super.remove(obj);
    }
}
