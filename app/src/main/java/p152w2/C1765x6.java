package p152w2;

import sun.misc.Unsafe;

/* renamed from: w2.x6 */
/* loaded from: classes.dex */
public final class C1765x6 extends AbstractC1777y6 {
    public C1765x6(Unsafe unsafe) {
        super(unsafe);
    }

    @Override // p152w2.AbstractC1777y6
    /* renamed from: a */
    public final void mo4390a(Object obj, long j6, byte b6) {
        if (C1789z6.f7313g) {
            C1789z6.m4485c(obj, j6, b6);
        } else {
            C1789z6.m4486d(obj, j6, b6);
        }
    }

    @Override // p152w2.AbstractC1777y6
    /* renamed from: b */
    public final boolean mo4391b(Object obj, long j6) {
        return C1789z6.f7313g ? C1789z6.m4503u(obj, j6) : C1789z6.m4504v(obj, j6);
    }

    @Override // p152w2.AbstractC1777y6
    /* renamed from: c */
    public final void mo4392c(Object obj, long j6, boolean z5) {
        if (C1789z6.f7313g) {
            C1789z6.m4485c(obj, j6, z5 ? (byte) 1 : (byte) 0);
        } else {
            C1789z6.m4486d(obj, j6, z5 ? (byte) 1 : (byte) 0);
        }
    }

    @Override // p152w2.AbstractC1777y6
    /* renamed from: d */
    public final float mo4393d(Object obj, long j6) {
        return Float.intBitsToFloat(m4467k(obj, j6));
    }

    @Override // p152w2.AbstractC1777y6
    /* renamed from: e */
    public final void mo4394e(Object obj, long j6, float f6) {
        m4468l(obj, j6, Float.floatToIntBits(f6));
    }

    @Override // p152w2.AbstractC1777y6
    /* renamed from: f */
    public final double mo4395f(Object obj, long j6) {
        return Double.longBitsToDouble(m4469m(obj, j6));
    }

    @Override // p152w2.AbstractC1777y6
    /* renamed from: g */
    public final void mo4396g(Object obj, long j6, double d6) {
        m4470n(obj, j6, Double.doubleToLongBits(d6));
    }
}
