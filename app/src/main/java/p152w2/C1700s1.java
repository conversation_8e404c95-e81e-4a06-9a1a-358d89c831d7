package p152w2;

import android.os.Build;
import java.util.Collections;
import java.util.List;

/* renamed from: w2.s1 */
/* loaded from: classes.dex */
public final class C1700s1 extends C1643n4<C1712t1, C1700s1> {
    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public C1700s1() {
        /*
            r1 = this;
            w2.t1 r0 = p152w2.C1712t1.m4159D0()
            r1.<init>(r0)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1700s1.<init>():void");
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public /* synthetic */ C1700s1(p008b0.C0385m r1) {
        /*
            r0 = this;
            w2.t1 r1 = p152w2.C1712t1.m4159D0()
            r0.<init>(r1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1700s1.<init>(b0.m):void");
    }

    /* renamed from: A */
    public final C1700s1 m4072A(String str) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4203j0((C1712t1) this.f7099k, str);
        return this;
    }

    /* renamed from: B */
    public final C1700s1 m4073B() {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4204k0((C1712t1) this.f7099k);
        return this;
    }

    /* renamed from: C */
    public final C1700s1 m4074C(long j6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4205l0((C1712t1) this.f7099k, j6);
        return this;
    }

    /* renamed from: D */
    public final C1700s1 m4075D(int i6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4206m0((C1712t1) this.f7099k, i6);
        return this;
    }

    /* renamed from: E */
    public final C1700s1 m4076E(String str) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4207n0((C1712t1) this.f7099k, str);
        return this;
    }

    /* renamed from: F */
    public final C1700s1 m4077F() {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4208o0((C1712t1) this.f7099k);
        return this;
    }

    /* renamed from: G */
    public final String m4078G() {
        return ((C1712t1) this.f7099k).m4227H();
    }

    /* renamed from: H */
    public final C1700s1 m4079H(String str) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4209p0((C1712t1) this.f7099k, str);
        return this;
    }

    /* renamed from: I */
    public final C1700s1 m4080I() {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4210q0((C1712t1) this.f7099k);
        return this;
    }

    /* renamed from: J */
    public final C1700s1 m4081J(Iterable<? extends C1580i1> iterable) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4211r0((C1712t1) this.f7099k, iterable);
        return this;
    }

    /* renamed from: K */
    public final C1700s1 m4082K() {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4212s0((C1712t1) this.f7099k);
        return this;
    }

    /* renamed from: L */
    public final C1700s1 m4083L() {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4160E0((C1712t1) this.f7099k);
        return this;
    }

    /* renamed from: M */
    public final C1700s1 m4084M(String str) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4177V0((C1712t1) this.f7099k, str);
        return this;
    }

    /* renamed from: N */
    public final C1700s1 m4085N(int i6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4179W0((C1712t1) this.f7099k, i6);
        return this;
    }

    /* renamed from: O */
    public final C1700s1 m4086O() {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4181X0((C1712t1) this.f7099k);
        return this;
    }

    /* renamed from: P */
    public final C1700s1 m4087P(long j6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4183Y0((C1712t1) this.f7099k, j6);
        return this;
    }

    /* renamed from: Q */
    public final C1700s1 m4088Q(long j6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4185Z0((C1712t1) this.f7099k, j6);
        return this;
    }

    /* renamed from: R */
    public final C1700s1 m4089R() {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        int i6 = C1712t1.zza;
        throw null;
    }

    /* renamed from: S */
    public final C1700s1 m4090S() {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4187a1((C1712t1) this.f7099k);
        return this;
    }

    /* renamed from: T */
    public final C1700s1 m4091T(int i6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4189b1((C1712t1) this.f7099k, i6);
        return this;
    }

    /* renamed from: U */
    public final C1700s1 m4092U(String str) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4191c1((C1712t1) this.f7099k, str);
        return this;
    }

    /* renamed from: V */
    public final C1700s1 m4093V(Iterable<? extends Integer> iterable) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4193d1((C1712t1) this.f7099k, iterable);
        return this;
    }

    /* renamed from: W */
    public final C1700s1 m4094W(long j6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4195e1((C1712t1) this.f7099k, j6);
        return this;
    }

    /* renamed from: X */
    public final C1700s1 m4095X(long j6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4197f1((C1712t1) this.f7099k, j6);
        return this;
    }

    /* renamed from: Y */
    public final String m4096Y() {
        return ((C1712t1) this.f7099k).m4273z0();
    }

    /* renamed from: Z */
    public final C1700s1 m4097Z(String str) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4199g1((C1712t1) this.f7099k, str);
        return this;
    }

    /* renamed from: a0 */
    public final C1700s1 m4098a0(String str) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4201h1((C1712t1) this.f7099k, str);
        return this;
    }

    /* renamed from: b0 */
    public final List<C1628m1> m4099b0() {
        return Collections.unmodifiableList(((C1712t1) this.f7099k).m4242i1());
    }

    /* renamed from: c0 */
    public final int m4100c0() {
        return ((C1712t1) this.f7099k).m4243j1();
    }

    /* renamed from: d0 */
    public final C1628m1 m4101d0(int i6) {
        return ((C1712t1) this.f7099k).m4244k1(i6);
    }

    /* renamed from: e0 */
    public final C1700s1 m4102e0(int i6, C1616l1 c1616l1) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4161F0((C1712t1) this.f7099k, i6, c1616l1.m3947f());
        return this;
    }

    /* renamed from: f0 */
    public final C1700s1 m4103f0(C1616l1 c1616l1) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4162G0((C1712t1) this.f7099k, c1616l1.m3947f());
        return this;
    }

    /* renamed from: g0 */
    public final C1700s1 m4104g0(Iterable<? extends C1628m1> iterable) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4163H0((C1712t1) this.f7099k, iterable);
        return this;
    }

    /* renamed from: h0 */
    public final C1700s1 m4105h0() {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4164I0((C1712t1) this.f7099k);
        return this;
    }

    /* renamed from: i0 */
    public final C1700s1 m4106i0(int i6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4165J0((C1712t1) this.f7099k, i6);
        return this;
    }

    /* renamed from: j0 */
    public final List<C1508c2> m4107j0() {
        return Collections.unmodifiableList(((C1712t1) this.f7099k).m4245l1());
    }

    /* renamed from: k0 */
    public final int m4108k0() {
        return ((C1712t1) this.f7099k).m4246m1();
    }

    /* renamed from: l */
    public final C1700s1 m4109l() {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4176V((C1712t1) this.f7099k);
        return this;
    }

    /* renamed from: l0 */
    public final C1508c2 m4110l0(int i6) {
        return ((C1712t1) this.f7099k).m4247n1(i6);
    }

    /* renamed from: m */
    public final C1700s1 m4111m() {
        String str = Build.VERSION.RELEASE;
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4178W((C1712t1) this.f7099k);
        return this;
    }

    /* renamed from: m0 */
    public final C1700s1 m4112m0(int i6, C1508c2 c1508c2) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4166K0((C1712t1) this.f7099k, i6, c1508c2);
        return this;
    }

    /* renamed from: n */
    public final C1700s1 m4113n() {
        String str = Build.MODEL;
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4180X((C1712t1) this.f7099k);
        return this;
    }

    /* renamed from: n0 */
    public final C1700s1 m4114n0(C1508c2 c1508c2) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4167L0((C1712t1) this.f7099k, c1508c2);
        return this;
    }

    /* renamed from: o */
    public final C1700s1 m4115o(String str) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4182Y((C1712t1) this.f7099k, str);
        return this;
    }

    /* renamed from: o0 */
    public final C1700s1 m4116o0(C1495b2 c1495b2) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4167L0((C1712t1) this.f7099k, c1495b2.m3947f());
        return this;
    }

    /* renamed from: p */
    public final C1700s1 m4117p(int i6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4184Z((C1712t1) this.f7099k, i6);
        return this;
    }

    /* renamed from: p0 */
    public final C1700s1 m4118p0(int i6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4168M0((C1712t1) this.f7099k, i6);
        return this;
    }

    /* renamed from: q */
    public final C1700s1 m4119q(String str) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4186a0((C1712t1) this.f7099k, str);
        return this;
    }

    /* renamed from: q0 */
    public final C1700s1 m4120q0(long j6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4169N0((C1712t1) this.f7099k, j6);
        return this;
    }

    /* renamed from: r */
    public final String m4121r() {
        return ((C1712t1) this.f7099k).m4252s();
    }

    /* renamed from: r0 */
    public final long m4122r0() {
        return ((C1712t1) this.f7099k).m4251r1();
    }

    /* renamed from: s */
    public final C1700s1 m4123s(String str) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4188b0((C1712t1) this.f7099k, str);
        return this;
    }

    /* renamed from: s0 */
    public final C1700s1 m4124s0(long j6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4170O0((C1712t1) this.f7099k, j6);
        return this;
    }

    /* renamed from: t */
    public final C1700s1 m4125t(String str) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4190c0((C1712t1) this.f7099k, str);
        return this;
    }

    /* renamed from: t0 */
    public final long m4126t0() {
        return ((C1712t1) this.f7099k).m4256t1();
    }

    /* renamed from: u */
    public final C1700s1 m4127u(long j6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4192d0((C1712t1) this.f7099k, j6);
        return this;
    }

    /* renamed from: u0 */
    public final C1700s1 m4128u0(long j6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4171P0((C1712t1) this.f7099k, j6);
        return this;
    }

    /* renamed from: v */
    public final C1700s1 m4129v() {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4194e0((C1712t1) this.f7099k);
        return this;
    }

    /* renamed from: v0 */
    public final C1700s1 m4130v0(long j6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4172Q0((C1712t1) this.f7099k, j6);
        return this;
    }

    /* renamed from: w */
    public final C1700s1 m4131w(String str) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4196f0((C1712t1) this.f7099k, str);
        return this;
    }

    /* renamed from: w0 */
    public final C1700s1 m4132w0() {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4173R0((C1712t1) this.f7099k);
        return this;
    }

    /* renamed from: x */
    public final C1700s1 m4133x() {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4198g0((C1712t1) this.f7099k);
        return this;
    }

    /* renamed from: x0 */
    public final C1700s1 m4134x0(long j6) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4174T((C1712t1) this.f7099k, j6);
        return this;
    }

    /* renamed from: y */
    public final C1700s1 m4135y(boolean z5) {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4200h0((C1712t1) this.f7099k, z5);
        return this;
    }

    /* renamed from: y0 */
    public final C1700s1 m4136y0() {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4175U((C1712t1) this.f7099k);
        return this;
    }

    /* renamed from: z */
    public final C1700s1 m4137z() {
        if (this.f7100l) {
            m3950i();
            this.f7100l = false;
        }
        C1712t1.m4202i0((C1712t1) this.f7099k);
        return this;
    }
}
