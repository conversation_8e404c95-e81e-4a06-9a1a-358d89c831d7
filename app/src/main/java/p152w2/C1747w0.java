package p152w2;

import androidx.activity.result.C0052a;

/* renamed from: w2.w0 */
/* loaded from: classes.dex */
public final class C1747w0 extends AbstractC1691r4<C1747w0, C1723u0> implements InterfaceC1728u5 {
    private static final C1747w0 zzj;
    private int zza;
    private int zze;
    private boolean zzf;
    private String zzg = "";
    private String zzh = "";
    private String zzi = "";

    static {
        C1747w0 c1747w0 = new C1747w0();
        zzj = c1747w0;
        AbstractC1691r4.m4061q(C1747w0.class, c1747w0);
    }

    /* renamed from: C */
    public static C1747w0 m4341C() {
        return zzj;
    }

    /* renamed from: A */
    public final boolean m4343A() {
        return (this.zza & 16) != 0;
    }

    /* renamed from: B */
    public final String m4344B() {
        return this.zzi;
    }

    @Override // p152w2.AbstractC1691r4
    /* renamed from: r */
    public final Object mo3604r(int i6) {
        int i7 = i6 - 1;
        if (i7 == 0) {
            return (byte) 1;
        }
        if (i7 == 2) {
            return new C1525d6(zzj, "\u0001\u0005\u0000\u0001\u0001\u0005\u0005\u0000\u0000\u0000\u0001ဌ\u0000\u0002ဇ\u0001\u0003ဈ\u0002\u0004ဈ\u0003\u0005ဈ\u0004", new Object[]{"zza", "zze", C1735v0.f7200a, "zzf", "zzg", "zzh", "zzi"});
        }
        if (i7 == 3) {
            return new C1747w0();
        }
        if (i7 == 4) {
            return new C1723u0(0);
        }
        if (i7 != 5) {
            return null;
        }
        return zzj;
    }

    /* renamed from: s */
    public final boolean m4345s() {
        return (this.zza & 1) != 0;
    }

    /* renamed from: t */
    public final int m4346t() {
        int m99c = C0052a.m99c(this.zze);
        if (m99c == 0) {
            return 1;
        }
        return m99c;
    }

    /* renamed from: u */
    public final boolean m4347u() {
        return (this.zza & 2) != 0;
    }

    /* renamed from: v */
    public final boolean m4348v() {
        return this.zzf;
    }

    /* renamed from: w */
    public final boolean m4349w() {
        return (this.zza & 4) != 0;
    }

    /* renamed from: x */
    public final String m4350x() {
        return this.zzg;
    }

    /* renamed from: y */
    public final boolean m4351y() {
        return (this.zza & 8) != 0;
    }

    /* renamed from: z */
    public final String m4352z() {
        return this.zzh;
    }
}
