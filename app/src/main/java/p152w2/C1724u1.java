package p152w2;

/* renamed from: w2.u1 */
/* loaded from: classes.dex */
public final class C1724u1 extends C1643n4 {
    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public /* synthetic */ C1724u1(int r2) {
        /*
            r1 = this;
            r0 = 1
            if (r2 == r0) goto Lb
            w2.e2 r2 = p152w2.C1533e2.m3742u()
            r1.<init>(r2)
            return
        Lb:
            w2.h2 r2 = p152w2.C1569h2.m3830B()
            r1.<init>(r2)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1724u1.<init>(int):void");
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public /* synthetic */ C1724u1(p008b0.C0385m r1) {
        /*
            r0 = this;
            w2.w1 r1 = p152w2.C1748w1.m4353s()
            r0.<init>(r1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: p152w2.C1724u1.<init>(b0.m):void");
    }
}
