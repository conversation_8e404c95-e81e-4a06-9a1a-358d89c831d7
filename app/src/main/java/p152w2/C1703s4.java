package p152w2;

import java.nio.charset.Charset;
import java.util.AbstractList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Objects;
import java.util.RandomAccess;

/* renamed from: w2.s4 */
/* loaded from: classes.dex */
public final class C1703s4 extends AbstractC1654o3<Integer> implements RandomAccess, InterfaceC1739v4, InterfaceC1486a6 {

    /* renamed from: m */
    public static final C1703s4 f7175m;

    /* renamed from: k */
    public int[] f7176k;

    /* renamed from: l */
    public int f7177l;

    static {
        C1703s4 c1703s4 = new C1703s4(new int[0], 0);
        f7175m = c1703s4;
        c1703s4.f7118j = false;
    }

    public C1703s4() {
        this(new int[10], 0);
    }

    public C1703s4(int[] iArr, int i6) {
        this.f7176k = iArr;
        this.f7177l = i6;
    }

    @Override // java.util.AbstractList, java.util.List
    public final /* bridge */ /* synthetic */ void add(int i6, Object obj) {
        int i7;
        int intValue = ((Integer) obj).intValue();
        m3967c();
        if (i6 < 0 || i6 > (i7 = this.f7177l)) {
            throw new IndexOutOfBoundsException(m4144l(i6));
        }
        int[] iArr = this.f7176k;
        if (i7 < iArr.length) {
            System.arraycopy(iArr, i6, iArr, i6 + 1, i7 - i6);
        } else {
            int[] iArr2 = new int[((i7 * 3) / 2) + 1];
            System.arraycopy(iArr, 0, iArr2, 0, i6);
            System.arraycopy(this.f7176k, i6, iArr2, i6 + 1, this.f7177l - i6);
            this.f7176k = iArr2;
        }
        this.f7176k[i6] = intValue;
        this.f7177l++;
        ((AbstractList) this).modCount++;
    }

    @Override // p152w2.AbstractC1654o3, java.util.AbstractList, java.util.AbstractCollection, java.util.Collection, java.util.List
    public final /* bridge */ /* synthetic */ boolean add(Object obj) {
        m4142i(((Integer) obj).intValue());
        return true;
    }

    @Override // p152w2.AbstractC1654o3, java.util.AbstractCollection, java.util.Collection, java.util.List
    public final boolean addAll(Collection<? extends Integer> collection) {
        m3967c();
        Charset charset = C1775y4.f7289a;
        Objects.requireNonNull(collection);
        if (!(collection instanceof C1703s4)) {
            return super.addAll(collection);
        }
        C1703s4 c1703s4 = (C1703s4) collection;
        int i6 = c1703s4.f7177l;
        if (i6 == 0) {
            return false;
        }
        int i7 = this.f7177l;
        if (Integer.MAX_VALUE - i7 < i6) {
            throw new OutOfMemoryError();
        }
        int i8 = i7 + i6;
        int[] iArr = this.f7176k;
        if (i8 > iArr.length) {
            this.f7176k = Arrays.copyOf(iArr, i8);
        }
        System.arraycopy(c1703s4.f7176k, 0, this.f7176k, this.f7177l, c1703s4.f7177l);
        this.f7177l = i8;
        ((AbstractList) this).modCount++;
        return true;
    }

    @Override // java.util.AbstractCollection, java.util.Collection, java.util.List
    public final boolean contains(Object obj) {
        return indexOf(obj) != -1;
    }

    @Override // p152w2.InterfaceC1763x4
    /* renamed from: d, reason: merged with bridge method [inline-methods] */
    public final InterfaceC1739v4 mo3662k(int i6) {
        if (i6 >= this.f7177l) {
            return new C1703s4(Arrays.copyOf(this.f7176k, i6), this.f7177l);
        }
        throw new IllegalArgumentException();
    }

    @Override // p152w2.AbstractC1654o3, java.util.AbstractList, java.util.Collection, java.util.List
    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof C1703s4)) {
            return super.equals(obj);
        }
        C1703s4 c1703s4 = (C1703s4) obj;
        if (this.f7177l != c1703s4.f7177l) {
            return false;
        }
        int[] iArr = c1703s4.f7176k;
        for (int i6 = 0; i6 < this.f7177l; i6++) {
            if (this.f7176k[i6] != iArr[i6]) {
                return false;
            }
        }
        return true;
    }

    /* renamed from: g */
    public final int m4141g(int i6) {
        m4143j(i6);
        return this.f7176k[i6];
    }

    @Override // java.util.AbstractList, java.util.List
    public final /* bridge */ /* synthetic */ Object get(int i6) {
        m4143j(i6);
        return Integer.valueOf(this.f7176k[i6]);
    }

    @Override // p152w2.AbstractC1654o3, java.util.AbstractList, java.util.Collection, java.util.List
    public final int hashCode() {
        int i6 = 1;
        for (int i7 = 0; i7 < this.f7177l; i7++) {
            i6 = (i6 * 31) + this.f7176k[i7];
        }
        return i6;
    }

    /* renamed from: i */
    public final void m4142i(int i6) {
        m3967c();
        int i7 = this.f7177l;
        int[] iArr = this.f7176k;
        if (i7 == iArr.length) {
            int[] iArr2 = new int[((i7 * 3) / 2) + 1];
            System.arraycopy(iArr, 0, iArr2, 0, i7);
            this.f7176k = iArr2;
        }
        int[] iArr3 = this.f7176k;
        int i8 = this.f7177l;
        this.f7177l = i8 + 1;
        iArr3[i8] = i6;
    }

    @Override // java.util.AbstractList, java.util.List
    public final int indexOf(Object obj) {
        if (!(obj instanceof Integer)) {
            return -1;
        }
        int intValue = ((Integer) obj).intValue();
        int i6 = this.f7177l;
        for (int i7 = 0; i7 < i6; i7++) {
            if (this.f7176k[i7] == intValue) {
                return i7;
            }
        }
        return -1;
    }

    /* renamed from: j */
    public final void m4143j(int i6) {
        if (i6 < 0 || i6 >= this.f7177l) {
            throw new IndexOutOfBoundsException(m4144l(i6));
        }
    }

    /* renamed from: l */
    public final String m4144l(int i6) {
        int i7 = this.f7177l;
        StringBuilder sb = new StringBuilder(35);
        sb.append("Index:");
        sb.append(i6);
        sb.append(", Size:");
        sb.append(i7);
        return sb.toString();
    }

    @Override // p152w2.AbstractC1654o3, java.util.AbstractList, java.util.List
    public final /* bridge */ /* synthetic */ Object remove(int i6) {
        m3967c();
        m4143j(i6);
        int[] iArr = this.f7176k;
        int i7 = iArr[i6];
        if (i6 < this.f7177l - 1) {
            System.arraycopy(iArr, i6 + 1, iArr, i6, (r2 - i6) - 1);
        }
        this.f7177l--;
        ((AbstractList) this).modCount++;
        return Integer.valueOf(i7);
    }

    @Override // java.util.AbstractList
    public final void removeRange(int i6, int i7) {
        m3967c();
        if (i7 < i6) {
            throw new IndexOutOfBoundsException("toIndex < fromIndex");
        }
        int[] iArr = this.f7176k;
        System.arraycopy(iArr, i7, iArr, i6, this.f7177l - i7);
        this.f7177l -= i7 - i6;
        ((AbstractList) this).modCount++;
    }

    @Override // java.util.AbstractList, java.util.List
    public final /* bridge */ /* synthetic */ Object set(int i6, Object obj) {
        int intValue = ((Integer) obj).intValue();
        m3967c();
        m4143j(i6);
        int[] iArr = this.f7176k;
        int i7 = iArr[i6];
        iArr[i6] = intValue;
        return Integer.valueOf(i7);
    }

    @Override // java.util.AbstractCollection, java.util.Collection, java.util.List
    public final int size() {
        return this.f7177l;
    }
}
