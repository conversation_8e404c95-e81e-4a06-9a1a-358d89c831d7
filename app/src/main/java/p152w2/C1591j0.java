package p152w2;

import androidx.lifecycle.C0258k;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.Callable;
import p090n.C1090c;
import p124s1.C1336c;
import p129t0.C1372n;

/* renamed from: w2.j0 */
/* loaded from: classes.dex */
public final class C1591j0 {

    /* renamed from: a */
    public final C1372n f7017a;

    /* renamed from: b */
    public C1090c f7018b;

    /* renamed from: c */
    public final C1505c f7019c;

    /* renamed from: d */
    public final C1336c f7020d;

    public C1591j0() {
        C1372n c1372n = new C1372n(2);
        this.f7017a = c1372n;
        this.f7018b = ((C1090c) c1372n.f6354k).m2805d();
        this.f7019c = new C1505c();
        final int i6 = 1;
        this.f7020d = new C1336c(1);
        final int i7 = 0;
        ((C0258k) c1372n.f6356m).m889a("internal.registerCallback", new Callable(this) { // from class: w2.a

            /* renamed from: b */
            public final C1591j0 f6806b;

            {
                this.f6806b = this;
            }

            @Override // java.util.concurrent.Callable
            public final Object call() {
                switch (i7) {
                    case 0:
                        return new C1597j6(this.f6806b.f7020d);
                    default:
                        return new C1655o4(this.f6806b.f7019c);
                }
            }
        });
        ((C0258k) c1372n.f6356m).m889a("internal.eventLogger", new Callable(this) { // from class: w2.a

            /* renamed from: b */
            public final C1591j0 f6806b;

            {
                this.f6806b = this;
            }

            @Override // java.util.concurrent.Callable
            public final Object call() {
                switch (i6) {
                    case 0:
                        return new C1597j6(this.f6806b.f7020d);
                    default:
                        return new C1655o4(this.f6806b.f7019c);
                }
            }
        });
    }

    /* renamed from: a */
    public final boolean m3869a(C1492b c1492b) {
        try {
            C1505c c1505c = this.f7019c;
            c1505c.f6907b = c1492b;
            c1505c.f6908c = c1492b.clone();
            ((List) c1505c.f6909d).clear();
            ((C1090c) this.f7017a.f6355l).m2807f("runtime.counter", new C1578i(Double.valueOf(0.0d)));
            this.f7020d.m3270a(this.f7018b.m2805d(), this.f7019c);
            C1505c c1505c2 = this.f7019c;
            if (!(!((C1492b) c1505c2.f6908c).equals((C1492b) c1505c2.f6907b))) {
                if (!(!((List) this.f7019c.f6909d).isEmpty())) {
                    return false;
                }
            }
            return true;
        } catch (Throwable th) {
            throw new C1627m0(th);
        }
    }

    /* renamed from: b */
    public final void m3870b(C1545f2 c1545f2) {
        AbstractC1590j abstractC1590j;
        try {
            this.f7018b = ((C1090c) this.f7017a.f6354k).m2805d();
            if (this.f7017a.m3344a(this.f7018b, (C1569h2[]) c1545f2.m3782s().toArray(new C1569h2[0])) instanceof C1566h) {
                throw new IllegalStateException("Program loading failed");
            }
            for (C1533e2 c1533e2 : c1545f2.m3783t().m3711s()) {
                List<C1569h2> m3744t = c1533e2.m3744t();
                String m3743s = c1533e2.m3743s();
                Iterator<C1569h2> it = m3744t.iterator();
                while (it.hasNext()) {
                    InterfaceC1662p m3344a = this.f7017a.m3344a(this.f7018b, it.next());
                    if (!(m3344a instanceof C1626m)) {
                        throw new IllegalArgumentException("Invalid rule definition");
                    }
                    C1090c c1090c = this.f7018b;
                    if (c1090c.m2806e(m3743s)) {
                        InterfaceC1662p m2809h = c1090c.m2809h(m3743s);
                        if (!(m2809h instanceof AbstractC1590j)) {
                            String valueOf = String.valueOf(m3743s);
                            throw new IllegalStateException(valueOf.length() != 0 ? "Invalid function name: ".concat(valueOf) : new String("Invalid function name: "));
                        }
                        abstractC1590j = (AbstractC1590j) m2809h;
                    } else {
                        abstractC1590j = null;
                    }
                    if (abstractC1590j == null) {
                        String valueOf2 = String.valueOf(m3743s);
                        throw new IllegalStateException(valueOf2.length() != 0 ? "Rule function is undefined: ".concat(valueOf2) : new String("Rule function is undefined: "));
                    }
                    abstractC1590j.mo3670a(this.f7018b, Collections.singletonList(m3344a));
                }
            }
        } catch (Throwable th) {
            throw new C1627m0(th);
        }
    }
}
