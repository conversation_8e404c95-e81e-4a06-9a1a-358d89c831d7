package p152w2;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import p099o2.AbstractC1146a;
import p153w3.C1798e;

/* renamed from: w2.k0 */
/* loaded from: classes.dex */
public final class C1603k0 extends AbstractC1146a {
    public static final Parcelable.Creator<C1603k0> CREATOR = new C1615l0();

    /* renamed from: j */
    public final long f7045j;

    /* renamed from: k */
    public final long f7046k;

    /* renamed from: l */
    public final boolean f7047l;

    /* renamed from: m */
    public final String f7048m;

    /* renamed from: n */
    public final String f7049n;

    /* renamed from: o */
    public final String f7050o;

    /* renamed from: p */
    public final Bundle f7051p;

    /* renamed from: q */
    public final String f7052q;

    public C1603k0(long j6, long j7, boolean z5, String str, String str2, String str3, Bundle bundle, String str4) {
        this.f7045j = j6;
        this.f7046k = j7;
        this.f7047l = z5;
        this.f7048m = str;
        this.f7049n = str2;
        this.f7050o = str3;
        this.f7051p = bundle;
        this.f7052q = str4;
    }

    @Override // android.os.Parcelable
    public final void writeToParcel(Parcel parcel, int i6) {
        int m4526S = C1798e.m4526S(parcel, 20293);
        long j6 = this.f7045j;
        C1798e.m4540g0(parcel, 1, 8);
        parcel.writeLong(j6);
        long j7 = this.f7046k;
        C1798e.m4540g0(parcel, 2, 8);
        parcel.writeLong(j7);
        boolean z5 = this.f7047l;
        C1798e.m4540g0(parcel, 3, 4);
        parcel.writeInt(z5 ? 1 : 0);
        C1798e.m4524Q(parcel, 4, this.f7048m);
        C1798e.m4524Q(parcel, 5, this.f7049n);
        C1798e.m4524Q(parcel, 6, this.f7050o);
        C1798e.m4522O(parcel, 7, this.f7051p);
        C1798e.m4524Q(parcel, 8, this.f7052q);
        C1798e.m4539f0(parcel, m4526S);
    }
}
