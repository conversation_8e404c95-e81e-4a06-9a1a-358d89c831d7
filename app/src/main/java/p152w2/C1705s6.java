package p152w2;

import java.util.ListIterator;

/* renamed from: w2.s6 */
/* loaded from: classes.dex */
public final class C1705s6 implements ListIterator<String> {

    /* renamed from: j */
    public final ListIterator<String> f7178j;

    public C1705s6(C1729u6 c1729u6, int i6) {
        this.f7178j = c1729u6.f7195j.listIterator(i6);
    }

    @Override // java.util.ListIterator
    public final /* bridge */ /* synthetic */ void add(String str) {
        throw new UnsupportedOperationException();
    }

    @Override // java.util.ListIterator, java.util.Iterator
    public final boolean hasNext() {
        return this.f7178j.hasNext();
    }

    @Override // java.util.ListIterator
    public final boolean hasPrevious() {
        return this.f7178j.hasPrevious();
    }

    @Override // java.util.ListIterator, java.util.Iterator
    public final /* bridge */ /* synthetic */ Object next() {
        return this.f7178j.next();
    }

    @Override // java.util.ListIterator
    public final int nextIndex() {
        return this.f7178j.nextIndex();
    }

    @Override // java.util.ListIterator
    public final /* bridge */ /* synthetic */ String previous() {
        return this.f7178j.previous();
    }

    @Override // java.util.ListIterator
    public final int previousIndex() {
        return this.f7178j.previousIndex();
    }

    @Override // java.util.ListIterator, java.util.Iterator
    public final void remove() {
        throw new UnsupportedOperationException();
    }

    @Override // java.util.ListIterator
    public final /* bridge */ /* synthetic */ void set(String str) {
        throw new UnsupportedOperationException();
    }
}
