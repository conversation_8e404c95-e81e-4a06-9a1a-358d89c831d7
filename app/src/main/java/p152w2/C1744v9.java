package p152w2;

/* renamed from: w2.v9 */
/* loaded from: classes.dex */
public final class C1744v9 implements InterfaceC1732u9 {

    /* renamed from: a */
    public static final AbstractC1773y2<Boolean> f7239a;

    static {
        C1749w2 c1749w2 = new C1749w2(C1713t2.m4275a());
        f7239a = (C1737v2) c1749w2.m4355b("measurement.config.persist_last_modified", true);
        c1749w2.m4354a("measurement.id.config.persist_last_modified", 0L);
    }

    @Override // p152w2.InterfaceC1732u9
    /* renamed from: a */
    public final void mo4324a() {
    }

    @Override // p152w2.InterfaceC1732u9
    /* renamed from: b */
    public final boolean mo4325b() {
        return f7239a.m4460c().booleanValue();
    }
}
