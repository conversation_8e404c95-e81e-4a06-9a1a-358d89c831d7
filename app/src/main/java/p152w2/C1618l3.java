package p152w2;

import androidx.activity.result.AbstractC0055d;
import java.io.PrintStream;

/* renamed from: w2.l3 */
/* loaded from: classes.dex */
public final class C1618l3 {

    /* renamed from: a */
    public static final AbstractC0055d f7067a;

    static {
        AbstractC0055d c1594j3;
        Integer num = null;
        try {
            try {
                num = (Integer) Class.forName("android.os.Build$VERSION").getField("SDK_INT").get(null);
            } catch (Exception e6) {
                System.err.println("Failed to retrieve value from android.os.Build$VERSION.SDK_INT due to the following exception.");
                e6.printStackTrace(System.err);
            }
            c1594j3 = (num == null || num.intValue() < 19) ? !Boolean.getBoolean("com.google.devtools.build.android.desugar.runtime.twr_disable_mimic") ? new C1582i3() : new C1594j3() : new C1606k3();
        } catch (Throwable th) {
            PrintStream printStream = System.err;
            String name = C1594j3.class.getName();
            StringBuilder sb = new StringBuilder(name.length() + 133);
            sb.append("An error has occurred when initializing the try-with-resources desuguring strategy. The default strategy ");
            sb.append(name);
            sb.append("will be used. The error is: ");
            printStream.println(sb.toString());
            th.printStackTrace(System.err);
            c1594j3 = new C1594j3();
        }
        f7067a = c1594j3;
        if (num == null) {
            return;
        }
        num.intValue();
    }
}
