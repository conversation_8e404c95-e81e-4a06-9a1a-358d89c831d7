<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center"
    android:orientation="vertical"
    android:id="@+id/tip_layout_disconnect"
    android:background="@color/presentation_bg_color"
    android:visibility="gone"
    android:clickable="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView
        android:textSize="20sp"
        android:textColor="@color/title_color"
        android:gravity="center"
        android:id="@+id/tv_disconnect_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/virtual_tip_disconnect"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"/>
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp">
        <TextView
            android:textSize="18sp"
            android:textColor="@color/blue"
            android:gravity="center"
            android:id="@+id/tv_disconnect_cancel"
            android:background="@drawable/icon_shape_btn_selector"
            android:layout_width="80dp"
            android:layout_height="40dp"
            android:text="@string/devices_dialog_cancel"
            android:singleLine="true"
            android:layout_centerVertical="true"
            android:textAllCaps="false"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:layout_alignParentEnd="true"/>
        <TextView
            android:textSize="18sp"
            android:textColor="@color/blue"
            android:gravity="center"
            android:id="@+id/tv_disconnect_confirm"
            android:background="@drawable/icon_shape_btn_selector"
            android:layout_width="80dp"
            android:layout_height="40dp"
            android:text="@string/devices_dialog_confirm"
            android:singleLine="true"
            android:layout_centerVertical="true"
            android:textAllCaps="false"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:layout_marginStart="40dp"
            android:layout_alignParentEnd="true"/>
    </LinearLayout>
</LinearLayout>
