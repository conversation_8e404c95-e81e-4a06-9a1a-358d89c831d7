<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center"
    android:orientation="vertical"
    android:id="@+id/tip_layout_browser_local"
    android:background="@color/presentation_bg_color"
    android:visibility="gone"
    android:clickable="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView
        android:textSize="20sp"
        android:textColor="@color/title_color"
        android:gravity="center"
        android:id="@+id/tv_tip_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/virtual_tip_phone_open"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"/>
    <TextView
        android:textSize="18sp"
        android:textColor="@color/blue"
        android:gravity="center"
        android:id="@+id/tv_tip_local_browsing"
        android:background="@drawable/icon_shape_btn_selector"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginTop="56dp"
        android:text="@string/virtual_tip_local_browsing"
        android:singleLine="true"
        android:textAllCaps="false"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"/>
</LinearLayout>
