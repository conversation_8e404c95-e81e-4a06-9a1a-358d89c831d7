<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:id="@+id/bottom_btn_layout"
    android:background="@drawable/bg_browser_navi_def_color"
    android:clickable="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/home"
        android:background="?android:attr/selectableItemBackground"
        android:padding="14dp"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/ic_ctrl_home_selector"
        android:scaleType="fitCenter"
        android:layout_weight="1"
        android:contentDescription="@string/ctrl_home_description"/>
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/send_to_phone"
        android:background="?android:attr/selectableItemBackground"
        android:padding="14dp"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/ic_ctrl_disconnect"
        android:scaleType="fitCenter"
        android:layout_weight="1"
        android:contentDescription="@string/ctrl_send_to_phone_description"/>
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/reward"
        android:background="?android:attr/selectableItemBackground"
        android:padding="14dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/ic_ctrl_arrow_backward_selector"
        android:scaleType="fitCenter"
        android:layout_weight="1"
        android:contentDescription="@string/ctrl_reward_description"/>
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/forward"
        android:background="?android:attr/selectableItemBackground"
        android:padding="14dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/ic_ctrl_arrow_forward_selector"
        android:scaleType="fitCenter"
        android:layout_weight="1"
        android:contentDescription="@string/ctrl_forward_description"/>
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/send_to_remote"
        android:background="?android:attr/selectableItemBackground"
        android:padding="14dp"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/ic_ctrl_send_to_remote_selector"
        android:scaleType="fitCenter"
        android:layout_weight="1"
        android:contentDescription="@string/ctrl_send_to_remote_description"/>
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/refresh"
        android:background="?android:attr/selectableItemBackground"
        android:padding="14dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/ic_ctrl_refresh_selector"
        android:scaleType="fitCenter"
        android:layout_weight="1"
        android:contentDescription="@string/ctrl_refresh_description"/>
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/star"
        android:background="?android:attr/selectableItemBackground"
        android:padding="14dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/ic_ctrl_star_border_selector"
        android:scaleType="fitCenter"
        android:layout_weight="1"
        android:contentDescription="@string/ctrl_start_description"/>
</LinearLayout>
